window.theme = window.theme || {};

/* ==============================
  UTILITIES
  ============================== */

theme.utils = {

  // _.compact from lodash
  // Creates an array with all falsey values removed. The values `false`, `null`,
  // `0`, `""`, `undefined`, and `NaN` are falsey.
  // _.compact([0, 1, false, 2, '', 3]);
  // => [1, 2, 3]

  compact: function(array) {
    var index = -1,
        length = array == null ? 0 : array.length,
        resIndex = 0,
        result = [];

    while (++index < length) {
      var value = array[index];
      if (value) {
        result[resIndex++] = value;
      }
    }
    return result;

  }

};

Shopify.Theme = {

  selectors: {

    productForm: '.product-form',

  },

  sections: {

    sectionName: {

      _init: function() {

        // Custom private section method

        var section = this.container;

        console.log(section);

      },

      // Shortcut function called when a section is loaded via 'sections.load()' or by the Theme Editor 'shopify:section:load' event.
      onLoad: function() {

        this._init();

      }

    }

  },

  functions: {

    fadeOut: function( elem, ms ) {
      if( ! elem )
        return;

      if( ms )
      {
        var opacity = 1;
        var timer = setInterval( function() {
          opacity -= 50 / ms;
          if( opacity <= 0 )
          {
            clearInterval(timer);
            opacity = 0;
            elem.style.display = "none";
            elem.style.visibility = "hidden";
          }
          elem.style.opacity = opacity;
          elem.style.filter = "alpha(opacity=" + opacity * 100 + ")";
        }, 50 );
      }
      else
      {
        elem.style.opacity = 0;
        elem.style.filter = "alpha(opacity=0)";
        elem.style.display = "none";
        elem.style.visibility = "hidden";
      }
    }

  },

  components: {

    tooltips: function(){

      tippy("[data-tippy-content]", {
        theme: "dark",
        placement: "bottom"
      });

    },

  }

};

// Shopify.Theme.components.tooltips();

window.addEventListener('beforeunload', (event) => {


});
