/**
 * NOTE: most of the CSS variables used are defined in the "css-variables.liquid" snippet file
 */

@media screen and (max-width: 740px) {
  .hidden-phone {
    display: none !important;
  }
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .hidden-tablet {
    display: none !important;
  }
}

@media screen and (min-width: 741px) {
  .hidden-tablet-and-up {
    display: none !important;
  }
}

@media screen and (max-width: 999px) {
  .hidden-pocket {
    display: none !important;
  }
}

@media screen and (min-width: 1000px) and (max-width: 1199px) {
  .hidden-lap {
    display: none !important;
  }
}

@media screen and (min-width: 1000px) {
  .hidden-lap-and-up {
    display: none !important;
  }
}

@media screen and (min-width: 1200px) {
  .hidden-desk {
    display: none !important;
  }
}

@media screen and (min-width: 1400px) {
  .hidden-wide {
    display: none !important;
  }
}

@media screen and (pointer: fine) {
  .hidden-no-touch {
    display: none !important;
  }
}

@media not screen and (pointer: fine) {
  .hidden-touch {
    display: none !important;
  }
}

@media print {
  .hidden-print {
    display: none !important;
  }
}

/*! minireset.css v0.0.6 | MIT License | github.com/jgthms/minireset.css */

*,
*::before,
*::after {
  box-sizing: border-box !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html {
  font-family: sans-serif;
  -ms-overflow-style: -ms-autohiding-scrollbar;
  -webkit-text-size-adjust: 100%;
          text-size-adjust: 100%;
}

body {
  margin: 0;
}

[hidden] {
  display: none;
}

blockquote:first-child,
ul:first-child,
ol:first-child,
p:first-child,
h1:first-child,
h2:first-child,
h3:first-child,
h4:first-child,
h5:first-child,
h6:first-child {
  margin-top: 0 !important;
}

blockquote:last-child,
ul:last-child,
ol:last-child,
p:last-child,
h1:last-child,
h2:last-child,
h3:last-child,
h4:last-child,
h5:last-child,
h6:last-child {
  margin-bottom: 0 !important;
}

a {
  color: inherit;
  text-decoration: none;
}

button,
input,
select,
textarea {
  color: inherit;
  font: inherit;
  margin: 0;
  text-align: inherit;
}

button,
[type="submit"] {
  padding: 0;
  overflow: visible;
  background: none;
  border: none;
  border-radius: 0;
  cursor: pointer;
  -webkit-appearance: none;
          appearance: none;
  touch-action: manipulation;
}

button[disabled],
html input[disabled] {
  cursor: default;
}

img,
video {
  height: auto;
  max-width: 100%;
  border-style: none;
  vertical-align: top;
}

/* By default Firefox show the alt tag of image while image is loading, which is often not desirable */
img:-moz-loading {
  visibility: hidden;
}

iframe {
  border: 0;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td,
th {
  padding: 0;
}
/*! PhotoSwipe main CSS by Dmitry Semenov | photoswipe.com | MIT license */

.pswp {
  display: none;
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  overflow: hidden;
  touch-action: none;
  z-index: 1500;
  -webkit-backface-visibility: hidden;
  outline: none;
}

.pswp img {
  max-width: none;
}

.pswp--animate_opacity {
  opacity: 0.001;
  will-change: opacity;
  transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);
}

.pswp--open {
  display: block;
}

.pswp--zoom-allowed .pswp__img {
  cursor: var(--zoom-cursor-svg-url) 26 26, zoom-in;
}

.pswp--zoomed-in .pswp__img {
  cursor: grab;
}

.pswp--dragging .pswp__img {
  cursor: grabbing;
}

/* Background is added as a separate element, as animating opacity is much faster than animating rgba() background-color. */
.pswp__bg {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgb(var(--background));
  opacity: 0;
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  will-change: opacity;
}

.pswp__scroll-wrap {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.pswp__container,
.pswp__zoom-wrap {
  touch-action: none;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

/* Prevent selection and tap highlights */
.pswp__container,
.pswp__img {
  -webkit-user-select: none;
          user-select: none;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}

.pswp__zoom-wrap {
  position: absolute;
  width: 100%;
  transform-origin: left top;
  transition: transform 333ms cubic-bezier(0.4, 0, 0.22, 1);
}

.pswp__bg {
  will-change: opacity;
  transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);
}

.pswp--animated-in .pswp__bg,
.pswp--animated-in .pswp__zoom-wrap {
  transition: none;
}

.pswp__container,
.pswp__zoom-wrap {
  -webkit-backface-visibility: hidden;
}

.pswp__item {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  overflow: hidden;
}

.pswp__img {
  position: absolute;
  width: auto;
  height: auto;
  top: 0;
  left: 0;
}

/* UI */

[dir="ltr"] .pswp__top-bar {
  right: var(--container-gutter);
}

[dir="rtl"] .pswp__top-bar {
  left: var(--container-gutter);
}

.pswp__top-bar {
  position: absolute;
  top: var(--container-gutter);
}

.pswp__prev-next-buttons {
  position: absolute;
  display: flex;
  justify-content: space-between;
  left: 0;
  right: 0;
  top: calc(50% - 28px); /* 28px is half the height of button */
  margin-left: var(--container-gutter);
  margin-right: var(--container-gutter);
  pointer-events: none;
}

.pswp__dots-nav-wrapper {
  display: flex;
  position: absolute;
  bottom: 0;
  padding-left: var(--container-gutter);
  padding-right: var(--container-gutter);
  padding-top: 20px;
  padding-bottom: 20px;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  background: rgb(var(--background));
  transition: opacity 0.25s ease-in-out, transform 0.25s ease-in-out;
  transition-delay: 0.2s;
}

.pswp__dots-nav-wrapper .dots-nav {
  padding-left: 20px;
  padding-right: 20px;
}

.pswp__ui--hidden .pswp__dots-nav-wrapper {
  opacity: 0;
  transform: translateY(10px);
}

.pswp__button svg {
  transition: transform 0.25s ease-in-out;
}

@supports (padding: max(0px)) {
  .pswp__dots-nav-wrapper {
    padding-bottom: max(20px, env(safe-area-inset-bottom, 0px) + 20px);
  }
}

@media screen and (pointer: fine) {
  .pswp__button:hover svg {
    transform: rotateZ(90deg);
  }
}
.flickity-enabled {
  position: relative;
  overflow: visible !important;
}

.flickity-enabled:focus {
  outline-offset: 2px;
}

.flickity-viewport {
  overflow: hidden;
  position: relative;
  height: 100%;
  width: 100%;
}

.flickity-slider {
  position: absolute;
  width: 100%;
  height: 100%;
}

/* draggable */
.flickity-enabled.is-draggable {
  -webkit-tap-highlight-color: transparent;
  -webkit-user-select: none;
          user-select: none;
}

.flickity-enabled.is-draggable:not(.is-hovering-right):not(.is-hovering-left) .flickity-viewport {
  cursor: grab;
}

.flickity-enabled.is-draggable:not(.is-hovering-right):not(.is-hovering-left) .flickity-viewport.is-pointer-down {
  cursor: grabbing;
}

.flickity-enabled.is-hovering-right .flickity-viewport {
  cursor: var(--arrow-right-svg-url) 17 14, e-resize;
}

.flickity-enabled.is-hovering-left .flickity-viewport {
  cursor: var(--arrow-left-svg-url) 17 14, w-resize;
}

.flickity-rtl .flickity-page-dots {
  direction: rtl;
}

/* flickity-fade */
.flickity-enabled.is-fade .flickity-slider > * {
  pointer-events: none;
  z-index: 0;
  transition: visibility 0.2s linear; /* this is a hotfix for Safari mobile */
}

.flickity-enabled.is-fade .flickity-slider > .is-selected {
  pointer-events: auto;
  z-index: 1;
}

.flickity-enabled.is-fade .flickity-slider > :not(.is-selected) {
  visibility: hidden;
}

/**
 * -------------------------------------------------------------
 * GENERAL TYPOGRAPHY
 * -------------------------------------------------------------
 */

html {
  font-family: var(--text-font-family);
  font-weight: var(--text-font-weight);
  font-style: var(--text-font-style);
  font-size: calc(var(--base-font-size) - 1px);
  line-height: 1.7142857143;
  color: rgb(var(--text-color));
  background: rgb(var(--background));
}

:lang(ar) * {
  letter-spacing: normal !important; /* Arabic should never have letter spacing as it makes it unreadable on some fonts */
}

p strong,
p b {
  font-weight: var(--text-font-bold-weight);
}

.heading,
.blockquote,
.rte h1,
.rte h2,
.rte h3,
.rte h4,
.rte h5,
.rte h6,
.rte blockquote {
  display: block;
  font-family: var(--heading-font-family);
  font-weight: var(--heading-font-weight);
  font-style: var(--heading-font-style);
  color: rgb(var(--heading-color));
  text-transform: var(--heading-text-transform);
}

.text--small {
  font-size: calc(var(--base-font-size) - 2px);
  line-height: 1.6923076923;
}

.text--xsmall {
  font-size: calc(var(--base-font-size) - 3px);
  line-height: 1.5;
}

.text--xxsmall {
  font-size: calc(var(--base-font-size) - 5px);
  line-height: 1.5;
}

.text--large {
  font-size: calc(var(--base-font-size) + 1px);
}

.text--subdued {
  color: rgba(var(--text-color), 0.7);
}

[dir="ltr"] .text--left {
  text-align: left;
}

[dir="rtl"] .text--left {
  text-align: right;
}

.text--center {
  text-align: center;
}

[dir="ltr"] .text--right {
  text-align: right;
}

[dir="rtl"] .text--right {
  text-align: left;
}

.text--strong {
  font-weight: var(--text-font-bold-weight);
}

.text--underlined {
  text-decoration: underline;
  text-underline-offset: 3px;
  -webkit-text-decoration-color: currentColor;
          text-decoration-color: currentColor;
}

p a:not(.button),
.rte a:not(.button),
.link {
  text-decoration: underline;
  text-underline-offset: 2px;
  -webkit-text-decoration-color: rgba(var(--text-color), 0.35);
          text-decoration-color: rgba(var(--text-color), 0.35);
  transition: color 0.2s ease-in-out, -webkit-text-decoration-color 0.2s ease-in-out;
  transition: text-decoration-color 0.2s ease-in-out, color 0.2s ease-in-out;
  transition: text-decoration-color 0.2s ease-in-out, color 0.2s ease-in-out, -webkit-text-decoration-color 0.2s ease-in-out;
}

/* When the link class is assigned to the heading--small, we have to force increase the line height to prevent the line to overlap */
.heading--small.link {
  line-height: 1.8;
}

@media screen and (pointer: fine) {
  p a:not(.button):hover,
  .rte a:not(.button):hover,
  .link:hover {
    color: rgb(var(--text-color));
    -webkit-text-decoration-color: rgb(var(--text-color));
            text-decoration-color: rgb(var(--text-color));
  }
}

/* Animated link */
.link--animated {
  display: block;
  position: relative;
  width: max-content;
}

.link--animated::after {
  content: '';
  position: absolute;
  bottom: 2px;
  left: 0;
  width: 100%;
  height: 1px;
  background: currentColor;
  transform: scaleX(0);
  transform-origin: var(--transform-origin-end);
  transition: transform 0.3s;
}

.text--underlined.link--animated {
  text-decoration: none;
}

.text--underlined.link--animated::after {
  transform: scaleX(1);
}

@media screen and (pointer: fine) {
  .link--animated[aria-expanded="true"]::after,
  .link--animated:hover::after {
    transform: scaleX(1);
    transform-origin: var(--transform-origin-start);
  }

  @keyframes textUnderlinedAnimatedKeyframes {
    0% {
      transform: scaleX(1);
      transform-origin: var(--transform-origin-end);
    }

    50% {
      transform: scaleX(0);
      transform-origin: var(--transform-origin-end);
    }

    51% {
      transform-origin: var(--transform-origin-start);
    }

    100% {
      transform: scaleX(1);
    }
  }

  .text--underlined.link--animated:hover::after {
    animation: textUnderlinedAnimatedKeyframes 0.6s;
  }
}

/* Reduced opacity linked */
.link--faded {
  transition: opacity 0.25s ease-in-out;
}

.link--faded:hover {
  opacity: 0.7;
}

/* Hide a text visually without removing it from screen readers (mostly used for accessibility) */
.visually-hidden {
  position: absolute !important;
  overflow: hidden;
  clip: rect(0 0 0 0);
  height: 1px;
  width: 1px;
  margin: -1px;
  padding: 0;
  border: 0;
}

/* Headings */

@media screen and (min-width: 741px) {
  html {
    font-size: var(--base-font-size);
    line-height: 1.7333333333;
  }

  .text--small {
    font-size: calc(var(--base-font-size) - 1px);
    line-height: 1.714285713;
  }

  .text--xsmall {
    font-size: calc(var(--base-font-size) - 2px);
    line-height: 1.5384615385;
  }

  .text--xxsmall {
    font-size: calc(var(--base-font-size) - 4px);
    line-height: 1.5384615385;
  }

  .text--large {
    font-size: calc(var(--base-font-size) + 5px);
  }
}

/**
 * -------------------------------------------------------------
 * RTE STYLES
 *
 * NOTE: In Focal, all text are spaced consistently in RTE fields (those written in text editor of Shopify). However,
 *       we also re-use internally in the theme for most section the same spacing between elements. In order to space
 *       those, a "text-container" class is introduced and allow to introduce a consistent spacing.
 * -------------------------------------------------------------
 */

.heading:first-child {
  margin-top: 0;
}

.heading:last-child {
  margin-bottom: 0;
}

/*
 * IMPLEMENTATION NOTE: while those are called "heading--small", they are actually using the text font and not the heading
 * font. The reason is that we found out that due to their small size, the body font usually works better visually. Of
 * course if both heading and body font are the same, this won't change anything.
 */
.heading--small,
.heading--xsmall,
.heading--xxsmall {
  font-family: var(--text-font-family);
  font-style: var(--text-font-style);
  text-transform: uppercase; /* Small headings are always uppercase */
  font-weight: var(--text-font-bold-weight);
  line-height: 1.4663636;
  letter-spacing: 1px;
}

.heading--xxsmall {
  font-size: var(--heading-xxsmall-font-size);
}

.heading--xsmall {
  font-size: var(--heading-xsmall-font-size);
}

.heading--small {
  font-size: var(--heading-small-font-size);
  line-height: 1.5; /* Slightly bigger line height */
}

.heading--large,
.rte .heading--large {
  font-size: var(--heading-large-font-size);
  line-height: 1.11111111;
  letter-spacing: -0.9px;
}

.h1,
.rte h1 {
  font-size: var(--heading-h1-font-size);
  line-height: 1.11111111;
  letter-spacing: -0.9px;
}

.h2,
.rte h2 {
  font-size: var(--heading-h2-font-size);
  line-height: 1.13333333;
  letter-spacing: -0.6px;
}

.h3,
.rte h3 {
  font-size: var(--heading-h3-font-size);
  line-height: 1.1538461538;
  letter-spacing: -0.4px;
}

.h4,
.rte h4 {
  font-size: var(--heading-h4-font-size);
  line-height: 1.1666666667;
  letter-spacing: -0.3px;
}

.h5,
.rte h5 {
  font-size: var(--heading-h5-font-size);
  line-height: 1.2;
  letter-spacing: -0.3px;
}

.h6,
.rte h6 {
  font-size: var(--heading-h6-font-size);
  line-height: 1.25;
}

.blockquote,
.rte blockquote {
  position: relative;
  padding: 24px 24px 0;
  font-size: var(--heading-h4-font-size);
  line-height: 1.1666666667;
  letter-spacing: -0.3px;
}

[dir="ltr"] .blockquote::before,[dir="ltr"]
.rte blockquote::before {
  left: calc(50% - (71px / 2));
}

[dir="rtl"] .blockquote::before,[dir="rtl"]
.rte blockquote::before {
  right: calc(50% - (71px / 2));
}

.blockquote::before,
.rte blockquote::before {
  content: '';
  position: absolute;
  width: 71px;
  height: 56px;
  top: -10px;
  background: rgb(var(--text-color));
  opacity: 0.15;
  -webkit-mask-image: url(data:image/svg+xml;base64,PHN2ZyBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDEgODAiPiAgICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTAgNDEuNzdWMGg0MS42MDR2NDEuNzdMMjAuMDI2IDgwSC45ODdMMjEuNzIgNDEuNzdIMHptNTkuMzk2IDBWMEgxMDF2NDEuNzdMNzkuNDIyIDgwSDYwLjM4M2wyMC43MzItMzguMjNINTkuMzk2eiIgZmlsbD0iY3VycmVudENvbG9yIiAvPjwvc3ZnPg==);
          mask-image: url(data:image/svg+xml;base64,PHN2ZyBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDEgODAiPiAgICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTAgNDEuNzdWMGg0MS42MDR2NDEuNzdMMjAuMDI2IDgwSC45ODdMMjEuNzIgNDEuNzdIMHptNTkuMzk2IDBWMEgxMDF2NDEuNzdMNzkuNDIyIDgwSDYwLjM4M2wyMC43MzItMzguMjNINTkuMzk2eiIgZmlsbD0iY3VycmVudENvbG9yIiAvPjwvc3ZnPg==);
  -webkit-mask-size: 71px 56px;
          mask-size: 71px 56px;
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
}

[dir="ltr"] .unordered-list,[dir="ltr"]
.text-container ul,[dir="ltr"]
.rte ul {
  margin-left: 1.25em;
  margin-right: 0;
}

[dir="rtl"] .unordered-list,[dir="rtl"]
.text-container ul,[dir="rtl"]
.rte ul {
  margin-right: 1.25em;
  margin-left: 0;
}

.unordered-list,
.text-container ul,
.rte ul {
  list-style-type: square;
  padding: 0;
}

[dir="ltr"] .ordered-list,[dir="ltr"]
.text-container ol,[dir="ltr"]
.rte ol {
  margin-left: 1em;
  margin-right: 0;
}

[dir="rtl"] .ordered-list,[dir="rtl"]
.text-container ol,[dir="rtl"]
.rte ol {
  margin-right: 1em;
  margin-left: 0;
}

.ordered-list,
.text-container ol,
.rte ol {
  padding: 0;
}

[dir="ltr"] .unordered-list li,[dir="ltr"]
.text-container ul li,[dir="ltr"]
.rte ul li {
  padding: 2px 0 2px 5px;
}

[dir="rtl"] .unordered-list li,[dir="rtl"]
.text-container ul li,[dir="rtl"]
.rte ul li {
  padding: 2px 5px 2px 0;
}

[dir="ltr"] .ordered-list li,[dir="ltr"]
.text-container ol li,[dir="ltr"]
.rte ol li {
  padding: 3px 0 3px 9px;
}

[dir="rtl"] .ordered-list li,[dir="rtl"]
.text-container ol li,[dir="rtl"]
.rte ol li {
  padding: 3px 9px 3px 0;
}

.unordered-list li::marker,
.text-container ul li::marker,
.rte ul li::marker {
  color: inherit;
  font-size: 16px;
}

.ordered-list li::marker,
.text-container ol li::marker,
.rte ol li::marker {
  color: inherit;
  font-size: 11px;
}

/* Minimal table styling, with a bordered variation */

.table-wrapper {
  overflow-x: auto;
}

.table,
.rte table {
  --table-spacing: 16px;
  width: 100%;
}

.table--loose {
  --table-spacing: 24px;
}

[dir="ltr"] .table th:not([class*="text--"]),[dir="ltr"]
.rte table th:not([class*="text--"]) {
  text-align: left;
}

[dir="rtl"] .table th:not([class*="text--"]),[dir="rtl"]
.rte table th:not([class*="text--"]) {
  text-align: right;
}

.table th,
.rte table th {
  padding-bottom: 15px;
  border-bottom: 1px solid rgb(var(--border-color));
}

.table th,
.rte table th {
  padding-left: var(--table-spacing);
  padding-right: var(--table-spacing);
}

.table td.half-spaced,
.rte table td.half-spaced {
  padding: calc(var(--table-spacing) / 2);
}

.table td,
.rte table td {
  padding: var(--table-spacing);
  padding-bottom: 0;
}

.table tr[onclick] {
  cursor: pointer;
}

[dir="ltr"] .table th:first-child,[dir="ltr"]
.rte table th:first-child,[dir="ltr"]
.table td:first-child,[dir="ltr"]
.rte table td:first-child {
  padding-left: 0;
}

[dir="rtl"] .table th:first-child,[dir="rtl"]
.rte table th:first-child,[dir="rtl"]
.table td:first-child,[dir="rtl"]
.rte table td:first-child {
  padding-right: 0;
}

[dir="ltr"] .table th:last-child,[dir="ltr"]
.rte table th:last-child,[dir="ltr"]
.table td:last-child,[dir="ltr"]
.rte table td:last-child {
  padding-right: 0;
}

[dir="rtl"] .table th:last-child,[dir="rtl"]
.rte table th:last-child,[dir="rtl"]
.table td:last-child,[dir="rtl"]
.rte table td:last-child {
  padding-left: 0;
}

.table tfoot tr:first-child td,
.rte table tfoot tr:first-child td {
  border-top: 1px solid rgb(var(--border-color));
}

.table tfoot tr:not(:first-child) td,
.rte table tfoot tr:not(:first-child) td {
  padding-top: 8px;
}

.table--bordered td {
  border-top: 1px solid rgb(var(--border-color));
  padding-bottom: var(--table-spacing);
}

.table--footered tbody tr:last-child td {
  padding-bottom: var(--table-spacing);
}

@media screen and (max-width: 740px) {
  .table tfoot td,
  .rte table tfoot td {
    padding-top: 16px;
  }
}

@media screen and (min-width: 741px) {
  .ordered-list li::marker,
  .text-container ol li::marker,
  .rte ol li::marker {
    font-size: 12px;
  }

  .heading--xsmall {
    line-height: 1.466666666;
  }

  .heading--small {
    line-height: 1.2307692308;
  }

  .heading--large {
    line-height: 1;
  }

  .h1,
  .rte h1 {
    line-height: 1.0416666667;
    letter-spacing: -1px;
  }

  .h2,
  .rte h2 {
    line-height: 1.1052631579;
    letter-spacing: -1px;
  }

  .h3,
  .rte h3 {
    line-height: 1.0625;
    letter-spacing: -0.8px;
  }

  .h4,
  .rte h4 {
    line-height: 1.1666666667;
    letter-spacing: -0.6px;
  }

  .h5,
  .rte h5 {
    line-height: 1.2;
    letter-spacing: -0.3px;
  }

  .h6,
  .rte h6 {
    line-height: 1.2222222222;
  }

  [dir="ltr"] .blockquote,[dir="ltr"]
  .rte blockquote {
    padding-left: 49px;
    padding-right: 0;
  }

  [dir="rtl"] .blockquote,[dir="rtl"]
  .rte blockquote {
    padding-right: 49px;
    padding-left: 0;
  }

  .blockquote,
  .rte blockquote {
    line-height: 1.1666666667;
    letter-spacing: -0.6px;
    min-height: 63px;
  }

  [dir="ltr"] .blockquote--center {
    padding-left: 0;
  }

  [dir="rtl"] .blockquote--center {
    padding-right: 0;
  }

  [dir="ltr"] .blockquote:not(.blockquote--center)::before,[dir="ltr"]
  .rte blockquote:not(.blockquote--center)::before {
    left: 0;
  }

  [dir="rtl"] .blockquote:not(.blockquote--center)::before,[dir="rtl"]
  .rte blockquote:not(.blockquote--center)::before {
    right: 0;
  }

  .blockquote:not(.blockquote--center)::before,
  .rte blockquote:not(.blockquote--center)::before {
    width: 80px;
    height: 63px;
    top: 0;
    -webkit-mask-size: 80px 63px;
            mask-size: 80px 63px;
  }

  [dir="ltr"] .unordered-list li,[dir="ltr"]
  .text-container ul li,[dir="ltr"]
  .rte ul li {
    padding: 4px 0 4px 5px;
  }

  [dir="rtl"] .unordered-list li,[dir="rtl"]
  .text-container ul li,[dir="rtl"]
  .rte ul li {
    padding: 4px 5px 4px 0;
  }

  [dir="ltr"] .ordered-list li,[dir="ltr"]
  .text-container ol li,[dir="ltr"]
  .rte ol li {
    padding: 4px 0 4px 9px;
  }

  [dir="rtl"] .ordered-list li,[dir="rtl"]
  .text-container ol li,[dir="rtl"]
  .rte ol li {
    padding: 4px 9px 4px 0;
  }
}

@media screen and (min-width: 1000px) {
  /* The rich text table keep the smaller padding as merchant often use RTE with lot of columns */
  .table {
    --table-spacing: 24px;
  }

  .table--loose {
    --table-spacing: 32px;
  }
}

@media screen and (min-width: 1200px) {
  .heading--large,
  .rte .heading--large {
    line-height: 1;
  }

  .h1,
  .rte h1 {
    line-height: 1.0714285714;
    letter-spacing: -1px;
  }

  .h2,
  .rte h2 {
    line-height: 1.0833333333;
    letter-spacing: -1px;
  }

  .h3,
  .rte h3 {
    line-height: 1.1111111111;
    letter-spacing: -0.8px;
  }

  .h4,
  .rte h4 {
    line-height: 1.1333333333;
    letter-spacing: -0.7px;
  }

  .h5,
  .rte h5 {
    line-height: 1.1666666667;
    letter-spacing: -0.4px;
  }

  .h6,
  .rte h6 {
    line-height: 1.2222222222;
  }

  [dir="ltr"] .blockquote,[dir="ltr"]
  .rte blockquote {
    padding-left: 69px;
  }

  [dir="rtl"] .blockquote,[dir="rtl"]
  .rte blockquote {
    padding-right: 69px;
  }

  .blockquote,
  .rte blockquote {
    line-height: 1.1333333333;
    letter-spacing: -0.7px;
    min-height: 80px;
  }

  [dir="ltr"] .blockquote--center,[dir="ltr"]
  .rte .blockquote--center {
    padding-left: 0;
  }

  [dir="rtl"] .blockquote--center,[dir="rtl"]
  .rte .blockquote--center {
    padding-right: 0;
  }

  .blockquote:not(.blockquote--center)::before,
  .rte blockquote:not(.blockquote--center)::before {
    width: 101px;
    height: 81px;
    -webkit-mask-size: 101px 81px;
            mask-size: 101px 81px;
  }
}

/**
 * -------------------------------------------------------------
 * RTE SPACING
 *
 * NOTE: to have the same spacing as in an RTE field, you can wrap
 *       the element by a div with the class "text-container"
 * -------------------------------------------------------------
 */

.text-container p:not(.heading) + p,
.rte p:not(.heading) + p,
.text-container p + form,
.rte p + form {
  margin-top: 24px;
}

.text-container .heading--large,
.rte .heading--large {
  margin: 48px 0 40px;
}

.text-container .h1,
.rte h1 {
  margin: 48px 0 24px;
}

.text-container .h2,
.rte h2,
.text-container .h3,
.rte h3,
.text-container .h4,
.rte h4,
.text-container .h5,
.rte h5,
.text-container .h6,
.rte h6 {
  margin: 40px 0 16px;
}

.text-container .heading--small,
.rte .heading--small {
  margin: 16px 0;
}

.text-container .heading--xsmall,
.rte .heading--xsmall {
  margin: 12px 0;
}

.blockquote,
.rte blockquote {
  margin: 48px 0 64px;
}

.text-container img:not([style*="float"]),
.rte img:not([style*="float"]) {
  display: block;
  margin: 34px 0;
}

.text-container ul,
.rte ul,
.text-container ol,
.rte ol {
  margin-top: 1em;
  margin-bottom: 1em;
}

@media screen and (min-width: 1000px) {
  .text-container p + form,
  .rte p + form {
    margin-top: 32px;
  }

  .text-container .h1,
  .rte h1 {
    margin: 48px 0 32px;
  }

  .text-container .h2,
  .rte h2,
  .text-container .h3,
  .rte h3,
  .text-container .h4,
  .rte h4 {
    margin: 48px 0 24px;
  }

  .text-container .h5,
  .rte h5,
  .text-container .h6,
  .rte h6 {
    margin: 40px 0 16px;
  }

  .blockquote,
  .rte blockquote {
    margin: 80px 0 96px;
  }
}

/* We remove the spacing for first item and last item */

.rte > :first-child,
.text-container > :first-child {
  margin-top: 0;
}

.rte > :last-child,
.text-container > :last-child {
  margin-bottom: 0;
}

/**
 * -------------------------------------------------------------
 * SPACING COMBINATIONS
 *
 * NOTE: this define common combination in order to provide a consistent
 *       styling throughout the theme
 * -------------------------------------------------------------
 */

.heading--small + .heading--large {
  margin-top: 32px;
}

.heading--small + p,
.heading--xsmall + p {
  margin-top: 16px;
}

.heading--small + p,
.heading--small + .h1,
.heading--small + h1,
.heading--small + .h2,
.heading--small + h2,
.heading--small + .h3,
.heading--small + h3,
.heading--small + .h4,
.heading--small + h4,
.heading--small + .h5,
.heading--small + h5,
.heading--small + .h6,
.heading--small + h6 {
  margin-top: 16px;
}

.heading--xsmall + p,
.heading--xsmall + .h1,
.heading--xsmall + h1,
.heading--xsmall + .h2,
.heading--xsmall + h2,
.heading--xsmall + .h3,
.heading--xsmall + h3 {
  margin-top: 16px;
}

.heading--xsmall + .h4,
.heading--xsmall + h4,
.heading--xsmall + .h5,
.heading--xsmall + h5,
.heading--xsmall + .h6,
.heading--xsmall + h6 {
  margin-top: 12px;
}

.heading + .button-wrapper,
.heading + .button-group,
p + .button-wrapper,
p + .button-group,
.button-wrapper + p,
.button-group + p {
  margin-top: 32px;
}

@media screen and (min-width: 741px) {
  .heading--small + p,
  .heading--small + .h1,
  .heading--small + h1,
  .heading--small + .h2,
  .heading--small + h2,
  .heading--small + .h3,
  .heading--small + h3,
  .heading--small + .h4,
  .heading--small + h4 {
    margin-top: 24px;
  }

  .heading--small + .h5,
  .heading--small + h5,
  .heading--small + .h6,
  .heading--small + h6 {
    margin-top: 16px;
  }

  .heading--xsmall + .h1,
  .heading--xsmall + h1,
  .heading--xsmall + .h2,
  .heading--xsmall + h2 {
    margin-top: 24px;
  }
}
/**
 * -------------------------------------------------------------
 * SHOPIFY SECTION
 * -------------------------------------------------------------
 */

.shopify-section {
  color: rgb(var(--text-color)); /* Allow easy override of per-section color */
  scroll-margin-top: calc(var(--header-height, 0px) * var(--enable-sticky-header) + var(--announcement-bar-height, 0px) * var(--enable-sticky-announcement-bar));
}

/**
 * -------------------------------------------------------------
 * TABBING MANAGEMENT
 * -------------------------------------------------------------
 */

.js .no-focus-outline *:focus {
  outline: none;
}

/**
 * -------------------------------------------------------------
 * LOCK UTILITY
 * -------------------------------------------------------------
 */

.lock-all {
  overflow: hidden;
  touch-action: none;
}

@media screen and (max-width: 740px) {
  .lock-mobile {
    overflow: hidden;
    touch-action: none;
  }
}

/**
 * -------------------------------------------------------------
 * CONTAINER
 * -------------------------------------------------------------
 */

/* Implementation note: the double selector for policy container allows to increase selector specificity to override
   Shopify default style */
.container,
.shopify-policy__container.shopify-policy__container {
  width: 100%;
  max-width: var(--container-max-width);
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--container-gutter);
  padding-right: var(--container-gutter);
  --container-outer-margin: 0px; /* Inside a container only the gutter remains as margin */
}

.container--small {
  max-width: 930px;
}

@media screen and (max-width: 999px) {
  /* Variation that remove the padding on small devices */
  .container--flush {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 1400px) {
  .container--medium {
    padding-left: calc(var(--container-gutter) + var(--grid-column-width) + var(--grid-gap));
    padding-right: calc(var(--container-gutter) + var(--grid-column-width) + var(--grid-gap)); /* Medium container just adds an extra column on left and top */
  }
}

/**
 * --------------------------------------------------------------------
 * NON-JS AND REVEAL ELEMENTS
 * --------------------------------------------------------------------
 */

.js .no-js {
  display: none !important;
}

[reveal] {
  opacity: 0;
}

[reveal-visibility] {
  visibility: hidden;
}

.no-js [reveal] {
  opacity: 1;
}

.no-js [reveal-visibility] {
  visibility: visible;
}

/**
 * --------------------------------------------------------------------
 * ACCESSIBILITY
 * --------------------------------------------------------------------
 */

.skip-to-content:focus {
  clip: auto;
  width: auto;
  height: auto;
  margin: 0;
  color: rgb(var(--text-color));
  background-color: rgb(var(--background));
  padding: 10px;
  opacity: 1;
  z-index: 10000;
  transition: none;
}

/**
 * --------------------------------------------------------------------
 * VERTICAL BREATHER
 * --------------------------------------------------------------------
 */

.vertical-breather {
  padding-top: var(--vertical-breather);
  padding-bottom: var(--vertical-breather);
}

.vertical-breather--tight {
  padding-top: var(--vertical-breather-tight);
  padding-bottom: var(--vertical-breather-tight);
}

.vertical-breather--margin {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  margin-top: var(--vertical-breather);
  margin-bottom: var(--vertical-breather);
}

.vertical-breather--margin.vertical-breather--tight {
  margin-top: var(--vertical-breather-tight);
  margin-bottom: var(--vertical-breather-tight);
}

@media screen and (min-width: 741px) {
  .vertical-breather--extra-tight {
    padding-top: 48px;
    padding-bottom: 48px;
  }

  .vertical-breather--margin.vertical-breather--extra-tight {
    margin-top: 48px;
    margin-bottom: 48px;
  }
}

/**
 * -------------------------------------------------------------
 * ANCHOR
 * Doc: https://gomakethings.com/how-to-prevent-anchor-links-from-scrolling-behind-a-sticky-header-with-one-line-of-css/
 * -------------------------------------------------------------
 */

.anchor {
  scroll-margin-top: calc(var(--header-height, 0px) * var(--enable-sticky-header) + var(--announcement-bar-height, 0px) * var(--enable-sticky-announcement-bar) + var(--anchor-offset, 20px));
}

.anchor.vertical-breather:not(.vertical-breather--margin) {
  --anchor-offset: 0px; /* There is already lot of space brought by vertical breather so no need for extra one */
}

.anchor.vertical-breather--margin {
  --anchor-offset: var(--vertical-breather); /* This is used to compensate the margin */
}

.anchor.vertical-breather--tight.vertical-breather--margin {
  --anchor-offset: var(--vertical-breather-tight);
}

/**
 * --------------------------------------------------------------------
 * ICON
 * --------------------------------------------------------------------
 */

.icon {
  display: block;
  vertical-align: middle;
  background: none;
  pointer-events: none;
  overflow: visible;
}

.icon--inline {
  display: inline-block;
}

/* Simple component allowing to align a text with an icon */
.icon-text {
  display: flex;
  align-items: center;
}

[dir="ltr"] .icon-text svg,[dir="ltr"]
.icon-text img {
  margin-right: 12px;
}

[dir="rtl"] .icon-text svg,[dir="rtl"]
.icon-text img {
  margin-left: 12px;
}

@supports (scale: 1) {
  [dir="rtl"] .icon--direction-aware {
    scale: -1 1; /* Allows to have better support if it is combined with other transforms */
  }
}

@supports not (scale: 1) {
  [dir="rtl"] .icon--direction-aware {
    transform: scale(-1, 1);
  }
}

/**
 * -------------------------------------------------------------
 * LIST
 * -------------------------------------------------------------
 */

.list--unstyled {
  list-style: none;
  padding: 0;
  margin: 0;
}

/**
 * --------------------------------------------------------------------
 * LOADING BAR
 * --------------------------------------------------------------------
 */

.loading-bar {
  position: fixed;
  top: 0;
  left: 0;
  height: 3px;
  width: 100%;
  opacity: 0;
  background: rgb(var(--loading-bar-background));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.25s ease-in-out;
  z-index: 50;
  pointer-events: none;
}

.loading-bar.is-visible {
  opacity: 1;
}

/**
 * -------------------------------------------------------------
 * SECTION
 * -------------------------------------------------------------
 */

.section {
  display: block;
  margin: var(--vertical-breather) 0;
}

.section--tight {
  margin-top: var(--vertical-breather-tight);
  margin-bottom: var(--vertical-breather-tight);
}

.section:empty {
  display: none;
}

/* This class is actually not really useful as you could just remove the "section" class to remove the padding. However
   for clarity, I preferred to assign the class "section" to every section, and then add an extra class conditionally to
   flush, as it makes it clear in the code that it is a section */
.section--flush {
  margin-top: 0;
  margin-bottom: 0;
}

.section__color-wrapper {
  display: flow-root; /* Modern clearfix */
  background: rgb(var(--section-background, var(--background)));
}

.section__color-wrapper--boxed {
  border-radius: var(--block-border-radius);
}

.section__header {
  max-width: 1000px;
  margin-bottom: min(32px, var(--vertical-breather));
  margin-left: auto;
  margin-right: auto;
  text-align: center;
}

.section__header:only-child {
  margin-bottom: 0;
}

.section__header--tight {
  max-width: 800px;
}

[dir="ltr"] .section__header--left {
  text-align: left;
}

[dir="rtl"] .section__header--left {
  text-align: right;
}

.section__header--left {
  margin-left: 0;
}

[dir="ltr"] .section__header--right {
  text-align: right;
}

[dir="rtl"] .section__header--right {
  text-align: left;
}

.section__header--right {
  margin-right: 0;
}

.section__footer {
  margin-top: 32px;
  text-align: center;
}

@media screen and (min-width: 741px) {
  .section__header {
    margin-bottom: min(40px, var(--vertical-breather));
  }

  .section__footer {
    margin-top: min(40px, var(--vertical-breather));
  }
}

@media screen and (min-width: 1000px) {
  .section__header {
    margin-bottom: min(48px, var(--vertical-breather));
  }

  .section__footer {
    margin-top: min(48px, var(--vertical-breather));
  }
}

/**
 * -------------------------------------------------------------
 * PAGE
 * -------------------------------------------------------------
 */

.page-header {
  position: relative;
  text-align: center;
}

.page-header--secondary {
  background: rgb(var(--secondary-background));
}

.page-header--clear::after,
.page-header::before {
  content: '';
  display: table;
  clear: left;
}

.page-header__text-wrapper {
  max-width: 850px;
  margin: 38px auto;
}

.page-header--small .page-header__text-wrapper {
  margin-top: 24px;
  margin-bottom: 24px;
}

.page-header--alone .page-header__text-wrapper {
  margin-bottom: 72px;
}

.page-content,
.shopify-policy__body {
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: min(var(--vertical-breather), 80px);
}

.page-content--medium,
.shopify-policy__body {
  max-width: 670px;
}

.page-content--small {
  max-width: 460px;
}

.page-content--fluid {
  max-width: none;
}

@media screen and (min-width: 741px) {
  .page-header__text-wrapper {
    margin-top: 68px;
    margin-bottom: 68px;
  }

  .page-header--small .page-header__text-wrapper {
    margin-top: 48px;
    margin-bottom: 40px;
  }

  .page-header--alone .page-header__text-wrapper {
    margin-bottom: 120px;
  }

  /* When the page header is immediately preceded by a floating breadcrumb, we increase the size */
  .breadcrumb--floating + .page-header__text-wrapper {
    margin-top: 80px;
  }
}

/**
 * -------------------------------------------------------------
 * BREADCRUMB
 * -------------------------------------------------------------
 */

.breadcrumb {
  z-index: 1;
}

[dir="ltr"] .breadcrumb--floating {
  left: 0;
}

[dir="rtl"] .breadcrumb--floating {
  right: 0;
}

.breadcrumb--floating {
  position: absolute;
  top: 0;
}

.breadcrumb__list {
  display: inline-flex;
  list-style: none;
  padding: 26px 0;
  margin: 0;
}

.breadcrumb__item + .breadcrumb__item::before {
  content: '/';
  margin: 0 4px;
  opacity: 0.7;
  float: left;
}

.breadcrumb__link {
  transition: opacity 0.2s ease-in-out;
}

.breadcrumb__link:not([aria-current="page"]):not(:hover) {
  opacity: 0.7;
}

[dir="rtl"] .breadcrumb__item + .breadcrumb__item::before {
  float: right;
}

/**
 * -------------------------------------------------------------
 * PAGINATION
 * -------------------------------------------------------------
 */

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

.pagination__nav {
  display: table;
  border-collapse: separate;
  table-layout: fixed;
}

.pagination__nav-item {
  position: relative;
  display: table-cell;
  box-shadow: 1px 0 0 0 rgb(var(--border-color)), 0 1px 0 0 rgb(var(--border-color)), 1px 1px 0 0 rgb(var(--border-color)), 1px 0 0 0 rgb(var(--border-color)) inset, 0 1px 0 0 rgb(var(--border-color)) inset;
  vertical-align: middle;
  height: 47px;
  width: 47px;
  text-align: center;
}

[dir="ltr"] .pagination__nav-item:first-child,[dir="ltr"]
.pagination__nav-item:first-child::before {
  border-top-left-radius: var(--button-border-radius);
}

[dir="rtl"] .pagination__nav-item:first-child,[dir="rtl"]
.pagination__nav-item:first-child::before {
  border-top-right-radius: var(--button-border-radius);
}

[dir="ltr"] .pagination__nav-item:first-child,[dir="ltr"]
.pagination__nav-item:first-child::before {
  border-bottom-left-radius: var(--button-border-radius);
}

[dir="rtl"] .pagination__nav-item:first-child,[dir="rtl"]
.pagination__nav-item:first-child::before {
  border-bottom-right-radius: var(--button-border-radius);
}

[dir="ltr"] .pagination__nav-item:last-child,[dir="ltr"]
.pagination__nav-item:last-child::before {
  border-top-right-radius: var(--button-border-radius);
}

[dir="rtl"] .pagination__nav-item:last-child,[dir="rtl"]
.pagination__nav-item:last-child::before {
  border-top-left-radius: var(--button-border-radius);
}

[dir="ltr"] .pagination__nav-item:last-child,[dir="ltr"]
.pagination__nav-item:last-child::before {
  border-bottom-right-radius: var(--button-border-radius);
}

[dir="rtl"] .pagination__nav-item:last-child,[dir="rtl"]
.pagination__nav-item:last-child::before {
  border-bottom-left-radius: var(--button-border-radius);
}

.pagination__nav-item svg {
  margin: 0 auto;
}

.pagination__nav-item[aria-current]::before {
  content: '';
  position: absolute;
  max-width: calc(100% - 3px);
  max-height: calc(100% - 3px);
  top: 2px;
  left: 2px;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
  box-shadow: 0 0 0 2px currentColor;
}

@media screen and (min-width: 741px) {
  .pagination {
    margin-top: 48px;
  }

  .pagination__nav-item {
    height: 56px;
    width: 56px;
  }
}

/**
 * -------------------------------------------------------------
 * LINKLIST
 * -------------------------------------------------------------
 */

.linklist__item:not(:first-child) {
  padding-top: 12px;
}

.linklist__item a {
  display: inline-block;
  word-break: break-word;
}

@media screen and (min-width: 1000px) {
  .linklist__item:not(:first-child) {
    padding-top: 6px; /* On desktop we use less space as we do not have as much constraint for tap area */
  }
}

/**
 * -------------------------------------------------------------
 * ANIMATED PLUS
 * -------------------------------------------------------------
 */

.animated-plus {
  position: relative;
  width: 10px;
  height: 10px;
}

.animated-plus::before,
.animated-plus::after {
  position: absolute;
  content: '';
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-90deg);
  background-color: currentColor;
  transition: transform 0.4s ease-in-out, opacity 0.4s ease-in-out;
}

.animated-plus::before {
  width: 10px;
  height: 2px;
  opacity: 1;
}

.animated-plus::after {
  width: 2px;
  height: 10px;
}

[aria-expanded="true"] > .animated-plus::before {
  opacity: 0;
}

[aria-expanded="true"] > .animated-plus::before,
[aria-expanded="true"] > .animated-plus::after {
  transform: translate(-50%, -50%) rotate(90deg);
}

/**
 * --------------------------------------------------------------------
 * ASPECT RATIO
 * --------------------------------------------------------------------
 */

.aspect-ratio {
  position: relative;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

/* The aspect-ratio can also contain a native HTML5 video element */
.aspect-ratio img,
.aspect-ratio video,
.aspect-ratio svg {
  position: absolute;
  height: 100%;
  width: 100%;
  max-width: 100%;
  max-height: 100%;
  top: 0;
  left: 0;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
}

.aspect-ratio--square img,
.aspect-ratio--short img,
.aspect-ratio--tall img {
  position: absolute;
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
  top: 50%;
  left: 50%; /* We must use this and not logical property here */
  transform: translate(-50%, -50%) !important;
}

.aspect-ratio--square {
  padding-bottom: 100% !important;
}

.aspect-ratio--short {
  padding-bottom: 75% !important;
}

.aspect-ratio--tall {
  padding-bottom: 150% !important;
}

@supports (aspect-ratio: 1 / 1) {
  .aspect-ratio {
    padding-bottom: 0 !important;
    aspect-ratio: var(--aspect-ratio);
  }

  .aspect-ratio--natural img,
  .aspect-ratio--natural video,
  .aspect-ratio--natural svg {
    position: relative;
    width: auto;
  }

  .aspect-ratio--square {
    aspect-ratio: 1;
  }

  .aspect-ratio--short {
    aspect-ratio: 4 / 3;
  }

  .aspect-ratio--tall {
    aspect-ratio: 2 / 3;
  }
}

/**
 * -------------------------------------------------------------
 * PLACEHOLDER
 * -------------------------------------------------------------
 */

.placeholder-image {
  position: relative;
  padding-bottom: 75%;
  background-color: rgb(var(--secondary-background));
}

.placeholder-background {
  background-color: rgb(var(--secondary-background));
}

[dir="ltr"] .placeholder-image svg {
  left: 0;
}

[dir="rtl"] .placeholder-image svg {
  right: 0;
}

.placeholder-image svg {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
}

@media screen and (min-width: 1000px) {
  .placeholder-image {
    padding-bottom: 45%;
  }
}

/**
 * -------------------------------------------------------------
 * PROGRESS BAR
 * -------------------------------------------------------------
 */

.progress-bar {
  display: block;
  position: relative;
  height: 2px;
  background: rgba(var(--text-color), 0.15);
}

[dir="ltr"] .progress-bar::before {
  left: 0;
}

[dir="rtl"] .progress-bar::before {
  right: 0;
}

.progress-bar::before {
  content: '';
  position: absolute;
  top: 0;
  width: calc(100% / var(--divider));
  height: 100%;
  transform: translateX(calc(var(--transform-logical-flip) * var(--transform, 0%) * (var(--divider) - 1)));
  transform-origin: var(--transform-origin-start);
  background: rgb(var(--text-color));
}

/**
 * -------------------------------------------------------------
 * CUSTOM DRAG CURSOR
 * -------------------------------------------------------------
 */

[draggable].is-scrollable {
  cursor: none;
}

.custom-drag-cursor {
  position: absolute;
  display: block;
  top: 0;
  left: 0; /* Must not use logical properties here !! */
  width: 60px;
  height: 60px;
  pointer-events: none;
  visibility: visible;
  transition: visibility 0.15s linear;
}

.custom-drag-cursor svg {
  transform: scale(1);
  opacity: 1;
  transition: transform 0.15s ease-in-out, opacity 0.15s ease-in-out;
}

.custom-drag-cursor[hidden] svg {
  transform: scale(0.5);
  opacity: 0;
}

@media screen and (max-width: 999px), not screen and (pointer: fine) {
  .custom-drag-cursor {
    display: none;
  }
}

/**
 * -------------------------------------------------------------
 * TAP AREA (this allows to increase the tap area on mobile)
 * -------------------------------------------------------------
 */

.tap-area {
  position: relative;
}

[dir="ltr"] .tap-area::before {
  right: -6px;
}

[dir="rtl"] .tap-area::before {
  left: -6px;
}

[dir="ltr"] .tap-area::before {
  left: -6px;
}

[dir="rtl"] .tap-area::before {
  right: -6px;
}

.tap-area::before {
  content: '';
  position: absolute;
  top: -6px;
  bottom: -6px;
}

[dir="ltr"] .tap-area--large::before {
  right: -10px;
}

[dir="rtl"] .tap-area--large::before {
  left: -10px;
}

[dir="ltr"] .tap-area--large::before {
  left: -10px;
}

[dir="rtl"] .tap-area--large::before {
  right: -10px;
}

.tap-area--large::before {
  top: -10px;
  bottom: -10px;
}

/**
 * --------------------------------------------------------------------
 * SCROLLER (only for mobile and desktop)
 * --------------------------------------------------------------------
 */

@media screen and (max-width: 999px) {
  .scroller {
    overflow-x: auto;
    overflow-y: hidden;
    scroll-snap-type: x mandatory;
    margin-left: calc(-1 * var(--container-gutter));
    margin-right: calc(-1 * var(--container-gutter)); /* On mobile we remove the container gutter to make sure the scroll is edge to edge */
    scrollbar-width: none; /* Hide scrollbar for Firefox */
  }

  .scroller::-webkit-scrollbar {
    display: none;  /* Hide scrollbar for Chrome and Safari */
  }

  .scroller__inner {
    min-width: min-content;
    padding-left: var(--container-gutter);
    padding-right: var(--container-gutter);
    margin-left: auto;
    margin-right: auto; /* margin rules allow to center the content if there are not enough elements to scroll */
  }
}

/**
 * -------------------------------------------------------------
 * HIDE SCROLLBAR
 * -------------------------------------------------------------
 */

.hide-scrollbar {
  scrollbar-width: none; /* For Firefox */
  overflow-x: auto;
  overflow-y: hidden;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;  /* For Chrome and Safari */
}

/**
 * --------------------------------------------------------------------
 * ANIMATION
 * --------------------------------------------------------------------
 */

.js .animated-element {
  visibility: hidden; /* An animated element will be an element that is dynamically transitioned */
}

/**
 * --------------------------------------------------------------------
 * SQUARE SEPARATOR
 * --------------------------------------------------------------------
 */

.square-separator {
  position: relative;
  display: inline-block;
  height: 4px;
  width: 4px;
  margin: 0 8px;
  flex-shrink: 0; /* in case it is a flex children */
  background: currentColor;
  vertical-align: middle;
}

.square-separator--block {
  top: 1px;
  margin-left: 12px;
  margin-right: 12px;
}

.square-separator--subdued {
  opacity: 0.5;
}

/**
 * --------------------------------------------------------------------
 * PREV NEXT BUTTONS (used in different places to navigate in scrollable elements)
 * --------------------------------------------------------------------
 */

/* Animation states that we apply to those buttons */
@keyframes prevNextButtonKeyframe {
  0% {
    transform: translateX(0%) scale(var(--transform-logical-flip), 1);
  }

  50% {
    transform: translateX(calc(50% + 10px)) scale(var(--transform-logical-flip), 1);
  }

  51% {
    transform: translateX(calc(-50% - 10px)) scale(var(--transform-logical-flip), 1);
  }

  100% {
    transform: translateX(0%) scale(var(--transform-logical-flip), 1);
  }
}

.prev-next-buttons {
  display: inline-grid;
  pointer-events: none;
}

.prev-next-buttons--row {
  grid-auto-flow: column;
}

.prev-next-button {
  display: flex;
  height: 40px;
  width: 40px;
  justify-content: center;
  align-items: center;
  background: rgb(var(--prev-next-button-background, var(--root-background)));
  color: rgb(var(--prev-next-button-color, var(--root-text-color)));
  border: 1px solid rgba(var(--prev-next-button-color, var(--root-text-color)), 0.15);
  border-radius: var(--button-border-radius);
  transition: color 0.2s ease-in-out;
  pointer-events: auto;
  overflow: hidden;
}

.prev-next-button[disabled] {
  color: rgba(var(--prev-next-button-color), 0.3);
}

.prev-next-button svg {
  width: 100%;
}

[dir="ltr"] .prev-next-buttons:not(.prev-next-buttons--row) .prev-next-button:first-child {
  border-bottom-right-radius: 0;
}

[dir="rtl"] .prev-next-buttons:not(.prev-next-buttons--row) .prev-next-button:first-child {
  border-bottom-left-radius: 0;
}

[dir="ltr"] .prev-next-buttons:not(.prev-next-buttons--row) .prev-next-button:first-child {
  border-bottom-left-radius: 0;
}

[dir="rtl"] .prev-next-buttons:not(.prev-next-buttons--row) .prev-next-button:first-child {
  border-bottom-right-radius: 0;
}

[dir="ltr"] .prev-next-buttons:not(.prev-next-buttons--row) .prev-next-button:last-child {
  border-top-right-radius: 0;
}

[dir="rtl"] .prev-next-buttons:not(.prev-next-buttons--row) .prev-next-button:last-child {
  border-top-left-radius: 0;
}

[dir="ltr"] .prev-next-buttons:not(.prev-next-buttons--row) .prev-next-button:last-child {
  border-top-left-radius: 0;
}

[dir="rtl"] .prev-next-buttons:not(.prev-next-buttons--row) .prev-next-button:last-child {
  border-top-right-radius: 0;
}

[dir="ltr"] .prev-next-buttons--row .prev-next-button:first-child {
  border-bottom-right-radius: 0;
}

[dir="rtl"] .prev-next-buttons--row .prev-next-button:first-child {
  border-bottom-left-radius: 0;
}

[dir="ltr"] .prev-next-buttons--row .prev-next-button:first-child {
  border-top-right-radius: 0;
}

[dir="rtl"] .prev-next-buttons--row .prev-next-button:first-child {
  border-top-left-radius: 0;
}

[dir="ltr"] .prev-next-buttons--row .prev-next-button:last-child {
  border-bottom-left-radius: 0;
}

[dir="rtl"] .prev-next-buttons--row .prev-next-button:last-child {
  border-bottom-right-radius: 0;
}

[dir="ltr"] .prev-next-buttons--row .prev-next-button:last-child {
  border-top-left-radius: 0;
}

[dir="rtl"] .prev-next-buttons--row .prev-next-button:last-child {
  border-top-right-radius: 0;
}

@media screen and (min-width: 741px) {
  .prev-next-button:not(.prev-next-button--small) {
    width: 56px;
    height: 56px;
  }

  .prev-next-buttons:not(.prev-next-buttons--row) .prev-next-button:last-child {
    border-top: none;
  }

  .prev-next-buttons--row .prev-next-button:last-child {
    border-left: none;
  }
}

@media screen and (pointer: fine) {
  .prev-next-button--prev:hover svg {
    animation: prevNextButtonKeyframe 0.3s ease-in-out reverse forwards;
  }

  .prev-next-button--next:hover svg {
    animation: prevNextButtonKeyframe 0.3s ease-in-out forwards;
  }
}

/**
 * --------------------------------------------------------------------
 * DOT NAVIGATION
 * --------------------------------------------------------------------
 */

.dots-nav {
  display: flex;
  margin: -6px;
  justify-content: flex-start;
  flex-wrap: wrap;
}

.dots-nav--centered {
  justify-content: center;
}

.dots-nav__item {
  position: relative;
  width: 6px;
  height: 6px;
  margin: 6px;
  background: rgb(var(--text-color));
  border-radius: min(var(--button-border-radius), 6px);
  opacity: 0.3;
  transition: opacity 0.2s ease-in-out;
}

.dots-nav__item[aria-current="true"] {
  opacity: 1;
}

/**
 * -------------------------------------------------------------
 * PRICE
 * -------------------------------------------------------------
 */

.price-list {
  display: inline-flex;
  flex-wrap: wrap;
  align-items: baseline;
}

.price-list--centered {
  justify-content: center;
}

.price-list--stack {
  display: inline-grid;
}

[dir="ltr"] .price-list:not(.price-list--stack) > .price:not(:last-child) {
  margin-right: 10px;
}

[dir="rtl"] .price-list:not(.price-list--stack) > .price:not(:last-child) {
  margin-left: 10px;
}

[dir="ltr"] .price-list > .price--block {
  margin-left: 0 !important;
}

[dir="rtl"] .price-list > .price--block {
  margin-right: 0 !important;
}

.price-list > .price--block {
  flex-basis: 100%;
}

[dir="ltr"] .price-list + .link {
  margin-left: 16px;
}

[dir="rtl"] .price-list + .link {
  margin-right: 16px;
}

.price--highlight {
  color: rgb(var(--product-on-sale-accent));
}

.price--compare {
  text-decoration: line-through;
  opacity: 0.7;
}

.price--large {
  font-size: calc(var(--base-font-size) + 3px);
}

.unit-price-measurement {
  display: inline-flex; /* This allows to remove the space between each elements */
}

@media screen and (min-width: 1000px) {
  .price--large:not(.price--compare) {
    font-size: calc(var(--base-font-size) + 7px);
  }
}

/**
 * --------------------------------------------------------------------
 * LABEL
 * --------------------------------------------------------------------
 */

.label {
  display: inline-block;
  padding: 0 5px;
  vertical-align: top;
  font-family: var(--text-font-family);
  font-style: var(--text-font-style);
  font-weight: var(--text-font-bold-weight);
  font-size: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-radius: min(var(--block-border-radius), 2px);
}

.label--highlight {
  background: rgb(var(--product-on-sale-accent));
  color: rgb(255, 255, 255);
}

.label--subdued {
  background: rgb(var(--product-sold-out-accent));
  color: rgb(255, 255, 255);
}

.label--custom {
  background: rgb(var(--product-custom-label-background));
  color: rgb(var(--product-custom-label-text-color));
}

.label--custom2 {
  background: rgb(var(--product-custom-label-2-background));
  color: rgb(var(--product-custom-label-2-text-color));
}

.label-list {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  pointer-events: none;
}

.label-list:not(.label-list--horizontal) .label:not(:last-child) {
  margin-bottom: 4px;
}

.label-list--horizontal {
  flex-direction: row;
}

[dir="ltr"] .label-list--horizontal .label:not(:last-child) {
  margin-right: 4px;
}

[dir="rtl"] .label-list--horizontal .label:not(:last-child) {
  margin-left: 4px;
}

@media screen and (min-width: 741px) {
  .label {
    font-size: 12px;
  }
}

/**
 * -------------------------------------------------------------
 * TAG
 * -------------------------------------------------------------
 */

.tag-list {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin: -6px;
}

[dir="ltr"] .tag {
  padding: 7px 14px 8px 13px;
}

[dir="rtl"] .tag {
  padding: 7px 13px 8px 14px;
}

.tag {
  display: flex;
  align-items: center;
  margin: 6px;
  background: rgba(var(--text-color), 0.05);
}

[dir="ltr"] .tag__icon {
  margin: 1px 9px 0 0;
}

[dir="rtl"] .tag__icon {
  margin: 1px 0 0 9px;
}

.tag__icon {
  position: relative; /* For pixel perfect precision ! */
  cursor: pointer;
}

[dir="ltr"] .tag-link {
  padding-left: 6px;
}

[dir="rtl"] .tag-link {
  padding-right: 6px;
}

/**
 * -------------------------------------------------------------
 * SOCIAL MEDIA
 * -------------------------------------------------------------
 */

.social-media {
  display: flex;
  flex-wrap: wrap;
}

.social-media__item {
  position: relative;
  box-shadow: 1px 0 0 0 rgb(var(--border-color)),0 1px 0 0 rgb(var(--border-color)),1px 1px 0 0 rgb(var(--border-color)),1px 0 0 0 rgb(var(--border-color)) inset,0 1px 0 0 rgb(var(--border-color)) inset;
  transform: translateZ(0); /* allow to promote on its own layer */
}

.no-focus-outline .social-media__item {
  overflow: hidden;
}

.social-media__item::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  background: currentColor;
  -webkit-clip-path: polygon(0 25%, 100% 0, 100% 100%, 0% 100%);
          clip-path: polygon(0 25%, 100% 0, 100% 100%, 0% 100%);
  transform: scaleY(0);
  transform-origin: bottom;
  transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1), -webkit-clip-path 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1), clip-path 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1), clip-path 0.3s cubic-bezier(0.215, 0.61, 0.355, 1), -webkit-clip-path 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  pointer-events: none;
  z-index: 1;
}

[dir="ltr"] .social-media:not(.social-media--no-radius) .social-media__item:first-child {
  border-top-left-radius: var(--button-border-radius);
}

[dir="rtl"] .social-media:not(.social-media--no-radius) .social-media__item:first-child {
  border-top-right-radius: var(--button-border-radius);
}

[dir="ltr"] .social-media:not(.social-media--no-radius) .social-media__item:first-child {
  border-bottom-left-radius: var(--button-border-radius);
}

[dir="rtl"] .social-media:not(.social-media--no-radius) .social-media__item:first-child {
  border-bottom-right-radius: var(--button-border-radius);
}

[dir="ltr"] .social-media:not(.social-media--no-radius) .social-media__item:last-child {
  border-top-right-radius: var(--button-border-radius);
}

[dir="rtl"] .social-media:not(.social-media--no-radius) .social-media__item:last-child {
  border-top-left-radius: var(--button-border-radius);
}

[dir="ltr"] .social-media:not(.social-media--no-radius) .social-media__item:last-child {
  border-bottom-right-radius: var(--button-border-radius);
}

[dir="rtl"] .social-media:not(.social-media--no-radius) .social-media__item:last-child {
  border-bottom-left-radius: var(--button-border-radius);
}

.social-media__link {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 45px;
  width: 45px;
  color: currentColor;
  z-index: 1;
  transition: color 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
}

@media screen and (min-width: 741px) {
  .social-media__link {
    height: 55px;
    width: 55px;
  }
}

@media screen and (pointer: fine) {
  .social-media__item:hover .social-media__link {
    color: rgb(var(--background));
  }

  .social-media__item:hover::before {
    -webkit-clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
            clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
    transform: scaleY(1);
  }
}

/**
 * -------------------------------------------------------------
 * BANNER
 * -------------------------------------------------------------
 */

[dir="ltr"] .banner {
  text-align: left;
}

[dir="rtl"] .banner {
  text-align: right;
}

.banner {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 12px 16px;
}

.banner--centered {
  justify-content: center;
}

.banner--margin {
  margin-top: 24px;
}

[dir="ltr"] .banner__ribbon {
  margin-right: 10px;
}

[dir="rtl"] .banner__ribbon {
  margin-left: 10px;
}

.banner__content {
  margin: 0;
}

.banner--success {
  --text-color: rgb(var(--success-color));

  background: rgb(var(--success-background));
  color: rgb(var(--success-color));
}

.banner--error {
  --text-color: rgb(var(--error-color));

  background: rgb(var(--error-background));
  color: rgb(var(--error-color));
}

[dir="ltr"] .banner__content ul {
  padding-left: 10px;
}

[dir="rtl"] .banner__content ul {
  padding-right: 10px;
}

.banner__content ul {
  list-style-position: inside;
}

@media screen and (min-width: 741px) {
  .banner {
    padding: 13px 18px;
  }
}

/**
 * -------------------------------------------------------------
 * TABS (used in product pages and for the collections section)
 * -------------------------------------------------------------
 */

.tabs-nav {
  position: relative;
  display: block;
  margin-bottom: 32px;
}

.tabs-nav:not(:first-child) {
  margin-top: 24px;
}

.tabs-nav__scroller {
  display: block;
}

.tabs-nav__scroller-inner {
  position: relative;
  line-height: 1;
}

.tabs-nav__item-list {
  display: inline-grid;
  grid-auto-flow: column;
  grid-auto-columns: max-content;
  justify-content: flex-start;
  gap: 32px;
  vertical-align: top;
  box-shadow: 0 -1px rgb(var(--border-color)) inset;
}

.tabs-nav__item {
  padding-bottom: 18px;
  opacity: 0.7;
  transition: opacity 0.25s ease-in-out;
}

.tabs-nav__item[aria-expanded="true"] {
  opacity: 1;
}

[dir="ltr"] .tabs-nav__position {
  left: 0;
}

[dir="rtl"] .tabs-nav__position {
  right: 0;
}

.tabs-nav__position {
  position: absolute;
  bottom: 0;
  height: 2px;
  width: 100%;
  background: currentColor;
  transform: scaleX(var(--scale, 0)) translateX(var(--translate, 0));
  transform-origin: left; /* Make sure to always use left here even in RTL, as the calculation is based on LTR */
}

.tabs-nav__position.is-initialized {
  transition: transform 0.4s ease-in-out;
}

@supports (scale: 0) {
  .tabs-nav__position {
    scale: var(--scale, 0) 1;
    translate: calc(var(--translate, 0) * var(--scale, 0));
    transform: none;
  }

  .tabs-nav__position.is-initialized {
    transition: scale 0.2s ease-in-out, translate 0.4s ease-in-out;
  }
}

/* Center variation */

.tabs-nav--center .tabs-nav__scroller-inner {
  max-width: max-content;
  margin-left: auto;
  margin-right: auto;
}

/* Arrow variation */

[dir="ltr"] .tabs-nav__arrows {
  right: 0;
}

[dir="rtl"] .tabs-nav__arrows {
  left: 0;
}

.tabs-nav__arrows {
  display: none;
  position: absolute;
  top: -5px;
  z-index: 1;
}

.tabs-nav__scroller.is-scrollable + .tabs-nav__arrows {
  display: flex;
}

.tabs-nav__arrow-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: rgb(var(--background));
  border: 1px solid rgb(var(--border-color));
}

[dir="ltr"] .tabs-nav__arrow-item + .tabs-nav__arrow-item {
  border-left: none;
}

[dir="rtl"] .tabs-nav__arrow-item + .tabs-nav__arrow-item {
  border-right: none;
}

.tabs-nav[arrows] .tabs-nav__scroller {
  overflow: hidden; /* Prevent to be manually scrollable as we want to allow scroll with arrows only */
}

[dir="ltr"] .tabs-nav[arrows] .tabs-nav__scroller.is-scrollable::before {
  right: 48px;
}

[dir="rtl"] .tabs-nav[arrows] .tabs-nav__scroller.is-scrollable::before {
  left: 48px;
}

.tabs-nav[arrows] .tabs-nav__scroller.is-scrollable::before {
  content: '';
  position: absolute;
  width: 48px;
  height: 100%;
  top: -2px; /* Prevent to overlap the bottom border */ /* Shift by the arrows size */
  z-index: 1;
  pointer-events: none;
  background: linear-gradient(to var(--transform-origin-start), rgb(var(--section-background, var(--background))), rgba(var(--section-background, var(--background)), 0));
}

[dir="ltr"] .tabs-nav[arrows] .tabs-nav__item-list {
  margin-right: 0;
}

[dir="rtl"] .tabs-nav[arrows] .tabs-nav__item-list {
  margin-left: 0;
}

.tabs-nav[arrows] .tabs-nav__item-list {
  width: 100%; /* In arrow mode we want to extend the list all the time */
  min-width: max-content;
}

.tabs-nav[arrows] .tabs-nav__item-list::after {
  display: block;
  content: '';
  width: 35px;
}

/* No border variation */

.tabs-nav--no-border.tabs-nav--narrow {
  margin-bottom: 24px; /* When there is no bottom border spacing with next elements is closer */
}

.tabs-nav--no-border .tabs-nav__item-list {
  box-shadow: none;
}

.tabs-nav--no-border.tabs-nav--narrow .tabs-nav__item {
  padding-bottom: 5px; /* Due to the lack of border we reduce the spacing with the moving link */
}

@media screen and (max-width: 999px) {
  /* On phone and tablet, when the tab nav is contained within a section header, we stretch it to be edge to edge */
  .tabs-nav--edge2edge {
    margin-left: calc(-1 * var(--container-gutter));
    margin-right: calc(-1 * var(--container-gutter));
  }

  .tabs-nav--edge2edge .tabs-nav__scroller-inner {
    padding-left: var(--container-gutter);
    padding-right: var(--container-gutter);
    min-width: max-content;
  }
}

@media screen and (min-width: 741px) {
  .tabs-nav:not(:first-child) {
    margin-top: 32px;
  }

  .tabs-nav--no-border.tabs-nav--narrow {
    margin-bottom: 32px; /* When there is no bottom border spacing with next elements is closer */
  }

  .tabs-nav__item-list {
    gap: 54px;
  }

  .tabs-nav--loose .tabs-nav__item-list {
    gap: 72px;
  }

  .tabs-nav--narrow .tabs-nav__item-list {
    gap: 40px;
  }
}

/**
 * -------------------------------------------------------------
 * EMPTY STATE
 * -------------------------------------------------------------
 */

.empty-state {
  position: relative;
  text-align: center;
  margin: 100px 0;
}

.empty-state--bottom-only {
  margin-top: 24px;
}

[dir="ltr"] .empty-state__background-text {
  left: 0;
}

[dir="rtl"] .empty-state__background-text {
  right: 0;
}

.empty-state__background-text {
  position: absolute;
  width: 100%;
  margin-top: -20px;
  text-align: center;
  font-size: 120px;
  opacity: 0.05;
  font-weight: bold;
  line-height: 0;
}

@media screen and (min-width: 741px) {
  .empty-state {
    margin-top: 150px;
    margin-bottom: 150px;
  }

  .empty-state--bottom-only {
    margin-top: 50px;
  }

  .empty-state__background-text {
    position: absolute;
    margin-top: -35px;
    font-size: 200px;
  }
}

@media screen and (min-width: 1200px) {
  .empty-state {
    margin-top: 225px;
    margin-bottom: 225px;
  }

  .empty-state--bottom-only {
    margin-top: 50px;
  }
}

/**
 * -------------------------------------------------------------
 * BUBBLE COUNT (used in various places to show a count)
 * -------------------------------------------------------------
 */

.bubble-count {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: rgb(var(--background));
  background: rgb(var(--heading-color));
  font-weight: var(--text-font-bold-weight);
  font-family: var(--text-font-family);
  font-style: var(--text-font-style);
  font-size: 9px;
  height: 21px;
  min-width: 21px;
  line-height: 1;
  border-radius: 21px;
  letter-spacing: 0;
  transition: background 0.2s ease-in-out, color 0.2s ease-in-out;
}

.bubble-count--top {
  vertical-align: top;
}

/**
 * --------------------------------------------------------------------
 * QUANTITY SELECTOR
 * --------------------------------------------------------------------
 */

.quantity-selector {
  --quantity-selector-height: 46px;
  display: inline-flex;
  align-items: center;
  justify-content: flex-start;
  border: 1px solid rgb(var(--border-color));
  border-radius: var(--button-border-radius);
  vertical-align: middle;
}

.quantity-selector__button {
  display: flex;
  justify-content: center;
  align-items: center;
  height: var(--quantity-selector-height);
  width: var(--quantity-selector-height);
}

.quantity-selector__input {
  padding: 0 10px;
  height: var(--quantity-selector-height);
  line-height: var(--quantity-selector-height);
  text-align: center;
  background: transparent;
  border: none;
  -webkit-appearance: none;
          appearance: none;
}

.quantity-selector--small {
  --quantity-selector-height: 28px;
}

.quantity-selector--small .quantity-selector__input {
  padding: 0 2px;
}


/**
 * --------------------------------------------------------------------
 * SPINNER
 * --------------------------------------------------------------------
 */

@keyframes spinnerRotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes spinnerDash {
  0% {
    stroke-dasharray: 1,200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 89,200;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 89,200;
    stroke-dashoffset: -124;
  }
}

@keyframes spinnerColor {
  0% {
    stroke: currentColor;
  }
  40% {
    stroke: currentColor;
  }
  66% {
    stroke: currentColor;
  }
  80%, 90% {
    stroke: currentColor;
  }
}

.spinner svg {
  margin: auto;
  animation: spinnerRotate 2s linear infinite;
  transform-origin: center center;
}

.spinner circle {
  animation: spinnerDash 1.5s ease-in-out infinite, spinnerColor 6s ease-in-out infinite;
}

/**
 * --------------------------------------------------------------------
 * TOOLTIP
 * --------------------------------------------------------------------
 */

[data-tooltip] {
  position: relative;
}

[data-tooltip]::before {
  position: absolute;
  content: attr(data-tooltip);
  bottom: calc(100% + 6px);
  left: 50%;
  padding: 5px 10px;
  white-space: nowrap;
  background: rgb(var(--heading-color));
  color: rgb(var(--background));
  font-size: calc(var(--base-font-size) - 2px);
  pointer-events: none;
  visibility: hidden;
  opacity: 0;
  transition: visibility 0.2s ease-in-out, opacity 0.2s ease-in-out;
  z-index: 1;
  transform: translateX(-50%);
}

[data-tooltip]::after {
  position: absolute;
  content: '';
  left: calc(50% - 7px);
  bottom: calc(100% + 1px);
  width: 0;
  height: 0;
  border-width: 6px;
  border-style: solid;
  border-color: transparent transparent rgb(var(--heading-color)) rgb(var(--heading-color));
  visibility: hidden;
  z-index: 1;
  opacity: 0;
  transition: visibility 0.2s ease-in-out, opacity 0.2s ease-in-out;
  transform: rotate(-45deg);
  box-shadow: -1px 1px 1px 0 rgba(0, 0, 0, 0.1);
}

[data-tooltip]:hover::before, [data-tooltip]:hover::after {
  opacity: 1;
  visibility: visible;
}

[data-tooltip-position="bottom-left"]::before {
  top: calc(100% + 4px);
  bottom: auto;
  left: auto;
  right: -6px;
  transform: none;
}

[data-tooltip-position="bottom-left"]::after {
  top: calc(100% - 1px);
  transform: rotate(135deg);
  left: calc(50% - 6px);
}

/**
 * --------------------------------------------------------------------
 * CART NOTIFICATION
 * --------------------------------------------------------------------
 */

.cart-notification {
  --heading-color: 255, 255, 255;
  --text-color: 255, 255, 255;
  --cart-notification-background: rgb(var(--success-color));

  display: block;
  position: absolute;
  top: 100%;
  width: 100%;
  transform: translateY(var(--cart-notification-offset, 0px));
  color: rgb(var(--text-color));
  transition: visibility 0.25s ease-in-out, transform 0.25s ease-in-out;
  visibility: visible;
  overflow: hidden;
}

.cart-notification--error {
  --cart-notification-background: rgb(var(--error-color));
}

.cart-notification--drawer {
  --cart-notification-offset: 0;
  top: var(--header-height-without-bottom-nav);
  z-index: 1;
}

.cart-notification--fixed {
  position: fixed;
  top: 0;
}

.cart-notification[hidden] {
  visibility: hidden;
}

.cart-notification__overflow {
  background: var(--cart-notification-background);
  transform: translateY(0);
  transition: opacity 0.25s ease-in-out, transform 0.25s ease-in-out;
}

.cart-notification[hidden] .cart-notification__overflow {
  transform: translateY(-100%);
  opacity: 0;
}

.cart-notification__wrapper {
  position: relative;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 14px;
  padding-bottom: 14px;
}

.cart-notification .icon--cart-notification {
  position: relative;
  top: 2px;
}

[dir="ltr"] .cart-notification__text-wrapper {
  margin-left: 12px;
}

[dir="rtl"] .cart-notification__text-wrapper {
  margin-right: 12px;
}

.cart-notification__text-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
}

[dir="ltr"] .cart-notification__heading {
  margin-right: 12px;
}

[dir="rtl"] .cart-notification__heading {
  margin-left: 12px;
}

.cart-notification__heading {
  font-size: 14px;
}

[dir="ltr"] .cart-notification__close {
  right: 0;
}

[dir="rtl"] .cart-notification__close {
  left: 0;
}

.cart-notification__close {
  position: absolute;
  margin-top: -1px;
}

.cart-notification--drawer .cart-notification__text-wrapper {
  flex-grow: 1;
  justify-content: space-between;
}

@media screen and (max-width: 740px) {
  .cart-notification__text-wrapper {
    flex-grow: 1;
    justify-content: space-between;
  }
}

@media screen and (max-width: 999px) {
  .cart-notification {
    transform: none !important; /* You cannot have any offset on pocket */
  }
}

@media screen and (min-width: 741px) {
  .cart-notification__wrapper {
    padding-top: 19px;
    padding-bottom: 19px;
  }

  .cart-notification .icon--cart-notification {
    top: 4px;
  }

  [dir="ltr"] .cart-notification__heading {
    margin-right: 16px;
  }

  [dir="rtl"] .cart-notification__heading {
    margin-left: 16px;
  }

  .cart-notification__heading {
    font-size: 16px;
  }

  .cart-notification__close svg {
    width: 15px;
    height: 15px;
  }
}

/**
 * -------------------------------------------------------------
 * PAYMENT METHODS
 * -------------------------------------------------------------
 */

.payment-methods-list {
  display: grid;
  grid-gap: 8px;
  gap: 8px;
  grid-template-columns: repeat(auto-fit, 38px);
}

.payment-methods-list--center {
  justify-content: center;
}

@media screen and (min-width: 741px) {
  .payment-methods-list--auto {
    grid-auto-flow: column;
  }
}

/**
 * -------------------------------------------------------------
 * LINK BAR (show a list of links usually for tags ; used in collection and blog pages)
 * -------------------------------------------------------------
 */

.link-bar {
  position: relative;
  display: block;
  box-shadow: 0 1px rgb(var(--border-color)), 0 -1px rgb(var(--border-color));
  text-align: center;
}

.link-bar__wrapper {
  position: relative;
  display: inline-flex;
  align-items: center;
  max-width: 100%;
}

.link-bar__scroller {
  scroll-snap-type: x proximity;
}

.link-bar__title {
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  flex-shrink: 0;
  z-index: 1;
  background: rgb(var(--background));
}

.link-bar__title::after {
  content: '';
  position: absolute;
  height: 100%;
  top: 0;
  width: 28px;
  background-image: linear-gradient(to var(--transform-origin-end), rgb(var(--background)) 35%, rgba(var(--background), 0));
}

[dir="ltr"] .link-bar__title + .link-bar__scroller {
  padding-left: 28px;
}

[dir="rtl"] .link-bar__title + .link-bar__scroller {
  padding-right: 28px;
}

.link-bar__linklist {
  display: grid;
  grid-auto-flow: column;
  align-items: center;
  grid-gap: 28px;
  gap: 28px;
  min-width: max-content;
}

.link-bar__link-item,
.link-bar__title {
  padding-top: 14px;
  padding-bottom: 14px;
}

.link-bar__link-item--selected {
  scroll-snap-align: center;
}

@media screen and (max-width: 999px) {
  [dir="ltr"] .link-bar__wrapper::after {
    left: 100%;
  }
  [dir="rtl"] .link-bar__wrapper::after {
    right: 100%;
  }
  .link-bar__wrapper::after {
    content: '';
    position: absolute;
    height: 100%;
    width: var(--container-gutter);
    top: 0;
    background-image: linear-gradient(to var(--transform-origin-start), rgb(var(--background)), rgba(var(--background), 0));
  }

  [dir="ltr"] .link-bar__scroller {
    margin-right: calc(-1 * var(--container-gutter));
  }

  [dir="rtl"] .link-bar__scroller {
    margin-left: calc(-1 * var(--container-gutter));
  }

  [dir="ltr"] .link-bar__linklist {
    padding-right: var(--container-gutter);
  }

  [dir="rtl"] .link-bar__linklist {
    padding-left: var(--container-gutter);
  }
}

@media screen and (min-width: 741px) {
  [dir="ltr"] .link-bar__title + .link-bar__scroller {
    padding-left: 48px;
  }
  [dir="rtl"] .link-bar__title + .link-bar__scroller {
    padding-right: 48px;
  }

  .link-bar__linklist {
    gap: 40px;
  }

  .link-bar__link-item,
  .link-bar__title {
    padding-top: 20px;
    padding-bottom: 20px;
  }
}

@media screen and (min-width: 1200px) {
  .link-bar__link-item,
  .link-bar__title {
    padding-top: 27px;
    padding-bottom: 27px;
  }
}

/**
 * -------------------------------------------------------------
 * MOBILE SHARE BUTTONS
 *
 * Those are used in product pages and blog posts
 * -------------------------------------------------------------
 */

.mobile-share-buttons__item {
  display: flex;
  align-items: center;
  padding-top: 20px;
  padding-bottom: 20px;
  margin-left: 24px;
  margin-right: 24px;
}

@supports (padding: max(0px)) {
  .mobile-share-buttons {
    padding-bottom: max(20px, env(safe-area-inset-bottom) + 20px);
  }
}

.mobile-share-buttons__item:not(:last-child) {
  border-bottom: 1px solid rgb(var(--border-color));
}

[dir="ltr"] .mobile-share-buttons__item svg {
  margin-right: 16px;
}

[dir="rtl"] .mobile-share-buttons__item svg {
  margin-left: 16px;
}

/**
 * --------------------------------------------------------------------
 * MOBILE TOOLBAR
 *
 * Those are used on collection page and account pages to create links
 * to sub-section
 * --------------------------------------------------------------------
 */

.mobile-toolbar {
  position: -webkit-sticky;
  position: sticky;
  top: calc(var(--enable-sticky-header) * var(--header-height, 0px) + var(--announcement-bar-height, 0px) * var(--enable-sticky-announcement-bar));
  display: flex;
  align-items: center;
  visibility: visible;
  z-index: 2;
  transition: margin-top 0.2s ease-in-out, visibility 0.2s ease-in-out;
}

.mobile-toolbar.is-collapsed {
  margin-top: -48px;
  visibility: hidden;
}

.mobile-toolbar--fixed {
  position: relative;
  top: 0;
}

.mobile-toolbar__item {
  display: flex;
  flex: 1 0 0;
  justify-content: center;
  align-items: center;
  padding: 11px;
  border-top: 1px solid rgb(var(--border-color));
  border-bottom: 1px solid rgb(var(--border-color));
  background: rgb(var(--background));
}

[dir="ltr"] .mobile-toolbar__item + .mobile-toolbar__item {
  border-left: 1px solid rgb(var(--border-color));
}

[dir="rtl"] .mobile-toolbar__item + .mobile-toolbar__item {
  border-right: 1px solid rgb(var(--border-color));
}

[dir="ltr"] .mobile-toolbar__item .icon--chevron {
  margin-left: 10px;
}

[dir="rtl"] .mobile-toolbar__item .icon--chevron {
  margin-right: 10px;
}

/**
 * -------------------------------------------------------------
 * COMBO BOX
 *
 * Implementation note: on mobile the combo-box is largely similar to the
 * popover and therefore re-use lot of styles. I preferred doing some
 * duplication here rather than rely on JavaScript to dynamically transformed
 * a non-popover on desktop to a popover
 * -------------------------------------------------------------
 */

@media screen and (max-width: 740px) {
  [dir="ltr"] .combo-box {
    left: 0;
  }
  [dir="rtl"] .combo-box {
    right: 0;
  }
  .combo-box {
    --heading-color: var(--root-heading-color);
    --text-color: var(--root-text-color);
    --background: var(--root-background);

    display: flex;
    position: fixed;
    z-index: 10;
    color: rgb(var(--text-color));
    background: rgb(var(--background));
    visibility: hidden;
    flex-direction: column;
    bottom: 0;
    width: 100vw;
    max-height: 75vh;
    border-radius: 10px 10px 0 0;
    transform: translateY(100%);
    transition: transform 0.7s cubic-bezier(0.75, 0, 0.175, 1), visibility 0.7s cubic-bezier(0.75, 0, 0.175, 1);
  }

  .combo-box[open] {
    visibility: visible;
    transform: translateY(0);
  }

  [dir="ltr"] .combo-box__overlay {
    left: 0;
  }

  [dir="rtl"] .combo-box__overlay {
    right: 0;
  }

  .combo-box__overlay {
    position: absolute;
    content: '';
    height: 100vh;
    width: 100%;
    bottom: calc(100% - 10px); /* There is a border radius on the header on mobile so we slightly move down the overlay */
    background: #000000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.5s ease-in-out, visibility 0.5s ease-in-out;
  }

  .combo-box__header {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 18px 32px;
    min-height: 64px;
    text-align: center;
    border-radius: 10px 10px 0 0;
    box-shadow: 0 1px rgb(var(--border-color));
  }

  .combo-box__header,
  .combo-box__content {
    background: inherit;
  }

  .combo-box__title {
    margin-bottom: 0;
  }

  [dir="ltr"] .combo-box__close-button {
    right: 24px;
  }

  [dir="rtl"] .combo-box__close-button {
    left: 24px;
  }

  .combo-box__close-button {
    position: absolute;
    top: 24px;
    z-index: 1;
  }

  .combo-box[open] > .combo-box__overlay {
    visibility: visible;
    opacity: 0.3;
  }

  .combo-box__option-list {
    overflow-x: hidden;
    overflow-y: auto;
    padding-left: 24px;
    padding-right: 24px;
    padding-bottom: max(16px, env(safe-area-inset-bottom, 0px) + 16px);
  }

  .combo-box__option-item {
    position: relative;
    width: 100%;
    padding-top: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgb(var(--border-color));
  }

  .combo-box__option-item:not([hidden]) {
    display: block;
  }

  .combo-box__option-item:last-child {
    border-bottom: none;
  }

  [dir="ltr"] .combo-box__option-item[aria-selected="true"]::after {
    margin-left: 12px;
  }

  [dir="rtl"] .combo-box__option-item[aria-selected="true"]::after {
    margin-right: 12px;
  }

  .combo-box__option-item[aria-selected="true"]::after {
    content: '';
    display: inline-block;
    width: 12px;
    height: 9px;
    background-color: currentColor;
    -webkit-mask-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEgMy40TDQuNzQ5MzEgN0wxMSAxIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIi8+PC9zdmc+);
            mask-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEgMy40TDQuNzQ5MzEgN0wxMSAxIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIi8+PC9zdmc+);
    -webkit-mask-repeat: no-repeat;
            mask-repeat: no-repeat;
    -webkit-mask-size: 12px 9px;
            mask-size: 12px 9px;
  }

  .combo-box__option-item.is-disabled {
    color: rgba(var(--text-color), 0.5);
  }
}

@media screen and (min-width: 741px) {
  [dir="ltr"] .combo-box {
    left: 0;
  }
  [dir="rtl"] .combo-box {
    right: 0;
  }
  .combo-box {
    position: absolute;
    display: block;
    top: 100%;
    width: 100%;
    max-height: 245px;
    overscroll-behavior: contain;
    overflow: auto;
    background: rgb(var(--background));
    z-index: 2;
    padding: 8px 0;
    border: 1px solid rgb(var(--border-color));
    border-top: none;
    border-radius: 0 0 var(--button-border-radius) var(--button-border-radius);
    transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
  }

  .combo-box--top {
    top: auto;
    bottom: 100%;
    border-top: 1px solid rgb(var(--border-color));
    border-bottom: none;
    border-radius: var(--button-border-radius) var(--button-border-radius) 0 0;
  }

  .combo-box:not([open]) {
    visibility: hidden;
    opacity: 0;
  }

  .combo-box__header {
    display: none; /* Nothing on tablet and up */
  }

  .combo-box__option-list {
    min-width: max-content;
  }

  .combo-box__option-item:not([hidden]) {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 8px 18px;
    transition: background 0.2s ease-in-out;
    text-align: left;
  }

  .combo-box__option-item.is-disabled {
    color: rgba(var(--text-color), 0.5);
  }

  .combo-box__option-item:hover,
  .combo-box__option-item:focus {
    background: rgb(var(--secondary-background));
  }

  [dir="ltr"] .combo-box__color-swatch {
    margin-right: 10px;
  }

  [dir="rtl"] .combo-box__color-swatch {
    margin-left: 10px;
  }

  .combo-box__color-swatch {
    width: 16px;
    height: 16px;
    border-radius: var(--color-swatch-border-radius);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
  }

  /* Adjustment to the toggle button */
  .combo-box + .select {
    transition: border-radius 0.2s ease-in-out;
  }

  [dir="ltr"] .combo-box[open] + .select {
    border-bottom-left-radius: 0;
  }

  [dir="rtl"] .combo-box[open] + .select {
    border-bottom-right-radius: 0;
  }

  [dir="ltr"] .combo-box[open] + .select {
    border-bottom-right-radius: 0;
  }

  [dir="rtl"] .combo-box[open] + .select {
    border-bottom-left-radius: 0;
  }

  [dir="ltr"] .combo-box--top[open] + .select {
    border-top-left-radius: 0;
  }

  [dir="rtl"] .combo-box--top[open] + .select {
    border-top-right-radius: 0;
  }

  [dir="ltr"] .combo-box--top[open] + .select {
    border-top-right-radius: 0;
  }

  [dir="rtl"] .combo-box--top[open] + .select {
    border-top-left-radius: 0;
  }
}

/**
 * -------------------------------------------------------------
 * PRICE RANGE
 * -------------------------------------------------------------
 */

.price-range {
  display: block;
  padding-top: 5px;
}

.price-range__input-group {
  display: flex;
  align-items: center;
}

.price-range__input {
  flex: 1 0 0;
  min-width: 0; /* Required for Firefox */
}

.price-range__delimiter {
  margin-left: 20px;
  margin-right: 20px;
}

.price-range__range-group {
  position: relative;
  margin-bottom: 15px;
}

.no-js .price-range__range-group {
  display: none !important; /* When JS is disabled we only rely on the input field */
}

@media not screen and (pointer: fine) {
  /* On non-touch device the thumb are bigger so we need to adjust the spacing */
  .price-range {
    padding-top: 7px;
  }

  .price-range__range-group {
    margin-bottom: 18px;
  }
}

/**
 * -------------------------------------------------------------
 * SCROLL SPY
 * -------------------------------------------------------------
 */

.scroll-spy {
  display: block;
  position: -webkit-sticky;
  position: sticky;
  box-shadow: 1px 0 rgba(var(--text-color), 0.25) inset;
  top: calc(24px + var(--header-height, 0px) * var(--enable-sticky-header) + var(--announcement-bar-height, 0px) * var(--enable-sticky-announcement-bar));
}

.scroll-spy__list {
  padding-left: 6px;
}

.scroll-spy__item {
  opacity: 0.7;
  transition: opacity 0.25s ease-in-out;
}

.scroll-spy__item.is-visible {
  opacity: 1;
}

.scroll-spy__anchor {
  display: block;
  padding: 10px 24px;
}

[dir="ltr"] .scroll-spy svg {
  left: 0;
}

[dir="rtl"] .scroll-spy svg {
  right: 0;
}

.scroll-spy svg {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.scroll-spy path {
  transition: all 0.3s ease;
  fill: transparent;
  stroke: currentColor;
  stroke-width: 2px;
  stroke-dasharray: 0 0 0 1000;
  stroke-linecap: square;
}

/**
 * -------------------------------------------------------------
 * SPLIT LINE (custom element)
 * -------------------------------------------------------------
 */

split-lines {
  display: block;
}

/**
 * -------------------------------------------------------------
 * EFFECT
 * -------------------------------------------------------------
 */

@media screen and (pointer: fine) {
  .features--image-zoom .image-zoom img {
    transition: transform 0.5s ease;
    transform: translateZ(0); /* Allow to promote on its own layer */
  }

  .features--image-zoom .image-zoom:hover img {
    transform: scale(1.03);
  }
}

/**
 * -------------------------------------------------------------
 * RATING
 * -------------------------------------------------------------
 */

.rating {
  display: inline-flex;
  align-items: center;
  vertical-align: bottom;
}

.rating__stars {
  display: grid;
  grid-auto-flow: column;
  grid-column-gap: 2px;
  column-gap: 2px;
}

.rating__star {
  color: rgb(var(--product-star-rating));
}

.rating__star--empty {
  color: rgba(var(--product-star-rating), 0.4);
}

[dir="ltr"] .rating__caption {
  margin-left: 8px;
}

[dir="rtl"] .rating__caption {
  margin-right: 8px;
}

/**
 * -------------------------------------------------------------
 * OPENABLE
 * -------------------------------------------------------------
 */

.openable__overlay {
  position: absolute;
  content: '';
  height: 100vh;
  width: 100%;
  bottom: 100%;
  left: 0; /* We must make sure to not use logical property here */
  background: #000000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.6s ease-in-out, visibility 0.6s ease-in-out;
}

[open] > .openable__overlay {
  visibility: visible;
  opacity: 0.2;
} /* Make sure to keep this order the same as it relies on inheritance */
/*
 * FORM GENERIC
 */

.form__banner:not(:last-child),
.form__info {
  margin-bottom: 24px;
}

.form__info {
  margin-top: 0;
}

.form__submit {
  margin-top: var(--form-submit-margin);
}

.form__submit--closer {
  margin-top: 16px;
}

/* Element that is added below a submit form to provide a secondary, subdued action */
.form__secondary-action {
  display: block;
  width: 100%;
  margin-top: 18px;
  text-align: center;
}

.form__secondary-action .link {
  padding-left: 8px;
}

@media screen and (min-width: 1000px) {
  .form__banner:not(:last-child),
  .form__info {
    margin-bottom: 32px;
  }
}

/*
 * INPUT
 */

.input {
  position: relative;
}

.input + .input,
.input + .input-row,
.input-row + .input-row,
.input-row + .input {
  margin-top: var(--form-input-gap);
}

[dir="ltr"] .input__field,[dir="ltr"]
#shopify-product-reviews .spr-form-input-text,[dir="ltr"]
#shopify-product-reviews .spr-form-input-email,[dir="ltr"]
#shopify-product-reviews .spr-form-input-textarea {
  text-align: left;
}

[dir="rtl"] .input__field,[dir="rtl"]
#shopify-product-reviews .spr-form-input-text,[dir="rtl"]
#shopify-product-reviews .spr-form-input-email,[dir="rtl"]
#shopify-product-reviews .spr-form-input-textarea {
  text-align: right;
}

.input__field,
#shopify-product-reviews .spr-form-input-text,
#shopify-product-reviews .spr-form-input-email,
#shopify-product-reviews .spr-form-input-textarea {
  -webkit-appearance: none;
          appearance: none;
  width: 100%;
  height: var(--form-input-field-height);
  line-height: var(--form-input-field-height);
  padding: 0 18px;
  border-radius: var(--button-border-radius);
  border: 1px solid rgba(var(--text-color), 0.15);
  box-shadow: none;
  background: transparent;
  color: var(--form-input-color);
  transition: border 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.input__field:focus,
select:focus,
#shopify-product-reviews .spr-form-input-text:focus,
#shopify-product-reviews .spr-form-input-email:focus,
#shopify-product-reviews .spr-form-input-textarea:focus {
  border-color: rgb(var(--text-color));
  box-shadow: 0 0 0 1px rgb(var(--text-color)) inset;
  outline: none;
}

.input__field::placeholder {
  color: rgba(var(--text-color), 0.7);
}

.input__field--transparent {
  background: transparent;
}

.input__field--textarea,
#shopify-product-reviews .spr-form-input-textarea {
  resize: vertical;
  height: auto;
  padding-top: 12px;
  padding-bottom: 12px;
  vertical-align: top;
  line-height: inherit;
}

[dir="ltr"] .input__label {
  left: 12px;
}

[dir="rtl"] .input__label {
  right: 12px;
}

.input__label {
  position: absolute;
  top: calc((var(--form-input-field-height) / 2) - 0.5em);
  padding: 0 5px;
  pointer-events: none;
  transform: translateY(0);
  transform-origin: var(--transform-origin-start) top;
  transition: transform 0.2s ease-in-out;
  background: rgb(var(--section-block-background, var(--section-background, var(--background)))); /* Try to inherit from specific section background first, then global background */
  color: rgba(var(--text-color), 0.7);
  line-height: 1;
  white-space: nowrap;
}

[focus-within] ~ .input__label,
.is-filled ~ .input__label {
  transform: scale(0.733) translateY(calc(-24px - 0.5em)) translateX(calc(5px * 0.733));
}

:focus-within ~ .input__label,
.is-filled ~ .input__label {
  transform: scale(0.733) translateY(calc(-24px - 0.5em)) translateX(calc(5px * 0.733));
}

/* Block label is outside the field itself (used on very few forms actually) */
.input__block-label {
  display: inline-block;
  margin-bottom: 8px;
}

/* Used for instance in the login form for the "forgot password" link */
[dir="ltr"] .input__field-link {
  right: 18px;
}
[dir="rtl"] .input__field-link {
  left: 18px;
}
.input__field-link {
  position: absolute;
  top: 1.1em;
}

/* This icon is placed within an input div */
[dir="ltr"] .input__submit-icon {
  right: 20px;
}
[dir="rtl"] .input__submit-icon {
  left: 20px;
}
.input__submit-icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.input-row .button {
  width: 100%;
}

@media screen and (min-width: 741px) {
  .input-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(10px, 1fr));
    grid-gap: var(--form-input-gap);
    gap: var(--form-input-gap);
  }

  .input-row .input {
    margin-top: 0;
  }

  .input + .input--checkbox,
  .input-row + .input--checkbox {
    margin-top: 30px;
  }

  [dir="ltr"] .input__field-link {
    right: 18px;
  }

  [dir="rtl"] .input__field-link {
    left: 18px;
  }
}

/*
 * INPUT PREFIX (allow to create small input with a prefixed value)
 */

.input-prefix {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 10px;
  border: 1px solid rgb(var(--border-color));
}

[dir="ltr"] .input-prefix__field {
  text-align: right;
}

[dir="rtl"] .input-prefix__field {
  text-align: left;
}

.input-prefix__field {
  padding: 0;
  background: transparent;
  -webkit-appearance: none;
          appearance: none;
  -moz-appearance: textfield;
  min-width: 0;
  width: 100%;
  border: none;
}

.input-prefix__field::-webkit-outer-spin-button,
.input-prefix__field::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/*
 * CHECKBOX
 */

.checkbox-container {
  display: flex;
  align-items: baseline;
}

.checkbox-container + .checkbox-container {
  margin-top: 10px;
}

.checkbox {
  position: relative;
  top: 2px;
  -webkit-appearance: none;
  flex: none;
  width: 14px;
  height: 14px;
  border: 1px solid rgb(var(--border-color-darker));
  background-color: rgb(var(--background));
  border-radius: 0;
  transition: background-color 0.2s ease-in-out, border 0.2s ease-in-out;
  cursor: pointer;
}

.checkbox:checked {
  border-color: rgb(var(--heading-color));
  background-color: rgb(var(--heading-color));
  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEwIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEgM0w0IDZMOS4wMDE0NiAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjEuNSIvPjwvc3ZnPg==);
  background-position: center;
  background-repeat: no-repeat;
}

.checkbox:disabled + label {
  opacity: 0.7;
  cursor: default;
}

[dir="ltr"] .checkbox + label {
  padding-left: 12px;
}

[dir="rtl"] .checkbox + label {
  padding-right: 12px;
}

.checkbox + label {
  cursor: pointer;
}

@media screen and (min-width: 1000px) {
  .checkbox-container + .checkbox-container {
    margin-top: 2px;
  }
}

/*
 * SELECT
 */

.select-wrapper {
  position: relative;
}

.select {
  -webkit-appearance: none;
          appearance: none; /* Remove default style if applied to a built-in select */
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 18px;
  height: var(--form-input-field-height);
  width: 100%;
  border: 1px solid rgb(var(--border-color));
  border-radius: var(--button-border-radius);
  background: transparent;
  cursor: pointer;
}

[dir="ltr"] .select--collapse-start {
  border-top-left-radius: 0;
}

[dir="rtl"] .select--collapse-start {
  border-top-right-radius: 0;
}

[dir="ltr"] .select--collapse-start {
  border-bottom-left-radius: 0;
}

[dir="rtl"] .select--collapse-start {
  border-bottom-right-radius: 0;
}

[dir="ltr"] .select--collapse-end {
  border-top-right-radius: 0;
}

[dir="rtl"] .select--collapse-end {
  border-top-left-radius: 0;
}

[dir="ltr"] .select--collapse-end {
  border-bottom-right-radius: 0;
}

[dir="rtl"] .select--collapse-end {
  border-bottom-left-radius: 0;
}

[dir="ltr"] .select svg {
  margin-left: 20px;
}

[dir="rtl"] .select svg {
  margin-right: 20px;
}

.select svg {
  transition: transform 0.25s ease-in-out;
}

/* When the select class is applied to a native select, then the svg arrow is positioned absolutely */
[dir="ltr"] .select ~ svg {
  right: 18px;
}
[dir="rtl"] .select ~ svg {
  left: 18px;
}
.select ~ svg {
  position: absolute;
  top: calc(50% - 4px);
}

.select__selected-value {
  position: relative;
  display: flex;
  align-items: center;
  top: -1px; /* Designer want to move by 1px up */
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

[dir="ltr"] .select__color-swatch {
  margin-right: 10px;
}

[dir="rtl"] .select__color-swatch {
  margin-left: 10px;
}

.select__color-swatch {
  position: relative;
  width: 16px;
  height: 16px;
  border-radius: var(--color-swatch-border-radius);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}

.select__color-swatch--white {
  box-shadow: 0 0 0 1px rgba(var(--text-color), 0.3) inset;
}

.select--small {
  padding: 6px 12px;
  height: auto; /* As the input is much smaller we size it with padding instead */
}

[dir="ltr"] .select--small svg {
  margin-left: 10px;
}

[dir="rtl"] .select--small svg {
  margin-right: 10px;
}

.select[aria-expanded="true"] svg {
  transform: rotateZ(180deg);
}

@media screen and (min-width: 741px) {
  .select__selected-value {
    pointer-events: none;
  }
}

/*
 * RANGE ELEMENT
 * Styling range are pretty complex as each browsers has their own way to do it
 */

/* First we revert the styling of range elements */

.range {
  -webkit-appearance: none;
          appearance: none; /* Hides the slider so that custom slider can be made */
  width: 100%; /* Specific width is required for Firefox. */
  background: transparent; /* Otherwise white in Chrome */
}

.range::-webkit-slider-thumb {
  -webkit-appearance: none;
}

/* Chrome and Safari */

.range::-webkit-slider-thumb {
  position: relative;
  height: 14px;
  width: 14px;
  border-radius: 100%;
  border: none;
  background: rgb(var(--background));
  cursor: pointer;
  margin-top: -5px;
  z-index: 1;
  box-shadow: 0 0 0 5px rgb(var(--text-color)) inset;
}

.range::-webkit-slider-runnable-track {
  width: 100%;
  height: 6px;
  cursor: pointer;
  background: rgb(var(--border-color));
  border-radius: 4px;
  border: none;
}

/* Firefox */

.range::-moz-range-thumb {
  height: 14px;
  width: 14px;
  border-radius: 100%;
  border: none;
  background: rgb(var(--background));
  cursor: pointer;
  box-shadow: 0 0 0 5px rgb(var(--text-color)) inset;
}

.range::-moz-range-progress,
.range::-moz-range-track {
  width: 100%;
  height: 6px;
  cursor: pointer;
  border-radius: 4px;
  border: none;
}

.range::-moz-range-progress {
  background-color: rgba(var(--text-color), 0.7);
}

.range::-moz-range-track {
  background-color: rgb(var(--border-color));
}

/* On non-hover devices, we make the thumb bigger */

@media not screen and (pointer: fine) {
  .range::-webkit-slider-thumb {
    height: 20px;
    width: 20px;
    margin-top: -7px;
    box-shadow: 0 0 0 7px rgb(var(--text-color)) inset;
  }

  .range::-moz-range-thumb {
    height: 20px;
    width: 20px;
    box-shadow: 0 0 0 7px rgb(var(--text-color)) inset;
  }
}

/* Range group (when using double range, we need to rely on some clever trick) */

.range-group {
  height: 6px;
  background: linear-gradient(to var(--transform-origin-end), rgb(var(--border-color)) var(--range-min), rgba(var(--text-color), 0.7) var(--range-min), rgba(var(--text-color), 0.7) var(--range-max), rgb(var(--border-color)) var(--range-max));
  border-radius: 4px;
}

.range-group .range {
  pointer-events: none;
  height: 6px;
  vertical-align: top;
}

.range-group .range::-webkit-slider-runnable-track {
  background: none;
}

.range-group .range::-webkit-slider-thumb {
  pointer-events: auto;
}

.range-group .range::-moz-range-progress,
.range-group .range::-moz-range-track {
  background: none;
}

.range-group .range::-moz-range-thumb {
  pointer-events: auto;
}

[dir="ltr"] .range-group .range:last-child {
  left: 0;
}

[dir="rtl"] .range-group .range:last-child {
  right: 0;
}

.range-group .range:last-child {
  position: absolute;
  top: 0;
}
/**
 * -------------------------------------------------------------
 * BUTTON
 * -------------------------------------------------------------
 */

.button,
.shopify-challenge__button,
#shopify-product-reviews .spr-summary-actions-newreview,
#shopify-product-reviews .spr-button {
  position: relative;
  display: inline-block;
  -webkit-appearance: none;
          appearance: none;
  line-height: var(--button-height);
  padding: 0 30px;
  text-align: center;
  text-decoration: none;
  border-radius: var(--button-border-radius);
  background: rgb(var(--button-background));
  color: rgb(var(--button-text-color));
}

.button:not(.button--text),
.shopify-challenge__button,
#shopify-product-reviews .spr-summary-actions-newreview,
#shopify-product-reviews .spr-button {
  font-size: calc(var(--base-font-size) - 3px);
  font-family: var(--text-font-family);
  font-weight: var(--text-font-bold-weight);
  text-transform: uppercase;
  letter-spacing: 1.5px;
}

.button--small,
#shopify-product-reviews .spr-summary-actions-newreview {
  padding: 0 20px;
  line-height: var(--button-small-height);
}

.button--primary,
.shopify-challenge__button,
#shopify-product-reviews .spr-summary-actions-newreview,
#shopify-product-reviews .spr-button {
  --button-background: var(--primary-button-background);
  --button-text-color: var(--primary-button-text-color);
}

.button--secondary {
  --button-background: var(--secondary-button-background);
  --button-text-color: var(--secondary-button-text-color);
}

.button--ternary {
  --button-background: var(--secondary-background);
  --button-text-color: var(--root-text-color);
}

.button--outline {
  --button-background: var(--background);
  --button-text-color: var(--root-text-color);
  border: 1px solid rgb(var(--border-color));
}

.button--full {
  width: 100%;
}

/**
 * IMPLEMENTATION NOTE: As of today, when the dynamic checkout button is added on non-product page (for instance
 * drawer or collection page), Shopify forces their style. We therefore need to use !important rules to make sure
 * the theme styling is applied. I did not want to apply that globally to the .button, so I am applying this
 * scoped for this button only, even if this incurs duplicate code
 */

.shopify-payment-button {
  min-height: var(--button-height); /* This prevents a page jump */
}

.shopify-payment-button__button--branded {
  border-radius: var(--button-border-radius) !important;
  overflow: hidden !important;
  min-height: var(--button-height) !important;
}

.shopify-payment-button__button--unbranded {
  --button-background: var(--primary-button-background);
  --button-text-color: var(--primary-button-text-color);

  position: relative !important;
  display: inline-block !important;
  -webkit-appearance: none !important;
          appearance: none !important;
  line-height: var(--button-height) !important;
  padding: 0 30px !important;
  text-align: center !important;
  text-decoration: none !important;
  border-radius: var(--button-border-radius) !important;
  font-size: calc(var(--base-font-size) - 3px) !important;
  font-family: var(--text-font-family) !important;
  font-weight: var(--text-font-bold-weight) !important;
  text-transform: uppercase !important;
  letter-spacing: 1.5px !important;
  background-color: rgb(var(--button-background)) !important;
  color: rgb(var(--button-text-color)) !important;
}

.shopify-payment-button__more-options[aria-hidden="true"] {
  display: none;
}

@media screen and (min-width: 741px) {
  .button:not(.button--text),
  .shopify-challenge__button,
  #shopify-product-reviews .spr-summary-actions-newreview,
  #shopify-product-reviews .spr-button {
    padding-left: 35px;
    padding-right: 35px;
    font-size: calc(var(--base-font-size) - 2px);
    letter-spacing: 2px;
  }

  .button--small:not(.button--text),
  #shopify-product-reviews .spr-summary-actions-newreview {
    padding-left: 28px;
    padding-right: 28px;
    font-size: calc(var(--base-font-size) - 3px);
  }

  /* Please refer to a comment a few lines before for the reason of those !important */
  .shopify-payment-button__button--unbranded {
    padding-left: 35px !important;
    padding-right: 35px !important;
    font-size: calc(var(--base-font-size) - 2px) !important;
    letter-spacing: 2px !important;
  }
}

/**
 * IMPLEMENTATION NOTE: the design team wanted a specific transition on hover that implies a slightly tilted background
 * with a reduced opacity. Doing that with a pseudo element (with an efficient scale transform) caused to be problematic,
 * because the text would appear below the actual pseudo-element, causing the color to be off. One solution would have
 * been to add an extra <span> wrapping the text itself so that we could set a z-index for the text to be on top. Unfortunately,
 * there are lot of buttons (Shopify Reviews, dynamic checkout button...) for which controlling the markup is not possible.
 * As a consequence, I had to go with a more creating approach that uses multiple linear-gradient and animate the background-position.
 * Animating background-position is known to not be very efficient, but as this is only for desktop and on elements (buttons) whose
 * size is extremely small, this did not cause any visible slugger animation.
 */
@media screen and (pointer: fine) {
  .button,
  .shopify-challenge__button,
  #shopify-product-reviews .spr-summary-actions-newreview,
  #shopify-product-reviews .spr-button,
  .shopify-payment-button__button {
    background-image: linear-gradient(178deg, rgb(var(--button-background)), rgb(var(--button-background)) 10%, rgba(0, 0, 0, 0.07) 10%, rgba(0, 0, 0, 0.07) 100%), linear-gradient(rgb(var(--button-background)), rgb(var(--button-background)));
    background-size: 100% 200%, 100% 100%;
    background-position: 100% -100%, 100% 100%;
    background-repeat: no-repeat;
    transition: background-position 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translateZ(0); /* Make sure to promote the button on its own layer */
  }

  .button:hover,
  .shopify-challenge__button:hover,
  #shopify-product-reviews .spr-summary-actions-newreview:hover,
  #shopify-product-reviews .spr-button:hover,
  .shopify-payment-button__button:hover {
    background-position: 100% 25%, 100% 100%;
  }

  /* Only on super modern browsers, allow to automatically switch to a white color based on contrast */
  @supports (color: color-contrast(wheat vs black, white)) and (color: rgb(from wheat r g b / 0.07)) {
    .button,
    .shopify-challenge__button,
    #shopify-product-reviews .spr-summary-actions-newreview,
    #shopify-product-reviews .spr-button,
    .shopify-payment-button__button {
      --button-overlay-color: rgb(from color-contrast(rgb(var(--button-background)) vs white, black) r g b / 7%);
      background-image: linear-gradient(178deg, rgb(var(--button-background)), rgb(var(--button-background)) 10%, var(--button-overlay-color) 10%, var(--button-overlay-color) 100%), linear-gradient(rgb(var(--button-background)), rgb(var(--button-background)));
    }
  }
}

/**
 * -------------------------------------------------------------
 * LOADER BUTTON
 * -------------------------------------------------------------
 */

.loader-button__text {
  display: flex;
  align-items: center;
  justify-content: center;
}


.loader-button__loader {
  position: absolute;
  top: 50%;
  left: 50%;
  opacity: 0;
}

/**
 * -------------------------------------------------------------
 * BUTTON GROUP
 * -------------------------------------------------------------
 */

.button-group {
  font-size: 0; /* Collapse margin */
}

@media screen and (max-width: 740px) {
  .button-group .button {
    margin: 12px;
    padding: 0 18px; /* We halve the padding to increase probability to button to fit on same row on mobile */
  }

  .button-group__wrapper {
    margin: -12px;
  }
}

@media screen and (min-width: 741px) {
  /* On tablet as we have more space we want to ensure the button has the same width */
  .button-group__wrapper {
    display: inline-grid;
    gap: 24px;
    grid-template-columns: 1fr 1fr;
  }
}
/**
 * -------------------------------------------------------------
 * COLLAPSIBLE
 * -------------------------------------------------------------
 */

.collapsible {
  display: block;
  height: 0;
  overflow: hidden;
  visibility: hidden;
}

.no-js .collapsible,
.collapsible[open] {
  height: auto;
  overflow: visible;
  visibility: visible;
}

.collapsible-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 18px 0;
}

[dir="ltr"] .collapsible-toggle__selected-value {
  margin-left: auto;
  margin-right: 12px;
}

[dir="rtl"] .collapsible-toggle__selected-value {
  margin-right: auto;
  margin-left: 12px;
}

.collapsible-toggle__selected-value {
  font-weight: normal;
  max-width: 45%;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.collapsible-toggle svg {
  transition: transform 0.2s ease-in-out;
}

.collapsible-toggle[aria-expanded="true"] svg {
  transform: rotateZ(180deg);
}

@media screen and (min-width: 741px) {
  .collapsible-toggle {
    padding: 21px 0;
  }
}
/**
 * -------------------------------------------------------------
 * CONTENT BOX
 *
 * In Focal, a lot of content (especially on home page sections) are aligned
 * according a 10 columns grid (20 on tablet and higher). This special content
 * box has different alignments and three variation styles. As it is used
 * throughout the theme, we create an abstraction here.
 * -------------------------------------------------------------
 */

.content-box {
  position: relative;
  z-index: 1;
}

/* Text alignment variations */

.content-box--text-center {
  text-align: center;
}

[dir="ltr"] .content-box--text-right {
  text-align: right;
}

[dir="rtl"] .content-box--text-right {
  text-align: left;
}

@media screen and (max-width: 740px) {
  /*
     Small and medium variation on mobile have some extra spacing and are not directly based on the grid. We also
     apply extra margin if the content box is contained within a flushed mobile container
   */
  .content-box--small,
  .content-box--medium,
  .container--flush .content-box--large {
    margin-left: 24px;
    margin-right: 24px;
  }

  /* Positional variation */

  [dir="ltr"] .content-box--left {
    margin-right: auto;
  }

  [dir="rtl"] .content-box--left {
    margin-left: auto;
  }

  [dir="ltr"] .content-box--right {
    margin-left: auto;
  }

  [dir="rtl"] .content-box--right {
    margin-right: auto;
  }
}

@media screen and (min-width: 741px) {
  /* On tablet, all sizes are shifted by one column left and right, and span a different number of columns */
  .content-box {
    margin-left: auto;
    margin-right: auto;
    width: calc(var(--grid-column-width) * 16 + var(--grid-gap) * 15);
  }

  .content-box--medium {
    width: calc(var(--grid-column-width) * 14 + var(--grid-gap) * 13);
  }

  .content-box--small {
    width: calc(var(--grid-column-width) * 10 + var(--grid-gap) * 9);
  }

  .content-box--fill {
    width: 100% !important;
  }

  /* Positional variations */

  [dir="ltr"] .content-box--left {
    margin-left: calc(var(--grid-column-width) + var(--grid-gap));
  }

  [dir="rtl"] .content-box--left {
    margin-right: calc(var(--grid-column-width) + var(--grid-gap));
  }

  [dir="ltr"] .content-box--right {
    margin-right: calc(var(--grid-column-width) + var(--grid-gap));
  }

  [dir="rtl"] .content-box--right {
    margin-left: calc(var(--grid-column-width) + var(--grid-gap));
  }
}

@media screen and (min-width: 1000px) {
  .content-box {
    width: calc(var(--grid-column-width) * 14 + var(--grid-gap) * 13);
  }

  .content-box--medium {
    width: calc(var(--grid-column-width) * 10 + var(--grid-gap) * 9);
  }

  .content-box--small {
    width: calc(var(--grid-column-width) * 8 + var(--grid-gap) * 7);
  }
}

@media screen and (min-width: 1400px) {
  .content-box--small {
    width: calc(var(--grid-column-width) * 7 + var(--grid-gap) * 6);
  }
}
/**
 * -------------------------------------------------------------
 * DRAWER
 * -------------------------------------------------------------
 */

[dir="ltr"] .drawer {
  right: 0;
}

[dir="rtl"] .drawer {
  left: 0;
}

[dir="ltr"] .drawer {
  text-align: left;
}

[dir="rtl"] .drawer {
  text-align: right;
}

.drawer {
  /* Make sure the drawer component does not inherit values from the section it is included to */
  --heading-color: var(--root-heading-color);
  --text-color: var(--root-text-color);
  --background: var(--root-background);
  --primary-button-background: var(--root-primary-button-background);
  --primary-button-text-color: var(--root-primary-button-text-color);
  --section-background: initial; /* Allows to remove the inheritance */

  position: fixed;
  display: flex;
  flex-direction: column;
  top: 0;
  width: 89vw;
  max-width: 400px;
  height: 100%;
  max-height: 100vh;
  background: rgb(var(--background));
  color: rgb(var(--text-color));
  z-index: 10;
  transform: translateX(calc(var(--transform-logical-flip) * 100%));
  visibility: hidden;
  font-size: 1rem;
  transition: transform 0.6s cubic-bezier(0.75, 0, 0.175, 1), visibility 0.6s cubic-bezier(0.75, 0, 0.175, 1);
}

[dir="ltr"] .drawer--from-left {
  right: auto;
}

[dir="rtl"] .drawer--from-left {
  left: auto;
}

[dir="ltr"] .drawer--from-left {
  left: 0;
}

[dir="rtl"] .drawer--from-left {
  right: 0;
}

.drawer--from-left {
  transform: translateX(calc(var(--transform-logical-flip) * -100%));
}

.drawer[open] {
  transform: translateX(0);
  visibility: visible;
}

[dir="ltr"] .drawer--from-left .drawer__overlay {
  left: 100%;
}

[dir="rtl"] .drawer--from-left .drawer__overlay {
  right: 100%;
}

[dir="ltr"] .drawer--from-left .drawer__overlay {
  right: auto;
}

[dir="rtl"] .drawer--from-left .drawer__overlay {
  left: auto;
}

.drawer--large {
  max-width: 500px;
  --drawer-width: 500px;
}

[dir="ltr"] .drawer__overlay {
  right: 100%;
}

[dir="rtl"] .drawer__overlay {
  left: 100%;
}

.drawer__overlay {
  content: '';
  position: fixed;
  top: 0;
  height: 100vh;
  width: 100vw;
  background: #000000;
  opacity: 0;
  visibility: hidden;
  transition: visibility 0.6s ease-in-out, opacity 0.6s ease-in-out;
}

.drawer[open] > .drawer__overlay {
  visibility: visible;
  opacity: 0.3;
}

.drawer__header {
  position: relative;
  display: flex;
  align-items: center;
  flex-shrink: 0;
  padding-top: 20px;
  padding-bottom: 20px;
  height: var(--header-height-without-bottom-nav);
  max-height: 80px;
  border-bottom: 1px solid rgb(var(--root-border-color));
  background: rgb(var(--root-background)); /* Setting an explicit background allow element to go beyond */
  z-index: 1;
}

/* This variation remove the border and add a shadow on scroll */
.drawer__header--shadowed {
  height: auto;
  border-bottom: none;
  padding-bottom: 6px;
}

[dir="ltr"] .drawer__header--shadowed::after {
  left: 0;
}

[dir="rtl"] .drawer__header--shadowed::after {
  right: 0;
}

.drawer__header--shadowed::after {
  content: '';
  position: absolute;
  top: 100%;
  width: 100%;
  height: 24px;
  background: linear-gradient(var(--root-background), rgba(var(--root-background), 0));
  z-index: 1;
  pointer-events: none;
}

.drawer__title {
  /* We use flex positioning in case the title contains an icon or extra content */
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin: 0;
  max-width: 100%;
}

.drawer__title--stack {
  flex-direction: column;
  align-items: flex-start;
  flex-grow: 1;
}

[dir="ltr"] .drawer__title .icon {
  margin-right: 12px;
}

[dir="rtl"] .drawer__title .icon {
  margin-left: 12px;
}

[dir="ltr"] .drawer__header-action {
  margin-left: 16px;
}

[dir="rtl"] .drawer__header-action {
  margin-right: 16px;
}

[dir="ltr"] .drawer__close-button {
  right: var(--container-gutter);
}

[dir="rtl"] .drawer__close-button {
  left: var(--container-gutter);
}

.drawer__close-button {
  position: absolute;
  top: var(--container-gutter);
}

.drawer__header .drawer__close-button {
  top: calc(50% - 7px); /* When inside the header its alignment is relative to the header part */
}

.drawer__close-button--block {
  position: relative;
  top: auto !important;
  right: auto !important;
  bottom: auto !important;
  left: auto !important;
}

.drawer__header,
.drawer__content,
.drawer__footer {
  padding-left: var(--container-gutter);
  padding-right: var(--container-gutter);
}

.drawer__content {
  overflow-x: hidden;
  overflow-y: auto;
  flex-grow: 1;
  padding-bottom: 24px; /* We add a bit of spacing as the drawer footer may add a shadow */
}

.drawer__content--padded-start {
  padding-top: 24px;
}

.drawer__content--center {
  text-align: center;
  margin-top: auto;
  margin-bottom: auto;
  padding-bottom: 0;
  flex-grow: 0;
}

.drawer__footer {
  margin-top: auto;
  padding-top: 20px;
  padding-bottom: 20px; /* The footer in drawer has a slightly smaller padding in block direction */
  z-index: 1;
  transform: translateZ(0); /* For some reason position: relative cause an issue on Safari so I use this to promote the element */
}

@supports (padding: max(0px)) {
  .drawer__footer {
    padding-bottom: max(20px, env(safe-area-inset-bottom, 0px) + 20px);
  }
}

.drawer__footer--bordered {
  box-shadow: 0 1px rgb(var(--root-border-color)) inset; /* We use box-shadow due to the shadow */
}

.drawer__footer--no-top-padding {
  padding-top: 0 !important;
}

[dir="ltr"] .drawer__footer::before {
  left: 0;
}

[dir="rtl"] .drawer__footer::before {
  right: 0;
}

.drawer__footer::before {
  content: '';
  position: absolute;
  bottom: 100%;
  width: 100%;
  height: 24px;
  background: linear-gradient(rgba(var(--root-background), 0), rgb(var(--root-background)));
  z-index: 1;
  pointer-events: none;
}

@media screen and (min-width: 741px) {
  .drawer__header {
    padding-top: 24px;
    padding-bottom: 24px;
    max-height: 90px;
  }

  [dir="ltr"] .drawer__header-action {
    margin-left: 24px;
  }

  [dir="rtl"] .drawer__header-action {
    margin-right: 24px;
  }

  .drawer__content--padded-start {
    padding-top: 30px;
  }

  .drawer__footer:not(.drawer__footer--tight) {
    padding-top: var(--container-gutter);
    padding-bottom: var(--container-gutter);
  }
}
/**
 * -------------------------------------------------------------
 * POPOVER
 * -------------------------------------------------------------
 */

.popover-button {
  width: max-content;
}

[dir="ltr"] .popover-button svg {
  margin-left: 10px;
}

[dir="rtl"] .popover-button svg {
  margin-right: 10px;
}

.popover-button svg {
  position: relative;
  top: -1px; /* For pixel perfect */
  transition: transform 0.2s ease-in-out;
}

.popover-button[aria-expanded="true"] svg {
  transform: rotateZ(180deg);
}

[dir="ltr"] .popover {
  text-align: left;
}

[dir="rtl"] .popover {
  text-align: right;
}

.popover {
  /* Make sure the popover component does not inherit values from the section it is included to */
  --heading-color: var(--root-heading-color);
  --text-color: var(--root-text-color);
  --background: var(--root-background);
  --primary-button-background: var(--root-primary-button-background);
  --primary-button-text-color: var(--root-primary-button-text-color);
  --section-background: var(--root-background);

  display: block;
  position: fixed;
  z-index: 10;
  color: rgb(var(--text-color));
  background: rgb(var(--background));
  visibility: hidden;
  font-size: 1rem;
  transition: visibility 0.25s ease-in-out;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}

.popover[open] {
  visibility: visible;
}

.popover__overlay {
  position: absolute;
  content: '';
  height: 100vh;
  width: 100%;
  bottom: calc(100% - 10px); /* There is a border radius on the header on mobile so we slightly move down the overlay */
  left: 0; /* We must make sure to not use logical property here */
  background: #000000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.6s ease-in-out, visibility 0.6s ease-in-out;
}

.popover[open] > .popover__overlay {
  visibility: visible;
  opacity: 0.3;
}

.popover__header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  padding: 10px 24px;
  min-height: 64px;
  border-bottom: 1px solid rgb(var(--root-border-color));
  border-radius: 10px 10px 0 0;
}

.popover__header--no-border {
  border-bottom: none;
}

.popover__title {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0;
}

[dir="ltr"] .popover__title svg {
  margin-right: 12px;
}

[dir="rtl"] .popover__title svg {
  margin-left: 12px;
}

[dir="ltr"] .popover__close-button {
  right: 24px;
}

[dir="rtl"] .popover__close-button {
  left: 24px;
}

.popover__close-button {
  position: absolute;
  top: 24px;
  z-index: 1;
}

.popover__content {
  padding: 24px;
  overflow: auto;
}

.popover__content--no-padding {
  padding: 0 !important;
}

@supports (padding: max(0px)) {
  .popover__content {
    padding-bottom: max(24px, env(safe-area-inset-bottom, 0px) + 24px);
  }
}

@media screen and (max-width: 999px) {
  .popover {
    display: flex;
    flex-direction: column;
    bottom: 0;
    left: 0; /* We must make sure to not use logical property here */
    width: 100vw;
    max-height: 75vh;
    border-radius: 10px 10px 0 0;
    transform: translateY(100%);
    transition: transform 0.6s cubic-bezier(0.75, 0, 0.175, 1), visibility 0.6s cubic-bezier(0.75, 0, 0.175, 1);
    touch-action: manipulation;
  }

  .popover[open] {
    transform: translateY(0);
  }

  .popover__header,
  .popover__content {
    background: inherit;
  }

  [dir="ltr"] .drawer:not(.drawer--from-left) .popover {
    left: -11vw;
  }

  [dir="rtl"] .drawer:not(.drawer--from-left) .popover {
    right: -11vw;
  }

  .drawer:not(.drawer--from-left) .popover { /* Popover takes 89vw so we shift by 11vw */
  }
}

@media screen and (min-width: 1000px) {
  .popover-container {
    position: relative;
  }

  .popover {
    position: absolute;
    top: calc(100% + 18px);
    border: 1px solid rgb(var(--root-border-color));
    z-index: 2;
    opacity: 0;
    transition: opacity 0.25s ease-in-out, visibility 0.25s ease-in-out;
    border-radius: min(var(--block-border-radius), 4px);
  }

  .popover[open] {
    opacity: 1;
  }

  [dir="ltr"] .popover::after,[dir="ltr"]
  .popover::before {
    right: 24px;
  }

  [dir="rtl"] .popover::after,[dir="rtl"]
  .popover::before {
    left: 24px;
  }

  .popover::after,
  .popover::before {
    position: absolute;
    content: '';
    bottom: 100%;
    width: 0;
    height: 0;
    border-style: solid;
    border-color: transparent transparent rgb(var(--root-background)) transparent;
    border-width: 8px;
  }

  [dir="ltr"] .popover::before {
    right: 23px;
  }

  [dir="rtl"] .popover::before {
    left: 23px;
  }

  .popover::before {
    border-color: transparent transparent rgb(var(--root-border-color)) transparent;
    border-width: 9px;
  }

  .popover__overlay,
  .popover__header {
    display: none; /* No overlay nor header on tablet and up for popover */
  }

  .popover__content {
    padding-left: 32px;
    padding-right: 32px;
  }

  .popover__content--restrict {
    max-height: 400px;
  }

  /* Small variation */

  .popover--small {
    font-size: calc(var(--base-font-size) - 3px);
    line-height: 1.5;
  }

  /* Positional variation */

  .popover--top {
    top: auto;
    bottom: calc(100% + 18px);
  }

  .popover--top::before,
  .popover--top::after {
    top: 100%;
    bottom: auto;
    border-color: rgb(var(--root-background)) transparent transparent transparent;
  }

  .popover--top::before {
    border-color: rgb(var(--root-border-color)) transparent transparent transparent;
  }

  [dir="ltr"] .popover--left {
    left: 0;
  }

  [dir="rtl"] .popover--left {
    right: 0;
  }

  [dir="ltr"] .popover--left {
    right: auto !important;
  }

  [dir="rtl"] .popover--left {
    left: auto !important;
  }

  .popover--left::before,
  .popover--left::after {
    display: none;
  }

  /* Position related to various UX element */

  [dir="ltr"] .popover-button + .popover {
    right: -28px;
  }

  [dir="rtl"] .popover-button + .popover {
    left: -28px;
  }

  [dir="ltr"] .select + .popover {
    right: -15px;
  }

  [dir="rtl"] .select + .popover {
    left: -15px;
  }
}

/**
 * -------------------------------------------------------------
 * POPOVER CHOICE LIST
 *
 * This child component is used to create a easily usable list of
 * choices
 * -------------------------------------------------------------
 */

.popover__choice-list {
  white-space: nowrap;
}

.popover__choice-item {
  display: block;
  width: 100%;
  text-align: center;
}

.popover__choice-item:not(:first-child) {
  margin-top: 7px;
}

.popover__choice-label {
  position: relative;
  cursor: pointer;
}

[dir="ltr"] input:checked + .popover__choice-label::after,[dir="ltr"]
.popover__choice-label[aria-current]::after {
  right: -26px;
}

[dir="rtl"] input:checked + .popover__choice-label::after,[dir="rtl"]
.popover__choice-label[aria-current]::after {
  left: -26px;
}

input:checked + .popover__choice-label::after,
.popover__choice-label[aria-current]::after {
  content: '';
  position: absolute;
  width: 12px;
  height: 9px;
  top: calc(50% - (9px / 2));
  background-color: currentColor;
  -webkit-mask-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEgMy40TDQuNzQ5MzEgN0wxMSAxIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIi8+PC9zdmc+);
          mask-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEgMy40TDQuNzQ5MzEgN0wxMSAxIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIi8+PC9zdmc+);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  -webkit-mask-size: 12px 9px;
          mask-size: 12px 9px;
}

@media screen and (max-width: 999px) {
  .popover__choice-item:not(:first-child) {
    margin-top: 16px;
  }
}

@media screen and (min-width: 1000px) {
  [dir="ltr"] input:checked + .popover__choice-label::after,[dir="ltr"]
  .popover__choice-label[aria-current]::after {
    right: -22px;
  }
  [dir="rtl"] input:checked + .popover__choice-label::after,[dir="rtl"]
  .popover__choice-label[aria-current]::after {
    left: -22px;
  }

  [dir="ltr"] .popover__choice-label {
    margin-right: 22px;
  }

  [dir="rtl"] .popover__choice-label {
    margin-left: 22px;
  }

  .popover__choice-item {
    text-align: left;
  }

  .popover--small .popover__content {
    padding: 14px 20px;
  }
}
/**
 * -------------------------------------------------------------
 * MODAL
 *
 * Modal allows to provide a full-screen off-screen elements. On mobile, they look
 * like popover, while on tablet and desktop they use an immersive full-screen elements. Because
 * modal are much more "free" than popover and drawer in their structure, the styling
 * is left to the actual inner content
 * -------------------------------------------------------------
 */

[dir="ltr"] .modal {
  left: 0;
}

[dir="rtl"] .modal {
  right: 0;
}

.modal {
  /* Make sure the modal component does not inherit values from the section it is included to */
  --heading-color: var(--root-heading-color);
  --text-color: var(--root-text-color);
  --background: var(--root-background);

  position: fixed;
  font-size: 1rem;
  z-index: 10;
  visibility: hidden;
  transition: visibility 0.25s ease-in-out;
}

.modal[open] {
  visibility: visible;
}

[dir="ltr"] .modal__overlay {
  left: 0;
}

[dir="rtl"] .modal__overlay {
  right: 0;
}

.modal__overlay {
  position: absolute;
  content: '';
  height: 100vh;
  width: 100vw;
  bottom: calc(100% - 10px); /* There is a border radius on the header on mobile so we slightly move down the overlay */
  background: #000000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.5s ease-in-out, visibility 0.5s ease-in-out;
}

.modal[open] > .modal__overlay {
  visibility: visible;
  opacity: 0.3;
}

[dir="ltr"] .modal__close-button {
  right: 24px;
}

[dir="rtl"] .modal__close-button {
  left: 24px;
}

.modal__close-button {
  position: absolute;
  top: 24px;
  z-index: 1;
}

.modal__content {
  position: relative;
  display: flow-root;
  background: rgb(var(--background));
  color: rgb(var(--text-color));
  border-radius: var(--block-border-radius);
}

@media screen and (max-width: 740px) {
  .modal {
    bottom: 0;
    width: 100vw;
    transform: translateY(100%);
    transition: transform 0.7s cubic-bezier(0.75, 0, 0.175, 1), visibility 0.7s cubic-bezier(0.75, 0, 0.175, 1);
    touch-action: manipulation;
  }

  .modal[open] {
    transform: translateY(0);
  }

  .modal__content {
    max-height: 81vh;
    border-radius: 10px 10px 0 0;
    overflow: hidden;
  }
}

@media screen and (min-width: 741px) {
  .modal {
    display: flex;
    top: 0;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
  }

  .modal__overlay {
    position: fixed;
    top: 0;
    bottom: auto;
  }

  .modal__content {
    margin: 80px; /* Allows to guarantee spacing around the edges */
    max-height: calc(100vh - 160px);
    overflow: auto;
    transform: scale(0.8);
    opacity: 0;
    transition: transform 0.3s cubic-bezier(0.75, 0, 0.175, 1), opacity 0.3s cubic-bezier(0.75, 0, 0.175, 1);
    will-change: transform;
  }

  .modal[open] .modal__content {
    transform: scale(1);
    opacity: 1;
  }
}
/**
 * --------------------------------------------------------------------
 * COLOR SWATCH
 * --------------------------------------------------------------------
 */

.color-swatch-list {
  display: grid;
  justify-content: flex-start;
  grid-template-columns: repeat(auto-fit, 40px);
  grid-gap: 8px;
  gap: 8px;
}

.color-swatch__item {
  position: relative;
  display: block;
  cursor: pointer;
  width: 40px;
  height: 40px;
  border: 3px solid rgb(var(--section-background, var(--background)));
  border-radius: var(--color-swatch-border-radius);
  background-size: cover;
  background-position: center;
  -webkit-tap-highlight-color: transparent;
}

.color-swatch__item::before,
.color-swatch__item::after {
  position: absolute;
  content: '';
  border: 2px solid rgb(var(--section-background, var(--background)));
  pointer-events: none;
  border-radius: inherit;
}

.color-swatch__item::before {
  top: -1px;
  left: -1px;
  width: calc(100% + 2px);
  height: calc(100% + 2px);
}

.color-swatch__item::after {
  top: -3px;
  left: -3px;
  width: calc(100% + 6px);
  height: calc(100% + 6px);
  border-color: rgb(var(--text-color));
  opacity: 0;
  transform: scale(0.8);
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.color-swatch--white .color-swatch__item::before {
  box-shadow: 0 0 0 1px rgba(var(--text-color), 0.3) inset;
}

.color-swatch__radio:checked + .color-swatch__item::after {
  opacity: 1;
  transform: scale(1);
}

.color-swatch-list--mini {
  grid-template-columns: repeat(auto-fit, 14px);
}

.color-swatch-list--mini .color-swatch__item {
  width: 14px;
  height: 14px;
  border-width: 2px;
}

.color-swatch-list--mini .color-swatch__item::before,
.color-swatch-list--mini .color-swatch__item::after {
  border-width: 1px;
}

/* Disabled state */

.color-swatch.is-disabled .color-swatch__item::before {
  background: linear-gradient(to bottom right, transparent calc(50% - 1px), rgb(var(--background)) calc(50% - 1px), rgb(var(--background)) calc(50% + 1px), transparent calc(50% + 1px)) no-repeat;
}

@media not screen and (pointer: fine) {
  .color-swatch[data-tooltip]::before,
  .color-swatch[data-tooltip]::after {
    display: none;
  }
}

/**
 * --------------------------------------------------------------------
 * BLOCK SWATCH
 * --------------------------------------------------------------------
 */

.block-swatch-list {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  margin: -4px;
}

.block-swatch__item {
  display: block;
  position: relative;
  min-width: 56px;
  padding: 11px 18px 13px 18px;
  margin: 4px;
  border: 1px solid rgb(var(--border-color));
  border-radius: var(--button-border-radius);
  background: rgb(var(--background));
  color: rgb(var(--text-color));
  text-align: center;
  cursor: pointer;
  transition: background 0.2s ease;
  -webkit-tap-highlight-color: transparent;
}

[dir="ltr"] .block-swatch__item::after {
  left: 0;
}

[dir="rtl"] .block-swatch__item::after {
  right: 0;
}

.block-swatch__item::after {
  content: '';
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  box-shadow: 0 0 0 1px rgb(var(--text-color)) inset, 0 0 0 1px rgb(var(--text-color));
  border-radius: var(--button-border-radius);
  opacity: 0;
  transform: scale(0.9);
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.block-swatch-list--small .block-swatch__item {
  min-width: 44px;
  padding: 4px 12px;
  margin: 4px;
}

.block-swatch__radio:checked + .block-swatch__item {
  background: rgb(var(--secondary-background));
}

.block-swatch__radio:checked + .block-swatch__item::after {
  opacity: 1;
  transform: scale(1);
}

/* Disabled state */

.block-swatch.is-disabled .block-swatch__item {
  color: rgba(var(--text-color), 0.5);
  background: linear-gradient(to bottom right, transparent calc(50% - 1px), rgb(var(--border-color)) 50%, transparent calc(50% + 1px)) no-repeat;
}

/**
 * --------------------------------------------------------------------
 * VARIANT IMAGE SWATCH
 * --------------------------------------------------------------------
 */

.variant-swatch-list {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  margin: -6px;
}

.variant-swatch__item {
  display: block;
  position: relative;
  margin: 6px;
  border: 1px solid rgb(var(--border-color));
  text-align: center;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
}

.variant-swatch__image {
  width: 50px;
}

.variant-swatch__item,
.variant-swatch__image {
  border-radius: min(var(--block-border-radius), 4px);
}

[dir="ltr"] .variant-swatch__item::after {
  left: 0;
}

[dir="rtl"] .variant-swatch__item::after {
  right: 0;
}

.variant-swatch__item::after {
  content: '';
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  box-shadow: 0 0 0 1px rgb(var(--text-color)) inset, 0 0 0 1px rgb(var(--text-color));
  border-radius: min(var(--block-border-radius), 3px);
  opacity: 0;
  transform: scale(0.9);
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.variant-swatch__radio:checked + .variant-swatch__item::after {
  opacity: 1;
  transform: scale(1);
}

/* Disabled state */

.variant-swatch.is-disabled .variant-swatch__image {
  opacity: 0.4;
}

/* We have to use a pseudo element as background image does not work on image */
[dir="ltr"] .variant-swatch.is-disabled .variant-swatch__item::before {
  left: 0;
}
[dir="rtl"] .variant-swatch.is-disabled .variant-swatch__item::before {
  right: 0;
}
.variant-swatch.is-disabled .variant-swatch__item::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  background: linear-gradient(to bottom right, transparent calc(50% - 1px), rgb(var(--border-color)) 50%, transparent calc(50% + 1px)) no-repeat;
  z-index: 1;
}

@media screen and (min-width: 741px) {
  .variant-swatch-list {
    margin: -6px;
  }

  .variant-swatch__item {
    margin: 6px;
  }

  .variant-swatch__image {
    width: 72px;
  }
}

/**
 * --------------------------------------------------------------------
 * ACCESSIBILITY
 * --------------------------------------------------------------------
 */

.color-swatch__radio.focus-visible + label,
.block-swatch__radio.focus-visible + label,
.variant-swatch__radio.focus-visible + label {
  outline: auto 5px -webkit-focus-ring-color;
}

.color-swatch__radio:focus-visible + label,
.block-swatch__radio:focus-visible + label,
.variant-swatch__radio:focus-visible + label {
  outline: auto 5px -webkit-focus-ring-color;
}
/**
 * --------------------------------------------------------------------
 * 3D MODEL
 * --------------------------------------------------------------------
 */

.shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__controls-area {
  background: rgb(var(--background));
  border-color: rgba(var(--text-color), 0.25);
}

.shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button {
  color: rgb(var(--text-color));
}

.shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control:hover {
  color: rgba(var(--text-color), 0.55);
}

.shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control:active, .shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control.focus-visible:focus {
  color: rgba(var(--text-color), 0.55);
  background: rgba(var(--text-color), 0.25);
}

.shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control:not(:last-child):after {
  border-color: rgba(var(--text-color), 0.25);
}

.shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster {
  background: rgb(var(--background));
  border-color: rgba(var(--text-color), 0.25);
}

.shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster:hover, .shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster:focus {
  color: rgba(var(--text-color), 0.55);
}

.model-wrapper {
  display: block;
  position: relative;
  padding-bottom: 100%;
}

[dir="ltr"] .model-wrapper .shopify-model-viewer-ui,[dir="ltr"]
.model-wrapper model-viewer {
  left: 0;
}

[dir="rtl"] .model-wrapper .shopify-model-viewer-ui,[dir="rtl"]
.model-wrapper model-viewer {
  right: 0;
}

.model-wrapper .shopify-model-viewer-ui,
.model-wrapper model-viewer {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 1;
}

/**
 * --------------------------------------------------------------------
 * VIDEO WRAPPER
 * --------------------------------------------------------------------
 */

.video-wrapper {
  display: block;
  position: relative;
}

.video-wrapper::after {
  content: '';
  display: block;
  padding-bottom: 56.25%; /* 16:9 */
  pointer-events: none;
}

[dir="ltr"] .video-wrapper iframe {
  left: 0;
}

[dir="rtl"] .video-wrapper iframe {
  right: 0;
}

.video-wrapper iframe {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
}

/* The cover variation is used when we use an iframe such as YouTube and Vimeo, and want the video to covers the whole div */

.video-wrapper--cover {
  overflow: hidden;
  width: 100%;
  height: 100%;
}

.video-wrapper--cover::after {
  display: none;
}

.video-wrapper--cover iframe {
  position: absolute;
  top: 50% !important;
  left: 50% !important;
  right: auto !important;
  width: var(--video-width, 100%);
  height: var(--video-height, 100%);
  transform: translate(-50%, -50%);
}

@media (min-aspect-ratio: 16/9) {
  .video-wrapper--cover iframe {
    --video-height: 56.25vw;
  }
}

@media (max-aspect-ratio: 16/9) {
  .video-wrapper--cover iframe {
    --video-width: 177.78vh;
    height: calc(var(--video-height) + 200px);
  }
}

/* Inert variation is useful for background video */
.video-wrapper--inert iframe {
  pointer-events: none;
}

/* For native one we use a different thing */
.video-wrapper--native {
  aspect-ratio: var(--aspect-ratio);
}

.video-wrapper--native video {
  width: 100%;
  height: 100%;
}

.video-wrapper--native::after {
  display: none;
}

@supports not (aspect-ratio: 1) {
  .video-wrapper--native video {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }

  .video-wrapper--native::after {
    display: block;
    padding-bottom: calc(100% / var(--aspect-ratio));
  }
}

/* A cover image can be optionally added before the iframe / video tag */

.video-wrapper__poster {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.video-wrapper__poster,
.video-wrapper iframe {
  opacity: 1;
  visibility: visible;
  transition: opacity 0.25s ease-in-out, visibility 0.25s ease-in-out;
}

.video-wrapper__poster-content {
  position: absolute;
}

.video-wrapper__poster-image {
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  height: 100%;
  width: 100%;
}

@keyframes playButtonRipple {
  0% {
    box-shadow: 0 0 0 0 rgb(var(--play-button-background)), 0 0 0 0 rgb(var(--play-button-background));
  }

  100% {
    box-shadow: 0 0 0 9px rgba(var(--play-button-background), 0), 0 0 0 18px rgba(var(--play-button-background), 0);
  }
}

.video-wrapper__play-button {
  border-radius: 100%;
}

.video-wrapper__play-button--ripple {
  animation: playButtonRipple 1400ms ease-in-out infinite;
}

.video-wrapper__play-button:not(:only-child) {
  margin-bottom: 32px;
}

@media screen and (min-width: 1000px) {
  @keyframes playButtonRipple {
    0% {
      box-shadow: 0 0 0 0 rgb(var(--play-button-background)), 0 0 0 0 rgb(var(--play-button-background));
    }

    100% {
      box-shadow: 0 0 0 17px rgba(var(--play-button-background), 0), 0 0 0 32px rgba(var(--play-button-background), 0);
    }
  }

  .video-wrapper__play-button:not(:only-child) {
    margin-bottom: 40px;
  }

  .video-wrapper__play-button--large svg {
    width: 104px;
    height: 104px;
  }
}
/**
 * -------------------------------------------------------------
 * FACETING (used on collection and search page)
 * -------------------------------------------------------------
 */

.product-facet {
  display: block;
  margin-top: 24px;
  margin-bottom: 48px;
}

.product-facet__filters-header {
  padding-bottom: 24px;
  border-bottom: 1px solid rgb(var(--border-color));
}

.product-facet__filters:not(.drawer) {
  display: block;
  padding-bottom: 24px; /* Allows to add a bit of spacing if too much links */
}

.product-facet__active-list {
  margin-top: -6px;
  margin-bottom: 18px;
}

.product-facet__filter-item + .product-facet__filter-item {
  border-top: 1px solid rgb(var(--border-color));
}

[dir="ltr"] .product-facet__filter-item .collapsible__content {
  padding-left: 8px;
}

[dir="rtl"] .product-facet__filter-item .collapsible__content {
  padding-right: 8px;
}

.product-facet__filter-item:not(:last-child) .collapsible__content {
  margin-bottom: 25px;
}

.product-facet__submit {
  margin-top: 40px;
}

[dir="ltr"] .product-facet__active-count {
  margin-left: 8px;
}

[dir="rtl"] .product-facet__active-count {
  margin-right: 8px;
}

[dir="ltr"] .product-facet__sort-by-title {
  padding-right: 7px;
}

[dir="rtl"] .product-facet__sort-by-title {
  padding-left: 7px;
}

.product-facet__product-list {
  margin-top: calc(var(--container-gutter) / 2);
}

/* Meta bar */

.product-facet__meta-bar {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  margin-bottom: 16px;
}

.product-facet__meta-bar-item {
  display: flex;
  align-items: center;
}

[dir="ltr"] .product-facet__meta-bar-item .icon--filters {
  margin-right: 13px;
}

[dir="rtl"] .product-facet__meta-bar-item .icon--filters {
  margin-left: 13px;
}

/* Toolbar */

[dir="ltr"] .mobile-toolbar__item .icon--filters {
  margin-right: 13px;
}

[dir="rtl"] .mobile-toolbar__item .icon--filters {
  margin-left: 13px;
}

@media screen and (max-width: 740px) {
  .product-facet__active-list {
    margin-left: -18px;
    margin-right: -18px;
  }

  .mobile-toolbar__item--filters.has-filters .mobile-toolbar__item-label::after {
    position: absolute;
    content: '';
    width: 6px;
    height: 6px;
    background: currentColor;
    border-radius: 6px;
  }
}

@media screen and (max-width: 999px) {
  .product-facet__filters:not(.drawer) {
    display: none; /* By default the filters is hidden until dynamically transformed to a modal */
  }
}

@media screen and (min-width: 741px) {
  .product-facet {
    margin-top: 40px;
    margin-bottom: 80px;
  }

  .product-facet__active-list {
    margin-top: 18px;
    margin-bottom: 0;
  }

  .drawer .product-facet__active-list {
    margin-top: 26px; /* Margin is bigger when it is inside the drawer to keep visual rhythm consistency */
  }

  .product-facet__meta-bar {
    margin-bottom: 24px;
  }

  [dir="ltr"] .product-facet__meta-bar-item--filter {
    margin-right: 44px;
  }

  [dir="rtl"] .product-facet__meta-bar-item--filter {
    margin-left: 44px;
  }
}

@media screen and (min-width: 1000px) {
  .product-facet {
    display: flex;
    justify-content: flex-start;
  }

  [dir="ltr"] .product-facet__aside {
    margin-right: 40px;
  }

  [dir="rtl"] .product-facet__aside {
    margin-left: 40px;
  }

  .product-facet__aside {
    flex: none;
    width: 230px;
  }

  .product-facet__aside-inner {
    position: -webkit-sticky;
    position: sticky;
    display: block;
    top: calc(var(--header-height, 0px) * var(--enable-sticky-header) + var(--announcement-bar-height, 0px) * var(--enable-sticky-announcement-bar) + 24px);
  }

  .product-facet__main {
    flex: 1 0 0;
  }

  .product-facet__filters:not(.drawer) .drawer__content {
    overflow: visible;
    padding: 0;
  }

  .product-facet__meta-bar {
    justify-content: flex-start;
  }

  [dir="ltr"] .product-facet__meta-bar-item:last-child {
    margin-left: auto;
  }

  [dir="rtl"] .product-facet__meta-bar-item:last-child {
    margin-right: auto;
  }
}

@media screen and (min-width: 1200px) {
  .product-facet__aside {
    width: 265px;
  }
}

/* General. The back button has a very specific positioning that is used only here so it has special style */

.account__block-list  {
  display: grid;
  grid-row-gap: 24px;
  row-gap: 24px;
}

.account__block-item:empty {
  display: none;
}

.account__back-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
  z-index: 1;
}

[dir="ltr"] .account__back-button svg {
  margin-right: 14px;
}

[dir="rtl"] .account__back-button svg {
  margin-left: 14px;
}

@media screen and (min-width: 741px) {
  .account__block-list  {
    row-gap: 32px;
  }
}

@media screen and (min-width: 1000px) {
  [dir="ltr"] .account__back-button {
    left: var(--container-outer-width);
  }
  [dir="rtl"] .account__back-button {
    right: var(--container-outer-width);
  }
  .account__back-button {
    position: absolute;
    margin-top: 48px;
  }
}

/* Main account */

.account__order-table-item:hover .link--animated::after {
  transform: scale(1);
  transform-origin: var(--transform-origin-start);
}

.account__order-list-item + .account__order-list-item {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid rgb(var(--border-color));
}

.account__order-item-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 24px 64px;
  gap: 24px 64px;
  margin-top: 16px;
  margin-bottom: 16px;
}

.account__order-item-block .heading {
  margin-bottom: 8px;
}

/* Order details */

.account__order-date {
  display: block;
  margin-top: -6px; /* Small irregularity here as the designer want the date to be closer */
}

@media screen and (max-width: 740px) {
  [dir="ltr"] .account--order .page-header {
    text-align: left;
  }
  [dir="rtl"] .account--order .page-header {
    text-align: right;
  }
  .account--order .page-header { /* Small exception */
  }
}

@media screen and (min-width: 741px) {
  .account__order-date {
    margin-top: -18px;
  }

  .account__order-addresses .account__addresses-list {
    margin-top: 24px;
  }
}

/* Addresses */

.account__addresses-list {
  display: grid;
  border: 1px solid rgb(var(--border-color));
}

.account__address {
  display: flex;
  flex-direction: column;
  padding: 24px;
  min-height: 200px;
}

.account__address--auto {
  min-height: 0 !important;
}

.account__address:not(:first-child) {
  border-top: 1px solid rgb(var(--border-color));
}

.account__address--empty {
  justify-content: center;
  align-items: center;
}

.account__address--empty svg {
  margin-bottom: 16px;
}

.account__address-details {
  margin-top: 10px;
}

.account__address--empty {
  background: rgb(var(--secondary-background));
}

.account__address-actions {
  display: grid;
  grid-auto-flow: column;
  justify-content: flex-start;
  grid-gap: 20px;
  gap: 20px;
  margin-top: auto;
  padding-top: 10px;
}

@media screen and (min-width: 741px) {
  .account__addresses-list {
    border: none;
    grid-template-columns: repeat(auto-fit, 50%);
    justify-content: center;
  }

  [dir="ltr"] .account__address {
    border-right: 1px solid rgb(var(--border-color));
  }

  [dir="rtl"] .account__address {
    border-left: 1px solid rgb(var(--border-color));
  }

  .account__address {
    border-top: 1px solid rgb(var(--border-color));
    border-bottom: 1px solid rgb(var(--border-color));
  }
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .account__address:nth-child(2n) ~ .account__address {
    border-top: none;
  }

  [dir="ltr"] .account__address:nth-child(2n + 1) {
    border-left: 1px solid rgb(var(--border-color));
  }

  [dir="rtl"] .account__address:nth-child(2n + 1) {
    border-right: 1px solid rgb(var(--border-color));
  }
}

@media screen and (min-width: 1000px) {
  .account__addresses-list:not(.account__addresses-list--wide) {
    grid-template-columns: repeat(auto-fit, 33.33333%);
  }

  .account__address:nth-child(3n) ~ .account__address {
    border-top: none;
  }

  [dir="ltr"] .account__address:nth-child(3n + 1) {
    border-left: 1px solid rgb(var(--border-color));
  }

  [dir="rtl"] .account__address:nth-child(3n + 1) {
    border-right: 1px solid rgb(var(--border-color));
  }
}
.announcement-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 20px;
  padding-right: 20px;
  background: rgb(var(--section-background));
}

.announcement-bar--multiple {
  justify-content: space-between;
}

.announcement-bar__list {
  padding-left: 10px;
  padding-right: 10px;
}

.announcement-bar__item {
  display: block;
}

.announcement-bar__item[hidden] {
  visibility: hidden;
  height: 0;
}

.announcement-bar__message {
  padding-top: 15px;
  padding-bottom: 15px;
  text-align: center;
}

[dir="ltr"] .announcement-bar__message .link {
  margin-left: 4px;
}

[dir="rtl"] .announcement-bar__message .link {
  margin-right: 4px;
}

[dir="ltr"] .announcement-bar__close-button {
  right: var(--container-gutter);
}

[dir="rtl"] .announcement-bar__close-button {
  left: var(--container-gutter);
}

.announcement-bar__close-button {
  position: absolute;
  top: var(--container-gutter);
}

.announcement-bar__content {
  display: block;
  z-index: 5;
}

.announcement-bar__content[hidden] {
  visibility: hidden;
}

.announcement-bar__content-inner {
  background: rgb(var(--section-background));
}

[dir="ltr"] .announcement-bar__content-overlay {
  left: 0;
}

[dir="rtl"] .announcement-bar__content-overlay {
  right: 0;
}

.announcement-bar__content-overlay {
  position: absolute;
  content: '';
  height: 100vh;
  width: 100%;
  bottom: calc(100% - 10px); /* There is a border radius on the header on mobile so we slightly move down the overlay */
  background: #000000;
  opacity: 0.3;
  z-index: -1;
  transition: opacity 0.5s ease-in-out;
}

.announcement-bar__content[hidden] .announcement-bar__content-overlay {
  opacity: 0;
}

@media screen and (max-width: 740px) {
  [dir="ltr"] .announcement-bar__content {
    left: 0;
  }
  [dir="rtl"] .announcement-bar__content {
    right: 0;
  }
  .announcement-bar__content {
    position: fixed;
    bottom: 0;
    width: 100%;
    transition: visibility 0.6s linear, opacity 0.6s cubic-bezier(0.75, 0, 0.175, 1), transform 0.6s cubic-bezier(0.75, 0, 0.175, 1);
  }

  .announcement-bar__content[hidden] {
    transform: translateY(100%);
  }

  .announcement-bar__content-inner {
    display: flex;
    flex-direction: column;
    max-height: 81vh;
    border-radius: 10px 10px 0 0;
    overflow: hidden;
  }

  .announcement-bar__content-image {
    flex: none;
  }

  .announcement-bar__content-text-wrapper {
    overflow: auto;
    padding: 32px 48px;
    text-align: center;
  }

  .announcement-bar__content.has-image .announcement-bar__close-button {
    color: rgb(255, 255, 255);
  }

  @supports (padding: max(0px)) {
    .announcement-bar__content-text-wrapper {
      padding-bottom: max(32px, env(safe-area-inset-bottom, 0px) + 32px);
    }
  }
}

@media screen and (min-width: 741px) {
  .announcement-bar {
    position: relative;
    justify-content: center;
    padding-left: var(--container-gutter);
    padding-right: var(--container-gutter);
  }

  .announcement-bar__list {
    max-width: var(--container-max-width);
    padding-left: 40px;
    padding-right: 40px;
  }

  [dir="ltr"] .announcement-bar__content {
    left: 0;
  }

  [dir="rtl"] .announcement-bar__content {
    right: 0;
  }

  .announcement-bar__content {
    position: absolute;
    top: 100%;
    width: 100%;
    box-shadow: 0 -1px rgba(var(--text-color), 0.2);
    transition: visibility 0.5s linear, box-shadow 0.5s ease-in-out;
  }

  .announcement-bar__content[hidden] {
    box-shadow: none;
  }

  .announcement-bar__content-overflow {
    overflow: hidden;
    height: calc(100vh - var(--announcement-bar-height));
  }

  .announcement-bar__content-inner {
    display: grid;
    grid-auto-columns: 1fr;
    max-height: 80vh;
    overflow: hidden;
    align-items: center;
    background: rgb(var(--section-background));
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.5s cubic-bezier(0.75, 0, 0.175, 1), transform 0.5s cubic-bezier(0.75, 0, 0.175, 1);
  }

  .announcement-bar__content[hidden] .announcement-bar__content-inner {
    transform: translateY(-100%);
    opacity: 0;
  }

  .announcement-bar__content-overlay {
    bottom: auto;
    top: 0;
  }

  .announcement-bar__content-image,
  .announcement-bar__content-text-wrapper {
    grid-row: 1;
    max-height: inherit;
  }

  .announcement-bar__content-image {
    height: 100%;
    max-height: max-content;
    -o-object-fit: cover;
       object-fit: cover;
    -o-object-position: center;
       object-position: center;
  }

  .announcement-bar__content-text-wrapper {
    padding: 80px var(--container-gutter);
    overflow: auto;
    overscroll-behavior: contain;
  }

  .announcement-bar__content-text {
    max-width: 420px;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
  }

  .announcement-bar__message {
    min-width: 495px;
    max-width: 1000px;
  }
}
/* Header part */

.article__header {
  position: relative;
  display: block;
  background: rgb(var(--section-header-background));
}

.article__header-content {
  padding: 40px var(--container-gutter);
  margin-left: auto;
  margin-right: auto;
  color: rgb(var(--text-color));
}

.article__image-wrapper {
  overflow: hidden;
}

@media screen and (min-width: 1000px) {
  .article__header {
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
  }

  .article__header-content {
    padding-top: 72px;
    padding-bottom: 72px;
  }

  /* Because the header of blog post is not in the natural flow, we have to position the breadcrumb differently */
  [dir="ltr"] .article__header .breadcrumb {
    left: var(--container-outer-margin);
  }
  [dir="rtl"] .article__header .breadcrumb {
    right: var(--container-outer-margin);
  }

  .article__image-wrapper {
    flex: none;
    align-self: stretch;
  }

  .article__image-wrapper--tall {
    width: 37.5%;
  }

  .article__image-wrapper--square {
    width: 50%;
  }

  .article__image {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
       object-fit: cover;
    -o-object-position: center;
       object-position: center;
  }

  [dir="ltr"] .article__image-wrapper--tall + .article__header-content {
    padding-right: 100px;
  }

  [dir="rtl"] .article__image-wrapper--tall + .article__header-content {
    padding-left: 100px;
  }

  .article__header-content:only-child {
    padding-left: 0;
    padding-right: 0;
    max-width: 668px;
    text-align: center;
  }
}

@media screen and (min-width: 1400px) {
  [dir="ltr"] .article__header-content {
    padding-left: calc(var(--container-outer-margin) + var(--grid-column-width) + var(--grid-gap));
  }
  [dir="rtl"] .article__header-content {
    padding-right: calc(var(--container-outer-margin) + var(--grid-column-width) + var(--grid-gap));
  }
  [dir="ltr"] .article__header-content {
    padding-right: 70px;
  }
  [dir="rtl"] .article__header-content {
    padding-left: 70px;
  }

  [dir="ltr"] .article__image-wrapper--tall + .article__header-content {
    padding-right: 160px;
  }

  [dir="rtl"] .article__image-wrapper--tall + .article__header-content {
    padding-left: 160px;
  }
}

/* Top navigation */

.article__nav {
  position: fixed;
  display: block;
  width: 100%;
  top: calc(var(--enable-sticky-header) * var(--header-height, 0px) + var(--announcement-bar-height, 0px) * var(--enable-sticky-announcement-bar));
  border-bottom: 1px solid rgb(var(--border-color));
  background: rgb(var(--background));
  z-index: 1;
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  transition: 0.3s transform ease, 0.3s opacity ease, 0.3s visibility ease;
}

.article__nav.is-visible {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

[dir="ltr"] .article__nav::after {
  left: 0;
}

[dir="rtl"] .article__nav::after {
  right: 0;
}

.article__nav::after {
  position: absolute;
  content: '';
  bottom: 0;
  height: 2px;
  width: 100%;
  transform-origin: var(--transform-origin-start);
  transform: scaleX(var(--transform));
  background: currentColor;
  box-shadow: 0 1px currentColor;
}

.article__nav-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  padding: 18px 0;
}

.article__nav-item {
  display: flex;
  align-items: center;
}

.article__nav-item-title {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
}

[dir="ltr"] .article__nav-item-label {
  margin-right: 10px;
}

[dir="rtl"] .article__nav-item-label {
  margin-left: 10px;
}

[dir="ltr"] .article__nav-item--next .article__nav-arrow {
  margin-left: 20px;
}

[dir="rtl"] .article__nav-item--next .article__nav-arrow {
  margin-right: 20px;
}

[dir="ltr"] .article__nav-item--prev .article__nav-arrow {
  margin-right: 20px;
}

[dir="rtl"] .article__nav-item--prev .article__nav-arrow {
  margin-left: 20px;
}

.article__reading-time {
  flex-shrink: 0;
}

@media screen and (max-width: 740px) {
  .article__nav-item {
    width: 100%;
    justify-content: center;
    transition: transform 0.2s ease-in-out, opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
    will-change: transform;
  }

  [dir="ltr"] .article__nav-item--next {
    left: 0;
  }

  [dir="rtl"] .article__nav-item--next {
    right: 0;
  }

  .article__nav-item--next {
    position: absolute;
    align-items: baseline;
    opacity: 0;
    transform: translateY(-6px);
    visibility: hidden;
    transition-delay: 0s;
  }

  .article__nav-item--current {
    transition-delay: 0.15s;
  }

  [dir="ltr"] .article__nav-arrow {
    margin-left: auto;
  }

  [dir="rtl"] .article__nav-arrow {
    margin-right: auto;
  }

  .article__nav-arrow {
    align-self: center;
  }

  .article__nav--show-next .article__nav-item--current {
    opacity: 0;
    transform: translateY(6px);
    transition-delay: 0s;
  }

  .article__nav--show-next .article__nav-item--next {
    opacity: 1;
    transform: translateX(0);
    visibility: visible;
    transition-delay: 0.15s;
  }
}

@media screen and (min-width: 741px) {
  .article__nav-wrapper {
    justify-content: center;
  }

  .article__nav-item--prev,
  .article__nav-item--next {
    position: absolute;
  }

  [dir="ltr"] .article__nav-item--prev {
    left: 0;
  }

  [dir="rtl"] .article__nav-item--prev {
    right: 0;
  }

  [dir="ltr"] .article__nav-item--next {
    right: 0;
  }

  [dir="rtl"] .article__nav-item--next {
    left: 0;
  }

  .article__nav-item-title {
    max-width: 225px;
  }

  .article__nav-item--prev .article__nav-item-title,
  .article__nav-item--next .article__nav-item-title {
    opacity: 0;
    transform: translateY(-6px);
    transition: transform 0.2s ease-in-out, opacity 0.2s ease-in-out;
    will-change: transform;
  }

  .article__nav-item:hover .article__nav-item-title,
  .article__nav-item:focus .article__nav-item-title {
    opacity: 1;
    transform: translateY(0);
  }
}

@media screen and (min-width: 1000px) {
  .article__nav-wrapper {
    padding: 27px 0;
  }
}

@media screen and (min-width: 1200px) {
  .article__nav-item-title {
    max-width: 300px;
  }
}

/* Inner part */

.article {
  margin: 40px 0;
}

.article__inner {
  position: relative;
  display: flex;
  justify-content: center;
  flex-direction: column-reverse;
}

.article__info {
  display: grid;
  grid-gap: 14px;
  gap: 14px;
  margin-top: 40px;
  padding-top: 24px;
  border-top: 1px solid rgb(var(--border-color));
  width: 100%;
}

.article__meta-item + .article__meta-item::before {
  display: inline-block;
  content: '';
  width: 4px;
  height: 4px;
  margin-left: 12px;
  margin-right: 12px;
  vertical-align: 2px;
  background: rgb(var(--text-color));
}

.article__tags {
  display: flex;
  align-items: center;
}

[dir="ltr"] .article__tags-item {
  margin-right: 12px;
}

[dir="rtl"] .article__tags-item {
  margin-left: 12px;
}

.article__tags-item {
  display: inline-block;
}

[dir="ltr"] .article__tags-label {
  margin-right: 15px;
}

[dir="rtl"] .article__tags-label {
  margin-left: 15px;
}

.article__share {
  display: flex;
  align-items: center;
  width: max-content;
}

.article__info {
  max-width: max-content;
}

[dir="ltr"] .article__share-button-list {
  margin-left: 15px;
}

[dir="rtl"] .article__share-button-list {
  margin-right: 15px;
}

.article__share-button-list {
  display: grid;
  grid-auto-flow: column;
  justify-content: flex-start;
  align-items: center;
  grid-gap: 26px;
  gap: 26px;
  list-style: none;
  padding: 0;
}

.article__comments-count {
  display: inline-flex;
  vertical-align: top;
}

[dir="ltr"] .article__comments-count svg {
  margin-right: 8px;
}

[dir="rtl"] .article__comments-count svg {
  margin-left: 8px;
}

.article__comments-count svg {
  position: relative;
  top: 1px; /* Micro alignment required by designer */
}

@media screen and (min-width: 1000px) {
  .article {
    margin-top: 80px;
    margin-bottom: 80px;
  }

  .article__inner {
    align-items: center;
  }

  .article__content,
  .article__info {
    max-width: 668px;
    flex-grow: 1;
  }
}

@media screen and (min-width: 1200px) {
  .article__inner {
    flex-direction: column;
    min-height: 200px;
  }

  [dir="ltr"] .article__info {
    left: 0;
  }

  [dir="rtl"] .article__info {
    right: 0;
  }

  .article__info {
    position: absolute;
    top: 0;
    width: 170px;
    margin-top: 0;
    padding-top: 30px;
    gap: 28px;
  }

  .article__meta {
    display: grid;
    grid-gap: 8px;
    gap: 8px;
    justify-content: flex-start;
  }

  .article__meta-item::before {
    display: none !important;
  }

  .article__tags-label {
    margin-bottom: 10px;
  }

  .article__share,
  .article__tags {
    display: block;
  }

  .article__share-label {
    display: block;
    margin-bottom: 16px;
  }

  [dir="ltr"] .article__share-button-list {
    margin-left: 6px;
  }

  [dir="rtl"] .article__share-button-list {
    margin-right: 6px;
  }
}

@media screen and (min-width: 1400px) {
  .article__inner {
    margin-left: calc(var(--grid-column-width) + var(--grid-gap));
    margin-right: calc(var(--grid-column-width) + var(--grid-gap));
  }

  .article__info {
    width: 185px;
  }
}

/* Prev next */

.article__prev-next {
  background: rgb(var(--secondary-background));
}

@media screen and (min-width: 741px) {
  .article__prev-next .article-list {
    grid-template-columns: none;
    grid-auto-columns: 310px;
    justify-content: center;
  }

  .article__prev-next .article-item {
    width: auto !important;
  }
}

/* Comment */

.article__comment-list-heading {
  margin-bottom: 30px;
}

.article-comment {
  padding: 24px;
  background: rgb(var(--secondary-background));
  border-radius: var(--block-border-radius-reduced);
}

.article-comment + .article-comment {
  margin-top: 16px;
}

.article-comment__meta {
  display: flex;
  align-items: center;
}

[dir="ltr"] .article-comment__gravatar {
  margin-right: 16px;
}

[dir="rtl"] .article-comment__gravatar {
  margin-left: 16px;
}

.article-comment__gravatar {
  border-radius: 100%;
  width: 40px;
}

.article-comment__author {
  margin-bottom: 0;
}

.article-comment__date {
  display: block;
  margin-top: 2px;
  margin-bottom: 4px;
}

.article-comment__content {
  margin-top: 15px;
}

.article__comment-list + .article__comment-form {
  margin-top: 48px;
}

.article__comment-form-title {
  margin-top: 0;
}

@media screen and (min-width: 741px) {
  .article-comment {
    padding: 32px;
  }

  [dir="ltr"] .article-comment__gravatar {
    margin-right: 21px;
  }

  [dir="rtl"] .article-comment__gravatar {
    margin-left: 21px;
  }

  .article-comment__gravatar {
    width: 48px;
    align-self: flex-start;
  }

  .article__comment-list-heading {
    margin-bottom: 34px;
  }

  .article__comment-list + .article__comment-form {
    margin-top: 64px;
  }
}

@media screen and (min-width: 1000px) {
  .article__comment-box {
    max-width: 748px;
    margin-left: auto;
    margin-right: auto;
  }
}
.article-list {
  --article-list-row-gap: 40px;
  --article-list-column-gap: var(--container-gutter);

  display: grid;
  grid-gap: var(--article-list-row-gap) var(--article-list-column-gap);
  gap: var(--article-list-row-gap) var(--article-list-column-gap);
}

.article-list--scrollable {
  grid-auto-flow: column;
}

.article-item {
  width: 100%;
}

.article-item__image-container {
  position: relative;
  display: block;
  margin-bottom: 20px;
  overflow: hidden;
  border-radius: var(--block-border-radius-reduced);
  z-index: 0; /* Creating new stacking context is needed on Safari to apply the border radius */
}

[dir="ltr"] .article-item__arrow {
  right: 20px;
}

[dir="rtl"] .article-item__arrow {
  left: 20px;
}

.article-item__arrow {
  position: absolute;
  bottom: 20px;
  border: none;
  opacity: 0;
  visibility: hidden;
  transform: scale(0.5);
  transition: opacity 0.15s ease-in-out, visibility 0.15s ease-in-out, transform 0.15s ease-in-out;
}

.article-item--horizontal {
  display: flex;
  align-items: center;
}

.article-item--horizontal .article-item__image-container {
  border-radius: calc(var(--block-border-radius-reduced) / 2); /* When shown as horizontal the tiles are smaller so we reduced the radius */
  z-index: 0; /* Creating new stacking context is needed on Safari to apply the border radius */
}

[dir="ltr"] .article-item--horizontal .article-item__arrow {
  right: 12px;
}

[dir="rtl"] .article-item--horizontal .article-item__arrow {
  left: 12px;
}

.article-item--horizontal .article-item__arrow {
  bottom: 12px;
}

[dir="ltr"] .article-item--featured .article-item__arrow {
  right: 32px;
}

[dir="rtl"] .article-item--featured .article-item__arrow {
  left: 32px;
}

.article-item--featured .article-item__arrow {
  bottom: 32px;
}

.article-item__category {
  color: rgba(var(--text-color), 0.7);
  width: max-content;
}

.article-item__excerpt {
  margin-top: -4px; /* We want the excerpt to be a bit closer to the title than usual paragraph */
}

.article-list--section .article-item:only-child {
  max-width: 668px;
}

@media screen and (max-width: 999px) {
  .article-list--scrollable .article-item:not(:only-child) {
    width: 81vw;
    scroll-snap-align: center;
    scroll-snap-stop: always;
  }
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .article-list--has-four {
    grid-template-columns: repeat(2, 1fr);
  }

  .article-list--scrollable .article-item:not(:only-child) {
    width: 52vw;
  }
}

@media screen and (min-width: 741px) {
  .article-list--stacked {
    grid-template-columns: repeat(auto-fit, calc(100% / 3 - var(--article-list-column-gap) * (2 / 3)));
    justify-content: safe center;
  }
}

@media screen and (min-width: 1000px) {
  .article-list {
    --article-list-row-gap: 48px;
    --article-list-column-gap: 48px;
  }

  .article-list + .pagination {
    margin-top: 64px; /* Small exception here to separate more the pagination */
  }

  .article-list--section {
    grid-template-columns: repeat(auto-fit, calc(100% / 3 - var(--article-list-column-gap) * (2 / 3)));
    justify-content: safe center;
  }

  .article-item__image-container {
    margin-bottom: 24px;
  }

  .article-item__excerpt {
    margin-top: -8px; /* We want the excerpt to be a bit closer to the title than usual paragraph */
  }

  /* When this layout is used, the most recent blog post takes larger part */
  .article-list--collage {
    grid-template-columns: 1.37731fr 1fr;
    column-gap: 48px;
  }

  .article-item--featured .article-item__image-container {
    margin-bottom: 32px;
  }

  .article-list__secondary-list {
    display: grid;
    grid-auto-rows: max-content;
    grid-row-gap: 48px;
    row-gap: 48px;
  }

  [dir="ltr"] .article-list__secondary-list .article-item__image-container {
    margin-right: 32px;
  }

  [dir="rtl"] .article-list__secondary-list .article-item__image-container {
    margin-left: 32px;
  }

  .article-list__secondary-list .article-item__image-container {
    width: 42%;
    flex: none;
    margin-bottom: 0;
  }
}

@media screen and (min-width: 1200px) {
  .article-list {
    margin-left: calc(var(--grid-column-width) + var(--grid-gap));
    margin-right: calc(var(--grid-column-width) + var(--grid-gap));
  }

  .article-list--collage {
    column-gap: 70px;
  }
}

@media screen and (pointer: fine) {
  .article-item:hover .article-item__arrow {
    visibility: visible;
    opacity: 1;
    transform: scale(1);
  }
}
/* General */

.checkout-button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

[dir="ltr"] .checkout-button__lock {
  left: 18px;
}

[dir="rtl"] .checkout-button__lock {
  right: 18px;
}

.checkout-button__lock {
  position: absolute;
}

[dir="ltr"] .checkout-button .square-separator {
  margin-left: calc(12px - 0.5 * 2px);
  margin-right: 12px;
}

[dir="rtl"] .checkout-button .square-separator {
  margin-right: calc(12px - 0.5 * 2px);
  margin-left: 12px;
}

.checkout-button .square-separator { /* This is a micro adjustment but because of the letter spacing we have to compensate with negative margin */
}

@media screen and (min-width: 741px) {
  [dir="ltr"] .checkout-button__lock {
    left: 20px;
  }
  [dir="rtl"] .checkout-button__lock {
    right: 20px;
  }
}

/* Discount badge (used both at line and cart level) */

.discount-badge {
  display: flex;
  align-items: center;
  padding: 3px 6px;
  font-family: var(--text-font-family);
  font-style: var(--text-font-style);
  font-weight: var(--text-font-bold-weight);
  font-size: 11px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: rgb(var(--heading-color));
  color: rgb(var(--background));
}

[dir="ltr"] .discount-badge svg {
  margin-right: 8px;
}

[dir="rtl"] .discount-badge svg {
  margin-left: 8px;
}

.discount-badge svg {
  position: relative;
}

/* Free shipping bar */

.shipping-bar {
  display: block;
  margin-top: 16px;
  margin-bottom: 4px;
  margin-left: auto;
  margin-right: auto;
  width: 100%;
  text-align: center;
}

.shipping-bar__text {
  display: block;
}

.shipping-bar__progress {
  position: relative;
  display: block;
  margin-top: 6px;
  height: 7px;
  border: 2px solid currentColor;
  border-radius: 4px;
}

.shipping-bar__progress::after {
  position: absolute;
  content: '';
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: currentColor;
  transition: transform 0.2s ease;
  transform: scaleX(var(--progress));
  transform-origin: var(--transform-origin-start);
}

.shipping-bar--large {
  max-width: 392px;
}

@media screen and (min-width: 741px) {
  .shipping-bar {
    margin-bottom: 8px;
  }

  .shipping-bar__progress {
    margin-top: 8px;
    height: 8px;
  }

  .shipping-bar--large .shipping-bar__progress {
    margin-top: 16px;
  }
}

/* Line item */

.line-item {
  display: flow-root;
}

.line-item__content-wrapper {
  position: relative;
  display: flex;
  margin-top: 20px;
}

.line-item--centered .line-item__content-wrapper {
  align-items: center;
}

[dir="ltr"] .line-item__image-wrapper {
  margin-right: 24px;
}

[dir="rtl"] .line-item__image-wrapper {
  margin-left: 24px;
}

.line-item__image-wrapper {
  position: relative;
  display: block;
  width: 80px;
  flex: none;
  align-self: flex-start;
}

.line-item__image {
  border-radius: min(var(--block-border-radius), 4px);
}

[dir="ltr"] .line-item__loader {
  left: calc(50% - 16px);
}

[dir="rtl"] .line-item__loader {
  right: calc(50% - 16px);
}

.line-item__loader {
  display: flex;
  position: absolute;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  top: calc(50% - 16px);
  background: rgb(var(--root-background));
  color: rgb(var(--root-text-color));
  border-radius: 32px;
  transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out, visibility 0.2s ease-in-out;
}

.line-item__loader[hidden] {
  transform: scale(0.4);
  opacity: 0;
  visibility: hidden;
}

/* Used for secondary info such as variant title, subscription plan, attributes... */

.line-item__quantity {
  display: block;
  margin-top: 8px;
}

.line-item__discount-list + .line-item__quantity {
  margin-top: 12px;
}

[dir="ltr"] .line-item__remove-button {
  margin-left: 12px;
}

[dir="rtl"] .line-item__remove-button {
  margin-right: 12px;
}

/* Discount at line item level */

.line-item__discount-list {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-top: 8px;
}

.line-item__discount-badge:not(:last-child) {
  margin-bottom: 4px;
}

@media screen and (min-width: 741px) {
  .line-item__content-wrapper {
    margin-top: 24px;
  }

  .line-item__image-wrapper {
    width: 92px;
  }

  .line-item__quantity {
    vertical-align: top;
    margin-top: 14px;
  }

  [dir="ltr"] .line-item__price-list-container {
    margin-left: auto;
  }

  [dir="rtl"] .line-item__price-list-container {
    margin-right: auto;
  }

  [dir="ltr"] .line-item__price-list-container {
    padding-left: 30px;
  }

  [dir="rtl"] .line-item__price-list-container {
    padding-right: 30px;
  }

  .line-item__price-list-container {
    flex-shrink: 0;
    line-height: 1.5; /* Used to simulate the same line-height as the product title */ /* Gives a minimum spacing for the price */
    text-align: right;
    vertical-align: top;
  }

  [dir="ltr"] .line-item__quantity--block .line-item__remove-button {
    margin-left: 0;
  }

  [dir="rtl"] .line-item__quantity--block .line-item__remove-button {
    margin-right: 0;
  }

  .line-item__quantity--block .line-item__remove-button {
    display: block;
    margin-top: 10px;
  }

  .line-item__discount-list {
    margin-top: 14px;
  }
}

/* FULFILLMENT */

@media screen and (min-width: 741px) {
  [dir="ltr"] .line-item__fulfillment {
    margin-left: 116px;
  }
  [dir="rtl"] .line-item__fulfillment {
    margin-right: 116px;
  }
}

/* TABLE */

.line-item-table {
  margin-bottom: 40px;
}

.line-item-table .line-item {
  display: table-row;
}

.line-item-table .line-item__content-wrapper {
  margin-top: 0; /* The spacing is brought by the table */
}

@media screen and (max-width: 740px) {
  .line-item-table {
    table-layout: fixed;
  }

  .line-item-table__list .line-item:first-child .line-item__product {
    padding-top: 0;
  }

  [dir="ltr"] .line-item-table__list .line-item__product {
    padding-right: 0;
  }

  [dir="rtl"] .line-item-table__list .line-item__product {
    padding-left: 0;
  }

  .line-item-table__list .line-item__product {
    width: 100%;
  }

  .line-item-table__footer {
    display: table-row;
  }

  [dir="ltr"] .line-item-table__footer td:nth-child(2) {
    padding-left: 0;
  }

  [dir="rtl"] .line-item-table__footer td:nth-child(2) {
    padding-right: 0;
  }

  .line-item-table__footer td:nth-child(2) {
    width: 100%;
  }
}

@media screen and (min-width: 741px) {
  .line-item-table__list .line-item__quantity {
    margin-top: 0;
  }
}

/* Mini-cart specificities */

.mini-cart__discount-list {
  display: grid;
  grid-gap: 10px;
  gap: 10px;
  margin-bottom: 8px;
}

.mini-cart__discount {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

[dir="ltr"] .mini-cart__discount-badge {
  margin-right: 14px;
}

[dir="rtl"] .mini-cart__discount-badge {
  margin-left: 14px;
}

.mini-cart__actions {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-bottom: 14px;
}

[dir="ltr"] .mini-cart__order-note {
  left: 0;
}

[dir="rtl"] .mini-cart__order-note {
  right: 0;
}

.mini-cart__order-note {
  display: block;
  position: absolute;
  bottom: 0;
  width: 100%;
  background: rgb(var(--background));
  padding: var(--container-gutter);
  z-index: 1;
  visibility: hidden;
  opacity: 0;
  transform: translateY(100%);
  transition: visibility 0.25s ease-in-out, opacity 0.25s ease-in-out, transform 0.25s ease-in-out;
}

.mini-cart__order-note[open] {
  visibility: visible;
  opacity: 1;
  transform: translateY(0);
}

.mini-cart__order-note-title {
  margin-bottom: 24px;
}

@media screen and (max-width: 740px) {
  .mini-cart__actions {
    flex-direction: column;
    align-items: flex-start;
  }

  .mini-cart__actions > :first-child:not(:only-child) {
    margin-bottom: 7px;
  }

  /* On mobile we want the order note to take full width. As the drawer is taking 89vw, we need to shift left by 11vw */
  [dir="ltr"] .mini-cart__order-note {
    left: -11vw;
  }
  [dir="rtl"] .mini-cart__order-note {
    right: -11vw;
  }
  .mini-cart__order-note {
    width: 100vw;
  }

  @supports (padding: max(0px)) {
    .mini-cart__order-note {
      padding-bottom: max(24px, env(safe-area-inset-bottom, 0px) + 24px);
    }
  }
}

@media screen and (min-width: 741px) {
  .mini-cart__drawer-footer {
    padding-bottom: var(--container-gutter);
  }

  .mini-cart__discount {
    justify-content: flex-end;
  }

  .mini-cart__actions {
    margin-bottom: 26px;
  }
}

/* Mini cart recommendations */

.mini-cart__recommendations:not([hidden]) {
  display: block;
}

.mini-cart__recommendations-inner {
  margin-top: 24px;
  margin-left: calc(-1 * var(--container-gutter));
  margin-right: calc(-1 * var(--container-gutter));
  padding: 16px var(--container-gutter);
  background: rgb(var(--secondary-background));
}

.mini-cart__recommendations-heading {
  margin-top: 0 !important;
}

.mini-cart__recommendations .product-item-meta__title {
  white-space: nowrap;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

@media screen and (max-width: 999px) {
  .mini-cart__recommendations-list {
    display: grid;
    grid-auto-flow: column;
    grid-auto-columns: minmax(64vw, 1fr);
    grid-gap: var(--grid-gap);
  }

  .mini-cart__recommendations .product-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    scroll-snap-align: start;
    scroll-snap-stop: always;
    scroll-margin: var(--container-gutter);
  }

  [dir="ltr"] .mini-cart__recommendations .product-item__image-wrapper {
    margin: 0 24px 0 0;
  }

  [dir="rtl"] .mini-cart__recommendations .product-item__image-wrapper {
    margin: 0 0 0 24px;
  }

  .mini-cart__recommendations .product-item__image-wrapper {
    width: 65px;
    flex: none;
  }

  [dir="ltr"] .mini-cart__recommendations .product-item__info {
    text-align: left;
  }

  [dir="rtl"] .mini-cart__recommendations .product-item__info {
    text-align: right;
  }

  .mini-cart__recommendations .product-item__info {
    min-width: 0;
  }
}

@media screen and (min-width: 1000px) {
  [dir="ltr"] .mini-cart__recommendations {
    right: 100%;
  }
  [dir="rtl"] .mini-cart__recommendations {
    left: 100%;
  }
  .mini-cart__recommendations {
    position: absolute;
    top: 0;
    width: 240px;
    height: 100%;
    overflow: hidden;
    text-align: center;
  }

  .mini-cart__recommendations-inner {
    margin: 0;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    scrollbar-width: thin;
    padding-top: 35px;
    padding-bottom: 35px;
    box-shadow: -10px 0 24px 4px rgb(var(--text-color), 0.05) inset;
    transition: transform 0.25s ease-in;
  }

  .mini-cart:not([open]) .mini-cart__recommendations-inner {
    transform: translateX(100%);
  }

  .mini-cart__recommendations .product-item {
    margin-top: 40px;
  }

  .mini-cart__recommendations .product-item__image-wrapper {
    width: 92px;
    margin: 0 auto 24px;
    flex: none;
  }

  .mini-cart__recommendations .spinner {
    display: flex;
    height: 100%;
  }
}

/* Cart details */

.cart__recap {
  padding: var(--container-gutter);
  background: rgb(var(--secondary-background));
  border-radius: var(--block-border-radius-reduced);
}

.cart__recap-block {
  display: grid;
  grid-gap: 10px;
  gap: 10px;
  margin-bottom: 10px;
}

.cart__recap-block > * {
  margin-top: 0;
  margin-bottom: 0;
}

.cart__recap-note {
  margin-bottom: 24px;
}

.cart__total-container,
.cart__discount {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px;
}

.cart__discount-list {
  display: grid;
  grid-gap: 10px;
  gap: 10px;
}

.cart__discount-badge {
  align-self: stretch;
}

.cart__order-note {
  padding-top: 14px;
}

.cart__checkout-button:not(:only-child) {
  margin-top: 24px;
}

.cart__payment-methods {
  margin-top: 24px;
  text-align: center;
}

.cart__payment-methods-label {
  display: block;
  margin-bottom: 16px;
}

@media screen and (max-width: 999px) {
  .cart__aside {
    margin-top: 24px;
  }
}

@media screen and (min-width: 1000px) {
  .cart {
    display: grid;
    grid-template-columns: 1fr 300px;
    grid-gap: 40px;
    gap: 40px;
  }

  .cart__aside-inner {
    display: block;
    position: -webkit-sticky;
    position: sticky;
    top: calc(var(--header-height, 0px) * var(--enable-sticky-header) + var(--announcement-bar-height, 0px) * var(--enable-sticky-announcement-bar) + 24px);
  }
}

@media screen and (min-width: 1200px) {
  .cart {
    grid-template-columns: 1fr 390px;
    gap: 70px;
  }
}

/* Shipping estimator */

.shipping-estimator {
  border: 1px solid rgb(var(--border-color));
  border-radius: var(--block-border-radius-reduced);
}

.shipping-estimator__toggle-button,
.shipping-estimator__form {
  padding: 24px;
}

.shipping-estimator__form {
  display: block;
  padding-top: 0 !important;
}

.shipping-estimator__results {
  margin-top: 24px;
}

@media screen and (min-width: 741px) {
  .shipping-estimator__form .input-row .input:nth-child(1),
  .shipping-estimator__form .input-row .input:nth-child(2) {
    grid-column: span 2; /* The third one is the zip and can be smaller */
  }

  .shipping-estimator__toggle-button,
  .shipping-estimator__form {
    padding: 32px;
  }
}
/** NOTE: Collection related styles are very scarce, as most of them are abstracted in the "product-facet" that is used on search page as well */

/* Promotion block override for the collection page */

.product-facet__main .promotion-block-list {
  --promotion-block-gutter: 24px;
}

.product-facet__main .promotion-block-list--top {
  margin-bottom: 20px;
}

.product-facet__main .promotion-block-list--bottom {
  margin-top: 36px;
}

@media screen and (max-width: 740px) {
  /* On mobile only we want the promotion block and filters closer to the edge so that they align with products */
  .product-facet__main .promotion-block-list {
    --promotion-block-gutter: 12px;
    margin-left: calc(-1 * (var(--container-gutter) - var(--promotion-block-gutter) / 2));
    margin-right: calc(-1 * (var(--container-gutter) - var(--promotion-block-gutter) / 2));
  }
}

@media screen and (min-width: 741px) {
  .product-facet__main .promotion-block-list--bottom {
    margin-top: 60px;
  }
}
.section__header + .contact__form {
  margin-top: 24px;
}

.contact__text-list {
  padding: 32px;
  border: 1px solid rgba(var(--text-color), 0.15);
}

.contact__text-item + .contact__text-item {
  margin-top: 32px;
}

@media screen and (max-width: 999px) {
  .contact__form,
  .contact__aside {
    max-width: 460px;
    margin-left: auto;
    margin-right: auto;
  }

  .contact__aside {
    margin-top: 40px;
  }
}

@media screen and (min-width: 741px) {
  .contact__text-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(10px, 1fr));
    grid-gap: 32px;
    gap: 32px;
  }

  .contact__text-item {
    margin-top: 0 !important;
  }
}

@media screen and (min-width: 1000px) {
  .contact {
    display: flex;
    flex-direction: row-reverse;
    justify-content: center;
  }

  .contact__main {
    flex: 1 0 auto;
    max-width: 460px;
  }

  .contact__main:only-child {
    flex-grow: 1;
    max-width: none;
  }

  [dir="ltr"] .contact__main:not(:only-child) .section__header {
    text-align: left;
  }

  [dir="rtl"] .contact__main:not(:only-child) .section__header {
    text-align: right;
  }

  .contact__main:not(:only-child) .section__header {
    margin-bottom: 32px;
  }

  .contact__form {
    max-width: 460px;
    margin-left: auto;
    margin-right: auto;
  }

  [dir="ltr"] .contact__aside {
    margin-right: 40px;
  }

  [dir="rtl"] .contact__aside {
    margin-left: 40px;
  }

  .contact__aside {
    max-width: 530px;
  }
}

@media screen and (min-width: 1200px) {
  [dir="ltr"] .contact__aside {
    margin-right: 90px;
  }
  [dir="rtl"] .contact__aside {
    margin-left: 90px;
  }
}
[dir="ltr"] .cookie-bar {
  right: var(--container-gutter);
}
[dir="rtl"] .cookie-bar {
  left: var(--container-gutter);
}
.cookie-bar {
  display: block;
  position: fixed;
  width: calc(100% - var(--container-gutter) * 2);
  bottom: var(--container-gutter);
  padding: 20px;
  max-width: 400px;
  border: 1px solid rgb(var(--root-border-color));
  background: rgb(var(--root-background));
  z-index: 2;
  transition: visibility 0.2s ease-in-out, opacity 0.2s ease-in-out;
}

.cookie-bar[hidden] {
  visibility: hidden;
  opacity: 0;
}

.cookie-bar__actions {
  margin-top: 18px;
}

[dir="ltr"] .cookie-bar__actions .button:last-child {
  margin-left: 4px;
}

[dir="rtl"] .cookie-bar__actions .button:last-child {
  margin-right: 4px;
}

@media screen and (max-width: 740px) {
  @supports (padding: max(0px)) {
    .cookie-bar {
      bottom: max(24px, env(safe-area-inset-bottom, 0px) + 24px);
    }
  }
}

@media screen and (min-width: 741px) {
  .cookie-bar {
    padding: 30px;
  }
}
.faq {
  position: relative;
}

.faq__category {
  --anchor-offset: 20px;

  padding: 20px 24px;
  margin-bottom: 0;
  background: rgb(var(--secondary-background));
}

.faq__item + .faq__item {
  border-top: 1px solid rgba(var(--text-color), 0.15);
}

[dir="ltr"] .faq__item .collapsible__content {
  padding-right: 40px;
}

[dir="rtl"] .faq__item .collapsible__content {
  padding-left: 40px;
}

.faq__item .collapsible__content {
  padding-bottom: 26px; /* We add extra spacing horizontally */
}

@media screen and (max-width: 740px) {
  .faq {
    margin-left: calc(-1 * var(--container-gutter));
    margin-right: calc(-1 * var(--container-gutter));
  }

  .faq__item {
    padding-left: var(--container-gutter);
    padding-right: var(--container-gutter);
  }

  .faq__item ~ .faq__category {
    margin-top: 30px;
  }
}

@media screen and (min-width: 741px) {
  .faq__item .collapsible-toggle,
  .faq__item .collapsible__content {
    padding-left: 24px;
    padding-right: 24px;
  }
}

@media screen and (min-width: 1000px) {
  .faq__wrapper {
    max-width: 668px;
    width: calc(var(--grid-column-width) * 10 + var(--grid-gap) * 9);
    margin-left: auto;
    margin-right: auto;
  }

  [dir="ltr"] .faq-navigation {
    left: 0;
  }

  [dir="rtl"] .faq-navigation {
    right: 0;
  }

  .faq-navigation {
    position: absolute;
    display: block;
    height: 100%;
    max-width: calc(var(--grid-column-width) * 4 + var(--grid-gap) * 3);
    top: 0;
  }
}

@media screen and (min-width: 1400px) {
  [dir="ltr"] .faq-navigation {
    left: calc(var(--grid-column-width) + var(--grid-gap));
  }
  [dir="rtl"] .faq-navigation {
    right: calc(var(--grid-column-width) + var(--grid-gap));
  }
  .faq-navigation {
    max-width: calc(var(--grid-column-width) * 3 + var(--grid-gap) * 2);
  }
}
.footer {
  padding: 48px 0;
  background: rgb(var(--background));
  color: rgb(var(--text-color));
}

.footer--bordered {
  border-top: 1px solid rgb(var(--border-color));
}

.footer__item-list {
  display: grid;
  grid-gap: 40px;
  gap: 40px;
  grid-template-columns: 1fr 1fr;
  justify-content: space-between;
}

.footer__item {
  max-width: 325px; /* Ensure a given item does not grow too big */
  word-break: break-word;
}

.footer__item-title {
  margin-bottom: 12px;
}

.footer__image {
  display: block;
}

.footer__item--social-media .footer__item-content {
  margin-top: 20px;
}

.footer__aside {
  margin-top: 42px;
}

.footer__cross-border {
  display: flex;
}

[dir="ltr"] .footer__cross-border .popover-container + .popover-container {
  margin-left: -1px;
}

[dir="rtl"] .footer__cross-border .popover-container + .popover-container {
  margin-right: -1px;
}

.footer__cross-border .popover-container + .popover-container { /* Allows to collapse the border */
}

.footer__newsletter-form {
  margin-top: 16px;
}

.footer__copyright,
.footer__payment-methods {
  display: block;
  margin-top: 32px;
  color: rgba(var(--footer-text-color), 0.7);
}

.footer__copyright {
  display: flex;
  align-items: center;
}

.footer__payment-methods-label {
  display: inline-block;
  margin-bottom: 8px;
}

@media screen and (max-width: 740px) {
  .footer__item--image,
  .footer__item--newsletter,
  .footer__item--newsletter + .footer__item--social-media:last-child {
    grid-column: span 2;
  }
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .footer__item-list {
    grid-template-columns: 1fr 1fr 1fr;
  }

  .footer__item--image.is-first {
    grid-column: span 3;
  }

  .footer__item--newsletter {
    grid-column: span 2;
  }
}

@media screen and (min-width: 741px) {
  .footer__payment-methods {
    display: flex;
    align-items: center;
  }

  [dir="ltr"] .footer__payment-methods-label {
    margin-right: 14px;
  }

  [dir="rtl"] .footer__payment-methods-label {
    margin-left: 14px;
  }

  .footer__payment-methods-label {
    margin-bottom: 0;
  }
}

@media screen and (min-width: 1000px) {
  .footer {
    padding-top: 72px;
    padding-bottom: 50px;
  }

  .footer__item-list {
    grid-auto-flow: column;
    grid-template-columns: none;
  }

  .footer__item-title {
    margin-bottom: 20px;
  }

  .footer__aside {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: nowrap;
    row-gap: 24px;
    margin-top: 50px;
  }

  [dir="ltr"] .footer__cross-border {
    margin-right: 32px;
  }

  [dir="rtl"] .footer__cross-border {
    margin-left: 32px;
  }

  .footer__payment-methods,
  .footer__copyright {
    margin-top: 0;
  }

  [dir="ltr"] .footer__payment-methods {
    margin-left: auto;
  }

  [dir="rtl"] .footer__payment-methods {
    margin-right: auto;
  }
}

@media screen and (min-width: 1200px) {
  [dir="ltr"] .footer__cross-border {
    margin-right: 50px;
  }
  [dir="rtl"] .footer__cross-border {
    margin-left: 50px;
  }
}
.gallery {
  --gallery-image-height: 370px;
  position: relative;
  display: block;
}

.gallery__list-wrapper:not(.is-scrollable) ~ .custom-drag-cursor {
  visibility: hidden;
}

.gallery__list-wrapper:not(.is-scrollable) ~ .gallery__prev-next-buttons,
.gallery__list-wrapper:not(.is-scrollable) ~ .gallery__progress-bar-wrapper {
  display: none;
}

.gallery__list-wrapper {
  display: block;
}

.gallery__list {
  display: flex;
  flex-wrap: nowrap;
}

.gallery__list-wrapper.is-scrollable .gallery__list::after {
  content: '';
  flex: 0 0 var(--container-outer-width); /* Allows to create the gap after the last image */
}

.gallery__item {
  flex-shrink: 0;
  width: max-content;
}

[dir="ltr"] .gallery__item:not(:first-child) {
  margin-left: var(--container-gutter);
}

[dir="rtl"] .gallery__item:not(:first-child) {
  margin-right: var(--container-gutter);
}

.gallery__figure {
  display: table;
  margin: 0;
}

.gallery__image {
  display: block;
  height: var(--gallery-image-height);
  width: auto;
  border-radius: var(--block-border-radius-reduced);
  overflow: hidden;
  -webkit-user-select: none;
          user-select: none;
}

.gallery__caption {
  display: table-caption;
  caption-side: bottom;
  margin-top: 16px;
}

.gallery__progress-bar {
  display: block;
  margin-top: 32px;
}

[dir="ltr"] .gallery__prev-next-buttons {
  right: calc(var(--container-outer-width) - 28px);
}

[dir="rtl"] .gallery__prev-next-buttons {
  left: calc(var(--container-outer-width) - 28px);
}

.gallery__prev-next-buttons {
  position: absolute;
  top: calc(var(--gallery-image-height) / 2 - 56px); /* 56px is the height of a single button */ /* 28px is half the width of button */
  z-index: 1;
}

@media not screen and (pointer: fine) {
  .gallery__prev-next-buttons {
    display: none !important; /* Arrows are hidden on touch devices */
  }
}

@media screen and (min-width: 1000px) {
  .gallery {
    --gallery-image-height: 40vw;
  }
}

@media screen and (min-width: 1200px) {
  .gallery {
    --gallery-image-height: 35vw;
  }
}

@media screen and (min-width: 1400px) {
  .gallery {
    --gallery-image-height: 30vw;
  }
}
.gift-card {
  -webkit-print-color-adjust: exact;
          color-adjust: exact; /* Make sure it prints using the same background */
  background: rgb(var(--background));
  min-height: var(--window-height, 100vh);
  text-align: center;
}

.gift-card__wrapper {
  max-width: 530px;
  margin-left: auto;
  margin-right: auto;
}

.gift-card__logo {
  margin-bottom: 32px;
}

.gift-card__logo-image {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.gift-card__image-wrapper {
  max-width: 280px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: -65px;
}

.gift-card__image {
  border-radius: 18px;
}

.gift-card__card {
  padding: 32px;
  background: rgb(var(--section-card-background));
  color: rgb(var(--text-color));
}

.gift-card__card + .gift-card__card {
  margin-top: 16px;
}

.gift-card__main {
  padding-top: calc(32px + 65px); /* 65px is the offset of the image */
}

.gift-card__amount {
  color: rgb(var(--product-on-sale-accent));
}

.gift-card__code-container {
  display: grid;
  grid-gap: 8px;
  gap: 8px;
}

.gift-card__code {
  -webkit-appearance: none;
          appearance: none;
  padding-left: 12px;
  padding-right: 12px;
  height: var(--button-height);
  line-height: var(--button-height);
  border: 1px solid rgb(var(--border-color));
  border-radius: 0;
  background: transparent;
}

.gift-card__expires-on {
  margin-top: 16px;
}

.gift-card__aside {
  display: grid;
  grid-gap: 24px;
  gap: 24px;
}

.gift-card__qr {
  display: block;
}

.gift-card__qr img,
.gift-card__wallet {
  margin-left: auto;
  margin-right: auto;
  width: 132px;
}

.gift-card__button-wrapper {
  margin-top: 24px;
}

@media screen and (min-width: 741px) {
  .gift-card__logo {
    margin-bottom: 56px;
  }

  .gift-card__card {
    padding: 40px;
  }

  .gift-card__image-wrapper {
    margin-bottom: -95px;
  }

  .gift-card__main {
    padding-top: calc(40px + 95px); /* 95px is the offset of the image */
  }

  .gift-card__image-wrapper {
    max-width: 360px;
  }

  .gift-card__code-container {
    grid-template-columns: 1fr auto;
    gap: 16px;
  }

  .gift-card__aside {
    grid-auto-flow: column;
    gap: 32px;
    align-items: center;
  }

  [dir="ltr"] .gift-card__aside .heading {
    text-align: left;
  }

  [dir="rtl"] .gift-card__aside .heading {
    text-align: right;
  }

  .gift-card__qr img {
    width: 68px;
  }

  .gift-card__wallet {
    width: 145px;
  }

  .gift-card__button-wrapper {
    margin-top: 40px;
  }
}
.header {
  display: block;
  background: rgb(var(--header-background));
  color: rgb(var(--header-text-color));
  transition: background 0.2s ease-in-out, color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.header--bordered {
  box-shadow: 0 1px transparent;
}

.header--bordered:not(.header--transparent) {
  box-shadow: 0 1px rgb(var(--border-color));
}

.header__logo {
  display: block;
  position: relative;
  margin: 0;
}

.header__logo-link,
.header__logo-image {
  display: block;
  width: max-content;
}

.header__logo-text {
  color: currentColor;
  max-width: min(350px, 60vw);
}

.header__logo-image {
  transition: opacity 0.2s ease-in-out;
}

[dir="ltr"] .header__logo-image--transparent {
  left: 0;
}

[dir="rtl"] .header__logo-image--transparent {
  right: 0;
}

.header__logo-image--transparent {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  -o-object-fit: contain;
     object-fit: contain;
  -o-object-position: center;
     object-position: center;
}

.js .header--transparent .header__logo-image:not(:last-child) {
  opacity: 0;
}

.js .header--transparent .header__logo-image--transparent {
  opacity: 1;
}

.header__wrapper {
  display: flex;
  align-items: center;
  padding: 20px 0;
}

.header__inline-navigation {
  display: flex;
  align-items: center;
}

.header__inline-navigation,
.header__secondary-links {
  flex: 1 1 0; /* Allows to give the same width to left and right part */
}

.header__icon-wrapper {
  display: block;
}

@media screen and (max-width: 740px) {
  .header__logo-text {
    text-align: center;
  }
}

@media screen and (min-width: 741px) {
  .header__wrapper {
    padding: calc(27px - var(--reduce-header-padding) * 6px) 0;
  }
}

@media screen and (min-width: 1000px) and (max-width: 1199px) {
  [dir="ltr"] .header__search-bar {
    margin-left: 24px;
  }
  [dir="rtl"] .header__search-bar {
    margin-right: 24px;
  }
}

/** SECONDARY LINKS **/

.header__secondary-links {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.header__secondary-links .header__linklist {
  flex-wrap: nowrap;
}

.header__icon-list {
  display: grid;
  grid-auto-flow: column;
  grid-gap: 20px;
  gap: 20px;
  justify-content: flex-start;
  align-items: center;
}

[dir="ltr"] .header__cart-count {
  margin-left: 8px;
}

[dir="rtl"] .header__cart-count {
  margin-right: 8px;
}

.header__cart-count {
  top: -1px; /* pixel perfect alignment */
}

[dir="ltr"] .header__cart-count--floating {
  margin-left: 0;
}

[dir="rtl"] .header__cart-count--floating {
  margin-right: 0;
}

[dir="ltr"] .header__cart-count--floating {
  right: -14px;
}

[dir="rtl"] .header__cart-count--floating {
  left: -14px;
}

.header__cart-count--floating {
  position: absolute;
  top: -8px;
}

.header__cart-count {
  background: rgb(var(--header-text-color));
  color: rgb(var(--header-background));
}

.js .header--transparent .header__cart-count {
  color: rgb(var(--header-transparent-bubble-text-color));
}

@media screen and (min-width: 741px) {
  .header__icon-list {
    gap: 24px;
  }
}

/** CROSS BORDER **/

.header__cross-border {
  display: grid;
  grid-auto-flow: column;
  grid-gap: 18px;
  gap: 18px;
}

[dir="ltr"] .header__secondary-links .header__cross-border {
  margin-right: 24px;
}

[dir="rtl"] .header__secondary-links .header__cross-border {
  margin-left: 24px;
}

@media screen and (min-width: 1200px) {
  [dir="ltr"] .header__secondary-links .header__cross-border {
    margin-right: 30px;
  }
  [dir="rtl"] .header__secondary-links .header__cross-border {
    margin-left: 30px;
  }
}

/** NAVIGATION **/

.header__bottom-navigation {
  padding-top: calc(17px - var(--reduce-header-padding) * 8px);
  padding-bottom: calc(19px - var(--reduce-header-padding) * 8px); /* Designer want slightly irregular padding */
  border-top: 1px solid rgb(var(--header-border-color));
  transition: border-top 0.2s ease-in-out;
}

.header__linklist {
  display: flex;
  flex-wrap: wrap;
  row-gap: 12px; /* This will add extra spacing on modern browsers */
}

.header__linklist-item {
  flex-shrink: 0;
}

[dir="ltr"] .header__linklist-item:not(:last-child) {
  margin-right: 32px;
}

[dir="rtl"] .header__linklist-item:not(:last-child) {
  margin-left: 32px;
}

/* This is a trick to increase the bounding size of the link, and therefore prevent to loose hover */
[dir="ltr"] .header__linklist-item.has-dropdown:hover::before {
  margin-left: -32px;
}
[dir="rtl"] .header__linklist-item.has-dropdown:hover::before {
  margin-right: -32px;
}
.header__linklist-item.has-dropdown:hover::before {
  content: attr(data-item-title);
  position: absolute;
  height: 100%;
  top: 0;
  padding-left: 32px;
  padding-right: 32px;
  opacity: 0; /* Allow to visually hide it */
}

.header__bottom-navigation .header__linklist-item:hover::before {
  height: calc(100% - var(--header-height-without-bottom-nav));
  top: auto;
  bottom: 0;
}

.header__linklist-link {
  display: block;
}

.header__bottom-navigation .header__linklist {
  justify-content: center;
}

/** DROPDOWN MENU **/

[dir="ltr"] .nav-dropdown {
  margin-left: -32px;
}

[dir="rtl"] .nav-dropdown {
  margin-right: -32px;
}

.nav-dropdown {
  display: block;
  position: absolute;
  top: 100%;
  padding-top: 20px;
  padding-bottom: 20px;
  background: rgb(var(--background));
  color: rgb(var(--text-color));
  border: 1px solid rgba(var(--text-color), 0.15);
  white-space: nowrap;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.25s ease-in-out, visibility 0.25s ease-in-out;
}

.nav-dropdown:not([hidden]),
.focus-outline [focus-within] > .nav-dropdown,
.no-js [focus-within] > .nav-dropdown,
.no-js :hover > .nav-dropdown {
  visibility: visible;
  opacity: 1;
}

.nav-dropdown:not([hidden]),
.focus-outline :focus-within > .nav-dropdown,
.no-js :focus-within > .nav-dropdown,
.no-js :hover > .nav-dropdown {
  visibility: visible;
  opacity: 1;
}

.nav-dropdown--restrict {
  max-height: calc(100vh - var(--header-height) - 20px);
  overflow: auto;
}

[dir="ltr"] .nav-dropdown .nav-dropdown {
  margin-left: 0;
}

[dir="rtl"] .nav-dropdown .nav-dropdown {
  margin-right: 0;
}

[dir="ltr"] .nav-dropdown .nav-dropdown {
  left: 100%;
}

[dir="rtl"] .nav-dropdown .nav-dropdown {
  right: 100%;
}

.nav-dropdown .nav-dropdown {
  top: -20px;
}

.nav-dropdown__item {
  position: relative;
}

.nav-dropdown__link {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 3px 30px;
}

[dir="ltr"] .nav-dropdown__link > svg {
  margin-left: 16px;
}

[dir="rtl"] .nav-dropdown__link > svg {
  margin-right: 16px;
}

.nav-dropdown__link > svg {
  position: relative;
  top: 2px;
  transition: transform 0.25s ease-in-out;
}

.nav-dropdown__link[aria-expanded="true"] > svg {
  transform: translateX(calc(var(--transform-logical-flip) * 8px));
}

/** MEGA MENU **/

.mega-menu {
  --mega-menu-column-gap: 48px;
  --mega-menu-image-gap: 24px;

  position: absolute;
  display: block;
  top: 100%;
  left: 0;
  width: 100%;
  visibility: hidden;
  opacity: 0;
  background: rgb(var(--background));
  color: rgb(var(--text-color));
  transition: opacity 0.25s ease-in-out, visibility 0.25s ease-in-out;
}

.mega-menu.is-closing,
.mega-menu[hidden] {
  z-index: -1;
}

.mega-menu::after {
  content: '';
  position: absolute;
  height: calc(100vh - 100% - var(--header-height, 0px));
  width: 100%;
  left: 0;
  top: 100%;
  background: #000000;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.25s ease-in-out;
}

.mega-menu:not([hidden]),
.focus-outline [focus-within] > .mega-menu,
.no-js [focus-within] > .mega-menu,
.no-js :hover > .mega-menu {
  visibility: visible;
  opacity: 1;
}

.mega-menu:not([hidden]),
.focus-outline :focus-within > .mega-menu,
.no-js :focus-within > .mega-menu,
.no-js :hover > .mega-menu {
  visibility: visible;
  opacity: 1;
}

.mega-menu:not([hidden])::after,
.focus-outline [focus-within] > .mega-menu::after,
.no-js [focus-within] > .mega-menu::after,
.no-js :hover > .mega-menu::after {
  opacity: 0.3;
}

.mega-menu:not([hidden])::after,
.focus-outline :focus-within > .mega-menu::after,
.no-js :focus-within > .mega-menu::after,
.no-js :hover > .mega-menu::after {
  opacity: 0.3;
}

.mega-menu.is-closing::after {
  opacity: 0;
  transition-delay: 0.15s;
}

.header--bordered .mega-menu {
  margin-top: 1px; /* Prevent overlapping the border */
}

.mega-menu__inner {
  position: relative;
  display: grid;
  grid-auto-flow: column;
  justify-content: center;
  grid-column-gap: var(--mega-menu-column-gap);
  column-gap: var(--mega-menu-column-gap);
  padding-top: 48px;
  padding-bottom: 48px;
  max-height: calc(100vh - var(--header-height, 0px) - var(--announcement-bar-height, 0px) - 50px);
  overflow: auto;
  z-index: 1;
}

.mega-menu__columns-wrapper {
  display: flex;
  flex-wrap: wrap;
  margin: calc(-1 * var(--mega-menu-column-gap) / 2);
  /* gap: var(--mega-menu-column-gap); NOT YET FULLY SUPPORTED */
}

.mega-menu__column {
  margin: calc(var(--mega-menu-column-gap) / 2);
}

.mega-menu__images-wrapper {
  display: grid;
  grid-auto-flow: column;
  align-items: flex-start;
  grid-gap: var(--mega-menu-image-gap);
  gap: var(--mega-menu-image-gap);
}

.mega-menu__images-wrapper--tight {
  gap: 20px;
}

.mega-menu__image-push {
  width: 180px;
  text-align: center;
}

.mega-menu__image-push:only-child {
  width: 200px;
}

.mega-menu__image-wrapper {
  margin-bottom: 18px;
  overflow: hidden;
  border-radius: var(--block-border-radius-reduced);
  z-index: 0; /* Creating new stacking context is needed on Safari to apply the border radius */
}

.mega-menu__heading {
  margin-bottom: 7px;
}

.mega-menu__title {
  margin-bottom: 16px;
}

@media screen and (min-width: 1200px) {
  .mega-menu {
    --mega-menu-column-gap: 64px;
  }
}

@media screen and (min-width: 1400px) {
  .mega-menu {
    --mega-menu-column-gap: 80px;
    --mega-menu-image-gap: 40px;
  }

  .mega-menu__image-push {
    width: 240px;
  }

  .mega-menu__image-push:only-child {
    width: 280px;
  }
}

/** MOBILE MENU **/

.mobile-nav__item {
  display: flow-root;
}

.mobile-nav__item:not(:last-child) {
  border-bottom: 1px solid rgba(var(--text-color), 0.15);
}

.mobile-nav__link {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin: 0;
  padding: 21px 0;
}

.mobile-nav__image-heading {
  margin-bottom: 7px;
}

.mobile-nav__image-text {
  display: block;
}

.mobile-nav .collapsible {
  margin-left: calc(-1 * var(--container-gutter));
  margin-right: calc(-1 * var(--container-gutter));
}

/* Level 2 */

[dir="ltr"] .mobile-nav .mobile-nav {
  padding-left: 8px;
}

[dir="rtl"] .mobile-nav .mobile-nav {
  padding-right: 8px;
}

.mobile-nav .mobile-nav {
  margin-left: var(--container-gutter);
  margin-right: var(--container-gutter);
  margin-bottom: 24px !important; /* Sorry about the important, may me soul be in peace */
}

.mobile-nav .mobile-nav .mobile-nav__item {
  margin-bottom: 15px;
  border: none;
}

.mobile-nav .mobile-nav .mobile-nav__link {
  padding: 0;
}

/* Level 3 */

[dir="ltr"] .mobile-nav .mobile-nav .mobile-nav {
  border-left: 1px solid rgba(var(--text-color), 0.15);
}

[dir="rtl"] .mobile-nav .mobile-nav .mobile-nav {
  border-right: 1px solid rgba(var(--text-color), 0.15);
}

[dir="ltr"] .mobile-nav .mobile-nav .mobile-nav {
  padding-left: 20px;
}

[dir="rtl"] .mobile-nav .mobile-nav .mobile-nav {
  padding-right: 20px;
}

.mobile-nav .mobile-nav .mobile-nav {
  margin-top: 16px !important;
  margin-bottom: 10px !important;
}

.mobile-nav .mobile-nav .mobile-nav .mobile-nav__item {
  margin-bottom: 14px;
}

.mobile-nav .mobile-nav .mobile-nav .mobile-nav__item:last-child {
  margin-bottom: 0;
}

/* Mobile images */

[dir="ltr"] .mobile-nav +.mobile-nav__images-wrapper {
  padding-left: 8px;
}

[dir="rtl"] .mobile-nav +.mobile-nav__images-wrapper {
  padding-right: 8px;
}

.mobile-nav__images-scroller {
  display: grid;
  grid-gap: 12px;
  gap: 12px;
  grid-auto-flow: column;
  margin-bottom: 32px;
  padding-left: var(--container-gutter);
  padding-right: var(--container-gutter);
  width: -moz-fit-content;
  width: fit-content;
}

.mobile-nav__image-push {
  min-width: 120px;
  max-width: 134px;
  text-align: center;
}

.mobile-nav__image {
  display: block;
  margin-bottom: 14px;
  border-radius: min(var(--block-border-radius), 4px);
}

/* Footer */

.mobile-nav__footer {
  display: flex;
  justify-content: space-between;
}
.image-with-text {
  /* Thanks to the ratio of the image and the height of the div we can calculate the height taken by the image */
  --image-height: calc((100vw - var(--container-gutter) * 4) * (1.0 / var(--image-aspect-ratio)));

  position: relative;
  display: block;
  text-align: center;
}

[dir="ltr"] .image-with-text::before {
  left: 0;
}

[dir="rtl"] .image-with-text::before {
  right: 0;
}

.image-with-text::before {
  position: absolute;
  content: '';
  top: 0;
  width: 100%;
  height: 100%;
  background: rgb(var(--section-accent-background));
  z-index: -1;
}

.image-with-text--overlap-image::before {
  height: var(--image-height);
}

.image-with-text--overlap-text::before {
  top: auto;
  bottom: 0;
  height: calc(100% - var(--image-height)); /* When we overlap the text we actually take the full height minus image height */
}

.image-with-text__image-wrapper {
  position: relative;
  margin-bottom: 32px;
  overflow: hidden;
  border-radius: var(--block-border-radius-reduced);
  z-index: 0; /* Creating new stacking context is needed on Safari to apply the border radius */
}

.image-with-text__image {
  position: relative;
  display: block;
}

.image-with-text__image:not([hidden]) {
  z-index: 1;
}

/* Next images are resized to keep the format of the first image */
[dir="ltr"] .image-with-text__image:not(:first-child) {
  left: 0;
}
[dir="rtl"] .image-with-text__image:not(:first-child) {
  right: 0;
}
.image-with-text__image:not(:first-child) {
  position: absolute;
  top: 0;
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
}

.image-with-text__image[hidden] {
  visibility: hidden;
  z-index: -1;
  transition: visibility 0.6s linear;
}

.image-with-text__wrapper {
  overflow: hidden;
}

/* We want to have the div to have the height of the tallest element, so we align them horizontally */
.image-with-text__content-list {
  display: flex;
  flex-wrap: nowrap;
}

.image-with-text__content {
  display: block;
  flex-shrink: 0;
  width: 100%;
  order: 0; /* Force the active to be the visible one */
}

.image-with-text__content[hidden] {
  visibility: hidden;
  order: 1;
}

.image-with-text__navigation {
  display: inline-grid;
  grid-template-columns: 1fr 1fr;
  column-gap: 24px;
  align-items: flex-start;
  margin-top: 40px;
}

@keyframes navigationItemAnimation {
  from {
    transform: scaleX(0);
  }

  to {
    transform: scaleX(1);
  }
}

.image-with-text__navigation-item {
  position: relative;
}

.image-with-text__navigation-item::before,
.image-with-text__navigation-item::after {
  content: '';
  display: block;
  width: 100%;
  height: 2px;
  margin-bottom: 16px;
  background: rgba(var(--text-color), 0.15);
}

[dir="ltr"] .image-with-text__navigation-item::after {
  left: 0;
}

[dir="rtl"] .image-with-text__navigation-item::after {
  right: 0;
}

.image-with-text__navigation-item::after {
  position: absolute;
  top: 0;
  background: rgb(var(--text-color));
  transform-origin: var(--transform-origin-start);
  transform: scaleX(0);
}

.image-with-text__navigation-item[aria-current="true"]::after {
  animation: navigationItemAnimation var(--section-autoplay-duration) linear;
  animation-play-state: var(--section-animation-play-state, running);
}

@media screen and (max-width: 999px) {
  /* Extra spacing just for the pocket devices */
  .image-with-text {
    padding: var(--vertical-breather) var(--container-gutter);
  }

  .image-with-text--boxed {
    padding: var(--container-gutter);
  }
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .image-with-text {
    --image-height: calc((100vw - var(--container-gutter) * 2 - (var(--grid-column-width) + var(--grid-gap)) * 4) * (1.0 / var(--image-aspect-ratio)));

    padding: var(--vertical-breather) calc((var(--grid-column-width) + var(--grid-gap)) * 2);
  }

  .image-with-text--boxed {
    padding: var(--vertical-breather) calc((var(--grid-column-width) + var(--grid-gap)) * 2 + var(--container-gutter));
  }

  .image-with-text__image-wrapper {
    margin-bottom: 48px;
  }
}

@media screen and (min-width: 1000px) {
  [dir="ltr"] .image-with-text::before {
    left: auto;
    right: 0;
  }
  [dir="rtl"] .image-with-text::before {
    right: auto;
    left: 0;
  }
  .image-with-text::before {
    height: 100%;
    top: 0;
    width: calc(var(--grid-column-width) * 13 + (var(--grid-gap) * 12) + var(--container-outer-margin));
  }

  [dir="ltr"] .image-with-text--reverse:not(.image-with-text--overlap-image)::before,[dir="ltr"]
  .image-with-text--overlap-image:not(.image-with-text--reverse)::before {
    left: 0;
    right: auto;
  }

  [dir="rtl"] .image-with-text--reverse:not(.image-with-text--overlap-image)::before,[dir="rtl"]
  .image-with-text--overlap-image:not(.image-with-text--reverse)::before {
    right: 0;
    left: auto;
  }

  .image-with-text--overlap-image::before {
    width: calc(var(--grid-column-width) * 7 + (var(--grid-gap) * 6) + var(--container-outer-margin));
  }

  .image-with-text--overlap-both::before {
    width: 100% !important; /* When it overlap both we force it to be 100% */
  }

  .image-with-text__wrapper {
    display: flex;
    padding: var(--vertical-breather) 0;
    align-items: center;
  }

  .image-with-text--reverse .image-with-text__wrapper {
    flex-direction: row-reverse;
  }

  [dir="ltr"] .image-with-text__image-wrapper {
    margin-left: calc(var(--grid-column-width) + var(--grid-gap));
  }

  [dir="rtl"] .image-with-text__image-wrapper {
    margin-right: calc(var(--grid-column-width) + var(--grid-gap));
  }

  .image-with-text__image-wrapper {
    width: calc(var(--grid-column-width) * 8 + (var(--grid-gap) * 7));
    margin-bottom: 0;
  }

  [dir="ltr"] .image-with-text--reverse .image-with-text__image-wrapper {
    margin-left: 0;
    margin-right: calc(var(--grid-column-width) + var(--grid-gap));
  }

  [dir="rtl"] .image-with-text--reverse .image-with-text__image-wrapper {
    margin-right: 0;
    margin-left: calc(var(--grid-column-width) + var(--grid-gap));
  }

  .image-with-text__content-wrapper {
    width: calc(var(--grid-column-width) * 9 + (var(--grid-gap) * 8));
    margin-left: auto;
    margin-right: auto;
  }
}

@media screen and (min-width: 1200px) {
  .image-with-text__navigation {
    margin-top: 48px;
    column-gap: 40px;
  }
}

@media screen and (min-width: 1400px) {
  [dir="ltr"] .image-with-text__content-wrapper {
    margin-right: calc((var(--grid-column-width) + var(--grid-gap)) * 2);
  }
  [dir="rtl"] .image-with-text__content-wrapper {
    margin-left: calc((var(--grid-column-width) + var(--grid-gap)) * 2);
  }
  .image-with-text__content-wrapper {
    width: calc(var(--grid-column-width) * 7 + (var(--grid-gap) * 6));
  }
}
.image-with-text-block {
  display: block;
}

.image-with-text-block__image-wrapper {
  overflow: hidden;
  background: rgb(var(--secondary-background)); /* Act as a filler */
}

.image-with-text-block__content {
  padding: 48px 24px;
  background-color: rgb(var(--section-block-background));
  border-radius: var(--block-border-radius);
}

.image-with-text-block__text-container {
  margin-top: 24px;
}

@media screen and (max-width: 999px) {
  .image-with-text-block__content {
    width: auto;
  }

  .image-with-text-block--overlap-left .image-with-text-block__content,
  .image-with-text-block--overlap-right .image-with-text-block__content {
    margin: calc(-1 * var(--container-gutter)) var(--container-gutter) 0;
    padding: 40px;
  }

  .image-with-text-block:not(.image-with-text-block--overlap-left):not(.image-with-text-block--overlap-right) .image-with-text-block__content {
    border-radius: 0;
  }

  /* Cover variation: on this mode, the image will still cover the image. There is a bit of duplication of
     code but I could not find cleaner way */
  .image-with-text-block--cover {
    position: relative;
    display: flex;
    align-items: center;
  }

  [dir="ltr"] .image-with-text-block--cover .image-with-text-block__image-wrapper {
    left: 0;
  }

  [dir="rtl"] .image-with-text-block--cover .image-with-text-block__image-wrapper {
    right: 0;
  }

  .image-with-text-block--cover .image-with-text-block__image-wrapper {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
  }

  .image-with-text-block--cover .image-with-text-block__image {
    height: 100%;
    -o-object-fit: cover;
       object-fit: cover;
    -o-object-position: center;
       object-position: center;
  }

  .image-with-text-block--cover .image-with-text-block__content {
    padding: var(--container-gutter);
    margin: var(--vertical-breather) var(--container-gutter);
  }
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .image-with-text-block--cover .image-with-text-block__content {
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
  }
}

@media screen and (min-width: 741px) {
  .image-with-text-block__content {
    padding-left: 48px;
    padding-right: 48px;
  }
}

@media screen and (min-width: 1000px) {
  .image-with-text-block {
    position: relative;
    display: flex;
    align-items: center;
    min-height: 500px;
  }

  [dir="ltr"] .image-with-text-block__image-wrapper {
    left: 0;
  }

  [dir="rtl"] .image-with-text-block__image-wrapper {
    right: 0;
  }

  .image-with-text-block__image-wrapper {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
  }

  [dir="ltr"] .image-with-text-block__image {
    left: 0;
  }

  [dir="rtl"] .image-with-text-block__image {
    right: 0;
  }

  .image-with-text-block__image {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
       object-fit: cover;
    -o-object-position: center;
       object-position: center;
  }

  .image-with-text-block__content {
    margin-top: var(--vertical-breather);
    margin-bottom: var(--vertical-breather);
  }

  /* Overlap variation */

  .image-with-text-block--overlap-right .image-with-text-block__image-wrapper,
  .image-with-text-block--overlap-left .image-with-text-block__image-wrapper {
    width: calc(var(--grid-column-width) * 14 + (var(--grid-gap) * 13) + var(--container-outer-margin));
  }

  [dir="ltr"] .image-with-text-block--overlap-left .image-with-text-block__image-wrapper {
    right: 0;
  }

  [dir="rtl"] .image-with-text-block--overlap-left .image-with-text-block__image-wrapper {
    left: 0;
  }

  [dir="ltr"] .image-with-text-block--overlap-left .image-with-text-block__image-wrapper {
    left: auto;
  }

  [dir="rtl"] .image-with-text-block--overlap-left .image-with-text-block__image-wrapper {
    right: auto;
  }
}

@media screen and (min-width: 1200px) {
  .image-with-text-block {
    min-height: 700px;
  }

  .image-with-text-block--small {
    min-height: 400px;
  }

  .image-with-text-block__content:not(.image-with-text-block__content--tight) {
    padding: 64px;
  }
}
.image-overlay {
  --image-height: auto;

  position: relative;
  display: flex;
  min-height: var(--image-height);
  color: rgb(var(--text-color));
  background: rgb(var(--section-overlay-color));
}

.image-overlay--small {
  --image-height: 375px;
}

.image-overlay--medium {
  --image-height: 500px;
}

.image-overlay--large {
  --image-height: 600px;
}

.image-overlay::before {
  content: '';
  display: block;
  padding-bottom: calc(100% / var(--image-aspect-ratio));
  width: 0;
}

[dir="ltr"] .image-overlay__image-wrapper::after {
  left: 0;
}

[dir="rtl"] .image-overlay__image-wrapper::after {
  right: 0;
}

.image-overlay__image-wrapper::after {
  content: '';
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(var(--section-overlay-color), var(--section-overlay-opacity));
  pointer-events: none;
}

[dir="ltr"] .image-overlay__image-wrapper,[dir="ltr"]
.image-overlay__image {
  left: 0;
}

[dir="rtl"] .image-overlay__image-wrapper,[dir="rtl"]
.image-overlay__image {
  right: 0;
}

.image-overlay__image-wrapper,
.image-overlay__image {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.image-overlay__image {
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
}

.image-overlay__image--placeholder {
  background: rgb(var(--background));
}

.image-overlay__content-wrapper {
  position: relative;
  display: flex;
  height: 100%;
  width: 100%;
  align-items: var(--section-items-alignment);
  justify-content: center;
  padding: var(--vertical-breather) 0;
}

.image-overlay__text-container {
  margin-top: 24px;
}

.image-overlay__text-container .button-wrapper:only-child {
  margin-top: 32px; /* We add an exception here to compensate the margin */
}

@media screen and (min-width: 741px) {
  .image-overlay--small {
    --image-height: 400px;
  }

  .image-overlay--medium {
    --image-height: 550px;
  }

  .image-overlay--large {
    --image-height: 700px;
  }
}
/* This section describe the main list of collections used on list of collections */

.list-collections {
  display: block;
  position: relative;
}

.list-collections__item-list {
  display: grid;
  align-items: center;
  grid-gap: 24px;
  gap: 24px;
}

.list-collections__item {
  --heading-color: 255, 255, 255;
  --text-color: 255, 255, 255;

  position: relative;
  display: block;
  text-align: center;
  background: rgb(var(--secondary-background)); /* We fill with the secondary background */
  border-radius: var(--block-border-radius-reduced);
  overflow: hidden;
  z-index: 0; /* Creating new stacking context is needed on Safari to apply the border radius */
}

.list-collections__item:hover .link {
  -webkit-text-decoration-color: rgb(var(--text-color));
          text-decoration-color: rgb(var(--text-color)); /* Make sure that the underlined link is in active state on hover of the whole block */
}

.list-collections__item-image-wrapper {
  position: relative;
  height: 100%;
}

.list-collections__item.has-overlay .list-collections__item-image-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(var(--section-block-overlay, 0, 0, 0), var(--section-block-overlay-opacity, 0.2));
  z-index: 1;
}

.list-collections__item-image {
  min-height: 120px; /* Ensure enough space for text */
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
}

.list-collections__item-info {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  transform: translateY(-50%);
  z-index: 1;
  padding-left: 24px;
  padding-right: 24px;
}

@media screen and (min-width: 741px) {
  .list-collections__item-list {
    grid-template-columns: repeat(auto-fit, calc(100% / 3 - var(--container-gutter) * 2 / 3));
    grid-gap: var(--container-gutter);
    justify-content: safe center;
  }
}

@media screen and (min-width: 1000px) {
  .list-collections__item-info {
    padding-left: var(--container-gutter);
    padding-right: var(--container-gutter);
  }
}

/* Collage variation */

@media screen and (min-width: 741px) {
  .list-collections--collage {
    --list-collections-collage-rows-height: 220px;
  }

  .list-collections--collage .list-collections__item-list {
    grid-template-columns: repeat(var(--section-collage-column), 1fr);
    grid-auto-rows: var(--list-collections-collage-rows-height);
    grid-auto-flow: dense;
  }

  .list-collections--collage .list-collections__item:only-child {
    width: 590px;
    margin-left: auto;
    margin-right: auto;
  }

  .list-collections--collage .list-collections__item {
    height: 100%;
  }

  .list-collections--collage .list-collections__item--highlight {
    grid-row: auto / span 2;
  }

  .list-collections--collage .list-collections__item--shift {
    grid-column: 2;
  }
}

@media screen and (min-width: 1000px) {
  .list-collections--collage {
    --list-collections-collage-rows-height: 250px;
  }

  .list-collections--collage .list-collections__item-list {
    margin-left: calc(var(--grid-column-width) + var(--grid-gap));
    margin-right: calc(var(--grid-column-width) + var(--grid-gap));
  }
}

@media screen and (min-width: 1200px) {
  .list-collections--collage {
    --list-collections-collage-rows-height: 300px;
  }
}

@media screen and (min-width: 1400px) {
  .list-collections--collage {
    --list-collections-collage-rows-height: 350px;
  }
}

/* Carousel (scroller) variation */

.list-collections__scroller {
  display: block;
  overflow: auto;
  scroll-snap-type: x mandatory;
}

.list-collections--carousel .list-collections__item-list {
  grid-auto-flow: column;
  grid-auto-columns: 80vw;
  grid-template-columns: none;
  width: min-content;
  min-width: 100%;
  padding: 0 var(--container-gutter);
}

.list-collections--carousel .list-collections__item {
  scroll-snap-align: center;
  scroll-snap-stop: always;
}

@media screen and (min-width: 741px) {
  .list-collections--carousel .list-collections__item-list {
    grid-auto-columns: 60vw;
  }
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  /* Just on tablet, the designer wants to have the collections bigger on grid mode and use a carousel mode, so we have
   to do a specific exception just for tablet size. This unfortunately cause code duplication :( */

  .list-collections--grid .container {
    display: block;
    overflow: auto;
    padding-left: 0;
    padding-right: 0;
    scroll-snap-type: x mandatory;
    scrollbar-width: none; /* For Firefox */
  }

  .list-collections--grid .container::-webkit-scrollbar {
    display: none;
  }

  .list-collections--grid .list-collections__item-list {
    grid-auto-columns: 60vw;
    grid-template-columns: none;
    grid-auto-flow: column;
    width: min-content;
    min-width: 100%;
    padding: 0 var(--container-gutter);
  }

  .list-collections--grid .list-collections__item {
    scroll-snap-align: center;
    scroll-snap-stop: always;
  }
}

@media screen and (min-width: 1000px) {
  .list-collections__scroller {
    /* overflow: hidden; Uncomment this line if you just want scroll to be done in JS with arrows */
    scroll-snap-type: none;
  }

  .list-collections--carousel .list-collections__item-list {
    grid-auto-columns: 23vw;
    padding-left: var(--container-outer-margin);
    padding-right: var(--container-outer-margin);
  }

  [dir="ltr"] .list-collections__scroller.is-scrollable .list-collections__item-list {
    padding-right: calc(var(--container-outer-margin) + 28px);
  }

  [dir="rtl"] .list-collections__scroller.is-scrollable .list-collections__item-list {
    padding-left: calc(var(--container-outer-margin) + 28px);
  }

  .list-collections__scroller.is-scrollable .list-collections__item-list { /* 28px is half the width of arrows */
  }

  [dir="ltr"] .list-collections__prev-next {
    right: var(--container-outer-width);
  }

  [dir="rtl"] .list-collections__prev-next {
    left: var(--container-outer-width);
  }

  .list-collections__prev-next {
    display: none;
    position: absolute;
    top: calc(50% - 56px); /* 56px is the height of a single button */
    z-index: 1;
  }

  .list-collections__scroller.is-scrollable + .list-collections__prev-next {
    display: block;
  }

  .list-collections__arrow:last-child {
    border-top: none;
  }
}
.logo-list {
  display: block;
  position: relative;
}

.logo-list__list {
  --logos-per-row: 2;
  --logos-gap: 8px;

  display: grid;
  grid-template-columns: repeat(min(var(--logos-per-row), var(--section-logo-count)), minmax(140px, 200px));
  grid-gap: var(--logos-gap);
  gap: var(--logos-gap);
  justify-content: center;
}

.logo-list__item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  background: rgb(var(--section-logo-background));
  border-radius: var(--block-border-radius-reduced);
}

.logo-list__image--placeholder {
  height: 100px;
}

@media screen and (max-width: 999px) {
  /* Carousel variation */
  .logo-list--carousel .logo-list__list {
    grid-template-columns: none;
    grid-auto-flow: column;
    grid-auto-columns: 140px;
  }

  .logo-list--grid {
    padding-left: var(--container-gutter);
    padding-right: var(--container-gutter); /* We add extra internal spacing on pocket */
  }
}

@media screen and (min-width: 741px) {
  .logo-list__list {
    --logos-per-row: 3;
    --logos-gap: 16px;
  }

  /* Scroller variation */
  .logo-list--carousel .logo-list__list {
    grid-auto-columns: 185px;
  }
}

@media screen and (min-width: 1000px) {
  .logo-list__list {
    --logos-per-row: 6;
    --logos-gap: 24px;
  }

  /* On desktop, the scroller mode is only visible if we have the arrows, independently of the selected settings */

  .logo-list__prev-next {
    position: absolute;
    display: flex;
    justify-content: space-between;
    width: 100%;
    pointer-events: none;
    top: calc(50% - 28px); /* 28px is half the height of the button */
  }

  .logo-list--carousel .logo-list__prev-next + .logo-list__list {
    grid-template-columns: none;
    grid-auto-flow: column;
    grid-auto-columns: calc(100% / var(--logos-per-row) - (var(--logos-gap) / var(--logos-per-row) * (var(--logos-per-row) - 1)));
    justify-content: flex-start;
    overflow: hidden; /* We will scroll in JS */
    margin-left: calc(56px + 32px);
    margin-right: calc(56px + 32px); /* 56px is the width of arrows and 32px the extra space we add between arrows and logos */
  }

  .logo-list__arrow {
    opacity: 0;
    visibility: hidden;
    transform: scale(0.5);
    transition: opacity 0.15s ease-in-out, visibility 0.15s ease-in-out, transform 0.15s ease-in-out;
  }

  .logo-list__arrow:not([disabled]) {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
  }
}

@media screen and (min-width: 1200px) {
  .logo-list--grid {
    margin-left: calc(var(--grid-column-width) + var(--grid-gap));
    margin-right: calc(var(--grid-column-width) + var(--grid-gap));
  }
}
.multi-column {
  --multi-column-row-gap: 32px;
  --multi-column-column-gap: 24px;
  --section-items-per-row: 2;
  --item-width: calc(var(--container-inner-width) / var(--section-items-per-row) - (var(--multi-column-column-gap) / var(--section-items-per-row) * (var(--section-items-per-row) - 1)));

  position: relative;
  display: block;
}

.multi-column--spacing-tight {
  --multi-column-column-gap: 12px;
}

.multi-column--spacing-loose {
  --multi-column-column-gap: 32px;
}

.multi-column--pocket-medium,
.multi-column--pocket-large {
  --section-items-per-row: 1;
}

.multi-column__inner {
  display: grid;
  grid-template-columns: repeat(auto-fit, var(--item-width));
  grid-gap: var(--multi-column-row-gap) var(--multi-column-column-gap);
  gap: var(--multi-column-row-gap) var(--multi-column-column-gap);
  justify-content: safe center;
  padding-bottom: 2px; /* This allows to allocate extra space and make sure that link border are not hidden */
}

.multi-column__inner--left {
  justify-content: safe start;
}

.multi-column__inner--right {
  justify-content: safe end;
}

.multi-column__inner--scroller {
  grid-auto-flow: column;
  grid-template-columns: none !important;
}

.multi-column__image-wrapper {
  display: block;
  margin-bottom: 20px;
  margin-left: auto;
  margin-right: auto;
  border-radius: min(8px, var(--block-border-radius));
  overflow: hidden;
  z-index: 0; /* Creating new stacking context is needed on Safari to apply the border radius */
}

.multi-column__image-wrapper:only-child {
  margin-bottom: 0;
}

.multi-column__image {
  width: 100%;
}

@media screen and (max-width: 999px) {
  .multi-column__inner--scroller {
    padding-left: var(--container-gutter);
    padding-right: var(--container-gutter);
    min-width: min-content;
  }
}

@media screen and (max-width: 740px) {
  /* On mobile, for the scroller we use fixed width based on viewport size */
  .multi-column__inner--scroller {
    grid-auto-columns: 25vw;
  }

  .multi-column--pocket-medium .multi-column__inner--scroller {
    grid-auto-columns: 35vw;
  }

  .multi-column--pocket-large .multi-column__inner--scroller {
    grid-auto-columns: 56vw;
  }
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .multi-column--pocket-small {
    --section-items-per-row: 5;
  }

  .multi-column--pocket-medium {
    --section-items-per-row: 4;
  }

  .multi-column--pocket-large {
    --section-items-per-row: 3;
  }

  /* On mobile, for the scroller we use fixed width based on viewport size */
  .multi-column__inner--scroller {
    grid-auto-columns: 20vw;
  }

  .multi-column--pocket-medium .multi-column__inner--scroller {
    grid-auto-columns: 26vw;
  }

  .multi-column--pocket-large .multi-column__inner--scroller {
    grid-auto-columns: 36vw;
  }
}

@media screen and (min-width: 741px) {
  .multi-column--spacing-normal {
    --multi-column-row-gap: 40px;
  }

  .multi-column--spacing-loose {
    --multi-column-row-gap: 48px;
    --multi-column-column-gap: 32px;
  }
}

@media screen and (min-width: 1000px) {
  .multi-column--spacing-normal {
    --multi-column-column-gap: 40px;
  }

  .multi-column--spacing-tight {
    --multi-column-column-gap: 24px;
  }

  .multi-column--spacing-loose {
    --multi-column-column-gap: 60px;
  }

  .multi-column--pico {
    --section-items-per-row: 6;
  }

  .multi-column--small {
    --section-items-per-row: 5;
  }

  .multi-column--medium {
    --section-items-per-row: 4;
  }

  .multi-column--large {
    --section-items-per-row: 3;
  }

  .multi-column__inner--scroller {
    grid-auto-columns: var(--item-width);
    overflow: hidden;
  }

  .multi-column__inner:not(.is-scrollable) + .multi-column__prev-next {
    display: none;
  }

  .multi-column__image-wrapper {
    margin-bottom: 24px;
  }

  .multi-column__prev-next {
    position: absolute;
    pointer-events: none;
    display: flex;
    justify-content: space-between;
    width: calc(100% - 56px);
    top: calc(var(--item-width) / var(--smallest-image-aspect-ratio) / 2 - 28px); /* 28px is half the height of the button */
    left: 28px;
  }

  .multi-column__prev-next--no-image {
    top: calc(50% - 28px); /* 28px is half the height of the button */
  }

  .multi-column__arrow {
    z-index: 1;
    opacity: 0;
    visibility: hidden;
    transform: scale(0.5);
    transition: opacity 0.15s ease-in-out, visibility 0.15s ease-in-out, transform 0.15s ease-in-out;
  }

  .multi-column:hover .multi-column__arrow:not([disabled]) {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
  }
}

@media screen and (min-width: 1200px) {
  .multi-column--pico {
    --section-items-per-row: 8;
  }

  .multi-column--small {
    --section-items-per-row: 7;
  }

  .multi-column--medium {
    --section-items-per-row: 5;
  }
}

@media screen and (pointer: fine) {
  .multi-column__item:hover .multi-column__link {
    -webkit-text-decoration-color: rgb(var(--text-color));
            text-decoration-color: rgb(var(--text-color));
  }
}

@media not screen and (pointer: fine) {
  .multi-column__arrow:not([disabled]) {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
  }
}
.newsletter__form {
  margin-top: 32px;
}

.newsletter__form .input-row {
  grid-template-columns: none; /* As the newsletter box is small there is not enough space to fit on smaller screens */
}

@media screen and (min-width: 1200px) {
  .newsletter__form .input-row {
    grid-template-columns: 1fr auto; /* Small variation for this section */
  }
}
.newsletter-modal {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.newsletter-modal__image {
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
}

.newsletter-modal__content {
  padding: 32px 24px 40px;
  width: 100%;
}

.newsletter-modal__content--extra {
  padding-top: 40px; /* If there is an image the close button is within the text so we have to increase the padding */
}

@media screen and (max-width: 740px) {
  .newsletter-modal {
    max-height: inherit;
  }

  .newsletter-modal__image {
    max-height: 200px;
  }

  .newsletter-modal__content {
    overflow: auto;
  }
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .newsletter-modal__image {
    max-height: 350px;
  }
}

@media screen and (min-width: 741px) {
  .newsletter-modal__content {
    padding: 60px;
  }
}

@media screen and (min-width: 1000px) {
  .newsletter-modal {
    flex-direction: row;
  }

  .newsletter-modal--reverse {
    flex-direction: row-reverse;
  }

  .newsletter-modal__image,
  .newsletter-modal__content {
    flex: 1 0 0;
    max-width: 500px;
    min-width: 500px;
  }

  .newsletter-modal__content {
    padding: 80px;
  }
}
.password {
  background: rgb(var(--section-background));
  color: rgb(var(--text-color));
}

.password__logo {
  margin-bottom: 0;
}

.password__logo-image {
  display: block;
}

.password__main {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  min-height: 100vh;
  padding-left: 48px;
  padding-right: 48px;
  text-align: center;
  z-index: 1;
}

@supports (--css: variables) {
  .password__main {
    min-height: var(--window-height, 100vh);
  }
}

.password__content {
  max-width: 390px;
  width: 100%;
  padding-top: 24px;
  padding-bottom: 24px;
}

.password__storefront-login {
  display: block;
  margin-top: 20px;
}

[dir="ltr"] .password__storefront-login svg {
  margin-right: 12px;
}

[dir="rtl"] .password__storefront-login svg {
  margin-left: 12px;
}

.password__storefront-login svg {
  vertical-align: sub;
}

.password__storefront-form {
  max-width: 340px;
  margin-left: auto;
  margin-right: auto;
}

.password__newsletter {
  margin-top: 24px;
}

[dir="ltr"] .password__shopify-logo svg {
  margin-left: 12px;
}

[dir="rtl"] .password__shopify-logo svg {
  margin-right: 12px;
}

.password__copyright {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.password__powered-by {
  display: flex;
  align-items: center;
}

@media screen and (max-width: 999px) {
  .password__image {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    -o-object-fit: cover;
       object-fit: cover;
    -o-object-position: center;
       object-position: center;
    opacity: 0.1;
    filter: grayscale(1);
  }

  .password__newsletter-form .input__label {
    background: transparent;
  }

  .password__newsletter-form [focus-within] ~ .input__label,
  .password__newsletter-form .is-filled ~ .input__label {
    background: rgb(var(--section-background));
  }

  .password__newsletter-form :focus-within ~ .input__label,
  .password__newsletter-form .is-filled ~ .input__label {
    background: rgb(var(--section-background));
  }

  .password__admin-link {
    padding-top: 8px;
  }

  .password__storefront-form {
    position: relative;
    padding: 62px 48px 48px 48px;
    max-width: none;
    margin-left: 0;
    margin-right: 0;
    background: inherit;
    border-radius: 10px 10px 0 0;
  }
}

@media screen and (min-width: 741px) {
  .password__copyright {
    flex-direction: row;
  }

  .password__newsletter {
    margin-top: 32px;
  }

  .password__storefront-login {
    margin-top: 28px;
  }

  .password__shopify-logo svg {
    width: 98px;
    height: 28px;
  }

  [dir="ltr"] .password__admin-link {
    padding-left: 18px;
  }

  [dir="rtl"] .password__admin-link {
    padding-right: 18px;
  }
}

@media screen and (min-width: 1000px) {
  .password {
    display: grid;
    grid-auto-columns: 50%;
    grid-auto-flow: column;
    justify-content: center;
  }

  .password__image {
    -o-object-fit: cover;
       object-fit: cover;
    -o-object-position: center;
       object-position: center;
    max-height: 100vh;
    height: 100%;
  }
}
.predictive-search__form {
  display: flex;
  align-items: center;
}

[dir="ltr"] .predictive-search__input {
  margin-left: 8px;
}

[dir="rtl"] .predictive-search__input {
  margin-right: 8px;
}

.predictive-search__input {
  padding: 0;
  min-width: 300px;
  background: transparent;
  box-shadow: none;
  border: none;
  -webkit-appearance: none;
          appearance: none;
}

.predictive-search__input::placeholder {
  color: rgba(var(--text-color), 0.7);
  transition: color 0.2s ease-in-out;
}

.header__search-bar .predictive-search__input::placeholder {
  color: rgba(var(--header-text-color), 0.8);
}

.predictive-search .tabs-nav,
.predictive-search__menu-list {
  padding-top: 24px;
}

.predictive-search__menu + .predictive-search__menu {
  padding-top: 40px;
}

.predictive-search__menu-title {
  margin-bottom: 16px;
}

/* Product */

.predictive-search__product-item:first-child .line-item__content-wrapper {
  margin-top: 0;
}

.predictive-search__product-item {
  position: relative;
}

.predictive-search__product-item svg {
  position: absolute;
  top: calc(50% - 7px);
  right: 0;
  opacity: 0;
  transform: translateX(calc(var(--transform-logical-flip) * min(var(--container-gutter), 30px)));
  transition: opacity .2s ease-in-out, transform .2s ease-in-out;
}

@media screen and (pointer: fine) {
  .predictive-search__product-item:hover svg {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Linklist */

.predictive-search__linklist {
  max-width: 450px;
  margin-left: auto;
  margin-right: auto;
}

.predictive-search__linklist--narrow {
  max-width: 390px;
}

.predictive-search__linklist--bordered {
  border-top: 1px solid rgb(var(--border-color));
  border-bottom: 1px solid rgb(var(--border-color));
}

.predictive-search__linklist-item {
  border-top: 1px solid rgb(var(--border-color));
}

.predictive-search__linklist-item:first-child {
  border-top: none;
}

.predictive-search__linklist-link {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 15px;
  padding-bottom: 15px;
}

.predictive-search__linklist-link svg {
  opacity: 0;
  transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
  transform: translateX(calc(var(--transform-logical-flip) * min(var(--container-gutter), 30px)));
}

@media screen and (pointer: fine) {
  .predictive-search__linklist-link:hover svg {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Blog post */

.predictive-search__article-item + .predictive-search__article-item {
  margin-top: 24px;
}

[dir="ltr"] .predictive-search__article-image-wrapper {
  margin-right: 18px;
}

[dir="rtl"] .predictive-search__article-image-wrapper {
  margin-left: 18px;
}

.predictive-search__article-image-wrapper {
  position: relative;
  display: block;
  flex: none;
  width: 100px;
}

.predictive-search__article-category {
  margin-bottom: 8px;
}

@supports (-webkit-touch-callout: none) {
  @media screen and (max-width: 740px) {
    /* On iOS, when the keyboard is visible it does not contribute to the size of the viewport, so do not center on iOS */
    .predictive-search .drawer__content--center {
      margin-top: 150px;
    }
  }
}

@media screen and (min-width: 741px) {
  [dir="ltr"] .predictive-search__input {
    margin-left: 12px;
  }
  [dir="rtl"] .predictive-search__input {
    margin-right: 12px;
  }

  .predictive-search .tabs-nav,
  .predictive-search__menu-list {
    padding-top: 32px;
  }

  .predictive-search__article-image-wrapper {
    width: 140px;
  }
}
.press-list {
  display: block;
  text-align: center;
}

.press-list__wrapper {
  display: flex;
  flex-wrap: nowrap;
  max-width: 800px;
  padding-top: 10px;
  margin-left: auto;
  margin-right: auto;
  overflow: hidden;
}

.press-list__item {
  display: block;
  flex-shrink: 0;
  width: 100%;
  order: 0;
}

.press-list__item[hidden] {
  visibility: hidden;
  order: 1;
}

.press-list__logo-list {
  display: inline-grid;
  grid-auto-flow: column;
  align-items: center;
  gap: 48px;
  margin-top: 40px;
}

.press-list__logo-item {
  opacity: 0.3;
  transition: opacity 0.2s ease-in-out;
}

.press-list__logo-item[aria-current="true"] {
  opacity: 1;
}

.press-list__logo-image {
  vertical-align: middle;
}

@media screen and (max-width: 999px) {
  .press-list__logo-list-wrapper {
    margin-left: calc(-1 * var(--container-gutter));
    margin-right: calc(-1 * var(--container-gutter));
    overflow-x: auto;
    overflow-y: hidden;
  }

  .press-list__logo-list {
    margin-left: var(--container-gutter);
    margin-right: var(--container-gutter);
  }
}

@media screen and (min-width: 741px) {
  .press-list__logo-list {
    gap: 80px;
    margin-top: 48px;
  }
}
/*
 * MAIN LAYOUT
 */

@media screen and (max-width: 740px) {
  .product:not(.product--featured) {
    margin-top: var(--container-gutter);
    margin-bottom: 36px;
  }
}

@media screen and (min-width: 1000px) {
  .product {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .product:not(.product--featured) {
    margin-bottom: 80px;
  }

  .product__media {
    --product-media-width: calc(var(--grid-column-width) * 10 + var(--grid-gap) * 10);
    width: var(--product-media-width);
  }

  .product__info {
    --product-info-width: calc(var(--grid-column-width) * 9 + var(--grid-gap) * 8);
    width: var(--product-info-width);
    flex: none;
  }

  .product__info:only-child {
    margin-left: auto;
    margin-right: auto; /* Allows to center if this is the only child */
  }
}

@media screen and (min-width: 1200px) {
  .product {
    margin-left: calc(var(--grid-column-width) + var(--grid-gap));
    margin-right: calc(var(--grid-column-width) + var(--grid-gap));
  }

  .product__media {
    --product-media-width: calc(var(--grid-column-width) * 9 + var(--grid-gap) * 9);
  }

  .product__info {
    --product-info-width: calc(var(--grid-column-width) * 8 + var(--grid-gap) * 7);
  }
}

@media screen and (min-width: 1400px) {
  .product__media {
    --product-media-width: calc(var(--grid-column-width) * 10 + var(--grid-gap) * 10);
  }

  .product__info {
    --product-info-width: calc(var(--grid-column-width) * 7 + var(--grid-gap) * 6);
  }

  .product--thumbnails-bottom .product__media {
    --product-media-width: calc(var(--grid-column-width) * 9 + var(--grid-gap) * 8);
  }

  .product--thumbnails-bottom .product__info {
    --product-info-width: calc(var(--grid-column-width) * 8 + var(--grid-gap) * 7);
  }
}

/*
 * PRODUCT MEDIA PART
 */

.product__media {
  display: block;
}

.product__media-list-wrapper {
  position: relative;
  margin-left: auto;
  margin-right: auto;
}

.product__media-list,
.product__media-item {
  display: block;
  min-width: 100%;
  text-align: center;
}

.product__media-item {
  width: 100%;
}

.product__media-list:not(.flickity-enabled) .product__media-item:not(.is-selected),
.product__media .is-filtered {
  display: none;
}

.product__media-image-wrapper {
  overflow: hidden;
  background: rgb(var(--secondary-background)); /* Act as a placeholder until image is loaded */
  border-radius: var(--block-border-radius-reduced);
  z-index: 0; /* Creating new stacking context is needed on Safari to apply the border radius */
}

.product__media-nav {
  position: relative;
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;
  margin-top: 18px;
}

.product__thumbnail-scroll-shadow {
  max-width: 100%;
}

.product__thumbnail-list {
  position: relative;
}

.product__thumbnail-list-inner {
  display: grid;
  grid-auto-flow: column;
  align-items: start;
}

.product__thumbnail-item {
  position: relative;
  display: inline-block;
  padding: 2px;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
}

.product__thumbnail {
  position: relative;
  width: 76px;
  min-width: 76px;
  padding: 2px;
}

.product__thumbnail,
.product__thumbnail > img {
  border-radius: min(var(--block-border-radius), 4px);
}

.product__thumbnail .placeholder-background {
  display: block;
}

[dir="ltr"] .product__thumbnail::after {
  left: 0;
}

[dir="rtl"] .product__thumbnail::after {
  right: 0;
}

.product__thumbnail::after {
  content: '';
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  box-shadow: 0 0 0 2px rgb(var(--text-color));
  border-radius: inherit;
  opacity: 0;
  transform: scale(0.9);
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.product__thumbnail-item[aria-current] .product__thumbnail::after {
  opacity: 1;
  transform: scale(1);
}

[dir="ltr"] .product__thumbnail-badge {
  right: 4px;
}

[dir="rtl"] .product__thumbnail-badge {
  left: 4px;
}

.product__thumbnail-badge {
  position: absolute;
  top: 4px;
}

.product__view-in-space {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 8px;
  margin-bottom: 8px;
}

[dir="ltr"] .product__view-in-space svg {
  margin-right: 14px;
}

[dir="rtl"] .product__view-in-space svg {
  margin-left: 14px;
}

.product__view-in-space[data-shopify-xr-hidden] {
  visibility: hidden;
}

[dir="ltr"] .product__zoom-button {
  right: 16px;
}

[dir="rtl"] .product__zoom-button {
  left: 16px;
}

.product__zoom-button {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 36px;
  width: 36px;
  bottom: 16px;
  border: 1px solid rgb(var(--border-color));
  border-radius: var(--button-border-radius);
  background: rgb(var(--root-background));
  color: rgb(var(--root-text-color));
  transition: opacity 0.25s ease-in-out, visibility 0.25s ease-in-out, transform 0.25s ease-in-out, color 0.25s ease-in-out;
  z-index: 1;
}

.product__zoom-button[hidden] {
  opacity: 0;
  transform: scale(0.4);
  visibility: hidden;
}

@media screen and (max-width: 999px) {
  .product__media-list {
    margin-left: calc(-1 * var(--container-gutter));
    margin-right: calc(-1 * var(--container-gutter));
  }

  .product__media-item {
    padding-left: var(--container-gutter);
    padding-right: var(--container-gutter);
  }

  .product__media-nav .dots-nav {
    padding-left: 20px;
    padding-right: 20px;
  }

  .product__thumbnail-list {
    margin-left: calc(-1 * var(--container-gutter));
    margin-right: calc(-1 * var(--container-gutter));
  }

  .product__thumbnail-list-inner {
    padding-left: var(--container-gutter);
    padding-right: var(--container-gutter);
    width: max-content;
  }
}

@media screen and (min-width: 1000px) {
  .product__view-in-space {
    display: none; /* We do not show that button on desktop */
  }

  .product__media-nav {
    margin-top: 16px;
  }

  .product__thumbnail-scroll-shadow {
    --scroll-shadow-size: 65px;
    --scroll-shadow-right: linear-gradient(to left, rgb(var(--background)), rgba(var(--background), 0));
    --scroll-shadow-left: linear-gradient(to right, rgb(var(--background)), rgba(var(--background), 0));
    --scroll-shadow-bottom: linear-gradient(to top, rgb(var(--background)), rgba(var(--background), 0));
    --scroll-shadow-top: linear-gradient(to bottom, rgb(var(--background)), rgba(var(--background), 0));
  }

  .product__thumbnail-list {
    overflow: hidden;
  }

  .product__thumbnail-list-inner {
    gap: 8px;
  }

  .product__thumbnail {
    width: 64px;
    min-width: 64px;
  }

  /* Thumbnails left variation */
  .product--thumbnails-left .product__media {
    display: flex;
    flex-direction: row-reverse;
    align-items: flex-start;
  }

  .product--thumbnails-left .product__media-nav {
    margin-top: 0;
    align-items: flex-start;
  }

  .product--thumbnails-left .product__media-list-wrapper {
    flex-grow: 1;
  }

  .product--thumbnails-left .product__media-prev-next {
    transform: rotate(90deg) scale(var(--scale-factor));
  }

  [dir="ltr"] .product--thumbnails-left .product__thumbnail-list {
    margin-right: 36px;
  }

  [dir="rtl"] .product--thumbnails-left .product__thumbnail-list {
    margin-left: 36px;
  }

  .product--thumbnails-left .product__thumbnail-list {
    max-height: calc((var(--product-media-width) - 136px) / var(--largest-image-aspect-ratio));
  }

  .product--thumbnails-left .product__thumbnail-list-inner {
    grid-auto-flow: row;
  }

  .product--thumbnails-left .product__thumbnail {
    width: 60px;
    min-width: 60px;
  }
}

@media screen and (min-width: 1400px) {
  [dir="ltr"] .product__media {
    padding-left: 36px;
  }
  [dir="rtl"] .product__media {
    padding-right: 36px;
  }
}

@media screen and (pointer: fine) {
  .product__zoom-button:hover {
    color: rgba(var(--text-color), 0.7);
  }
}

/*
 * PRODUCT META
 */

.product-meta {
  display: block;
  margin: 24px 0;
  padding-bottom: 16px;
  border-bottom: 1px solid rgb(var(--border-color));
}

.product-meta__price-list-container {
  display: flex;
  align-items: center;
  margin-top: -8px;
}

[dir="ltr"] .product-meta__label-list:not(:empty) {
  margin-left: 16px;
}

[dir="rtl"] .product-meta__label-list:not(:empty) {
  margin-right: 16px;
}

.product-meta__reference {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
}

.product-meta__sku {
  position: relative;
  top: 1px;
  letter-spacing: 0.45px;
}

.product-meta__taxes-included {
  margin-top: 0;
}

.product-meta__aside {
  display: flex;
  justify-content: space-between;
  margin-top: 18px;
}

.product-meta__share {
  display: flex;
  align-items: center;
}

[dir="ltr"] .product-meta__share-label {
  margin-right: 20px;
}

[dir="rtl"] .product-meta__share-label {
  margin-left: 20px;
}

.product-meta__share-button-list {
  display: inline-grid;
  grid-auto-flow: column;
  justify-content: flex-start;
  align-items: center;
  gap: 18px;
}

.product-meta__reviews-badge .rating__caption {
  position: relative;
}

.product-meta__reviews-badge .rating__caption::after {
  content: '';
  position: absolute;
  bottom: 2px;
  left: 0;
  width: 100%;
  height: 1px;
  background: currentColor;
  transform: scaleX(0);
  transform-origin: var(--transform-origin-end);
  transition: transform 0.3s;
}

@media screen and (min-width: 1000px) {
  .product-meta {
    margin-top: 0;
  }

  .product-meta__title {
    margin-bottom: 24px;
  }
}

@media screen and (pointer: fine) {
  .product-meta__reviews-badge:hover .spr-badge-caption::after,
  .product-meta__reviews-badge:hover .rating__caption::after {
    transform: scaleX(1);
    transform-origin: var(--transform-origin-start);
  }
}

/*
 * PRODUCT FORM
 */

.product-form {
  display: grid;
  grid-row-gap: 16px;
  row-gap: 16px;
}

.product-form__variants {
  display: block;
}

.product-form__option-info,
.product-form__quantity-label {
  display: flex;
  margin-bottom: 8px;
}

[dir="ltr"] .product-form__option-value {
  margin-left: 8px;
}

[dir="rtl"] .product-form__option-value {
  margin-right: 8px;
}

[dir="ltr"] .product-form__option-link {
  margin-left: auto;
}

[dir="rtl"] .product-form__option-link {
  margin-right: auto;
}

.no-js .product-form__option-selector {
  display: none;
}

.product-form__option-selector + .product-form__option-selector {
  margin-top: 16px;
}

.product-form__payment-container {
  display: grid;
  grid-gap: 10px;
  gap: 10px;
  margin-top: 8px;
}

.product-form__description {
  margin-top: 8px;
  margin-bottom: 8px;
}

.product-form__image--center {
  text-align: center;
}

[dir="ltr"] .product-form__image--right {
  text-align: right;
}

[dir="rtl"] .product-form__image--right {
  text-align: left;
}

.product-form__image img {
  width: 100%;
}

/* Custom element that controls the "payment terms" */
shopify-payment-terms {
  display: block;
}

.product-form__view-details {
  margin-top: 24px;
  text-align: center;
}

.product-form__store-availability-container {
  display: block;
}

.product-form__store-availability-container:empty {
  display: none; /* Make sure that it does not add extra space */
}

@media screen and (min-width: 741px) {
  .product-form__payment-container {
    margin-top: 16px;
  }
}

/*
 * INVENTORY
 */

.inventory {
  color: rgb(var(--product-in-stock-text-color));
}

.inventory--low {
  color: rgb(var(--product-low-stock-text-color));
}

/*
 * PRODUCT CONTENT PART
 */

.product-content {
  display: flex;
  margin-top: 36px;
  margin-bottom: 36px;
}

@media screen and (max-width: 999px) {
  .product-content {
    flex-direction: column-reverse; /* In mobile we show the "complete the look" products first */
  }

  .product-content__tabs + .product-content__featured-products {
    margin-bottom: 38px;
  }
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .product-content {
    margin-top: 48px;
    margin-bottom: 48px;
  }
}

@media screen and (min-width: 1000px) {
  .product-content {
    margin-top: 80px;
    margin-bottom: 80px;
    justify-content: space-between;
  }
}

@media screen and (min-width: 1200px) {
  .product-content {
    margin-left: calc(var(--grid-column-width) + var(--grid-gap));
    margin-right: calc(var(--grid-column-width) + var(--grid-gap));
  }
}

/*
 * PRODUCT CONTENT PART (TABS)
 */

.product-tabs__trust-list:not(:first-child) {
  margin-top: 32px;
}

.product-tabs__tab-item-wrapper:not([hidden]) {
  display: block;
}

@media screen and (max-width: 740px) {
  .product-content__tabs {
    margin: 0 calc(-1 * var(--container-gutter));
  }

  .product-tabs__tab-item-wrapper {
    --anchor-offset: 0px;
    padding: 0 var(--container-gutter);
  }

  .product-tabs__trust-title:not(:last-child) {
    margin-bottom: 24px;
  }
}

@media screen and (max-width: 999px) {
  .product-tabs__tab-item-wrapper {
    display: block;
    border-top: 1px solid rgb(var(--border-color));
  }

  .product-tabs__tab-item-wrapper:last-child {
    border-bottom: 1px solid rgb(var(--border-color));
  }

  .product-tabs__tab-item-content {
    margin-top: -2px;
    margin-bottom: 25px;
  }
}

@media screen and (min-width: 741px) {
  [dir="ltr"] .product-tabs__trust-list:not(:first-child) {
    padding-left: 0;
  }
  [dir="rtl"] .product-tabs__trust-list:not(:first-child) {
    padding-right: 0;
  }
  .product-tabs__trust-list:not(:first-child) {
    margin-top: 15px;
  }

  .product-tabs__trust-list:first-child {
    text-align: center;
  }

  .product-tabs__trust-title {
    display: inline-flex;
    margin-top: 25px;
  }

  [dir="ltr"] .product-tabs__trust-title:not(:last-child) {
    margin-right: 35px;
  }

  [dir="rtl"] .product-tabs__trust-title:not(:last-child) {
    margin-left: 35px;
  }
}

@media screen and (min-width: 1000px) {
  .product-content__tabs {
    flex: none;
    width: calc(var(--grid-column-width) * 10 + var(--grid-gap) * 10);
  }

  .product-content__tabs:only-child {
    /* The auto margin allows to center if only tabs are present */
    margin-left: auto;
    margin-right: auto;
    width: calc(var(--grid-column-width) * 12 + var(--grid-gap) * 12);
  }

  /* On lap and up, the inner collapsible are always visible */
  .product-tabs__tab-item-wrapper .collapsible {
    height: auto;
    overflow: auto;
    visibility: visible;
  }
}

/*
 * PRODUCT CONTENT PART (PRODUCTS)
 */

.product-content__featured-products-title {
  margin-bottom: 0;
}

.product-content__featured-products-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: var(--grid-gap);
  margin-top: 20px;
}

@media screen and (max-width: 740px) {
  /* On mobile we show the product a bit differently */
  .product-content__featured-products .product-item {
    flex-direction: row;
    align-items: center;
    scroll-snap-align: center;
    scroll-snap-stop: always;
    scroll-snap-margin: var(--container-gutter); /* iOS uses non-standard property */
    scroll-margin: var(--container-gutter);
  }

  .product-content__featured-products-list {
    grid-auto-flow: column;
    grid-auto-columns: minmax(64vw, 1fr);
    grid-template-columns: none;
  }

  [dir="ltr"] .product-content__featured-products .product-item__image-wrapper {
    margin: 0 24px 0 0;
  }

  [dir="rtl"] .product-content__featured-products .product-item__image-wrapper {
    margin: 0 0 0 24px;
  }

  .product-content__featured-products .product-item__image-wrapper {
    width: 104px;
    flex: none;
  }

  [dir="ltr"] .product-content__featured-products .product-item__info {
    text-align: left;
  }

  [dir="rtl"] .product-content__featured-products .product-item__info {
    text-align: right;
  }

  .product-content__featured-products .price-list {
    justify-content: flex-start;
  }

  [dir="ltr"] .product-content__featured-products .product-item__link {
    margin-left: 0;
  }

  [dir="rtl"] .product-content__featured-products .product-item__link {
    margin-right: 0;
  }
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .product-content__featured-products-list {
    grid-template-columns: 214px 214px;
  }
}

@media screen and (min-width: 1000px) {
  .product-content__featured-products {
    flex: none;
    width: calc(var(--grid-column-width) * 9 + var(--grid-gap) * 8);
  }

  .product-content__featured-products-list {
    margin-top: 32px;
  }
}

@media screen and (min-width: 1200px) {
  .product-content__featured-products {
    width: calc(var(--grid-column-width) * 7 + var(--grid-gap) * 6);
  }
}

/*
 * QUICK BUY (POPOVER AND DRAWER)
 */

.quick-buy-product {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
}

[dir="ltr"] .quick-buy-product__image {
  margin-right: 20px;
}

[dir="rtl"] .quick-buy-product__image {
  margin-left: 20px;
}

.quick-buy-product__image {
  width: 65px;
  flex: none;
  align-self: flex-start;
}

@media screen and (max-width: 740px) {
  [dir="ltr"] .quick-buy-product {
    padding-right: 32px;
  }
  [dir="rtl"] .quick-buy-product {
    padding-left: 32px;
  }
  .quick-buy-product {
    padding-top: 14px;
    padding-bottom: 14px;
  }

  .popover--quick-buy .product-form {
    padding-left: 24px;
    padding-right: 24px;
  }

  .popover--quick-buy .product-form > :first-child:not(.product-form__buy-buttons) {
    padding-top: 16px;
  }

  .popover--quick-buy .product-form__buy-buttons {
    margin-left: calc(-1 * var(--container-gutter));
    margin-right: calc(-1 * var(--container-gutter));
    padding: 16px;
    padding-bottom: max(16px, env(safe-area-inset-bottom, 0px) + 16px);
  }

  .popover--quick-buy .product-form__buy-buttons:not(:only-child) {
    position: -webkit-sticky;
    position: sticky;
    bottom: 0;
    margin-top: 8px;
    border-top: 1px solid rgb(var(--border-color));
    background: rgb(var(--root-background));
  }

  .popover--quick-buy .product-form__payment-container {
    margin-top: 0;
  }
}

@media screen and (min-width: 741px) {
  .quick-buy-product {
    margin: 32px 0 24px;
  }

  [dir="ltr"] .quick-buy-product__image {
    margin-right: 32px;
  }

  [dir="rtl"] .quick-buy-product__image {
    margin-left: 32px;
  }

  .quick-buy-product__image {
    width: 114px;
  }
}

/*
 * PRODUCT STICKY FORM
 */

[dir="ltr"] .product-sticky-form {
  left: 0;
}

[dir="rtl"] .product-sticky-form {
  right: 0;
}

.product-sticky-form {
  display: block;
  position: fixed;
  bottom: 0;
  padding-bottom: max(24px, env(safe-area-inset-bottom, 0px) + 24px);
  width: 100%;
  z-index: 2;
  transition: opacity 0.25s ease-in-out, transform 0.25s ease-in-out, visibility 0.25s ease-in-out;
}

.product-sticky-form[hidden] {
  opacity: 0;
  transform: translateY(100%);
  visibility: hidden;
}

@media screen and (max-width: 999px) {
  .product-sticky-form .product-form__add-button {
    width: 100%;
  }
}

@media screen and (min-width: 1000px) {
  .product-sticky-form {
    padding-top: 16px;
    padding-bottom: 16px;
    background: rgb(var(--background));
    border-bottom: 1px solid rgb(var(--border-color));
    top: calc(var(--header-height, 0px) * var(--enable-sticky-header) + var(--announcement-bar-height, 0px) * var(--enable-sticky-announcement-bar));
    bottom: auto;
    box-shadow: 0 6px 5px -5px rgba(var(--border-color), 0.4), 0 1px rgb(var(--border-color)) inset;
    transition: opacity 0.25s ease-in-out, transform 0.25s ease-in-out, visibility 0.25s ease-in-out;
  }

  .product-sticky-form[hidden] {
    transform: translateY(-100%);
  }

  .product-sticky-form:hover {
    z-index: 4;
  }

  [dir="ltr"] .product-sticky-form__form,[dir="ltr"]
  .product-sticky-form__variants {
    margin-left: auto;
  }

  [dir="rtl"] .product-sticky-form__form,[dir="rtl"]
  .product-sticky-form__variants {
    margin-right: auto;
  }

  .product-sticky-form__form,
  .product-sticky-form__variants {
    display: flex;
  }

  .product-sticky-form__content-wrapper,
  .product-sticky-form__inner {
    display: flex;
    align-items: center;
  }

  [dir="ltr"] .product-sticky-form__image-wrapper {
    margin-right: 18px;
  }

  [dir="rtl"] .product-sticky-form__image-wrapper {
    margin-left: 18px;
  }

  .product-sticky-form__image-wrapper {
    width: 55px;
    flex: none;
  }

  [dir="ltr"] .product-sticky-form__variants .select-wrapper + .select-wrapper {
    margin-left: -1px;
  }

  [dir="rtl"] .product-sticky-form__variants .select-wrapper + .select-wrapper {
    margin-right: -1px;
  }

  .product-sticky-form__variants .select-wrapper + .select-wrapper { /* Allow the borders to collapse */
  }

  .product-sticky-form__variants .select-wrapper:not(:first-child):not(:last-child) .select {
    border-radius: 0;
  }

  [dir="ltr"] .product-sticky-form__variants .select-wrapper:first-child:not(:only-child) .select {
    border-top-right-radius: 0;
  }

  [dir="rtl"] .product-sticky-form__variants .select-wrapper:first-child:not(:only-child) .select {
    border-top-left-radius: 0;
  }

  [dir="ltr"] .product-sticky-form__variants .select-wrapper:first-child:not(:only-child) .select {
    border-bottom-right-radius: 0;
  }

  [dir="rtl"] .product-sticky-form__variants .select-wrapper:first-child:not(:only-child) .select {
    border-bottom-left-radius: 0;
  }

  [dir="ltr"] .product-sticky-form__variants .select-wrapper:last-child:not(:only-child) .select {
    border-top-left-radius: 0;
  }

  [dir="rtl"] .product-sticky-form__variants .select-wrapper:last-child:not(:only-child) .select {
    border-top-right-radius: 0;
  }

  [dir="ltr"] .product-sticky-form__variants .select-wrapper:last-child:not(:only-child) .select {
    border-bottom-left-radius: 0;
  }

  [dir="rtl"] .product-sticky-form__variants .select-wrapper:last-child:not(:only-child) .select {
    border-bottom-right-radius: 0;
  }

  .product-sticky-form__variants .select {
    min-width: 150px;
    max-width: 300px;
  }

  @supports (height: min(1px, 2px)) {
    .product-sticky-form__variants .select {
      min-width: min(max(var(--largest-option-width), 150px), 300px);
    }
  }

  /* The height of the selectors is slightly reduced only here */
  .product-sticky-form .button,
  .product-sticky-form .select {
    line-height: 48px;
    height: 48px;
  }

  [dir="ltr"] .product-sticky-form__payment-container {
    margin-left: 10px;
  }

  [dir="rtl"] .product-sticky-form__payment-container {
    margin-right: 10px;
  }
}
/**
 * -------------------------------------------------------------
 * PRODUCT ITEM META
 *
 * This component is used on many elements (like grid item,
 * cart page, mini-cart, order...) and guarantee a consistent
 * spacing across all elements
 * -------------------------------------------------------------
 */

.product-item-meta {
  display: block;
}

.product-item-meta__vendor {
  display: block;
  margin-bottom: 6px;
}

.product-item-meta__title {
  display: block;
  line-height: 1.6;
  margin-bottom: 4px;
}

.product-item-meta__reviews-badge {
  display: block;
  margin-top: 2px;
}

.product-item-meta__color-count {
  margin-top: 4px;
}

.product-item-meta__reviews-badge + .product-item-meta__color-count {
  margin-top: 4px; /* When preceded by reviews badge we have a smaller gap */
}

.product-item-meta__reviews-badge .spr-icon {
  width: 12px;
  height: 12px;
}

.product-item-meta__swatch-list {
  margin-top: 10px;
  margin-bottom: 2px; /* The extra 2px at the bottom is to avoid the border to be clipped on iOS */
  justify-content: center;
  gap: 6px;
}

.product-item-meta__property-list {
  display: grid;
  margin-top: 6px;
  margin-bottom: 4px;
  grid-auto-flow: row;
}

@media screen and (min-width: 741px) {
  .product-item-meta__property-list {
    margin-top: 6px;
    margin-bottom: 6px;
  }

  .product-item-meta__color-count {
    margin-top: 8px;
  }

  .product-item-meta__title {
    line-height: 1.5; /* We slightly reduce the line height on larger screen */
  }
}

/**
 * -------------------------------------------------------------
 * PRODUCT ITEM
 *
 * extra info only available on collection and featured collection pages
 * -------------------------------------------------------------
 */

.product-item {
  position: relative;
  display: flex;
  flex-direction: column;
}

.product-item__image-wrapper {
  position: relative;
  display: block;
  margin-bottom: 16px;
  overflow: hidden;
}

.product-item__image-wrapper--placeholder {
  fill: currentColor;
}

[dir="ltr"] .product-item__label-list {
  left: 10px;
}

[dir="rtl"] .product-item__label-list {
  right: 10px;
}

.product-item__label-list {
  position: absolute;
  z-index: 1;
  top: 10px;
}

.product-item__image-wrapper--multiple .product-item__primary-image,
.product-item__image-wrapper--multiple .product-item__secondary-image {
  transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
}

.product-item__aspect-ratio {
  isolation: isolate;
}

.product-item__primary-image,
.product-item__secondary-image {
  border-radius: var(--block-border-radius-reduced);
}

.product-item__secondary-image {
  position: absolute;
  display: none; /* Allows to make sure it does not trigger lazyload */
  visibility: hidden;
  opacity: 0;
  top: 50% !important;
  left: 50% !important; /* We must use this instead of logical property here */
  transform: translate(-50%, -50%) !important;
  -o-object-fit: contain;
     object-fit: contain;
  -o-object-position: center;
     object-position: center;
}

.product-item__info {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-grow: 1;
  text-align: center;
}

.product-item__info--with-button {
  justify-content: space-between;
}

.product-item__cta {
  margin-top: 16px;
}

[dir="ltr"] .product-item__quick-form {
  left: 0;
}

[dir="rtl"] .product-item__quick-form {
  right: 0;
}

.product-item__quick-form {
  position: absolute;
  padding: 10px;
  bottom: 0;
  width: 100%;
  visibility: hidden;
  opacity: 0;
  transition: visibility 0.2s ease-in-out, opacity 0.2s ease-in-out;
}

/* This button is only appearing on touch device */
[dir="ltr"] .product-item__quick-buy-button {
  right: 12px;
}
[dir="rtl"] .product-item__quick-buy-button {
  left: 12px;
}
.product-item__quick-buy-button {
  position: absolute;
  bottom: 12px;
  padding: 5px;
  background: rgb(var(--root-background));
  color: rgb(var(--root-text-color));
  border: 1px solid rgb(var(--root-border-color));
}

.product-item__link {
  display: inline-block;
  margin: 6px auto 0;
}

@media screen and (pointer: fine) {
  .product-item__secondary-image {
    display: block;
  }

  .product-item__image-wrapper--multiple:hover .product-item__primary-image {
    visibility: hidden;
    opacity: 0;
  }

  .product-item__image-wrapper--multiple:hover .product-item__secondary-image {
    visibility: visible;
    opacity: 1;
  }

  .product-item__image-wrapper:hover .product-item__quick-form {
    visibility: visible;
    opacity: 1;
  }
}

@media screen and (pointer: fine) and (prefers-reduced-motion: no-preference) {
  .product-item__quick-form {
    transform: translateY(16px);
    transition: visibility 0.2s ease-in-out, opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
  }

  .product-item__image-wrapper:hover .product-item__quick-form {
    transform: translateY(0);
    visibility: visible;
    opacity: 1;
  }
}

@media not screen and (pointer: fine) {
  .product-item__quick-form {
    visibility: visible;
    opacity: 1;
  }
}

@media screen and (min-width: 1000px) {
  .product-item__image-wrapper {
    margin-bottom: 24px;
  }
}
/**
 * -------------------------------------------------------------
 * PRODUCT LIST
 * -------------------------------------------------------------
 */

.product-list {
  --product-list-column-gap: var(--grid-gap);
}

.product-list:not([hidden]) {
  position: relative;
  display: block;
}

.product-list__inner {
  display: grid;
  grid-template-columns: repeat(auto-fit, calc(100% / var(--section-products-per-row) - var(--product-list-column-gap) * (var(--section-products-per-row) - 1) / var(--section-products-per-row)));
  grid-gap: var(--product-list-block-spacing) var(--product-list-column-gap);
  gap: var(--product-list-block-spacing) var(--product-list-column-gap);
  overflow: hidden;
}

.product-list__inner--scroller {
  overflow-x: auto;
}

[dir="ltr"] .product-list--center .product-list__inner {
  justify-content: safe center; /* For some reason it does not work in RTL on Chrome */
}

@media screen and (max-width: 740px) {
  /* On mobile when the products are shown in grid we reduce spacing */
  .product-list {
    --product-list-column-gap: 12px;
  }

  .product-list__inner:not(.product-list__inner--scroller) {
    margin-left: calc(-1 * var(--container-gutter) / 2);
    margin-right: calc(-1 * var(--container-gutter) / 2);
  }
}

@media screen and (max-width: 999px) {
  .product-list__inner--scroller {
    padding-left: var(--container-gutter);
    padding-right: var(--container-gutter);
    grid-auto-flow: column;
    grid-auto-columns: 52vw;
    grid-template-columns: none;
    min-width: min-content;
  }
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .product-list__inner--scroller {
    grid-auto-columns: 35vw;
  }
}

@media screen and (min-width: 1000px) {
  .product-list {
    /* We calculate the width of one product item cell thanks to power of CSS variables */
    --item-width: calc((var(--container-max-width-minus-gutters) - 56px) / var(--section-products-per-row) - (var(--grid-gap) / var(--section-products-per-row) * (var(--section-products-per-row) - 1)));
  }

  @supports (width: max(1px, 2px)) {
    .product-list {
      --item-width: calc((min(100vw - var(--container-gutter) * 2, var(--container-max-width-minus-gutters)) - 56px) / var(--section-products-per-row) - (var(--grid-gap) / var(--section-products-per-row) * (var(--section-products-per-row) - 1)));
    }
  }

  .product-list__prev-next {
    position: absolute;
    pointer-events: none;
    display: flex;
    justify-content: space-between;
    width: 100%;
    top: calc(var(--item-width) / var(--smallest-image-aspect-ratio) / 2 - 28px); /* 28px is half the height of the button */
  }

  .product-list__arrow {
    z-index: 1;
    opacity: 0;
    visibility: hidden;
    transform: scale(0.5);
    transition: opacity 0.15s ease-in-out, visibility 0.15s ease-in-out, transform 0.15s ease-in-out;
  }

  .product-list:hover .product-list__arrow:not([disabled]) {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
  }

  .product-list__inner--scroller {
    grid-template-columns: none;
    grid-auto-flow: column;
    grid-auto-columns: calc(100% / var(--section-products-per-row, 4) - (var(--grid-gap) / var(--section-products-per-row, 4) * (var(--section-products-per-row, 4) - 1)));
    margin-left: 28px;
    margin-right: 28px; /* We add a bit of space for the arrow */
    overflow: hidden;
  }

  .product-list__inner--desktop-no-scroller {
    margin-left: 0;
    margin-right: 0;
  }
}

@media not screen and (pointer: fine) {
  .product-list__arrow:not([disabled]) {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
  }
}
.promotion-block-list {
  --promotion-block-gutter: var(--container-gutter);

  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin: calc(-1 * var(--promotion-block-gutter) / 2);
}

.promotion-block {
  --promotion-block-padding: 32px;
  --promotion-block-min-height: 180px;
  position: relative;
  display: flex;
  width: 100%;
  margin: calc(var(--promotion-block-gutter) / 2);
  padding: var(--promotion-block-padding);
  background: rgb(var(--section-block-background));
  color: rgb(var(--text-color));
  min-height: var(--promotion-block-min-height);
  min-width: 0;
  align-items: var(--section-blocks-alignment, flex-end);
  overflow: hidden;
  border-radius: var(--block-border-radius-reduced);
}

.promotion-block--medium {
  --promotion-block-min-height: 210px;
}

.promotion-block--large {
  --promotion-block-min-height: 250px;
}

.promotion-block__content-wrapper {
  position: relative;
  width: 100%;
}

.promotion-block:hover .link {
  -webkit-text-decoration-color: rgb(var(--text-color));
          text-decoration-color: rgb(var(--text-color)); /* Make sure that the underlined link is in active state on hover of the whole block */
}

/* IMAGE DEDICATED BLOCK */

[dir="ltr"] .promotion-block__image {
  right: 0;
}

[dir="rtl"] .promotion-block__image {
  left: 0;
}

.promotion-block__image {
  position: absolute;
  top: 0;
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  border-radius: var(--block-border-radius-reduced);
}

/* QUOTE DEDICATED BLOCK */

[dir="ltr"] .promotion-block--quote::before {
  left: var(--promotion-block-padding);
}

[dir="rtl"] .promotion-block--quote::before {
  right: var(--promotion-block-padding);
}

.promotion-block--quote::before {
  content: '';
  position: absolute;
  width: 50px;
  height: 40px;
  top: var(--promotion-block-padding);
  -webkit-mask-image: url(data:image/svg+xml;base64,PHN2ZyBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDEgODAiPiAgICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTAgNDEuNzdWMGg0MS42MDR2NDEuNzdMMjAuMDI2IDgwSC45ODdMMjEuNzIgNDEuNzdIMHptNTkuMzk2IDBWMEgxMDF2NDEuNzdMNzkuNDIyIDgwSDYwLjM4M2wyMC43MzItMzguMjNINTkuMzk2eiIgZmlsbD0iY3VycmVudENvbG9yIiAvPjwvc3ZnPg==);
          mask-image: url(data:image/svg+xml;base64,PHN2ZyBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDEgODAiPiAgICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTAgNDEuNzdWMGg0MS42MDR2NDEuNzdMMjAuMDI2IDgwSC45ODdMMjEuNzIgNDEuNzdIMHptNTkuMzk2IDBWMEgxMDF2NDEuNzdMNzkuNDIyIDgwSDYwLjM4M2wyMC43MzItMzguMjNINTkuMzk2eiIgZmlsbD0iY3VycmVudENvbG9yIiAvPjwvc3ZnPg==);
  -webkit-mask-size: 50px 40px;
          mask-size: 50px 40px;
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background: rgb(var(--text-color));
  opacity: 0.3;
  pointer-events: none;
}

.promotion-block--quote .promotion-block__content-wrapper {
  margin-top: 50px; /* This ensure a minimum space so that text does not cover the quote */
}

/* VIDEO DEDICATED BLOCK */

.promotion-block--video {
  padding: 0 !important; /* The video cover the tile */
}

.promotion-block .video-wrapper {
  --video-height: calc(var(--promotion-block-min-height) + 60px); /* We add a bit of extra space in case other blocks may be taller */
  min-height: var(--promotion-block-min-height);
  height: 100%;
}

/* PRODUCT DEDICATED BLOCK */

.promotion-block--products {
  padding: 24px !important;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.promotion-block__product-list-wrapper {
  display: block;
}

.promotion-block__product-list {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  overflow: hidden;
  margin-left: -24px;
  margin-right: -24px;
  scroll-snap-type: x mandatory;
}

.promotion-block__product-list-item {
  display: block;
  min-width: 100%;
  scroll-snap-align: center;
  scroll-snap-stop: always;
}

.promotion-block__product-list-item .placeholder-background {
  background: transparent;
}

.promotion-block__product-list-prev-next {
  position: absolute;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  top: calc(50% - 20px); /* 20px is half the height of the buttons */
  pointer-events: none;
}

.promotion-block__product-list .product-item__image-wrapper {
  width: 100%;
  max-width: 150px;
  margin-left: auto;
  margin-right: auto;
}

@media not screen and (pointer: fine) {
  .promotion-block__product-list {
    overflow: auto;
  }
}

@media screen and (max-width: 740px) {
  .promotion-block-list--scrollable {
    flex-wrap: nowrap;
  }

  .promotion-block-list--scrollable .promotion-block {
    width: 81vw;
    scroll-snap-align: center;
    scroll-snap-stop: always;
    scroll-snap-margin: var(--promotion-block-gutter); /* iOS uses non-standard property */
    scroll-margin: var(--promotion-block-gutter);
  }
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .promotion-block {
    flex: 1 0 calc(50% - var(--container-gutter));
  }

  .promotion-block .newsletter__form .input-row {
    grid-template-columns: 1fr auto; /* We have enough space :) */
  }
}

@media screen and (min-width: 1000px) {
  .promotion-block {
    flex: 1 0 calc(33.3333% - var(--promotion-block-gutter));
  }

  .promotion-block:not(.promotion-block--expand):only-child {
    max-width: 50%;
  }

  .promotion-block--highlight {
    flex-basis: calc(66.6666% - var(--promotion-block-gutter));
  }

  .promotion-block--quote::before {
    width: 60px;
    height: 48px;
    -webkit-mask-size: 60px 48px;
            mask-size: 60px 48px;
  }

  .promotion-block:not(.promotion-block--highlight) .newsletter__form .input-row {
    grid-template-columns: none; /* We do not have enough space */
  }
}

@media screen and (min-width: 1200px) {
  .promotion-block {
    --promotion-block-min-height: 250px;
    --promotion-block-padding: 48px;
  }

  .promotion-block--compact {
    --promotion-block-padding: 40px;
  }

  .promotion-block--medium {
    --promotion-block-min-height: 320px;
  }

  .promotion-block--large {
    --promotion-block-min-height: 370px;
  }
}

@media screen and (min-width: 1400px) {
  .promotion-block .newsletter__form .input-row {
    grid-template-columns: 1fr auto; /* We have enough space :) */
  }
}
.main-search__form {
  --form-input-field-height: 60px;

  position: relative;
  max-width: 390px;
  margin-left: auto;
  margin-right: auto;
}

[dir="ltr"] .main-search__input {
  padding-right: 55px;
}

[dir="rtl"] .main-search__input {
  padding-left: 55px;
}

[dir="ltr"] .main-search__submit {
  right: 20px;
}

[dir="rtl"] .main-search__submit {
  left: 20px;
}

.main-search__submit {
  position: absolute;
  top: calc(50% - 10px);
}

.main-search__empty-text {
  margin-top: 40px;
}

.main-search__results {
  display: block;
}

.main-search__form + .tabs-nav {
  margin-top: 38px;
}

@media screen and (min-width: 741px) {
  .main-search__form {
    --form-input-field-height: 80px;
  }

  [dir="ltr"] .main-search__submit {
    right: 32px;
  }

  [dir="rtl"] .main-search__submit {
    left: 32px;
  }

  [dir="ltr"] .main-search__input {
    padding-left: 30px;
    padding-right: 80px;
  }

  [dir="rtl"] .main-search__input {
    padding-right: 30px;
    padding-left: 80px;
  }

  .main-search__form + .tabs-nav {
    margin-top: 68px;
  }
}
.shop-the-look,
.shop-the-look__item {
  position: relative;
  display: block;
  overflow: hidden;
}

.shop-the-look__item {
  background: rgb(var(--secondary-background)); /* Use as a filler */
}

.shop-the-look__item[hidden] {
  position: absolute;
  visibility: hidden;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: -1;
}

.shop-the-look__item[hidden] .popover,
.shop-the-look__item[hidden] .drawer {
  display: none; /* Making sure popover and drawer are hidden for hidden look helps alleviating issues with Safari */
}

.shop-the-look__image {
  width: 100%;
}

.shop-the-look__product-wrapper {
  position: absolute; /* Position is set dynamically in Liquid */
}

/* Dot elements */

.shop-the-look__dot {
  position: relative;
  display: block;
  width: 24px;
  height: 24px;
  border-radius: 100%;
  background: var(--section-dot-inner-background);
  box-shadow: 0 0 0 8px rgb(var(--section-dot-background)) inset, 0 1px 5px rgba(0, 0, 0, 0.15);
}

@keyframes shopTheLookDotKeyframe {
  0% {
    opacity: 1;
    transform: scale(0.4);
  }

  100% {
    opacity: 0;
    transform: scale(1.3);
  }
}

.shop-the-look__dot::after {
  content: '';
  position: absolute;
  left: -4px;
  top: -4px;
  width: 32px;
  height: 32px;
  border: 2px solid rgba(var(--section-dot-background), 0.6);
  border-radius: 100%;
  animation: shopTheLookDotKeyframe 2s ease-in-out infinite;
}

/* Product part */

/* This allows to shift by the size of the dot */
[dir="ltr"] .shop-the-look__product-wrapper {
  margin-left: -12px;
}
[dir="rtl"] .shop-the-look__product-wrapper {
  margin-right: -12px;
}
.shop-the-look__product-wrapper {
  margin-top: -12px;
}

[dir="ltr"] .shop-the-look__product {
  padding: 15px 32px 15px 15px;
}

[dir="rtl"] .shop-the-look__product {
  padding: 15px 15px 15px 32px;
}

[dir="ltr"] .shop-the-look__product {
  left: calc(100% + 28px);
}

[dir="rtl"] .shop-the-look__product {
  right: calc(100% + 28px);
}

.shop-the-look__product {
  position: absolute;
  display: flex;
  align-items: center;
  width: max-content;
  top: 50%;
  max-width: 46vw;
  background: rgb(var(--background));
  visibility: hidden;
  opacity: 0;
  transform: scale(0.8) translateY(-50%);
  transition: visibility 0.4s cubic-bezier(0.75, 0, 0.175, 1), opacity 0.4s cubic-bezier(0.75, 0, 0.175, 1), transform 0.4s cubic-bezier(0.75, 0, 0.175, 1);
  will-change: transform;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
  transform-origin: top var(--transform-origin-start);
  z-index: 1;
  border-radius: min(var(--block-border-radius), 4px);
}

.shop-the-look__product[open] {
  visibility: visible;
  opacity: 1;
  transform: scale(1) translateY(-50%);
}

[dir="ltr"] .shop-the-look__product::before {
  right: 100%;
}

[dir="rtl"] .shop-the-look__product::before {
  left: 100%;
}

.shop-the-look__product::before {
  position: absolute;
  content: '';
  width: 0;
  height: 0;
  border-style: solid;
  border-color: transparent rgb(var(--background)) transparent transparent;
  border-width: 8px;
}

[dir="ltr"] .shop-the-look__product--reverse {
  left: auto;
}

[dir="rtl"] .shop-the-look__product--reverse {
  right: auto;
}

[dir="ltr"] .shop-the-look__product--reverse {
  right: calc(100% + 28px);
}

[dir="rtl"] .shop-the-look__product--reverse {
  left: calc(100% + 28px);
}

.shop-the-look__product--reverse {
  transform-origin: top var(--transform-origin-end);
}

[dir="ltr"] .shop-the-look__product--reverse::before {
  right: auto;
}

[dir="rtl"] .shop-the-look__product--reverse::before {
  left: auto;
}

[dir="ltr"] .shop-the-look__product--reverse::before {
  left: 100%;
}

[dir="rtl"] .shop-the-look__product--reverse::before {
  right: 100%;
}

.shop-the-look__product--reverse::before {
  border-color: transparent transparent transparent rgb(var(--background));
}

.shop-the-look__product-bottom-wrapper {
  display: grid;
  grid-auto-flow: column;
  justify-content: flex-start;
  grid-gap: 12px;
  gap: 12px;
}

[dir="ltr"] .shop-the-look__product-image {
  margin-right: 24px;
}

[dir="rtl"] .shop-the-look__product-image {
  margin-left: 24px;
}

.shop-the-look__product-image {
  flex: none;
  width: 72px;
}

.shop-the-look__product-vendor {
  display: block;
  max-width: max-content;
  margin-bottom: 1px;
}

.shop-the-look__product-title {
  display: block;
  margin-bottom: 2px;
}

/* Nav */

.shop-the-look__nav {
  position: absolute;
  display: block;
  bottom: 24px;
  background: rgb(var(--background));
  color: rgb(var(--text-color));
  border: 1px solid rgb(var(--border-color));
  border-radius: min(var(--button-border-radius), 10px);
  overflow: hidden;
  z-index: 1;
  transform: translateZ(0); /* Solves an extremely odd rendering issue on Safari */
}

.shop-the-look__prev-next-buttons {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.shop-the-look__arrow {
  border: none;
  border-radius: 0;
}

[dir="ltr"] .shop-the-look__arrow:first-child {
  border-right: 1px solid rgb(var(--border-color));
}

[dir="rtl"] .shop-the-look__arrow:first-child {
  border-left: 1px solid rgb(var(--border-color));
}

@media screen and (min-width: 741px) {
  .shop-the-look__nav {
    bottom: 40px;
  }
}

@media screen and (min-width: 1000px) {
  .shop-the-look__label {
    text-align: center;
    line-height: 56px;
    padding-left: 20px;
    padding-right: 20px;
    border-bottom: 1px solid rgb(var(--border-color));
  }

  [dir="ltr"] .shop-the-look__arrow:last-child {
    border-left: 1px solid rgb(var(--border-color));
  }

  [dir="rtl"] .shop-the-look__arrow:last-child {
    border-right: 1px solid rgb(var(--border-color));
  }

  .shop-the-look__counter {
    flex-grow: 1;
    text-align: center;
    padding-left: 20px;
    padding-right: 20px;
    line-height: 1.4;
    overflow: hidden;
  }

  .shop-the-look__counter-page {
    position: relative;
  }

  .shop-the-look__counter-page-base {
    opacity: 0; /* Just to allocate the space */
  }

  [dir="ltr"] .shop-the-look__counter-page-transition {
    left: 0;
  }

  [dir="rtl"] .shop-the-look__counter-page-transition {
    right: 0;
  }

  .shop-the-look__counter-page-transition {
    position: absolute;
    display: inline-block;
    top: 0;
    height: 100%;
    line-height: normal;
  }

  .shop-the-look__counter-page-transition[hidden] {
    transform: translateY(100%);
    visibility: hidden;
  }
}
/**
 * IMPLEMENTATION NOTE: as always, styling Shopify Reviews is a whole mess, as the app adds ton of useless and outdated
 * CSS, so we have to override everything. You should really touch this code with extra extra care...
 */

/* GENERAL */

.spr-starrating,
#shopify-product-reviews .spr-starratings {
  display: inline-flex;
  align-items: center;
  margin: 0 !important;
}

.spr-icon.spr-icon {
  width: 14px;
  height: 14px;
  top: 0;
  background-color: currentColor;
  -webkit-mask-image: url(data:image/svg+xml;base64,PHN2ZyBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxNSAxNCI+ICA8cGF0aCBkPSJNNy41LjVsMS42NDYgNC43MzUgNS4wMTEuMTAyLTMuOTk0IDMuMDI4IDEuNDUxIDQuNzk4TDcuNSAxMC4zbC00LjExNCAyLjg2MyAxLjQ1MS00Ljc5OEwuODQzIDUuMzM3bDUuMDExLS4xMDJMNy41LjV6IiBmaWxsPSIjMUUzMTZBIi8+PC9zdmc+);
          mask-image: url(data:image/svg+xml;base64,PHN2ZyBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxNSAxNCI+ICA8cGF0aCBkPSJNNy41LjVsMS42NDYgNC43MzUgNS4wMTEuMTAyLTMuOTk0IDMuMDI4IDEuNDUxIDQuNzk4TDcuNSAxMC4zbC00LjExNCAyLjg2MyAxLjQ1MS00Ljc5OEwuODQzIDUuMzM3bDUuMDExLS4xMDJMNy41LjV6IiBmaWxsPSIjMUUzMTZBIi8+PC9zdmc+);
  -webkit-mask-size: cover;
          mask-size: cover;
}

.spr-icon, .spr-icon-star-hover {
  color: rgb(var(--product-star-rating));
}

.spr-icon-star-hover {
  opacity: 1 !important;
}

[dir="ltr"] .spr-icon:not(:last-child) {
  margin-right: 2px;
}

[dir="rtl"] .spr-icon:not(:last-child) {
  margin-left: 2px;
}

.spr-icon::before {
  content: none !important; /* Remove the default Shopify styles */
}

.spr-icon.spr-icon.spr-icon-star-empty {
  opacity: 0.4;
}

/* BADGE */

[dir="ltr"] .spr-summary-starrating + .spr-summary-caption {
  margin-left: 8px;
}

[dir="rtl"] .spr-summary-starrating + .spr-summary-caption {
  margin-right: 8px;
}

/* MAIN REVIEW */

/* There are lot of elements we do not want */
.product-tabs .spr-header-title, /* the title must only be hidden if the reviews are embedded into the tab system */
.spr-form-title,
.spr-summary::before,
.spr-summary::after,
.spr-form-contact::before,
.spr-form-contact::after,
.spr-form-review::before,
.spr-form-review::after,
.spr-form-actions::before,
.spr-form-actions::after {
  display: none !important;
}

#shopify-product-reviews {
  display: block !important; /* Make sure reviews are always visible whatever is checked in the Shopify app */
  margin: 0 !important;
}

#shopify-product-reviews .spr-container {
  border: none;
  padding: 0;
}

#shopify-product-reviews .spr-form:not(.spr-form--success) {
  padding: 24px;
  border: 1px solid rgb(var(--border-color));
}

#shopify-product-reviews .spr-form {
  border-radius: var(--block-border-radius-reduced);
}

#shopify-product-reviews .spr-form--success {
  border: none;
  padding: 0;
}

#shopify-product-reviews .spr-form,
#shopify-product-reviews #shopify-product-reviews {
  margin-top: 24px;
}

#shopify-product-reviews .spr-form-contact {
  margin-top: -8px;
}

#shopify-product-reviews .spr-form-label {
  display: inline-block;
  margin-bottom: 8px;
  font-size: inherit;
  line-height: inherit;
}

#shopify-product-reviews .spr-form-input::placeholder {
  color: rgba(var(--text-color), 0.7);
}

#shopify-product-reviews fieldset > * {
  margin-bottom: 16px;
}

#shopify-product-reviews .spr-form-review-body {
  margin-bottom: 0 !important; /* This is the last field */
}

#shopify-product-reviews .spr-form-input.spr-starrating {
  display: block;
}

#shopify-product-reviews .spr-form-input .spr-icon {
  width: 20px;
  height: 20px;
  opacity: 1;
}

#shopify-product-reviews .spr-form-input .spr-icon-star-empty:not(.spr-icon-star-hover) {
  color: rgba(var(--text-color), 0.4);
}

[dir="ltr"] #shopify-product-reviews .spr-summary {
  text-align: left;
}

[dir="rtl"] #shopify-product-reviews .spr-summary {
  text-align: right;
}

#shopify-product-reviews .spr-summary-actions {
  display: block;
  margin-top: 16px;
}

#shopify-product-reviews .spr-summary-starrating {
  vertical-align: text-top;
}

#shopify-product-reviews .spr-summary-starrating .spr-icon {
  width: 18px;
  height: 18px;
}

#shopify-product-reviews .spr-button-primary {
  float: none;
  margin-top: var(--form-submit-margin);
  margin-bottom: 0;
}

#shopify-product-reviews .spr-form-message {
  padding: 12px 16px;
  outline: none;
}

#shopify-product-reviews .spr-form-message:not(:last-child) {
  margin-bottom: 24px;
}

#shopify-product-reviews .spr-form-message-success {
  background: rgb(var(--success-background));
  color: rgb(var(--success-color));
}

#shopify-product-reviews .spr-form-message-error {
  background: rgb(var(--error-background));
  color: rgb(var(--error-color));
}

#shopify-product-reviews .spr-review {
  position: relative;
  padding: 24px !important;
  background: rgb(var(--secondary-background));
  border: none;
  border-radius: var(--block-border-radius-reduced);
}

#shopify-product-reviews .spr-review + .spr-review {
  margin-top: 16px;
}

#shopify-product-reviews .spr-review-header-starratings {
  vertical-align: top;
}

#shopify-product-reviews .spr-review-content {
  margin-top: 12px;
  margin-bottom: calc(2em + 14px);
}

#shopify-product-reviews .spr-review-header-byline,
#shopify-product-reviews .spr-review-reportreview {
  float: none;
  font-style: normal;
  font-size: calc(var(--base-font-size) - 2px);
  line-height: 1.6923076923;
  opacity: 1;
  color: rgba(var(--text-color), 0.7);
}

#shopify-product-reviews .spr-review-reportreview {
  text-decoration: underline;
  text-underline-offset: 3px;
  -webkit-text-decoration-color: rgba(var(--text-color), 0.35);
          text-decoration-color: rgba(var(--text-color), 0.35);
  transition: color 0.2s ease-in-out, -webkit-text-decoration-color 0.2s ease-in-out;
  transition: text-decoration-color 0.2s ease-in-out, color 0.2s ease-in-out;
  transition: text-decoration-color 0.2s ease-in-out, color 0.2s ease-in-out, -webkit-text-decoration-color 0.2s ease-in-out;
}

#shopify-product-reviews .spr-review-reportreview:hover {
  color: rgb(var(--text-color));
  -webkit-text-decoration-color: rgb(var(--text-color));
          text-decoration-color: rgb(var(--text-color));
}

#shopify-product-reviews .spr-review-header-title,
#shopify-product-reviews .spr-review-content-body {
  font-size: inherit;
  line-height: inherit;
}

#shopify-product-reviews .spr-review-header-title {
  margin-top: 2px;
  font-weight: var(--text-font-bold-weight);
}

/* We cannot control the DOM generated so we have to be creative... */
#shopify-product-reviews .spr-review-header-byline {
  position: absolute;
  margin: 0;
  bottom: calc(30px + 2em);
}

#shopify-product-reviews .spr-review-header-byline strong {
  font-weight: normal;
}

[dir="ltr"] #shopify-product-reviews .spr-pagination {
  text-align: left;
}

[dir="rtl"] #shopify-product-reviews .spr-pagination {
  text-align: right;
}

#shopify-product-reviews .spr-pagination {
  margin-top: 24px;
  padding: 0;
  border-top: none;
}

#shopify-product-reviews .spr-pagination > div {
  display: table;
  border-collapse: collapse;
  table-layout: fixed;
}

#shopify-product-reviews .spr-pagination-page,
#shopify-product-reviews .spr-pagination-next,
#shopify-product-reviews .spr-pagination-prev {
  position: relative;
  display: table-cell;
  border: 1px solid rgb(var(--border-color));
  vertical-align: middle;
  height: 47px;
  width: 47px;
  text-align: center;
}

.spr-pagination-prev a,
.spr-pagination-next a {
  min-width: max-content;
  padding-left: 8px;
  padding-right: 8px;
}

/* Allows to make the whole pagination link clickable */
#shopify-product-reviews .spr-pagination a {
  display: flex;
  position: relative;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
}

#shopify-product-reviews .spr-pagination-page.is-active {
  box-shadow: 0 0 0 1px currentColor inset;
  outline: 1px solid currentColor;
  outline-offset: -1px;
  border-color: currentColor;
  font-weight: var(--text-font-bold-weight);
}

[dir="rtl"] .spr-form-review-rating a,
[dir="rtl"] .spr-form-review-rating a:hover {
  float: right;
}

@media screen and (min-width: 741px) {
  #shopify-product-reviews .spr-summary {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    row-gap: 16px;
  }

  [dir="ltr"] #shopify-product-reviews .spr-summary-actions {
    margin-left: auto;
  }

  [dir="rtl"] #shopify-product-reviews .spr-summary-actions {
    margin-right: auto;
  }

  #shopify-product-reviews .spr-summary-actions {
    margin-top: 0;
  }

  #shopify-product-reviews .spr-form:not(.spr-form--success) {
    padding: 32px;
  }

  #shopify-product-reviews .spr-form,
  #shopify-product-reviews #shopify-product-reviews {
    margin-top: 32px;
  }

  #shopify-product-reviews .spr-form-contact {
    display: grid;
    grid-auto-flow: column;
    grid-gap: var(--form-input-gap);
    gap: var(--form-input-gap);
  }

  #shopify-product-reviews .spr-form-message {
    padding: 13px 18px;
  }

  #shopify-product-reviews .spr-form-message:not(:last-child) {
    margin-bottom: 32px;
  }

  #shopify-product-reviews .spr-review {
    padding: 32px !important;
  }

  #shopify-product-reviews .spr-review-header-byline,
  #shopify-product-reviews .spr-review-reportreview {
    font-size: calc(var(--base-font-size) - 1px);
    line-height: 1.714285713;
  }

  #shopify-product-reviews .spr-review-content {
    margin-top: 8px;
    margin-bottom: calc(1em - 2px);
  }

  /* We cannot control the DOM generated so we have to be creative... */
  #shopify-product-reviews .spr-review-header-byline {
    bottom: 32px;
  }

  .spr-review-reportreview {
    float: right !important;
  }

  [dir="rtl"] .spr-review-reportreview {
    float: left !important;
  }

  #shopify-product-reviews .spr-pagination {
    margin-top: 32px;
  }

  #shopify-product-reviews .spr-pagination-page,
  #shopify-product-reviews .spr-pagination-next,
  #shopify-product-reviews .spr-pagination-prev {
    height: 56px;
    width: 56px;
  }
}
/**
 * STYLES FOR SOME PAGES CONTROLLED BY SHOPIFY
 */

.shopify-challenge__container {
  margin-top: var(--vertical-breather) !important;
  margin-bottom: var(--vertical-breather) !important;
}

.shopify-challenge__container .shopify-challenge__button {
  margin-top: 30px;
}
.slideshow {
  --slideshow-min-height: 0;

  display: block;
  position: relative;
}

.slideshow--small {
  --slideshow-min-height: 120vw;
}

.slideshow--medium {
  --slideshow-min-height: 133vw;
}

.slideshow--large {
  --slideshow-min-height: 160vw;
}

.slideshow--fit {
  --slideshow-min-height: calc(var(--window-height) - var(--header-height, 0px) * (-1 * (var(--enable-transparent-header) - 1)) - var(--announcement-bar-height, 0px));
}

.slideshow
.slideshow__slide-list,
.slideshow__slide,
.slideshow__slide-inner {
  min-height: var(--slideshow-min-height);
}

.slideshow__slide {
  position: relative;
  display: block;
  z-index: 1;
}

.slideshow__slide:not(:only-child) {
  cursor: grab;
  -webkit-user-select: none;
          user-select: none;
}

.slideshow__slide[hidden] {
  position: absolute;
  visibility: hidden;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 0;
}

.slideshow__slide:not(:only-child) .slideshow__text-wrapper--bottom {
  padding-bottom: calc(var(--vertical-breather) + 30px);
}

.slideshow__slide-inner {
  position: relative;
  display: flex;
  align-items: var(--section-blocks-alignment);
  width: 100%;
  height: 100%;
}

.slideshow__slide-inner::before {
  content: '';
  display: block;
  padding-bottom: calc(100.0 / var(--mobile-image-aspect-ratio) * 1%);
  width: 0;
}

.slideshow__image-wrapper {
  overflow: hidden;
}

[dir="ltr"] .slideshow__image-wrapper,[dir="ltr"]
.slideshow__image {
  left: 0;
}

[dir="rtl"] .slideshow__image-wrapper,[dir="rtl"]
.slideshow__image {
  right: 0;
}

.slideshow__image-wrapper,
.slideshow__image {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
}

.slideshow__image {
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
}

.slideshow__image--placeholder {
  width: auto;
  background: rgb(var(--secondary-background));
}

[dir="ltr"] .slideshow__image-wrapper::before {
  left: 0;
}

[dir="rtl"] .slideshow__image-wrapper::before {
  right: 0;
}

.slideshow__image-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(var(--section-blocks-overlay-color), var(--section-blocks-overlay-opacity));
  pointer-events: none;
  z-index: 1;
}

/* When the text is aligned at the top, we should add extra space if the header is transparent to avoid it to collide */
@supports (width: max(1px, 2px)) {
  .slideshow__text-wrapper--top {
    padding-top: max(var(--vertical-breather), calc((var(--header-height) + 25px) * var(--enable-transparent-header)));
  }
}

@media screen and (min-width: 1000px) {
  @supports (width: max(1px, 2px)) {
    .slideshow__text-wrapper--top {
      padding-top: max(var(--vertical-breather), calc((var(--header-height) + 40px) * var(--enable-transparent-header)));
    }
  }
}

/* Navigation (progress bar) */

@keyframes slideshowProgressBarAnimation {
  from {
    transform: scaleX(0);
  }

  to {
    transform: scaleX(1);
  }
}

.slideshow__nav {
  display: flex;
  position: absolute;
  justify-content: center;
  bottom: 15px;
  left: 0;
  right: 0;
  z-index: 1;
  pointer-events: none;
}

.slideshow__progress-bar {
  position: relative;
  flex-basis: 48px;
  padding: 18px 0;
  margin: 0 8px;
  pointer-events: auto;
}

[dir="ltr"] .slideshow__progress-bar::before,[dir="ltr"]
.slideshow__progress-bar::after {
  left: 0;
}

[dir="rtl"] .slideshow__progress-bar::before,[dir="rtl"]
.slideshow__progress-bar::after {
  right: 0;
}

.slideshow__progress-bar::before,
.slideshow__progress-bar::after {
  position: absolute;
  content: '';
  height: 2px;
  width: 100%;
  top: calc(50% - 1px);
  background: rgba(var(--progress-bar-color), 0.5);
  transition: background 0.2s ease-in-out;
}

.slideshow__progress-bar::after {
  transform-origin: var(--transform-origin-start);
  transform: scaleX(0);
  background: rgb(var(--progress-bar-color));
}

.slideshow__progress-bar[aria-current="true"]::after {
  animation: slideshowProgressBarAnimation var(--section-autoplay-duration) linear;
  animation-play-state: var(--section-animation-play-state, paused);
  animation-fill-mode: forwards;
}

@media screen and (min-width: 741px) {
  .slideshow--small {
    --slideshow-min-height: 70vw;
  }

  .slideshow--medium {
    --slideshow-min-height: 90vw;
  }

  .slideshow--large {
    --slideshow-min-height: 105vw;
  }
}

@media screen and (min-width: 1000px) {
  .slideshow--small {
    --slideshow-min-height: 42vw;
  }

  .slideshow--medium {
    --slideshow-min-height: 48vw;
  }

  .slideshow--large {
    --slideshow-min-height: 54vw;
  }

  .slideshow__slide--split .slideshow__image-wrapper {
    width: 50%;
  }

  [dir="ltr"] .slideshow__slide--split .slideshow__image-wrapper--secondary {
    left: calc(50% - 1px);
  }

  [dir="rtl"] .slideshow__slide--split .slideshow__image-wrapper--secondary {
    right: calc(50% - 1px);
  }

  .slideshow__slide--split .slideshow__image-wrapper--secondary { /* The -1px allows to take into account the potential approximation rounding that browsers do */
    width: calc(50% + 1px);
  }

  .slideshow__slide-inner::before {
    padding-bottom: calc(100.0 / var(--image-aspect-ratio) * 1%);
  }

  /* Navigation (progress bar) */

  .slideshow__nav {
    bottom: 23px;
  }

  .slideshow__progress-bar {
    flex-basis: 64px;
  }
}

@media screen and (min-width: 1200px) {
  .slideshow--small {
    --slideshow-min-height: 38vw;
  }

  .slideshow--medium {
    --slideshow-min-height: 40vw;
  }

  .slideshow--large {
    --slideshow-min-height: 48vw;
  }
}
.store-availability-container:not(:first-child) {
  margin-top: 8px;
}

.store-availability-information,
.store-availability-list__stock {
  display: flex;
  align-items: center;
}

.store-availability-information {
  align-items: baseline;
}

.store-availability-information .icon--store-availability-out-of-stock {
  position: relative;
  top: 1px;
}

[dir="ltr"] .store-availability-information-container {
  margin-left: 8px;
}

[dir="rtl"] .store-availability-information-container {
  margin-right: 8px;
}

.store-availability-information__title,
.store-availability-information__link {
  display: block;
}

.store-availability-information__title {
  margin-bottom: -2px;
}

.store-availability-information__link {
  margin-top: 10px;
}

/* MODAL */

.store-availabilities-modal__product-title {
  max-width: 85%;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  margin: 0;
}

.store-availabilities-modal__variant-title {
  margin-top: 6px;
}

.store-availabilities-list {
  margin-top: 10px;
}

.store-availability-list__item {
  padding-top: 24px;
}

.store-availability-list__item + .store-availability-list__item {
  border-top: 1px solid rgb(var(--border-color));
  margin-top: 22px;
}

.store-availability-list__location {
  margin-bottom: 2px;
}

[dir="ltr"] .store-availability-list__stock svg {
  margin-right: 8px;
}

[dir="rtl"] .store-availability-list__stock svg {
  margin-left: 8px;
}

.store-availability-list__contact {
  margin-top: 8px;
}

.store-availability-list__contact p {
  margin-bottom: 0; /* Remove the margin of the formatted address by Shopify */
}
.testimonial-list {
  display: block;
  max-width: 580px;
  margin-left: auto;
  margin-right: auto;
}

.testimonial__author,
.testimonial-list__nav {
  padding: 0 24px; /* The blockquote has a natural spacing that we have to add on other elements */
}

.testimonial-list__wrapper {
  display: flex;
  flex-wrap: nowrap;
  overflow: hidden;
}

.testimonial {
  display: block;
  flex-shrink: 0;
  width: 100%;
  order: 0;
  will-change: transform;
}

.testimonial[hidden] {
  visibility: hidden;
  order: 1;
}

.testimonial:not(:only-child) {
  cursor: grab;
  -webkit-user-select: none;
          user-select: none;
}

.testimonial__content {
  margin: 0;
}

.testimonial__author {
  margin-top: 32px;
}

.testimonial-list__nav {
  margin-top: 40px;
}

@media screen and (min-width: 741px) {
  [dir="ltr"] .testimonial__author,[dir="ltr"]
  .testimonial-list__nav {
    padding: 0 0 0 49px;
  }
  [dir="rtl"] .testimonial__author,[dir="rtl"]
  .testimonial-list__nav {
    padding: 0 49px 0 0;
  }
  .testimonial__author,
  .testimonial-list__nav { /* The blockquote has a natural spacing that we have to add on other elements */
    margin-top: 32px;
  }
}

@media screen and (min-width: 1000px) {
  .testimonial-list {
    max-width: 690px;
  }
}

@media screen and (min-width: 1200px) {
  .testimonial-list {
    max-width: 875px;
  }

  [dir="ltr"] .testimonial__author,[dir="ltr"]
  .testimonial-list__nav {
    padding: 0 0 0 69px;
  }

  [dir="rtl"] .testimonial__author,[dir="rtl"]
  .testimonial-list__nav {
    padding: 0 69px 0 0;
  }

  .testimonial__author,
  .testimonial-list__nav { /* The blockquote has a natural spacing that we have to add on other elements */
    margin-top: 40px;
  }
}
.text-with-icons {
  display: block;
}

.text-with-icons__list {
  display: flex;
  scroll-snap-type: x mandatory;
  margin-left: calc(-1 * var(--container-gutter));
  margin-right: calc(-1 * var(--container-gutter));
}

.text-with-icons__item {
  display: block;
  text-align: center;
  padding: 0 48px;
  width: 100%;
  flex: none;
  scroll-snap-align: center;
  scroll-snap-stop: always;
}

.text-with-icons__icon-wrapper {
  margin-bottom: 16px;
}

.text-with-icons__custom-icon {
  display: block;
  max-width: 24px;
}

.text-with-icons__icon-wrapper > * {
  margin: 0 auto;
}

.text-with-icons__dots {
  margin-top: 26px;
}

@media screen and (min-width: 1000px) {
  .text-with-icons__list {
    display: grid;
    grid-gap: 48px;
    gap: 48px;
    grid-auto-flow: column;
    grid-auto-columns: minmax(200px, 400px);
    justify-content: center;
    margin-left: 0;
    margin-right: 0;
  }

  .text-with-icons__item {
    padding: 0; /* Spacing is already added by the grid */
  }

  .text-with-icons__content-wrapper .heading + p {
    margin-top: 16px; /* We have a slightly different spacing here */
  }
}
.timeline__inner {
  position: relative;
}

.timeline__list-wrapper {
  display: block;
}

.timeline__list {
  position: relative;
  display: grid;
  grid-auto-flow: column;
  align-items: center;
  justify-content: safe center;
  min-width: min-content;
}

.timeline__item {
  color: rgb(var(--text-color));
}

.timeline__content {
  padding: 40px;
  background: rgb(var(--section-box-background));
}

.timeline__image.placeholder-background {
  fill: rgb(var(--section-background));
  background: rgb(var(--text-color));
}

.timeline__nav-wrapper {
  margin-top: 40px;
}

.timeline__nav {
  position: relative;
  display: grid;
  grid-template-columns: repeat(var(--section-items-count), minmax(0, 1fr));
  align-items: start;
  padding-top: 18px;
}

[dir="ltr"] .timeline__nav-item {
  padding-right: 48px;
}

[dir="rtl"] .timeline__nav-item {
  padding-left: 48px;
}

.timeline__nav-item {
  opacity: 0.7;
  transition: opacity 0.2s ease-in-out;
}

[dir="ltr"] .timeline__nav-item:last-child {
  padding-right: 0;
}

[dir="rtl"] .timeline__nav-item:last-child {
  padding-left: 0;
}

.timeline__nav-item[aria-current="true"] {
  opacity: 1;
}

[dir="ltr"] .timeline__progress-bar {
  left: 0;
}

[dir="rtl"] .timeline__progress-bar {
  right: 0;
}

.timeline__progress-bar {
  position: absolute;
  top: 0;
  width: 100%;
}

.timeline__progress-bar::before {
  transition: transform 0.3s ease-in-out;
}

[dir="ltr"] .timeline__prev-next-buttons {
  right: calc(var(--container-outer-width) - 28px);
}

[dir="rtl"] .timeline__prev-next-buttons {
  left: calc(var(--container-outer-width) - 28px);
}

.timeline__prev-next-buttons {
  position: absolute;
  top: calc(50% - 56px); /* 56px is the height of a single button */ /* 28px is half the width of button */
  z-index: 1;
}

@media screen and (max-width: 999px) {
  /* On mobile and tablet we have a very special layout and scrolling pattern. What happens is that by default
     images appear "static", and when scrolling the content goes "on top" of the image */
  .timeline {
    --timeline-image-max-width: 70vw;
    --timeline-content-max-width: 79vw;
  }

  .timeline__list-wrapper {
    scroll-snap-type: x mandatory;
  }

  .timeline__list-wrapper .container {
    padding-left: 0;
    padding-right: 0;
  }

  .timeline__list {
    padding-left: calc((100vw - var(--timeline-image-max-width)) / 2);
    padding-right: calc((100vw - var(--timeline-image-max-width)) / 2);
  }

  .timeline__item {
    display: grid;
    grid-template-columns: var(--timeline-image-max-width) var(--timeline-content-max-width);
    align-items: center;
  }

  [dir="ltr"] .timeline__item:not(:last-child) {
    padding-right: calc((100vw - var(--timeline-image-max-width)));
  }

  [dir="rtl"] .timeline__item:not(:last-child) {
    padding-left: calc((100vw - var(--timeline-image-max-width)));
  }

  [dir="ltr"] .timeline__image-wrapper {
    left: calc((100vw - var(--timeline-image-max-width)) / 2);
  }

  [dir="rtl"] .timeline__image-wrapper {
    right: calc((100vw - var(--timeline-image-max-width)) / 2);
  }

  .timeline__image-wrapper {
    position: -webkit-sticky;
    position: sticky;
  }

  .timeline__image {
    border-radius: var(--block-border-radius);
    width: 100%;
  }

  .timeline__content-wrapper {
    max-width: var(--timeline-content-max-width);
    box-shadow: -10px 0 30px 10px rgba(var(--section-background), 0.2);
    border-radius: var(--block-border-radius);
    z-index: 1;
  }

  [dir="ltr"] .timeline__content {
    margin-right: calc(-1 * (var(--timeline-content-max-width) - var(--timeline-image-max-width)));
  }

  [dir="rtl"] .timeline__content {
    margin-left: calc(-1 * (var(--timeline-content-max-width) - var(--timeline-image-max-width)));
  }

  .timeline__content {
    position: relative;
    border-radius: var(--block-border-radius);
  }

  [dir="ltr"] .timeline__content::before {
    right: 100%;
  }

  [dir="rtl"] .timeline__content::before {
    left: 100%;
  }

  .timeline__content::before {
    content: '';
    position: absolute;
    width: var(--timeline-image-max-width);
    height: 100%;
    top: 0;
    pointer-events: none;
  }

  .timeline__content,
  .timeline__content::before {
    scroll-snap-align: center;
    scroll-snap-stop: always;
  }

  .timeline__nav-wrapper {
    margin-left: calc(-1 * var(--container-gutter));
    margin-right: calc(-1 * var(--container-gutter));
  }

  .timeline__nav-scroller {
    width: max-content;
    min-width: 100%;
  }

  .timeline__nav {
    margin-left: 24px;
    margin-right: 24px;
  }

  .timeline__nav-item {
    max-width: 190px;
  }
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .timeline {
    --timeline-image-max-width: 50vw;
    --timeline-content-max-width: 54vw;
  }
}

@media screen and (min-width: 1000px) {
  .timeline__list {
    align-items: stretch;
  }

  .timeline__list-wrapper {
    overflow: hidden;
  }

  .timeline__item {
    display: flex;
    flex: none;
    width: calc(var(--grid-column-width) * 13 + var(--grid-gap) * 12);
    transition: opacity 0.25s ease-in-out;
    background: rgb(var(--section-box-background));
    border-radius: var(--block-border-radius);
    overflow: hidden;
    z-index: 0; /* Creating new stacking context is needed on Safari to apply the border radius */
  }

  .timeline__item[hidden] {
    opacity: 0.2;
    will-change: opacity;
  }

  [dir="ltr"] .timeline__item:not(:last-child) {
    margin-right: calc(var(--grid-column-width) + var(--grid-gap));
  }

  [dir="rtl"] .timeline__item:not(:last-child) {
    margin-left: calc(var(--grid-column-width) + var(--grid-gap));
  }

  .timeline__item:last-child:not(:only-child) {
    margin-right: var(--container-outer-width);
  }

  .timeline__content-wrapper {
    align-self: center;
  }

  .timeline__content-wrapper--top {
    align-self: flex-start;
  }

  .timeline__content-wrapper--bottom {
    align-self: flex-end;
  }

  .timeline__image-wrapper,
  .timeline__content-wrapper {
    flex: none;
    width: 50%;
  }

  .timeline__image-wrapper {
    min-height: 100%;
  }

  .timeline__image {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
       object-fit: cover;
    -o-object-position: center;
       object-position: center;
  }

  [dir="ltr"] .timeline__nav-item {
    padding-right: 20px;
  }

  [dir="rtl"] .timeline__nav-item {
    padding-left: 20px;
  }
}

@media screen and (min-width: 1400px) {
  .timeline__content {
    padding: 64px;
  }
}
/* For the boxed variation, the size setting controls the width */

.video-section--boxed {
  margin-left: auto;
  margin-right: auto;
}

.video-section--boxed.video-section--small {
  max-width: 800px;
}

.video-section--boxed.video-section--medium {
  max-width: 1000px;
}

.video-section--boxed.video-section--large {
  max-width: 1200px;
}

/* For the full-width, the size setting controls the height */

.video-section--full .video-wrapper {
  --video-width: 100vw;
  height: var(--video-height);
}

.video-section--full.video-section--small {
  --video-height: 250px;
}

.video-section--full.video-section--medium {
  --video-height: 350px;
}

.video-section--full.video-section--large {
  --video-height: 450px;
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .video-section--full.video-section--small {
    --video-height: 300px;
  }

  .video-section--full.video-section--medium {
    --video-height: 375px;
  }

  .video-section--full.video-section--large {
    --video-height: 425px;
  }
}

@media screen and (min-width: 1000px) and (max-width: 1199px) {
  .video-section--full.video-section--small {
    --video-height: 400px;
  }

  .video-section--full.video-section--medium {
    --video-height: 500px;
  }

  .video-section--full.video-section--large {
    --video-height: 570px;
  }
}

@media screen and (min-width: 1200px) {
  .video-section--full.video-section--small {
    --video-height: 600px;
  }

  .video-section--full.video-section--medium {
    --video-height: 700px;
  }

  .video-section--full.video-section--large {
    --video-height: 800px;
  }
}
