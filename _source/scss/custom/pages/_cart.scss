body.template-cart {

  /* ----- Shipping Bar ----- */

  .shipping-bar {
    
    .shipping-bar__progress {
      box-shadow: 0 0 0 1px var(---color--tertiary) inset;
    }

  }

  .cart-header {}

  .cart-body {

    background: var(---background-color--content-1);
    padding: calc(var(--vertical-breather) / 2) 0;

    .page-content {
      margin-bottom: 0;
    }

    .cart__recap {
      background: var(---background-color--tertiary);
    }

  }

  /* ----- Terms ----- */

  .terms {
    
    margin: 12px 0;
    padding: 12px 0;

    border-top: 1px solid #e6e6e6;
    border-bottom: 1px solid #e6e6e6;

  }

  .terms__checkbox {

    margin-top: 12px;
    display: flex;
    align-items: center;

  }

  #cart-order-terms {
    flex: 1 0 auto;
  }

}
