body.template-article {

  --secondary-background: var(---background-color--content-2--rgb);
  --section-background: var(---background-color--content-1--rgb);
  --section-block-background: var(---background-color--content-1--rgb);
  
  background: RGB(var(--section-background));

  .article__nav {
    --text-color: var(---color--primary--rgb);

    &:before {
      content: '';
      display: block;
      position: absolute;
      height: 3px;
      bottom: -1px;
      width: 100%;
      background: currentColor;
      color: RGB(var(--text-color));
      opacity: 0.2;
    }

    &:after {
      color: RGB(var(--text-color));
    }

    .article__nav-item {
      --text-color: var(---color--primary--rgb);
      color: RGB(var(--text-color));
    }

  }

  .article__prev-next {
    background: transparent;
  }

  .article__nav {
    background: var(---background-color--content-1);
    z-index: 2;
  }

  .article__nav-item-label,
  .article__nav-item-title,
  .article__reading-time {
    color: var(---color-text);
  }

  .article__info {
    @media screen and (min-width: 1400px) {
      width: 240px;
    }
  }

  .article__upsells {
    .product-item {
      margin-bottom: 30px;
    }
  }

  .article__comment-form {
    .button--primary {
      --button-background: var(---color--primary--rgb);
    }
  }

  @include respond-to($medium-down) {

    .breadcrumb--floating {
      position: static;
      text-align: center;

      .breadcrumb__list {
        padding: 1.5em 0;
      }
    }

    .article__tags,
    .article__meta {
      margin-bottom: 10px;
      display: grid;
      grid-gap: 4px;
      gap: 6px;
      justify-content: flex-start;
    }

    .article__meta-item {
      &:before {
        content: none !important;
      }
    }
  
  }

}