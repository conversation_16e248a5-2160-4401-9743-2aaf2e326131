.section.custom-testimonials-slider {

  overflow: hidden;

  .gallery__item {

    flex-direction: column;
    flex: 1 0 80%;

    background: RGB(var(--block-background));
    border-radius: var(--block-border-radius);
    color: RGB(var(--text-color));

    @include respond-to($small-up) {
      flex: 1 0 60%;
      flex-direction: row;
    }

    @include respond-to($medium-up) {
      flex: 1 0 70%;
    }

    @include respond-to($large-up) {
      flex: 1 0 50%;
      max-width: 800px;
    }

  }

  .gallery__figure {

    display: flex;
    flex-direction: row;
    height: 100%;

    border-radius: var(--block-border-radius);
    overflow: hidden;

    @include respond-to($medium-down) {
      flex-direction: column;
    }

  }

  .gallery__image {
    border-radius: unset;
    flex: 1 1 50%;
    height: 100%;
    border-radius: 0 !important;
    object-fit: cover;

    @include respond-to($medium-down) {
      max-height: 40vh;
    }

  }

  .gallery__caption {

    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: var(--container-gutter);
    flex: 1;

    margin-top: 0;
    padding: var(--container-gutter);

    border-radius: 0;

    .gallery__caption-name {
      margin-bottom: 0;
    }

    @include respond-to($medium-up) {
      height: 100%;
      flex: 1 1 50%;
    }

  }

  .gallery__caption-top {
    display: flex;
    flex-direction: column;
    gap: 20px;

    > * {
      margin: 0;
    }
  }

  .gallery__caption-rating {
    display: flex;
    gap: 4px;

    svg {
      width: 12px;
      height: 12px;
      * {
        stroke: RGB(var(--star-color));
        fill: RGB(var(--star-color));
      }
    }

    @include respond-to($medium-up) {
      svg {
        width: 16px;
        height: 16px;
      }
    }
  }

}