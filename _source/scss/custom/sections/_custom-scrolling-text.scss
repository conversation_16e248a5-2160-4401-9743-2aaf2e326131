
:root {
  
  --scrolling-text-direction: 1;

  --scrolling-text-font-size: 18px;
  --scrolling-text-small-font-size: 12px;
  --scrolling-text-large-font-size: 24px;

  @media screen and (min-width: 741px) {

    --scrolling-text-font-size: 24px;
    --scrolling-text-small-font-size: 18px;
    --scrolling-text-large-font-size: 40px;

  }
  
}

.scrolling-text {
  
  // --background: 0,0,0;
  // --text-color: 0,0,0;
  
  --text-font-size: var(--scrolling-text-font-size);

  display: flex;
  justify-content: center;
  overflow: hidden;

  padding-block: 1.8em;

  color: RGB(var(--text-color));
  background-color: RGB(var(--background));

  &.scrolling-text--font-large {
    --text-font-size: var(--scrolling-text-large-font-size);
  }

  &.scrolling-text--font-small {
    --text-font-size: var(--scrolling-text-small-font-size);
  }

  &.scrolling-text--inner-shadow {
    box-shadow: var(--section-shadow-inner);
  }

  &.scrolling-text--text-shadow {
    text-shadow: 0px 0px 10px rgba(0, 0, 0, 0.05);
  }

  // BRAND

  .scrolling-text__text {
    p {
      font-weight: 500;
    }
  }
  
}

.scrolling-text__wrapper {
  display: grid;
}

.scrolling-text__text {
  
  padding-inline-start: min(1em, 2rem);
  padding-inline-end: min(1em, 2rem);

  font-size: var(--text-font-size);

  > * {
  line-height: 1.6;
  }

}

@supports (overflow: clip) {
  .scrolling-text {
    overflow: clip visible;
  }

  .scrolling-text__text {
    line-height: 1;
  }
}

@media screen and (min-width: 700px) {
  .scrolling-text__text {
    padding-inline-start: min(1.5em, 4rem);
    padding-inline-end: min(1.5em, 4rem);
  }
}

@media (prefers-reduced-motion: no-preference) {
  .scrolling-text__wrapper {
    grid: auto / auto-flow max-content;
  }

  .scrolling-text--auto {
    &.scrolling-text--backwards .scrolling-text__text {
      animation: marquee var(--marquee-animation-duration, 0s) linear infinite;
    }
    &.scrolling-text--forwards .scrolling-text__text {
      animation: marquee-reverse var(--marquee-animation-duration, 0s) linear infinite;
    }
  }

  .scrolling-text--backwards {
    transform-origin: center left;
    .scrolling-text__text  {
        animation-direction: reverse !important;
    }
  }

  .scrolling-text--forwards {
    transform-origin: center right;
  }

  .scrolling-text--scroll .scrolling-text__wrapper {
    transform: translateX(calc(var(--scrolling-text-direction) * (50vw - 10% * var(--visibility-progress, 0))));
    min-width: min-content;
    transition: transform 50ms linear;
  }
}

@media (prefers-reduced-motion: reduce) {

  .scrolling-text__wrapper {
    text-align: center;
    justify-content: center;
  }

}

@keyframes marquee {
  from {
    transform: translateX(0);
  }
  to {
    // transform: translateX(100%);
    transform: translateX(calc(100% * var(--scrolling-text-direction)));
  }
}

@keyframes marquee-reverse {
  from {
    // transform: translateX(-100%);
    transform: translateX(calc(100% * var(--scrolling-text-direction)));
  }
  to {
    transform: translateX(0);
  }
}