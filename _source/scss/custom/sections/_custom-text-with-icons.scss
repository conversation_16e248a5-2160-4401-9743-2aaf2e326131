.section.custom-text-with-icons {

  .text-with-icons__item {
    .text-with-icons__content-wrapper {
      max-width: 75%;
      margin: auto;
    }
  }

  .text-with-icons__icon-wrapper {
    margin-bottom: 0;
  }

  .heading.heading--small {
    margin: 0 0 6px;
  }

  .text-with-icons__icon-wrapper {
    svg {
      * {
        stroke: RGB(var(--icon-color));
      }
    }
  }

  .text-with-icons__description {

    p {
      margin-top: 10px;
      font-size: var(---font-size-body-small--desktop); 
    }

  }


}