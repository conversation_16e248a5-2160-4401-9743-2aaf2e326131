.bespoke-faq-videos {

  @include respond-to($medium-up) {
    margin-left: calc(var(--grid-column-width) + var(--grid-gap));
    margin-right: calc(var(--grid-column-width) + var(--grid-gap));
  }

  .section__color-wrapper {
    overflow: hidden;
  }

  .faq-videos__inner {
    display: flex;

    @media (max-width: 1000px) {
      flex-direction: column;
    }
  }

  .faq-videos__playback {

    min-width: 40%;
    
    background: var(---color--brand-7);

    @include respond-to($medium-up) {
      max-width: 40%;
    }

    native-video {
      width: 100%;
      height: 100%;

      img,
      svg {
        cursor: pointer;
        vertical-align: top;
        object-fit: cover;
        height: 100%;
      }
    }

  }

  .faq-videos__content {
    width: 100%;
  }

  .faq-videos__content-inner {
    max-width: 600px;
    margin: auto;
    padding: var(--vertical-breather);
  }

  .faq-videos-list {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 30px 50px;
  }

  .native-video--stopped {

    &:before {

      content: '';

      display: block;
      width: 60px;
      height: 60px;

      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      margin: auto;

      z-index: 1;

      transform: scale(1);
      transition: transform 0.25s;

      background-image: url("data:image/svg+xml,%3Csvg width='58' height='58' viewBox='0 0 58 58' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M28.9987 53.1667C42.3456 53.1667 53.1654 42.3469 53.1654 29C53.1654 15.6532 42.3456 4.83337 28.9987 4.83337C15.6518 4.83337 4.83203 15.6532 4.83203 29C4.83203 42.3469 15.6518 53.1667 28.9987 53.1667Z' stroke='white' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M24.168 19.3334L38.668 29L24.168 38.6667V19.3334Z' stroke='white' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");

      pointer-events: none;

    }

    &:after {

      position: absolute;
      display: block;
      content: '';
      top: 0;
      left: 0;
      height: 100%;
      width: 100%;
      background: rgba(0, 0, 0, .1);

      // backdrop-filter: blur(3px);

      transition: 0.25s background;

    }

    &:hover {
      &:before {
        transform: scale(1.2);
      }

      &:after {
        background: rgba(0, 0, 0, .3);
      }
    }

  }

}

.faq-video {

  &:focus,
  &:hover {

    .faq-video__image {
      .faq-video__image-overlay {
        opacity: 1;
      }
    }

  }

}

.faq-video__image {

  position: relative;
  overflow: hidden;
  border-radius: var(--block-border-radius);
  overflow: hidden;


  img,
  svg {

    vertical-align: top;

  }

  .faq-video__image-overlay {

    content: "";

    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.2);

    opacity: 0;
    transition: opacity 0.25s;

    display: flex;
    justify-content: center;
    align-items: center;

    svg {
      width: 30px;
      height: 30px;
    }

  }

}

.faq-video__text {
  margin-top: 0.5em;
}