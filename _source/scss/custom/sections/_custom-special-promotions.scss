.section.custom-featured-promotions {

  .special-promotions__list {
    
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: var(--container-gutter);

    > native-carousel-item {
      display: block;
    }

    @include respond-to($medium-down) {
      display: flex;
      scroll-snap-type: x mandatory;
      margin-left: calc(-1 * var(--container-gutter));
      margin-right: calc(-1 * var(--container-gutter));

      >native-carousel-item {
        display: block;
        padding: 0 var(--container-gutter);
        width: 100%;
        flex: none;
        scroll-snap-align: center;
        scroll-snap-stop: always;
      }
    }

  }

  .special-promotion-item {

    display: block;
    background: #FFFFFF;
    flex: 1 0 30%;
    margin: 0;
    border: 1px solid var(---color-line);
    border-radius: var(--block-border-radius);
    overflow: hidden;

    box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.04);

    img {
      object-fit: cover;
    }

  }

  .special-promotion-item__caption {
    padding: 30px;
  }

  .special-promotion-item__figure {
    margin: -1px;
  }

  .special-promotion-item__figure-inner {
    overflow: hidden;
  }

  .special-promotion-item__title {
    margin-bottom: 8px;
    text-align: left;
  }

  .special-promotion-item__icon-wrapper {
    display: flex;
    align-items: center;
    gap: 15px;
    height: 40px;
  }

  .special-promotion-item__icon-title {
    margin-bottom: 0;
    color: var(---color-heading-3);
  }

  .special-promotion-item__icon-image {
    height: 28px;
    width: 32px;
    @include respond-to($medium-up) {
      height: 40px;
    }
    img {
      margin: 0;
    }
  }

  a.special-promotion-item {
    .special-promotion-item__figure {
      position: relative;
    }
  }

}