#mobile-menu-drawer {

  // background-color: RGB(var(--background-2)) !important;

  .linklist__item-swatch {
    display: inline-block;
    height: 18px;
    width: 18px;
    border-radius: 100%;
  }

  .linklist__item>a {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  // Drawers

  .drawer__header {
    display: flex;
    align-items: center;
    gap: 20px;
    border-bottom: 1px solid rgba(var(--text-color), .15);
  }

  .drawer__logo-link {
    display: flex;
    align-items: center;
    max-width: 150px;
    color: RGB(var(--logo-color));

    img,
    svg {
      width: 100%;
    }

    svg {
      * {
        fill: currentColor;
      }
    }
  }

  // Links

  .mobile-nav__link-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .mobile-nav__image-push {
    .mobile-nav__image-heading {
      --heading-color: var(---color--primary--rgb)
    }
  }

  .mobile-nav__link.color-link {
    justify-content: start;
  }

  .mobile-nav__link-inner {

    display: flex;
    align-items: center;
    gap: 0.5em;

  }

  .mobile-nav__link-inner-icon {
    
    opacity: 0.5;
    display: flex;
    align-items: center;

  }

  .mobile-nav__link-inner-text {

  }

  // Secondary Menu

  .mobile-secondary-menu {

    .mobile-secondary_menu__item {
      border: none !important;
      font-size: var(---font-size-body--mobile);
    }
  
    .mobile-secondary_menu__link {
      padding: 8px 0;
    }

  }

}


.sub-brand-slider {

  padding: 20px 0;
  margin: 20px calc(-1 * var(--container-gutter));
  background-color: RGB(var(--background-2));

  border-top: 1px solid rgba(var(--text-color), .15);
  border-bottom: 1px solid rgba(var(--text-color), .15);

  .sub-brand-slider__title {
    margin-bottom: 0.5em;
  }

  .sub-brand-slider__images-wrapper {
    align-items: flex-start;
    gap: 12px;
    grid-auto-flow: column;
    display: grid;
  }

  .sub-brand-slider__images-scroller {
    padding: 6px 0;
    padding-inline: var(--container-gutter);
    grid-auto-flow: column;
    gap: 12px;
    width: -moz-fit-content;
    width: fit-content;
    display: grid;
  }

  .sub-brand-slider-logo {

    --transition-duration: 0.25s;

    position: relative;
    display: inline-flex;
    min-width: 220px;
    max-width: 220px;
    padding: 10px 25px;

    // background-color: RGB(var(--background-color--content-1--rgb));
    background-color: #fff;
    box-shadow: var(---shadow--section);
    border-radius: var(--block-border-radius);

    transition: var(--transition-duration) background-color;

    img,
    svg {
      display: block;
      width: 100%;
    }

    svg {
      * {
        fill: RGB(var(--logo-color));
      }
    }

    .sub-brand-slider-logo__inner {
      display: flex;
      align-items: center;
      opacity: 0.5;
      transition: var(--transition-duration) opacity;
    }

    &:hover,
    &.sub-brand-slider-logo--active {
      .sub-brand-slider-logo__inner {
        opacity: 1;
      }
    }

    &.sub-brand-slider-logo--active {}

  }

}