.section {

  &.section--use-padding {
    margin: 0;
    padding: var(--vertical-breather) 0;
  }

  &.section--half-padding {
    --vertical-breather: calc(var(--vertical-breather) / 2);
  }

  &.section--extra-padding {
    --vertical-breather: var(--vertical-breather-large);
  }
  
  &.section--no-padding {
    margin: 0;
    padding: 0;
  }
  
  &.section--show-shadow {
    box-shadow: 0 0 10px rgba(0,0,0,.1) inset;
  }
  
  &.section--contain-and-clip {
    position: relative;
    overflow: hidden;
  }

  &.section--inner-shadow {
    .section__shadow-wrapper {
      box-shadow: inset 0px 0px 10px rgba(0, 0, 0, 0.1);
    }
  }

  .container {

    // margin-top: 0;
    // margin-bottom: 0;
    // padding-top: var(--vertical-breather);
    // padding-bottom: var(--vertical-breather);

  }

  .container--no-horizontal-padding--mobile {

    @include respond-to($small-down) {
      padding-left: 0;
      padding-right: 0;
    }

    // margin-top: 0;
    // margin-bottom: 0;
    // padding-top: var(--vertical-breather);
    // padding-bottom: var(--vertical-breather);

  }
  
  &.section--no-spacing {
    // margin-top: 0;
    // margin-bottom: 0;
    // padding-top: 0;
    // padding-bottom: 0;
  }

  &.section--no-spacing--top {
    // margin-top: 0;
    // padding-top: 0;
  }

  .subheading {
    + .heading {
      
    }
  }

  .section__header,
  .section__footer {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1em;
    > * {
      margin: 0 !important;
    }
  }

  .text-container {
    .subheading,
    .heading--small {
      --heading-color: var(---color-heading-3--rgb);
    }
  }

  .horizontal-header {
    
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--vertical-breather-tight);

    @include respond-to($small-down) {
      padding-bottom: 20px;
      border-bottom: 1px solid var(---color-line);
    }

    &.horizontal-header--tabs {
      
      border-bottom: 1px solid var(---color-line);
      padding-bottom: 0;

      .tabs-nav {
        margin-bottom: 0;
        margin-top: auto;
      }

      .tabs-nav__item-list {
        box-shadow: none;
      }

      .text-container {
        .h3 {
          margin-bottom: 10px !important;
        }
      }

      @include respond-to($small-down) {
          
        flex-direction: column;
        border-bottom: 0;
        
        .heading {
          text-align: center;
        }
      
        .tabs-nav {
          margin-top: 20px;
        }
      
        .tabs-nav--edge2edge {
          border-bottom: 1px solid var(---color-line);
        }

      }

    }

    &.horizontal-header--equal {

      display: grid;
      align-items: flex-start;
      grid-template-columns: repeat(2, minmax(0, 1fr));
      gap: 50px;
      width: 100%;

      > * {
        width: 100%;
      }

      @media(max-width: 1000px) {
        display: flex;
        flex-direction: column;
        gap: 20px;
      }

    }

  }

}


.section-header-images {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
  .section-header-image {
    margin: 0;
  }
  img {
    max-width: 200px;
  }
}

.section-footer-images {
  
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;

  .section-footer-image {
    margin: 0;
    @include respond-to($small-down) {
      max-width: 30vw;
    }
  }

}


.section--show-texture {
  
  position: relative;

  &:before {

    content: '';
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    background: var(---pattern-wood);
    opacity: 0.15;

  }
}

.section--texture {
    
  .section__color-wrapper {

    background-blend-mode: multiply;

    &:after {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: var(---pattern-wood);
      opacity: 0.15;
      z-index: 0;
    }

    .container {
      position: relative;
      z-index: 1;
    }

  }

}

.section--image-border {
    
  .image-with-text__image {
      border: 5px solid #fff;
      box-shadow: 0 0 10px 5px rgba(0,0,0,.25);
  }
  
}

.image-with-text-block {
  .button-wrapper {
    margin-top: 20px;
  }
}

.vertical-breather.vertical-breather--extra-tight {
  padding-top: var(--container-gutter);
  padding-bottom: var(--container-gutter);
}


/* ----- Content Boxes ----- */

@include respond-to($medium-down) {
  
  .content-box--larger {
    margin-left: 24px;
    margin-right: 24px;
  }

}

@include respond-to($medium-up) {

  .content-box--larger {
    width: calc(var(--grid-column-width) * 17 + var(--grid-gap) * 16);
  }

}

@include respond-to($large-up) {

  .content-box--larger {
    width: calc(var(--grid-column-width) * 17 + var(--grid-gap) * 16);
  }

}