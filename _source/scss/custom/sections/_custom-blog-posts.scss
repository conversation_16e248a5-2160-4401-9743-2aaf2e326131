.section.custom-blog-posts {

  .article-list {
    margin: 0;
    @include respond-to($small-down) {
      gap: 10px;
    }
  }

  .article-item {

    @include respond-to($small-down) {

      display: flex;
      gap: 15px;

      .article-item__image-container {
        max-width: 120px;
        width: 100%;
      }

      .article-item__content {
        .heading {
          margin: 6px 0;
        }
      }

      .horizontal-header {
        padding-bottom: 20px;
        border-bottom: 1px solid var(---color-line);
      }

    }

  }

}