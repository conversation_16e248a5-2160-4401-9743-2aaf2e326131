.section.custom-video {

  .video-wrapper__poster-content {
    position: static;
  }

  .video-wrapper__poster-content .text-container {

    display: flex;

    position: absolute;
    z-index: 1;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    margin: auto;

    max-width: var(--container-max-width);
    margin-left: auto;
    margin-right: auto;
    padding: var(--container-gutter);

  }

  // Text Colours

  .text-container {
    .heading {
      margin: 10px 0;
    }
    
    .button-wrapper {
      margin-top: 30px;
    }
  }

  // Text Alignment

  .custom-video__content--text-left {
    text-align: left;
  }

  .custom-video__content--text-right {
    text-align: right;
  }

  .custom-video__content--text-center {
    text-align: center;
  }

  // Text Position

  .custom-video__content--top_left {
    .text-container {
      align-items: flex-start;
      justify-content: flex-start;
    }
  }

  .custom-video__content--top_center {
    .text-container {
      align-items: flex-start;
      justify-content: center;
    }
  }

  .custom-video__content--top_right {
    .text-container {
      align-items: flex-start;
      justify-content: flex-end;
    }
  }

  .custom-video__content--middle_left {
    .text-container {
      align-items: center;
      justify-content: flex-start;
    }
  }

  .custom-video__content--middle_center {
    .text-container {
      align-items: center;
      justify-content: center;
    }
  }

  .custom-video__content--middle_right {
    .text-container {
      align-items: center;
      justify-content: flex-end;
    }
  }

  .custom-video__content--bottom_left {
    .text-container {
      align-items: flex-end;
      justify-content: flex-start;
    }
  }

  .custom-video__content--bottom_center {
    .text-container {
      align-items: flex-end;
      justify-content: center;
    }
  }

  .custom-video__content--bottom_right {
    .text-container {
      align-items: flex-end;
      justify-content: flex-end;
    }
  }

}