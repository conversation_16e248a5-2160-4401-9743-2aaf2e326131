.section.custom-featured-collections {
  position: relative;
  
  .product-list__inner-wrapper {
    position: relative;
  }

  .product-list__header {
    margin-bottom: 40px;
  }

  .section__header {
    .button-wrapper {
      margin-top: 30px;
    }
  }

  &.custom-featured-collection--horizontal-layout {
  
    &.custom-featured-collection--border-bottom {
      
      .horizontal-layout-wrapper {
        padding-bottom: 50px;
        border-bottom: 1px solid var(---color-line);
      }

    }

    .horizontal-layout-wrapper {

      @include respond-to($medium-up) {

        display: flex;
        width: 100%;

        .featured-collections {
          --card-width: 330px;
          width: calc(var(--container-max-width) - var(--container-gutter) - var(--container-gutter) - var(--card-width)) !important;
        }

        .section__header {
          min-width: 330px;
          max-width: 330px;
          padding-right: 30px;
          text-align: left;
        }

        .product-list__inner {
          margin: 0;
        }

        .prev-next-button--next {
          position: relative;
          right: -20px;
        }

        .prev-next-button--prev {
          position: relative;
          left: -20px;
        }

        .product-list__inner--scroller {
          grid-auto-columns: 330px;
        }
        
      }

    }
    
  }
  
}