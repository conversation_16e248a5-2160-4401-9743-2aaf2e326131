.shopify-section--mini-cart {

  /* ----- Footer ----- */

  .mini-cart__drawer-footer {
    border: 0 !important;
    padding-top: 0;
  }

  /* ----- Cart Notice Message ----- */

  .cart-notice-message {
    display: flex;
    gap: 10px;
  }

  .cart-notice-message__icon {
    max-width: 30px;
  }

  /* ----- Gift Wrap ----- */

  .mini-cart__actions {
    padding-top: 1em;
    border-top: 1px solid var(---color-line);
  }
  
  #order-note-toggle {
    margin-left: auto;
  }

  .line-item {

    margin-top: 20px;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(---color-line);

    .price {
      white-space: nowrap;
    }

    /* ----- Line Items ----- */

    .line-item__info {
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .line-item__remove-button {
      margin-left: 0;
    }

    .line-item__image-wrapper {
      border: 1px solid var(---color-line);
      width: 150px;
      border-radius: 8px;
      overflow: hidden;
      @include respond-to($medium-down) {
        width: 85px;
      }
    }

    .line-item__content-wrapper {
      margin: 10px 0;
    }

    .product-item-meta__title {
      font-weight: 400;
      margin-bottom: 0;
    }

    .product-item-meta__badges {
      padding: 0;
    }

    .product-item-meta__property-list {
      margin-top: 0;
    }

    /* ----- Quantity Selectors ----- */

    .quantity-selector {
      margin-right: 10px;
    }

    .quantity-selector__button {
      background-color: var(---color--kyte-light-grey);
    }

    .quantity-selector__input,
    .quantity-selector__button {
      --quantity-selector-height: 45px;
      height: var(--quantity-selector-height);
      width: var(--quantity-selector-height);
    }

    // Unstyled

    .quantity-selector.quantity-selector--unstyled {
    
      padding: 0;
      border: 0;
    
      display: inline-flex;
      align-items: center;
      outline: none !important;
    
      &:before {
        content: "x";
      }
    
      .quantity-selector__input {
    
        --quantity-selector-height: auto;
    
        padding: 0;
        cursor: default;
    
      }
    
    }

    /* ----- Addons ----- */

    .cart-product-addons {
      display: flex;
      flex-direction: column;
      margin-top: 15px;
      gap: 15px;
    }

    .cart-product-addon {
      padding: 10px 20px;
      background: var(---background-color--content-2);
      border-radius: var(--block-border-radius);
    }

    .cart-product-addon__header {
      display: flex;
      align-items: center;
      gap: 1em;
    }

    .cart-product-addon__remove {
      font-size: var(---font-size-body-small--desktop);
    }

    .cart-product-addon__price {
      margin-left: auto;
    }

    .cart-product-addon-details {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      flex: 1 0 auto;

      margin-top: 10px;
      margin-bottom: 5px;
      
      @include respond-to($medium-up) {
        grid: minmax(0, 1fr) / auto-flow;
      }

    }

    .cart-product-addon-detail {
      display: flex;
      gap: 10px;
    }

    .cart-product-addon-detail__icon {

      display: flex;
      gap: 10px;

      width: 30px;
      min-width: 30px;
      height: 30px;
      border: 2px solid var(---color-line);
      background-color: #fff;

      background-position: center;
      background-repeat: no-repeat;
      background-size: 16px;

    }

    .cart-product-addon-detail--font,
    .cart-product-addon-detail--color {
      .cart-product-addon-detail__icon {
        border-radius: 100%;
      }
    }

    .cart-product-addon-detail--color {
      .cart-product-addon-detail__icon {
        background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9.99998 2C5.60003 2 2 5.60004 2 10C2 14.4002 5.60003 18 9.99998 18C10.7556 18 11.3333 17.4223 11.3333 16.6667C11.3333 16.3112 11.2001 16 10.9779 15.7778C10.7556 15.5555 10.6224 15.2443 10.6224 14.8889C10.6224 14.1333 11.2001 13.5555 11.9557 13.5555H13.5556C16 13.5555 18 11.5555 18 9.11113C18 5.20008 14.4001 2 9.99998 2ZM5.11112 10C4.35545 10 3.77779 9.42233 3.77779 8.66667C3.77779 7.911 4.35545 7.33333 5.11112 7.33333C5.86674 7.33333 6.44445 7.911 6.44445 8.66667C6.44445 9.42233 5.86674 10 5.11112 10ZM7.77778 6.44446C7.02211 6.44446 6.44445 5.86675 6.44445 5.11113C6.44445 4.35546 7.02211 3.77779 7.77778 3.77779C8.5334 3.77779 9.11111 4.35546 9.11111 5.11113C9.11111 5.86675 8.5334 6.44446 7.77778 6.44446ZM12.2222 6.44446C11.4666 6.44446 10.8889 5.86675 10.8889 5.11113C10.8889 4.35546 11.4666 3.77779 12.2222 3.77779C12.9778 3.77779 13.5555 4.35546 13.5555 5.11113C13.5555 5.86675 12.9778 6.44446 12.2222 6.44446ZM14.8888 10C14.1332 10 13.5555 9.42233 13.5555 8.66667C13.5555 7.911 14.1332 7.33333 14.8888 7.33333C15.6445 7.33333 16.2222 7.911 16.2222 8.66667C16.2222 9.42233 15.6445 10 14.8888 10Z' fill='black'/%3E%3C/svg%3E%0A");
      }
    }

    .cart-product-addon-detail--font {
      .cart-product-addon-detail__icon {
        background-image: url("data:image/svg+xml,%3Csvg width='15' height='21' viewBox='0 0 15 21' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M2.86133 20.5439C2.18815 20.5439 1.6582 20.3721 1.27148 20.0283C0.877604 19.6846 0.680664 19.2692 0.680664 18.7822C0.680664 18.4313 0.798828 18.1484 1.03516 17.9336C1.26432 17.7188 1.52572 17.6113 1.81934 17.6113C2.10579 17.6113 2.36361 17.708 2.59277 17.9014C2.81478 18.1019 2.92578 18.3704 2.92578 18.707C2.92578 18.8861 2.86849 19.0615 2.75391 19.2334C2.63216 19.4124 2.57129 19.5342 2.57129 19.5986C2.57129 19.6774 2.61068 19.7383 2.68945 19.7812C2.76107 19.8242 2.85059 19.8457 2.95801 19.8457C3.50944 19.8457 3.92839 19.4053 4.21484 18.5244C4.5013 17.6507 5.0957 15.0941 5.99805 10.8545L6.90039 7.04102H5.28906L5.49316 6.12793H7.10449C7.2334 5.58366 7.50553 4.84603 7.9209 3.91504C8.34342 2.97689 8.88053 2.22135 9.53223 1.64844C10.32 0.968099 11.1292 0.62793 11.96 0.62793C12.626 0.62793 13.1702 0.77832 13.5928 1.0791C14.0225 1.37988 14.2373 1.82747 14.2373 2.42188C14.2373 2.77279 14.1263 3.05566 13.9043 3.27051C13.6895 3.47819 13.4316 3.58203 13.1309 3.58203C12.8372 3.58203 12.583 3.47819 12.3682 3.27051C12.1533 3.06283 12.0459 2.81934 12.0459 2.54004C12.0459 2.34668 12.1032 2.14974 12.2178 1.94922C12.3395 1.74154 12.4004 1.61979 12.4004 1.58398C12.4004 1.53385 12.3753 1.4694 12.3252 1.39062C12.2679 1.31901 12.1676 1.2832 12.0244 1.2832C11.6234 1.2832 11.2581 1.5625 10.9287 2.12109C10.6064 2.67969 10.3057 3.49609 10.0264 4.57031L9.61816 6.12793H11.6592L11.4551 7.04102H9.46777C8.4222 12.1829 7.55924 15.556 6.87891 17.1602C5.91927 19.416 4.58008 20.5439 2.86133 20.5439Z' fill='black'/%3E%3C/svg%3E%0A");
      }
    }

    .cart-product-addon-detail--embroidery-text {
      .cart-product-addon-detail__icon {
        background-image: url("data:image/svg+xml,%3Csvg width='17' height='17' viewBox='0 0 17 17' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10.6205 10.5156L7.77314 3.56273L4.96184 10.5156H10.6205ZM0 17V16.5439C0.632744 16.4699 1.1053 16.2234 1.41767 15.8042C1.73804 15.3851 2.28269 14.2468 3.05159 12.3894L8.18163 0H8.66219L14.7894 14.3125C15.1979 15.2659 15.5223 15.8576 15.7625 16.0877C16.0108 16.3096 16.4233 16.4617 17 16.5439V17H10.7406V16.5439C11.4615 16.4781 11.926 16.4 12.1343 16.3096C12.3425 16.211 12.4466 15.9768 12.4466 15.607C12.4466 15.4837 12.4066 15.2659 12.3265 14.9536C12.2464 14.6413 12.1343 14.3125 11.9901 13.9674L10.9689 11.5388H4.52933C3.88857 13.1907 3.50412 14.2016 3.37597 14.5714C3.25583 14.933 3.19576 15.2207 3.19576 15.4344C3.19576 15.8617 3.36396 16.1576 3.70035 16.322C3.9086 16.4206 4.30106 16.4946 4.87774 16.5439V17H0Z' fill='black'/%3E%3C/svg%3E%0A");
      }
    }

  }

  /* ----- Recommendations ----- */

  .mini-cart__recommendations-list {
    display: grid;
    grid-auto-flow: column;
    grid-auto-columns: minmax(64vw, 1fr);
    grid-gap: var(--grid-gap);
  }

  .mini-cart__recommendations-inner {

    margin-top: 0;
    padding-top: 0;

    background: transparent;

    .scroller {
      overflow-x: auto;
      overflow-y: hidden;
      scroll-snap-type: x mandatory;
      margin-left: calc(-1 * var(--container-gutter));
      margin-right: calc(-1 * var(--container-gutter));
      scrollbar-width: none;

      -ms-overflow-style: none;
      /* Internet Explorer 10+ */
      scrollbar-width: none;

      /* Firefox */
      &::-webkit-scrollbar {
        display: none;
        /* Safari and Chrome */
      }

    }

    .scroller__inner {
      min-width: min-content;
      padding-left: var(--container-gutter);
      padding-right: var(--container-gutter);
      margin-left: auto;
      margin-right: auto;
    }

    .product-item {
      &.product-item--upsell {
        
        background: var(---background-color--content-2);
        margin: 0;
        max-width: 300px;

        .product-item__info {
          flex-direction: row;
          width: 100%;
        }

        .product-item__image-wrapper {
          width: 80px;
          height: 100%;
          margin-right: 15px;
        }
      }
    }

    .mini-cart__recommendations-list {
      grid-auto-columns: minmax(40vw,1fr);
      grid-auto-columns: 300px;
      .product-item {
        max-width: 300px;
        padding: 15px;
        background-color: var(---color--kyte-cream);
        border-radius: var(--block-border-radius);
      }
      > div {
        display: inline-block;
        max-width: 100%;
      }
    }

    .product-item-meta__title {
      white-space: normal;
    }

  }

  /* ----- Checkout Button ----- */

  .checkout-button__cart {
    .icon {
      position: relative;
      top: 0px;
      width: 20px;
      height: 20px;
      max-height: 20px;
      stroke: none;
    }
  }

  .checkout-button-message {
    margin-top: 10px;
  }

  /* ----- Terms Checkbox ----- */

  .mini-cart__drawer-footer {
    padding-top: 15px;
  }

  .mini-cart__checkbox {
    margin-top: 12px;
    display: flex;
    align-items: center;
  }

}
