.list-collections {

  .list-collections__item {
    .heading {
      color: var(---color-text--reversed);
    }

    p {
      color: RGB(var(--text-color));
    }
  }

  .list-collections__item-info {
    .h3 {
      margin-bottom: 12px;
    }

    .list-collections__item-info-text {
      max-width: 340px;
      margin: auto;
      margin-bottom: 12px;
    }
  }

  .list-collections__item-list {
    gap: 20px;
  }

  .list-collections__item-info-text {
    margin: 0;
  }

}