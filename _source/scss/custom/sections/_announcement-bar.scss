.shopify-section--announcement-bar {

  .announcement-bar__message {
    padding: 10px 0;
  }

  .announcement-bar__content-image {
    padding: 30px;

    img {
      border-radius: var(--block-border-radius);
      overflow: hidden;
    }
  }

  .announcement-bar__message-inner {

    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: 0.5em;
  
    button {
      margin: 0 !important;
    }

  }

  /* ----- Rewards Points ----- */

  .announcement-bar__rewards-points {

    svg,
    img {
      display: inline-block;
      mix-blend-mode: screen;
      max-height: 14px;
      max-width: 14px;

      @include respond-to($medium-up) {
        width: 18px;
        height: 18px;
      }
    }

  }

  .announcement-bar__divider {
    &:before {
      display: inline-block;
      content: "|";
      margin: 0 0.25em;
      opacity: 0.25;
    }
  }

}