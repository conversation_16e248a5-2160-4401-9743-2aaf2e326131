section.custom-collection-banner {

  .image-overlay {

    border-radius: var(--block-border-radius);
    overflow: hidden;

  }
  
}

section.custom-collection-banner--split {

  .image-overlay {

    border-radius: var(--block-border-radius);
    overflow: hidden;

    &.image-overlay--small {
      --image-height: 300px;
    }

    &.image-overlay--medium {
      --image-height: 375px;
    }

    &.image-overlay--large {
      --image-height: 600px;
    }

  }

  .image-overlay--no-image {
    .text-container {
      max-width: unset !important;
      text-align: center;
    }
  }

  .collection-grid-banner__overlay-subheading {
    margin: 0;

    +.heading {
      margin-top: 0;
    }
  }

  .image-overlay__split-images {
    display: flex;
    width: 100%;

    @include respond-to($medium-down) {
      flex-direction: column;
    }
  }

  .image-overlay__split-image {
    width: 100%;
  }

  .image-overlay__image-wrapper {
    @include respond-to($medium-down) {
      position: static;
    }
  }

  .image-overlay__image {
    position: static;
    @include respond-to($medium-down) {
      max-height: 30vh;
    }
  }

  .image-overlay__split-image {
    position: relative;
    z-index: 1;
    width: 100%;
    flex: 1 1 auto;
  }

  .image-overlay__split-image--content {

    display: flex;
    flex-direction: column;
    justify-content: center;

    padding: 30px;
    
    @include respond-to($medium-up) {
      padding: 50px;
    }

    .heading {
      margin-bottom: 0;
    }

  }
  
}