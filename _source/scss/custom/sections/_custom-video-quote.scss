.section.custom-video-quote {

  &.video-quote-background--overlap {

    position: relative;

    .section__color-wrapper {
      position: relative;
      z-index: 1;
      background: transparent !important;
    }

    &:before {
      content: '';

      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 0;

      display: block;
      width: 100%;
      height: 75%;

      background: RGB(var(--section-accent-background));
      // background: #000;
    }
  }

  .video-wrapper {
    border-radius: var(--block-border-radius);
    overflow: hidden;
  }

  .section__header {
    margin-top: 60px;
    margin-bottom: 60px;
  }

  .gallery__caption-cite {
    >p:not(last-child) {
      margin-bottom: 0;
    }
  }

}