.section.custom-product-feature-icons {

  .section__color-wrapper {
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;

    @media (max-width: 1000px) {
      background-position: center 0px;
    }
  }

  .product-feature-icons-container {
    margin-top: 30px;
  }

  .product-feature-icons {

    display: flex;
    flex-wrap: nowrap;
    justify-content: center;
    max-width: 1050px;
    margin: auto;
    flex-direction: column;

    @include respond-to($small-up) {
      flex-wrap: wrap;
      flex-direction: row;
    }

  }

  .product-feature-icon {

    gap: 20px;

    @include respond-to($small-up) {
      width: calc(20%);
      gap: 0;
      margin-bottom: 30px;
      margin-top: 30px;
    }

  }

  .product-feature-icon {
    display: flex;

    @include respond-to($small-down) {
      align-items: flex-start;
      margin-bottom: 20px;
      align-items: center;
    }

    @include respond-to($small-up) {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-direction: column;
      width: calc(20%);
      margin-bottom: 30px;
      margin-top: 30px;

    }

  }

  .product-feature-icon__title {
    width: 100%;
  }

  .product-feature-icon__image {
    
    min-width: 60px;

    @include respond-to($small-up) {
      min-width: unset;
    }

  }

  .section__color-wrapper--boxed {
    padding: 0 30px;
  }

  .content-box {
    @include respond-to($small-down) {
      padding-top: 30px;
    }
  }

}