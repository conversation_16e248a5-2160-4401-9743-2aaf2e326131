footer.footer--custom {

  .text--xxsmall {
    font-size: var(---font-size-body-xs--desktop);
  }

  .social-media {
    @include respond-to($medium-down) {
      margin-left: -10px;
    }
  }

  .linklist__item {
    
    padding-bottom: 5px;
    
    @include respond-to($medium-up) {
      padding-bottom: 10px;
    }

    a {
      font-size: var(---font-size-body-small--desktop);
    }

  }

  .footer__aside {
    @include respond-to($medium-down) {
      display: flex;
      gap: 5px;
      flex-direction: column;
    }
  }

  .footer__copyright {
    
    font-size: var(---font-size-body-xs--desktop);
    .separator {
      margin: 0 1em;
      @include respond-to($medium-down) {
        display: none;
      }
    }
    a {
      margin-left: 0.25em;
      margin-right: 0.25em;
    }

    @include respond-to($medium-down) {
      display: flex;
      gap: 5px;
      flex-direction: column;
      align-items: flex-start;
      margin-top: 0;
    }
  }

  .footer__social {

    display: flex;
    align-items: center;
    gap: 20px;
    margin-left: auto;

    @include respond-to($medium-down) {
      order: -1 !important;
      margin-left: 0;

      ul {
        padding-left: 0;
      }
    }

  }

  .footer__item-list {
    @include respond-to($medium-down) {
      column-gap: 20px;
    }
  }

  .footer__voltage-credit {
    margin-left: 80px;
    @include respond-to($medium-down) {
      margin-left: 0;
    }
  }

  .footer__item-title {
    font-size: var(---font-size-subheading--desktop);
  }

  .footer__item--button-with-text {
    
    max-width: 400px;

    @include respond-to($medium-down) {
      padding: 20px 0;
      border-top: 1px solid var(---color-line);
      border-bottom: 1px solid var(---color-line);
      max-width: 100%;
      grid-column: 1/3;
    }

  }

}