store-header.header {

  border-bottom: 1px solid var(---color-line--light);

  .header__logo {
    display: flex;
    // gap: 20px;
  }

  .header__logo-link {
    svg {
      height: auto !important;
    }

    @include respond-to($large-up) {
      min-height: 70px;
      display: flex;
      align-items: center;
    }
  }

  .header__wrapper {
    padding: 6px 0;
  }

  .header__linklist-link {

    font-family: var(---font-family-body--alternate);
    font-style: var(---font-style-body--alternate);
    letter-spacing: var(---letter-spacing--body);

    font-variation-settings: 'wght' 500;

    font-size: var(---font-size-body--mobile);
    line-height: var(---line-height-body--mobile);

    @include respond-to($medium-up) {
      font-size: var(---font-size-body--desktop);
      line-height: var(---line-height-body--desktop);
    }
  }

  .header__cross-border .popover-button {
    font-size: var(---font-size-body-small--desktop);
    margin-bottom: 0px;
  }

  .header__logo-container {
    margin: 0;
  }

  .shop-by-color__content {

    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 10px 20px;

    padding-top: 0.5em;

    @media (min-width: 1400px) {
      max-height: 250px;
    }

    @media (max-width: 1300px) {
      grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
    }

  }

}















/* ============================== TABBED HEADER ============================== */


store-header.tabbed-header {

  --transition-duration: 0.25s;
  --text-color--hover: var(--tabbed-header-color-text--hover);

  // color: RGB(var(--text-color));

  // Icons

  .header__secondary-links {
    .icon {
      color: RGB(var(--icon-color));
  
      * {
        fill: currentColor;
      }
  
      &.icon--header-search {
        * {
          fill: transparent;
          stroke: currentColor;
          stroke-width: 2px;
        }
      }
    }
  }


  .header__wrapper {
    // position: relative;
    min-height: 70px;
    gap: 20px;
    // height: 70px;
  }

  .header__linklist {
    flex-wrap: wrap;
  }

  .header__linklist-item {
    margin-right: 24px;
  }


  /* -------------------- LEFT PART -------------------- */

  .header__inline-navigation {
    // gap: var(--tabbed-header-gap);
    gap: 0;
    // flex: 1 0 auto;
  }

  .header__secondary-links {
    gap: var(--tabbed-header-gap);
    flex: 0 1 0;
  }

  /* ----- Expanding Logo ----- */

  .header__expanding-logo {
    transition: width 0.7s;
    overflow: hidden;
    height: 100%;
    width: 0;

    position: relative;

    &:after {
      content: '';
      position: absolute;
      width: 20px;
      height: 100%;
      right: 0;
      bottom: 0;
      background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
      pointer-events: none;
    }

    img,
    svg {
      width: 100%;
    }

    .header__logo-link {
      padding-left: 0;
      padding-right: 10px;
      opacity: 0;
      transition: opacity 0.5s;
    }

  }

  .header__logo-link-static {
    width: 100%;
  }

  .header__logo-link {
    display: flex;
    align-items: center;
    padding-left: 20px;
    max-width: 160px;

    svg {
      * {
        fill: RGB(var(--logo-color));
      }
    }

  }

  &.header--scrolled {
    .header__expanding-logo {
      width: 180px;

      .header__logo-link {
        opacity: 1;
      }
    }
  }


  /* -------------------- RIGHT PART -------------------- */

  /* ----- Search Bar ----- */

  .predictive-search-input-wrapper {

    display: flex;
    align-items: center;
    padding: 10px 15px;
    border-radius: var(--tabbed-header-input-radius);

    background-color: RGB(var(--background-2));
    color: var(--text-color);

    cursor: pointer;

    transition:
      background-color var(--transition-duration),
      color var(--transition-duration);

    svg {
      color: RGB(var(--background-2));
      * {
        transition:
          stroke var(--transition-duration),
          fill var(--transition-duration);
      }
    }

    .predictive-search__input {
      min-width: 250px;

      &::placeholder {
        color: RGB(var(--text-color));
      }
    }

    /* ----- Hover ----- */

    &:hover {
      color: RGB(var(--background-2));
      background-color: RGB(var(--primary-button-background));

      svg {
        * {
          stroke: RGB(var(--background-2));
        }
      }

      .predictive-search__input {
        &::placeholder {
          color: RGB(var(--background-2));
        }
      }

    }

  }

  .predictive-search__form {}

  /* ----- Icons ----- */

  .header__icon-list {
    gap: var(--tabbed-header-gap);
  }

  .header__cart-count {
    // background-color: RGB(var(--bubble-color));
  }



  .mega-menu {

    --padding: 32px;

    background-color: RGB(var(--block-background, var(--background)));
    padding-block: var(--padding);

    @include respond-to($large-up) {
      --padding: 48px;
    }

  }

  .mega-menu__inner {
    padding: 0;
  }

  .mega-menu-header {
    padding-bottom: calc(var(--padding) / 2);
    text-align: center;
  }

  .mega-menu-footer {
    padding-top: calc(var(--padding) / 2);
    text-align: center;
  }

}



/* ============================== FLOATING ICON ============================== */

.header-floating-badge {

  --top: calc(var(--header-height) + 20px + var(--product-sticky-form-height) + var(--sticky-product-form-spacer));

  position: absolute;
  z-index: -1;
  top: var(--top);
  right: calc(var(--container-gutter));
  // right: calc(100vw - var(--container-max-width));

  display: flex;
  align-items: center;
  justify-content: center;

  width: 70px;
  height: 70px;
  border-radius: 100%;

  padding: var(--badge-border-width);
  background: var(--badge-color-border);

  opacity: 0;
  transform: scale(0);

  @include respond-to($large-up) {
    right: calc(var(--container-gutter) * 2);
  }

  &:hover {
    .header-floating-badge__inner {
      transform: rotate(20deg);
    }
  }

}

.header-floating-badge--loaded {

  animation: floating-badge-in 1s cubic-bezier(0.175, 0.885, 0.320, 1.275) both;

}

.header-floating-badge__inner {

  border-radius: 100%;
  background-color: RGB(var(--badge-color-background));
  transition: 0.25s ease transform;

}

.header-floating-badge__image {
  width: 100%;
  overflow: hidden;

  img {
    display: block;
    width: 100%;
    height: 100%;
  }
}

.header-floating-badge__label {

  position: absolute;
  bottom: 0;
  padding: 0.3em 1em;
  transform: translateY(calc(100% - 15px));

  font-size: var(--font-size-xs);
  font-weight: bold;

  background-color: RGB(var(--badge-color-label-background));
  color: RGB(var(--badge-color-label-text));

  border-radius: 100px;

}

.header-floating-badge__label-inner {
  white-space: nowrap;
}