.footer--kyte {
  --footer-block-spacing: 20px;

  @include respond-to($small-up) {
    --footer-block-spacing: 30px;
  }

  padding-block: 20px;

  .social-media__link {
    width: 50px;
    height: 50px;
  }

  .footer-navigation__header,
  .footer-info-block__title,
  .footer-signup-block__title {
    font-size: var(---font-size-subheading-small) !important;

    @include respond-to($medium-down) {
      font-size: var(---font-size-subheading) !important;
    }
  }

  .button:not(.button--outline) {
    color: RGB(var(--button-text));
  }
  .input__field {
    background: RGB(var(--input-background));
  }

}

.footer-block {
  
  padding: var(--footer-block-spacing);
  background-color: RGB(var(--block-background));
  border-radius: var(--block-border-radius);

}

.footer__inner {
  display: grid;
  grid-auto-flow: row;
  gap: var(--footer-block-spacing);
}


/* ----- Top - Brand and Marketing ----- */

.footer__top {

  display: grid;
  gap: var(--footer-block-spacing);
  grid-auto-flow: row;

  @include respond-to($medium-up) {
    grid-template-columns: minmax(200px, 300px) minmax(0, 1fr);
  }

  .social-media {
    flex-wrap: wrap;

    @include respond-to($medium-down) {
      justify-content: center;
    }
  }

}

.footer__logo-container {
  display: flex;
  flex-direction: column;
  gap: 20px;

  @include respond-to($medium-down) {
    text-align: center;
  }

}

.footer__logo {
  img {
    max-width: 200px;
  }
}

.footer-signup-blocks {
  display: grid;
  gap: var(--footer-block-spacing);

  // display: flex;
  // gap: var(--footer-block-spacing);

  @include respond-to($medium-up) {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

.footer-signup-block {
  display: flex;
  flex-direction: column;
  gap: 0.5em;
  border-radius: var(--block-border-radius);

  form {
    margin: 0;
  }

  .banner {
    margin: 1em 0;
  }

  .button:not(.button--text) {
    margin: 0;
  }

  .newsletter__form {
    .button {
      height: calc(var(--form-input-field-height) - 2px) !important;
    }
  }

  @include respond-to($medium-down) {
    border: 1px solid var(---color-line);
    gap: 0;
  }
}

.footer-signup-block__header {
  display: flex;
  justify-content: space-between;

}

.footer-signup-block__title {
  @include respond-to($medium-down) {
    margin-bottom: 0;
    width: 100%;
    padding: 15px var(--footer-block-spacing);
  }
}

.footer-signup-block__expander {
  display: none;

  svg {
    transition: transform .4s ease-in-out,opacity .4s ease-in-out;
  }

  &[aria-expanded="true"] {
    svg {
      transform: rotate(180deg);
    }
  }

  @include respond-to($medium-down) {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 50px;
  }
}

.footer-signup-block__collapsible {
  @include respond-to($medium-up) {
    height: auto;
    visibility: visible;
    overflow: visible;
  }
}

.footer-signup-block__body {
  display: grid;
  grid-auto-flow: row;
  gap: 0.5em;

  @include respond-to($medium-down) {
    // display: none;
    padding: 15px var(--footer-block-spacing);
    border-top: 1px solid var(---color-line);
  }
}

.footer-signup-block__title {

  font-size: var(---font-size-subheading-small) !important;

  @include respond-to($medium-down) {
    font-size: var(---font-size-subheading) !important;
  }

}

/* ----- Middle - Navigation and Info ----- */

.footer__middle {

  display: grid;
  gap: var(--footer-block-spacing);

  @include respond-to($large-up) {
    grid-template-columns: minmax(0, 2fr) minmax(0, 1fr);
  }

}

.footer__info-blocks {

  order: 0;

  @include respond-to($large-up) {
    order: 1;
  }
}

.footer__navigation {

  order: 1;

  @include respond-to($large-up) {
    order: 0;
  }
}

// Navigation

.footer-navigation {
  overflow: hidden;

  @media (max-width: 479px) {
    padding: 0;
  }
}

.footer-navigation__inner {

  display: grid;
  gap: calc(var(--footer-block-spacing) + 1em) var(--footer-block-spacing);

  @media (min-width: 500px) {
    grid-template-columns: repeat(2, minmax(200px, 300px));
  }

  @include respond-to($small-up) {
    grid-template-columns: repeat(3, minmax(180px, 300px));
  }

  @media (max-width: 479px) {
    gap: 0;
  }

}

.footer-navigation__header {

  width: 100%;

  @media (max-width: 479px) {
    margin-bottom: 0;
    padding: 15px var(--footer-block-spacing);
  }

}

.footer-navigation__column {
  @media (max-width: 479px) {
    border-bottom: 1px solid var(---color-line);

    &:last-child {
      border-bottom: 0;
    }
  }
}

.footer-navigation__linklist {

  display: grid;
  grid-auto-flow: row;
  gap: 0.75em;

}


// Info Blocks

.footer-info-blocks {

  // display: grid;
  // grid-auto-flow: row;

  display: flex;
  flex-direction: column;
  gap: var(--footer-block-spacing);

  height: 100%;

}

.footer-info-block {

  --image-block-size: 95px;

  @include respond-to($medium-up) {
    --image-block-size: 120px;
  }

  @include respond-to($large-up) {
    --image-block-size: 150px;
  }

  // display: grid;
  // grid-auto-flow: row;

  position: relative;

  display: flex;
  flex-direction: column;
  gap: 0.5em;
  // height: 100%;


}

.footer-info-block--text-offset {
  padding-right: calc(var(--image-block-size) * 0.75);
}

.footer-info-block__image {
  position: absolute;
  top: 0;
  right: 0;

  transform: translate(15%, -15%);

  display: block;
  width: var(--image-block-size);

  @include respond-to($medium-up) {
    transform: translate(25%, -25%);
  }

  // float: right;
  // shape-outside: circle(50%);
}

.footer-info-block__image-zone {
  display: block;
  width: 75px;
  height: 75px;
  float: right;
  shape-outside: circle(50%);
}


/* ----- Bottom - Legal, Credit and Selectors ----- */

.footer__bottom {
  display: flex;
  align-items: start;
  justify-content: space-between;
  gap: 20px;
  // grid-auto-flow: row;
  // gap: var(--footer-block-spacing);

  @media (max-width: 1200px) {
    flex-direction: column;
    align-items: center;
  }
}

.footer__bottom-left {
  display: grid;
  grid-auto-flow: row;
  gap: var(--footer-block-spacing);
}

.footer__legal {
  display: flex;
  gap: var(--footer-block-spacing);

  @include respond-to($small-down) {
    flex-direction: column;
    align-items: center;
  }
}

.footer__legal,
.footer__localization {
  // Height fix for larger screens.
  min-height: 36px;
}

.footer__credit {
  display: inline-flex;
  flex-wrap: wrap;
  gap: 0.5em;

  align-items: center;

  @include respond-to($small-down) {
    justify-content: center;
    text-align: center;
    gap: var(--footer-block-spacing);
  }

  @media (max-width: 479px) {
    justify-content: center;
    text-align: center;
    gap: 0.5em;
  }

}

.footer__legal-nav {
  display: inline-flex;
  gap: 1em;

  align-items: center;

  @media (max-width: 479px) {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
}

.footer__country-selector {
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 1em;
  white-space: nowrap;

  // flex-direction: row;

  @media (max-width: 479px) {
    flex-wrap: wrap;
  }

}

.footer__localization {}

.footer-popover-container {
  
  @include respond-to($small-down) {
    display: flex;
    flex-direction: column;
  }
  
  gap: 0.5em;

}

.footer-selector {

  --heading-color: var(--root-heading-color);
  --text-color: var(--root-text-color);
  --background: var(--root-background);
  --primary-button-background: var(--root-primary-button-background);
  --primary-button-text-color: var(--root-primary-button-text-color);
  --section-background: var(--root-background);
  
  z-index: 10;
  color: RGB(var(--text-color));
  background: RGB(var(--background));
  width: auto;

}

.footer__localization {
  display: inline-flex;
  flex-direction: column;
  gap: 1em;
  flex-wrap: wrap;
  justify-content: end;
}

// Footer Accordion

.footer-accordion {

  display: grid;
  grid-auto-flow: row;
  gap: 0.75em;

  @media (max-width: 479px) {
    gap: 0;
  }

}

.footer-accordion__header {
  display: flex;
}

.footer-accordion__expander {

  display: flex;
  align-items: center;
  justify-content: center;

  min-width: 50px;

  @media (min-width: 480px) {
    display: none;
  }

}

.footer-accordion__collapsible {
  
  // @include respond-to($small-up) {
  @media (min-width: 480px) {
    height: auto;
    visibility: visible;
  }

}

.footer-accordion-content {

  // display: none;
  @media (max-width: 479px) {
    padding: var(--footer-block-spacing);
    border-top: 1px solid var(---color-line);
  }

}