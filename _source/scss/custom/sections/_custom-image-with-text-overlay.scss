.custom-image-with-text-overlay {
  position: relative;
  overflow: hidden;

  .button-wrapper {
    margin-top: 20px;
  }
  
  &.custom-image-with-text-overlay--border-radius {
    .image-overlay {
      border-radius: var(--block-border-radius);
      overflow: hidden;
    }
  }

  @include respond-to($small-down) {
    
    .image-overlay {

      min-height: 600px;
      min-height: calc(80vh - var(--announcement-bar-height) - var(--header-height) - var(--vertical-breather) - var(--vertical-breather));

      .image-overlay__content-wrapper {
        display: flex;

        .image-overlay__content {
          height: auto;
          margin: auto 24px;
          width: 100%;
        }
      }
  
    }

  }

}