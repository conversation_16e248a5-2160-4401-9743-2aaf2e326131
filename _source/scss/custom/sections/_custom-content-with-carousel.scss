.section.custom-content-with-carousel {
  
  .heading {
    margin-block-end: 0.25em;
    max-width: 80%;
  }

  .container {
    padding-left: var(--container-gutter);
    padding-right: var(--container-gutter);
  }

  .container--extra-padding {
    padding-left: calc(var(--container-gutter) * 2);
    padding-right: calc(var(--container-gutter) * 2);
  }

  @include respond-to($medium-down) {

    .horizontal-header {
      border-bottom: 0;
      // margin-bottom: 10px;
    }

  }
  
}

.usp-icons-carousel {

  @include respond-to($medium-down) {
    border-top: 0;
  }
  
  .usp-icons-carousel__list {
    
    display: flex;
    gap: var(--container-gutter);
    scroll-snap-type: x mandatory;

    @include respond-to($medium-up) {
      display: grid;
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }

  }

  .usp-icons-carousel__item {
    
    display: block;
    flex: none;
    scroll-snap-align: center;
    scroll-snap-stop: always;
    
    width: 100%;
    margin-bottom: 20px;
    
  }

  .usp-icons-carousel__item-inner {
    display: flex;
    gap: 1em;
    padding: 12px;
    @include respond-to($medium-down) {
      border: 1px solid var(---color-line);
      border-radius: var(--block-border-radius);
      box-shadow: var(--block-shadow);
    }
  }

  .usp-icons-carousel__icon {
    min-width: 50px;
    max-width: 50px;
    max-height: 50px;
    img {
      height: 100%;
      object-fit: contain;
    }
  }

  .usp-icons-carousel__item-description {
    margin-left: 1em;
    @include respond-to($medium-down) {
      margin-left: 0;
    }
  }

  .usp-icons-carousel__item-heading {
    // margin-bottom: 0.5em;
  }

  .usp-icons-carousel__dots {
    margin-top: 20px;
    @include respond-to($medium-down) {
      margin-top: 0;
      margin-bottom: 20px;
    }
  }

}