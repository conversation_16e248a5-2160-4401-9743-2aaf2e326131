/*

  1. Utilities
  2. Responsive
  3. UI Elements
    3.1. Buttons

*/

/*  ==============================
    1. Utilities
    ============================== */

@mixin hide-scrollbars() {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
  &::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
  }
}

@mixin clearfix() {
  &::after {
    content: '';
    display: table;
    clear: both;
  }

  // sass-lint:disable
  *zoom: 1;
}

@mixin visually-hidden() {
  // sass-lint:disable no-important
  position: absolute !important;
  overflow: hidden;
  clip: rect(0 0 0 0);
  height: 1px;
  width: 1px;
  max-width: 1px;
  max-height: 1px;
  font-size: 0;
  margin: -1px;
  padding: 0;
  border: 0;
}

@mixin visually-shown($position: inherit) {
  // sass-lint:disable no-important
  position: $position !important;
  overflow: auto;
  clip: auto;
  width: auto;
  height: auto;
  margin: 0;
}

/*  ==============================
    2. Responsive
    ============================== */

@mixin respond-to($media-query) {
  $breakpoint-found: false;

  @each $breakpoint in $breakpoints {
    $name: nth($breakpoint, 1);
    $declaration: nth($breakpoint, 2);

    @if $media-query == $name and $declaration {
      $breakpoint-found: true;

      @media only screen and #{$declaration} {
        @content;
      }
    }
  }

  @if $breakpoint-found == false {
    @warn 'Breakpoint "#{$media-query}" does not exist';
  }
}

/*================ Responsive Show/Hide Helper ================*/
@mixin responsive-display-helper($breakpoint: '') {
  // sass-lint:disable no-important
  .#{$breakpoint}shown {
    display: block !important;
  }

  .#{$breakpoint}hidden {
    display: none !important;
  }
}


/*================ Responsive Text Alignment Helper ================*/
@mixin responsive-text-align-helper($breakpoint: '') {
  // sass-lint:disable no-important
  .#{$breakpoint}text-left {
    text-align: left !important;
  }

  .#{$breakpoint}text-right {
    text-align: right !important;
  }

  .#{$breakpoint}text-center {
    text-align: center !important;
  }
}

/*  ==============================
    3. UI Elements
    ============================== */

/*  ------------------------------
    3.1. Buttons
    ------------------------------ */

@mixin button-structure() {

  // --button-text-color: var(---color--secondary--rgb);
  // --button-text-color--hover: var(---color-text--reversed--rgb);
  // --button-background: var(---color--secondary--rgb);
  // --hover-effect-color: var(---color--secondary--rgb);

  overflow: hidden;
  position: relative;
  display: inline-flex;
  gap: 20px;
  align-items: center;
  justify-content: space-between;

  margin: 0;
  width: auto;

  height: auto;
  min-height: 0;
  max-height: none;

  border: 0;

  font-family: var(---font-family-heading-alternate);
  font-style: var(---font-style-heading-alternate);
  font-weight: var(---font-weight-heading-alternate);

  line-height: 1.2;
  text-align: center;
  text-transform: unset;

  letter-spacing: var(---button-letter-spacing);
  vertical-align: top;
  // white-space: nowrap;

  border-radius: var(--button-border-radius);
  // background: transparent;

  transition: all 0.2s ease-in-out;
  appearance: none;

  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-smoothing: antialiased;

  cursor: pointer;

  padding: 1.5em 2.2em;
  
  margin-top: 0.25em;
  margin-bottom: 0.25em;
  margin-right: 0.5em;
  margin-left: 0;

  font-size: var(---font-size-button--mobile);
  @include respond-to($small-up) {
    font-size: var(---font-size-button--desktop);
  }

  &:not(.button--link) {
    min-width: var(---button-min-width);
  }

  &:last-child {
    margin-right: 0 !important;
  }
  &:only-child {
    margin-bottom: 0 !important;
  }

  // Sizes

  @include respond-to($medium-down) {
    padding: 1em 2em;
  }

  &.button--xtiny {
    padding: 0.8em 1.2em;
    min-width: unset;
    text-transform: none;
    letter-spacing: var(---letter-spacing-body--mobile);

    font-size: var(---font-size-button-small--mobile);
    @include respond-to($medium-up) {
      font-size: var(---font-size-button-large--mobile);
    }
  }

  &.button--tiny {
    padding: 0.6em 1em;
    min-width: unset;
    
    font-size: var(---font-size-button-small--mobile);
    @include respond-to($medium-up) {
      font-size: var(---font-size-button--desktop);
    }
  }

  &.button--small {
    padding: 1.2em 1.8em;
  }

  &.button--xxs {
    font-size: var(---font-size-body-small--desktop);
    padding: 0.5em 0.8em;
    min-width: 0;
    letter-spacing: 0;
  }

  &.button--large {
    padding: 2em 2.8em;
  }

  &.button--huge {
    padding: 1.4em 1.8em;
    font-size: var(---font-size-button-large--mobile);
    @include respond-to($medium-up) {
      font-size: var(---font-size-button-large--mobile);
    }
  }

  &.button--full {
    display: flex;
    width: 100%;
    margin-left: 0 !important;
    margin-right: 0 !important;
    text-align: center;
    justify-content: center;
  }

  &.disabled,
  &[disabled] {

    cursor: not-allowed;

  }

  // Style

  .loader-button__text {
    gap: 0.5em;
  }

  svg {
    position: relative;
    top: -1px;
    max-height: 16px;
    fill: currentColor;
    stroke: currentColor;
  }

}

@mixin button-style($color){

  --button--background: var(---color--#{$color}--rgb);
  --master-color--light--rgb: var(---color--#{$color}--light--rgb);
  --master-color--dark--rgb: var(---color--#{$color}--dark--rgb);
  --master-color-background--rgb: var(---background-color--#{$color}--rgb);

  --button-background: var(--button--background);
  --button-background--hover: var(--master-color--light--rgb);
  --button-background--active: var(--master-color--dark--rgb);
  --button-text-color: var(---color-text--reversed--rgb);
  --button-text-color--hover: var(---color-text--reversed--rgb);

  &:not(.button--inverted) {
    --border-color: transparent;
    --color-text--hover: var(---color-text--reversed--rgb);
  }
  
  &:not([disabled]) {
  
    background-image: 

    linear-gradient(178deg, 
      rgb(var(--button-background)), 
      rgb(var(--button-background)) 10%, 
      rgba(var(--button-background--hover),1) 10%, 
      rgba(var(--button-background--hover),1) 100%),

      linear-gradient(rgb(var(--button-background)), rgb(var(--button-background)));
      
  }

  background-color: RGB(var(--button-background));
  
}

@mixin button-style--inverted(){

  background-color: transparent;
  --button-text-color: var(--button--background);

  box-shadow: 0 0 0 1px RGBA(var(--button--background), 1) inset;

  &:not([disabled]) {

    background-image: 
      linear-gradient(178deg, 
      rgba(var(--button--background), 0), 
      rgba(var(--button--background), 0) 10%, 
      rgba(var(--button--background), 1) 10%, 
      rgba(var(--button--background), 1) 100%),
      linear-gradient(rgba(var(--button--background), 0), rgba(var(--button--background), 0));

      --button-text-color: var(--button--background);

    background-size: 100% 200%, 100% 100%;
    background-position: 100% -100%, 100% 100%;
    background-repeat: no-repeat;
    transition: background-position 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translateZ(0); /* Make sure to promote the button on its own layer */

    &:focus,
    &:focus-within,
    &:hover {

      --button-text-color: var(---color-text--reversed--rgb);

      background-position: 100% 25%, 100% 100%;

    }

    &:active {

      background-position: 100% 25%, 100% 100%;

    }

  }

}

@mixin button-style--light(){

  --button-background: var(--master-color-background--rgb);
  --button-text-color: var(--button--background);

  &:not([disabled]) {

    background-image: 

    linear-gradient(178deg, 
      rgb(var(--button-background)), 
      rgb(var(--button-background)) 10%, 
      rgba(var(--button-background--hover),0.35) 10%, 
      rgba(var(--button-background--hover),0.35) 100%),

      linear-gradient(rgb(var(--button-background)), rgb(var(--button-background)));

    background-size: 100% 200%, 100% 100%;
    background-position: 100% -100%, 100% 100%;
    background-repeat: no-repeat;
    transition: background-position 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translateZ(0); /* Make sure to promote the button on its own layer */

    &:active,
    &:focus,
    &:focus-within,
    &:hover {

      // --button-background: var(--button--background);
      // --button-text-color: var(---color-text--reversed--rgb);

      background-position: 100% 25%,100% 100%;

    }

  }

}

/* ------------------------------
   Headings
   ------------------------------ */

@mixin heading-style--1() {

  font-family: var(---font-family-heading);
  font-style: var(---font-style-heading);
  font-weight: var(---font-weight-heading);
  font-variation-settings: "wght" 400;

  // letter-spacing: var(---letter-spacing-heading--mobile);
  line-height: var(---line-height-heading--mobile);

  span.heading--alternate {
    color: var(---color-heading-1);
    text-transform: none;
    font-weight: var(---font-weight-heading);
  }

  a {
    color: var(---color-link);
  }

  @include respond-to($small-up) {
    // letter-spacing: var(---letter-spacing-heading--desktop);
    line-height: var(---line-height-heading--desktop);
  }

}

@mixin heading-style--2() {

  font-family: var(---font-family-body);
  font-style: var(---font-style-body);
  font-weight: var(---font-weight-body--bolder);

  // letter-spacing: var(---letter-spacing-body--mobile);
  line-height: var(---line-height-body--mobile);

  span.heading--alternate {
    color: var(---color-heading-2);
    text-transform: none;
    font-weight: var(---font-weight-body);
  }

  a {
    color: var(---color-link);
  }

  @include respond-to($small-up) {
    // letter-spacing: var(---letter-spacing-heading--desktop);
    line-height: var(---line-height-heading--desktop);
  }

}

@mixin blockquote-style() {

  font-family: var(---font-family-body);
  font-style: var(---font-style-body);
  font-weight: var(---font-weight-body--bolder);

  letter-spacing: var(---letter-spacing-body--mobile);
  line-height: var(---line-height-body--mobile);

  a {
    color: var(---color-link);
  }

  @include respond-to($small-up) {
    letter-spacing: var(---letter-spacing-heading--desktop);
    line-height: var(---line-height-heading--desktop);
  }

}

@mixin subheading-style() {

  text-transform: uppercase;
  // letter-spacing: var(---letter-spacing-subheading--mobile);
  font-style: var(---font-style-subheading);
  font-variation-settings: 'wght' 400;

  font-family: var(---font-family-subheading);

  color: var(--subheading-color, inherit);

  @include respond-to($small-up) {
    // letter-spacing: var(---letter-spacing-subheading--desktop);
    line-height: var(---line-height-subheading--desktop);
  }

}


/* ------------------------------
   Labels
   ------------------------------ */

@mixin label-structure() {

  // display: block;
  // width: 100%;

}

@mixin label-style() {



}

/* ------------------------------
   Inputs
   ------------------------------ */

@mixin input-structure() {

  $border-color: var(---color--secondary);
  $border-color-hover: var(---color--secondary-hover);

  display: block;
  // margin-bottom: 1em;
  // width: 100%;
  padding: 1em 1.4em;

  font-size: var(---font-size-button--mobile);

  border-width: 1px;

  &:not([disabled]){
    &:focus,
    &:hover {
      border-color: $border-color-hover;
    }
  }

  @include respond-to($medium-up) {
    font-size: var(---font-size-button--desktop);
  }

}

@mixin input-style() {

  --text-color: var(---color-text);
  --background: var(---background-color--content-2);

  // border-radius: 12px;
  border: 1px solid var(---color-line);
  // margin-bottom: 1em;

  &::placeholder {
    color: var(---color-text--light);
    opacity: 1;
  }

  &:not([disabled]){
    &:focus,
    &:hover {
      border-color: var(---color--primary);
    }
  }

  &.input--rounded {
    border-radius: var(---input-border-radius);
  }

  /*

  // Removing this as Focal already has good base styles.

  background: var(---input-background);
  border-style: solid;
  color: var(---input-color);

  outline: none;

  */

}

@mixin input-style--2() {

  border: 0;
  padding: 0.5em 0.2em;

  border-bottom: 1px solid var(---color--black);

  font-size: var(---font-size-body--mobile);
  @include respond-to($medium-up) {
    font-size: var(---font-size-body--desktop);
  }

}

/* ------------------------------
   RTE
   ------------------------------ */

    @mixin link-style() {

      &:after {

        content: '';
        display: block;
        height: 2px;
        width: 100%;
        background: currentColor;
        color: var(---color-link);
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;

        transition: transform 0.25s;
        transform-origin: left;
        transform: scale(0);

      }

      &:hover {
        color: var(---color-link--hover);
        &:after {
          transform: scale(1);
        }
      }

    }

/*  ------------------------------
    3.3. Shopify
    ------------------------------ */

@mixin when-logged-in {

  body.logged-in & {
    @content;
  }

}

@mixin when-logged-out {

  body.logged-out & {
    @content;
  }

}
