// Breakpoint Helpers

/*================ Build Base Grid Classes ================*/

@include responsive-display-helper();
@include responsive-text-align-helper();

/*================ Build Responsive Grid Classes ================*/

@each $breakpoint in $breakpoint-has-widths {

	@include respond-to($breakpoint) {

		@include responsive-display-helper('#{$breakpoint}--');
    @include responsive-text-align-helper('#{$breakpoint}--');

		.br--#{$breakpoint} {
			display: block;
		}

  }

}

.clearfix {
  @include clearfix();
}

.fallback-text,
.visually-hidden {
  @include visually-hidden();
}

.hidden {
  display: none;
}

.flex-column {
  flex-direction: column;
}

.justify-content-start {
  justify-content: start;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-end {
  justify-content: end;
}

.align-items-start {
  align-items: start;
}

.align-items-center {
  align-items: center;
}

.align-items-end {
  align-items: end;
}

.uppercase,
.text-transform--uppercase { text-transform: uppercase !important; }

.text-transform--none { text-transform: none !important; }

.strikethrough {
  text-decoration: line-through;
}

// Colors

.color--primary { color: var(---color--primary) !important; }
.color--secondary { color: var(---color--secondary) !important; }
.color--tertiary { color: var(---color--tertiary) !important; }

@for $i from 1 through 7 {
  .color--brand-#{$i} {
    color: var(---color--brand-#{$i}) !important;
  }
}

.color--kyte-black { color: var(---color--kyte-black) !important; }
.color--kyte-dark-grey { color: var(---color--kyte-dark-grey) !important; }
.color--kyte-light-grey { color: var(---color--kyte-light-grey) !important; }
.color--kyte-white { color: var(---color--kyte-white) !important; }
.color--kyte-cream { color: var(---color--kyte-cream) !important; }
.color--kyte-dark-cream { color: var(---color--kyte-dark-cream) !important; }

.weight-normal {
  font-weight: normal !important;
}

@each $color in $semiotics {

  .background-color--#{$color} {
    background: var(---color--#{$color});
  }

}


.justify-content-center { justify-content: center !important; }

.object-position--top { object-position: top !important; }
.object-position--bottom { object-position: bottom !important; }
.object-position--center { object-position: center !important; }
.object-position--left { object-position: left !important; }
.object-position--right { object-position: right !important; }

// Text

.text-align--center { text-align: center !important; }
.text-align--left { text-align: left !important; }
.text-align--right { text-align: right !important; }

.text-align--center--mobile { 
  @include respond-to($small-down) {
    text-align: center !important;
  }
}
.text-align--left--mobile { 
  @include respond-to($small-down) {
    text-align: left !important;
  }
}
.text-align--right--mobile { 
  @include respond-to($small-down) {
    text-align: right !important;
  }
}

.mix-blend-mode--multiply {
  mix-blend-mode: multiply;
}

// Layout

.no-margin { margin: 0 !important; }
.no-margin--top { margin-top: 0 !important; }
.no-margin--right { margin-right: 0 !important; }
.no-margin--left { margin-left: 0 !important; }
.no-margin--bottom { margin-bottom: 0 !important; }

.no-padding { padding: 0 !important; }
.no-padding--top { padding-top: 0 !important; }
.no-padding--right { padding-right: 0 !important; }
.no-padding--left { padding-left: 0 !important; }
.no-padding--bottom { padding-bottom: 0 !important; }

.no-padding--mobile {
  @include respond-to($small-down) {
    padding: 0 !important;
  }
}

.no-margin--mobile {
  @include respond-to($small-down) {
    margin: 0 !important;
  }
}

@for $i from 1 through 5 {

  .padding-top--#{$i}0 { padding-top: #{$i}0px !important; }
  .padding-right--#{$i}0 { padding-right: #{$i}0px !important; }
  .padding-bottom--#{$i}0 { padding-bottom: #{$i}0px !important; }
  .padding-left--#{$i}0 { padding-left: #{$i}0px !important; }
  
  .margin-top--#{$i}0 { margin-top: #{$i}0px !important; }
  .margin-right--#{$i}0 { margin-right: #{$i}0px !important; }
  .margin-bottom--#{$i}0 { margin-bottom: #{$i}0px !important; }
  .margin-left--#{$i}0 { margin-left: #{$i}0px !important; }

}


body.logged-in {
  .logged-in--hidden {
    display: none !important;
  }
}

body.logged-out {
  .logged-out--hidden {
    display: none !important;
  }
}
