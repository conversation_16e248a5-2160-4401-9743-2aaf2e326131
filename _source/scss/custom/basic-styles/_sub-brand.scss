.sub-brand {

  ---color-line: var(--sub-brand--secondary-background-color);
  ---color-line--rgb: var(--sub-brand--secondary-background-color--rgb);

  --subheading-color: var(--sub-brand--subheading-color--rgb);
  --heading-color: var(--sub-brand--heading-color--rgb);

  ---color--primary: var(--sub-brand--button-color);
  ---color--primary--rgb: var(--sub-brand--button-color--rgb);

  .text--subdued {
    color: RGBA(var(--sub-brand--text-color--rgb), 0.5);
    text-decoration-color: RGBA(var(--sub-brand--text-color--rgb), 0.1);
  }

  /* ========== Basic Styles ========== */

  #main {

    .heading {}

    .label {
      color: RGB(var(--sub-brand--subheading-color--rgb));
      background-color: RGB(var(--sub-brand--heading-color--rgb));
    }

    .label.label--subdued {
      background-color: RGB(var(--sub-brand--subheading-color--rgb));
      color: RGB(var(--sub-brand--heading-color--rgb));
    }

    .label.label--light {
      background-color: RGB(var(--sub-brand--secondary-background-color--rgb));
      color: RGB(var(--sub-brand--heading-color--rgb));
    }

    .label.label--custom {
      background-color: RGB(var(--sub-brand-color-4--rgb));
      color: RGB(var(--sub-brand--heading-color--rgb));
    }

    .price {
      color: RGB(var(--sub-brand--subheading-color--rgb));
    }

  }

  /* ========== Sections ========== */

  .drawer,
  .shopify-section {
    .heading {
      // --heading-color: var(--sub-brand--heading-color--rgb);
    }
  }

  /* ----- Header ----- */

  store-header.header {
    --header-text-color: var(--sub-brand--heading-color--rgb);
  }

  .header__icon-wrapper {
    color: RGB(var(--header-text-color));

    &:not([aria-controls="search-drawer"]) {
      svg {
        * {
          fill: RGB(var(--header-text-color));
        }
      }
    }
    svg {
      &.icon--custom-search {
        * {
          fill: RGB(var(--header-text-color));
        }
      }
    }
  }

  .header__cart-count {
    background-color: RGB(var(--header-text-color));
  }

  .shopify-section--top-menu-bar {
    a {
      color: RGB(var(--text-color));
    }
  }

  /* ========== Pages ========== */

  /* ----- Product ----- */



  /* ========== Components ========== */

  /* ----- Shipping Bar ----- */

  .shipping-bar {
    .shipping-bar__progress {
      background-color: RGB(var(--sub-brand--secondary-background-color--rgb));

      &:after {
        background-color: RGB(var(--primary-button-background));
      }
    }
  }

  /* ----- Quantity Selector ----- */

  .shopify-section--mini-cart {
    .line-item {

      .quantity-selector {
        .quantity-selector__button {
          ---color-text: var(--sub-brand--button-color--rgb);

          background-color: RGB(var(--sub-brand--button-text-color--rgb));
          color: RGB(var(--sub-brand--button-color--rgb));
          box-shadow: 0 0 0 1px RGBA(var(---color-text), 0.25);

          transition: 0.25s background, 0.25s color;

          &:hover,
          &:active {
            background-color: RGB(var(--sub-brand-color-6--rgb));
            color: RGB(var(--sub-brand--button-text-color--rgb));
            box-shadow: 0 0 0 1px RGBA(var(--sub-brand--button-text-color--rgb), 0.25);
          }
        }
      }

    }
  }


  /* ----- Buttons ----- */

  .button {

    &.button--primary {

      ---color-text: var(--sub-brand--button-color--rgb);

      background-color: RGB(var(--sub-brand--button-color--rgb));
      color: RGB(var(--sub-brand--button-text-color--rgb));
      box-shadow: 0 0 0 1px RGBA(var(--sub-brand--button-text-color--rgb), 0.25);

      &:hover,
      &:active {
        background-color: RGB(var(--sub-brand--button-text-color--rgb));
        color: RGB(var(--sub-brand--button-color--rgb));
        box-shadow: 0 0 0 1px RGBA(var(--sub-brand--button-color--rgb), 0.25);
      }

    }

    &.button--secondary {

      ---color-text: var(--sub-brand--button-color--rgb);

      background-color: RGB(var(--sub-brand--button-text-color--rgb));
      color: RGB(var(--sub-brand--button-color--rgb));
      box-shadow: 0 0 0 1px RGBA(var(---color-text), 0.25);


      &:hover,
      &:active {
        background-color: RGB(var(--sub-brand--button-color--rgb));
        color: RGB(var(--sub-brand--button-text-color--rgb));
        box-shadow: 0 0 0 1px RGBA(var(--sub-brand--button-text-color--rgb), 0.25);
      }

    }

    &.button--tertiary {

      ---color-text: var(--sub-brand--button-color--rgb);

      background-color: RGB(var(--sub-brand--button-text-color--rgb));
      color: RGB(var(--sub-brand--button-color--rgb));
      box-shadow: 0 0 0 1px RGBA(var(---color-text), 0.25);

      &:hover,
      &:active {
        background-color: RGB(var(--sub-brand--subheading-color--rgb));
        color: RGB(var(--sub-brand--button-text-color--rgb));
        box-shadow: 0 0 0 1px RGBA(var(--sub-brand--button-text-color--rgb), 0.25);
      }

    }

  }

  /* ----- Tabs ----- */

  .tabs-nav {
    .tabs-nav__position {
      color: RGB(var(--sub-brand--button-color--rgb));
    }
  }


  /* ----- Swatches ----- */

  .color-swatch {
    .color-swatch__item {
      &:after {
        --text-color: var(--sub-brand--subheading-color--rgb);
      }
    }
  }

  .block-swatch {

    // --background: var(--sub-brand--secondary-background-color--rgb);
    // --secondary-background: var(--sub-brand--button-color--rgb);
    --text-color: var(--sub-brand--heading-color--rgb);

    &:not(.is-disabled) {

      .block-swatch__item {
        ---color-primary: var(--sub-brand--button-color); // Move to global style.
        --text-color: var(--sub-brand--heading-color--rgb);
        outline: 1px solid RGBA(var(--sub-brand--subheading-color--rgb), 0.5);

        &:focus,
        &:hover {
          +.block-swatch__item {
            --text-color: var(--sub-brand--subheading-color--rgb);
          }
        }

      }

    }

    input {
      &:checked {
        +.block-swatch__item {
          --text-color: var(--sub-brand--subheading-color--rgb);
        }
      }
    }

    &.is-disabled {
      .block-swatch__item {
        cursor: default;
      }
    }

  }

  /* ----- Product Card ----- */

  .product-item {
    .product-item-meta__title {
      color: RGB(var(--sub-brand--heading-color--rgb));
    }

    .product-item-meta__price-list-container {
      .price {
        color: RGB(var(--sub-brand--subheading-color--rgb));
      }
    }

    .product-item__cta {}

    &.product-item--slider .product-item__cta-wrapper {

      .block-swatch {
        &:not(.is-disabled) {

          .block-swatch__item {
            color: RGB(var(--sub-brand--button-color--rgb));
            border-color: RGB(var(--sub-brand--button-color--rgb));

            &:hover,
            &:active {
              color: RGB(var(---color-text--reversed--rgb));
              background-color: RGB(var(--sub-brand--button-color--rgb));
            }
          }
  
        }
      }

      

      >.button {
        color: RGB(var(--sub-brand--button-color--rgb));

        &:hover,
        &:active {
          background: transparent
        }

        svg {
          * {}
        }
      }
    }

  }

}