/*  -----------------------------------
    Text Size
    ----------------------------------- */

.text--small {
  font-size: var(---font-size-body--desktop);
}

.text--xsmall {
  font-size: var(---font-size-body-small--desktop);
}

.text--xxsmall {
  font-size: var(---font-size-body-xs--desktop);
}

.text--large {
  font-size: var(---font-size-body-large--desktop);
}

@minclude respond-to($small-up) {

  .text--small {
    font-size: var(---font-size-body--mobile);
  }

  .text--xsmall {
    font-size: var(---font-size-body-small--mobile);
  }

  .text--xxsmall {
    font-size: var(---font-size-body-xs--mobile);
  }

  .text--large {
    font-size: var(---font-size-body-large--mobile);
  }

}

/*  -----------------------------------
    Links
    ----------------------------------- */

.link--animated {
  &.link--animated--spaced {
    &:after {
      bottom: -0.25em;
    }
  }
  &.link--animated--bold {
    font-weight: var(---font-weight-body--bold);
    &:after {
      height: 2px;
    }
  }
  &.link--animated--show-underline {
    &:after {
      transform: scaleX(1);
    }
    &:hover, &:focus {
      &:after {
        transform: scaleX(0);
      }
    }
  }
}


/*  -----------------------------------
    Icon with Text
    ----------------------------------- */

[dir=ltr] .icon-text svg,
[dir=ltr] .icon-text img {
    // margin-right: 5px;
}
