
/*  ------------------------------
    1. Inputs
    ------------------------------ */

// 1.1. General

textarea,
select,
input[type="text"],
input[type="number"],
input[type="email"],
input[type="tel"],
input[type="password"],
// input[type="search"],
input[type="date"] {

  // @include input-structure();

  &[disabled] {

    // background: transparent;
    cursor: not-allowed;

  }

}

.input-row {
  position: relative;
  width: 100%;
}

.input-row--compact {

  --form-input-field-height: 40px;
  // height: 39px;
  display: flex;

  >.input {
    &:not(:first-child) {
      * {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
    }

    &:not(:last-child) {
      * {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
    }
  }

  >.input+.input {
    margin: 0;
  }

  .input {
    button {
      margin: 0;
    }
  }

  .button {
    height: calc(var(--form-input-field-height) - 2px);
    margin: 1px 0 !important;
  }

  input {
    &:focus-within {
      ~.input__label {
        transform:
          scale(.733) translateY(calc(-1 * var(--form-input-field-height) / 2 - 5px)) translate(3.665px);
      }
    }
  }

  .input__label {}

  .input {
    width: auto;
  }

}


.input {

  .button {
    margin: 0;
  }

  .input__label {

    letter-spacing: var(---label-letter-spacing);
    font-weight: var(---label-font-weight);
    color: var(---label-color);

    font-size: var(---font-size-label--mobile);

    @include respond-to($small-up) {
      font-size: var(---font-size-label--desktop);
    }

  }

  &:not(.input--floating) {
    width: 100%;
  }

  &.input--floating {

    display: flex;
    align-items: center;

    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    height: 100%;
    
  }

}

.input__field {
  @include input-style();
}

.input__field--xtiny {

  height: var(--form-input-field-height);
  padding: 0.8em 1.2em;

  min-width: unset;
  text-transform: none;
  letter-spacing: var(---letter-spacing-body--mobile);
  font-size: var(---font-size-button-small--mobile);

}

// 1.2. Selects

select {
  
  appearance: none;
  background: transparent;
  background-image: var(---icon--chevron-down);
  background-position: right 1em top 50%;
  background-repeat: no-repeat;
  background-size: 14px;

  @include input-style();

}

option,
optgroup {
  font-size: 1rem;
}

.hint {
  margin-top: 0.5em;
  font-size: var(---font-size-body-small--mobile);
  font-style: italic;
  @include respond-to($medium-up) {
    font-size: var(---font-size-body-small--desktop);
  }
}

.select-wrapper {

  .select {

    color: var(---color-text--dark);
    border: 1px solid var(---color-line);
    border-radius: 0;

  }

}

// 1.3. Search

input[type="search"] {

  background-image: var(---icon-search);
  background-repeat: no-repeat;
  background-position: right 15px center;

}

/*  ------------------------------
    2. Labels
    ------------------------------ */

label {
  @include label-structure();
}

.label-style {
  @include label-style();
}


/*  ------------------------------
    3. Fieldsets
    ------------------------------ */

fieldset {

  display: block;
  appearance: none;
  border: none;
  margin: 0;
  padding: 0;

}

.italic {
  font-style: italic;
}