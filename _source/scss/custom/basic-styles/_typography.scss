// Headings

h1, .h1,
h2, .h2,
h3, .h3,
h4, .h4 {
  @include heading-style--1();
}

h5, .h5,
h6, .h6 {
  @include heading-style--1();
}

h1, .h1 {
  font-size: var(--font-size-h1);
}
h2, .h2 {
  font-size: var(--font-size-h2);
}
h3, .h3 {
  font-size: var(--font-size-h3);
}
h4, .h4 {
  font-size: var(--font-size-h4);
}
h5, .h5 {
  font-size: var(--font-size-h5);
}
h6, .h6 {
  font-size: var(--font-size-h6);
}

h1, .h1,
.rte h1, .rte .h1 {
  line-height: 1.2;
}

.h1--mobile {
  @include respond-to($small-down){
    font-size: var(---font-size-h1--mobile) !important;
  }
}

.h2--mobile {
  @include respond-to($small-down){
    font-size: var(---font-size-h2--mobile) !important;
  }
}

.h3--mobile {
  @include respond-to($small-down){
    font-size: var(---font-size-h3--mobile) !important;
  }
}

.h4--mobile {
  @include respond-to($small-down){
    font-size: var(---font-size-h4--mobile) !important;
  }
}

.h5--mobile {
  @include respond-to($small-down){
    font-size: var(---font-size-h5--mobile) !important;
  }
}

.h6--mobile {
  @include respond-to($small-down){
    font-size: var(---font-size-h6--mobile) !important;
  }
}

.heading.heading--small {

  @include subheading-style();
  
  + p,
  + .h1,
  + h1,
  + .h2,
  + h2,
  + .h3,
  + h3,
  + .h4,
  + h4 {
      margin-top: 12px;
  }

  + hr {
    margin-top: 0;
  }

  font-size: var(---font-size-subheading--mobile);
  @include respond-to($small-up) {
    font-size: var(---font-size-subheading--desktop);
  }

}

.subheading.heading--xsmall,
.heading.heading--xsmall {

  @include subheading-style();
  font-size: var(---font-size-subheading-small);

}

.heading.heading--xsmall,
.subheading {
  color: RGB(var(--subheading-color, var(--heading-color))) !important;
}

.h3--mobile {
  @include respond-to($small-down) {
    font-size: var(---font-size-h3--mobile);
  }
}


// Text Content

p:not(.heading),
.p:not(.heading) {

  margin-top: 0;
  margin-bottom: 1em;

  font-size: var(---font-size-body--mobile);
  font-family: var(---font-family-body);
  font-weight: var(---font-weight-body);
  font-variation-settings: 'wght' 400;

  @include respond-to($small-up){
    font-size: var(---font-size-body--desktop);
  }

  &.text--xxsmall,
  &.tiny {
    font-size: var(---font-size-body-xs--mobile);
    @include respond-to($small-up){
      font-size: var(---font-size-body-xs--desktop);
    }
  }

  &.text--xsmall,
  &.minor {
    font-size: var(---font-size-body-small--mobile);
    font-variation-settings: 'wght' 300;
    @include respond-to($small-up){
      font-size: var(---font-size-body-small--desktop);
    }
  }

  &.text--large,
  &.major {
    font-family: var(--heading-font-family--alternate);
    font-size: var(---font-size-body-large--mobile);
    @include respond-to($small-up){
      font-size: var(---font-size-body-large--desktop);
    }
  }

}

.p--mobile {

  @include respond-to($small-down){

    font-size: var(---font-size-body--mobile);
    font-family: var(---font-family-body);
    font-weight: var(---font-weight-body);


  }

}

.text--xxsmall,
.tiny {
  font-size: var(---font-size-body-xs--mobile);
  @include respond-to($small-up){
    font-size: var(---font-size-body-xs--desktop);
  }
  p {
    font-size: var(---font-size-body-xs--mobile);
    @include respond-to($small-up){
      font-size: var(---font-size-body-xs--desktop);
    }
  }
}

.text--xxsmall--mobile,
.p-tiny--mobile {

  @include respond-to($small-down){
    font-family: var(---font-family-body);
    font-weight: var(---font-weight-body);

    font-size: var(---font-size-body-xs--mobile);
  }

}

.text--xsmall,
.minor {
  font-variation-settings: 'wght' 300;
  font-size: var(---font-size-body-small--mobile);
  @include respond-to($small-up){
    font-size: var(---font-size-body-small--desktop);
  }
  p:not(.heading) {
    font-variation-settings: 'wght' 300;
    font-size: var(---font-size-body-small--mobile);
    @include respond-to($small-up){
      font-size: var(---font-size-body-small--desktop);
    }
  }
}

.text--small--mobile,
.p-minor--mobile {

  @include respond-to($small-down){
    font-size: var(---font-size-body-small--mobile);
    font-family: var(---font-family-body);
    font-weight: var(---font-weight-body);

  }

}

.text--large,
.major {
  font-family: var(--heading-font-family--alternate);
  font-size: var(---font-size-body-large--mobile);
  @include respond-to($small-up){
    font-size: var(---font-size-body-large--desktop);
  }
  p:not(.heading) {
    font-family: var(--heading-font-family--alternate);
    font-size: var(---font-size-body-large--mobile);
    @include respond-to($small-up){
      font-size: var(---font-size-body-large--desktop);
    }
  }
}

.text--large--mobile,
.p-major--mobile {

  font-family: var(--heading-font-family--alternate);

  @include respond-to($small-down){
    font-size: var(---font-size-body-large--mobile);
    font-family: var(---font-family-body);
    font-weight: var(---font-weight-body);

  }

}

.text--subdued {
  font-weight: 300;
  color: var(---color--kyte-dark-grey);
}

strong, .strong {

  --color: var(---color-text-dark);

  font-weight: var(---font-weight-body--bold);
  font-variation-settings: "wght" var(---font-weight-body--bold);

}

// Links

p a:not(.button), 
.rte a:not(.button) {
  
  // --text-color: var(---color-link--rgb);
  // color: RGB(var(--text-color));

}

.link--animated {
  display: inline-block;
}

.link__icon {
  
  display: inline;
  line-height: 0;
  vertical-align: middle;

  svg {
    display: inline-block;
  }

  .icon--custom-external {
    opacity: 0.25;
  }

}