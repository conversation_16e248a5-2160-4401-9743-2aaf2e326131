.rte table,
.table {

  width: 100%;
  border-collapse: collapse;
  text-align: left;

  th {
    padding: calc(var(--table-spacing) / 2);
  }

  td {
    vertical-align: top;
    padding: calc(var(--table-spacing) / 2);
  }

  th,
  td {

    &:not([class*="text--"]){

      &:first-child {
        text-align: left;
      }
      
      &:last-child {
        text-align: right;
      }

    }

  }

  td {

    
    
    &:first-child {
      font-weight: bold;
    }

  }

  tr {

    &:hover {
      background: var(---background-color--default);
      td {
        background: transparent;
      }
    }

  }

  &.table--style-1 {



  }

  &.table--style-2 {



  }

  &.table--style-3 {

    

  }

}
