/*  ==============================
    1. Root Styles
    ============================== */

* {
  box-sizing: border-box;
}

::selection {
  color: var(---color--primary);
  background: var(---background-color--primary);
}

html,
body {

  font-family: var(---font-family-body);

  font-weight: var(---font-weight-body);
  font-style: var(---font-style-body);
  letter-spacing: var(---letter-spacing--body);

  font-size: var(---font-size-body--mobile);
  line-height: var(---line-height-body--mobile);

  @include respond-to($medium-up) {
    font-size: var(---font-size-body--desktop);
    line-height: var(---line-height-body--desktop);
  }

  scroll-behavior: smooth;

  // overflow-x: hidden;

}