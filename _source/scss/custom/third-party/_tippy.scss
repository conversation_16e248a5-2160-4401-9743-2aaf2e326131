.tippy-content {
  
  padding: 1em;
  text-align: center;

  p {
    font-size: var(---font-size-body-small--mobile);
  }

}

.tippy-box {

  --tooltip-box-shadow: 0 3px 10px rgba(0, 0, 0, .10);

  font-size: var(---font-size-body--mobile);

  background: var(--tooltip-background-color);
  color: var(--tooltip-text-color);
  border-radius: var(--tooltip-border-radius);
  box-shadow: var(--tooltip-box-shadow);
  // outline: 1px solid var(--tooltip-text-color);

  @include respond-to($medium-up) {
    font-size: var(---font-size-body--desktop);
  }

  &[data-placement^=bottom] {
    > .tippy-arrow {
      &:before {
        border-bottom-color: var(--tooltip-background-color);
      }
    }
  }

  &[data-theme~='kyte--light'] {

    --tooltip-background-color: var(---color--brand-4);
    --tooltip-text-color: var(---color-text);
    --tooltip-border-radius: var(--block-border-radius);
    // --tooltip-box-shadow: var(0 3px 10px rgba(0,0,0,.10));
    
    
  }

  &[data-theme~='kyte--dark'] {

    --tooltip-text-color: var(---color--brand-3);
    --tooltip-background-color: var(---color-text);
    --tooltip-border-radius: var(--block-border-radius);
    
  }

}