.quantity-selector {

  overflow: visible;

  border: 0 !important;
  outline: 1px solid RGB(var(--border-color)) !important;

  .quantity-selector__button {
    &:first-child {
      border-top-left-radius: var(--button-border-radius);
      border-bottom-left-radius: var(--button-border-radius);
    }
    &:last-child {
      border-top-right-radius: var(--button-border-radius);
      border-bottom-right-radius: var(--button-border-radius);
    }
  }

  span.quantity-selector__button {
    opacity: 0.8;
    cursor: not-allowed;
  }

  .quantity-selector__input {
    @include respond-to($medium-down) {
      padding: 0 !important;
      min-width: 50px !important;
    }
  }

  &.quantity-selector--small {
    --quantity-selector-height: 40px;
  }

}

// Quantity Selector with Add to Cart

.quantity-selector-atc-container {
  
  display: grid;
  grid-auto-flow: row;
  gap: var(--vertical-gap);

}

.quantity-selector-atc {
  
  display: flex;
  gap: 10px;

  .product-form__payment-container {
    width: 100%;
  }

  .button {
    height: 60px;
    max-height: 60px;
  }

  .product-form__add-button {
    
    // padding: 1.4em 2em !important;
    display: flex;
    gap: 20px;

    .icon--custom-cart {

      min-width: 22px;
      min-height: 22px;
      align-items: center;
      color: inherit;
      stroke: none;

    }

    .loader-button__text {
      gap: 15px;
    }

  }

  .price {
    color: var(---color-text--reversed);
  }

  @include respond-to($small-down){
    flex-direction: column;
    align-items: flex-start;
    .quantity-selector__button {
        height: 100%;
        min-height: 48px;
    }
    .quantity-selector {
        display: inline-flex;
    }
  }

}

.quantity-selector-atc__qty {

  display: flex;

  input {
    min-width: 64px !important;
    @include respond-to($medium-down) {
      min-width: 50px !important;
      padding: 0 !important;
    }
  }

  .quantity-selector__button {
    margin: 0;
    padding: 0;
    height: 100%;
    text-align: center !important;
    justify-content: center;
    min-width: 64px !important;

    @include respond-to($medium-down) {
      min-width: 50px !important;
    }

    &:first-child {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    &:last-child {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
}

.product-form__gift-card-recipient {

  --background: var(---background-color--content-2--rgb);
  --section-block-background: var(---background-color--content-2--rgb);

  padding: 20px;
  margin: 20px 0;

  background-color: RGB(var(---background-color--content-2--rgb));
  border-radius: var(--block-border-radius);

  .input__field {
    // --background: var(---background-color--content-1--rgb);
    // background: RGB(var(--background));
  }

  select,
  input {
    // background: RGB(var(--background));
  }

}