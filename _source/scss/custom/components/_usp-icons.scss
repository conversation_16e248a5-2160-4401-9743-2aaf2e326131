.usp-icons {

  --gap: 20px;
  --padding: 30px;
  
}

.usp-icons__inner {
  display: flex;
  
  gap: var(--gap);

  padding-inline-start: var(--padding);
  padding-inline-end: 100vw;
  // padding-inline-end: var(--padding);
  margin-inline-start: calc(-1 * var(--padding));
  margin-inline-end: calc(-1 * var(--padding));

  flex-wrap: nowrap;
  overflow: scroll;
  gap: 10px;
  scroll-snap-type: x mandatory;

  @include respond-to($medium-up) {
    flex-wrap: wrap;
    padding-inline-start: 0;
    padding-inline-end: 0;
  }

}

.usp-icon {

  display: flex;
  gap: 0.5em;

  scroll-snap-align: start;
  scroll-snap-stop: always;

  padding-inline-start: var(--padding);
  
  @include respond-to($medium-down) {
    flex: 1 0 auto;
  }

  @include respond-to($medium-up) {
    max-width: calc(50% - var(--gap));
    padding-inline-start: var(--padding);
    gap: 0.25em;
  }

  .usp-icon__icon {
    // display: flex;
    // flex: 1 0 50px;
    // width: 50px;

    svg,
    img {
      margin: 0;
      width: 50px;
      min-width: 50px;
    }
  }

  .usp-icon__details {
    
    display: flex;
    justify-content: center;
    flex-direction: column;
    flex: 1 0 auto;
    gap: 0.25em;

  }

}

.image-overlay__usp-container {
  margin-top: 24px;
}