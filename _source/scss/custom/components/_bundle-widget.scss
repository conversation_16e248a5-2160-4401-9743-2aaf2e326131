.bundle-widget {

  --gap: var(--agap, 15px);

  display: flex;

  align-items: flex-start;
  background: var(---background-color--content-2);
  border: 1px solid var(---color-line);
  border-radius: var(--block-border-radius);
  display: flex;


}

.bundle-widget__header {
  display: flex;
  gap: var(--gap);
  padding-bottom: var(--gap);
  border-bottom: 1px solid var(---color-line);
}

.bundle-widget__header-text {
  > * {
    margin: 0 !important;
  }
}

.bundle-widget__products {
  display: flex;
  flex-direction: column;
  gap: var(--gap);
}

.bundle-widget__inner {
  display: flex;
  flex-direction: column;
  width: 100%;

  >* {
    width: 100%;
    padding: var(--gap);
  }

}

.bundle-widget__footer {
  display: flex;
  justify-content: center;
  
  .button {
    font-weight: 300;
    letter-spacing: 0;
    gap: 0.4em;

    .price {
      color: currentColor;
    }

    &.button--text {
      line-height: 1;
    }
  }

}


.bundle-widget-product {

  --gap: var(--agap, 15px);

  border-radius: var(--block-border-radius);

  overflow: hidden;

  border: 1px solid var(---color-line);

  .bundle-widget-product__inner {
    display: flex;
    flex-direction: colunn;
    gap: var(--gap);
  }

  .bundle-widget-product__image {
    max-width: 80px;
    min-width: 80px;
    width: 80px;
    flex: 80px;
    border-right: 1px solid var(---color-line);
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .bundle-widget-product__details {
    width: 100%;
    padding-block: var(--gap);

    >* {
      margin: 0;
    }
  }

  .bundle-widget-product__price {
    line-height: 1;
    .price {
      font-size: var(--font-size-xs);
      color: var(---color-price);
    }
  }

  .bundle-widget-product__actions {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 0.5em;

    line-height: 1;
    align-items: center;
    margin-top: 0.5em !important;
  }

}



.product-form__strip-banner {
  .product-form-banner__content {
    > * {
      margin: 0;
    }
  }
}