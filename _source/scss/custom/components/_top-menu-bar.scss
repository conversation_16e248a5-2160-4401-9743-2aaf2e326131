.top-menu-bar {

  .top-menu-bar__inner {
    border-bottom: 1px solid var(---color-line);
    color: var(---color-text);
  }

  .header__linklist {
    height: 48px;
    justify-content: flex-end;
    align-items: center;

    font-size: var(---font-size-body-small--desktop);
  }

  .top-menu-bar__info-links {
    gap: var(--tabbed-header-gap);
  }

  /* Sub Brand */

  .top-menu-bar__inner {
    display: flex;
    justify-content: space-between;
  }

  .top-menu-bar__sub-brand-links {
    margin-right: auto;
  }

  .top-menu-bar__info-links {
    margin-left: auto;
  }

}



/* -------------------- TABBED MENU BAR -------------------- */

.top-menu-bar.tabbed-top-menu-bar {

  color: RGB(var(--link-color));
  background-color: RGB(var(--background-color-2));
  border-bottom: 1px solid RGB(var(--border-color));

  /* ----- Basic Styles ----- */

  a:not(.tabbed-header-logo) {
    color: RGB(var(--link-color));
  }

  /* ----- Components ----- */

  .top-menu-bar__inner {
    min-height: 55px;
    border-bottom: 0;
    align-items: center;
  }

  /* ----- Menu ----- */

  .header__linklist-item {
    margin: 0;
  }

  .header__linklist-link {
    position: relative;
    display: flex;
    align-items: center;
    gap: 6px;
    color: RGB(var(--link-color));
  }

  .header__linklist-link-icon {
    opacity: 0.3;
    color: RGB(var(--icon-color));
  }

  /* ----- Sub Brands ----- */

  .top-menu-bar__sub-brand-logos {
    display: flex;
    align-items: stretch;
    flex-wrap: wrap;
    padding: 0;
  }

}



/* -------------------- Logos -------------------- */

.tabbed-header-logo {

  --transition-duration: 0.25s;

  position: relative;
  display: inline-flex;
  max-width: 200px;
  min-width: 200px;
  padding: 10px 25px;
  margin-right: -1px;

  border-left: 1px solid RGB(var(--border-color));
  border-right: 1px solid RGB(var(--border-color));
  border-top: 3px solid RGBA(var(--logo-color), 0.2);

  background-color: RGB(var(--background-color-2));

  transition: var(--transition-duration) background-color;

  @include respond-to($large-up) {
    max-width: 200px;
    min-width: 200px;
  }

  .tabbed-header-logo__logo {
    height: auto !important;
    svg, img {
      height: auto !important;
    }
  }

  img,
  svg {
    display: block;
    width: 100%;
  }

  svg {
    * {
      fill: RGB(var(--logo-color));
    }
  }

  .tabbed-header-logo__inner {
    opacity: 0.5;
    transition: var(--transition-duration) opacity;
  }

  &:hover,
  &.tabbed-header-logo--active {
    background-color: RGB(var(--background-color));
    .tabbed-header-logo__inner {
      opacity: 1;
    }
  }

  &.tabbed-header-logo--active {
    &:after {
      content: '';
      position: absolute;
      display: block;
      width: 100%;
      height: 4px;
      left: 0;
      bottom: 0;
      transform: translateY(1px);
      background-color: RGB(var(--background));
    }
  }

  &.tabbed-header-logo--image {
    &:before {
      content: '';
      display: block;
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: RGB(var(--logo-color));
      mix-blend-mode: lighten;
    }
  }

}

.top-menu-bar__sub-brand-logos {
  margin: 0;
}