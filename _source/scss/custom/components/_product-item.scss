.product-list__inner {
  // overflow: visible;
}

.scroller {
  .product-list__inner {
      // gap: 0 !important;
      // overflow: visible;
  }
}
.product-item {

  .product-form__option-link,
  .product-usps-container,
  .product-media-banner-carousel,
  swatch-collections {
    display: none;
  }
}

/*  -----------------------------------
    Product Item
    ----------------------------------- */

.product-item {

  .product-item__image-wrapper {
    border-radius: var(--block-border-radius);
    border: 1px solid var(---color-line);
    margin-bottom: 1em;
  }

  .product-item__label-list {
    
    right: 10px;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: flex-end;
  
  }

  .product-item-meta {
    text-align: left;
  }

  .product-item-meta__reviews-badge {
    margin: 0;
  }

  .product-item__secondary-image {
    object-fit: contain;
    top: 50%;
    width: 100%;
    height: 100%;
    position: absolute;
    left: 50%;
    transform: translate(-50%, -50%) !important;
    object-fit: cover;
  }

  native-video.product-item__secondary-image {
    display: block;
  }

  .product-item__primary-image,
  .product-item__secondary-image {
    background: var(---background-color--content-1);

    border-radius: 10px;

    @include respond-to($medium-down) {
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
      border-top-left-radius: 10px;
      border-top-right-radius: 10px;
    }
  }

  .product-item-meta__vendor {

    // color: var(---color-text--light) !important;
    color: var(---color-text--light);
  }

  .product-item-meta__title {

    // color: var(---color-products-title) !important;
    color: var(---color-products-title);
    font-weight: var(---font-weight--product-title);
    font-size: var(---font-size-product-title--mobile);
    white-space: wrap;

    @include respond-to($small-up) {
      font-size: var(---font-size-product-title--desktop);
    }

  }

  .product-item-meta__property-list {
    gap: 0.25em;
  }

  .product-item-meta__property {
    font-weight: var(---font-weight-body);
  }

  .product-item-meta__title {
    margin: 0;
  }

  .product-item-meta__price-list-container {
    font-weight: var(---font-weight--product-title);
    margin-top: 0;
  }

  // Button

  .button__icon {
    &:not(:only-child) {
      margin-left: auto;
    }
    svg {
      background-image: url("data:image/svg+xml,%3Csvg width='12' height='12' viewBox='0 0 12 12' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11.9953 5.94871C12.0245 6.57531 11.5218 7.08897 10.9024 7.08897H7.75855C7.3906 7.08897 7.08824 7.38769 7.08824 7.75928V10.9032C7.08824 11.5225 6.57093 12.0216 5.94798 11.9961C5.32503 11.9706 4.90244 11.4314 4.90244 10.834V7.75928C4.90244 7.39134 4.60372 7.08897 4.23213 7.08897H1.09188C0.472571 7.09261 -0.0265184 6.57531 -0.00101751 5.94871C0.0244834 5.32212 0.563646 4.90318 1.1611 4.90318H4.23578C4.60372 4.90318 4.90609 4.60445 4.90609 4.23287V1.09261C4.90609 0.473303 5.41975 -0.025786 6.04634 -0.000285093C6.67294 0.0252158 7.09188 0.564378 7.09188 1.16183V4.23651C7.09188 4.60445 7.3906 4.90682 7.76219 4.90682H10.8369C11.4343 4.90682 11.9735 5.35491 11.999 5.95236L11.9953 5.94871Z' fill='black'/%3E%3C/svg%3E%0A");
    }
  }

  // Loader Button

  .loader-button__text {
    width: 100%;
  }

  .loader-button__icon {
    margin-left: auto;
  }

  .product-item__primary-image {
    object-fit: cover;
  }

  .popover {
    .block-swatch-list {
      padding-left: 0;
      padding-right: 0;
    }
  }

  .block-swatch {
    &[disabled] {
      .block-swatch__item {
        cursor: default;
      }
    }
  }

  .product-item-meta__additional-text-fields {
    margin: 0.25em 0;
  }

/*  ----------------------------------------
    Standard
    ---------------------------------------- */

  .popover {
    .loader-button__text {
      justify-content: center;
    }
  }

  /*  ========================================
      Standard
      ======================================== */

  &.product-item--standard {



  }

  /*  ========================================
      Swatch Buttons
      ======================================== */

  &.product-item--swatch-buttons {

    position: relative;
    padding: calc(var(--grid-gap) / 2);

    .product-item__quick-add--mobile {
      border-top: 1px solid var(---color-line);
    }

    .product-item__cta {
      &.product-item__cta--single-variant {
        .button__icon {
          svg {
            background: none;
          }
        }
      }
    }

    @include respond-to($medium-up) {

      &:hover {
        .product-item__content-hover {
          display: block;
        }
      }

    }

    @include respond-to($medium-down) {

      padding: 0;

      border: 1px solid var(---color-line);
      border-radius: var(--block-border-radius);

      .product-item__image-wrapper {
        border: 0;
        border-bottom: 1px solid var(---color-line);
        border-radius: 0;
        margin-bottom: 8px;
      }

      .product-item__info {
        padding: 10px;
        padding-top: 0;
      }

      .product-item__cta {

        margin-top: 0;
        margin-bottom: 0;
        padding: 10px;
        width: 100%;

        align-items: center;

        background: var(---background-color--content-1);
        box-shadow: none;
        color: var(---color--primary);

        font-size: var(---font-size-body-small--mobile);
        
        border-radius: 0;
        border-bottom-left-radius: var(--block-border-radius);
        border-bottom-right-radius: var(--block-border-radius);

      }

      // Hiding elements added to popovers by new product-rerender method of showing quick buy popover content.

      .quantity-selector-atc {
        margin: 0;
        gap: 16px;
      }

      .giftreggie-pdp-cta-area {
        margin-top: 10px;
        margin-bottom: 0;
      }

      .product-form__option-link,
      .product-usps-container,
      .product-media-banner-carousel,
      swatch-collections {
        display: none;
      }


    }

    .product-item__content {
      position: relative;
      z-index: 0;

      .product-item__quick-add {
        display: none;
      }
    }

    .product-item__quick-add {
      
      text-align: left;
      margin-top: 1em;

    }

    .product-item__content-hover {

      padding: calc(var(--grid-gap) / 2);

      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      top: 0;

      z-index: 2;

      display: none;

    }

    .product-item__content-hover__inner {
      box-shadow: 0 0 10px rgba(0, 0, 0, .1);
      background-color: var(---background-color--content-1);
      border-radius: var(--block-border-radius);
      overflow: hidden;
      padding: 20px;
      margin-left: -20px;
      margin-right: -20px;
      margin-top: -20px;
    }


    // ----- Kyte Curates ----- 

    .product-item__image-wrapper {

      &:hover {
        .product-item__badge {
          transform: rotate(10deg);
        }
      }

    }

    .product-item__badge {

      position: absolute;
      z-index: 1;

      width: 40px;
      height: 40px;
      bottom: 10px;
      right: 10px;

      pointer-events: none;

      transition: transform 0.25s;
      transform: rotate(0deg);
      will-change: transform;

      @media (min-width: 1000px) {
        width: 60px;
        height: 60px;
        bottom: 20px;
        right: 20px;
      }

    }

  }

/*  ========================================
    Sliders
    ======================================== */

    &.product-item--swatch-buttons,
    &.product-item--slider {

      .block-swatch {
        margin: 0;

        .block-swatch__item {
          min-height: 40px;
          font-size: var(---font-size-body-small--desktop);
        }

        &:not(.is-disabled) {
          .block-swatch__item {
            background-color: RGB(var(--secondary-background));
            color: RGB(var(---color-text--rgb));
            &:focus,
            &:hover {
              background-color: RGB(var(--primary-button-background));
              color: RGB(var(--primary-button-text-color));
            }
          }
        }

        &.is-disabled {
          .block-swatch__item {
            background-color: transparent;
            cursor: default;
          }
        }

      }
    }

    &.product-item--upsell {

      .product-item__info {
        background-color: RGB(var(--secondary-background));
      }

      .block-swatch {
        margin: 0;

        .block-swatch__item {
          min-height: 40px;
          font-size: var(---font-size-body-xs--desktop);
        }

        &:not(.is-disabled) {
          .block-swatch__item {
            background-color: RGB(var(--background));
            color: RGB(var(---color-text--rgb));
            &:focus,
            &:hover {
              background-color: RGB(var(--primary-button-background));
              color: RGB(var(--primary-button-text-color));
            }
          }
        }

        &.is-disabled {
          .block-swatch__item {
            background-color: transparent;
            cursor: default;
          }
        }

      }
    }

    &.product-item--slider {

      margin: 5px;

      background-color: var(---background-color--content-1);
      border-radius: var(--block-border-radius);
      box-shadow: 0 0 8px rgba(0, 0, 0, .075);

      overflow: hidden;

      .product-item__info {

        padding: 15px !important;
        height: 100%;

      }

      .product-item__cta-wrapper {
        border-top: 1px solid var(---color-line);

        > form > .button,
        > .button {
    
          padding: 15px 20px;
          margin: 0;
          border-radius: 0;
          width: 100%;
    
          background-color: var(---background-color--content-1);
          color: var(---color--primary);
          border: 0;
          box-shadow: none;

          font-size: var(---font-size-body-small--mobile);

          @include respond-to($medium-up) {
            font-size: var(---font-size-body-small--desktop);
          }
    
          &:focus,
          &:active,
          &:hover {
            box-shadow: none;
          }
    
        }

      }

      // Swatches

      .variant-container {
        > .block-swatch-list {
          padding: 20px;
          padding-top: 0;
        }
      }

    }

/*  ========================================
    Upsells
    ======================================== */

  &.product-item--upsell {

    overflow: hidden;

    padding: 0 !important;
    flex-direction: column;
    margin-top: 10px;

    padding: 20px;
    border-radius: 12px;
    background: RGB(var(--secondary-background));

    .product-item__info {
      
      padding: 15px !important;
      margin-top: 5px;
      height: 100%;

      @include respond-to($small-up) {
        flex-direction: column;
      }

      @include respond-to($small-down) {
        width: 100%;
        flex-direction: row;
      }
    }

    .product-item__image-wrapper {
      @include respond-to($small-down) {
        width: 70px;
        margin-right: 15px;
      }
      @include respond-to($small-up) {
        width: 100%;
      }
    }

    @include respond-to($small-down) {

      align-items: flex-start;

      .product-item__info {
        margin-top: 5px;
        height: 100%;
      }

      .product-item__aspect-ratio {
        width: 70px;
        margin: 0;
        aspect-ratio: 2/3;
      }

    }
    
    .product-item__cta-wrapper {
      
      margin-top: auto;
      width: 100%;
      border-top: 1px solid var(---color-line);

      .product-item__link {
        align-items: center;
      }
      
    }
  
    button.product-item__link {

      position: relative;
      
      display: flex;
      justify-content: space-between;
      width: 100%;
      margin-top: 0;
      margin: 0;
      padding: 10px 20px;

      background: RGB(var(--secondary-background));
      transition: 0.25s border-color, 0.25s color, 0.25s background-color;
      
      &:hover {
        border-color: var(---color-line--dark);
      }

      .loader-button__text {
        width: 100%;
      }

      .button__text {
        justify-content: flex-start;
      }

    }

    // Swatches

    .block-swatch-list {
      padding: 5px 20px 20px;
    }

  }

}

[dir=ltr] .product-item__label-list {
  left: auto;
  right: 10px;
}