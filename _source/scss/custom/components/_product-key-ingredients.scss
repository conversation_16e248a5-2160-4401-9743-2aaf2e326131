.product-key-ingredients {

  margin-bottom: 20px;  

}

.product-key-ingredients__inner {

  --gap: 20px;
  --padding: 30px;

  @include respond-to($medium-up) {
    display: grid;
    grid-auto-flow: row;
    gap: var(--gap);
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

}

.product-key-ingredient {

  @include respond-to($medium-down) {
    margin-bottom: 20px;
  }
  
  .product-key-ingredient__inner {
    
    display: flex;
    gap: 0.5em;
  
    @include respond-to($medium-down) {
  
    }
  
    @include respond-to($medium-up) {
      
    }

  }
  
  .product-key-ingredient__title {
    margin-block-end: 0.25em;
  }

  .product-key-ingredient__icon {
    svg,
    img {
      margin: 0;
      width: 50px;
      min-width: 50px;
      @include respond-to($medium-up) {
        width: 60px;
        min-width: 60px;
      }
    }
  }

  .product-key-ingredient__details {
    
  }

}