/* ----- Checkbox ----- */

.checkbox {
  --heading-color: var(---color--primary--rgb);
}

/* ----- Checkbox Container ----- */

.checkbox-container {

  input {

    &:not([disabled]) {
      --text-color: var(---color-link--rgb);

      +label {
        transition: color 0.25s;

        &:hover {
          --text-color: var(---color-link--rgb);
        }

      }
    }

    &[disabled] {
      opacity: 0.25;

      +label {
        opacity: 0.25;
      }
    }
  }

  .checkbox {
    height: 18px;
    width: 18px;

    background-color: transparent;
    border-radius: 4px;
    border-color: var(---color-line--dark);

    &.checked,
    &:checked {
      background-image: url("data:image/svg+xml,%3Csvg width='12' height='10' viewBox='0 0 12 10' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11.7647 1.50542L4.79326 9.6845C4.63079 9.87499 4.39309 9.99107 4.14035 10C4.12831 10 4.11628 10 4.10123 10C3.86353 10 3.63186 9.90773 3.46336 9.73808L0.264975 6.57122C-0.129182 6.18131 -0.0840494 5.52055 0.403381 5.19613C0.764441 4.95504 1.25789 5.02648 1.56479 5.33304L3.33699 7.08613C3.71911 7.46413 4.34796 7.43734 4.69698 7.02958L10.4198 0.317849C10.7507 -0.0720559 11.3465 -0.110749 11.7286 0.24344C12.0837 0.573818 12.0837 1.13933 11.7677 1.5084L11.7647 1.50542Z' fill='%239A9A9A'/%3E%3C/svg%3E%0A");
    }
  }

  label,
  .checkbox-label {
    color: RGB(var(--text-color));
    line-height: 1.2;
    position: relative;
    top: -1px;
  }

}

