.product-media-banner-carousel {
    
  border: 1px solid var(---color-line);
  overflow: hidden;
  border-radius: 12px;
  position: relative;
  
  .dots-nav {
      transform: translateY(-50%);
      margin: auto;
      padding-bottom: 10px;
  }

.product-media-banner {
  margin: auto;
  border: 0;
  
}

.product-media-banner-carousel__list {
  
  display: flex;
  scroll-snap-type: x mandatory;

  @media (max-width: 1000px) {
  // @include respond-to($medium-down) {
    margin-left: calc(var(--container-gutter)*-4);
    margin-right: calc(var(--container-gutter)*-4);
  }
  @media (min-width: 1000px) {
  // @include respond-to($medium-up) {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 50px;
  }
}

.product-media-banner-carousel__item {
  
  display: block;
  flex: none;
  scroll-snap-align: center;
  scroll-snap-stop: always;
  width: 100%;

}

.product-media-banner-carousel__item-inner {
  display: flex;
}

.product-media-banner-carousel__icon {
  min-width: 50px;
  max-width: 50px;
  max-height: 50px;
  img {
    height: 100%;
    object-fit: contain;
  }
}

.product-media-banner-carousel__item-description {
  margin-left: 1em;
}

.product-media-banner-carousel__item-heading {
  margin-bottom: 0.5em;
}

.product-media-banner-carousel__dots {
  margin-top: 20px;
}

}