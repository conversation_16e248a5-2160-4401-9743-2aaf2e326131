.note {

  // --note-color: var(---color--default);
  // --note-border-color: var(---color--default);
  // --note-background-color: var(---background-color--default);
  --note-color: var(---color--default);
  --note-border-color: var(---color--secondary);
  --note-background-color: var(---background-color--tertiary);

  display: flex;
  align-items: flex-start;
  gap: 10px;

  // border-left-width: 2px;
  // border-left-style: solid;
  // border-left-color: var(--note-border-color);

  // border: 1px solid var(--note-border-color);
  padding: 1em 1.2em;
  margin: 1em auto;

  border-radius: 4px;

  color: var(--note-color);
  background-color: var(--note-background-color);

  font-size: var(---font-size-body--mobile);

  @include respond-to($medium-up) {
    font-size: var(---font-size-body--desktop);
  }

  &:first-child {
    margin-top: 0;
  }

  &:last-child {
    margin-bottom: 0;
  }

  p {

    color: var(--note-color);

    &:last-child {
      margin: 0;
    }

  }

  .note__content {}

  /* ----- Styles ----- */

  &.note--primary {
    --note-color: var(---color--primary);
    --note-border-color: var(---color--primary);
    --note-background-color: var(---background-color--primary);
  }

  &.note--secondary {
    --note-color: var(---color--secondary);
    --note-border-color: var(---color--secondary);
    --note-background-color: var(---background-color--secondary);
  }

  &.note--tertiary {
    --note-color: var(---color--tertiary);
    --note-border-color: var(---color--tertiary);
    --note-background-color: var(---background-color--tertiary);
  }

  &.note--brand-1 {
    --note-color: var(---color--tertiary);
    --note-border-color: var(---color--tertiary);
    --note-background-color: var(---background-color--tertiary);
  }

  &.note--success {
    --note-color: var(---color--success);
    --note-border-color: var(---color--success);
    --note-background-color: var(---background-color--success);
  }

  &.note--warning {
    --note-color: var(---color--warning);
    --note-border-color: var(---color--warning);
    --note-background-color: var(---background-color--warning);
  }

  &.note--danger {
    --note-color: var(---color--danger);
    --note-border-color: var(---color--danger);
    --note-background-color: var(---background-color--danger);
  }

  &.note--info {
    --note-color: var(---color--info);
    --note-border-color: var(---color--info);
    --note-background-color: var(---background-color--info);
  }

  &.note--error {
    --note-color: var(---color--danger);
    --note-border-color: var(---color--danger);
    --note-background-color: var(---background-color--danger);
  }

  /* ----- Sizes ----- */

  &.note--large {
    padding: 1.4em 1.8em;

    font-size: var(---font-size-body-large--mobile);

    @include respond-to($medium-up) {
      font-size: var(---font-size-body-large--desktop);
    }
  }

  &.note--small {
    padding: .8em 1.4em;

    font-size: var(---font-size-body-small--mobile);

    @include respond-to($medium-up) {
      font-size: var(---font-size-body-small--desktop);
    }
  }

  &.note--tiny {
    padding: .6em 0.8em;

    font-size: var(---font-size-body-xs--mobile);

    @include respond-to($medium-up) {
      font-size: var(---font-size-body-xs--desktop);
    }
  }

}




// Default Theme Banners

.banner {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 12px 16px;

  border: 1px solid var(--text-color);

  &.banner--small {

    display: flex;
    gap: 0.5em;
    margin: 10px 0;
    padding: 0.5em 1em;
    align-items: center;

    .banner__ribbon {
      margin: 0;
    }

    .banner__content {
      font-size: var(---font-size-body-small--mobile);

      @include respond-to($medium-up) {
        font-size: var(---font-size-body-small--desktop);
      }
    }

  }

}