[dir=ltr],
[dir=rtl] {

  .rte  {

    /* ----- Links ----- */

    a, .a {

      &:not(.button) {

        cursor: pointer;
        transition: color 0.1s ease-in-out;

        &:hover {
          color: var(---color-link--hover);
        }

        &.subheading {

          text-decoration: none;

          &:hover {
            color: var(---color--primary);
          }

        }

      }

    }

    /* ----- Blockquote ----- */

    blockquote, .blockquote,
    blockquote p, .blockquote p {

      @include blockquote-style();

      font-size: var(---font-size-body-large--mobile);
      @include respond-to($small-up){
        font-size: var(---font-size-body-large--desktop);
      }

    }

    /* ----- Lists ----- */

    ul, .ul,
    ol, .ol {

      margin: 1.5em 0;
      padding-left: 1.5em;

      &:first-child,
      &:last-child {
        margin: 0.5em 0;
      }

      ul, .ul,
      ol, .ol {

        &:first-child {
          margin-top: 0.5em !important;
        }

      }
    }

    ol, .ol {
      li {
        &::marker {
          color: var(---color--secondary);
        }
      }
    }

    ul, .ul {
      li {
        list-style-type: '\25CF';
        padding-left: 0.75em;

        &::marker {
          content: '\25CF';
          color: var(---color--secondary);
        }
      }
    }

  }

}