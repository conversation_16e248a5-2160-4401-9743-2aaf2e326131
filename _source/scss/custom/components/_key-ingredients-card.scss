.key-ingredient-item {

  --card-width: 300px;

  // max-width: var(--image-width);
  // width: 50vw;

  display: block;
  width: 80vw;
  max-width: var(--card-width);

  border-radius: var(--block-border-radius-reduced);
  border: 1px solid var(---color-line);
  box-shadow: var(--block-shadow--card);

  scroll-snap-align: start;
  scroll-snap-stop: always;

  overflow: hidden;

  background: #fff;

  @media (min-width: 800px) {
    --card-width: 360px;
    width: 40vw;
  }

  .key-ingredient-item__inner {

    display: flex;
    flex-direction: column;

  }

  .key-ingredient-item__image {
    border-radius: 0;
    object-fit: cover;
  }

  .key-ingredient-item__caption {

    --padding: 30px;

    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: var(--padding);
    margin-top: 0;

    >* {
      margin: 0;
    }
  }

}

.custom-ingredients-slider {
  .gallery__list {
    padding-block: 20px;
    scroll-snap-type: x mandatory;
  }
}