.kyte-text-animation-container {

  svg {
    path {
      fill: transparent;
    }
  }

  text {
    user-select: none;
  }

}

.kyte-text-animation {

  &.kyte-text-animation--1 {

    position: absolute;
    z-index: 2;
    bottom: 0;
    right: 0;

    margin: auto;

    transform: translate(00%, 80%) rotate(165deg);

    width: 650px;
    overflow: visible;

    pointer-events: none;
    user-select: none;

    @media (max-width: 1000px) {
      width: 300px;
    }

    text {
      font-size: 240px;
      fill: var(--text-animation-color, #ffffff) !important;
      font-weight: 400;
      letter-spacing: -0.025em;
      opacity: 0.8;
    }

  }

  &.kyte-text-animation--2 {

    position: absolute;
    z-index: 2;
    bottom: 0;
    right: 0;

    margin: auto;

    width: 40% !important;
    top: 10% !important;
    right: 5% !important;
    bottom: unset !important;

    transform: translateY(0) rotate(160deg);
    z-index: 2;
    overflow: visible;

    text {
      font-size: 240px;
      font-weight: 400;
      letter-spacing: -0.025em;
      fill: var(--text-animation-color, #D9D9D9) !important;
      opacity: 0.5 !important;
    }

    @media (max-width: 1000px) {
      width: 60% !important;
      left: 0 !important;
      right: 0 !important;
    }

    @media (max-width: 740px) {
      width: 80% !important;
      left: 0 !important;
      right: 0 !important;
      top: 5% !important;
    }

  }

}

.section--meet-our-founder {
  position: relative;
  overflow: hidden;
}