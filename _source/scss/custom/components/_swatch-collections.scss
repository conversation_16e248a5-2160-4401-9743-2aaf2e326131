.swatch-collections-tabs {

  display: flex;
  flex-wrap: nowrap;
  width: 100%;
  margin-bottom: 20px;

  .swatch-collections-tab {

    position: relative;
    padding: 0.8em 1.4em;
    width: 100%;
    margin-right: 1px;

    font-size: var(---font-size-body-small--mobile);

    text-align: center;
    line-height: 1.2;
    
    box-shadow: 0 0 0 1px var(---color--primary);

    transition: 0.25s color, 0.25s background;

    @include respond-to($medium-up) {
      padding: 0.5em 1.4em;
      font-size: var(---font-size-body-small--desktop);
    }

    &[aria-hidden="true"] {
      display: none;
    }

    &:focus,
    &:focus-within,
    &:hover {
      background: var(---color--brand-7);
    }

    &:first-child {
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
    }

    &:last-child {
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
    }

    &[aria-expanded="true"] {
      background-color: var(---color--primary);
      color: var(---color-text--reversed);

      &:after {
        top: 100%;
        left: 50%;
        border: solid transparent;
        content: "";
        height: 0;
        width: 0;
        position: absolute;
        pointer-events: none;
        border-color: rgba(var(---color--primary--rgb), 0);
        border-top-color: var(---color--primary);
        border-width: 8px;
        margin-left: -8px;
      }
    }

  }

}

.swatch-collections-contents {
  
  .swatch-collections-content {

    hidden {
      display: none;
    }

  }

}

.color-swatch-list__loading-message {

  background: RGB(var(---background-color--content-2--rgb));

  padding: 0.5em 1em;
  border-radius: 8px;
  text-align: center;

  width: 100%;
  
}