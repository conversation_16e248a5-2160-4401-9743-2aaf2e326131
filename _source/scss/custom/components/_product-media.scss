.product.product--thumbnails-bottom {
  margin-top: 0;
}

product-media {

  .product__media-image-wrapper {
    border-radius: 0;
  }

  .product__media-list-wrapper {
    margin-left: calc(var(--container-gutter) * -1);
    margin-right: calc(var(--container-gutter) * -1);
    overflow: hidden;
  }

}

.product-media-banners {
  
  grid-column: 1 / span 2;
  min-height: 50px;
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: var(--grid-gap);

}

.product-media-banner {

  border: 1px solid var(---color-line);
  padding: 35px 20px;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 0.5em;

  .product-media-banner__link {
    justify-content: center;
  }

}

.product-media-badges {
  display: flex;
  position: absolute;
  left: 20px;
  top: 20px;
  z-index: 1;
  pointer-events: none;
}

.product-media-badge {
  width: 40px;
  transform: rotate(-10deg);

  @include respond-to($large-up) {
    width: 60px;
  }
}

.product-media-badge__image {}

.product__media {
  position: relative;
}