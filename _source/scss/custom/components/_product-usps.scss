.product-usps-container {

  padding: 20px;
  border: 1px solid var(---color-line);
  border-radius: 12px;
  margin-bottom: 20px;

  @include respond-to($medium-down) {
    margin-top: 0;
  }

  .product-usps {

    display: flex;
    flex-direction: column;

    @include respond-to($small-up) {
      flex-direction: row;
      margin-block: 10px;
    }
    
    &.product-usps--images-only {
      flex-direction: row;
      justify-content: space-around;
      .product-usp {
        width: auto;
      }
      .product-usp__image {
        max-width: 100px;
        svg {
          width: unset;
        }
      }
    }

  }

  .product-usp {

    display: flex;
    width: 100%;
    gap: 10px;
    text-align: center;
    align-items: center;

    @include respond-to($small-up) {
      flex-direction: column;
    }

  }

  .product-usp__image {
    @include respond-to($small-up) {
      max-width: 100px;
    }
    svg {
      width: 50px;
    }
  }

  .product-usp__title {
    line-height: 1.2;
    width: 100%;

    @include respond-to($small-up) {
      // max-width: 70%;
    }
  }

}