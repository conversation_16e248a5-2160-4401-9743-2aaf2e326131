.gallery-review-product {
  display: flex;
  gap: 15px;
}

.gallery-review-product__image {
  min-width: 60px;
  width: 60px;
  height: 60px;
  border-radius: 300px;
  overflow: hidden;

  img {
    height: 100%;
    width: 100%;
    min-width: 100%;
    margin: 0 !important;
  }
}

.gallery-review-product__text {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 2px;

  >* {
    margin: 0 !important;
  }

  .price {
    font-size: var(---font-size-subheading-small);
    font-family: var(---font-family-subheading);
    font-size: var(---font-size-subheading--mobile);
    font-style: var(---font-style-subheading);
    font-variation-settings: "wght" 400;
    letter-spacing: 0.05em;
    text-transform: uppercase;
  }

  .price--highlight {
    color: #fff !important;
  }
}