.line-item {

  .line-item__label-list {
    margin-bottom: 10px;
  }

  .product-item-meta__property-list {
    gap: 5px;
    margin-top: 8px !important;
  }
  
  .product-item-meta__property {
    line-height: 1em;
    li {
        margin-top: 5px;
        margin-bottom: 5px;
    }
  }

}

.line-item--offer {
  .line-item__quantity {
    display: none;
  }

  .line-item-quantity--display {
    &:before {
      content: "x";
    }

    [data-quantity] {
      &:before {
        content: attr(data-quantity);
      }
    }
  }
}
