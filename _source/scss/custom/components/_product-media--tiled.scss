/* ========== Product Media ========== */

.product__media {

  .product-media__label-list {
  
    position: absolute;
    z-index: 1;
    top: 10px;
    right: 10px;
    
    width: 100%;
    
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    gap: 10px;

    @include respond-to($small-down) {
      top: 24px;
    }
  
  }

}

/* ========== Product Media - Tiled ========== */

.product__media.product-media--tiled {

  padding: 0 !important;

  @include respond-to($medium-down) {
    margin-left: calc(var(--container-gutter) * -1);
    margin-right: calc(var(--container-gutter) * -1);
  }

  /*----- Gallery ----- */

  .product__media-list-wrapper {

    @include respond-to($medium-down) {
      display: flex;
      gap: var(--container-gutter);
      overflow: scroll;
      @include hide-scrollbars();
    }

    @include respond-to($medium-up) {
      display: grid;
      grid-template-columns: repeat(2, minmax(0, 1fr));
      gap: var(--grid-gap);
    }

  }

  .product__media-item {
    
    @include respond-to($medium-up) {
      
      .product__zoom-button {
        opacity: 0;
        transition: opacity 0.25s
      }

      &:hover {
        .product__zoom-button {
          opacity: 1;
        }
      }

    }

  }

  &.product-media--tiled-full-width {

    .product__media-list-wrapper {

      @include respond-to($medium-up) {
        display: flex;
        flex-direction: column;
        gap: var(--grid-gap);
      }

    }
    
  }

  &.product-media--tiled-large-first {

    .product__media-list-wrapper {

      .product__media-item {
        &:first-child {
          @include respond-to($medium-up) {
            grid-column: span 2;
          }
        }
      }

    }
    
  }

  .product__media-image-wrapper {

    background: transparent;

    img {
      transition: transform 0.5s;
      transform: scale(1);
      will-change: transform;
      &:hover {
        transform: scale(1.05);
      }
    }

  }

  .product__media-item {

    position: relative;
    overflow: hidden;
    height: 100%;
    width: 100%;
    object-fit: cover;

    video {
      object-fit: cover;
    }

    template,
    img {
      width: 100%;
      height: 100%;
      display: block;
    }

    span.media-caption {
      position: absolute;
      bottom: 10px;
      right: 10px;
      padding: 0.5em 0.75em;
      background: var(---background-color--content-1);

      font-size: 11px;
      text-transform: uppercase;
    }

    @include respond-to($medium-down) {

      padding: 0;
      min-width: 70vw;

      &:first-child {
        margin-left: var(--container-gutter);
      }

      &:last-child {
        margin-right: var(--container-gutter);
      }

    }

  }

  .video-wrapper {
    height: 100%;
    width: 100%;
    overflow: hidden;
    border-radius: var(--block-border-radius-reduced);
  }

}