.drawer {

  --root-background: var(---background-color--content-1--rgb);

  box-shadow: var(---shadow--modal);

  &[open] {
    > .drawer__overlay {
      opacity: 1;
    }
  }

  .drawer--large {
    @include respond-to($medium-down) {
      max-width: 100vw;
    }
  }

  // Drawer Overlay

  .drawer__overlay {
    background: var(---banner-overlay-background);
  }

  // Header

  .drawer__header {

    padding-top: 20px;
    padding-bottom: 20px;

    .drawer__close-button {
      top: calc(50% - 12px);
    }
    
  }

  .drawer__footer {
    // background-color: RGB(var(---background-color--tertiary--rgb));
    border-top: 1px solid var(---color-line);
    box-shadow: none;
    transform: none;
  }

  .drawer__content {
    background: var(---background-color--content-1);
  }

  .drawer__title {
    
    font-family: var(---font-family-body);
    font-weight: var(---font-weight-body--bolder);
    font-size: var(---font-size-h5--mobile);
    
    text-transform: none;
    color: var(---color-heading--2);
    gap: 7px;
    
    @include respond-to($small-up) {
      font-size: var(---font-size-h5--desktop);
    }

    .heading--alternate {
      color: RGB(var(---color--secondary--rgb));
    }
    .icon {
      margin: 0;
      color: var(---color--tertiary);
    }
  }

  .drawer__header--shadowed {
    
  }

  .product-form__add-button {
    --button-background: var(---color--primary--rgb);
  }

  // Content

  /*  ----- Popovers ----- */

  .popover {

    // background: var(---background-color--content-1);

    .wk-button {

    @extend .button;
    @extend .button--inverted;
    @extend .button--full;

    transition: 0.25s color;

    color: var(---color--primary);
    
    .wk-button__label {
      color: var(---color--primary);
      transition: 0.25s color;
    }

    .wk-button__icon {
      transition: 0.25s color;
    }

    &:focus,
    &:focus-within,
    &:hover {
      color: var(---color-text--reversed-strong);
      .wk-button__icon,
      .wk-button__label {
        color: var(---color-text--reversed-strong);
      }
    }

  }

    .product-form__add-button {
      --button-background: var(---color--primary--rgb);
    }

    .popover__header {
      
    }

  }

}

/*  -----------------------------------
    Quick Buy Drawer
    ----------------------------------- */

.drawer.drawer--quick-buy {

}


/*  -----------------------------------
    Drawer Cart
    ----------------------------------- */

.drawer.mini-cart {

  --container-gutter: 30px;

  .checkout-button {
    margin: 0;
  }

  .mini-cart__recommendations {

    &:after {
      content: '';
      right: 0;
      top: 0;
      bottom: 0;
      width: 20px;
      height: 100%;
      position: absolute;
      background: linear-gradient(to right, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.025));
    }
  }

} 