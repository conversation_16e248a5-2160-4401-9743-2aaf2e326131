.shipping-bar {

  .shipping-bar__progress {
    
    border: 0;
    height: 10px;
    border-radius: 10px;
    overflow: hidden;
    
    background: var(---color--kyte-light-grey);
    box-shadow: 0 0 0 1px var(---color--kyte-dark-grey) inset;

    &:after {
      background: var(---color--kyte-dark-grey);
      border-top-left-radius: 10px;
      border-bottom-left-radius: 10px;
    }
    
  }

  .shipping-bar__text {
    // font-size: var(---font-size-body-xs--desktop);
    display: inline;
    font-size: 13px;
  }

  .country-code-flag {
    font-size: 1.6em;
    line-height: 1em;
  }

  .shipping-bar__flag {
    line-height: 1;
    position: relative;
    top: 5px;
  }

}