.product-tabs {
  
  @include respond-to($small-down) {
    margin-top: 0;
  }

  @include respond-to($medium-down) {
    border-bottom: 1px solid var(---color-line);
  }

  .tabs-nav__arrows {
    top: 0;
  }

  .product-tabs__tab-item-wrapper {
    
    &[hidden] {
      display: none;
    }

    .product-tabs__tab-item-content,
    .collapsible-toggle {

      @include respond-to($medium-down) {
        padding-left: var(--container-gutter);
        padding-right: var(--container-gutter);
      }

    }

  }

  .product-tabs__content {
    em strong {
      font-size: 28px;
      font-style: normal;
    }
  }

  .product-tabs__trust-list {
    
    @include respond-to($small-down) {
        overflow: scroll;
        margin: 0 calc(-1 * var(--container-gutter));
        width: 100vw;
    }
    
  }

  .product-tabs__trust-list-inner {

    display: flex;
    padding: 10px calc(var(--container-gutter));

    scroll-snap-type: both mandatory;
    overscroll-behavior-x: contain;

    @include respond-to($small-down) {
      gap: 30px;
      white-space: nowrap;
    }

    @include respond-to($small-up) {

      display: flex;
      align-items: center;
      justify-content: center;
      flex-wrap: wrap;
      gap: 20px;
      
      padding: 0;

    }

  }

  .product-tabs__trust-title {

    scroll-snap-align: start;
    scroll-snap-stop: always;
    scroll-margin: var(--container-gutter);

  }

  .product-tabs__trust-list,
  .product-tabs__tab-item-wrapper {
    @include respond-to($medium-down) {
      padding: 0;
    }
  }

  .product-tabs__tab-item-wrapper {
    &[hidden] {
      @include respond-to($medium-down) {
        display: block !important;
      }
    }
  }


}