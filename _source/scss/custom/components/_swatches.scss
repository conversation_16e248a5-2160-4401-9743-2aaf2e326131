// Swatches

.swatch-list-container {}

.block-swatch-list,
.color-swatch-list {}

/*  ------------------------------------ 
	Color Swatches
	------------------------------------ */

.color-swatch-list {
	justify-content: flex-start;
}

color-swatch-list {

	display: block;

	@include respond-to($small-up) {

		.button.color-swatch-list__button-show-all {
			// display: none !important;
		}

	}

		&.color-swatch-list--expandable {

			position: relative;

			&.color-swatch-list--collapsed {

				overflow: hidden;
				max-height: 140px;

				&:after {
					background: linear-gradient(0deg, #fff, transparent);
				}

				.color-swatch {
					&:before, &:after {
						display: none !important;
					}
				}

			}

			.button.color-swatch-list__button-show-all {

				position: absolute;
				bottom: 0px;
				left: 0;
				right: 0;

				max-width: max-content;
				margin: auto !important;

				z-index: 1;

			}

			&:after {

				pointer-events: none;

				content: '';
				position: absolute;
				left: 0;
				bottom: 0;

				width: 100%;
				height: 50px;

			}

	}

}


.color-swatch {

	&.color-swatch--active {
		.color-swatch__item::after {
			opacity: 1;
			transform: scale(1);
		}
	}

	.color-swatch__item {
		border: 2px solid var(---color-line);
		background-size: contain;
	}

}

.color-swatch__item {
	// --text-color: var(---color--primary--rgb)
}

// Color Swatches in Combo Boxes

.combo-box__color-swatch {}

/*  ------------------------------------ 
	Block Swatches
	------------------------------------ */

.block-swatch-list {

	&.block-swatch-list--small {
		.block-swatch {
			padding: 0;
		}

		.block-swatch__item {
			
			min-height: 45px;
			padding: 0 1em;

			color: var(---color-text);

		}
	}

}

.block-swatch {

	--border-radius: var(--block-border-radius);
	--font-size: var(--font-size-sm);

	position: relative;

	.block-swatch__item {


		display: flex;
		align-items: center;
		justify-content: center;

		min-width: 50px;
		min-height: 50px;
		padding: 0.4em 1em;

		font-size: var(--font-size);

		border-radius: var(--border-radius);
		
		&:after {
			border-radius: var(--border-radius);
		}

	}

	&.is-disabled {
		--text-color: var(---color-text--rgb);
		.block-swatch__item {
			background-color: transparent;
		}	
	}

	&:not(.is-disabled) {

		.block-swatch__item {

			border: 0;
			outline: 1px solid RGBA(var(--text-color), 0.25);
			box-shadow: inset 0 0 0 2px RGBA(var(--text-color).25);
			background-color: RGB(var(--secondary-background));

			&:after {
				opacity: 0;
				transform: scale(0.8);
				transition: transform 0.2s, opacity 0.1;
			}

			&:focus,
			&:hover {
				background-color: RGB(var(--primary-button-background));
				color: var(---color-text--reversed);
			}

		}

		input {

			&:checked {
				+ {
					.block-swatch__item {
						box-shadow: 0 0 0 2px RGBA(var(--text-color).25);
						background-color: RGB(var(--background));
						&:after {
							opacity: 1;
							transform: scale(1);
						}
						&:focus,
						&:hover {
							color: RGB(var(--text-color));
						}
					}
				}
			}

		}

	}

	&.block-swatch--small {

		--border-radius: var(--block-border-radius-sm);
		--font-size: var(--font-size-xs);

		.block-swatch__item {
			min-width: 40px;
			min-height: 40px;
		}

	}

}