.button {

  &:not(.button--text) {
    @include button-structure();
  }

  .button--inline-block {
    display: inline-block;
  }

  &.button--primary {

    // --button-background: var(---color--primary--rgb);
    // --button-text-color: var(---color-text--reversed--rgb);
    
    background: var(---color--primary);
    color: var(---color-text--reversed);
    box-shadow: 0 0 0 1px var(---color-text);

    &[disabled] {
      background: RGB(var(---color--disabled--rgb));
    }

    &:not([disabled]) {

      &:focus,
      &:focus-within,
      &:hover {
        background: var(---color--brand-7);
        color: var(---color-text);
        box-shadow: 0 0 0 1px var(---color--brand-7);
      }

    }

  }

  &.button--secondary {
    
    background: transparent;
    color: var(---color-text);
    box-shadow: 0 0 0 1px var(---color-text);

    &[disabled] {
      background: RGB(var(---color--disabled--rgb));
    }

    &:not([disabled]) {

      &:focus,
      &:focus-within,
      &:hover {
        background: var(---color--brand-7);
        color: var(---color-text);
        box-shadow: 0 0 0 1px var(---color--brand-7);
      }

    }

  }

  &.button--tertiary {
    
    background: var(---color--brand-3);
    color: var(---color-text);

    &[disabled] {
      background: RGB(var(---color--disabled--rgb));
    }

    &:not([disabled]) {

      &:focus,
      &:focus-within,
      &:hover {
        background: var(---color--brand-7);
        color: var(---color-text);
      }

    }

  }

  &.button--outline {
    background: transparent;
    color: RGB(var(--button-background));
    box-shadow: 0 0 0 1px RGBA(var(--button-background), 1);

    &[disabled] {
      background: RGB(var(---color--disabled--rgb));
    }

    &:not([disabled]) {

      &:focus,
      &:focus-within,
      &:hover {
        background: RGB(var(--button-background));
        color: RGB(var(--button-text-color));
      }
      
    }

  }

  &.button--outline-faint {
    background: transparent;
    color: RGB(var(--button-background));
    box-shadow: 0 0 0 1px RGBA(var(--button-background), 0.1);

    &[disabled] {
      background: RGB(var(---color--disabled--rgb));
    }

    &:not([disabled]) {

      &:focus,
      &:focus-within,
      &:hover {
        background: RGB(var(--button-background));
        color: RGB(var(--button-text-color));
      }
      
    }

  }


  &.button--subdued {
    background: var(---color--brand-7);
    box-shadow: 0 0 0 1px var(---color-line);

    &[disabled] {
      background: RGB(var(---color--disabled--rgb));
    }

    &:not([disabled]) {

      &:focus,
      &:focus-within,
      &:hover {
        background: var(---color--primary);
        color: var(---color-text--reversed);
        box-shadow: 0 0 0 1px var(---color--primary);
      }

    }

  }


  &.button--disabled {
    cursor: default;
    user-select: none;
  }

  /*

  &.button--tertiary {
  	@include button-style($color-tertiary);
  }

  &.button--success {
  	@include button-style($color-success);
  }

  &.button--warning {
  	@include button-style($color-warning);
  }

  &.button--danger {
  	@include button-style($color-danger);
  }

  &.button--info {
  	@include button-style($color-info);
  }

  */

  .button--outline {
    border: 1px solid var(---color-line) !important;
    background: transparent;
  }

  &.button--link {
    padding: 0;
    margin: 0;
  }

  &.button--inverted {
    @include button-style--inverted();
  }

  &.button--light {
    @include button-style--light();
  }

  .button__icon {
    svg {
      fill: currentColor;
      stroke: currentColor;
    }
    &:only-child() {
      margin: 0;
    }
  }

  .button__text {
    
  }

}

.shopify-payment-button__button--unbranded {

  @extend .button;
  // @extend .button--primary;
  @extend .button--full;

}

.input-row {
  display: flex;

  .input {
    .button {
      margin: 0;
      padding: 1em 1.6em;
    }
  }
}