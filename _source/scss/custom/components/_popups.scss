.modal {

  --root-background: var(---background-color--content-1--rgb);

  box-shadow: var(---shadow--modal);

  &[open] {
    .modal__overlay {
      opacity: 0.9;
    }
  }

  .modal__overlay {
    background: var(---banner-overlay-background);
    opacity: 0;
  }

}

// Newsletter

.shopify-section--popup {
  --section-block-background: var(---background-color--content-2--rgb);
  
  .modal__close-button {
    top: 15px;
    right: 15px;
  }

  .modal__content {
    max-height: 95vh !important;
  }

}

.newsletter-modal {

  background: RGB(var(--section-block-background));

  @include respond-to($medium-up) {
    padding: 30px;
  }

  .newsletter-modal__content {
    @include respond-to($medium-up) {
      padding: 0 0 0 30px;
    }

    .heading {
      margin-top: 0;
      margin-bottom: 12px;
    }

  }

  .newsletter-modal__content,
  .newsletter-modal__image {
    max-width: 400px;
    min-width: 400px;
  }

  .newsletter-modal__form {
    margin-bottom: 20px;
  }

  .newsletter-modal__image-content-wrapper {
    max-width: 180px;
    margin: auto;
    display: inline-block;

    img {
      margin: 0 !important;
    }
  }

  @include respond-to($medium-down) {

    .newsletter-modal__image {
      margin: 0;
      max-width: unset;
      max-height: unset;
      min-width: unset;
      display: block;
      border-radius: var(--block-border-radius);
    }

    .newsletter-modal__content--extra {
      padding-top: 20px;
      padding-bottom: 20px;
    }

    .newsletter-modal__image-wrapper {
      width: calc(100% - 60px);
    }

    .newsletter-modal__image {
      border-radius: var(--block-border-radius);
    }

    .tiny {
      padding-bottom: 20px;
    }

  }


}

.preorder-modal {

  .preorder-modal__content {
    padding: 30px;
    max-width: 400px;
  }

}