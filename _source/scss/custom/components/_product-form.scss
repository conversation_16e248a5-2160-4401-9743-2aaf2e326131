.product-form {

  gap: var(--vertical-gap);

  .note {
    margin-top: 0;
    margin-bottom: 0;
  }

  .product-form__quantity-label {
    font-size: var(---font-size-body-small--desktop);
    font-weight: var(---font-weight-body--bold);
  }

  .product-form__payment-container {
    @include respond-to($small-up) {
      margin-top: 0;
    }
  }

  .product-form__text {
    text-align: center;
  }

  .product-form__option-selector {
    +.product-form__option-selector {
      margin: 0;
    }
  }

  .product-form__option-info {

    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: var(---font-size-subheading-small);

    color: var(---color-text--dark);

  }

  .product-form__option-links {
    margin-left: auto;
  }

  .product-form__inventory-wrapper,
  .product-form__option-info {
    margin: 0px 0 10px 0;

    &:first-child {
      margin-top: 0;
    }
  }

  .product-form__inventory-wrapper {
    display: block;
    margin-bottom: 0;
  }

  .product-form__text {
    p {
      font-weight: 300;
    }
  }



  /* ----- BIS ----- */

  #BIS_trigger {
    margin: 0;

    &:not([style="display: none;"]) {
      +product-payment-container {
        display: none !important;
      }
    }
  }


  /* ----- Inventory ----- */

  .product-form__inventory-block {

    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    line-height: 1;

    .delivery-estimate__button {
      line-height: 1;
    }

    .delivery-estimate__tooltip {
      display: none;
    }

    .tippy-content {
      .delivery-estimate__tooltip {
        display: block;
      }
    }

  }

  /* ----- Quantity Selector + ATC ----- */

  .quantity-atc {
    display: grid;
    grid-auto-flow: row;
    gap: var(--vertical-gap);
  }

  /* ----- Registry + Wish List Buttons ----- */

  .product-form__wishlist-buttons {

    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: stretch;
    
    gap: var(--vertical-gap);

    .giftreggie-pdp-registry-cta,
    .swym-wishlist-cta {

      font-weight: 400;
      letter-spacing: 0;
      // text-underline-offset: 2px;
      // text-decoration: underline;
      
      -webkit-text-decoration-color: rgba(var(--text-color), .35);
      text-decoration-color: rgba(var(--text-color), .35);
      transition: text-decoration-color .2s ease-in-out, color .2s ease-in-out;

      font-size: var(---font-size-body--mobile);
      @include respond-to($medium-up) {
        font-size: var(---font-size-body--desktop);
      }

      &:hover,
      &:focus {
        opacity: 0.5;
      }
    }


    // Gift Reggie

    .giftreggie-pdp-registry-cta {

      margin: 0;
      gap: 10px;

      padding: 1.2em 1.8em !important;
      @include respond-to($small-down) {
        padding: 1.2em 0 !important;
      }

      &:before {
        display: block;
        content: '';
        width: 22px;
        height: 22px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none'%3E%3Cg clip-path='url(%23clip0_2663_1768)'%3E%3Cpath d='M20.6676 1.35046C19.506 0.183167 17.8661 6.10352e-05 15.9301 6.10352e-05H7.0472C5.13396 6.10352e-05 3.49404 0.183167 2.33244 1.35046C1.17079 2.51775 1 4.15428 1 6.06547V14.9004C1 16.8573 1.17079 18.4824 2.33244 19.6497C3.49404 20.817 5.13396 21.0001 7.08135 21.0001H15.9301C17.8661 21.0001 19.506 20.817 20.6676 19.6497C21.8293 18.4824 22 16.8573 22 14.9004V6.0998C22 4.14285 21.8293 2.50632 20.6676 1.35046ZM2.8335 5.81366C2.8335 4.61205 2.98156 3.3761 3.66488 2.68943C4.35957 1.99133 5.60086 1.8426 6.79666 1.8426H10.6801V6.03114C9.92845 4.56629 8.64154 3.66219 7.20662 3.66219C5.57812 3.66219 4.34816 4.88671 4.34816 6.55756C4.34816 7.99951 5.3617 9.1897 6.79666 9.842H2.8335V5.81366ZM20.1665 5.77934V9.842H15.9301C17.365 9.1897 18.3786 7.99951 18.3786 6.55756C18.3786 4.88671 17.1487 3.66219 15.5087 3.66219C14.0852 3.66219 12.7983 4.56629 12.0467 6.03114V1.8426H16.2375C17.4106 1.8426 18.6405 2.0028 19.3352 2.68943C20.0185 3.38753 20.1665 4.60062 20.1665 5.77934ZM10.122 9.67037C7.87853 9.67037 5.95392 8.12539 5.95392 6.68344C5.95392 5.81366 6.47777 5.28724 7.29773 5.28724C8.98322 5.28724 10.4637 7.17553 10.4637 9.34991V9.67037H10.122ZM12.6047 9.67037H12.2631V9.34991C12.2631 7.17553 13.7321 5.28724 15.4176 5.28724C16.249 5.28724 16.7728 5.81366 16.7728 6.68344C16.7728 8.12539 14.8482 9.67037 12.6047 9.67037ZM16.2375 19.1576H12.0467V11.902H12.1378C12.8894 14.2595 15.7023 16.3194 17.4561 16.5826C18.1053 16.6742 18.5038 16.205 18.5038 15.7014C18.5038 15.1521 18.1736 14.7859 17.5814 14.7287C16.0098 14.557 13.8119 12.497 13.1513 11.1009H20.1665V15.2093C20.1665 16.3995 20.0185 17.6126 19.3352 18.3107C18.6405 18.9974 17.4106 19.1576 16.2375 19.1576ZM6.76246 19.1576C5.58949 19.1576 4.35957 18.9974 3.67625 18.3107C2.98156 17.6126 2.8335 16.3995 2.8335 15.2093V11.1009H9.57539C8.91486 12.497 6.71693 14.557 5.14533 14.7287C4.55316 14.7859 4.22289 15.1521 4.22289 15.7014C4.22289 16.205 4.62148 16.6742 5.27059 16.5826C7.02441 16.3194 9.83734 14.2595 10.589 11.902H10.6801V19.1576H6.76246Z' fill='black' /%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='clip0_2663_1768'%3E%3Crect width='24' height='24' fill='white' /%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E");
      }

    }

    .giftreggie-pdp-cta-area {
      margin: 0;
    }


    // Wishlist

    .swym-details-container {
      letter-spacing: 0 !important;

      .swym-emphasis {
        letter-spacing: 0 !important;
      }
    }

    .swym-wishlist-button-bar {
      display: flex;
      align-items: stretch;
      justify-content: center;
    }

    .swym-btn-container {
      display: flex;
      align-items: stretch;
    }

    .swym-add-to-wishlist {
      align-items: center;
      display: flex !important;
      gap: 10px !important;
      height: 100%;

      padding: 1.2em 1.8em !important;
      @include respond-to($small-down) {
        padding: 1.2em 0 !important;
      }
      
      &.swym-iconbtnlink {
        height: auto !important;
      }

      &:after {
        position: relative;
        width: 18px !important;
        height: 18px !important;
        order: 0;
      }

      .swym-wishlist-cta {

        text-indent: 0;
        order: 1;

      }

    }

  }


  product-payment-container {
    .button {
      margin: 0 !important;
    }

    .product-form__add-button {
      .loader-button__label {
        flex-grow: 1;
      }

      .loader-button__text {
        .loader-button__label {
          white-space: nowrap;
        }
      }

      .product-item__prices-wrapper {
        display: flex;
        flex-wrap: wrap;
      }
    }

    .product-item__prices-wrapper {
      white-space: nowrap;
      display: flex;
      gap: 0.25em;
    }

    .regular-price-container {
      opacity: 0.5 !important;
      text-decoration: line-through !important;
    }
  }

  .shopify-payment-button__button {
    width: 100% !important;
  }

/*  ------------------------------
    Customer Tier Banner
    ------------------------------ */

  .tier-welcome-message-banner__icon {
    mix-blend-mode: multiply;;
    img {
      // transform: translate(-10px, -10px) rotate(-10deg);
    }
  }

  /*  ----- Product Form Banner ----- */

  .product-form-banner {

    display: flex;
    gap: 15px;
    align-items: flex-start;

    border: 1px solid var(---color-line);
    padding: 20px;
    background: RGB(var(--secondary-background));
    border-radius: var(--block-border-radius);

    &.product-form-banner--small {
      padding: 10px;
    }

    &.product-form-banner--color {
      border-color: var(--banner-color);
      background: var(--banner-color);
    }

    &.product-form-banner--danger {
      background: var(---background-color--danger);
    }

  }

  .sale-stop-banner {
    .product-form-banner__icon {
      width: 50px;
      max-width: 50px;
      flex: 1 0 50px;
      img {
        mix-blend-mode: multiply;
      }
    }
  }

  .product-form-banner__content {}

  .product-form-banner__icon {
    display: inline-flex;
    max-width: 50px;
    img {
      flex-basis: auto;
      width: revert-layer;
    }
  }

  .product-form-banner__text {
    .heading {
      margin-bottom: 0.25em !important;
    }
  }

  .product-form-banner__heading {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
  }

  .product-form-banner__actions {
    margin-top: 0.75em;
  }

  // Accordions

  .product-form-accordion {

    +.product-form-accordion {
      // margin-top: -18px;
    }

    .product-form-accordion__title {
      margin-inline-end: auto;
    }

    .product-form-accordion__toggle {
      border-bottom: 1px solid var(---color-line);
      gap: 10px;
      padding: 10px 0;
    }

    .product-form-accordion__content-inner {
      padding-block: 18px;
    }

    .product-form-accordion__icon {
      img, svg {
        max-width: 40px;
        width: 40px;
        height: 40px;
      }
    }

  }

}