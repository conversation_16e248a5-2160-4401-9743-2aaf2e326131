.collection-sidebar-menu {

  margin-bottom: 36px;

  font-size: var(---font-size-body-small--desktop);
  color: var(---color-text);

  @include respond-to($medium-down) {
    display: none;
  }

  @include respond-to($medium-up) {
    font-size: var(---font-size-body-small--desktop);
  }

  .collapsible__content {
    padding-left: 24px;
    padding-bottom: 2px;
    padding-top: 4px;
  }

  .collection-sidebar-menu__header {
    padding-bottom: 12px;
    margin-bottom: 24px;
    border-bottom: 1px solid var(---color-line--light);
  }

  .collection-sidebar-menu__item-icon {
    transition: var(---transition-duration--general) transform;
  }

  .collection-sidebar-menu__item {
    width: 100%;
    margin: 6px 0;

    &:hover {
      color: var(---color-text--dark);
    }
  }

  .collection-sidebar-menu__item-title {}

  .collection-sidebar-menu__link {
    width: 100%;
    margin: 6px 0;
    transition: var(---transition-duration--general) color;

    &:focus,
    &:hover {
      color: var(---color-link);
    }
  }

  .collection-sidebar-menu__item {}

  .collection-sidebar-menu__toggle {

    display: flex;
    gap: 6px;

    &[aria-expanded="false"] {
      .collection-sidebar-menu__item-icon {
        transform: rotate(-90deg);
      }
    }

    &[aria-expanded="true"] {
      .collection-sidebar-menu__item-icon {
        transform: rotate(0deg);
      }
    }

  }

}