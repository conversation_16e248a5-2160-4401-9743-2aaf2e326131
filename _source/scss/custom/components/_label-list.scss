.label-list {

  &.product-item__label-list {
    .label {
      font-size: var(---font-size-body-xs--mobile);
      @include respond-to($small-up) {
        font-size: var(---font-size-body-xs--desktop);
      }
    }
  }

  &.label-list--flex {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 0.25em 0.5em;
  }

}

.label {

  font-size: var(---font-size-body-xs--desktop);
  font-weight: 400;

  letter-spacing: 0;
  text-transform: uppercase;
  
  padding: 0.2em 0.7em;
  
  transition: var(---transition-duration--general) background-color;
  border-radius: 50px;

  font-size: var(---font-size-body-small--mobile);
  @include respond-to($small-up) {
    font-size: var(---font-size-body-small--desktop);
  }
  
  &.label--subdued {
    background-color: var(---color--tertiary);
  }
  
  &.label--highlight {
    background-color: var(---color-price--sale);
  }

  &.label--custom {
    background: var(---background-color--content-2);
    color: var(---color--secondary);
  }

  &.label--custom2 {
    background: var(---color--brand-7);
    color: var(---color--secondary);
  }

  // Sizes

  &.label--small {
    padding: 0.2em 0.7em;
  }

  &.label--tiny {
    padding: 0.2em 0.7em;
    font-size: var(--font-size-xs);
    border-radius: var(---border-radius--inputs);
  }

}

a.label {

  &.label--primary {
    &:hover {
      background-color: var(---color--primary--light);
    }
  }

  &.label--secondary {
    &:hover {
      background-color: var(---color--secondary--light);
    }
  }

  &.label--tertiary {
    &:hover {
      background-color: var(---color--tertiary--light);
    }
  }

}