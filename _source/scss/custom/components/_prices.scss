.price-list {

}

.price {

  color: var(---color-price);
  font-weight: var(---font-weight--product-price);
  letter-spacing: -0.05em;
  
  white-space: nowrap;
  
  font-size: var(---font-size-product-title--mobile);

  @include respond-to($medium-up) {
    font-size: var(---font-size-product-title--desktop);
  }

  &.price--sale {
    color: var(---color-price--sale);
    font-weight: var(---font-weight--product-price--sale);
  }

  &.price--compare {
    color: var(---color-price--compare);
    font-weight: var(---font-weight-body);
  }

  &.price--highlight {
    font-weight: var(---font-weight-body--bolder);
    color: var(---color-price--sale);
  }

  &.price--large {

    color: var(---color-text--light);
    text-transform: uppercase;

    font-size: var(---font-size-price--major--mobile);
    @media(min-width: 1000px) {
      font-size: var(---font-size-price--major--desktop);
    }
  }

}