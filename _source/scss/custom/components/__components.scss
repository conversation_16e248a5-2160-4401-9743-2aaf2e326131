/* ----- Bubbles ----- */

.bubble-count {
  background: var(---color--primary);
  color: var(---color-text--reversed);
  font-size: var(---font-size-body-xs--mobile);
  font-weight: var(---font-weight-body--bold);
  letter-spacing: -1px;
  
  &[data-cart-count="0"],
  &:empty {
    display: none;
  }
}


/* ----- Carousel Buttons ----- */

.prev-next-button {
    
  --prev-next-button-background: var(---background-color--content-1--rgb);
  --prev-next-button-border-color: var(---color-line--rgb);
  --prev-next-button-text-color: var(---color-text--rgb);

  width: 40px;
  height: 40px;

  border: 1px solid rgba(var(--prev-next-button-border-color), 1);
  border-radius: 100%;
  
  svg {
    color: RGB(var(--prev-next-button-text-color));
  }

  &:not(.prev-next-button--small) {
    width: 40px;
    height: 40px;
  }

}

/* ----- Tables ----- */

.line-item-table {

  .line-item {

    > td {
      padding-top: 2em;
    }

    &:hover {
      background: transparent;
    }

  }

}

/* ----- HRs ----- */

hr, .hr {

  width: 100%;
  margin: 2em 0;
  border-width: 0.5px;
  border-bottom: 0;
  border-style: solid;
  border-color: var(---color-line);

  &.hr--light {
    border-color: var(---color-line--light);
  }

  &.hr--dark {
    border-color: var(---color-line--dark);
  }

  &.hr--clear {
    border-color: transparent;
  }

  &.hr--small {
    margin: 1em 0;
  }

  &.hr--nomargin {
    margin: 0;
  }

}


/* ----- Button Wrapper ----- */

.button-wrapper {
  
  display: inline-flex;
  flex-wrap: wrap;
  gap: 1em;
  margin: 0;
  justify-content: center;
  
  .button {
    margin-top: 0;
    margin-bottom: 0;
    margin-right: 0;
    margin-left: 0;

    &:last-child {
      margin-right: 0;
    }

    &:only-child {
      margin-bottom: 0;
    }
  }

}

.text--left {
  .button-wrapper {
      justify-content: flex-start;
  }
}

.text--right {
  .button-wrapper {
      justify-content: flex-end;
  }
}

.heading + .button-wrapper,
.heading + .button-group,
p + .button-wrapper,
p + .button-group,
.button-wrapper + p,
.button-group + p {
  margin-top: 0;
}

/* ----- Captcha ----- */

.square-separator {
  width: 6px;
  height: 6px;
  border-radius: 100%;
  opacity: 1;
}

/* ----- Captcha ----- */

.g-recaptcha {
    
}

.mobile-share-buttons__item--pinterest,
.mobile-share-buttons__item--facebook {
  svg {
    * {
      fill: var(---color-text);
    }
  }
}

.text-container {
  
  p + form, 
  .rte p + form {

    @include respond-to($medium-up) {
      margin-top: 16px;
    }
  
  }

}


/* ----- Tooltips ----- */

[data-tooltip-position="bottom"]:before {
  bottom: calc(-100% + 6px);
  left: 50%;
  transform: translateX(-50%);
}

[data-tooltip-position="bottom"]:after {
  bottom: calc(-30% + 1px);
  left: calc(50% - 7px);
  transform: rotate(135deg);
}


prev-next-buttons {
  top: 50% !important;
  transform: translateY(-50%);
}



.section-shadow {
  box-shadow: var(--section-shadow);
}




.section-botanicals-bottom-1 {
  background-image: url(https://cdn.shopify.com/s/files/1/0019/7106/0847/files/section-botanicals-bottom-1-combined.svg?v=1761058266);
  background-position: bottom;
  background-size: 100%;
  background-repeat: no-repeat;
}

.section-botanicals-top-1 {
  background-image: url(https://cdn.shopify.com/s/files/1/0019/7106/0847/files/section-botanicals-top-1-combined.svg?v=1761058366);
  background-position: top;
  background-size: 100%;
  background-repeat: no-repeat;
}