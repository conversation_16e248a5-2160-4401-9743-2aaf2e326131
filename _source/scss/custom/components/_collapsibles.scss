.collapsible-toggle {

  svg {

    height: 16px;
    width: 16px;

    background-repeat: no-repeat;
    background-position: center;

    * {
      fill: transparent;
      stroke: transparent;
    }
  }

  &[aria-expanded="false"] {
    svg {
      background-image: url("data:image/svg+xml,%3Csvg width='12' height='12' viewBox='0 0 12 12' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11.9953 5.94871C12.0245 6.57531 11.5218 7.08897 10.9024 7.08897H7.75855C7.3906 7.08897 7.08824 7.38769 7.08824 7.75928V10.9032C7.08824 11.5225 6.57093 12.0216 5.94798 11.9961C5.32503 11.9706 4.90244 11.4314 4.90244 10.834V7.75928C4.90244 7.39134 4.60372 7.08897 4.23213 7.08897H1.09188C0.472571 7.09261 -0.0265184 6.57531 -0.00101751 5.94871C0.0244834 5.32212 0.563646 4.90318 1.1611 4.90318H4.23578C4.60372 4.90318 4.90609 4.60445 4.90609 4.23287V1.09261C4.90609 0.473303 5.41975 -0.025786 6.04634 -0.000285093C6.67294 0.0252158 7.09188 0.564378 7.09188 1.16183V4.23651C7.09188 4.60445 7.3906 4.90682 7.76219 4.90682H10.8369C11.4343 4.90682 11.9735 5.35491 11.999 5.95236L11.9953 5.94871Z' fill='black'/%3E%3C/svg%3E%0A");
    }
  }


  &[aria-expanded="true"] {
    svg {
      background-image: url("data:image/svg+xml,%3Csvg width='12' height='2' viewBox='0 0 12 2' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10.9071 0H1.0929C0.48816 0 0 0.446667 0 1C0 1.55333 0.48816 2 1.0929 2H10.9071C11.5118 2 12 1.55333 12 1C12 0.446667 11.5118 0 10.9071 0Z' fill='black'/%3E%3C/svg%3E%0A");
    }
  }

}