.tabs-nav {

  .tabs-nav__item-list {
    box-shadow: 0 -2px RGB(var(--border-color)) inset;
  }

  .tabs-nav__item {

    --heading-color: var(---color--default);

    opacity: 1;
    transition: color 0.25s;

    color: var(---color-products-title);
    font-weight: var(---font-weight--product-title);

    font-size: var(---font-size-product-title--mobile);

    @include respond-to($small-up) {
      font-size: var(---font-size-product-title--desktop);
    }

    &[aria-expanded="false"] {
      // --heading-color: var(---color--brand-2--rgb);
      color: var(---color--brand-2);
    }

    &[aria-expanded="true"] {
      --heading-color: var(---color--primary--rgb);
    }

  }

  .heading.heading--small {
    text-transform: unset;
    letter-spacing: 0.025em;
  }

  .tabs-nav__item-list {
    gap: 30px;
  }

  .tabs-nav__position {
    color: var(---color--primary);
  }

  &.tabs-nav--no-border {
    &.tabs-nav--narrow {
      .tabs-nav__item {
        padding-bottom: 10px;
      }
    }
  }

} 