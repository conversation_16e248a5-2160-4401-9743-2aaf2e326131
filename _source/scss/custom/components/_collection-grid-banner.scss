.collection-grid-banner {

  display: flex;
  flex-direction: column;
  height: 100%;

  border-radius: var(--block-border-radius);
  overflow: hidden;

  hr {
    margin-top: 0;
  }

  // Layout

  &.collection-grid-banner--span-1 {
    @include respond-to($medium-up) {
      grid-column-end: span 1;
    }
  }

  &.collection-grid-banner--span-2 {
    @include respond-to($medium-up) {
      grid-column-end: span 2;
    }
  }

  &.collection-grid-banner--span-3 {
    @include respond-to($medium-up) {
      grid-column-end: span 3;
    }
  }

  &.collection-grid-banner--span-4 {
    @include respond-to($medium-up) {
      grid-column-end: span 4;
    }
  }

  &.collection-grid-banner--mobile-fullwidth {

    @include respond-to($medium-down) {

      grid-column-end: span 2;

      margin-left: 0;
      margin-right: 0;

      &:first-child {
        margin-top: calc(var(--container-gutter) / 2 * -1);
      }

    }

  }


  &.collection-grid-banner--cover {

    img {
      height: 100%;
      object-fit: cover;
    }

    .collection-grid-banner__video {
      height: 100%;
      object-fit: cover;
    }

  }

  .collection-grid-banner__video {
    margin-top: auto;
  }


  /* ----- Header ----- */

  .collection-grid-banner__header {
    margin-bottom: auto;
  }

  .collection-grid-banner__title {
    margin-top: 0.5em;
    margin-bottom: 0.25em;
  }

  .collection-grid-banner__text {
    margin-bottom: 1em;
  }


  /* ----- Media ----- */

  .collection-grid-banner__media {

    position: relative;
    overflow: hidden;
    height: 100%;

    display: flex;
    flex-direction: column;

    video,
    img {
      height: 100%;
      object-fit: cover;
    }

  }

  a.collection-grid-banner__media {

    video,
    img {
      transform: scale(1);
      z-index: 0;
      position: relative;
      transition: transform 1s ease-in-out;
    }

    &:hover {
      video,
      img {
        transform: scale(1.05);
      }
    }

    .video-wrapper {
      height: 100%;
    }

  }
  
  .collection-grid-banner__overlay {

    position: absolute;
    top: 0;
    bottom: 0;
    z-index: 1;

    display: flex;
    flex-direction: column;
    justify-content: flex-end;

    width: 100%;
    height: 100%;
    padding: 40px;

  }

  .collection-grid-banner__overlay-subheading {
    margin: 0;

    +.heading {
      margin-top: 0;
    }
  }

  .collection-grid-banner__overlay-inner {

    max-width: 400px; 

  }

  .collection-grid-banner__overlay_title {
    margin-bottom: .25em ;
  }

  .button-wrapper {
    margin-top: 0.5em;
    color: #fff;
    .link {
      --text-color: var(--text-color);
    }
  }

  @include respond-to($small-down) {
    .collection-grid-banner__overlay {
      padding: 20px;
    }
  }

}
