/*  -----------------------------------
    Addons
    ----------------------------------- */

.product-form__addons {

  --background: var(---background-color--content-2);

  border-radius: var(--block-border-radius);
  background-color: var(---background-color--content-2);
  padding: 20px;

}

.product-addon-container {

  display: grid;
  grid-template-rows: auto;
  gap: 0.5em;

  .checkbox-container {
    .checkbox {
      background-color: #fff;
    }
  }

}

.product-addon__details-link {
  margin-left: auto;
  display: none;
}

// Product Addon Checkbox

.product-addon {

  --background: var(---background-color--content-2--rgb);
  
  display: flex;
  gap: 0.5em;

  label {
    display: inline-block;
    padding: 0;
    cursor: pointer;
  }

  button[disabled] {
    opacity: 0.3;
    pointer-events: none; // Prevents drawer from opening if sold out / disabled.
    + label {
      opacity: 0.3;
      pointer-events: none; // Prevents drawer from opening if sold out / disabled.
    }
  }

  &.product-addon--added {
    .product-addon__details-link {
      display: inline-block;
    }
    .checkbox {
      background-repeat: no-repeat;
      background-position: center center;
      background-image: url("data:image/svg+xml,%3Csvg width='12' height='10' viewBox='0 0 12 10' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11.7647 1.50542L4.79326 9.6845C4.63079 9.87499 4.39309 9.99107 4.14035 10C4.12831 10 4.11628 10 4.10123 10C3.86353 10 3.63186 9.90773 3.46336 9.73808L0.264975 6.57122C-0.129182 6.18131 -0.0840494 5.52055 0.403381 5.19613C0.764441 4.95504 1.25789 5.02648 1.56479 5.33304L3.33699 7.08613C3.71911 7.46413 4.34796 7.43734 4.69698 7.02958L10.4198 0.317849C10.7507 -0.0720559 11.3465 -0.110749 11.7286 0.24344C12.0837 0.573818 12.0837 1.13933 11.7677 1.5084L11.7647 1.50542Z' fill='%239A9A9A'/%3E%3C/svg%3E%0A");
    }
  }

}

// Drawers

.product-addon-form {
  display: grid;
  grid-template-rows: auto;
  gap: 30px;
}

.addon-option__heading {
  display: flex;
  justify-content: space-between;
  margin-block-end: 0.5em;
}


// Example Gallery

.example-gallery {
  display: grid;
  gap: 10px;
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.example-gallery-item {

  position: relative;

  padding: 10px;
  border: 1px solid var(---color-line);
  border-radius: 12px;

  img {
    border-radius: 8px;
  }

}

.example-gallery-item__title {
  display: inline-block;
  margin: 0.5em 0 0 0;
  font-size: var(---font-size-body-small--desktop);
  text-align: center;
  line-height: 1.2;

}