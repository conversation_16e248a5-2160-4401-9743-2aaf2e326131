
.product-sticky-form {
  background-color: var(---background-color--content-1);
  z-index: 3; // Z-Index issue with reviews UGC.
  .square-separator {
    background-color: var(---color--primary);
  }
}

.product-sticky-form__form {
  @media (max-width: 1000px) {
      product-payment-container {
          .button {
              width: 100%;
          }
      }
  }
}

.product-sticky-form .select {
  height: 100%;
}

.product-sticky-form__title {
  font-size: var(---font-size-product-title--desktop);
  font-weight: var(---font-weight--product-title);
}

.product-sticky-form__bottom-info {
  display: flex;
  align-items: center;
  gap: 5px;
}



/* ========== Custom (with jump links ========== */

.product-sticky-form {
  &.product-sticky-form--custom {

    z-index: 3;

    display: flex;
    align-items: center;

    margin: auto;
    border: 0 !important;
    box-shadow: none;

    border-radius: var(--block-border-radius);
    background-color: transparent;

    @include respond-to($medium-up) {
      
      left: 40px !important;
      right: 40px;
      top: calc(var(--header-height) + 20px);

      width: unset;
      padding: 10px 0 !important;
      background-color: RGB(var(--secondary-background));

    }

    .container {
      padding: 0 20px;
    }

    .select {
      height: 32px;
      min-height: 32px;
      min-width: unset;
      padding: 0 10px;

      font-size: var(---font-size-body-small--desktop);

      background-color: var(---background-color--content-1);
      border-radius: var(--block-border-radius);
    }

    .combo-box__option-item {
      font-size: var(---font-size-body-small--desktop);
    }

    .button {
      margin: 0;
      text-align: center;
      justify-content: center;

      @include respond-to($medium-down) {
        padding: 15px !important;
      }
    }

    .product-sticky-form__bottom-info {
      gap: 15px;
    }

    .product-sticky-form__form {
      order: 1;
      margin: 0;
    }

    .product-sticky-form__inner {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      gap: 15px;

      @include respond-to($medium-down) {
        flex-direction: column;
      }
    }

    .product-sticky-form__jump-links {
      display: flex;
      gap: 15px;
      margin-left: auto;

      order: 1;

      @include respond-to($medium-down) {
        background: var(---background-color--content-2);
        padding: 1em 1.5em;
        border-radius: var(--block-border-radius);
        margin-right: auto;
        order: 0;
      }

    }
    
  }
}