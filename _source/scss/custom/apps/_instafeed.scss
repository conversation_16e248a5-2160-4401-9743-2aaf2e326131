#insta-feed {
  
  @include respond-to($small-down) {

    display: flex !important;

    padding: 0 var(--container-gutter);
    overflow: scroll;
    gap: 10px;
  
    -ms-overflow-style: none;
    /* Internet Explorer 10+ */
    scrollbar-width: none;

    /* Firefox */
    &::-webkit-scrollbar {
      display: none;
      /* Safari and Chrome */
    }

    > a {
      min-height: 110px;
      min-width: 110px;
      z-index: 9998 !important;

      > div {
        width: 100% !important;
        height: 100%;
      }

    }

  }

  

  .instafeed-container {

    .instafeed-overlay:before,
    .instafeed-overlay:after,
    img {
      border-radius: 12px;
    }
  }

}

.instafeed-section {
  
  @include respond-to($medium-down) {
    margin-top: 0;
  }
      
  p {
    @include respond-to($medium-down) {
      padding-left: var(--container-gutter);
      padding-right: var(--container-gutter);
    }
  }

}