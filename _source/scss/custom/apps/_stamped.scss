/*  ==============================
    Main Widget
    ============================== */

    #stamped-main-widget {

      font-family: var(---font-family-body);

      .stamped-summary-text {
        font-family: var(---font-family-body);
      }

      .reviews-app-container {
        .section__header {
          padding-top: var(--container-gutter);
          border-top: 0.5px solid var(---color-line);
          .subheading {
            font-weight: bold;
          }
        }
      }

      .stamped-container {

        /* ----- Components ----- */

        .fa-star,
        .fa-star-checked,
        .fa-star-half-o,
        .fa-star-o,
        .stamped-fa-star,
        .stamped-fa-star-checked,
        .stamped-fa-star-half-o,
        .stamped-fa-star-o {
          color: var(---color-reviews);
        }

        .stamped-summary-actions-newquestion,
        .stamped-summary-actions-newreview {

          @extend .button;
          @extend .button--small;

          font-size: var(---font-size-button--mobile);

          @include respond-to($medium-up) {
            font-size: var(---font-size-button--desktop);
          }

        }

        .stamped-summary-actions-newquestion {
          --button-background: var(---color--primary--rgb);
        }

        .stamped-summary-actions-newreview {
          --button-background: var(---color--secondary--rgb);
        }

        .stamped-summary-actions-clear,
        .stamped-summary-actions-mobile-filter,
        .stamped-summary-actions-newquestion,
        .stamped-summary-actions-newreview {

          box-shadow: none !important;
          border: 1px solid var(---color--secondary);
          border-radius: 0;

          @extend .subheading;

          &:hover {
            color: var(---color--primary);
            border-color: var(---color--primary);
          }
        }

        .stamped-widget-poweredby {
          display: none !important;
        }


        /* ----- Sections ----- */

        div.stamped-tab-container {

          ul.stamped-tabs {
            margin-top: 1em !important;
            border-bottom: 0.5px solid var(---color-line--dark);

            li {
              color: var(---color--primary) !important;
              padding-left: 0;
              font-family: var(---font-family-body);
              font-weight: var(---font-weight-body);

              &.active {
                border-color: var(---color--primary) !important;
              }

              &:after {
                background-color: var(---background-color--primary);
                font-weight: var(---font-weight-body--bolder);
              }

            }

          }

        }

        // Reviews Summary

        .stamped-summary {

          .stamped-summary-text-1 {
            font-family: var(---font-family-body);

            strong {
              font-weight: var(---font-weight-body--bolder);
              letter-spacing: -0.05em;
            }
          }

          .summary-overview {}

          .summary-rating {
            transition: opacity 0.25s;
          }

          .stamped-summary-ratings {
            border-right: 0.5px solid var(---color-line);
            border-left: 0.5px solid var(---color-line);

            .summary-rating-title {
              color: var(---color--secondary);
              &:before {
                color: var(---color-reviews);
              }
            }

            .summary-rating-bar {
              background-color: RGBA(var(---color--primary--rgb), 0.2);
              opacity: 1;

              .summary-rating-bar-content {
                background-color: var(---color--primary) !important;
              }
            }
          }
        }

        .new-review-form {}

        /* ----- Reviews ----- */

        .stamped-reviews {
          border-top: 0.5px solid var(---color-line);
          border-bottom: 0.5px solid var(---color-line);
        }

        // Reviews Header

        .stamped-reviews-filter {

          margin: 0 !important;

          .stamped-sort-select {

            position: relative;

            margin: 1em 0 !important;
            background-color: transparent !important;
            border-radius: 0 !important;

          }

          .stamped-sort-select-wrapper {
            text-align: left;
            background: transparent;
          }

        }

        // Reviews

        .stamped-review {

          border-top: 0.5px solid var(---color-line--dark);

          .stamped-review-avatar {
            background: var(---color--primary) !important;
            text-shadow: none !important;
          }

          .stamped-review-header {
            .created {
              font-family: var(---font-family-body);
              font-size: var(---font-size-body-small--mobile);
              @media (min-width: 1000px) {
                font-size: var(---font-size-body-small--desktop);
              }

            }
          }

          .stamped-review-header-title {
            margin: 0.5em 0 !important;
            @extend .h6;
          }

        }

        // Share Links

        .stamped-share-links {
          @extend .button;
        }

        // Confirmation Messages

        .stamped-messages {
          padding: 50px;

          .stamped-thank-you {
            border: 0;
            @extend .stamped-content-box;

            p {
              font-size: 1em;
              // extend .h3;
            }
          }

          .stamped-empty-state {
            margin-top: 0;
          }

        }

        .new-question-form,
        .new-review-form {
          @extend .stamped-content-box;
        }


      }

      .stamped-content-box {
        padding: var(--container-gutter) !important;
        background: var(---background-color--content-1);
      }

    }

