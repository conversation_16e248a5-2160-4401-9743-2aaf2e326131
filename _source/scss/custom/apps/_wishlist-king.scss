.product-form__payment-container {

  .wk-button {

    --button--background: var(---color--primary--rgb);

    @extend .button;
    @extend .button--inverted;
    @extend .button--full;

    transition: 0.25s color;

    color: var(---color--primary);
    
    .wk-button__label {
      color: var(---color--primary);
      transition: 0.25s color;
    }

    .wk-button__icon {
      transition: 0.25s color;
    }

    &:focus,
    &:focus-within,
    &:hover {
      color: var(---color-text--reversed-strong);
      .wk-button__icon,
      .wk-button__label {
        color: var(---color-text--reversed-strong);
      }
    }

  }

}

.wk-link--empty .wk-icon__svg {
  stroke-width: 2px;
}

/*  -----------------------------------
    Product Item
    ----------------------------------- */

.wk-grid__item {

  .rating {
    .rating__caption {
      color: var(---color-text--light);
    }
  }

  .wk-product-image {
    border-radius: 10px;
  }

  .wk-product-title {

    color: var(---color-products-title) !important;
    font-weight: var(---font-weight--product-title);

    font-size: var(---font-size-product-title--mobile);

    @include respond-to($small-up){
      font-size: var(---font-size-product-title--desktop);
    }

  }

  .wk-product-info {
    display: flex;
    flex-direction: column;
    gap: 0.25em;
  }

  .wk-product-image {
    background-size: cover;
  }

  .wk-product-form__option__select {
    padding: 1em;
  }

  .wk-product-form__submit {
    @extend .button;
    @extend .button--full;

    --button-primary-color: var(---color--primary--rgb);

  }

  .wk-product-price {
    color: var(---color-price);
    font-weight: var(---font-weight--product-price);
    letter-spacing: -0.05em;

    white-space: nowrap;

    font-size: var(---font-size-product-title--mobile);

    @include respond-to($small-up){
      font-size: var(---font-size-product-title--desktop);
    }
  }

  .wk-product-price--current {

  }

  .wk-product-price--compare {
    color: var(---color-price--compare);
    font-weight: var(---font-weight-body);
  }

}

/*  -----------------------------------
    Wishlist Page
    ----------------------------------- */

.template-page-wishlist,
.template-page-shared-wishlist {

  --wk-product-form__submit--color: white;
  --wk-product-form__submit--background: var(---color--primary);
  --wk-share-button--background: transparent;
  --wk-share-button--color: var(---color--secondary);

}