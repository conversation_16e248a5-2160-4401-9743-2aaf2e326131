.shopify-section--apps {

  .section__box-color-wrapper {
    
    background: RGB(var(--box-background));
    border-radius: var(--block-border-radius);

  }

  .section--apps {
    .text-container {
      margin-bottom: 30px;
    }
  }

}

.shopify-app-block {
  .container--no-horizontal-padding--mobile {
    p {
      @include respond-to($small-down) {
        padding-left: var(--container-gutter);
        padding-right: var(--container-gutter);
      }
    }
  }
}

/* --HIDE APPS-- */
.octane-plugin__checkbox,
shopify-payment-terms {
  display: none !important
}

.swym-wishlist-button-bar {
  padding: 30px 0;
  text-align: center;
}

.swym-added.swym-add-to-wishlist,
.swym-added {
  opacity: 1 !important;
  color: var(---color--tertiary) !important;
}


// Afterpay Text Alignment (PDP)
.product__info {
  square-placement {
    text-align: center;
  }
}


// Tolstoy
.tolstoy-stories-main-container {
  .tolstoy-stories-container {
    .tolstoy-stories-previous-button,
    .tolstoy-stories-next-button {
      z-index: 2;
    }
  }
}