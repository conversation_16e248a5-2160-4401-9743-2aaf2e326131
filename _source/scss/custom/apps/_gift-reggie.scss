/*  ==============================
    Gift Reggie
    ============================== */

.giftreggie-front {
  
  --section-background: 251, 246, 243;

  /* ----- Input Fixes ----- */
  
  .input__field {
    +.input__label {
      transform: scale(0.733) translateY(calc(-24px - 0.5em)) translateX(calc(5px * 0.733));
    }
  }

}

/*  -----------------------------------
    Landing
    ----------------------------------- */

.giftreggie-landing {

  .giftreggie-landing-overview {

    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    border-radius: var(--block-border-radius);
    overflow: hidden;

    .section__color-wrapper {
      --section-background: 251, 246, 243;
    }

    @media (max-width: 1000px) {
      display: flex;
      flex-direction: column;
    }

  }

  .giftreggie-landing-overview__links-inner {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: var(--container-gutter);
  }

  .giftreggie-landing-overview__image {
    img {
      height: 100%;
      object-fit: cover;
    }
  }

  .giftreggie-landing-overview__links {
    align-items: center;
    display: flex;
    width: 100%;

    &:hover,
    &:focus-within {
      .gift-reggie-landing-overview__link {
        opacity: 0.5;

        &:hover,
        &:focus {
          opacity: 1;
        }
      }
    }
  }

  .gift-reggie-landing-overview__link {

    width: 100%;
    padding: 20px;
    margin: 10px 0;

    background: #fff;
    border: 1px solid var(---color-line);
    border-radius: var(--block-border-radius);
    box-shadow: 0 8px 16px rgba(0, 0, 0, .04);

    transition: transform 0.25s, opacity 0.25s;

    h4 {
      margin: 0;
    }

    p {
      color: var(---color-heading-3);
      margin: 0;
    }

    &:hover {
      transform: translateX(10px);
    }

  }

}

/*  -----------------------------------
    Find Registry
    ----------------------------------- */

/*  -----------------------------------
    View Registry
    ----------------------------------- */

.giftreggie-view-registry {

  &.giftreggie-desktop {

    text-align: left;
    border: 1px solid var(---color-line);
    margin-bottom: var(--container-gutter);

    th,
    td {
      padding: 1em 1.5em;
    }

    td {
      padding-top: 0;
    }

    .input__field {
      min-width: unset;
      width: unset;
      max-width: 60px;
    }
  }

  &.giftreggie-mobile {}

}

/*  -----------------------------------
    Edit Registry
    ----------------------------------- */

.giftreggie-registry-summary {

  .giftreggie-desktop {
    text-align: left;
    border: 1px solid var(---color-line);
    margin-bottom: var(--container-gutter);

    th,
    td {
      padding: 1em 1.5em;
    }

    td {
      padding-top: 0;
    }

    th {
      padding-bottom: 0;
    }
  }

  .giftreggie-mobile {
    margin-top: var(--container-gutter);
    padding: var(--container-gutter);
    border: 1px solid var(---color-line);
  }

}

/*  -----------------------------------
    Components
    ----------------------------------- */

    /* ----- Profile Block ----- */

    .registry-profile-block {
      
      padding: var(--container-gutter);
      margin: var(--container-gutter) 0;
      background: RGB(var(--section-background));
      border-radius: var(--block-border-radius);

      .input {
        --section-background: var(---background-color--content-1--rgb);
    
        select,
        textarea,
        textarea,
        input[type="file"],
        input[type="password"],
        input[type="text"] {
          background: RGB(var(--section-background));
        }
      }

    }

    /* ----- Entry Form ----- */

    .giftreggie-entry-form {

      margin: 20px 0;
      padding: var(--container-gutter);

      background: RGB(var(--section-background));
      border-radius: var(--block-border-radius);

      .input {
        --section-background: var(---background-color--content-1--rgb);

        select,
        textarea,
        input[type="file"],
        input[type="password"],
        input[type="text"] {
          background: RGB(var(--section-background));
        }
      }

    }

    /* ----- Mobile Tables ----- */

    .giftreggie-mobile {
      ul {
        padding: 0;
      }

      li {
        margin-top: -1px;
        border: 1px solid var(---color-line);
        padding: var(--container-gutter);
        list-style: none;
      }
    }

    /* ----- Grid ----- */

    .registry-profile-grid {

      display: grid;
      width: 100%;
      gap: var(--container-gutter);

      span {
        display: block;
        width: 100% !important;
      }

      @media (min-width: 1000px) {
        display: grid;
        grid-template-columns: repeat(2, minmax(0, 1fr));
      }

      .input__field {
        width: 100% !important;
      }

    }

    /* ----- Floating Header (Registry) ----- */

    .giftreggie-floating-header {

      position: sticky;
      z-index: 1;
      top: var(--header-height);
      background: var(---background-color--content-2);

      h3 {
        margin: 0;
      }

      .giftreggie-header {
        display: flex;
        justify-content: space-between;
        padding: 20px 0;
      }

      .giftreggie-body {
        padding: 0 !important;
      }

      .giftreggie-admin-menu {
        margin-left: auto;
      }

      @media (max-width: 1000px) {

        .giftreggie-header {
      
          h3 {
            text-align: center;
          }
      
          flex-direction: column;
          align-items: center;
          gap: 10px;
      
        }

      }

    }

    .giftreggie-no-products {
      padding: 10px;
      border-radius: var(--block-border-radius);
      background: RGB(var(---background-color--content-2--rgb));
    }

    /* ----- Notice Banner ----- */

    .giftreggie-notice-banner {
      background: var(---background-color--content-2);
      padding: 20px 30px;
      border-radius: var(--block-border-radius);
    }


/*  -----------------------------------
    Extends
    ----------------------------------- */

    .swym-add-to-wishlist,
    .giftreggie-pdp-registry-cta {
      
      @extend .button;
      @extend .button--small;

      justify-content: center !important;

    }
