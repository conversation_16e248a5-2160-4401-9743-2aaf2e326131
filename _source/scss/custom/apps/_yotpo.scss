/*  ========================================
    YOTPO
    ======================================== */

.yotpo.yotpo-main-widget,
.yotpo {

  div,
  span,
  p,
  a,
  img,
  i,
  strong,
  sup,
  .yotpo ul,
  .yotpo li,
  .yotpo form,
  .yotpo label {
    font-family: var(---font-family-body) !important;
    font-style: unset !important;
    font-size: unset !important;
    line-height: unset !important;
    letter-spacing: unset !important;
  }

  .yotpo a {
    color: var(---color-text--light) !important;
  }

  .yotpo-stars {
    color: var(---color-text--light) !important;
  }

  /* ----- Misc ----- */

  .main-widget {
    .yotpo-reviews-filters {
      margin: 0;
    }

    .yotpo-reviews-header {
      margin: 0;
    }
  }

  .yotpo-header-title {
    margin-bottom: 0.5em !important;
  }

  .reviews-qa-label {

    font-size: var(---font-size-body-small--desktop) !important;
    font-weight: 300 !important;
    width: 100% !important;

    &:after {
      content: ")";
    }

    &:before {
      content: "(";
    }
  }

  .yotpo-regular-box {
    margin: 0 !important;
  }

  .form-group .form-element .yotpo-rating-field .yotpo-rating-field-content .yotpo-rating-field-bars,
  .form-group .form-element .yotpo-size-field .yotpo-size-field-content .yotpo-size-field-bars {
    width: auto;
  }

  /* ----- Labels ----- */
  
  .y-label,
  .yotpo-mandatory-explain {
    
    font-family: var(---font-family-body);
    padding-bottom: 0;
    margin-bottom: 0;

    font-size: var(---font-size-body-small--mobile) !important;
    @include respond-to($medium-up) {
      font-size: var(---font-size-body-small--desktop) !important;
    }

  }
  
  .yotpo-header-title {
    margin-bottom: 0.5em !important;
  }

  
  /* ----- Inputs ----- */

  .y-input {
    margin-top: 0.5em !important;
    margin-bottom: 1em !important;
    border-radius: 8px;
    min-height: 45px;
    border-color: var(---color-line--dark);

    transition: 0.25s border-color;

    &:hover {
      border-color: #000;
    }
  }


  /* ----- Buttons ----- */

  input[type="button"].yotpo-default-button,
  .yotpo-dropdown-button,
  .yotpo-default-button {

    // Mixins

    overflow: hidden;
    position: relative;
    display: inline-flex;
    gap: 20px;
    align-items: center;
    justify-content: space-between;

    margin: 0;
    width: auto;

    height: auto;
    min-height: 0;
    max-height: none;

    border: 0;

    font-family: var(---font-family-heading-alternate);
    font-style: var(---font-style-heading-alternate);
    font-weight: var(---font-weight-heading-alternate);

    line-height: 1.2;
    text-align: center;
    text-transform: unset;

    letter-spacing: var(---button-letter-spacing);
    vertical-align: top;
    // white-space: nowrap;

    border-radius: var(--button-border-radius);
    // background: transparent;

    transition: all 0.2s ease-in-out;
    appearance: none;

    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-smoothing: antialiased;

    cursor: pointer;

    margin-top: 0.25em;
    margin-bottom: 0.25em;
    margin-right: 0.5em;
    margin-left: 0;

    font-size: var(---font-size-button--mobile);
    @include respond-to($small-up) {
      font-size: var(---font-size-button--desktop);
    }

    //

    padding: 1em !important;
    border: 0 !important;
    height: auto;
    background: transparent;
    color: var(---color-text);
    box-shadow: 0 0 0 1px var(---color-text);

    min-width: 230px !important;



    &:focus,
    &:focus-within,
    &:hover {
      background: var(---color--brand-7) !important;
      color: var(---color-text);
      box-shadow: 0 0 0 1px var(---color--brand-7);
    }

    .write-question-review-button-text {
      font-weight: unset;
      width: unset;
      height: unset;
      font-stretch: unset;
      text-align: unset;
      color: unset;
    }

  }

  .yotpo-default-button.desktop-clear-all-btn {
    min-width: unset !important;
    float: none !important;
    padding: 0.75em 1.25em !important;

    .desktop-clear-btn-text {
      color: var(---color-text) !important;
      min-width: unset !important;
    }
  }

  /* ----- Forms ----- */

  .write-question-wrapper,
  .write-review-wrapper {

    .yotpo-header-title {
      color: var(---color-text);
    }

    .yotpo-header-title {
      font-size: var(---font-size-body-large--desktop) !important;
    }

    .stars-wrapper {
      .review-star {
        font-size: 1.4em !important;
      }
    }

  }

  /* ----- Dropdown Buttons ----- */

  .filters-dropdown {
    div.yotpo-dropdown-button {
      margin: 0;
    }
  }

  div.yotpo-dropdown-button {
    gap: 0 !important;
    min-width: unset !important;
    padding: 0.25em 1.25em !important;
    color: var(---color-text) !important;

    span {
      text-align: left !important;
      color: var(---color-text) !important;
    }
  }

  /* ----- Suggested Topics ----- */

  .suggested-topics {

    .suggested-topics-row {
      height: auto !important;
    }

    .suggested-topic {

      @extend .label;
      @extend .label--small;
      @extend .label--custom;

      padding: 0.25em;
      height: auto;
      background: var(---background-color--content-2);
      color: var(---color--text);
      letter-spacing: 0.05em;

      .suggested-topic-text {
        font-size: var(---font-size-body-small--desktop) !important;
      }

      &.active-topic {
        background-color: var(---color--primary);
        color: var(---color-text--reversed);
      }

    }

  }

  /* ----- Buttons ----- */

  .yotpo-icon-btn {
    .yotpo-icon {
      display: none !important;
    }
  }

  /* ----- Pagination ----- */

  .yotpo-page-element {
    
    padding: 15px 20px !important;
    margin: 0 !important;
  
    box-shadow: 0 0 0 1px var(---color-line);
    border: 0 !important;

    &.yotpo-active {
        box-shadow: 0 0 0 1px #000;
        color: #000 !important;
    }

  }

  .yotpo-icon-left-arrow {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
  }

  .yotpo-icon-right-arrow {
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
  }

  /* ----- Rating Stars ----- */

  .rating-stars-container {

    height: 30px !important;

    .rating-star {
      height: 20px !important;
    }

  }

  .yotpo-filter-stars {
    font-size: 20px !important;
  }

  .reviews-qa-labels-container {

    width: 100% !important;

    @media (max-width: 960px) {
      padding-top: 30px !important;
    }
  }

}



/*  ----------------------------------------
  YOTPO Main Widget
  ---------------------------------------- */

.yotpo.yotpo-main-widget {

  position: relative;

  .yotpo-label-container,
  .promoted-products-box {
    display: none !important;
  }

  /* ----- Tabs ----- */

  .yotpo-nav {

    padding-bottom: 1em;
    margin-top: 20px;

    .yotpo-nav-wrapper {

      span {
        text-transform: lowercase !important;
        font-family: var(---font-family-body) !important;
      }
    }

    .yotpo-nav-tab {

      font-weight: unset !important;
      font-family: inherit !important;
      font-size: var(---font-size-body--mobile);

      color: var(---color-text--light) !important;

      @media (min-width: 1000px) {
        font-size: var(---font-size-body--desktop);
      }

      &.yotpo-active {
        .yotpo-nav-wrapper {
          border-bottom: 2px solid #000 !important;
        }

        span {
          color: #000 !important;
        }

      }
    }
  }

  /* ----- Bottom Line Items ----- */

  .avg-score {
    display: none;
  }

  .bottom-line-items {
    float: left;
  }

  .bottom-line-items-container,
  .new-yotpo-small-box {
    margin: 0;
  }

  .bottom-line-items-container {
    border-bottom: 0;
    text-align: left;

    >.yotpo-clr {
      display: none !important;
    }

    >.bottom-line-items {
      display: flex;
      flex-direction: column;

      .reviews-qa-labels-container {
        font-family: 'Lexend Deca';
        line-height: 24px;
        color: #414B4F;
      }

    }
  }


  /* ----- Filters ----- */

  .filters-container {
    .filters-container-title {
        color: #000 !important;
    }
  }

  .more-filters-btn {
    
    @include respond-to($medium-down) {

      padding: 0.8em 1.6em !important;
      border: 0 !important;
      border-radius: 12px;
      height: auto;
      background: transparent;
      color: var(---color-text);
      box-shadow: 0 0 0 1px var(---color-text);

      .more-filters-icon {}

      .more-filters-text {
        color: var(---color--primary);
      }

    }
    
  }

  // Related Fields 

  .pT20 {

    float: left;
    padding: 0;
    display: flex;

    .product-related-fields-desktop-layout {
      @media (min-width: 961px) {
        margin-left: 50px;
      }
    } 

  }

  .product-related-fields-desktop-layout {

    @media (max-width: 960px) {
      display: none !important;
    }

    @media (min-width: 961px) {
      display: flex !important;
      flex-direction: column;
    }

  }

  .product-related-fields-mobile-layout {

    @media (max-width: 960px) {
      display: flex !important;
      flex-direction: column;

    }

    @media (min-width: 961px) {
      display: none !important;
    }

  }

  .aggregated-product-related-fields {

    .product-related-fields-item-value,
    .product-related-fields-item-title {
      color: #000 !important;
    }

    .product-related-fields-item-title {
      @extend .heading;
      @extend .heading--small;
      width: auto !important;
    }

    .product-related-fields-item-value {
      @extend .heading;
      @extend .heading--small;
      text-transform: none !important;
      letter-spacing: 0 !important;
    }

  }

  // Review Buttons

  .write-question-review-buttons-container {
    @media (max-width: 960px) {
      display: flex;
      flex-direction: column;
      float: none !important;
      align-items: flex-start;

      .yotpo-default-button {
        margin-left: 0 !important;
      }
    }

    @media(min-width: 959px) {
      position: absolute;
      top: 30px;
      right: 0;
    }
  }

  // Score Bars

  .yotpo-field-bars-container {

    .yotpo-rating-bars {
      .yotpo-product-related-field-score-bar {
        border-radius: 4px;
      }
    }

    .yotpo-product-related-field-score-bar {
      width: 24px;
      height: 8px;
      margin-top: 8px;
    }

    .yotpo-product-related-field-score-divider {
      width: 3px;
    }

    .product-related-fields-item-value {
      display: flex;
      align-items: center;
    }
  }

  @media (max-width: 960px) {

    .pT20,
    .bottom-line-items {
      float: none !important;
    }

    .pT20 {
      margin-top: 40px !important;
    }

    .rating-stars-container,
    .reviews-qa-labels-container {
      margin-left: 0 !important;
    }

    .product-related-fields-item-value {
      padding-right: 0 !important;
    }

  }


  /* ----- Reviews ----- */

  .reviews-header {

    .reviews-amount {
      color: var(---color--primary);
      
      font-size: var(---font-size-h3--mobile) !important;
      @include respond-to($medium-up) {
        font-size: var(---font-size-h3--desktop) !important;
      }
    }

  }

  .yotpo-question,
  .yotpo-review {

    .yotpo-header-element {
      font-size: var(---font-size-body-small--desktop) !important;
      .yotpo-review-stars  {
        margin-top: 0;
      }
    }

    .yotpo-user-name {
      color: var(---color--primary) !important;
    }

    .yotpo-review-stars {
      margin-top: 0;
    }

    .product-related-fields-desktop-layout {}

    .footer-additional-actions {
      float: none !important;
    }

    .yotpo-helpful {
      margin-top: 1em !important;
    }

  }


}

/*  ----------------------------------------
    Modals
    ---------------------------------------- */

.yotpo-modal-base {

  .mobile-clear-all-btn {
    width: auto !important;
  }

  .radio-label {

    .yotpo-filter-stars {
      margin-top: -20px !important;
      font-size: 16px !important;
      padding-top: 0 !important;
      position: relative;
      top: -5px !important;
      left: -2px;
    }

  }
}