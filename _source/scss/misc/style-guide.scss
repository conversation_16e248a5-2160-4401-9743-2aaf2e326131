@import '../custom/utilities/_variables.scss';
@import '../custom/utilities/_mixins.scss';

.styleguide-section {

  background: var(---background-color--content-2);

  padding: var(--vertical-breather) 0;

  .container {

  }

  .page-header {
  }

  .page-content {
    margin-top: 0;
    margin-bottom: 0;
  }

}

.styleguide-navigation {

  position: sticky;
  top: 0;
  padding: 1em;
  margin-bottom: 2em;

  background: var(---background-color--content-1);
  box-shadow: 0 0 5px rgba(0,0,0,0.1);

}


/* ----- Color Swatches ----- */

.styleguide-color-swatches {

  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  padding: 0;
  margin: 0;

  a {
    margin: 0.25em;
    &:after {
      content: none;
    }
  }

}


/* ----- Docs Sections ----- */

.Rte--preview,
.rte--preview {

  --background: var(---background-color--content-1);

  padding: 60px;
  margin-bottom: 50px;

  background: var(---background-color--content-1);

}

/* ----- Color Swatches ----- */

.docs-color-swatches {
  display: grid;
  grid-template-columns: 12.5% 12.5% 12.5% 12.5% 12.5% 12.5% 12.5% 12.5%;
}

.docs-color-swatch {

  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  align-items: center;

  margin: 0 1em;

  text-align: center;

  .docs-color-swatch__background {

    border-radius: 100%;
    cursor: pointer;

    background-color: var(--swatch-background-color);
    border: 1px solid var(--swatch-border-color);

    transition: 0.25s background, 0.25s color;

    &:hover,
    &:focus {
      background: var(--swatch-background-color--hover);
      .docs-color-swatch__swatch {
        color: var(--swatch-color--hover);
      }
    }

    &:active {
      .docs-color-swatch__swatch {
        color: var(--swatch-color--active);
      }
    }

  }

  .docs-color-swatch__swatch {

    margin: 1em;
    width: 50px;
    height: 50px;

    box-shadow: 0 0 5px rgba(0,0,0,.15) inset;

    border-radius: 100%;
    color: var(--swatch-color);
    background: currentColor;
    transition: 0.25s background, 0.25s color;

  }

  .docs-color-swatch__inner {

    border-radius: 100%;

    background: var(--swatch-background-color);
    border: 1px solid var(--swatch-border-color);

    .docs-color-swatch__swatch {
      box-shadow: none;
    }

  }

  .docs-color-swatch__title {
    margin-top: 0.5em;
  }

  // Brand Colors

  $brand-colors: "brand-1", "brand-2", "brand-3", "brand-4", "brand-5", "brand-6", "brand-7", "brand-8";

  @each $color-name in $brand-colors  {
    &.docs-color-swatch--#{$color-name} {
      --swatch-background-color: var(---background-color--#{$color-name});
      --swatch-background-color--hover: var(---background-color--#{$color-name}-hover);
      --swatch-border-color: var(---color-line--light);
      --swatch-color: var(---color--#{$color-name});
      --swatch-color--hover: var(---color--#{$color-name}-hover);
      --swatch-color--active: var(---color--#{$color-name}-active);
    }
  }

  // System Colors

  $system-colors: "default", "primary", "secondary", "tertiary", "success", "warning", "danger", "info", "disabled";

  @each $color-name in $system-colors  {
    &.docs-color-swatch--#{$color-name} {
      --swatch-background-color: var(---background-color--#{$color-name});
      --swatch-background-color--hover: var(---background-color--#{$color-name}-hover);
      --swatch-border-color: var(--color-swatch);
      --swatch-color: var(---color--#{$color-name});
      --swatch-color--hover: var(---color--#{$color-name}-hover);
      --swatch-color--active: var(---color--#{$color-name}-active);
    }
  }

  // Content Colors

  &.docs-color-swatch--body {
    --swatch-border-color: var(---color-line--light);
    --swatch-color: var(---color-text);
  }

  &.docs-color-swatch--body-strong {
    --swatch-border-color: var(---color-line--light);
    --swatch-color: var(---color-text--dark);
  }

  &.docs-color-swatch--body-light {
    --swatch-border-color: var(---color-line--light);
    --swatch-color: var(---color-text--light);
  }

  &.docs-color-swatch--links {
    --swatch-border-color: var(---color-line--light);
    --swatch-color: var(---color-link);
    --swatch-color--hover: var(---color-link--hover);
  }

  &.docs-color-swatch--heading-1 {
    --swatch-border-color: var(---color-line--light);
    --swatch-color: var(---color-heading-1);
  }

  &.docs-color-swatch--heading-2 {
    --swatch-border-color: var(---color-line--light);
    --swatch-color: var(---color-heading-2);
  }

  &.docs-color-swatch--heading-3 {
    --swatch-border-color: var(---color-line--light);
    --swatch-color: var(---color-heading-3);
  }

  // Layout Colors

  &.docs-color-swatch--background-body {
    --swatch-border-color: var(---color-line--light);
    --swatch-color: var(---background-color--body);
  }

  &.docs-color-swatch--background-content-1 {
    --swatch-border-color: var(---color-line--light);
    --swatch-color: var(---background-color--content-1);
  }

  &.docs-color-swatch--background-content-2 {
    --swatch-border-color: var(---color-line--light);
    --swatch-color: var(---background-color--content-2);
  }

  &.docs-color-swatch--background-content-3 {
    --swatch-border-color: var(---color-line--light);
    --swatch-color: var(---background-color--content-3);
  }

  &.docs-color-swatch--background-reversed-1 {
    --swatch-border-color: var(---color-line--light);
    --swatch-color: var(---background-color--content-reversed-1);
  }

  &.docs-color-swatch--background-reversed-2 {
    --swatch-border-color: var(---color-line--light);
    --swatch-color: var(---background-color--content-reversed-2);
  }
  
  // Gradients
  
    &.docs-color-swatch--gradient-primary {
      --swatch-border-color: var(---color-line--light);
      --swatch-background-color: linear-gradient(115deg, var(---color--primary--light) 20%, var(---color--primary--dark) 80%);;
      --swatch-color: transparent;
    }
  
    &.docs-color-swatch--gradient-secondary {
      --swatch-border-color: var(---color-line--light);
      --swatch-background-color: linear-gradient(115deg, var(---color--secondary--light) 20%, var(---color--secondary--dark) 80%);;
      --swatch-color: transparent;
    }
  
    &.docs-color-swatch--gradient-tertiary {
      --swatch-border-color: var(---color-line--light);
      --swatch-background-color: linear-gradient(115deg, var(---color--tertiary--light) 20%, var(---color--tertiary--dark) 80%);;
      --swatch-color: transparent;
    }

    &.docs-color-swatch--gradient-quarternary {
      --swatch-border-color: var(---color-line--light);
      --swatch-background-color: linear-gradient(115deg, var(---color--quarternary--light) 20%, var(---color--quarternary--dark) 80%);;
      --swatch-color: transparent;
    }

}

/* ----- Logos ----- */

.styleguide-logos {

  display: grid;
  grid-template-columns: 10% 10% 10% 10% 10% 10% 10% 10% 10% 10%;

  background: var(---background-color--content-2);

  &.styleguide-icons--small {
    grid-template-columns: 5% 5% 5% 5% 5% 5% 5% 5% 5% 5% 5% 5% 5% 5% 5% 5% 5% !important;
  }

  > div {

    position: relative;
    background: var(---background-color--content-1);;
    padding: 20px;
    outline: 1px solid #e6e6e6;

    svg {
      width: 100%;
      height: 100%;
      max-height: 20px;
      max-width: 20px;
    }

    &:hover {
      transform: scale(1.5);
      z-index: 1;
      svg {
        * {
          stroke: var(---color--primary);
          fill: var(---background-color--primary);
        }
      }
    }
  }

}
