{% include 'proxy_header' %}

<div class="giftreggie-front giftreggie-registry-password giftreggie-theme-{{ theme.name | handleize }}">

  <div class="section">
    <div class="container">

      <div class="page-content page-content--small">
        <div class="giftreggie-entry-form">

          <form method="get">
            <h2 class="heading h4">{{ 'gift-reggie.registry.password_protected' | t }}</h2>
            {% if active_error %}
              <div class="giftreggie-error">{{ active_error | escape_html }}</div>
            {% endif %}
            <div class="input">
              <input class="input__field" type='text' name='password'>
              <label class="input__label">{{ 'gift-reggie.terms.password' | t }}</label>
            </div>
            <div class="input">
              <button type="submit" class="form__submit button button--small button--primary button--full">{{ 'gift-reggie.registry.password_submit' | t }}</button>
            </div>
          </form>

        </div>
      </div>

    </div>
  </div>

</div>
{% include 'proxy_footer' %}
