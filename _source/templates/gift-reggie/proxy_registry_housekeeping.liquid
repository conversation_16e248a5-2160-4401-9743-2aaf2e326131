<script type='text/javascript'>
	window.jqReady.push(function(jQuery) {
		(function( $ ) {
			if (!getCookie("giftreggie-logged") || JSON.parse(getCookie("giftreggie-logged")).registry_id != {{ id }}) {
				{% unless is_admin %}
					{% if registry_first_popup %}
						$(document).ready(function() { displayModal("{{ registry_first_popup | escape_js | escape_html | newline_to_br }}"); });
					{% endif %}
				{% endunless %}
			}
			setCookie("giftreggie-logged", '{"admin": {{ is_admin }}, "registry_id": {{ id }}, "registry_title": "{{ escaped_name | escape_html }}", "wishlist_id": null }');
			{% if registry_shipping %}
				var address = {{ registry_shipping }};
				setCookie("giftreggie-address", JSON.stringify(address));
			{% endif %}
			var neededAttributes = {};
			{% unless cart.attributes.registry_id == id %}
				neededAttributes['attributes[registry_id]'] = "{{ id }}";
			{% endunless %}
			{% if note_attribute_title%}
				{% unless cart.attributes.registry_title == name %}
					neededAttributes['attributes[registry_title]'] = "{{ name }}";
				{% endunless %}
			{% endif%}
			{% if note_attribute_registrant%}
				{% unless cart.attributes.registrant_name == registrant %}
					neededAttributes['attributes[registrant_name]'] = "{{ registrant }}";
				{% endunless %}
			{% endif%}
			{% if note_attribute_event_date%}
				{% unless cart.attributes.registry_event_date == (event_datetime | date) %}
					neededAttributes['attributes[registry_event_date]'] = "{{ event_datetime | date }}";
				{% endunless %}
			{% endif%}
			{% if note_attribute_type%}
				{% unless cart.attributes.registry_type == type %}
					neededAttributes['attributes[registry_type]'] = "{{ type }}";
				{% endunless %}
			{% endif%}

			{% if is_admin %}
				{% unless cart.attribute.registry_admin %}
					neededAttributes['attributes[registry_admin]'] = true;
				{% endunless %}
			{% else %}
				{% if cart.attribute.registry_admin %}
					neededAttributes['attributes[registry_admin]'] = '';
				{% endif %}
			{% endif %}
			if (Object.keys(neededAttributes).length > 0)
				$.ajax({ url: '{% if testing %}/mock{% endif %}/cart/update.js', type: "POST", dataType: 'json', data: neededAttributes });

		})(jQuery);
	});
</script>


{% if is_admin %}
	<script type='text/javascript'>
	window.jqReady.push(function(jQuery) {
		jQuery('.giftreggie-edit-description button').click(function() {
      function getURL() {
        var vars = getUrlVars();
        var url = "{{ proxy_url }}/registry/{{ id }}/profile";
        if (vars['lang'])
            url += (url.indexOf("?") ? "&" : "?") + "lang=" + encodeURIComponent(vars['lang'])
        return url;
      }
      window.location.href = getURL();
			return false;
		});
	});
	</script>
{% endif %}
