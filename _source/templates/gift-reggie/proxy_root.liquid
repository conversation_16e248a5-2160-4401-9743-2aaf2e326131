{% include 'proxy_header' %}

<div class="giftreggie-front giftreggie-landing giftreggie-theme-{{ theme.name | handleize }}">

  <div class="section">
    
    <div class="container">

      <div class="">

        <h3 class="heading h2 h3--mobile text-center">{{ 'gift-reggie.landing.title' | t }}</h3>
      
        <div class="section__color-wrapper section__color-wrapper--boxed">
          
          <div class="giftreggie-landing-overview">
            
            <div class="giftreggie-landing-overview__image">
              <img src="https://kytebaby.com/cdn/shop/files/baby-sleeps-better.jpg?v=1664918994&width=800" alt="">
            </div>
            
            <div class="giftreggie-landing-overview__links">
              
              <div class="giftreggie-landing-overview__links-inner">
                <a class="gift-reggie-landing-overview__link" href="{{ proxy_url }}/find">
                  <div class="giftreggie-landing-row">
                    <h4 class="h3">{{ 'gift-reggie.terms.find' | t }} &gt;</h4>
                    <p>{{ 'gift-reggie.landing.find' | t }}</p>
                  </div>
                </a>
                {% if customer_account_can_create %}
                  <a class="gift-reggie-landing-overview__link" href="{{ proxy_url }}/{% if customer %}create{% else %}signup{% endif %}">
                    <div class="giftreggie-landing-row">
                      <h4 class="h3">{{ 'gift-reggie.terms.create' | t }} &gt;</h4>
                      <p>{{ 'gift-reggie.landing.create' | t }}</p>
                    </div>
                  </a>
                {% endif %}
                {% capture checkout_url %}{{ proxy_url }}/manage?lang={{ language }}{% endcapture %}
                <a class="gift-reggie-landing-overview__link" href="{% if customer %}{{ checkout_url }}{% else %}{% if language %}{% unless cart.attributes["language"] %}/{{ language }}{% endunless %}{% endif %}/account/login?checkout_url={{ checkout_url | url_escape }}{% endif %}">
                  <div class="giftreggie-landing-row">
                    <h4 class="h3">{{ 'gift-reggie.terms.manage' | t }} &gt;</h4>
                    <p>{{ 'gift-reggie.landing.manage' | t }}</p>
                  </div>
                </a>
              </div>
      
            </div>
      
          </div>
  
        </div>

      </div>
      
    </div>
    
  </div>
</div>

{% include 'proxy_footer' %}
