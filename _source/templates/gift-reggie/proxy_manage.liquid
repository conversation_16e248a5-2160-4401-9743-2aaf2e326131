{% include 'proxy_header' %}

<div class="giftreggie-front giftreggie-manage giftreggie-theme-{{ theme.name | handleize }}">

  <div class="section">
  
    <div class="container">

      {% if user %}
        <div class="giftreggie-header">
          <h3>{{ 'gift-reggie.manage-registries.title' | t }}</h3>
          {% if customer_account_can_create %}
            <p><a class="button button--tiny button--tertiary" href='{{ proxy_url }}/create'>{{ 'gift-reggie.manage-registries.create' | t }}</a></p>
          {% endif %}
          <p>{{ 'gift-reggie.manage-registries.description' | t }}</p>
        </div>
        <div class="giftreggie-body">
          {% if managed_registries.size > 0 %}
            <div class="giftreggie-desktop">
              <table>
                <tr>
                  <th>{{ 'gift-reggie.terms.registry_name' | t }}</th>
                  <th>{{ 'gift-reggie.terms.registry_event_date' | t }}</th>
                  <th>{{ 'gift-reggie.terms.registry_registrant' | t }}</th>
                  <th>{{ 'gift-reggie.terms.registry_coregistrant' | t }}</th>
                </tr>
                {% for registry in managed_registries %}
                <tr>
                  <td><a class="link text--subdued" href='{{ proxy_url }}/registry/{{ registry.id }}'>{{ registry.name | escape_html }}</a></td>
                  <td>{{ registry.event_datetime | date }}</td>
                  <td>{{ registry.registrant | escape_html }}</td>
                  <td>{% if registry.coregistrant %}{{ registry.coregistrant | escape_html }}{% else %}None{% endif %}</td>
                </tr>
                {% endfor %}
              </table>
            </div>
            <div class="giftreggie-mobile">
              <ul>
                {% for registry in managed_registries %}
                <li>
                  <p><a class="link text--subdued" href='{{ proxy_url }}/registry/{{ registry.id }}'>{{ registry.name | escape_html }}</a></p>
                  <p>{{ 'gift-reggie.terms.registry_event_date' | t }}: {{ registry.event_datetime | date }}</p>
                  <p>{{ 'gift-reggie.terms.registry_registrant' | t }}: {{ registry.registrant | escape_html }}</p>
                  {% if registry.coregistrant %}<p>{{ 'gift-reggie.terms.registry_coregistrant' | t }}: {{ registry.coregistrant | escape_html }}</p>{% endif %}
                </li>
                {% endfor %}
              </ul>
            </div>
          {% else %}
            <p style="text-align: center">{{ 'gift-reggie.manage-registries.no_results' | t }}</p>
          {% endif %}
        </div>
      {% else %}
        {% if customer %}
          {{ 'gift-reggie.please_wait_redirect' | t }}
        {% else %}
          {% capture checkout_url %}{{ proxy_url }}/manage?lang={{ language }}{% endcapture %}
          <div class="giftreggie-manage-non-customer"><a href='{% if language %}{% unless cart.attributes["language"] %}/{{ language }}{% endunless %}{% endif %}/account/login?checkout_url={{ checkout_url | url_escape }}'>{{ 'gift-reggie.not_logged' | t }}</a></div>
        {% endif %}
      {% endif %}
  
    </div>

  </div>

</div>

{% include 'proxy_footer' %}
