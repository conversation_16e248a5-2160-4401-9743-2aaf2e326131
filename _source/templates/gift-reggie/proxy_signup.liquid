{% include 'proxy_header' %}

<div class="giftreggie-front giftreggie-signup giftreggie-theme-{{ theme.name | handleize }}">

  <div class="section">

    <div class="container">

      <div class="page-content page-content--small">

        <div class="giftreggie-entry-form">

          <div class="giftreggie-header text-center">
            <h1 class="heading h3">{{ 'gift-reggie.signup.title' | t }}</h1>
            {% if type == "wishlist" %}{% capture translated_type %}{{ 'gift-reggie.terms.wishlist' | t }}{% endcapture %}{% else %}{% capture translated_type %}{{ 'gift-reggie.terms.registry' | t }}{% endcapture %}{% endif %}
            <p>{{ 'gift-reggie.signup.description' | t: type: translated_type }}</p>
          </div>

          <div class="giftreggie-body">
            
            <form action="?" method="post">
              
              <input type='hidden' name='form_type' value='create_customer'/ >
              <input type='hidden' name='return_to' />
              
              <div class="input">
                <input class="input__field" type='text' name="customer[first_name]" id="signup-first-name" />
                <label class="input__label" for="signup_first_name">{{ 'gift-reggie.terms.first_name' | t }}</label>
              </div>
              <div class="input">
                <input class="input__field" type='text' name="customer[last_name]" id="signup-last-name" />
                <label class="input__label" for="signup_last_name">{{ 'gift-reggie.terms.last_name' | t }}</label>
              </div>
              <div class="input">
                <input class="input__field" type='text' name="customer[email]" id="signup-email" />
                <label class="input__label" for="signup_email">{{ 'gift-reggie.terms.email' | t }}</label>
              </div>
              <div class="input">
                <input class="input__field" type='password' name="customer[password]" id="signup-password" />
                <label class="input__label" for="signup_password">{{ 'gift-reggie.terms.password' | t }}</label>
              </div>
              <div class="input">
                <input class="input__field" type='password' name="customer[password_confirmation]" id="signup-password-confirmation" />
                <label class="input__label" for="signup_password_confirmation">{{ 'gift-reggie.terms.password_confirmation' | t }}</label>
              </div>

              {% if recaptcha_verification %}
              <div class="input">
                <span><label for="signup_captcha">{{ 'gift-reggie.create-registry.captcha.title' | t  }}</label></span>
                <span>
                  <div class="registry-captcha">
                    <span>
                      <p><a id="registry-refresh-captcha" href='#'>{{ 'gift-reggie.create-registry.captcha.refresh' | t }}</a></p>
                      <p><img id="registry-captcha" src=''/></p>
                      <input id='registry-captcha-id' type='hidden' name='captcha_id' value=''>
                      <script type='text/javascript'>
                        window.jqReady.push(function(jQuery) {
                          (function( $ ) {
                            function refreshCaptcha(id) {
                              if (!id || id == "" || id == 0)
                                id = Math.floor(Math.random()*100000000000);
                              $('#registry-captcha').attr('src', '');
                              $('#registry-captcha').attr('src', '{{ proxy_url }}/serve_captcha?captcha_id=' + encodeURIComponent(id));
                              $('#registry-captcha-id').val(id);
                            }
                            $('#registry-refresh-captcha').click(function() {
                              refreshCaptcha();
                              return false;
                            });
                            refreshCaptcha("{{ sid }}");
                          })(jQuery);
                        });
                      </script>
                      <p><input type='text' name='captcha' placeholder='{{ 'gift-reggie.create-registry.captcha.please-enter' | t }}'/></p>
                    </span>
                  </div>
                </span>
              </input>
              {% endif %}				

              <div class="input">

                <input class="button button--small button--primary button--full" id="signup-button" is="loader-button" type='submit' value='{{ 'gift-reggie.terms.sign_up' | t }}'/ > 
                {% comment %} <img class='loading-spinner' style='display:none;' src='{{ external_hostline }}/static/img/catalyst/ajax-loader-f5f5f5.gif'/> {% endcomment %}
                
                <span class="form__secondary-action text--subdued">
                  {% capture checkout_url %}{{ proxy_url }}?lang={{ language }}{% endcapture %}
                  {{ 'gift-reggie.signup.returning_customer' | t }} 
                  <a class='registry-link' href='{% if language %}{% unless cart.attributes["language"] %}/{{ language }}{% endunless %}{% endif %}/account/login?checkout_url={{ checkout_url | url_escape }}' class="giftreggie-login-show">{{ 'gift-reggie.terms.login' | t }} &rarr;</a>
                </span>

              </div>

              <div style="{% unless active_error %}display:none;{% endunless %}" class="giftreggie-errors">
                {{ active_error }}
                <script type='text/javascript'>
                    {% if active_error contains 'is disabled' %}
                    window.jqReady.push(function(jQuery) {
                      (function( $ ) {
                          $('.giftreggie-body form').attr("action", "/account/register");
                          $('.giftreggie-body form').submit();
                      });
                    });
                    {% endif %}
                </script>
              </div>

            </form>
          </div>
          
          <div class="giftreggie-footer">&nbsp;</div>

        </div>
      
      </div>
    
    </div>
  
  </div>

</div>

<script type='text/javascript'>

	window.jqReady.push(function(jQuery) {
		(function( $ ) {
			var displayError = function(message) {
				$('.giftreggie-errors').html(message).show();
			}

			var submitForm = function(error_message) {
				var form = $(this);
				return $.ajax({ type: $(this).attr('method'), url: $(this).attr('action'), data: $(this).serialize(), dataType: 'json' });
			}
			
			
			var vars = getUrlVars();
			if (vars['type'] == 'wishlist') {
				$('.registry-text').html("{{ 'gift-reggie.terms.wishlist | t }}");
				$('.registry-text-capitalize').html("{{ 'gift-reggie.terms.wishlist | t | capitalize }}");
				$('.registry-link').each(function() {
					var href = $(this).attr('href');
					if (href.indexOf("?") == -1) 
						href += "?type=wishlist";
					else if (href.slice(-1) == "?")
						href += "type=wishlist";
					else
						href += "&type=wishlist";
					if (vars['id'])
						href += "&id=" + vars['id'];
					$(this).attr('href', href);
				});
			}
			
			$('.giftreggie-signup form').submit(function() {
				var email = $(this).find("input[name='customer[email]']").val();
				if ($(this).find("input[name='customer[password]']").val() != $(this).find("input[name='customer[password_confirmation]']").val()) {
					displayError("{{ 'gift-reggie.signup.errors.password_mismatch' | t }}");
					return false;
				}
				var url = $(this).attr('action');
				$('#signup-button').attr('disabled', 'disabled');
				$('.loading-spinner').show();

				submitForm.call($(this)).done(function(text) {
					function getURL() {
						if (vars['type'] == 'wishlist') {
								var url = "{{ proxy_url }}/wishlist";
								if (vars['id'])
										url += (url.match(/\?/) ? "&" : "?") + "id=" + vars['id'];
								if (vars['lang'])
										url += (url.match(/\?/) ? "&" : "?") + "lang=" + encodeURIComponent(vars['lang'])
								return url;
						}
						else {
								return "{{ proxy_url }}/create" + (vars['lang'] ? "?lang=" + encodeURIComponent(vars['lang']) : "");
						}
					}
					var redirect_url = getURL();
					var login_form = $("<form action='{% if testing %}/mock{% endif %}{% if language %}{% unless cart.attributes['language'] %}/{{ language }}{% endunless %}{% endif %}/account/login' method='POST'>").appendTo("body");
					login_form.append($("<input type='hidden' name='customer[email]' value='" + $('#signup-email').val() + "' />"));
					login_form.append($("<input type='hidden' name='customer[password]' value='" + $('#signup-password').val() + "' />"));
		login_form.append($("<input type='hidden' name='form_type' value='customer_login' />"));
		login_form.append($("<input type='hidden' name='checkout_url' value='" + redirect_url + "' />"));
					login_form.submit();
				}).fail(function(xhr) {
					try {
						var json = JSON.parse(xhr.responseText);
						var error = json.error;
						if (/activation email/i.test(error)) {
							$('.giftreggie-signup form input[type=text], .giftreggie-signup form input[type=password]').val('');
						}
						displayError(error);
					}
					catch (e) {
						displayError("Error signing up.");
					}
					$('#registry-refresh-captcha').click();
					$('input[name="captcha"]').val('');
					$('#signup-button').removeAttr('disabled');
					$('.loading-spinner').hide();
				});
				return false;
			});
		})(jQuery);
	});
</script>

{% include 'proxy_footer' %}