{% include 'proxy_header' %}

<div class="giftreggie-front giftreggie-floating-header giftreggie-admin giftreggie-registry {% if is_admin %}giftreggie-manager{% else %}giftreggie-guest{% endif %} giftreggie-theme-{{ theme.name | handleize }}">

  <div class="giftreggie-floating-header">
  
    <div class="container">
    
      <div class="giftreggie-header">
        <h3 class="heading h4"><a href='{{ proxy_url }}/registry/{{ id }}'>{{ 'gift-reggie.registry.home' | t }}</a></h3>
        {% if is_admin %}
          <ul id="giftreggie-admin-menu">
            <li><a href='{{ proxy_url }}/registry/{{ id }}'>{{ 'gift-reggie.terms.edit' | t }}</a></li>
            <li><a href='{{ proxy_url }}/registry/{{ id }}/share'>{{ 'gift-reggie.terms.share' | t }}</a></li>
            <li><a href='{{ proxy_url }}/registry/{{ id }}/profile'>{{ 'gift-reggie.terms.profile' | t }}</a></li>
            {% if owner_can_see_orders %}<li><a href='{{ proxy_url }}/registry/{{ id }}/orders'>{{ 'gift-reggie.terms.orders' | t }}</a></li>{% endif %}
          </ul>
          <script type='text/javascript'>
            window.jqReady.push(function(jQuery) {
              (function( $ ) {
                var longest_link;
                $('#giftreggie-admin-menu li').each(function() {
                  var location = window.location.hostname + window.location.pathname;
                  var link = $(this).find('a').attr('href')
                  if (location.indexOf(link) != -1 && (!longest_link || longest_link.find("a").attr("href").length < link.length)) {
                    longest_link = $(this);
                  }
                });
                longest_link.addClass('active');
                $('#giftreggie-admin-menu li').click(function() { $('#giftreggie-admin-menu li').removeClass('active'); $(this).addClass('active'); return true; }) 
              })(jQuery);
            });
          </script>
        {% endif %}
        {% unless is_admin %}
          <ul id="giftreggie-client-menu">
            {% if guest_can_message %}<li><a href='{{ proxy_url }}/registry/{{ id }}/message'>{{ 'gift-reggie.terms.message' | t }}</a></li>{% endif %}
          </ul>
        {% endunless %}
      </div>
      <div class="giftreggie-body">
        
        <div id='giftreggie-alert' class='giftreggie-alert' style='display:none;'></div>
        
        <script type='text/javascript'>
          window.jqReady.push(function(jQuery) {
            (function( $ ) {
              GiftReggie.displayMessage = function(type, message) {
                if (GiftReggie.messageTimeout)
                  clearTimeout(GiftReggie.messageTimeout);
                $('#giftreggie-alert').text(message);
                $('#giftreggie-alert').removeClass('giftreggie-success');
                $('#giftreggie-alert').removeClass('giftreggie-error');
                $('#giftreggie-alert').addClass('giftreggie-' + type);
                $('#giftreggie-alert').show();
                GiftReggie.messageTimeout = setTimeout(function() {
                  $('#giftreggie-alert').fadeOut(200);
                }, 5000);
              };
              GiftReggie.displaySuccess = function(message) {
                return GiftReggie.displayMessage('success', message);
              };
              GiftReggie.displayFailure = function(message) {
                return GiftReggie.displayMessage('failure', message);
              };
              GiftReggie.displayError = function(message) {
                return GiftReggie.displayMessage('failure', message);
              };
                
              var vars = getUrlVars();
              if (vars['message'])
                GiftReggie.displaySuccess(vars['message']);
            })(jQuery);
          });
        </script>
  
      </div>
  
    </div>

  </div>

</div>