{% if products.size == 0 %}
  <p class="giftreggie-no-products">
    {{ 'gift-reggie.wishlist.no_products' | t }}
    {% if is_admin -%}
      <a href="{{ browsing_link }}">{{ 'gift-reggie.registry.browsing' | t }}</a>
    {%- endif %}
  </p>
{% endif %}

<div class="giftreggie-band">
  <h2 class="giftreggie-registry-title">{{ name | escape_html }}</h2>
  {% if type and type != 'N/A' %}
    <h3 class="giftreggie-registry-type">{{ type | escape_html }}</h3>
  {% endif %}
  {% if image %}
    <div class="giftreggie-registry-image"><img src="{{ image }}"></div>
  {% endif %}
  {% if message %}
    <div class="giftreggie-store-message">{{ message | escape_html | newline_to_br }}</div>
  {% endif %}
  {% if description %}
    <div class="giftreggie-description">{{ description | escape_html | newline_to_br }}</div>
  {% endif %}
  {% if is_admin %}
    <div class="giftreggie-edit-description padding-bottom--30">
      {% comment %} <button class="button button--tiny button--primary">{{ 'gift-reggie.registry.edit_message' | t }}</button> {% endcomment %}
      <button class="button button--tiny button--primary">Edit Registry</button>
    </div>
  {% endif %}
</div>

<div class="giftreggie-registry-summary">
  <table class="giftreggie-desktop">
    <tr>
      <th>{{ 'gift-reggie.terms.registry_registrant' | t }}</th>
      <th>{{ 'gift-reggie.terms.registry_coregistrant' | t }}</th>
      <th>{{ 'gift-reggie.terms.created' | t }}</th>
      <th>{{ 'gift-reggie.terms.registry_event_date' | t }}</th>
      {% if is_admin %}
        <th>{{ 'gift-reggie.terms.status' | t }}</th>
        <th>{{ 'gift-reggie.terms.items' | t }}</th>
        {% assign value_bool = 1 %}
        {% for product in products %}
          {% for p in product.properties %}
            {% if p.name contains '_boldVariantIds' %}
              {% assign value_bool = 0 %}
            {% endif %}
          {% endfor %}
        {% endfor %}
        {% if value_bool %}
          <th>{{ 'gift-reggie.terms.value' | t }}</th>
        {% endif %}
      {% endif %}
    </tr>
    <tr>
      <td>{{ registrant | escape_html }}</td>
      <td>
        {% if coregistrant %}{{ coregistrant | escape_html }}{% else %}{{ 'gift-reggie.terms.none' | t }}{% endif %}
      </td>
      <td>{{ created_datetime | date }}</td>
      <td>{{ event_datetime | date }}</td>
      {% if is_admin %}
        <td>{{ status }}</td>
        <td>{{ item_count }}</td>
        {% if value_bool %}
          <td>
            <span class="money">{{ value | money }}</span>
          </td>
        {% endif %}
      {% endif %}
    </tr>
  </table>
  <div class="giftreggie-mobile">
    <p>{{ 'gift-reggie.terms.registry_registrant' | t }}: {{ registrant | escape_html }}</p>
    <p>
      {% if coregistrant -%}
        {{- 'gift-reggie.terms.registry_coregistrant' | t }}: {{ coregistrant | escape_html -}}
      {%- endif %}
    </p>
    <p>{{ 'gift-reggie.terms.created' | t }}: {{ created_datetime | date }}</p>
    <p>{{ 'gift-reggie.terms.registry_event_date' | t }}: {{ event_datetime | date }}</p>
    {% if is_admin %}
      <p>{{ 'gift-reggie.terms.status' | t }}: {{ status }}</p>
      <p>{{ 'gift-reggie.terms.items' | t }}: {{ item_count }}</p>
      <p>
        {{ 'gift-reggie.terms.value' | t }}: <span class="money">{{ value | money }}</span>
      </p>
    {% endif %}
  </div>
</div>

{% if products.size > 0 %}
  {% if max_pages > 1 %}
    <div class="giftreggie-items-page">
      {{ 'gift-reggie.items_per_page' | t }}
      <select id="per-page-selector">
        <option>25</option>
        <option>50</option>
        <option>100</option>
      </select>
    </div>
  {% endif %}
  <form method="post" class="giftreggie-registry-form">
    <input type="hidden" name="extra" value="{{ extra | escape_html }}">
    <table class="giftreggie-desktop giftreggie-view-registry" id="view-registry">
      <tr>
        <th>{{ 'gift-reggie.terms.line' | t }}</th>
        <th>{{ 'gift-reggie.terms.product_title' | t }}</th>
        <th>{{ 'gift-reggie.terms.product_image' | t }}</th>
        <th>{{ 'gift-reggie.terms.product_price' | t }}</th>
        <th>{{ 'gift-reggie.registry.wants' | t }}</th>
        <th>{{ 'gift-reggie.registry.has' | t }}</th>
        <th>{{ 'gift-reggie.registry.needs' | t }}</th>
        <th>
          {% if is_admin %}
            {{ 'gift-reggie.terms.delete' | t }}
          {% else %}
            {{ 'gift-reggie.terms.cart' | t }}
          {% endif %}
        </th>
      </tr>
      {% for product in products %}
        <input type="hidden" name="item-id" value="{{ product.id }}">
        <tr class="registry-variant">
          <td>
            {% if is_admin %}
              <input
                type="text"
                class="giftreggie-line-number input__field"
                name="position-{{ product.id }}"
                value="{{ product.line_number }}"
              >
            {% else %}
              {{ product.line_number }}
            {% endif %}
          </td>
          <td>
            {% if product.still_needs > 0 %}
              <a
                class="giftreggie-item-name"
                href="{% if testing %}/mock{% endif %}{% if language %}{% unless cart.attributes["language"] %}/{{ language }}{% endunless %}{% endif %}/products/{{ product.handle | escape_html }}?variant={{ product.variant_id }}"
              >
                {{- product.title | escape_html -}}
              </a>
            {% else %}
              <span class="giftreggie-item-name">{{ product.title | escape_html }}</span>
            {% endif %}
            {% if product.properties %}
              <div class="properties">
                {% assign bold_price = 0 %}
                {% for property in product.properties %}
                  {% if property.name contains '_boldVariantPrices' %}
                    {% assign bold_price = property.value %}
                  {% endif %}
                  {% if property.name contains 'bold' %}
                    <div style="display: none" class="property property-{{ property.name | handleize }}">
                      <span class="property-name">{{ property.name | escape_html }}</span>
                      <span class="property-value">{{ property.value | escape_html }}</span>
                    </div>
                  {% else %}
                    <div class="property property-{{ property.name | handleize }}">
                      <span class="property-name">{{ property.name | escape_html }}</span>
                      <span class="property-value">{{ property.value | escape_html }}</span>
                    </div>
                  {% endif %}
                {% endfor %}
              </div>
            {% endif %}
          </td>
          <td><img src="{{ product.image | img_url: "128x128" }}"></td>
          <td>
            <span class="money">{{ (product.price + bold_price) | money }}</span>
          </td>
          <td>
            {% if is_admin %}
              <input
                type="text"
                class="giftreggie-want-quantity input__field"
                name="want-quantity-{{ product.id }}"
                maxlength="3"
                value="{{ product.wants }}"
              >
            {% else %}
              {{ product.wants }}
            {% endif %}
          </td>
          <td>{{ product.has }}</td>
          <td class="giftreggie-needs-quantity">{{ product.still_needs }}</td>
          <input type="hidden" class="variant-id" name="variant-id" value="{{ product.variant_id }}">
          <input type="hidden" class="product-id" name="product-id" value="{{ product.product_id }}">
          {% if is_admin %}
            <td>
              <!--
                If we have a 'has' quantity associated with this, remove the option to remove the item, but keep it available in the back-end for those special cases.
              -->
              {% if product.has %}
                &nbsp;
              {% else %}
                <button name="action" value="delete-{{ product.id }}">&times;</button>
              {% endif %}
            </td>
          {% else %}
            <td>
              {% if product.still_needs > 0 %}
                <input type="text" class="input__field registry-variant-quantity" maxlength="3" name="amount" value="0">
              {% endif %}
            </td>
          {% endif %}
        </tr>
      {% endfor %}
    </table>
    <ul class="giftreggie-mobile giftreggie-view-registry">
      {% for product in products %}
        <li class="registry-variant">
          <input type="hidden" name="item-id" value="{{ product.id }}">
          <input type="hidden" class="variant-id" name="variant-id" value="{{ product.variant_id }}">
          <input type="hidden" class="product-id" name="product-id" value="{{ product.product_id }}">
          <p class="giftreggie-item-image">
            {% if product.still_needs > 0 or is_admin -%}
              <a
                href="{% if testing %}/mock{% endif %}{% if language %}{% unless cart.attributes["language"] %}/{{ language }}{% endunless %}{% endif %}/products/{{ product.handle | escape_html }}"
              >
            {%- endif -%}
            <img src="{{ product.image }}">
            {%- if product.still_needs > 0 or is_admin %}</a>{% endif %}
          </p>
          <p>
            {% if product.still_needs > 0 or is_admin %}
              <a
                class="giftreggie-item-name"
                href="{% if testing %}/mock{% endif %}{% if language %}{% unless cart.attributes["language"] %}/{{ language }}{% endunless %}{% endif %}/products/{{ product.handle | escape_html }}"
              >
                {{- product.title | escape_html -}}
              </a>

            {% else %}
              <span class="giftreggie-item-name">{{ product.title | escape_html }}</span>
            {% endif %}
            {% if product.properties %}
              <div class="properties">
                {% assign bold_price = 0 %}
                {% for property in product.properties %}
                  {% if property.name is '_boldVariantPrices' %}
                    {% assign bold_price = property.value %}
                  {% endif %}
                  {% if property.name contains 'bold' %}
                    <div style="display: none" class="property property-{{ property.name | handleize }}">
                      <span class="property-name">{{ property.name | escape_html }}</span>
                      <span class="property-value">{{ property.value | escape_html }}</span>
                    </div>
                  {% else %}
                    <div class="property property-{{ property.name | handleize }}">
                      <span class="property-name">{{ property.name | escape_html }}</span>
                      <span class="property-value">{{ property.value | escape_html }}</span>
                    </div>
                  {% endif %}
                {% endfor %}
              </div>
            {% endif %}
          </p>
          <p>
            <span class="money">{{ (product.price + bold_price) | money }}</span>
            {% unless is_admin -%}
              {%- if product.still_needs > 0 -%}
                <button class="giftreggie-add-to-cart btn">{{ 'gift-reggie.cart-update.add' | t }}</button>
              {%- endif -%}
            {%- endunless %}
          </p>
          <p>
            <table>
              <tr>
                <th>{{ 'gift-reggie.registry.wants' | t }}</th>
                <td>
                  {% if is_admin -%}
                    <input
                      type="text"
                      class="giftreggie-want-quantity"
                      name="want-quantity-{{ product.id }}"
                      maxlength="3"
                      value="{{ product.wants }}"
                    >
                  {%- else -%}
                    {{- product.wants -}}
                  {%- endif %}
                </td>
                <th>{{ 'gift-reggie.registry.has' | t }}</th>
                <td>{{ product.has }}</td>
                <th>{{ 'gift-reggie.registry.needs' | t }}</th>
                <td class="giftreggie-needs-quantity">{{ product.still_needs }}</td>
              </tr>
            </table>
          </p>
        </li>
      {% endfor %}
    </ul>
    {% if is_admin %}
      <div class="action-buttons">
        <span>
          <button class="button button--danger button--small" id="discard-changes">{{ 'gift-reggie.terms.discard_changes' | t }}</button>
          </span>
        <span>
          <button class="button button--primary button--small" id="save-changes" name="action" value="update">{{ 'gift-reggie.terms.save_changes' | t }}</button>
        </span
        >
      </div>
      <script type="text/javascript">
        window.jqReady.push(function (jQuery) {
          (function ($) {
            $('#view-registry input').keypress(function (e) {
              if (e.which == 13) {
                $('#save-changes').click();
                return false;
              }
            });
            $('#discard-changes').click(function () {
              window.location.href = window.location.href;
              return false;
            });
          })(jQuery);
        });
      </script>
    {% else %}
      {% if registry_notes %}
        <div class="giftreggie-notes giftreggie-desktop">
          <h4>{{ 'gift-reggie.cart-update.note' | t }}</h4>
          <textarea name="note">{{ cart.note | escape }}</textarea>
        </div>
      {% endif %}
      <div class="action-buttons giftreggie-desktop">
        <span><button class="button button--secondary button--small" id="registry-update-cart">{{ 'gift-reggie.cart-update.update' | t }}</button></span>
        <span>{% include 'snippet-giftreggie-checkout-button' %}</span>
      </div>
      <script type="text/javascript">
        window.jqReady.push(function (jQuery) {
          (function ($) {
            $('.registry-variant-quantity').keypress(function (e) {
              if (e.which == 13) {
                $('#registry-update-cart').click();
                return false;
              }
            });
          })(jQuery);
        });
      </script>
    {% endif %}
    {% if max_pages > 1 %}
      <div id="pagination-div"></div>
    {% endif %}
    <div id="status-bar" style="display:none;">{{ 'gift-reggie.cart-update.success' | t }}</div>
  </form>
  <script type="text/javascript">
    window.jqReady.push(function(jQuery) {
    	(function( $ ) {
    		createPagination($('#pagination-div'), {{ max_pages }});

    		$('#per-page-selector').change(function() {
    			var url = window.location.href.replace(/[\?\&](limit|page)=\d+/g, "").replace(/^([^\?])+&/, "$1\?");
    			url += (url.indexOf("?") == -1) ? "?" : "&";
    			window.location = url + "limit=" + $(this).val();
    		});

    		var vars = getUrlVars();
    		if (vars['limit'])
    			$('#per-page-selector').val(vars['limit']);

    		$('.giftreggie-add-to-cart').click(function() {
    			var variantRow = $(this).parents(".registry-variant").first();
    			var id = variantRow.find(".variant-id").val();

    			var prop_names = variantRow.find('.property-name');
    			var prop_values = variantRow.find('.property-value');
    			var properties = "";
    			for(var i = 0; i < prop_names.length; ++i){
    				properties = properties + '<input name="properties[' + prop_names[i].textContent + ']" value="' + prop_values[i].textContent + '">'
    			}
    			$('<form method="post" action="/cart/add"><input type="hidden" name="id" value="' + id + '"/><input type="hidden" name="quantity" value="1"/>' + properties + '</form>').appendTo("body").submit();
    			return false;
    		});

    		$('.giftreggie-registry-form').submit(function() {
    			$('.giftreggie-view-registry').each(function() {
    				var div = $(this);
    				if (!div.is(":visible")) {
    					div.find("input").remove();
    				}
    			});
    		});
    	})(jQuery);
    });
  </script>
  {% include 'proxy_cart_update' %}
  {% if is_admin %}
    <p class="giftreggie-browse-add-products text-center">
      <a href="{% if language %}{% unless cart.attributes["language"] %}/{{ language }}{% endunless %}{% endif %}{{ browsing_link }}" class="text-center">
        {{- 'gift-reggie.registry.browsing' | t -}}
      </a>
    </p>
  {% endif %}
{% endif %}
