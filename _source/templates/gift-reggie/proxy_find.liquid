{% include 'proxy_header' %}

<div class="giftreggie-front giftreggie-find giftreggie-theme-{{ theme.name | handleize }}">

  <div class="section">
  
    {% if searched %}

      <div class="container">
        <div class="page-content">

          <div class="giftreggie-header text-center">
            <h1 class="heading h3">{{ 'gift-reggie.find-registry.result_title' | t }}</h1>
            {% if search_results and search_results.size > 0 %}
              <p><a href='{{ proxy_url }}/find'>{{ 'gift-reggie.find-registry.find_again' | t }}</a></p>
            {% endif %}
          </div>
          <div class="giftreggie-body">
            {% if search_results.size > 0 %}
              <table class="giftreggie-desktop">
                <tr>
                  <th>{{ 'gift-reggie.terms.registry_name' | t }}</th>
                  <th>{{ 'gift-reggie.terms.registry_event_date' | t }}</th>
                  <th>{{ 'gift-reggie.terms.registry_registrant' | t }}</th>
                  <th>{{ 'gift-reggie.terms.registry_coregistrant' | t }}</th>
                </tr>
                {% for result in search_results %}
                <tr>
                  <td><a class="link text--subdued" href='{{ proxy_url }}/registry/{{ result.id }}'>{{ result.name | escape_html }}</a></td>
                  <td>{{ result.event_datetime | date }}</td>
                  <td>{{ result.registrant | escape_html }}</td>
                  <td>{% if result.coregistrant %}{{ result.coregistrant | escape_html }}{% else %}None{% endif %}</td>
                </tr>
                {% endfor %}
              </table>
              <div class="giftreggie-mobile">
                <ul>
                  {% for registry in search_results %}
                  <li>
                    <p><a href='{{ proxy_url }}/registry/{{ registry.id }}'>{{ registry.name | escape_html }}</a></p>
                    <p><strong>{{ 'gift-reggie.terms.registry_event_date' | t }}:</strong> {{ registry.event_datetime | date }}</p>
                    <p><strong>{{ 'gift-reggie.terms.registry_registrant' | t }}:</strong> {{ registry.registrant | escape_html }}</p>
                    <p><strong>{% if registry.coregistrant %}{{ 'gift-reggie.terms.registry_coregistrant' | t }}:</strong> {{ registry.coregistrant | escape_html }}{% endif %}</p>
                  </li>
                  {% endfor %}
                </ul>
              </div>
            {% else %}
              <p><a href='{{ proxy_url }}/find'>{{ 'gift-reggie.find-registry.no_results' | t }}</a></p>
            {% endif %}		
          </div>

        </div>
      </div>


    {% else %}

      <div class="container">
    
        <div class="page-content page-content--small">

          <div class="giftreggie-entry-form">

            <div class="giftreggie-header text-center">
              <h1 class="heading h3">{{ 'gift-reggie.find-registry.title' | t }}</h1>
              <p>{{ 'gift-reggie.find-registry.description' | t }}</p>
            </div>
        
            <div class="giftreggie-body">
              <form action="?" method="get" class="gift-reggie-search-form">
                <div class="input">
                  <input class="input__field" name='first-name' type='text'/>
                  <label class="input__label">{{ 'gift-reggie.terms.first_name' | t }}</label>
                </div>
                <div class="input">
                  <input class="input__field" name='last-name' type='text'/>
                  <label class="input__label">{{ 'gift-reggie.terms.last_name' | t }}</label>
                </div>
                <div class="input">
                  <input class="form__submit button button--primary button--small button--full" type='submit' is="loader-button" value='{{ 'gift-reggie.terms.find' | t }}'/>
                </div>
              </form>
            </div>

          </div>

        </div>

      </div>
  
    {% endif %}

    <div class="giftreggie-footer">
      
      {% if user %}
      
        <p class="text--xsmall text-center">
          <a href='{{ proxy_url }}/create'>{{ 'gift-reggie.find-registry.create_manage' | t }}</a>
        </p>

      {% else %}

        <div class="container">

          <div class="text-center">
            
            {% if customer_account_integration %}
              {% if customer %}
                {{ 'gift-reggie.please_wait_redirect' | t }}
              {% else %}
                {% capture checkout_url %}{{ proxy_url }}/find?lang={{ language }}{% endcapture %}
                <p class="text--xsmall text-center">
                  <a href='{% if language %}{% unless cart.attributes["language"] %}/{{ language }}{% endunless %}{% endif %}/account/login?checkout_url={{ checkout_url | url_escape }}'>{{ 'gift-reggie.not_logged' | t }}</a>
                </p>
              {% endif %}
            {% endif %}

          </div>

        </div>

      {% endif %}

    </div>
  
  </div>

</div>

{% include 'proxy_footer' %}