
<div class="registry-profile">
	<div class="registry-profile-block">

    <div class="registry-profile-grid">

      <span>
      
        <h4>{{ 'gift-reggie.registry-profile.title' | t }}</h4>

        <div class="input">
          <input class="input__field" name='registry-title' type='text' value='{{ name | escape_html }}'/>
          <label class="input__label" for="event-date">{{ 'gift-reggie.terms.registry_title' | t }}</label>
        </div>
        
        {% assign input_format = datetime_format_input | replace: "%m", "MM" | replace: "%d", "DD" | replace: "%Y", "YYYY" | replace: "%y", "YY" %}

        <div class="input">
          <input class="input__field" name='event-date' class="datepicker" type='text' {% unless event_datetime %}placeholder="{{ input_format }}"{% endunless %} value='{{ event_datetime | date: datetime_format_input }}'/>
          <label class="input__label" for="event-date">{{ 'gift-reggie.terms.registry_event_date' | t }} ({{ input_format }}) </label>
        </div>
        
        <div class="input">
          <textarea class="input__field input__field--textarea" name='registry-description'>{{ description | escape_html }}</textarea>
          <label class="input__label" for="registry-description">{{ 'gift-reggie.registry-profile.public_message' | t }}</label>
        </div>
    
        {% if event_types.size > 0 %}
          <div class="input">
            <select class="input__field" id="event-type" name='event-type' style="width: 100%;">
              {% for type in event_types %}<option>{{ type.name | escape_html }}</option>{% endfor %}
            </select>
            <label class="input__label" for="event-type">{{ 'gift-reggie.terms.registry_event_type' | t }}</label>
          </div>
        {% endif %}

      </span>
      
      <span>

        <h4>
          {% if image %}
            <a target="_blank" href='{{ image }}'>
          {% endif %}

          {{ 'gift-reggie.terms.registry_image' | t }}

          {% if image %}
            </a>
          {% endif %}
        </h4>

        <div class="input">

          <input class="input__field" type='file' name='registry-image'> 

          {% if image %}
            <button name='action' value='delete-image'>{{ 'gift-reggie.terms.registry_remove_image' | t }}</button>
          {% endif %}
          
          <p class="text text--xsmall">{{ 'gift-reggie.registry-profile.max_size' | t }}</p>

        </div>

      </span>
    
    </div>
  
	</div>

	<div class="registry-profile-block">

    <div class="registry-profile-grid">

      <span>
        <h4>{{ 'gift-reggie.registry-profile.registrant_info' | t }}</h4>
        <div class="input">
          <input class="input__field" name='registrant-title' type='text' maxlength="16" value="{{ registrant_title | escape_html }}"/>
          <label class="input__label" for="registrant-title">{{ 'gift-reggie.terms.title' | t }}</label>
        </div>
        <div class="input">
          <input class="input__field" name='registrant-first' type='text' {% unless registrant_first %}placeholder="{{ customer.first_name }}"{% endunless %} value="{% if registrant_first %}{{ registrant_first | escape_html }}{% else %}{{ customer.first_name }}{% endif %}"/>
          <label class="input__label" for="registrant-first">{{ 'gift-reggie.terms.first_name' | t }}</label>
        </div>
        <div class="input">
          <input class="input__field" name='registrant-last' type='text' {% unless registrant_last %}placeholder="{{ customer.last_name }}"{% endunless %} value="{% if registrant_last %}{{ registrant_last | escape_html }}{% else %}{{ customer.last_name }}{% endif %}"/>
          <label class="input__label" for="registrant-last">{{ 'gift-reggie.terms.last_name' | t }}</label>
        </div>
      </span>

      <span>
        <h4>{{ 'gift-reggie.registry-profile.coregistrant_info' | t }}</h4>
        <div class="input">
          <input class="input__field" name='coregistrant-title' type='text' maxlength="16" value="{{ coregistrant_title | escape_html }}"/>
          <label class="input__label" for="coregistrant-title">{{ 'gift-reggie.terms.title' | t }}</label>
        </div>
        <div class="input">
          <input class="input__field" name='coregistrant-first' type='text' value="{{ coregistrant_first | escape_html }}"/>
          <label class="input__label" for="coregistrant-first">{{ 'gift-reggie.terms.first_name' | t }}</label>
        </div>
        <div class="input">
          <input class="input__field" name='coregistrant-last' type='text' value="{{ coregistrant_last | escape_html }}"/>
          <label class="input__label" for="coregistrant-last">{{ 'gift-reggie.terms.last_name' | t }}</label>
        </div>
      </span>
    
    </div>

	</div>

	{% if allow_passwords %}
	<div class="registry-profile-block">
    <div class="registry-profile-grid">
      <span>
        <h4>{{ 'gift-reggie.registry-profile.protection.title' | t }} <input type='checkbox' id='registry-protection' {% if has_password %}checked{% endif %}></h4>
        <div class="input">
          {{ 'gift-reggie.registry-profile.protection.description' | t }}
        </div>
        <div class="input">
          <input class="input__field" type='password' name='registry-password' placeholder='{{ 'gift-reggie.registry-profile.protection.blank' | t }}' {% unless has_password %}disabled{% endunless %} type='text'/>
          <label class="input__label">{{ 'gift-reggie.terms.password' | t }}</label>
        </div>
      </span>
    </div>
	</div>
	{% endif %}
	<div class="registry-profile-block">
    <h4>{{ 'gift-reggie.registry-profile.contact_info' | t }}</h4>
    <div class="registry-profile-grid">
      <span>
        <div class="input">
          <input class="input__field" id='contact-address' name='contact-address' type='text' value="{{ contact_address | escape_html }}"/>
          <label class="input__label" for="contact-address">{{ 'gift-reggie.terms.address' | t }}</label>
        </div>
        <div class="input">
          <input class="input__field" id='contact-city' name='contact-city' type='text' value="{{ contact_city | escape_html }}"/>
          <label class="input__label" for="contact-city">{{ 'gift-reggie.terms.city' | t }} </label>
        </div>
        <div class="input">
          <input class="input__field" id='contact-province' name='contact-province' type='text' value="{{ contact_province | escape_html }}"/>
          <label class="input__label" for="contact-province">{{ 'gift-reggie.terms.province' | t }} </label>
        </div>
        <div class="input">
          <select class='input__field country-selector' id='contact-country' name='contact-country'>{% include 'proxy_countries' %}</select>
          <label class="input__label" for='contact-country'>{{ 'gift-reggie.terms.country' | t }} </label>
        </div>
        <div class="input">
          <input class="input__field" id='contact-postal' name='contact-postal' type='text' value="{{ contact_postal | escape_html }}"/>
          <label class="input__label" for='contact-postal'>{{ 'gift-reggie.terms.zip' | t }} </label>
        </div>
      </span>
      <span>
        <div class="input">
          <input class="input__field" id='contact-daytime' name='contact-daytime' type='text' value="{{ contact_daytime | escape_html }}"/>
          <label class="input__label" for="contact-daytime">{{ 'gift-reggie.terms.daytime_telephone' | t }}</label>
        </div>
        <div class="input">
          <input class="input__field" id='contact-evening' name='contact-evening' type='text' value="{{ contact_evening | escape_html }}"/>
          <label class="input__label" for="contact-evening">{{ 'gift-reggie.terms.evening_telephone' | t }}</label>
        </div>
        <div class="input">
          <input class="input__field" id='contact-email' name='contact-email' type='text' placeholder="{{ customer.email }}" value="{% if contact_email %}{{ contact_email | escape_html }}{% else %}{{ customer.email }}{% endif %}"/>
          <label class="input__label" for="contact-email">{{ 'gift-reggie.terms.email' | t }}</label>
        </div>
        <div class="input">
          <input class="input__field" id="contact-email-verify" name="contact-email-verify" type='text' placeholder="{{ customer.email }}" value="{% if contact_email %}{{ contact_email | escape_html }}{% else %}{{ customer.email }}{% endif %}"/>
          <label class="input__label" for="contact-email-verify">{{ 'gift-reggie.terms.verify_email' | t }} 
        </div>
        <div class="input">
          <div class="checkbox-container">
            <input class="checkbox" id='email-managers' name='email-managers' type='checkbox' {% if email_managers %}checked{% endif %}/> 
            <label for="email-managers">Send notification emails to registry managers (in addition to contact email)</label>
          </div>
        </div>
      </span>
      <span>
        
        <div class="input">
          <select class="input__field" id='about-type' name='about-type'>
          {% unless about_required %}<option>N/A</option>{% endunless %}
          {% for type in about_types %}
            <option>{{ type.name | escape_html }}</option>
          {% endfor %}
          </select>
          <label class="input__label" for="about-type">{{ 'gift-reggie.registry-profile.how_did_you_hear' | t }} </label>
        </div>
      </span>
    </div>
	</div>
	{% if allow_pickup %}
		<div class="registry-profile-block">
      <div class="registry-profile-grid">
        <div class="input">
          <select class="input__field" id='store-pickup' name='store-pickup'>
            <option value='0'>{{ 'gift-reggie.registry-profile.ship_items' | t }}</option>
            <option value='1' {% if in_store_pickup %}selected{% endif %}>{{ 'gift-reggie.registry-profile.pickup_items' | t }}</option>
          </select>
          <label class="input__label" for="about-type">{{ 'gift-reggie.registry-profile.ship_items' | t }}</label>
        </div>
      </div>
		</div>
	{% elsif force_pickup %}
		<input type='hidden' name='store-pickup' value='1'/>
	{% endif %}
	{% unless force_pickup %}
		<div class="registry-profile-block">
			<h4>{{ 'gift-reggie.registry-profile.shipping_info' | t }}</h4>
			<div class="registry-profile-grid">
        <span>
          <div class="input">
            {{ 'gift-reggie.registry-profile.before_title' | t }}
            <select {% if in_store_pickup %}disabled{% endif %} id="before-shipping-selector">
              <option value='above'>{{ 'gift-reggie.terms.above' | t }}</option>
              <option value='below' {% if before_address %}selected{% endif %}>{{ 'gift-reggie.terms.below' | t }}</option>
            </select>
            <br>
          </div>
          <div class="before-shipping">
            <br>
            <div class="input">
              <input class="input__field" name='before-address' type='text' {% if in_store_pickup %}disabled{% endif %} {% unless before_address %}disabled{% endunless %} value="{{ before_address | escape_html }}"/>
              <label class="input__label" for="">{{ 'gift-reggie.terms.address' | t }} </label>
            </div>
            <div class="input">
              <input class="input__field" name='before-city' type='text' {% if in_store_pickup %}disabled{% endif %} {% unless before_address %}disabled{% endunless %} value="{{ before_city | escape_html }}"/>
              <label class="input__label" for="">{{ 'gift-reggie.terms.city' | t }} </label>
            </div>
            <div class="input">
              <input class="input__field" name='before-province' {% if in_store_pickup %}disabled{% endif %}  type='text' {% unless before_address %}disabled{% endunless %} value="{{ before_province | escape_html }}"/>
              <label class="input__label" for="">{{ 'gift-reggie.terms.province' | t }} </label>
            </div>
            <div class="input">
              <select class="input__field"  name='before-country' class='country-selector' {% if in_store_pickup %}disabled{% endif %} {% unless before_address %}disabled{% endunless %}>{% include 'proxy_countries' %}</select>
              <label class="input__label" for="">{{ 'gift-reggie.terms.country' | t }} </label>
            </div>
            <div class="input">
              <input class="input__field" name='before-postal' {% if in_store_pickup %}disabled{% endif %} type='text' {% unless before_address %}disabled{% endunless %} value="{{ before_postal | escape_html }}"/>
              <label class="input__label" for="">{{ 'gift-reggie.terms.zip' | t }} </label>
            </div>
          </div>
        </span>
        <span>
          <div class="input">
            {{ 'gift-reggie.registry-profile.after_title' | t }}
            <select {% if in_store_pickup %}disabled{% endif %} id="after-shipping-selector">
              <option value='above'>{{ 'gift-reggie.terms.above' | t }}</option>
              <option value='below' {% if after_address %}selected{% endif %}>{{ 'gift-reggie.terms.below' | t }}</option>
            </select>
            <br>
          </div>
          <div class="after-shipping">
            <br>
            <div class="input">
              <input class="input__field" name='after-address' type='text' {% if in_store_pickup %}disabled{% endif %} {% unless after_address %}disabled{% endunless %} value="{{ after_address | escape_html }}"/>
              <label class="input__label" for="">{{ 'gift-reggie.terms.address' | t }}</label>
            </div>
            <div class="input">
              <input class="input__field" name='after-city' type='text' {% if in_store_pickup %}disabled{% endif %} {% unless after_address %}disabled{% endunless %} value="{{ after_city | escape_html }}"/>
              <label class="input__label" for="">{{ 'gift-reggie.terms.city' | t }}</label>
            </div>
            <div class="input">
              <input class="input__field" name='after-province' type='text' {% if in_store_pickup %}disabled{% endif %} {% unless after_address %}disabled{% endunless %} value="{{ after_province | escape_html }}"/>
              <label class="input__label" for="">{{ 'gift-reggie.terms.province' | t }}</label>
            </div>
            <div class="input">
              <select class="input__field" name='after-country' class='country-selector' {% if in_store_pickup %}disabled{% endif %} {% unless after_address %}disabled{% endunless %}>{% include 'proxy_countries' %}</select>
              <label class="input__label" for="">{{ 'gift-reggie.terms.country' | t }}</label>
            </div>
            <div class="input">
              <input class="input__field" name='after-postal' type='text' {% if in_store_pickup %}disabled{% endif %} {% unless after_address %}disabled{% endunless %} value="{{ after_postal | escape_html }}"/>
              <label class="input__label" for="">{{ 'gift-reggie.terms.zip' | t }}</label>
            </div>
          </div>
        </span>
      </div>
		</div>
	{% endunless %}
</div>
<script type='text/javascript'>
	window.jqReady.push(function(jQuery) {
		(function( $ ) {
			{% if default_country %}$('select.country-selector').val('{{ default_country }}');{% endif %}
			{% if contact_country %}$('select[name="contact-country"]').val("{% if (contact_country | downcase) == "us" or (contact_country | downcase) == "usa" %}United States{% else %}{{ contact_country | escape_html }}{% endif %}");{% endif %}
			{% if before_country %}$('select[name="before-country"]').val("{% if (before_country | downcase) == "us" or (before_country | downcase) == "usa" %}United States{% else %}{{ before_country | escape_html }}{% endif %}");{% endif %}
			{% if after_country %}$('select[name="after-country"]').val("{% if (after_country | downcase) == "us" or (after_country | downcase) == "usa" %}United States{% else %}{{ after_country | escape_html }}{% endif %}");{% endif %}
			
			$('input[name=registry-image]').change(function() {
				if (this.files[0].size >= 5*1024*1024) {
					alert("Images can be a maximum of 5 megabytes.");
					$(this).val('');
				}
			});
			
			$('#store-pickup').change(function() {
				if ($(this).val() == "1") {																
					$('#before-shipping-selector').val("{{ 'gift-reggie.terms.above' | t }}").change();
					$('#after-shipping-selector').val("{{ 'gift-reggie.terms.above' | t }}").change();
					$('#before-shipping-selector').attr('disabled', 'disabled');
					$('#after-shipping-selector').attr('disabled', 'disabled');
				} else {
					$('#before-shipping-selector').removeAttr('disabled');
					$('#after-shipping-selector').removeAttr('disabled');
					$('#before-shipping-selector').change();
					$('#after-shipping-selector').change();
				}
			});
			
			{% if event_type %}
				$('#event-type').val("{{ event_type | escape_html }}");
			{% endif %}
			{% if about_type %}
				$('#about-type').val("{{ about_type | escape_html }}");
			{% endif %}
			
			function toggleFields(value, ba) {
				if (value == 'below') {
					$('.' + ba + '-shipping input, .' + ba + '-shipping select').removeAttr('disabled');
				}
				else {
					$('.' + ba + '-shipping input, .' + ba + '-shipping select').attr('disabled', 'disabled');
					$('.' + ba + '-shipping input').val('');
				}
			}

			$('#before-shipping-selector').change(function() {
				toggleFields($(this).val(), "before");
			});
			$('#after-shipping-selector').change(function() {
				toggleFields($(this).val(), "after");
			});
			$('#registry-protection').click(function() {
				if (!$(this).is(":checked")) {
					$('input[name="registry-password"]').attr('disabled','disabled');
				} else {
					$('input[name="registry-password"]').removeAttr('disabled');
				}
				return true;
			});
		})(jQuery);
	});
</script>
