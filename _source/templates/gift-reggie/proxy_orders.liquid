{% include 'proxy_registry_header' %}

<div class="section">

  <div class="container">

    {% if orders.size > 0 %}

      <table class='giftreggie-orders'>
        
        <tr>
          <th>{{ 'gift-reggie.terms.order_number' | t }}</th>
          <th>{{ 'gift-reggie.terms.date' | t }}</th>
          <th>{{ 'gift-reggie.terms.user' | t }}</th>
          <th>{{ 'gift-reggie.terms.email' | t }}</th>
          <th>{{ 'gift-reggie.terms.purchase' | t }}</th>
          <th>{{ 'gift-reggie.terms.product_price' | t }}</th>
          <th>Quantity</th>
        </tr>

        {% for order in orders %}
          <tr>
            <td>{{ order.order_name | escape_html }}</td>
            <td>{{ order.created_datetime | date }}</td>
            <td>{{ order.name | escape_html }}</td>
            <td>{{ order.email | escape_html }}</td>
            <td>{{ order.product_name | escape_html }}</td>
            <td>{{ order.product_purchased_price }}</td>
            <td>{{ order.product_quantity }}</td>
          </tr>
        {% endfor %}

      </table>

    {% else %}

      <p class="giftreggie-no-orders giftreggie-notice-banner">{{ 'gift-reggie.manage-registries.no-orders' | t }}</p>

    {% endif %}
  
  </div>

</div>

<style type='text/css'>
	.giftreggie-profile-buttons {
		text-align: center;
		width: 100%;
		margin: 12px 0;
	}
</style>

{% include 'proxy_registry_footer' %}
