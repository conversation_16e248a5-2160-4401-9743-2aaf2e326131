<!DOCTYPE html>
<html lang="en">
  <head>
    <!--[if gte mso 15]>
      <xml>
        <o:OfficeDocumentSettings>
          <o:AllowPNG />
            <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
        </xml>
    <![endif]-->
    <meta name="viewport" content="width=device-width">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="x-apple-disable-message-reformatting">
    <meta http-equiv="Content-Type" content="text/html; charset=US-ASCII">
    <!-- Gift card created email -->
    <style type="text/css" data-premailer="ignore">
      /* What it does: Remove spaces around the email design added by some email clients. */
          /* Beware: It can remove the padding / Margin and add a background color to the compose a reply window. */
          html, body {
            Margin: 0 auto !important;
            padding: 0 !important;
            width: 100% !important;
              height: 100% !important;
          }
          /* What it does: Stops email clients resizing small text. */
          * {
            -ms-text-size-adjust: 100%;
            -webkit-text-size-adjust: 100%;
            text-rendering: optimizeLegibility;
              -webkit-font-smoothing: antialiased;
              -moz-osx-font-smoothing: grayscale;
          }
          /* What it does: Forces Outlook.com to display emails full width. */
          .ExternalClass {
            width: 100%;
          }
          /* What is does: Centers email on Android 4.4 */
          div[style*="Margin: 16px 0"] {
              Margin:0 !important;
          }
          /* What it does: Stops Outlook from adding extra spacing to tables. */
          table,
          th {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
          }
          /* What it does: Fixes Outlook.com line height. */
          .ExternalClass,
          .ExternalClass * {
            line-height: 100% !important;
          }
          /* What it does: Fixes webkit padding issue. Fix for Yahoo mail table alignment bug. Applies table-layout to the first 2 tables then removes for anything nested deeper. */
          table {
            border-spacing: 0 !important;
            border-collapse: collapse !important;
            border: none;
            Margin: 0 auto;
            direction: ltr;
          }
          div[style*="Margin: 16px 0"] {
              Margin:0 !important;
          }
          /* What it does: Uses a better rendering method when resizing images in IE. */
          img {
            -ms-interpolation-mode:bicubic;
          }
          /* What it does: Overrides styles added when Yahoo's auto-senses a link. */
          .yshortcuts a {
            border-bottom: none !important;
          }
          /* What it does: Overrides blue, underlined link auto-detected by iOS Mail. */
          /* Create a class for every link style needed; this template needs only one for the link in the footer. */
          /* What it does: A work-around for email clients meddling in triggered links. */
          *[x-apple-data-detectors],  /* iOS */
          .x-gmail-data-detectors,    /* Gmail */
          .x-gmail-data-detectors *,
          .aBn {
              border-bottom: none !important;
              cursor: default !important;
              color: inherit !important;
              text-decoration: none !important;
              font-size: inherit !important;
              font-family: inherit !important;
              font-weight: inherit !important;
              line-height: inherit !important;
          }
          /* What it does: Overrides blue, underlined link auto-detected by Gmail. */
          u   #body a {
              color: inherit;
              text-decoration: none;
              font-size: inherit;
              font-family: inherit;
              font-weight: inherit;
              line-height: inherit;
          }
      
          /* What it does: Prevents Gmail from displaying an download button on large, non-linked images. */
          .a6S {
              display: none !important;
              opacity: 0.01 !important;
          }
          /* If the above doesn't work, add a .g-img class to any image in question. */
          img.g-img   div {
              display:none !important;
          }
          /* What it does: Prevents underlining the button text in Windows 10 */
          a,
          a:link,
          a:visited {
              color: #666666;
              text-decoration: none !important;
          }
          .header a {
              color: ;
              text-decoration: none;
              text-underline: none;
          }
          .main a {
              color: #666666;
              text-decoration: none;
              text-underline: none;
              word-wrap: break-word;
          }
          .main .section.customer_and_shipping_address a,
          .main .section.shipping_address_and_fulfillment_details a {
              color: #212121;
              text-decoration: none;
              text-underline: none;
              word-wrap: break-word;
          }
          .footer a {
              color: #000000;
              text-transform: none;
              text-decoration: none;
              text-underline: none;
          }
      
          /* What it does: Overrides styles added images. */
          img {
            border: none !important;
            outline: none !important;
            text-decoration:none !important;
          }
          td.menu_bar_1 a:hover,
          td.menu_bar_6 a:hover {
            color: #666666 !important;
          }
          th.related_product_wrapper.first {
            border-right: 10px solid #ffffff;
            padding-right: 5px;
          }
          th.related_product_wrapper.last {
            border-left: 10px solid #ffffff;
            padding-left: 5px;
          }
    </style>
    <!--[if (mso)|(mso 16)]>
      <style type="text/css" data-premailer="ignore">
        a {text-decoration: none;}
      </style>
    <![endif]-->
    <!--[if gte mso 9]>
      <style>
        li {
              text-indent: -1em; /* Normalise space between bullets and text */
          }
      </style>
    <![endif]-->
    <!--[if !mso]><!-->
  <style type="text/css" data-premailer="ignore">
    /* What it does: Fixes fonts for Google WebFonts; */
    [style*="Lexend Deca"] {
        font-family: 'Lexend Deca',-apple-system,BlinkMacSystemFont,'Segoe UI',Arial,sans-serif !important;
    }
    [style*="Lexend Deca"] {
        font-family: 'Lexend Deca',-apple-system,BlinkMacSystemFont,'Segoe UI',Arial,sans-serif !important;
    }
    [style*="Lexend Deca"] {
        font-family: 'Lexend Deca',-apple-system,BlinkMacSystemFont,'Segoe UI',Arial,sans-serif !important;
    }
    [style*="Lexend Deca"] {
        font-family: 'Lexend Deca',-apple-system,BlinkMacSystemFont,'Segoe UI',Arial,sans-serif !important;
    }
  </style>
  <link href="https://fonts.googleapis.com/css?family=Lexend Deca:400,700|Lexend Deca:400,700|Lexend Deca:400,700|Lexend Deca:400,700&amp;subset=latin-ext" rel="stylesheet" type="text/css" data-premailer="ignore">
  <!--<![endif]-->
      <style type="text/css" data-premailer="ignore">
        /* Media Queries */
            /* What it does: Removes right gutter in Gmail iOS app */
            @media only screen and (min-device-width: 375px) and (max-device-width: 413px) { /* iPhone 6 and 6  */
                .container {
                    min-width: 375px !important;
                }
            }
            /* Main media query for responsive styles */
            @media only screen and (max-width:480px) {
              /* What it does: Overrides email-container's desktop width and forces it into a 100% fluid width. */
              .email-container {
                width: 100% !important;
                min-width: 100% !important;
              }
              .section > th {
                padding: 10px 10px 10px 10px !important;
              }
              .section.divider > th {
                padding: 20px 10px !important;
              }
              .main .section:first-child > th,
              .main .section:first-child > td {
                  padding-top: 20px !important;
              }
                .main .section:nth-last-child(2) > th,
                .main .section:nth-last-child(2) > td {
                    padding-bottom: 40px !important;
                }
              .section.recommended_products > th,
              .section.discount > th,
              .section.personal_discount > th {
                  padding: 20px 10px !important;
              }
              /* What it does: Forces images to resize to the width of their container. */
              img.fluid,
              img.fluid-centered {
                width: 100% !important;
                min-width: 100% !important;
                max-width: 100% !important;
                height: auto !important;
                Margin: auto !important;
                box-sizing: border-box;
              }
              /* and center justify these ones. */
              img.fluid-centered {
                Margin: auto !important;
              }
        
              /* What it does: Forces table cells into full-width rows. */
              th.stack-column,
              th.stack-column-left,
              th.stack-column-center,
              th.related_product_wrapper,
              .column_1_of_2,
              .column_2_of_2 {
                display: block !important;
                width: 100% !important;
                min-width: 100% !important;
                direction: ltr !important;
                box-sizing: border-box;
              }
              /* and left justify these ones. */
              th.stack-column-left {
                text-align: left !important;
              }
              /* and center justify these ones. */
              th.stack-column-center,
              th.related_product_wrapper {
                text-align: center !important;
                border-right: none !important;
                border-left: none !important;
              }
              .column_button,
              .column_button > table,
              .column_button > table th {
                width: 100% !important;
                text-align: center !important;
                Margin: 0 !important;
              }
              .column_1_of_2 {
                padding-bottom: 20px !important;
              }
              .column_1_of_2 th {
                  padding-right: 0 !important;
              }
              .column_2_of_2 th {
                  padding-left:  0 !important;
              }
              .column_text_after_button {
                padding: 0 10px !important;
              }
              /* Adjust product images */
              th.table-stack {
                padding: 0 !important;
              }
              th.product-image-wrapper {
                  padding: 20px 0 10px 0 !important;
              }
              img.product-image {
                    width: 160px !important;
                    max-width: 160px !important;
              }
              tr.row-border-bottom th.product-image-wrapper {
                border-bottom: none !important;
              }
              th.related_product_wrapper.first,
              th.related_product_wrapper.last {
                padding-right: 0 !important;
                padding-left: 0 !important;
              }
              .text_banner th.banner_container {
                padding: 10px !important;
              }
              .mobile_app_download .column_1_of_2 .image_container {
                padding-bottom: 0 !important;
              }
              .mobile_app_download .column_2_of_2 .image_container {
                padding-top: 0 !important;
              }
              .image_with_text th.column_1_of_2 {
                padding: 20px !important;
                padding-bottom: 10px !important;
              }
              .image_with_text th.column_2_of_2 {
                padding: 20px !important;
                padding-top: 10px !important;
              }
              .images_in_2_columns th.column_1_of_2 {
                padding: 0 !important;
                padding-bottom: 10px !important;
              }
              .images_in_2_columns th.column_2_of_2 {
                padding: 0 !important;
                padding-top: 10px !important;
              }
            }
      </style>
      <style type="text/css" data-premailer="ignore">
        /* Custom Media Queries */
          @media only screen and (max-width:480px) {
            .email-container > tbody > tr > th {
                padding: 0 0;
            }
            .header .section_wrapper_th,
            .main .section_wrapper_th,
            .column_unsubscribe {
                padding-right: 10px !important;
                padding-left: 10px !important;
            }
            .column_logo {
                display: block !important;
                width: 100% !important;
                min-width: 100% !important;
                direction: ltr !important;
                text-align: center !important;
            }
            p,
            .column_1_of_2 th p,
            .column_2_of_2 th p,
            .order_notes * {
                text-align: center !important;
            }
            .line-item-description p {
                text-align: left !important;
            }
            .line-item-price p,
            .line-item-qty p,
            .line-item-line-price p {
              text-align: right !important;
            }
            h1, h2, h3,
            .column_1_of_2 th,
            .column_2_of_2 th {
                text-align: center !important;
            }
            td.order-table-title {
              text-align: center !important;
            }
            .section.products_with_pricing .table-title,
            .section.products_with_refund .table-title,
            .section.products_with_exchange .table-title,
            .section.products_with_exchange_v2 .table-title {
                width: 65% !important;
            }
            .section.products_with_pricing .table-text,
            .section.products_with_refund .table-text,
            .section.products_with_exchange .table-text,
            .section.products_with_exchange_v2 .table-title {
                width: 35% !important;
            }
          }
      </style>
    </head>
    <body class="body" id="body" leftMargin="0" topMargin="0" Marginwidth="0" Marginheight="0" bgcolor="#ffffff" style="-webkit-text-size-adjust: none; -ms-text-size-adjust: none; Margin: 0; padding: 0;">
      <!--[if !mso 9]><!-->
        <div style="display: none; overflow: hidden; line-height: 1px; max-height: 0px; max-width: 0px; opacity: 0; mso-hide: all;">Here's your digital gift card from Kyte Baby.
        </div>
      <!--<![endif]-->
        <div id="custom-preview-text" style="display: none; overflow: hidden; line-height: 1px; max-height: 0px; max-width: 0px; opacity: 0; mso-hide: all;">&#847;&#8204;&nbsp;&#847;&#8204;&nbsp;&#847;&#8204;&nbsp;&#847;&#8204;&nbsp;&#847;&#8204;&nbsp;&#847;&#8204;&nbsp;&#847;&#8204;&nbsp;&#847;&#8204;&nbsp;&#847;&#8204;&nbsp;&#847;&#8204;&nbsp;&#847;&#8204;&nbsp;&#847;&#8204;&nbsp;&#847;&#8204;&nbsp;&#847;&#8204;&nbsp;&#847;&#8204;&nbsp;&#847;&#8204;&nbsp;&#847;&#8204;&nbsp;&#847;&#8204;&nbsp;&#847;&#8204;&nbsp;&#847;&#8204;&nbsp;&#847;&#8204;&nbsp;&#847;&#8204;&nbsp;&#847;&#8204;&nbsp;&#847;&#8204;&nbsp;&#847;&#8204;&nbsp;&#847;&#8204;&nbsp;</div>
        {% capture default_utms %}utm_campaign=gift-card-created-email&utm_medium=email&utm_source=OrderlyEmails{% endcapture %}
          {% capture question_mark %}?{% endcapture %}
            <!-- CM COMMERCE -->
            <!-- BEGIN: CONTAINER -->
            <table class="container container_full" cellpadding="0" cellspacing="0" border="0" width="100%" style="border-collapse: collapse; min-width: 100%;" role="presentation" bgcolor="#ffffff">
              <tbody>
                <tr>
                  <th valign="top" style="mso-line-height-rule: exactly;">
                    <center style="width: 100%;">
                      <table border="0" width="660" cellpadding="0" cellspacing="0" align="center" style="width: 660px; min-width: 660px; max-width: 660px; Margin: auto;" class="email-container" role="presentation">
                        <tbody>
                          <tr>
                            <th valign="top" style="mso-line-height-rule: exactly; padding: 10px 0;">
                              <!-- BEGIN : SECTION : HEADER -->
                              <table class="section_wrapper header" data-id="header" id="section-header" border="0" width="100%" cellpadding="0" cellspacing="0" align="center" style="min-width: 100%;" role="presentation" bgcolor="#ffffff">
                                <tr>
                                  <td class="section_wrapper_th" style="mso-line-height-rule: exactly; padding: 20px 40px 10px;" bgcolor="#ffffff">
                                    <table border="0" width="100%" cellpadding="0" cellspacing="0" align="center" style="min-width: 100%;" role="presentation">
                                      <tr>
                                        <th class="column_logo" style="mso-line-height-rule: exactly; padding-top: 5px; padding-bottom: 5px;" align="center" bgcolor="#ffffff">
                                          <!-- Logo : BEGIN -->
                                          <a href="{% capture link %}{{ shop.url }}{% endcapture %}{% if link contains shop.domain %}{% assign split_link = link | split: question_mark %}{{ split_link[0] }}?{{ default_utms }}&utm_content=logo{% if split_link[1] != blank %}&{{ split_link[1] }}{% endif %}{% else %}{{ link }}{% endif %}" target="_blank" style="text-decoration: none !important; text-underline: none;">
                                            <img src="https://cdn.filestackcontent.com/api/file/RSplkYBOT4ymOt6ZcJAd/convert?fit=max&w=422" class="logo " width="211" border="0" style="width: 211px; height: auto !important; display: block; text-align: center; Margin: auto;">
                                          </a>
                                          <!-- Logo : END -->
                                        </th>
                                      </tr>
                                    </table>
                                  </td>
                                </tr>
                              </table>
                              <!-- END : SECTION : HEADER -->
                              <!-- BEGIN : SECTION : MAIN -->
                              <table class="section_wrapper main" data-id="main" id="section-main" border="0" width="100%" cellpadding="0" cellspacing="0" align="center" style="min-width: 100%;" role="presentation" bgcolor="#ffffff">
                                <tr>
                                  <td class="section_wrapper_th" style="mso-line-height-rule: exactly; padding-right: 40px; padding-left: 40px;" bgcolor="#ffffff">
                                    <table border="0" width="100%" cellpadding="0" cellspacing="0" align="center" style="min-width: 100%;" id="mixContainer" role="presentation">
                                      <!-- BEGIN SECTION: Introduction -->
                                      <tr id="section-6425016" class="section introduction">
                                        <th style="mso-line-height-rule: exactly; padding: 20px;" bgcolor="#ffffff">
                                          {% if gift_card.customer.first_name != blank or billing_address.first_name != blank %}
                                            <p style="mso-line-height-rule: exactly; direction: ltr; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Arial,'Lexend Deca'; font-size: 14px; line-height: 20px; font-weight: 400; text-transform: none; color: #212121; Margin: 0 0 10px;" align="center">
                                              
                                              <span data-key="6425016_greeting_text" style="text-align: center; direction: ltr; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Arial,'Lexend Deca'; font-size: 14px; line-height: 20px; font-weight: 400; text-transform: none; color: #212121;">
                                                Hey
                                              </span>
                                              
                                              {%- capture first_name -%}
                                                {%- if gift_card.recipient.first_name != blank -%}
                                                  {{ gift_card.recipient.first_name }}
                                                {%- elsif gift_card.customer.first_name != blank -%}
                                                  {{ gift_card.customer.first_name }}
                                                {%- else -%}
                                                  {{ billing_address.first_name }}
                                                {%- endif -%}
                                              {%- endcapture -%}

                                              {{ first_name }}
                                              
                                            </p>
                                          {% endif %}

                                          {%- capture custom_message -%}
                                            {%- if gift_card.message != blank -%}
                                              {{ gift_card.message }}
                                            {%- endif -%}
                                          {%- endcapture -%}

                                          {% if custom_message != blank %}
                                            <p style="mso-line-height-rule: exactly; direction: ltr; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Arial,'Lexend Deca'; font-size: 14px; line-height: 20px; font-weight: 400; text-transform: none; color: #212121; Margin: 10px 0;" align="center">{{ custom_message }}</p>
                                          {% endif %}
                                          <span data-key="6425016_introduction_text" class="text" style="text-align: center; direction: ltr; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Arial,'Lexend Deca'; font-size: 14px; line-height: 20px; font-weight: 400; text-transform: none; color: #212121;">
                                            <p style="mso-line-height-rule: exactly; direction: ltr; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Arial,'Lexend Deca'; font-size: 14px; line-height: 20px; font-weight: 400; text-transform: none; color: #212121; Margin: 10px 0 0;" align="center"></p>
                                            <p style="mso-line-height-rule: exactly; direction: ltr; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Arial,'Lexend Deca'; font-size: 14px; line-height: 20px; font-weight: 400; text-transform: none; color: #212121; Margin: 10px 0 0;" align="center">Here's your digital gift card from Kyte Baby.</p>
                                          </span>
                                        </th>
                                      </tr>
                                      <!-- END SECTION: Introduction -->
                                      <!-- BEGIN SECTION: Heading -->
                                      <tr id="section-6425017" class="section heading">
                                        <th style="mso-line-height-rule: exactly; border-top-left-radius: 4px; border-top-right-radius: 4px; color: #1a1a1a; padding: 20px;" bgcolor="#ffffff">
                                          <table cellspacing="0" cellpadding="0" border="0" width="100%" role="presentation">
                                            <tr>
                                              <th style="mso-line-height-rule: exactly; color: #1a1a1a;" bgcolor="#ffffff" valign="top">
                                                <h1 data-key="6425017_heading" style="font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Arial,'Lexend Deca'; font-size: 24px; line-height: 32px; font-weight: 400; color: #1a1a1a; text-transform: none; letter-spacing: 0.5px; background-color: #ffffff !important; Margin: 0;">Your Gift Card is Ready</h1>
                                              </th>
                                            </tr>
                                          </table>
                                        </th>
                                      </tr>
                                      <!-- END SECTION: Heading -->
                                      <!-- BEGIN SECTION: Gift Card Balance -->
                                      <tr id="section-6425018" class="section gift_card_balance">
                                        <th style="mso-line-height-rule: exactly; padding: 20px;" bgcolor="#ffffff">
                                          <h2 data-key="6425018_heading" style="font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Arial,'Lexend Deca'; color: #1a1a1a; font-size: 16px; line-height: 20px; font-weight: 400; text-transform: none; letter-spacing: 0.5px; Margin: 0;" align="center">Available Balance</h2>
                                          <p style="mso-line-height-rule: exactly; direction: ltr; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Arial,'Lexend Deca'; font-size: 14px; line-height: 20px; font-weight: 400; text-transform: none; color: #212121; Margin: 0;" align="center">{{ gift_card.balance | money }}</p>
                                        </th>
                                      </tr>
                                      <!-- END SECTION: Gift Card Balance -->
                                      <!-- BEGIN SECTION: Button -->
                                      <tr id="section-6425020" class="section button">
                                        <th style="mso-line-height-rule: exactly; padding: 20px;" bgcolor="#ffffff">
                                          <table cellspacing="0" cellpadding="0" border="0" width="100%" role="presentation">
                                            <tr>
                                              <th class="sides column_text_before_button" data-key="6425020_text_before_button" style="mso-line-height-rule: exactly; direction: ltr; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Arial,'Lexend Deca'; font-size: 14px; line-height: 20px; font-weight: 400; text-transform: none; color: #212121; padding-bottom: 10px;" align="center" bgcolor="#ffffff" valign="top">To view or print your gift card, click the button below:
                                              </th>
                                            </tr>
                                            <!-- Button : BEGIN -->
                                            <tr>
                                              <th class="sides column_button button5" style="mso-line-height-rule: exactly; Margin: 0; padding: 10px 0;" align="center" bgcolor="#ffffff" valign="top">
                                                <table cellspacing="0" cellpadding="0" border="0" class="button" role="presentation" style="text-align: center; Margin: 0 auto;" bgcolor="transparent">
                                                  <tr>
                                                    <th class="button-inner" style="mso-line-height-rule: exactly; border-radius: 1px;" align="center" bgcolor="#000000" valign="top">
                                                      <a class="button-link" href="{{gift_card.url}}" target="_blank" style="color: #ffffff !important; text-decoration: none !important; text-underline: none; word-wrap: break-word; line-height: 14px; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Arial,'Lexend Deca'; font-size: 14px; font-weight: 400; text-transform: none; text-align: center; display: block; background-color: #000000; border-radius: 1px; padding: 1px 20px; border: 15px solid #000000;"><span class="text-on-button" style="line-height: 14px; color: #ffffff; font-weight: 400; text-decoration: none; text-underline: none; letter-spacing: 0.5px;"><!--[if mso]>&nbsp;&nbsp;&nbsp;&nbsp;<![endif]--><span class="text-on-button-inner" data-key="6425020_text_on_button" style="line-height: 14px; color: #ffffff; font-weight: 400; text-decoration: none; text-underline: none; letter-spacing: 0.5px;">View eGift Card</span><!--[if mso]>&nbsp;&nbsp;&nbsp;&nbsp;<![endif]--></span></a>
                                                    </th>
                                                  </tr>
                                                </table>
                                              </th>
                                            </tr>
                                            <!-- Button : END -->
                                          </table>
                                        </th>
                                      </tr>
                                      <!-- END SECTION: Button -->
                                      <!-- BEGIN SECTION: Text -->
                                      <tr id="section-17224917" class="section text" style="direction: ltr; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Arial,'Lexend Deca'; font-size: 14px; line-height: 20px; font-weight: 400; text-transform: none; color: #212121;" align="center">
                                        <th data-key="17224917_text" class="text " style="mso-line-height-rule: exactly; direction: ltr; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Arial,'Lexend Deca'; font-size: 14px; line-height: 20px; font-weight: 400; text-transform: none; color: #212121; padding: 20px 20px 40px;" align="center" bgcolor="#ffffff">
                                          <p style="mso-line-height-rule: exactly; direction: ltr; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Arial,'Lexend Deca'; font-size: 14px; line-height: 20px; font-weight: 400; text-transform: none; color: #212121; Margin: 0;" align="center"><b>Purchased this gift card for someone else? Feel free to forward them this email.</b></p>
                                        </th>
                                      </tr>
                                      <!-- END SECTION: Text -->
                                      <tr data-id="link-list">
                                        <td class="menu_bar menu_bar_6" style="mso-line-height-rule: exactly; padding: 20px 0;" bgcolor="#ffffff">
                                          <table class="table_menu_bar" border="0" width="100%" cellpadding="0" cellspacing="0" role="presentation">
                                            <tr>
                                              <th class="menu_bar_item first" style="width: 33%; mso-line-height-rule: exactly; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Arial,'Lexend Deca'; font-size: 14px; font-weight: 400; line-height: 20px; text-transform: uppercase; color: #212121; border-right-width: 2px; border-right-color: #d3d3d3; border-right-style: solid; border-left-width: 2px; border-left-color: #d3d3d3; border-left-style: none;" align="center" bgcolor="#ffffff">
                                                <a href="{% capture link %}http://kytebaby.com{% endcapture %}{% if link contains shop.domain %}{% assign split_link = link | split: question_mark %}{{ split_link[0] }}?{{ default_utms }}&utm_content=site-link&utm_term=shop{% if split_link[1] != blank %}&{{ split_link[1] }}{% endif %}{% else %}{{ link }}{% endif %}" target="_blank" style="color: #212121; text-decoration: none !important; text-underline: none; word-wrap: break-word; text-align: center !important; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Arial,'Lexend Deca'; font-size: 14px; font-weight: 400; line-height: 20px; text-transform: uppercase;">
                                                  Shop
                                                </a>
                                              </th>
                                              <th class="menu_bar_item" style="width: 33%; mso-line-height-rule: exactly; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Arial,'Lexend Deca'; font-size: 14px; font-weight: 400; line-height: 20px; text-transform: uppercase; color: #212121; border-right-width: 2px; border-right-color: #d3d3d3; border-right-style: solid; border-left-width: 2px; border-left-color: #d3d3d3; border-left-style: solid;" align="center" bgcolor="#ffffff">
                                                <a href="{% capture link %}http://kytebaby.com/pages/about-us{% endcapture %}{% if link contains shop.domain %}{% assign split_link = link | split: question_mark %}{{ split_link[0] }}?{{ default_utms }}&utm_content=site-link&utm_term=about{% if split_link[1] != blank %}&{{ split_link[1] }}{% endif %}{% else %}{{ link }}{% endif %}" target="_blank" style="color: #212121; text-decoration: none !important; text-underline: none; word-wrap: break-word; text-align: center !important; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Arial,'Lexend Deca'; font-size: 14px; font-weight: 400; line-height: 20px; text-transform: uppercase;">
                                                  About
                                                </a>
                                              </th>
                                              <th class="menu_bar_item last" style="width: 33%; mso-line-height-rule: exactly; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Arial,'Lexend Deca'; font-size: 14px; font-weight: 400; line-height: 20px; text-transform: uppercase; color: #212121; border-right-width: 2px; border-right-color: #d3d3d3; border-right-style: none; border-left-width: 2px; border-left-color: #d3d3d3; border-left-style: solid;" align="center" bgcolor="#ffffff">
                                                <a href="{% capture link %}http://kytebaby.com/pages/contact-us{% endcapture %}{% if link contains shop.domain %}{% assign split_link = link | split: question_mark %}{{ split_link[0] }}?{{ default_utms }}&utm_content=site-link&utm_term=contact{% if split_link[1] != blank %}&{{ split_link[1] }}{% endif %}{% else %}{{ link }}{% endif %}" target="_blank" style="color: #212121; text-decoration: none !important; text-underline: none; word-wrap: break-word; text-align: center !important; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Arial,'Lexend Deca'; font-size: 14px; font-weight: 400; line-height: 20px; text-transform: uppercase;">
                                                  Contact
                                                </a>
                                              </th>
                                            </tr>
                                          </table>
                                        </td>
                                      </tr>
                                    </table>
                                  </td>
                                </tr>
                              </table>
                              <!-- END : SECTION : MAIN -->
                              <!-- BEGIN : SECTION : FOOTER -->
                              <table class="section_wrapper footer" data-id="footer" id="section-footer" border="0" width="100%" cellpadding="0" cellspacing="0" align="center" style="min-width: 100%;" role="presentation" bgcolor="#fbf6f3">
                                <tr>
                                  <td class="section_wrapper_th" style="mso-line-height-rule: exactly; padding-top: 20px; padding-bottom: 20px;" bgcolor="#fbf6f3">
                                    <table border="0" width="100%" cellpadding="0" cellspacing="0" align="center" style="min-width: 100%;" role="presentation">
                                      <tr>
                                        <th class="column_social" data-id="social" style="mso-line-height-rule: exactly;" bgcolor="#fbf6f3">
                                          <table border="0" width="100%" cellpadding="0" cellspacing="0" role="presentation">
                                            <!-- Store Address : BEGIN -->
                                            <tr>
                                              <th class="column_shop_social_icons " width="100%" style="mso-line-height-rule: exactly;" bgcolor="#fbf6f3">
                                                <a class="social-link" href="https://www.facebook.com/kytebaby" target="_blank" title="Facebook" style="color: #000000; text-decoration: none !important; text-underline: none; font-size: 14px;">
                                                  <img width="26" class="social-icons" alt="Facebook" src="https://www.orderlyemails.com/facebook_1.png" style="width: 26px; height: auto !important; vertical-align: middle; padding: 6px 6px 26px;">
                                                </a>
                                                <a class="social-link" href="https://instagram.com/kytebaby" target="_blank" title="Instagram" style="color: #000000; text-decoration: none !important; text-underline: none; font-size: 14px;">
                                                  <img width="26" class="social-icons" alt="Instagram" src="https://www.orderlyemails.com/instagram_1.png" style="width: 26px; height: auto !important; vertical-align: middle; padding: 6px 6px 26px;">
                                                </a>
                                                <a class="social-link" href="https://www.pinterest.com/kytebaby/" target="_blank" title="Pinterest" style="color: #000000; text-decoration: none !important; text-underline: none; font-size: 14px;">
                                                  <img width="26" class="social-icons" alt="Pinterest" src="https://www.orderlyemails.com/pinterest_1.png" style="width: 26px; height: auto !important; vertical-align: middle; padding: 6px 6px 26px;">
                                                </a>
                                              </th>
                                            </tr>
                                          </table>
                                        </th>
                                      </tr>
                                      <tr>
                                        <th data-id="store-info" style="mso-line-height-rule: exactly;" bgcolor="#fbf6f3">
                                          <table border="0" width="100%" cellpadding="0" cellspacing="0" role="presentation">
                                            <!-- Store Website : BEGIN -->
                                            <tr>
                                              <th class="column_shop_block1 " width="100%" style="mso-line-height-rule: exactly; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Arial,'Lexend Deca'; font-size: 14px; line-height: 20px; font-weight: 400; color: #000000; text-transform: none; padding-bottom: 20px;" align="center" bgcolor="#fbf6f3">
                                                <a href="{% capture link %}{{shop.url}}{% endcapture %}{% if link contains shop.domain %}{% assign split_link = link | split: question_mark %}{{ split_link[0] }}?{{ default_utms }}&utm_content=footer-website-link{% if split_link[1] != blank %}&{{ split_link[1] }}{% endif %}{% else %}{{ link }}{% endif %}" target="_blank" data-key="section_shop_block1" style="color: #000000; text-decoration: none !important; text-underline: none; font-size: 14px; font-weight: 400; text-transform: none;">kytebaby.com</a>
                                              </th>
                                            </tr>
                                            <!-- Store Website : END -->
                                            <!-- Store Address : BEGIN -->
                                            <tr>
                                              <th class="column_shop_block2 " data-key="section_shop_block2" width="100%" style="mso-line-height-rule: exactly; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Arial,'Lexend Deca'; font-size: 14px; line-height: 20px; font-weight: 400; color: #000000; text-transform: none;" align="center" bgcolor="#fbf6f3">
                                                Kyte Baby<br>
                                                3100 Eagle Parkway<br>
                                                Fort Worth, TX 76177<br>
                                                <br>
                                                Copyright &#169; {{ 'now' | date: '%Y' }}
                                              </th>
                                            </tr>
                                            <!-- Store Address : END -->
                                          </table>
                                        </th>
                                      </tr>
                                      <tr>
                                        <th height="1" border="0" style="height: 1px; line-height: 1px; font-size: 1px; mso-line-height-rule: exactly; padding: 0;" bgcolor="#fbf6f3">
                                          <img id="open-image" src="https://{{ shop.domain }}/tools/emails/open/gift-card-created/13" alt="" width="1" height="1" border="0">
                                        </th>
                                      </tr>
                                    </table>
                                  </td>
                                </tr>
                              </table>
                              <!-- END : SECTION : FOOTER -->
                            </th>
                          </tr>
                        </tbody>
                      </table>
                    </center>
                  </th>
                </tr>
              </tbody>
            </table>
            <!-- END : CONTAINER -->
          </body>
        </html>