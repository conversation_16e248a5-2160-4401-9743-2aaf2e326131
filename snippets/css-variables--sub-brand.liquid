<style>

  /* ========== Sub Brand - {{ sub_brand.title }} ========== */

  :root {

    /* ----- Brand Colors ----- */

    {% for color in sub_brand.colors.value %}
      --sub-brand-color-{{ forloop.index }}: {{ color }};
      --sub-brand-color-{{ forloop.index }}--rgb: {{ color.red }}, {{ color.green }}, {{ color.blue }};
    {% endfor %}


    /* ----- Content Colors ----- */

    {% if sub_brand.heading_color %}
      --sub-brand--heading-color: {{ sub_brand.heading_color }};
      --sub-brand--heading-color--rgb: {{ sub_brand.heading_color.value.red }}, {{ sub_brand.heading_color.value.green }}, {{ sub_brand.heading_color.value.blue }};
      --tabbed-header-color-heading: var(--sub-brand--heading-color--rgb);
    {% endif -%}

    {% if sub_brand.subheading_color %}
      --sub-brand--subheading-color: {{ sub_brand.subheading_color }};
      --sub-brand--subheading-color--rgb: {{ sub_brand.subheading_color.value.red }}, {{ sub_brand.subheading_color.value.green }}, {{ sub_brand.subheading_color.value.blue }};
    {% endif -%}

    {% if sub_brand.text_color %}
      --sub-brand--text-color: {{ sub_brand.text_color }};
      --sub-brand--text-color--rgb: {{ sub_brand.text_color.value.red }}, {{ sub_brand.text_color.value.green }}, {{ sub_brand.text_color.value.blue }};
      --tabbed-header-color-text: var(--sub-brand--text-color--rgb);
    {% endif -%}

    {% if sub_brand.button_color %}
      --sub-brand--button-color: {{ sub_brand.button_color }};
      --sub-brand--button-color--rgb: {{ sub_brand.button_color.value.red }}, {{ sub_brand.button_color.value.green }}, {{ sub_brand.button_color.value.blue }};
      --tabbed-header-color-button: var(--sub-brand--button-color--rgb);
    {% endif -%}

    {% if sub_brand.button_text_color %}
      --sub-brand--button-text-color: {{ sub_brand.button_text_color }};
      --sub-brand--button-text-color--rgb: {{ sub_brand.button_text_color.value.red }}, {{ sub_brand.button_text_color.value.green }}, {{ sub_brand.button_text_color.value.blue }};
      --tabbed-header-color-text: var(--sub-brand--button-text-color--rgb);
    {% endif %}

    {% if sub_brand.background_color %}
      --sub-brand--background-color: {{ sub_brand.background_color }};
      --sub-brand--background-color--rgb: {{ sub_brand.background_color.value.red }}, {{ sub_brand.background_color.value.green }}, {{ sub_brand.background_color.value.blue }};
      --tabbed-header-color-background-1: var(--sub-brand--background-color--rgb);
      --secondary-background: var(--sub-brand--secondary-background-color--rgb);
    {% endif %}

    {% if sub_brand.secondary_background_color %}
      --sub-brand--secondary-background-color: {{ sub_brand.secondary_background_color }};
      --sub-brand--secondary-background-color--rgb: {{ sub_brand.secondary_background_color.value.red }}, {{ sub_brand.secondary_background_color.value.green }}, {{ sub_brand.secondary_background_color.value.blue }};
      --tabbed-header-color-background-2: var(--sub-brand--secondary-background-color--rgb);
      --secondary-background: var(--sub-brand--secondary-background-color--rgb);
    {% endif %}

    --header-text-color: var(--sub-brand--heading-color--rgb);

    --primary-button-background: var(--sub-brand--button-color--rgb);
    --primary-button-text-color: var(--sub-brand--button-text-color--rgb);
    --secondary-button-background: var(--sub-brand--subheading-color--rgb);
    --secondary-button-text-color: var(--sub-brand--button-text-color--rgb);

    --root-primary-button-background: var(--sub-brand--button-color--rgb);
    --root-primary-button-text-color: var(--sub-brand--button-text-color--rgb);
    --root-secondary-button-background: var(--sub-brand--subheading-color--rgb);
    --root-secondary-button-text-color: var(--sub-brand--button-text-color--rgb);


  }

  {% comment %} 
    When adding a new sub-brand, it might be useful to add some custom styles
    to override theme styles of elements on the page for that sub-brand.
  {% endcomment %}

  {{ sub_brand.custom_css }}

</style>