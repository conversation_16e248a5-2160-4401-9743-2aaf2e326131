<!-- Added with Analyzify V2.3 - Dec 5, 2022 11:08 am -->


<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','{{ shop.metafields.gtm.tracking_id }}');</script>
<!-- End Google Tag Manager -->

<script type="text/javascript">
    window.dataLayer = window.dataLayer || [];
    window.analyzify = {
        "pageFly" : false, //Set this true if you are using PageFly in your website
        "send_unhashed_email": false, //Set this true if you would like to collect unhashed email address of logged in users.
        "g_feed_region": "US", //Change this if your Google Ads feed is in another region
        //Modify the following button attributes if you do not see any ee_addToCart datalayer event when you click add to cart button in any product detail page.
        "addtocart_btn_attributes": {
        },
        //Modify the following element attribute if you see that quantity being pushed to datalayer is wrong when you try to add the a product to the cart with more than 1 as quantity.
        "product_quantity":{
            "name": ["quantity"]
        },
        //Modify the following button attributes if you do not see any ee_removeFromCart datalayer event when you remove any item from the cart in the cart page.
        "removefromcart_btn_attributes": {
            "data-remove-item": ["cart-template"],
            "data-cart-remove": ["Remove"],
            "aria-label": ["Remove"],
            "class": ["line-item__remove-button", "cart__remove-btn", "cart__remove", "cart__removee", "cart-item__remove", "item-remove"],
            "id": [],
            "href": ["/cart/change?id=", "/cart/change?line="]
        },
        //Modify the following button attributes if you do not see ee_checkout datalayer event when you click "checkout" button in the cart page or cart drawer.
        "checkout_btn_attributes": {
            "name": ["checkout"],
            "class": ["checkout-btn", "upcart-checkout-button", "cart__submit"],
            "href": ["/checkout"]
        },
        //Modify the following button attributes if you do not see any ee_productClick datalayer event when you click to a product in collection pages.
        "collection_prod_click_attributes":{
            "href": ["/products/"]
        },
        //Modify the following button attributes if you do not see any ee_addToCart datalayer event when you click add to cart button in any collection pages.
        "collection_atc_attributes":{
        },
        //Modify the following button attributes if you do not see any ee_productClick datalayer event when you click to a product in search result pages.
        "search_prod_click_attributes":{
            "href": ["/products/"]
        },
        "version": "2.31", //Analyzify version installed to this store
        "logging": false, //Enable this to see Analyzify logs in console
        "logs": [],
        "stopAtLog": false //To use a debugger while logging
    };

    analyzify.log = function(t) {
        if (window.analyzify.logging == true && t !== null) {
            if (typeof(t) == "object") {
                window.console.log("** Analyzify Logger:");
                window.console.log(t);
            } else {
                window.console.log("** Analyzify Logger: " + t);
            }

            analyzify.logs.push(t);
            if (analyzify.stopAtLog == true) {
                debugger;
            }
        }
    };
    window.analyzify.Initialize = function() {
        window.analyzify.loadScript = function(callback) {
            callback();
        }

        window.analyzify.AppStart = function(){

            var detected_cart = {{ cart | json}};
            var detected_currency = detected_cart.currency;

            {% assign template_name = template.name %}

            var sh_info_obj = {
                event: "sh_info",
                page_type: {{ template_name | replace: "index", "homepage" | default: 'other' | capitalize | json }},
                page_currency: detected_currency,
                user: {
                    {% if customer %}
                    type: "member",
                    id: `{{ customer.id | remove: "'" | remove: '"' }}`,
                    last_order: `{{ customer.last_order.created_at | date: "%B %d, %Y %I:%M%p" }}`,
                    orders_count: `{{ customer.orders_count }}`,
                    total_spent: `{{ customer.total_spent | money_without_currency }}`,
                    email_sha256: `{{ customer.email | sha256 }}`,
                    email_sha1: `{{ customer.email | sha1 }}`,
                    {% else %}
                    type: "visitor"
                    {% endif %}
                }
            }
            {% if customer %}
            if (analyzify.send_unhashed_email == true) {
                sh_info_obj["email"] = `{{ customer.email}}`;
            }
            {% endif %}

            window.dataLayer.push(sh_info_obj);

            window.analyzify.GetClickedProductPosition = function(elementHref, sku){
                if(sku != ''){
                    var collection = {{ collection | json }};
                    {% for product in collection.products %}
                    var collectionProductsSku = `{{ product.selected_or_first_available_variant.sku | remove: "'" | remove: '"' }}`;
                    if(sku == collectionProductsSku) {return {{ forloop.index }} ;}
                    {% endfor %}
                    return 0;
                }else{
                    var elementIndex = -1
                    collectionProductsElements = document.querySelectorAll('a[href*="/products/"]');
                    collectionProductsElements.forEach(function(element,index){
                        if (element.href.includes(elementHref)) {elementIndex = index + 1};
                    });
                    return elementIndex
                }
            }

            {% capture collectionInfos %}
            var collection = {{ collection | json }};
            var collectionAllProducts = {{ collection.products | json }};
            var collectionTitle = `{{ collection.title | remove: "'" | remove: '"' }}`;
            var collectionId = `{{ collection.id }}`;
            var collectionProductsBrand = [];
            var collectionProductsType = [];
            var collectionProductsSku = [];
            var collectionProductsName = [];
            var collectionProductsId = [];
            var collectionProductsPrice = [];
            var collectionProductsPosition = []; // we need to talk about, this data can be taken from DOM only (filter ON/OFF)
            var collectionGproductId = [];
            var collectionVariantId = [];
            var collectionVariantTitle = [];
            {% endcapture %}

            window.analyzify.CollectionPageHandle = function(){
                {{collectionInfos}}
                {% for product in collection.products %}
                collectionProductsBrand.push(`{{ product.vendor | remove: "'" | remove: '"' }}`)
                collectionProductsType.push(`{{ product.type | remove: "'" | remove: '"' }}`)
                collectionProductsSku.push(`{{ product.selected_or_first_available_variant.sku | remove: "'" | remove: '"' }}`)
                collectionProductsName.push(`{{ product.title | remove: "'" | remove: '"' }}`)
                collectionProductsId.push(`{{ product.id }}`)
                collectionProductsPrice.push(`{{ product.variants.first.price | times: 0.01 }}`)
                collectionProductsPosition.push({{ forloop.index }})
                collectionGproductId.push(`shopify_`+ analyzify.g_feed_region +`_`+`{{ product.id }}`+`_`+`{{ product.selected_or_first_available_variant.id | default: product.variants[0].id }}`)
                collectionVariantId.push(`{{ product.selected_or_first_available_variant.id | default: product.variants[0].id }}`)
                collectionVariantTitle.push(`{{ product.selected_or_first_available_variant.title | default: product.variants[0].title }}`)
                {% endfor %}

                window.dataLayer.push({
                    event: 'ee_productImpression',
                    category_name: collectionTitle,
                    category_id: collectionId,
                    product_brand: collectionProductsBrand,
                    product_type: collectionProductsType,
                    product_sku: collectionProductsSku,
                    product_name: collectionProductsName,
                    product_id: collectionProductsId,
                    product_price: collectionProductsPrice,
                    currency: detected_currency,
                    product_position: collectionProductsPosition,
                    g_product_id: collectionGproductId,
                    variant_id: collectionVariantId,
                    variant_title: collectionVariantTitle
                });
            }

            {% capture searchInfos %}
            var searchTerm = `{{ search.terms }}`;
            var searchResults = parseInt(`{{ search.results_count }}`);
            var searchResultsJson = {{ search.results | json }};
            var searchProductsBrand = [];
            var searchProductsType = [];
            var searchProductsSku = [];
            var searchProductsNames = [];
            var searchProductsIds = [];
            var searchProductsPrices = [];
            var searchProductsPosition = [];
            var searchGproductId = [];
            var searchVariantId = [];
            var searchVariantTitle = [];
            {% endcapture %}

            window.analyzify.SearchPageHandle = function(){
                {{searchInfos}}

                {% for product in search.results %}
                searchProductsBrand.push(`{{ product.vendor | remove: "'" | remove: '"' }}`);
                searchProductsType.push(`{{ product.type | remove: "'" | remove: '"' }}`)
                searchProductsSku.push(`{{ product.selected_or_first_available_variant.sku | remove: "'" | remove: '"' }}`)
                searchProductsNames.push(`{{ product.title | remove: "'" | remove: '"' }}`);
                searchProductsIds.push(`{{ product.id }}`);
                searchProductsPrices.push(`{{ product.variants.first.price | times: 0.01 }}`);
                searchProductsPosition.push({{ forloop.index }})
                searchGproductId.push(`shopify_`+ analyzify.g_feed_region +`_`+`{{ product.id }}`+`_`+`{{ product.selected_or_first_available_variant.id | default: product.variants[0].id }}`)
                searchVariantId.push(`{{ product.selected_or_first_available_variant.id | default: product.variants[0].id }}`)
                searchVariantTitle.push(`{{ product.selected_or_first_available_variant.title | default: product.variants[0].title }}`)
                {% endfor %}

                window.dataLayer.push({
                    event: 'searchListInfo',
                    page_type: 'search',
                    search_term: searchTerm,
                    search_results: searchResults,
                    product_brand: searchProductsBrand,
                    product_type: searchProductsType,
                    product_sku: searchProductsSku,
                    product_name: searchProductsNames,
                    product_id: searchProductsIds,
                    product_price: searchProductsPrices,
                    currency: detected_currency,
                    product_position: searchProductsPosition,
                    product_list_id: 'search',
                    product_list_name: 'Search',
                    g_product_id: searchGproductId,
                    variant_id: searchVariantId,
                    variant_title: searchVariantTitle
                });
            }

            var productJson = {{ product | json}};

            {% capture productInfos %}
            var productName = `{{ product.title | remove: "'" | remove: '"' }}`;
            var productId = `{{ product.id }}`;
            var productPrice = `{{ product.variants.first.price | times: 0.01 }}`;
            var productBrand = `{{ product.vendor | remove: "'" | remove: '"' }}`;
            var productType = `{{ product.type | remove: "'" | remove: '"' }}`;
            var productSku = `{{ product.selected_or_first_available_variant.sku | remove: "'" | remove: '"' }}`;
            var collectionTitle = `{{ product.collections.last.title | remove: "'" | remove: '"' }}`;
            var collectionId = `{{ product.collections.last.id | remove: "'" | remove: '"' }}`;
            {% endcapture %}

            window.analyzify.ProductPageHandle = function(){
                {{ productInfos }}

                window.dataLayer.push({
                    event: 'ee_productDetail',
                    product_id: productId,
                    product_name: productName,
                    product_type: productType,
                    product_brand: productBrand,
                    product_sku: productSku,
                    product_price: productPrice,
                    currency: detected_currency,
                    category_id: collectionId,
                    category_name: collectionTitle,
                    g_product_id: `shopify_`+ analyzify.g_feed_region +`_`+productId+`_`+`{{ product.selected_variant.id | default: product.variants[0].id }}`,
                    variant_id: `{{ product.selected_variant.id | default: product.variants[0].id }}`,
                    variant_title: `{{ product.selected_or_first_available_variant.title | remove: "'" | remove: '"' }}`
                });
            };

            var cartItemsJson = {{ cart | json }};

            window.analyzify.cartPageHandle = function(){
                var cartTotalValue = `{{ cart.total_price | times: 0.01 }}`;
                var cartTotalQuantity = 0;
                var cartItemsName = [];
                var cartItemsCategory = [];
                var cartItemsBrand = [];
                var cartItemsType = [];
                var cartItemsSku = [];
                var cartItemsId = [];
                var cartItemsVariantId = [];
                var cartItemsVariantTitle = [];
                var cartItemsPrice = [];
                var cartItemsQuantity = [];
                var cartItemsCategoryIds = [];

                fetch('/cart.js')
                    .then(response => response.json())
                    .then(cartItemsJson => {
                        for (var i = 0; i < cartItemsJson.items.length; i++) {
                            cartItemsName.push(cartItemsJson.items[i].product_title);
                            cartItemsBrand.push(cartItemsJson.items[i].vendor);
                            cartItemsType.push(cartItemsJson.items[i].product_type);
                            cartItemsSku.push(cartItemsJson.items[i].sku);
                            cartItemsId.push(cartItemsJson.items[i].product_id);
                            cartItemsVariantId.push(cartItemsJson.items[i].variant_id);
                            cartItemsVariantTitle.push(cartItemsJson.items[i].variant_title);
                            cartItemsPrice.push((cartItemsJson.items[i].price / 100).toFixed(2).toString());
                            cartItemsQuantity.push(cartItemsJson.items[i].quantity);
                            cartTotalQuantity += cartItemsJson.items[i].quantity;
                        }
                        window.dataLayer.push({
                            event: 'ee_cartView',
                            page_type: 'cart',
                            product_id: cartItemsId,
                            product_name: cartItemsName,
                            product_type: cartItemsType,
                            product_brand: cartItemsBrand,
                            product_sku: cartItemsSku,
                            product_list_id: 'cart',
                            product_list_name: 'Cart',
                            variant_id: cartItemsVariantId,
                            variant_title: cartItemsVariantTitle,
                            product_price: cartItemsPrice,
                            currency: detected_currency,
                            quantity: cartItemsQuantity,
                            totalValue: cartTotalValue,
                            totalQuantity: cartTotalQuantity
                        });
                    });
            }

            var found_element = [];
            var found_atc_element_form = [];

            window.findElemInPath = function(pth_arr, attr_obj) {
                var i;
                var btn_found = null;

                for (i = 0; i < pth_arr.length; i++) {
                    for (var k in attr_obj) {
                        if (attr_obj.hasOwnProperty(k)) {
                            var attribute_name = k;
                            var attribute_values = attr_obj[k];

                            if (pth_arr[i].hasAttribute !== undefined) {
                                if (pth_arr[i].hasAttribute(attribute_name) == true) {
                                    attribute_values.forEach(function(selected_value) {
                                        if (pth_arr[i].getAttribute(attribute_name).indexOf(selected_value) > -1) {
                                            analyzify.log(selected_value + " found in " + attribute_name + " attribute list.");
                                            btn_found = pth_arr[i];
                                            found_element.push(pth_arr[i]);
                                            found_atc_element_form.push(pth_arr[i].closest("form[action='/cart/add']"));
                                        }
                                    });
                                }
                            }
                        }
                    }
                }
                return btn_found;
            }

            document.addEventListener("click", (event) => {
                found_element = [];
                found_atc_element_form = [];
                var checkout_elem = window.findElemInPath(event.path, analyzify.checkout_btn_attributes);
                var rfc_elem = window.findElemInPath(event.path, analyzify.removefromcart_btn_attributes);
                {% case template_name %}
                {%- when 'product' -%}
                var atc_elem = window.findElemInPath(event.path, analyzify.addtocart_btn_attributes);
                {% when 'collection' %}
                var col_prod_click_elem = window.findElemInPath(event.path, analyzify.collection_prod_click_attributes);
                var coll_atc_elem = window.findElemInPath(event.path, analyzify.collection_atc_attributes);
                {% when 'search' %}
                var search_prod_click_elem = window.findElemInPath(event.path, analyzify.search_prod_click_attributes);
                {% endcase %}
                if(event.target.classList.contains('product-form__add-button') || event.target.closest('.product-form__add-button') || event.target.classList.contains('ee_quick_add_to_cart') || event.target.closest('.ee_quick_add_to_cart')){
                  analyzify.quickViewAtcEventFunc(event);
                }
                if (checkout_elem !== null) {
                    analyzify.checkoutEventFunc();
                }else if(rfc_elem !== null) {
                    analyzify.rfcEventFunc();
                }
                        {%- case template_name -%}
                        {%- when 'product' -%}
                else if(atc_elem !== null) {
                    analyzify.atcEventFunc();
                }
                        {%- when 'collection' -%}
                else if(col_prod_click_elem !== null){
                    analyzify.colProdClickFunc();
                }else if(coll_atc_elem !== null) {
                    analyzify.collAtcEventFunc();
                }
                        {%- when 'search' -%}
                else if(search_prod_click_elem !== null){
                    analyzify.searchProdClickFunc();
                }
                        {% endcase %}
                else{
                    analyzify.log("The clicked button/link was not a addtocart, removefromcart or checkout button.");
                    analyzify.log(event);
                }
            });

            window.analyzify.checkoutEventFunc = function(){
                var cartTotalValue = `{{ cart.total_price | times: 0.01 }}`;
                var cartTotalQuantity = 0;
                var cartItemsName = [];
                var cartItemsCategory = [];
                var cartItemsBrand = [];
                var cartItemsType = [];
                var cartItemsSku = [];
                var cartItemsId = [];
                var cartItemsVariantId = [];
                var cartItemsVariantTitle = [];
                var cartItemsPrice = [];
                var cartItemsQuantity = [];
                var cartItemsCategoryIds = [];

                fetch('/cart.js')
                    .then(response => response.json())
                    .then(cartItemsJson => {
                        for (var i = 0; i < cartItemsJson.items.length; i++) {
                            cartItemsName.push(cartItemsJson.items[i].product_title);
                            cartItemsBrand.push(cartItemsJson.items[i].vendor);
                            cartItemsType.push(cartItemsJson.items[i].product_type);
                            cartItemsSku.push(cartItemsJson.items[i].sku);
                            cartItemsId.push(cartItemsJson.items[i].product_id);
                            cartItemsVariantId.push(cartItemsJson.items[i].variant_id);
                            cartItemsVariantTitle.push(cartItemsJson.items[i].variant_title);
                            cartItemsPrice.push((cartItemsJson.items[i].price / 100).toFixed(2).toString());
                            cartItemsQuantity.push(cartItemsJson.items[i].quantity);
                            cartTotalQuantity += cartItemsJson.items[i].quantity;
                        }
                        window.dataLayer.push({
                            event: 'ee_checkout',
                            page_type: 'cart',
                            product_id: cartItemsId,
                            product_name: cartItemsName,
                            product_brand: cartItemsBrand,
                            product_type: cartItemsType,
                            product_sku: cartItemsSku,
                            variant_id: cartItemsVariantId,
                            variant_title: cartItemsVariantTitle,
                            product_price: cartItemsPrice,
                            currency: detected_currency,
                            quantity: cartItemsQuantity,
                            totalValue: cartTotalValue,
                            totalQuantity: cartTotalQuantity
                        });
                    });
            }

            window.analyzify.rfcEventFunc = function(){
                var removedItem = [];
                var possibleIDs = [];
                var formElement = found_element[0];
                if(formElement){
                    if(formElement.hasAttribute("href")) {
                      rfc_variant_id = formElement.getAttribute("href").split("?id=")[1].split(":")[0]
                      fetch('/cart.js').then((response) => response.json())
                        .then((data) => {
                            data['items'].forEach(function (item, index) {
                              if (item['id'] == rfc_variant_id){
                                window.dataLayer.push({
                                    event:'ee_removeFromCart',
                                    product_id : item['product_id'],
                                    product_name: item['product_title'],
                                    variant_id : item['id'],
                                    variant_title: item['variant_title'],
                                    product_price: (item['price'] / 100).toFixed(2).toString(),
                                    currency: detected_currency,
                                    product_brand: item['vendor'],
                                    quantity: item['quantity']
                                });
                              }
                            });
                        });
                    } else {
                        const attrValues = formElement
                            .getAttributeNames()
                            .map(name => formElement.getAttribute(name));

                        attrValues.forEach((formElement) => {
                            if(formElement.match(/([0-9]+)/g)){
                                possibleIDs.push(formElement.match(/([0-9]+)/g));
                            }
                        });

                        possibleIDs.forEach((possibleID) => {
                            possibleID.forEach((id) => {
                                cartItemsJson.items.filter(function(product) {
                                    if (product.variant_id == Number(id)){
                                        removedItem.push(product);
                                    }
                                });
                            })
                        })
                    }

                    if(removedItem[0]) {
                        window.dataLayer.push({
                            event:'ee_removeFromCart',
                            product_id : removedItem[0].product_id,
                            product_name: removedItem[0].product_title,
                            variant_id : removedItem[0].id,
                            variant_title: removedItem[0].variant_title,
                            product_price: (removedItem[0].price / 100).toFixed(2).toString(),
                            currency: detected_currency,
                            product_brand: removedItem[0].vendor,
                            quantity: removedItem[0].quantity
                        });
                        analyzify.log("Product ee_removeFromCart==>", window.dataLayer);
                    } else{
                        analyzify.log("Removed element not found");
                    }
                }
            }


            {% case template_name %}
            {% when 'product' %}
            window.onload = function(){
                if( analyzify.pageFly == true){
                    var element = document.querySelector('[data-pf-type="ProductATC"]');
                    element.setAttribute("onclick", "analyzify.atcEventFunc()");
                }
            }
            {% endcase %}

            window.analyzify.atcEventFunc = function(){

                {{ productInfos }}

                var formElement = found_atc_element_form[0];
                if( analyzify.pageFly == false){
                    if(formElement){
                        if(Array.from(formElement.elements).find(item => item.name == 'id')){
                            var formVariantInput = Array.from(formElement.elements).find(item => item.name == 'id').value;
                        }
                    }
                }

                var initial_url = window.location.href;

                if (formVariantInput){
                    var variantInput = formVariantInput;
                } else if(initial_url.includes("variant=")){
                    var variantInput = initial_url.split( 'variant=' )[1];
                } else{
                    var variantInput = '{{ product.selected_or_first_available_variant.id }}';
                }

                for (let i = 0; i < productJson.variants.length; i++) {
                    if(productJson.variants[i].id == variantInput){
                        var productPrice = productJson.variants[i].price;
                        var variantSku = productJson.variants[i].sku;
                        var variantName = productJson.variants[i].public_title;
                    }
                }

                var found_qty = [];

                window.findQty = function(attr_obj) {
                    found_qty = [];
                    for (var k in attr_obj) {
                        if (attr_obj.hasOwnProperty(k)) {
                            var attribute_name = k;
                            var attribute_values = attr_obj[k];
                            var qtyEl = document.querySelector('['+attribute_name+'="'+attribute_values+'"]');
                            if(qtyEl && qtyEl.value){
                                found_qty.push(qtyEl.value);
                            }
                        }
                    }
                }

                window.findQty(analyzify.product_quantity);

                if (found_qty.length > 0) {
                    var prodQty = found_qty[0];
                } else{
                    var prodQty = 1;
                }

                window.dataLayer.push({
                    event: 'ee_addToCart',
                    product_name: productName,
                    product_id: productId,
                    product_price: (productPrice / 100).toFixed(2).toString(),
                    currency: detected_currency,
                    product_brand: productBrand,
                    product_type: productType,
                    category_id: collectionId,
                    category_title: collectionTitle,
                    quantity: prodQty,
                    variant_id: variantInput,
                    variant_title: variantName,
                    product_sku : variantSku,
                    g_product_id: `shopify_`+ analyzify.g_feed_region +`_`+productId+`_`+variantInput
                });
                analyzify.log("Product ee_addToCart==>");
                analyzify.log(window.dataLayer);
            }

            window.analyzify.collAtcEventFunc = function(){
                {{collectionInfos}}
                var formElement = found_atc_element_form[0];
                var possibleIDs = [];
                var addedProduct = [];

                if(formElement){
                    if(formElement.querySelector('.pid')){
                        var productId = formElement.querySelector('.pid').value;

                        addedProduct = collectionAllProducts.filter(function(product) {
                            return product.id == Number(productId);
                        });
                    } else{
                        const attrValues = formElement
                            .getAttributeNames()
                            .map(name => formElement.getAttribute(name));

                        attrValues.forEach((formElement) => {
                            if(formElement.match(/([0-9]+)/g)){
                                possibleIDs.push(formElement.match(/([0-9]+)/g));
                            }
                        });

                        possibleIDs.forEach((possibleID) => {
                            possibleID.forEach((id) => {
                                collectionAllProducts.filter(function(product) {
                                    if (product.id == Number(id)){
                                        addedProduct.push(product);
                                    }
                                });
                            })
                        })
                    }

                    if (addedProduct.length == 0 ) return analyzify.log("Parent form element found but product id did not matched");
                    window.dataLayer.push({
                        event: 'ee_addToCart',
                        product_name: addedProduct[0].title,
                        product_id : addedProduct[0].id.toString(),
                        product_price: (addedProduct[0].price / 100).toFixed(2).toString(),
                        product_brand: addedProduct[0].vendor,
                        currency: detected_currency,
                        product_type: addedProduct[0].type,
                        category_id: collectionId,
                        category_title: collectionTitle,
                        variant_id: addedProduct[0].variants[0].id,
                        variant_title: addedProduct[0].variants[0].title,
                        product_sku: addedProduct[0].variants[0].sku
                    });
                    analyzify.log("Product ee_addToCart==>");
                    analyzify.log(window.dataLayer);
                } else {
                    analyzify.log("Parent form element not found for quick view atc");
                }
            }

            window.analyzify.colProdClickFunc = function(){
                {{ collectionInfos }}

                if(found_element[0].hasAttribute("href")){
                    var href = found_element[0].getAttribute("href");
                    if(href.includes("/products/")){
                        var handle = href.split('/products/')[1];
                        var clickedProduct = collectionAllProducts.filter(function(product) {
                            return product.handle === handle;
                        });

                        if (clickedProduct.length == 0 ) return analyzify.log("Clicked product does not found in collection product list");
                        window.dataLayer.push({
                            event: 'ee_productClick',
                            product_id : clickedProduct[0].id.toString(),
                            product_name: clickedProduct[0].title,
                            product_type: clickedProduct[0].type,
                            product_sku: clickedProduct[0].variants[0].sku,
                            product_brand: clickedProduct[0].vendor,
                            product_price: (clickedProduct[0].price / 100).toFixed(2).toString(),
                            category_id: collectionId,
                            category_name: collectionTitle,
                            currency: detected_currency,
                            product_position: analyzify.GetClickedProductPosition(href, clickedProduct[0].variants[0].sku),
                            variant_id: clickedProduct[0].variants[0].id,
                            variant_title: clickedProduct[0].variants[0].title
                        });
                    } else {
                        analyzify.log("Found element's href does not include an product handle.")
                    }
                } else {
                    analyzify.log("Found element does not have an href attribute.")
                }
            }

            window.analyzify.searchProdClickFunc = function(){
                {{ searchInfos }}

                if(found_element[0].hasAttribute("href")){
                    var href = found_element[0].getAttribute("href");
                    if(href.includes("/products/")){
                        var handle = href.split('/products/')[1];
                        var clickedProduct = searchResultsJson.filter(function(product) {
                            return product.handle === handle;
                        });

                        if (clickedProduct.length == 0 ) return analyzify.log("Clicked product does not found in search product list");
                        window.dataLayer.push({
                            event: 'ee_productClick',
                            product_id : clickedProduct[0].id,
                            product_name: clickedProduct[0].title,
                            product_type: clickedProduct[0].type,
                            product_sku: clickedProduct[0].variants[0].sku,
                            product_price: (clickedProduct[0].price / 100).toFixed(2).toString(),
                            currency: detected_currency,
                            product_brand: clickedProduct[0].vendor,
                            product_position: analyzify.GetClickedProductPosition(href, ""),
                            variant_id: clickedProduct[0].variants[0].id,
                            variant_title: clickedProduct[0].variants[0].title
                        });
                    } else {
                        analyzify.log("Found element's href does not include an product handle.")
                    }
                } else {
                    analyzify.log("Found element does not have an href attribute.")
                }
            }
            window.analyzify.quickViewAtcEventFunc = function(event){
              var formElement = event.target.closest('form');
              if(formElement !== null){
              	var ptitle = formElement.querySelector('.ptitle')
              	if(ptitle !== null){
                    if (formElement.querySelector('input[name="quantity"]')){
                      quantity = formElement.querySelector('input[name="quantity"]').value
                    }else{
                      quantity = 1
                    }
              		window.dataLayer.push({
                    event: 'ee_addToCart',
                    product_name: formElement.querySelector('.ptitle').value,
                    product_id : formElement.querySelector('.pid').value,
                    product_price: formElement.querySelector('.pprice').value,
                    product_brand: formElement.querySelector('.pbrand').value,
                    currency: detected_currency,
                    product_type: formElement.querySelector('.ptype').value,
                    category_id: formElement.querySelector('.pcategory_id').value,
                    category_title: formElement.querySelector('.pcategory_name').value,
                    variant_id: formElement.querySelector('*[name="id"]').value,
                    variant_title: formElement.querySelector('.pvtitle').value,
                    product_sku: formElement.querySelector('.psku').value,
                    quantity: quantity
                  });
              	}
              }
            }

            {% case template_name %}
            {% when 'collection' %}
            analyzify.CollectionPageHandle()
            {% when 'search' %}
            analyzify.SearchPageHandle()
            {% when 'product' %}
            analyzify.ProductPageHandle()
            {% when 'cart' %}
            analyzify.cartPageHandle()
            {% endcase %}

        }
    }

    analyzify.Initialize();
    analyzify.loadScript(function() {
        analyzify.AppStart();
    });
</script>