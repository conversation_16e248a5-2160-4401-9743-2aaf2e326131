{%- assign color_label_list = 'general.label.color' | t | replace: ', ', ',' | downcase | split: ',' -%}
{%- assign color_white_label = 'general.label.white' | t | downcase -%}
{%- assign size_label_list = 'general.label.size' | t | replace: ', ', ',' | downcase | split: ',' -%}

{%- assign variant = product.selected_or_first_available_variant -%}

{%- capture pre_order_one_tag -%}
  {%- if settings.pre_order_one_tag != blank -%}
    {{- settings.pre_order_one_tag -}}
  {%- endif -%}
{%- endcapture -%}

{%- capture pre_order_two_tag -%}
  {%- if settings.pre_order_two_tag != blank -%}
    {{- settings.pre_order_two_tag -}}
  {%- endif -%}
{%- endcapture -%}

{%- assign product_is_preorder = false -%}
{% if product.tags contains pre_order_one_tag or product.tags contains pre_order_two_tag %}
  {%- assign product_is_preorder = true -%}
{% endif %}

{%- assign unavailable_for_returns = false -%}
{% if product.tags contains settings.loop_returns_unavailable_tag and settings.loop_returns_unavailable_message %}
  {%- assign unavailable_for_returns = true -%}
{% endif %}

{%- assign cart_contains_preorder = false -%}
{%- for item in cart.items -%}
  {% if item.product.tags contains pre_order_one_tag or product.tags contains pre_order_two_tag %}
    {%- assign cart_contains_preorder = true -%}
    {%- break -%}
  {% endif %}
{%- endfor -%}

{%- assign cart_contains_non_preorder = false -%}
{%- for item in cart.items -%}
  {% if item.product.tags contains pre_order_one_tag or product.tags contains pre_order_two_tag %}
    {% else %}
      {%- assign cart_contains_non_preorder = true -%}
      {%- break -%}
  {% endif %}
{%- endfor -%}


{% comment %}
  Product Limits
{% endcomment %}

{%- liquid

  assign product_limit = ""
  assign locale_limit = ""
  
  for tag in product.tags
    if tag contains '_per_customer'
      assign split_tag = tag | split: '_'
      assign product_limit_word = split_tag[0]
      case product_limit_word
        when 'one'
          assign product_limit = "1"
        when 'two'
          assign product_limit = "2"
        when 'three'
          assign product_limit = "3"
        when 'four'
          assign product_limit = "4"
        when 'five'
          assign product_limit = "5"
        when 'six'
          assign product_limit = "6"
        when 'seven'
          assign product_limit = "7"
        when 'eight'
          assign product_limit = "8"
        when 'nine'
          assign product_limit = "9"
        when 'ten'
          assign product_limit = "10"
        else
          assign product_limit = ""
      endcase
    endif
    if tag == "CA-only"
      assign locale_limit = "Only available in Canada"
    endif
  endfor

-%}

{%- assign max_allowed_quantity = '' -%}
{%- if product_limit != blank -%}
  {%- assign max_allowed_quantity = product_limit | plus: 0 -%}
{%- endif -%}

{% comment %} If the product has a tier metafield tag set, find this tag and store it to show/hide the product form later. {% endcomment %}

{% assign customer_has_tier_tag = false %}
{% assign product_tags_array_string = '' %}

{% comment %}
  This first forloop puts together an array of tags that the product has.
  If the customer is tagged with the same tag, the banner is shown.
{% endcomment %}

{%- for i in (0..7) -%}

  {% capture tier_tag %}tier_{{ i }}_tag{% endcapture %}

  {% if product.metafields.custom.exclusive_access.value contains settings[tier_tag] %}
    
    {%- assign product_has_tier_tag = true -%}

    {% if product_tags_array_string == '' %}
      {%- assign product_tags_array_string = product_tags_array_string | append: settings[tier_tag] -%}
    {% else %}
      {%- assign product_tags_array_string = product_tags_array_string | append: "," | append: settings[tier_tag] -%}
    {% endif %}

  {% endif %}

{%- endfor -%}

{% comment %}
  This second forloop captures the message to be shown in the banner.
  The FIRST MATCHING TAG's message is always shown.
{% endcomment %}

{%- for i in (0..7) -%}
  
  {% capture tier_icon %}tier_{{ i }}_icon{% endcapture %}
  {% capture tier_simple_icon %}tier_{{ i }}_simple_icon{% endcapture %}
  {% capture tier_tag %}tier_{{ i }}_tag{% endcapture %}
  {% capture tier_message_heading %}tier_{{ i }}_message_heading{% endcapture %}
  {% capture tier_message_text %}tier_{{ i }}_message_text{% endcapture %}
  {% capture tier_cta_text %}tier_{{ i }}_cta_text{% endcapture %}
  {% capture tier_cta_link %}tier_{{ i }}_cta_link{% endcapture %}
  {% capture tier_atc_disabled_label %}tier_{{ i }}_atc_disabled_label{% endcapture %}
  
  {% capture tier_welcome_message_enable %}tier_{{ i }}_welcome_message_enable{% endcapture %}
  {% capture tier_welcome_message_title %}tier_{{ i }}_welcome_message_title{% endcapture %}
  {% capture tier_welcome_message_text %}tier_{{ i }}_welcome_message_text{% endcapture %}
  {% capture tier_welcome_message_cta_text %}tier_{{ i }}_welcome_message_cta_text{% endcapture %}
  {% capture tier_welcome_message_cta_link %}tier_{{ i }}_welcome_message_cta_link{% endcapture %}

  {% if product.metafields.custom.exclusive_access.value contains settings[tier_tag] %}
    {%- break -%}
  {% endif %}

{%- endfor -%}

{%- assign product_tags_array = product_tags_array_string | split: "," -%}

{%- for tag in product_tags_array -%}

  {%- if customer.tags contains tag -%}
    {%- assign customer_has_tier_tag = true -%}
    {%- break -%}
  {%- else -%}
    {%- assign customer_has_tier_tag = false -%}
  {%- endif -%}

{%- endfor -%}

{%- if product_has_tier_tag == true -%}

{%- else -%}

  {% assign tier_tag = blank %}
  {% assign tier_message_heading = blank %}
  {% assign tier_icon = blank %}
  {% assign tier_simple_icon = blank %}
  {% assign tier_message_text = blank %}
  {% assign tier_cta_text = blank %}
  {% assign tier_cta_link = blank %}
  {% assign tier_atc_disabled_label = blank %}

{%- endif -%}

{% capture price_display %}

  {%- if variant.price < variant.compare_at_price -%}

    <span class="sale-price-container">
      <span class="visually-hidden">{{ 'product.general.sale_price' | t }}</span>
      {{- variant.price | money_without_trailing_zeros -}}
    </span>

    <span class="regular-price-container">
      <span class="visually-hidden">{{ 'product.general.regular_price' | t }}</span>
      {{- variant.compare_at_price | money_without_trailing_zeros -}}
    </span>

    {% comment %}

    <span class="price--highlight">
      <span class="visually-hidden">{{ 'product.general.sale_price' | t }}</span>

      {%- if settings.currency_code_enabled -%}
        {{- variant.price | money_with_currency -}}
      {%- else -%}
        {%- if settings.currency_no_trailing_zeroes == false -%}
          {{- variant.price | money -}}
        {%- else -%}
          {{- variant.price | money_without_trailing_zeros -}}
        {%- endif -%}
      {%- endif -%}
    </span>

    <span class="price--compare">
      <span class="visually-hidden">{{ 'product.general.regular_price' | t }}</span>
      {%- if settings.currency_code_enabled -%}
        {{- variant.compare_at_price | money_with_currency -}}
      {%- else -%}
        {%- if settings.currency_no_trailing_zeroes == false -%}
          {{- variant.compare_at_price | money -}}
        {%- else -%}
          {{- variant.compare_at_price | money_without_trailing_zeros -}}
        {%- endif -%}
      {%- endif -%}
    </span>

  {% endcomment %}

  {%- else -%}

    <span class="">
      <span class="visually-hidden">{{ 'product.general.sale_price' | t }}</span>

      {%- if settings.currency_code_enabled -%}
        {{- variant.price | money_with_currency -}}
      {%- else -%}
        {%- if settings.currency_no_trailing_zeroes == false -%}
          {{- variant.price | money -}}
        {%- else -%}
          {{- variant.price | money_without_trailing_zeros -}}
        {%- endif -%}
      {%- endif -%}
    </span>

  {%- endif -%}

{% endcapture %}


{% comment %} Wishlist {% endcomment %}

{%- assign hide_wishlist = false -%}
{%- if product.metafields.wishlist.disable == true -%}
  {%- assign hide_wishlist = true -%}
{%- endif -%}

{%- assign hide_registry = false -%}
{%- if product.metafields.registry.disable == true -%}
  {%- assign hide_registry = true -%}
{%- endif -%}


{%- assign is_bundle = false -%}

{%- if product.metafields.bundle.bundle_product != blank -%}
  {%- assign bundle_components = product.metafields.bundle.bundle_product.value.metafields.bundle.components.value -%}
  {%- assign bundle_product = product.metafields.bundle.bundle_product.value -%}
{%- elsif product.metafields.bundle.components != blank -%}
  {%- assign bundle_components = product.metafields.bundle.components.value -%}
  {%- assign bundle_first_component = bundle_components | first -%}
  {%- assign bundle_product = product -%}
{%- endif -%}

{%- if product == bundle_product -%}
  {%- assign is_bundle = true -%}
{%- endif -%}


<div class="product-form {% if unavailable_for_returns %}product-form--has-unavailable-for-returns-tag{% endif %}">
  {%- for block in section.blocks -%}
    {%- case block.type -%}
      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      APP BLOCK TYPE
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      
      {%- when '@app' -%}
        {%- render block -%}

      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      DIVIDER
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      {%- when 'divider' -%}

        <div class="product-form__divider">
          <hr class="hr--nomargin">
        </div>

      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      ACCORDION
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}

      {%- when 'accordion' -%}

        {%- capture icon_content -%}
          {%- if block.settings.icon_svg != blank -%}
            {{ block.settings.icon_svg }}
          {%- elsif block.settings.icon_image != blank -%}
            {{ block.settings.icon_image | image_url: width: 200 | image_tag: sizes: '200px', widths: '200,300,400', width: '50px', height: '50px', alt: icon.alt }}
          {%- endif -%}
        {%- endcapture -%}

        <div class="product-form__accordions" {{ block.shopify_attributes }}>

          <div class="product-form-accordions">

            <div class="product-form-accordion">
    
              <button type="button" is="toggle-button" class="product-form-accordion__toggle collapsible-toggle" aria-controls="{{ section.id }}-product-form-accordion--{{ block.id }}" aria-expanded="false">
    
                {%- if icon_content != blank -%}
                  <div class="product-form-accordion__icon">
                    {{ icon_content }}
                  </div>
                {%- endif -%}
                
                <span class="product-form-accordion__title {{ block.settings.title_style }}">{{ block.settings.title }}</span>
                
                {%- render 'icon' with 'chevron' -%}
    
              </button>
    
              <collapsible-content id="{{ section.id }}-product-form-accordion--{{ block.id }}" class="collapsible product-form-accordion__content {{ block.settings.content_style }}">
                <div class="product-form-accordion__content-inner">
                  {{ block.settings.content }}
                </div>
              </collapsible-content>
    
            </div>

          </div>

        </div>

      {%- when 'benefit-accordions' -%}

        {%- assign benefits = product.metafields.custom.product_features_benefits.value -%}

        {%- capture benefit_accordions -%}

          {%- for benefit in benefits -%}

            {% comment %} {%- assign icon_svg = value -%} {% endcomment %}
            {%- assign icon_image = benefit.icon -%}
            {%- assign benefit_title = benefit.title -%}
            {%- assign benefit_description = benefit.description | metafield_tag -%}

            {%- if benefit_title == blank or benefit_description == blank -%}
              {%- continue -%}
            {%- endif -%}

            {%- capture icon_content -%}
              {%- if icon_svg != blank -%}
                {{ icon_svg }}
              {%- elsif icon_image != blank -%}
                {{ icon_image | image_url: width: 200 | image_tag: sizes: '200px', widths: '200,300,400', width: '50px', height: '50px', alt: icon_image.alt }}
              {%- endif -%}
            {%- endcapture -%}

            <div class="product-form-accordion">

              <button type="button" is="toggle-button" class="product-form-accordion__toggle collapsible-toggle" aria-controls="{{ section.id }}-product-form-accordion--{{ block.id }}--{{ forloop.index }}" aria-expanded="false">

                {%- if icon_content != blank -%}
                  <div class="product-form-accordion__icon">
                    {{ icon_content }}
                  </div>
                {%- endif -%}

                <span class="product-form-accordion__title {{ block.settings.title_style }}">{{ benefit_title }}</span>

                {%- render 'icon' with 'chevron' -%}

              </button>

              <collapsible-content id="{{ section.id }}-product-form-accordion--{{ block.id }}--{{ forloop.index }}" class="collapsible product-form-accordion__content {{ block.settings.content_style }}">
                <div class="product-form-accordion__content-inner">
                  {{ benefit_description }}
                </div>
              </collapsible-content>

            </div>

          {%- endfor -%}

        {%- endcapture -%}

        {%- if benefit_accordions != blank -%}
          
          <div class="product-form__accordions" {{ block.shopify_attributes }}>
            <div class="product-form-accordions">
              {{ benefit_accordions }}
            </div>
          </div>

        {%- endif -%}


      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      Loyalty Lion - Loyalty Points per PRoduct
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      {%- when 'loyalty-earnings' -%}

        {%- assign points_conversion_factor = 10 -%}

        {%- capture loyalty_message_text -%}{{ block.settings.text }}{%- endcapture -%}
        {%- capture loyalty_message_points_insert -%}<span data-lion-price-for-product-id="{{ product.id }}">{{ product.selected_or_first_available_variant.price | divided_by: points_conversion_factor }}</span>{%- endcapture -%}
        
        {%- assign loyalty_message_text = loyalty_message_text | replace: "$POINTS", loyalty_message_points_insert -%}
        {%- assign loyalty_message_text = loyalty_message_text | replace: "<p>", "" -%}
        {%- assign loyalty_message_text = loyalty_message_text | replace: "</p>", "" -%}

        <div class="product-form__loyalty-earnings" data-block-type="loyalty-earnings" data-block-id="{{ block.id }}" {{ block.shopify_attributes }}>

          <product-loyalty-earnings class="product-form-banner product-loyalty-earnings product-form__banner">
            
            <div class="product-form-banner__icon product-loyalty-earnings__icon">
              {% render 'icon-rewards', icon: "points-pretty" %}
            </div>

            <div class="product-form-banner__content product-loyalty-earnings__content text--xsmall">

              <div class="product-loyalty-earnings__content-text">
                {{ loyalty_message_text }}
              </div>

              <div class="product-loyalty-earnings__content-links">

                {% if customer and settings.page_customer_rewards %}
                  <a class="button button--tertiary button--xxs" href="{{ settings.page_customer_rewards }}" target="_blank">{{ 'rewards.customer.my_rewards' | t }}</a>
                {% endif %}

                <a class="link link--faded" href="{{ settings.page_rewards_landing }}" target="_blank">{{ 'rewards.customer.learn_more' | t }}</a>
                
              </div>

            </div>

          </product-loyalty-earnings>
          
        </div>
      
      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      Strip Banner
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}

      {%- when 'strip_banner' -%}

        {%- capture justify_class -%}
          {%- if block.settings.text_alignment == 'left' -%}
            justify-content-start
          {%- elsif block.settings.text_alignment == 'center' -%}
            justify-content-center
          {%- elsif block.settings.text_alignment == 'right' -%}
            justify-content-end
          {%- endif -%}
        {%- endcapture -%}

        {%- capture text_align_class -%}
          text-{{ block.settings.text_alignment }}
        {%- endcapture -%}

        {%- capture classes -%}
          {% if block.settings.banner_size != blank %}product-form-banner--{{ block.settings.banner_size }}{% endif %}
          {% if block.settings.banner_color != blank and block.settings.banner_color != 'rgba(0,0,0,0)' %}product-form-banner--color{% endif %}
          {{ justify_class }}
          {{ text_align_class }}
        {%- endcapture -%}

        {%- capture styles -%}
          {%- if block.settings.banner_color != blank and block.settings.banner_color != 'rgba(0,0,0,0)' %}--banner-color: {{ block.settings.banner_color }};{% endif -%}
        {%- endcapture -%}


        {%- capture banner_content -%}
          {%- if block.settings.title != blank -%}
            <p class="{{ block.settings.title_size }}">{{ block.settings.title }}</p>  
          {%- endif -%}
          {%- if block.settings.text != blank -%}
            <div class="{{ block.settings.text_size }}">{{ block.settings.text }}</div>
          {%- endif -%}
        {%- endcapture -%}

        {%- if banner_content != blank -%}
          
          <div class="product-form__strip-banner" data-block-type="strip-banner" data-block-id="{{ block.id }}" {% if styles != blank %}style="{{ styles }}"{% endif %} {{ block.shopify_attributes }}>
            <div class="product-form-banner product-form__strip-banner product-form__banner {{ classes }}">
              <div class="product-form-banner__content {{ text_align_class }}">
                {{ banner_content }}
              </div>
            </div>
          </div>

        {%- endif -%}


      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      BUNDLE WIDGET
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      {%- when 'bundle_widget' -%}

        {%- if product.metafields.bundle.bundle_product != blank -%}

          {% comment %} Is Child Product {% endcomment %}

          {%- assign bundle_components = product.metafields.bundle.bundle_product.value.metafields.bundle.components.value -%}
          {%- assign bundle_product = product.metafields.bundle.bundle_product.value -%}

        {%- elsif product.metafields.bundle.components != blank -%}

          {% comment %} Is Parent Product {% endcomment %}
          
          {%- assign bundle_components = product.metafields.bundle.components.value -%}
          {%- assign bundle_first_component = bundle_components | first -%}
          {%- assign bundle_product = product -%}

        {%- endif -%}

        {%- if bundle_components != blank and bundle_product != blank -%}
          {%- assign show_bundle_widget = true -%}
        {%- else -%}
          {%- assign show_bundle_widget = false -%}
        {%- endif -%}

        {%- assign bundle_components_total_price = 0 -%}

        {%- if show_bundle_widget == true -%}

          {% if is_bundle == true %}
            <div class="product-form-banner product-form-banner--danger product-form__banner hidden loop--display-flex">
              <div class="product-form-banner__icon">
                {% render 'icon', icon: 'custom-returns' %}
              </div>
              <div class="product-form-banner__content product-loyalty-earnings__content text--xsmall">
                <div class="product-loyalty-earnings__content-text">
                  <p class="heading h5 margin-bottom-10">{{ 'returns.no_exchanges_title' | t }}</p>
                  <p>{{ 'returns.no_exchanges_text' | t }}</p>
                </div>
              </div>
            </div>
          {% endif %}
          
          <bundle-widget class="bundle-widget" {{ block.shopify_attributes }}>

            <div class="bundle-widget__inner">

              <div class="bundle-widget__header">

                <div class="bundle-widget__header-icon">
                  {% render 'icon', icon: 'custom-bundle' %}
                </div>

                <div class="bundle-widget__header-text">

                  {%- if product.metafields.bundle.components != blank -%}
                    {%- assign bundle_widget_header_title = 'product.general.bundle.widget_header_title_bundle' | t -%}
                    {%- assign bundle_widget_header_text = 'product.general.bundle.widget_header_text_bundle_html' | t -%}
                  {%- else -%}
                    {%- assign bundle_widget_header_title = 'product.general.bundle.widget_header_title_component' | t -%}
                    {%- assign bundle_widget_header_text = 'product.general.bundle.widget_header_text_component_html' | t -%}
                  {%- endif -%}

                  {%- if bundle_widget_header_title -%}
                    <p class="bundle-widget__header-title h5">
                      {{ bundle_widget_header_title }}
                    </p>
                  {%- endif -%}

                  {%- if bundle_widget_header_text -%}
                    <p class="bundle-widget__header-message">
                      <span class="text--xsmall">{{ bundle_widget_header_text }}</span>
                    </p>
                  {%- endif -%}

                </div>

              </div>

              <div class="bundle-widget__body">

                <div class="bundle-widget__products">

                  {%- for component_product in bundle_components -%}

                    {%- if component_product.metafields.custom.alternate_title != blank -%}
                      {%- assign title = component_product.metafields.custom.alternate_title -%}
                    {%- else -%}
                      {%- assign title = component_product.title -%}
                    {%- endif -%}

                    <input type='hidden' name='properties[Item {{ forloop.index }}]' value='{{ component_product.title | escape }}' form="{{ product_form_id }}">

                    {%- assign bundle_components_total_price = bundle_components_total_price | plus: component_product.price -%}

                    <div class="bundle-widget-product">

                      <div class="bundle-widget-product__inner">

                        <div class="bundle-widget-product__image">
                          {%- capture sizes_attribute -%}sizes_attribute: '(max-width: 740px) 65px, 92px' {%- endcapture -%}
                          {%- if component_product.featured_media -%}
                            {{ component_product.featured_media | image_url: width: component_product.featured_media.width | image_tag: loading: 'lazy', sizes: sizes_attribute, widths: '200,300,400,500,600,700,800,900,1000,1100,1200', data-media-id: component_product.featured_media.id }}
                          {%- endif -%}
                        </div>

                        <div class="bundle-widget-product__details">

                          <p class="bundle-widget-product__title text--xsmall strong">
                            {{ title }}
                            {% if product == component_product %}
                              <span class="label label--tiny label--custom2">{{ 'product.general.bundle.this_product' | t }}</span>
                            {% endif %}
                          </p>

                          {%- capture bundle_product_price -%}
                            {% render 'price', product: component_product %}
                          {%- endcapture -%}

                          {%- if bundle_product_price != blank -%}
                            {%- assign bundle_product_price = bundle_product_price | replace: shop.currency, "" -%}
                            <div class="bundle-widget-product__price">
                              {{ bundle_product_price }}
                            </div>
                          {%- endif -%}

                          <div class="bundle-widget-product__actions text--xxsmall text--subdued">

                            {% unless product == component_product %}
                              <a class="link" href="{{ component_product.url }}" target="_blank">
                                {%- if product.metafields.bundle.components != blank -%}
                                  {{ 'product.general.bundle.buy_separate' | t }}
                                {%- else -%}
                                  {{ 'product.general.view_details' | t }}
                                {%- endif -%}
                              </a>
                            {% endunless %}

                            {%- if component_product.metafields.product.size_chart != blank -%}

                              {%- assign size_chart_page = component_product.metafields.product.size_chart.value -%}
                              
                              <button type="button" is="toggle-button" class="link hidden-phone" aria-controls="{{ section.id }}--bundle-widget-product--{{ forloop.index }}--drawer" aria-expanded="false">{{ 'product.general.size_chart' | t }}</button>
                              <button type="button" is="toggle-button" class="link hidden-tablet-and-up" aria-controls="{{ section.id }}--bundle-widget-product--{{ forloop.index }}--popover" aria-expanded="false">{{ 'product.general.size_chart' | t }}</button>

                              <drawer-content id="{{ section.id }}--bundle-widget-product--{{ forloop.index }}--drawer" class="drawer drawer--large hidden-phone">
                                <span class="drawer__overlay"></span>

                                <header class="drawer__header">
                                  <p class="drawer__title heading h6">{{ size_chart_page.title }}</p>

                                  <button type="button" class="drawer__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
                                    {%- render 'icon' with 'close' -%}
                                  </button>
                                </header>

                                <div class="drawer__content drawer__content--padded-start">
                                  <div class="rte">
                                    {{- size_chart_page.content -}}
                                  </div>
                                </div>
                              </drawer-content>

                              {%- comment -%}Popover is for mobile{%- endcomment -%}
                              <popover-content id="{{ section.id }}--bundle-widget-product--{{ forloop.index }}--popover" class="popover hidden-lap-and-up">
                                <span class="popover__overlay"></span>

                                <header class="popover__header">
                                  <p class="popover__title heading h6">{{ size_chart_page.title }}</p>

                                  <button type="button" class="popover__close-button tap-area tap-area--large" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
                                    {%- render 'icon' with 'close' -%}
                                  </button>
                                </header>

                                <div class="popover__content">
                                  <div class="rte">
                                    {{- size_chart_page.content -}}
                                  </div>
                                </div>
                              </popover-content>

                            {%- endif -%}

                          </div>

                        </div>

                      </div>

                    </div>

                  {%- endfor -%}

                </div>

              </div>

              {%- capture bundle_widget_footer -%}

                {%- capture bundle_price_display -%}
                  <span class="price">{{ bundle_product.price | money_without_trailing_zeros }}</span>
                  <span class="price price--compare">{{ bundle_components_total_price | money_without_trailing_zeros }}</span>
                {%- endcapture -%}

                {%- assign bundle_price_display = bundle_price_display | replace: shop.currency, "" -%}

                {%- if product.metafields.bundle.bundle_product != blank -%}
                  <a class="button button--tiny button--primary" href="{{ product.metafields.bundle.bundle_product.value.url }}" target="_blank">
                    {{ 'product.general.bundle.buy_together_html' | t: price: bundle_price_display }}
                  </a>
                {%- elsif product.metafields.bundle.components != blank -%}
                  <div class="button button--tiny button--outline-faint button--disabled">
                    {{ 'product.general.bundle.buy_together_html' | t: price: bundle_price_display }}
                  </div>
                {%- endif -%}

              {%- endcapture -%}

              {%- if bundle_widget_footer != blank -%}
                <div class="bundle-widget__footer">
                  {{ bundle_widget_footer }}
                </div>
              {%- endif -%}
              
            </div>
  
          </bundle-widget>

        {%- endif -%}

      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      BUNDLE VARIANT PICKER BLOCK TYPE
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      {%- when 'bundle_variant_picker_v1' -%}
        {%- unless product.has_only_default_variant or block.settings.hide_sold_out_variants and product.available == false -%}
          {%- assign size_chart_page = block.settings.size_chart_page -%}
          {%- assign tog_rating_page = block.settings.tog_rating_page -%}
          {%- assign found_size_option = false -%}

          <bundle-variant-picker 
            data-block-type="variant-picker" 
            data-block-id="{{ section.id }}" 
            handle="{{ product.handle }}" 
            section-id="{{ section.id }}" 
            form-id="{{ product_form_id }}" 
            {% if update_url %}update-url{% endif %}
            {% if block.settings.hide_sold_out_variants %}hide-sold-out-variants{% endif %}
            class="product-form__variants bundle-variant-picker" {{ block.shopify_attributes }}>
            
            {%- comment -%}
            The variant data is outputted as a JSON, which allows the theme to emit an event with the data when the variant changes. This must not be removed.
            {%- endcomment -%}
            <script data-variant type="application/json">
              {{- product.selected_or_first_available_variant | json -}}
            </script>

            {%- for option in product.options_with_values -%}
              {%- assign option_downcase = option.name | downcase -%}
              {%- capture option_id -%}option-{{ section.id }}-{{ template.suffix }}-{{ product.id }}-{{ forloop.index }}{%- endcapture -%}

              {%- assign selector_type = block.settings.selector_mode -%}
              {%- assign variant_image_options = block.settings.variant_image_options | replace: ', ', ',' | downcase | split: ',' -%}

              {%- assign swatch_count = option.values | map: 'swatch' | compact | size -%}

              {% assign hide_selector = false %}
              {% if block.settings.ignore_color == true %}
                {% if option.name == "Color" or option.name == "color" %}
                  {% assign hide_selector = true %}
                {% endif %}
              {% endif %}

              {%- if swatch_count > 0 and block.settings.color_mode == 'swatch' %}
                {%- assign selector_type = 'swatch' -%}
              {%- endif -%}

              {% if swatch_count == 0 and color_label_list contains option_downcase and block.settings.color_mode != 'none' %}
                {%- assign selector_type = 'swatch' -%}
              {%- endif -%}

              {%- if variant_image_options contains option_downcase -%}
                {%- assign selector_type = 'variant_image' -%}
              {%- endif -%}

              {%- if option.name contains "(Size)" -%}
                {%- assign global_bundle_option_name = 'Select a Size' -%}
              {%- endif -%}

              {%- assign bundle_option_product_color_remove = option.name | split: " in " | first -%}
              {%- assign bundle_option_color_name = option.name | split: " in " | last -%}

              {%- assign bundle_option_name = option.name -%}
              {%- assign bundle_option_name = bundle_option_name | split: "(" | last | split: ")" | first -%}

              <div class="product-form__option-selector bundle-option-selector" data-selector-type="{{ selector_type | replace: '_', '-' | escape }}" {% if hide_selector == true %}style="display: none;"{% endif %}>
                <div class="product-form__option-info-wrapper">
                  {%- if bundle_option_name != blank -%}
                    <div class="product-form__option-info-title">
                      <span class="heading heading--xsmall">
                        {% if global_bundle_option_name != blank %}
                          {{ global_bundle_option_name }}
                        {% else %}
                          {{ bundle_option_product_color_remove | default: option.name }}
                        {% endif %}
                      </span>
                    </div>
                  {%- endif -%}
                  <div class="product-form__option-info heading heading--small">
                    <span class="product-form__option-name text--subdued">{{ bundle_option_name | default: option.name }}</span>
  
                    {%- if selector_type != 'dropdown' -%}
                      <span id="{{ option_id }}-value" class="product-form__option-value">{{ option.selected_value }}</span>
                    {%- endif -%}
  
                    {% capture product_option_links %}
                    {%- if size_chart_page != blank and size_label_list contains option_downcase -%}
                      {%- assign found_size_option = true -%}
                        <button type="button" is="toggle-button" class="product-form__option-link link hidden-phone" aria-controls="product-{{ section.id }}-{{ product.id }}-size-chart-drawer" aria-expanded="false">{{ 'product.general.size_chart' | t }}</button>
                        <button type="button" is="toggle-button" class="product-form__option-link link hidden-tablet-and-up" aria-controls="product-{{ section.id }}-{{ product.id }}-size-chart-popover" aria-expanded="false">{{ 'product.general.size_chart' | t }}</button>
                    {%- endif -%}
  
                      {%- if tog_rating_page != blank and size_label_list contains option_downcase -%}
                        <button type="button" is="toggle-button" class="product-form__option-link link hidden-phone" aria-controls="product-{{ section.id }}-{{ product.id }}-tog-rating-drawer" aria-expanded="false">{{ 'product.general.tog_rating' | t }}</button>
                        <button type="button" is="toggle-button" class="product-form__option-link link hidden-tablet-and-up" aria-controls="product-{{ section.id }}-{{ product.id }}-tog-rating-popover" aria-expanded="false">{{ 'product.general.tog_rating' | t }}</button>
                      {%- endif -%}
                    {% endcapture %}
  
                    {%- if product_option_links != blank -%}
                      <div class="product-form__option-links">
                        {{ product_option_links }}
                      </div>
                    {%- endif -%}
                  </div>

                </div>

                {%- case selector_type -%}
                  {%- when 'variant_image' -%}
                    <div class="variant-swatch-list">
                      {%- for value in option.values -%}
                        {%- assign image = value.variant.featured_media | default: value.variant.product.featured_media | default: product.featured_media -%}

                        <div class="variant-swatch {% unless value.available %}is-disabled{% endunless %}">
                          {%- if value.product_url != blank -%}
                            {%- if value == option.selected_value -%}
                              <input type="hidden" form="{{ product_form_id }}" data-option-position="{{ option.position }}" name="option{{ option.position }}" value="{{ value.id }}">
                            {%- endif -%}

                            <a href="{{ value.product_url }}" class="variant-swatch__item {% if value == option.selected_value %}is-selected{% endif %}">
                              <span class="visually-hidden">{{ value }}</span>

                              {{- image | image_url: width: image.width | image_tag: loading: 'lazy', sizes: '(max-width: 740px) 64px, 72px', sizes: '64,72,128,144,192,216', class: 'variant-swatch__image' -}}
                            </a>
                          {%- else -%}
                            <input class="variant-swatch__radio visually-hidden" type="radio" data-option-position="{{ option.position }}" name="option{{ option.position }}" form="{{ product_form_id }}" id="{{ option_id }}-{{ forloop.index }}" value="{{ value.id }}" {% if value == option.selected_value %}checked="checked"{% endif %} aria-label="{{ value | escape }}">
                            <label class="variant-swatch__item" for="{{ option_id }}-{{ forloop.index }}">
                              <span class="visually-hidden">{{ value }}</span>

                              {{- image | image_url: width: image.width | image_tag: loading: 'lazy', sizes: '(max-width: 740px) 64px, 72px', sizes: '64,72,128,144,192,216', class: 'variant-swatch__image' -}}
                            </label>
                          {%- endif -%}
                        </div>
                      {%- endfor -%}
                    </div>

                  {%- when 'swatch' -%}
                    <div class="color-swatch-list">
                      {%- assign color_swatch_config = settings.color_swatch_config | newline_to_br | split: '<br />' -%}

                      {%- for value in option.values -%}
                        {%- assign color_value_downcase = value | downcase -%}

                        <div class="color-swatch {% if color_white_label == color_value_downcase %}color-swatch--white{% endif %} {% unless value.available %}is-disabled{% endunless %}">
                          {%- if value.product_url != blank -%}
                            {%- if value == option.selected_value -%}
                              <input type="hidden" form="{{ product_form_id }}" data-option-position="{{ option.position }}" name="option{{ option.position }}" value="{{ value.id }}">
                            {%- endif -%}

                            <a href="{{ value.product_url }}" class="color-swatch__item {% if value == option.selected_value %}is-selected{% endif %}" style="{% render 'color-swatch-style', swatch: value.swatch, color_swatch_config: color_swatch_config, value: value %}">
                              <span class="visually-hidden">{{ value }}</span>
                            </a>
                          {%- else -%}
                            <input class="color-swatch__radio visually-hidden" type="radio" data-option-position="{{ option.position }}" name="option{{ option.position }}" form="{{ product_form_id }}" id="{{ option_id }}-{{ forloop.index }}" value="{{ value.id }}" {% if value == option.selected_value %}checked="checked"{% endif %}>
                            <label class="color-swatch__item" for="{{ option_id }}-{{ forloop.index }}" style="{% render 'color-swatch-style', swatch: value.swatch, color_swatch_config: color_swatch_config, value: value %}">
                              <span class="visually-hidden">{{ value }}</span>
                            </label>
                          {%- endif -%}
                        </div>
                      {%- endfor -%}
                    </div>

                  {%- when 'block' -%}

                    {%- assign size_class = 'block-swatch--small' -%}

                    <div class="block-swatch-list">
                      {%- for value in option.values -%}
                        <div class="block-swatch {{ size_class }} {% unless value.available %}is-disabled{% endunless %}">
                          {%- if value.product_url != blank -%}
                            {%- if value == option.selected_value -%}
                              <input type="hidden" form="{{ product_form_id }}" data-option-position="{{ option.position }}" name="option{{ option.position }}" value="{{ value.id }}">
                            {%- endif -%}

                            <a href="{{ value.product_url }}" class="block-swatch__item {% if value == option.selected_value %}is-selected{% endif %}">
                              {{- value -}}
                            </a>
                          {%- else -%}
                            <input class="block-swatch__radio visually-hidden" type="radio" data-option-position="{{ option.position }}" name="option{{ option.position }}" form="{{ product_form_id }}" id="{{ option_id }}-{{ forloop.index }}" value="{{ value.id }}" {% if value == option.selected_value %}checked="checked"{% endif %}>
                            <label class="block-swatch__item" for="{{ option_id }}-{{ forloop.index }}">{{ value }}</label>
                          {%- endif -%}
                        </div>
                      {%- endfor -%}
                    </div>

                  {%- when 'dropdown' -%}
                    <div class="select-wrapper">
                      <combo-box initial-focus-selector="[aria-selected='true']" id="{{ option_id }}-combo-box" class="combo-box">
                        <span class="combo-box__overlay"></span>

                        <header class="combo-box__header">
                          <p class="combo-box__title heading h6">{{ option.name }}</p>

                          <button type="button" class="combo-box__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
                            {%- render 'icon' with 'close' -%}
                          </button>
                        </header>

                        <div class="combo-box__option-list" role="listbox">
                          {%- for value in option.values -%}
                            {%- if value.product_url != blank -%}
                              {%- if value == option.selected_value -%}
                                <input type="hidden" form="{{ product_form_id }}" data-option-position="{{ option.position }}" name="option{{ option.position }}" value="{{ value.id }}">
                              {%- endif -%}

                              <a href="{{ value.product_url }}" aria-selected="{% if value == option.selected_value %}true{% else %}false{% endif %}" class="combo-box__option-item {% if value.available == false %}is-disabled{% endif %}">
                                {{- value -}}
                              </a>
                            {%- else -%}
                              <button type="button" role="option" class="combo-box__option-item {% if value.available == false %}is-disabled{% endif %}" value="{{ value.id }}" aria-selected="{% if value == option.selected_value %}true{% else %}false{% endif %}">{{ value }}</button>
                            {%- endif -%}
                          {%- endfor -%}
                        </div>

                        <input type="hidden" data-option-position="{{ option.position }}" name="option{{ option.position }}" form="{{ product_form_id }}" value="{{ option.selected_value.id }}" aria-label="{{ option.name | escape }}">
                      </combo-box>

                      <button type="button" is="toggle-button" class="select" aria-expanded="false" aria-haspopup="listbox" aria-controls="{{ option_id }}-combo-box">
                        <span id="{{ option_id }}-value" class="select__selected-value">{{ option.selected_value }}</span>
                        {%- render 'icon' with 'chevron' -%}
                      </button>
                    </div>
                {%- endcase -%}
              </div>
            {%- endfor -%}

            <noscript>
              <label class="input__block-label" for="product-select-{{ section.id }}-{{ product.id }}">{{ 'product.form.variant' | t }}</label>

              <div class="select-wrapper">
                <select class="select" autocomplete="off" id="product-select-{{ section.id }}-{{ product.id }}" name="id" form="{{ product_form_id }}">
                  {%- for variant in product.variants -%}
                    <option {% if variant == product.selected_or_first_available_variant %}selected="selected"{% endif %} {% unless variant.available %}disabled="disabled"{% endunless %} value="{{ variant.id }}" data-sku="{{ variant.sku }}">{{ variant.title }} - {{ variant.price | money }}</option>
                  {%- endfor -%}
                </select>

                {%- render 'icon' with 'chevron' -%}
              </div>
            </noscript>
          </bundle-variant-picker>
        {%- endunless -%}

      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      VARIANT PICKER BLOCK TYPE
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      {%- when 'variant_picker' -%}
        {%- unless product.has_only_default_variant or block.settings.hide_sold_out_variants and product.available == false -%}
          {%- assign size_chart_page = block.settings.size_chart_page -%}
          {%- assign tog_rating_page = block.settings.tog_rating_page -%}
          {%- assign found_size_option = false -%}

          <variant-picker data-block-type="variant-picker" data-block-id="{{ section.id }}" handle="{{ product.handle }}" section-id="{{ section.id }}" form-id="{{ product_form_id }}" {% if update_url %}update-url{% endif %} {% if block.settings.hide_sold_out_variants %}hide-sold-out-variants{% endif %} class="product-form__variants" {{ block.shopify_attributes }}>
            {%- comment -%}
            The variant data is outputted as a JSON, which allows the theme to emit an event with the data when the variant changes. This must not be removed.
            {%- endcomment -%}
            <script data-variant type="application/json">
              {{- product.selected_or_first_available_variant | json -}}
            </script>

            {%- for option in product.options_with_values -%}
              {%- assign option_downcase = option.name | downcase -%}
              {%- capture option_id -%}option-{{ section.id }}-{{ template.suffix }}-{{ product.id }}-{{ forloop.index }}{%- endcapture -%}

              {%- assign selector_type = block.settings.selector_mode -%}
              {%- assign variant_image_options = block.settings.variant_image_options | replace: ', ', ',' | downcase | split: ',' -%}

              {%- assign swatch_count = option.values | map: 'swatch' | compact | size -%}

              {% assign hide_selector = false %}
              {% if block.settings.ignore_color == true %}
                {% if option.name == "Color" or option.name == "color" %}
                  {% assign hide_selector = true %}
                {% endif %}
              {% endif %}

              {%- if swatch_count > 0 and block.settings.color_mode == 'swatch' %}
                {%- assign selector_type = 'swatch' -%}
              {%- endif -%}

              {% if swatch_count == 0 and color_label_list contains option_downcase and block.settings.color_mode != 'none' %}
                {%- assign selector_type = 'swatch' -%}
              {%- endif -%}

              {%- if variant_image_options contains option_downcase -%}
                {%- assign selector_type = 'variant_image' -%}
              {%- endif -%}

              <div class="product-form__option-selector" data-selector-type="{{ selector_type | replace: '_', '-' | escape }}" {% if hide_selector == true %}style="display: none;"{% endif %}>
                <div class="product-form__option-info">
                  <span class="product-form__option-name">{{ option.name }}:</span>

                  {%- if selector_type != 'dropdown' -%}
                    <span id="{{ option_id }}-value" class="product-form__option-value">{{ option.selected_value }}</span>
                  {%- endif -%}

                  {% capture product_option_links %}
                  {%- if size_chart_page != blank and size_label_list contains option_downcase -%}
                    {%- assign found_size_option = true -%}
                      <button type="button" is="toggle-button" class="product-form__option-link link hidden-phone" aria-controls="product-{{ section.id }}-{{ product.id }}-size-chart-drawer" aria-expanded="false">{{ 'product.general.size_chart' | t }}</button>
                      <button type="button" is="toggle-button" class="product-form__option-link link hidden-tablet-and-up" aria-controls="product-{{ section.id }}-{{ product.id }}-size-chart-popover" aria-expanded="false">{{ 'product.general.size_chart' | t }}</button>
                  {%- endif -%}

                    {%- if tog_rating_page != blank and size_label_list contains option_downcase -%}
                      <button type="button" is="toggle-button" class="product-form__option-link link hidden-phone" aria-controls="product-{{ section.id }}-{{ product.id }}-tog-rating-drawer" aria-expanded="false">{{ 'product.general.tog_rating' | t }}</button>
                      <button type="button" is="toggle-button" class="product-form__option-link link hidden-tablet-and-up" aria-controls="product-{{ section.id }}-{{ product.id }}-tog-rating-popover" aria-expanded="false">{{ 'product.general.tog_rating' | t }}</button>
                    {%- endif -%}
                  {% endcapture %}

                  {%- if product_option_links != blank -%}
                    <div class="product-form__option-links">
                      {{ product_option_links }}
                    </div>
                  {%- endif -%}

                  
                </div>

                {%- case selector_type -%}
                  {%- when 'variant_image' -%}
                    <div class="variant-swatch-list">
                      {%- for value in option.values -%}
                        {%- assign image = value.variant.featured_media | default: value.variant.product.featured_media | default: product.featured_media -%}

                        <div class="variant-swatch {% unless value.available %}is-disabled{% endunless %}">
                          {%- if value.product_url != blank -%}
                            {%- if value == option.selected_value -%}
                              <input type="hidden" form="{{ product_form_id }}" data-option-position="{{ option.position }}" name="option{{ option.position }}" value="{{ value.id }}">
                            {%- endif -%}

                            <a href="{{ value.product_url }}" class="variant-swatch__item {% if value == option.selected_value %}is-selected{% endif %}">
                              <span class="visually-hidden">{{ value }}</span>

                              {{- image | image_url: width: image.width | image_tag: loading: 'lazy', sizes: '(max-width: 740px) 64px, 72px', sizes: '64,72,128,144,192,216', class: 'variant-swatch__image' -}}
                            </a>
                          {%- else -%}
                            <input class="variant-swatch__radio visually-hidden" type="radio" data-option-position="{{ option.position }}" name="option{{ option.position }}" form="{{ product_form_id }}" id="{{ option_id }}-{{ forloop.index }}" value="{{ value.id }}" {% if value == option.selected_value %}checked="checked"{% endif %} aria-label="{{ value | escape }}">
                            <label class="variant-swatch__item" for="{{ option_id }}-{{ forloop.index }}">
                              <span class="visually-hidden">{{ value }}</span>

                              {{- image | image_url: width: image.width | image_tag: loading: 'lazy', sizes: '(max-width: 740px) 64px, 72px', sizes: '64,72,128,144,192,216', class: 'variant-swatch__image' -}}
                            </label>
                          {%- endif -%}
                        </div>
                      {%- endfor -%}
                    </div>

                  {%- when 'swatch' -%}
                    <div class="color-swatch-list">
                      {%- assign color_swatch_config = settings.color_swatch_config | newline_to_br | split: '<br />' -%}

                      {%- for value in option.values -%}
                        {%- assign color_value_downcase = value | downcase -%}

                        <div class="color-swatch {% if color_white_label == color_value_downcase %}color-swatch--white{% endif %} {% unless value.available %}is-disabled{% endunless %}">
                          {%- if value.product_url != blank -%}
                            {%- if value == option.selected_value -%}
                              <input type="hidden" form="{{ product_form_id }}" data-option-position="{{ option.position }}" name="option{{ option.position }}" value="{{ value.id }}">
                            {%- endif -%}

                            <a href="{{ value.product_url }}" class="color-swatch__item {% if value == option.selected_value %}is-selected{% endif %}" style="{% render 'color-swatch-style', swatch: value.swatch, color_swatch_config: color_swatch_config, value: value %}">
                              <span class="visually-hidden">{{ value }}</span>
                            </a>
                          {%- else -%}
                            <input class="color-swatch__radio visually-hidden" type="radio" data-option-position="{{ option.position }}" name="option{{ option.position }}" form="{{ product_form_id }}" id="{{ option_id }}-{{ forloop.index }}" value="{{ value.id }}" {% if value == option.selected_value %}checked="checked"{% endif %}>
                            <label class="color-swatch__item" for="{{ option_id }}-{{ forloop.index }}" style="{% render 'color-swatch-style', swatch: value.swatch, color_swatch_config: color_swatch_config, value: value %}">
                              <span class="visually-hidden">{{ value }}</span>
                            </label>
                          {%- endif -%}
                        </div>
                      {%- endfor -%}
                    </div>

                  {%- when 'block' -%}
                    <div class="block-swatch-list">
                      {%- for value in option.values -%}
                        <div class="block-swatch {% unless value.available %}is-disabled{% endunless %}">
                          {%- if value.product_url != blank -%}
                            {%- if value == option.selected_value -%}
                              <input type="hidden" form="{{ product_form_id }}" data-option-position="{{ option.position }}" name="option{{ option.position }}" value="{{ value.id }}">
                            {%- endif -%}

                            <a href="{{ value.product_url }}" class="block-swatch__item {% if value == option.selected_value %}is-selected{% endif %}">
                              {{- value -}}
                            </a>
                          {%- else -%}
                            <input class="block-swatch__radio visually-hidden" type="radio" data-option-position="{{ option.position }}" name="option{{ option.position }}" form="{{ product_form_id }}" id="{{ option_id }}-{{ forloop.index }}" value="{{ value.id }}" {% if value == option.selected_value %}checked="checked"{% endif %}>
                            <label class="block-swatch__item" for="{{ option_id }}-{{ forloop.index }}">{{ value }}</label>
                          {%- endif -%}
                        </div>
                      {%- endfor -%}
                    </div>

                  {%- when 'dropdown' -%}
                    <div class="select-wrapper">
                      <combo-box initial-focus-selector="[aria-selected='true']" id="{{ option_id }}-combo-box" class="combo-box">
                        <span class="combo-box__overlay"></span>

                        <header class="combo-box__header">
                          <p class="combo-box__title heading h6">{{ option.name }}</p>

                          <button type="button" class="combo-box__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
                            {%- render 'icon' with 'close' -%}
                          </button>
                        </header>

                        <div class="combo-box__option-list" role="listbox">
                          {%- for value in option.values -%}
                            {%- if value.product_url != blank -%}
                              {%- if value == option.selected_value -%}
                                <input type="hidden" form="{{ product_form_id }}" data-option-position="{{ option.position }}" name="option{{ option.position }}" value="{{ value.id }}">
                              {%- endif -%}

                              <a href="{{ value.product_url }}" aria-selected="{% if value == option.selected_value %}true{% else %}false{% endif %}" class="combo-box__option-item {% if value.available == false %}is-disabled{% endif %}">
                                {{- value -}}
                              </a>
                            {%- else -%}
                              <button type="button" role="option" class="combo-box__option-item {% if value.available == false %}is-disabled{% endif %}" value="{{ value.id }}" aria-selected="{% if value == option.selected_value %}true{% else %}false{% endif %}">{{ value }}</button>
                            {%- endif -%}
                          {%- endfor -%}
                        </div>

                        <input type="hidden" data-option-position="{{ option.position }}" name="option{{ option.position }}" form="{{ product_form_id }}" value="{{ option.selected_value.id }}" aria-label="{{ option.name | escape }}">
                      </combo-box>

                      <button type="button" is="toggle-button" class="select" aria-expanded="false" aria-haspopup="listbox" aria-controls="{{ option_id }}-combo-box">
                        <span id="{{ option_id }}-value" class="select__selected-value">{{ option.selected_value }}</span>
                        {%- render 'icon' with 'chevron' -%}
                      </button>
                    </div>
                {%- endcase -%}
              </div>
            {%- endfor -%}

            <noscript>
              <label class="input__block-label" for="product-select-{{ section.id }}-{{ product.id }}">{{ 'product.form.variant' | t }}</label>

              <div class="select-wrapper">
                <select class="select" autocomplete="off" id="product-select-{{ section.id }}-{{ product.id }}" name="id" form="{{ product_form_id }}">
                  {%- for variant in product.variants -%}
                    <option {% if variant == product.selected_or_first_available_variant %}selected="selected"{% endif %} {% unless variant.available %}disabled="disabled"{% endunless %} value="{{ variant.id }}" data-sku="{{ variant.sku }}">{{ variant.title }} - {{ variant.price | money }}</option>
                  {%- endfor -%}
                </select>

                {%- render 'icon' with 'chevron' -%}
              </div>
            </noscript>
          </variant-picker>
        {%- endunless -%}

      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      LINE ITEM PROPERTY
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      {%- when 'line_item_property' -%}
        {%- if block.settings.label != blank -%}
          {%- case block.settings.type -%}
            {%- when 'text' -%}
              {%- if block.settings.allow_long_text -%}
                <div class="product-form__line-item-property" {{ block.shopify_attributes }}>
                  <label class="input__block-label" for="line-item-{{ section.id }}-{{ block.id }}">{{ block.settings.label | escape }}:</label>
                  <textarea {% if block.settings.required %}required{% endif %} id="line-item-{{ section.id }}-{{ block.id }}" form="{{ product_form_id }}" class="input__field input__field--textarea" name="properties[{{ block.settings.label | escape }}]"></textarea>
                </div>
              {% else %}
                <div class="product-form__line-item-property" {{ block.shopify_attributes }}>
                  <label class="input__block-label" for="line-item-{{ section.id }}-{{ block.id }}">{{ block.settings.label | escape }}:</label>
                  <input {% if block.settings.required %}required{% endif %} id="line-item-{{ section.id }}-{{ block.id }}" form="{{ product_form_id }}" type="text" class="input__field" name="properties[{{ block.settings.label | escape }}]">
                </div>
              {%- endif -%}

            {%- when 'checkbox' -%}
              <div class="product-form__line-item-property" {{ block.shopify_attributes }}>
                <div class="checkbox-container">
                  <input type="hidden" form="{{ product_form_id }}" class="checkbox" name="properties[{{ block.settings.label | escape }}]" value="{{ block.settings.unchecked_value | escape }}">
                  <input type="checkbox" form="{{ product_form_id }}" class="checkbox" name="properties[{{ block.settings.label | escape }}]" id="line-item-{{ section.id }}-{{ block.id }}" value="{{ block.settings.checked_value | escape }}">
                  <label for="line-item-{{ section.id }}-{{ block.id }}">{{ block.settings.label | escape }}</label>
                </div>
              </div>
          {%- endcase -%}
        {%- endif -%}

      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      TEXT
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      {%- when 'text' -%}
        {% if block.settings.text != blank %}
          <div class="product-form__text minor" {{ block.shopify_attributes }}>
            {{- block.settings.text -}}
          </div>
        {%- endif -%}
      
        {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      Special Line Items and Messaging
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      {%- when 'special_messaging' -%}

        {% capture special_messaging_content %}

          {%- comment -%}
            PRE-ORDERS
          {%- endcomment -%}

          {%- capture pre_order_one_tag -%}
            {%- if settings.pre_order_one_tag != blank -%}
              {{- settings.pre_order_one_tag -}}
            {%- endif -%}
          {%- endcapture -%}

          {%- capture pre_order_two_tag -%}
            {%- if settings.pre_order_two_tag != blank -%}
              {{- settings.pre_order_two_tag -}}
            {%- endif -%}
          {%- endcapture -%}

          {% if product.tags contains pre_order_one_tag %}

            {% assign preorder_date = settings.pre_order_one_date %}
            {% assign preorder_message = settings.pre_order_one_message %}

            <div class="note note--primary minor">
              <span class="note__icon">{% render 'icon' with settings.pre_order_one_icon %}</span>
              <span class="note__text">
                {{ 'product.general.special_messaging.pre_order_html' | t: date: preorder_date }}
                {%- if preorder_message != blank -%}
                  {{ preorder_message }}
                {%- endif -%}
              </span>
            </div>

            <input type="hidden" form="{{ product_form_id }}" name="properties[Pre-order]" value="{{ 'product.general.special_messaging.pre_order_html' | t: date: settings.pre_order_one_date | strip_html }}">
            
          {% elsif product.tags contains pre_order_two_tag %}

            {% assign preorder_date = settings.pre_order_two_date %}
            {% assign preorder_message = settings.pre_order_two_message %}

            <div class="note note--primary minor">
              <span class="note__icon">{% render 'icon' with settings.pre_order_two_icon %}</span>
              <span class="note__text">
                {{ 'product.general.special_messaging.pre_order_html' | t: date: preorder_date }}
                {%- if preorder_message != blank -%}
                  {{ preorder_message }}
                {%- endif -%} 
              </span>
            </div>
                
            <input type="hidden" form="{{ product_form_id }}" name="properties[Pre-order]" value="{{ 'product.general.special_messaging.pre_order_html' | t: date: settings.pre_order_two_date | strip_html }}">

          {% endif %}


          {%- comment -%}
            CLEARANCE
          {%- endcomment -%}

          {% comment %}
            Checks for the clearance message tag or clearance collection, and sets a variable to true if present.
          {% endcomment %}

          {%- assign is_clearance = false -%}

          {%- for collection in settings.clearance_collections -%}
            {%- if product.collections contains collection -%}
              {%- assign is_clearance = true -%}
              {%- break -%}
            {%- endif -%}
          {%- endfor -%}

          {%- if product.tags contains settings.clearance_tag -%}
            {%- assign is_clearance = true -%}
          {%- endif -%}

          {% comment %}
            Display a banner
          {% endcomment %}

          {%- if is_clearance == true and settings.clearance_message != blank -%}
            <div class="note note--primary minor">
              <span class="note__icon">{%- render 'icon' with settings.clearance_icon -%}</span>
              <span class="note__text">
                {{ settings.clearance_message }}
              </span>
            </div>
          {%- endif -%}

        {% endcapture %}

        {% if special_messaging_content != blank %}
          <div class="product-form__special-messaging" {{ block.shopify_attributes }}>
            {{ special_messaging_content }}
          </div>
        {% endif %}

      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      PRODUCT LIMIT
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      {%- when 'product_limit' -%}

        {%- if product_limit != "" -%}
          
          <product-limit-notice class="product-form__product-limit" {{ block.shopify_attributes }}>
            
            <div class="note note--small note--tertiary">
              <span class="note__icon">{% render 'icon' with 'picto-coupon' %}</span>
              <span class="note__text">{{ 'product.general.product_limit' | t: limit: product_limit }}</span>
            </div>

            {% comment %} <input type="hidden" form="{{ product_form_id }}" name="properties[Product Limit]" value="{{ product_limit }}"> {% endcomment %}

          </product-limit-notice>

        {%- endif -%}

      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      COLLECTION VARIANT SWATCHES
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      {%- when 'collection_swatches' -%}

      {%- liquid

        assign current_handle = product.handle
        assign swatch_collection = collections[product.metafields.product_link.collection]
  
        capture product_ids
          for product in swatch_collection.products
            echo product.handle | append: "|"
          endfor
        endcapture
  
        assign swatch_collections = product.metafields.product_link.collection_tabs.value
  
        assign has_all_collection = false
        for swatch_collection in swatch_collections
          if swatch_collection.title contains "All"
            assign has_all_collection = true
          endif
        endfor
  
      -%}

      {%- capture swatch_collection_tab_list -%}

        {%- if swatch_collections != blank -%}

          {%- if swatch_collections.count <= 1 -%}
            {%- assign only_swatch_collection = true -%}
          {%- endif -%}

          {%- for swatch_collection in swatch_collections -%}

            {%- if sub_brand and collection -%}
              {%- assign swatch_product_url = swatch_product.url | within: collection -%}
            {%- else -%}
              {%- assign swatch_product_url = swatch_product.url -%}
            {%- endif -%}

            {% assign tab_active = false %}
            {% for swatch_product in swatch_collection.products %}
              {% if swatch_product_url == product.url %}
                {%- assign style_title = swatch_collection.title | split: "-" | last -%}
                {%- break -%}
              {% endif %}
            {% endfor %}
    
          {% endfor %}

          <div class="product-form__option-selector {% if only_swatch_collection == true %}hidden{% endif %}">
            <div class="product-form__option-info">
              <span class="product-form__option-name">Style</span>
              <span class="variant__label-info">
                :
                <span class="style-title">
                  <!-- {{ product.first_available_variant.option1 }} -->
                  {{ style_title }}
                </span>
              </span>
            </div>
          </div>
    
          <div class="swatch-collections-tabs {% if only_swatch_collection == true %}hidden{% endif %}">
    
            {%- for swatch_collection in swatch_collections -%}

              {%- capture swatch_collection_title -%}
                {{- swatch_collection.title | split: "-" | last -}}
              {%- endcapture -%}

              {%- if swatch_collection_title contains "All" -%}
                {%- assign all_collection = true -%}
              {%- else -%}
                {%- assign all_collection = false -%}
              {%- endif -%}

              {% assign tab_active = false %}
              {% for swatch_product in swatch_collection.products %}

                {%- if sub_brand and collection -%}
                  {%- assign swatch_product_url = swatch_product.url | within: collection -%}
                {%- else -%}
                  {%- assign swatch_product_url = swatch_product.url -%}
                {%- endif -%}

                {% if has_all_collection == true %}
                  {% if all_collection == true %}
                    {% assign tab_active = true %}
                  {% endif %}
                {% else %}
                  {% if swatch_product.url == product.url and all_collection == false %}
                    {% assign tab_active = true %}
                  {% endif %}
                {% endif %}

              {% endfor %}

              <button class="swatch-collections-tab" aria-controls="{{ swatch_collection.id }}" aria-expanded="{% if tab_active == true %}true{% else %}false{% endif %}">
                {{ swatch_collection_title }}
              </button>
    
            {%- endfor -%}
    
          </div>
          
        {%- elsif swatch_collection != blank -%}

          
          
        {%- endif -%}

      {%- endcapture -%}
      
      {%- capture swatch_collection_content -%}

        {%- if swatch_collections != blank -%}

          {%- for swatch_collection in swatch_collections -%}

            {%- capture swatch_collection_title -%}
              {{- swatch_collection.title | split: "-" | last -}}
            {%- endcapture -%}

            {%- if swatch_collection_title contains "All" -%}
              {%- assign all_collection = true -%}
            {%- else -%}
              {%- assign all_collection = false -%}
            {%- endif -%}

            {% assign tab_active = false %}
            {% for swatch_product in swatch_collection.products %}

              {%- if sub_brand and collection -%}
                {%- assign swatch_product_url = swatch_product.url | within: collection -%}
              {%- else -%}
                {%- assign swatch_product_url = swatch_product.url -%}
              {%- endif -%}
              
              {% if has_all_collection == true %}
                {% if all_collection == true %}
                  {% assign tab_active = true %}
                {% endif %}
              {% else %}
                {% if swatch_product.url == product.url and all_collection == false %}
                  {% assign tab_active = true %}
                {% endif %}
              {% endif %}

            {% endfor %}
  
            <div class="swatch-collections-content" {% if tab_active == true %}{% else %}hidden{% endif %} id="{{ swatch_collection.id }}">
  
              <div class="product-form__option-selector">
                <div class="product-form__option-info">
                  <span class="product-form__option-name">Color</span>
                  <span class="variant__label-info">
                    :
                    <span
                      id="VariantColorLabel-{{ section_id }}-{{ forloop.index0 }}"
                      data-option-index="{{ color_option_index }}">
                      {{ product.first_available_variant.option1 }}
                    </span>
                  </span>
                </div>

                {% if all_collection == true %}
                
                  <color-swatch-list data-collection-id='{{ swatch_collection.id }}' remote {% if settings.collapsing_color_swatch_collections == true %}collapsing{% endif %}>

                    <div class="color-swatch-list__loading-message">
                      <span class="heading--xsmall">Loading...</span>
                    </div>

                    <div class="color-swatch-list">
                      {%- comment -%}
                        Color swatches loaded here.
                      {%- endcomment -%}
                    </div>

                  </color-swatch-list> 
 
                {% else %}

                  <color-swatch-list data-collection-id='{{ swatch_collection.id }}'>

                    <div class="color-swatch-list">
                    
                      {%- assign color_swatch_config = settings.color_swatch_config | newline_to_br | split: '<br />' -%}
      
                      {% for swatch_product in swatch_collection.products %}

                        {%- if sub_brand and collection -%}
                          {%- assign swatch_product_url = swatch_product.url | within: collection -%}
                        {%- else -%}
                          {%- assign swatch_product_url = swatch_product.url -%}
                        {%- endif -%}
      
                        {%- capture tooltip -%}
                          {%- if swatch_product.metafields.collection_swatches.tooltip != blank -%}
                            {{ swatch_product.metafields.collection_swatches.tooltip }}
                          {%- else -%}
                            {%- assign product_title_fragment = swatch_product.title | split: " in " | first -%}
                            {{- swatch_product.title | replace: product_title_fragment, "" | replace: " in ", "" -}}
                          {%- endif -%}
                        {%- endcapture -%}

                        <a class="color-swatch {% if product.url == swatch_product.url %}color-swatch--active{% endif %}" aria-label="{{ swatch_product_url }}" href="{{ swatch_product_url }}" data-tooltip="{{ tooltip }}">
                          <div class="color-swatch__item color_link_swatch{% if current_handle == swatch_product.handle %} selected{% endif %}" style="{% if swatch_product.metafields.product_link['product_color'] != blank %}background-color: {{swatch_product.metafields.product_link['product_color']}}; {% endif %}{% if swatch_product.metafields.product_link['product_img'] != blank %}background-image: url('{{ swatch_product.metafields.product_link['product_img']}}'){% endif %}"></div>
                        </a>
      
                      {% endfor %}
      
                    </div>

                  </color-swatch-list>

                {% endif %}

              </div>
  
            </div>
  
          {%- endfor -%}

        {%- elsif swatch_collection != blank -%}

          <div class="swatch-collections-content">
    
            <div class="product-form__option-selector">
              <div class="product-form__option-info">
                <span class="product-form__option-name">Color</span>
                <span class="variant__label-info">
                  :
                  <span
                    id="VariantColorLabel-{{ section_id }}-{{ forloop.index0 }}"
                    data-option-index="{{ color_option_index }}">
                    {{ product.first_available_variant.option1 }}
                  </span>
                </span>
              </div>
              <div class="color-swatch-list">
                    
                {%- assign color_swatch_config = settings.color_swatch_config | newline_to_br | split: '<br />' -%}
    
                {% for swatch_product in swatch_collection.products %}

                  {%- if sub_brand and collection -%}
                    {%- assign swatch_product_url = swatch_product.url | within: collection -%}
                  {%- else -%}
                    {%- assign swatch_product_url = swatch_product.url -%}
                  {%- endif -%}
    
                  {%- capture tooltip -%}
                    {%- if swatch_product.metafields.collection_swatches.tooltip != blank -%}
                      {{ swatch_product.metafields.collection_swatches.tooltip }}
                    {%- else -%}
                      {%- assign product_title_fragment = swatch_product.title | split: " in " | first -%}
                      {{- swatch_product.title | replace: product_title_fragment, "" | replace: " in ", "" -}}
                    {%- endif -%}
                  {%- endcapture -%}
    
                  <a class="color-swatch {% if product.url == swatch_product.url %}color-swatch--active{% endif %}" aria-label="{{ swatch_product_url }}" href="{{ swatch_product_url }}" data-tooltip="{{ tooltip }}">
                    <div class="color-swatch__item color_link_swatch{% if current_handle == swatch_product.handle %} selected{% endif %}" style="{% if swatch_product.metafields.product_link['product_color'] != blank %}background-color: {{swatch_product.metafields.product_link['product_color']}}; {% endif %}{% if swatch_product.metafields.product_link['product_img'] != blank %}background-image: url('{{ swatch_product.metafields.product_link['product_img']}}'){% endif %}"></div>
                  </a>
    
                {% endfor %}
    
              </div>
            </div>
    
          </div>

        {%- endif -%}

      {%- endcapture -%}

      {% if swatch_collections != blank or swatch_collection != blank %}

        <swatch-collections class="swatch-collections-tabs-container">

          {% if swatch_collection_tab_list != blank %}
            {{ swatch_collection_tab_list }}
          {% endif %}

          <div class="swatch-collections-contents">
            {{ swatch_collection_content }}
          </div>

        </swatch-collections>

      {% endif %}

      {% if block.settings.text != blank %}
        <div class="product-form__link-swatches" {{ block.shopify_attributes }}>
          {{- block.settings.text -}}
        </div>
      {%- endif -%}

      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      IMAGE
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      {%- when 'image' -%}
        {% if block.settings.image != blank and context != 'quick-buy' %}
          <div class="product-form__image product-form__image--{{ block.settings.image_alignment }}" {{ block.shopify_attributes }}>
            {%- capture image_sizes -%}{{ block.settings.image_width }}, {{ block.settings.image_width | times: 2 }}, {{ block.settings.image_width | times: 3 }}{%- endcapture -%}
            {%- capture sizes_attribute -%}{{ block.settings.image_width }}px{%- endcapture -%}
            {%- capture style_attribute -%}max-width: {{ block.settings.image_width }}px{%- endcapture -%}
            {{- block.settings.image | image_url: width: block.settings.image.width | image_tag: loading: 'lazy', sizes: sizes_attribute, widths: image_sizes, style: style_attribute -}}
          </div>
        {%- endif -%}

      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      BUTTON
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      {%- when 'button' -%}
        {% if block.settings.link != blank and block.settings.text != blank and context != 'quick-buy' %}
          <div class="product-form__button" {{ block.shopify_attributes }}>
            {%- if block.settings.background == 'rgba(0,0,0,0)' -%}
              {%- assign button_background = settings.primary_button_background -%}
            {%- else -%}
              {%- assign button_background = block.settings.background -%}
            {%- endif -%}

            {%- if block.settings.text_color == 'rgba(0,0,0,0)' -%}
              {%- assign button_text_color = settings.primary_button_text_color -%}
            {%- else -%}
              {%- assign button_text_color = block.settings.text_color -%}
            {%- endif -%}

            {%- capture button_background_rgb -%}{{ button_background.red }}, {{ button_background.green }}, {{ button_background.blue }}{%- endcapture -%}
            {%- capture button_text_color_rgb -%}{{ button_text_color.red }}, {{ button_text_color.green }}, {{ button_text_color.blue }}{%- endcapture -%}

            <a href="{{ block.settings.link }}" class="button button--primary {% if block.settings.stretch %}button--full{% endif %}" style="--primary-button-background: {{ button_background_rgb }}; --primary-button-text-color: {{ button_text_color_rgb }}">{{ block.settings.text | escape }}</a>
          </div>
        {%- endif -%}

      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      CUSTOM LIQUID
      ----------------------------------------------------------------------------------------------------------------
      
      {%- when 'liquid' -%}
        <div data-block-type="liquid" data-block-id="{{ block.id }}" {{ block.shopify_attributes }}>
          {% if block.settings.liquid != blank %}
            <div class="product-form__custom-liquid">
              {{- block.settings.liquid -}}
            </div>
          {%- endif -%}
        </div>

      {%- endcomment -%}
      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      QUANTITY SELECTOR
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      {%- when 'quantity_selector' -%}
        {%- if request.page_type != 'password' and product.available -%}
          <div class="product-form__quantity" {{ block.shopify_attributes }}>
            <span class="product-form__quantity-label">{{ 'product.form.quantity' | t }}:</span>

            <quantity-selector class="quantity-selector">
              <button type="button" class="quantity-selector__button">
                <span class="visually-hidden">{{ 'cart.general.decrease_quantity' | t }}</span>
                {%- render 'icon' with 'minus-big' -%}
              </button>

              <input type="text" form="{{ product_form_id }}" is="input-number" class="quantity-selector__input" inputmode="numeric" name="quantity" autocomplete="off" min="1" value="1" size="2" aria-label="{{ 'product.form.quantity' | t | escape }}">

              <button type="button" class="quantity-selector__button">
                <span class="visually-hidden">{{ 'cart.general.increase_quantity' | t }}</span>
                {%- render 'icon' with 'plus-big' -%}
              </button>
            </quantity-selector>
          </div>
        {%- endif -%}

      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      PRODUCT DESCRIPTION
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      {%- when 'addon' -%}

        {% comment %} {%- if request.page_type != 'password' and product.available and block.settings.addon_enabled == true and block.settings.addon_product != blank -%} {% endcomment %}
        {%- if request.page_type != 'password' and block.settings.addon_enabled == true and block.settings.addon_product != blank -%}

          {% comment %}
            Addon Checkbox
          {% endcomment %}

          {%- assign show_addon = true -%}

          {% if block.settings.addon_tag != blank %}
            {%- assign show_addon = false -%}
            {%- if product.tags contains block.settings.addon_tag -%}
              {%- assign show_addon = true -%}
            {%- endif -%}
          {% endif %}

          {%- if show_addon == true -%}

            {% comment %} {%- assign addon_has_options = false -%} {% endcomment %}
            {%- assign addon_has_options = false -%}
            {%- if block.settings.addon_has_options == true -%}
              {%- assign addon_has_options = true -%}
            {%- endif -%}

            {%- capture addon_name -%}
              {%- if block.settings.addon_name -%}
                {{ block.settings.addon_name }}
              {%- else -%}
                {{ block.settings.addon_product.title }}
              {%- endif -%}
            {%- endcapture -%}

            {%- assign addon_handle = addon_name | handle -%}

            {%- capture addon_id -%}{{ addon_handle }}--{{ block.id }}{%- endcapture -%}
            {%- capture addon_drawer_id -%}addon-{{ addon_id }}-drawer{%- endcapture -%}
            {%- capture addon_popover_id -%}addon-{{ addon_id }}-popover{%- endcapture -%}

            {%- capture addon_openable_attributes -%}{%- endcapture -%}

            {%- capture addon_checkbox_label -%}
              {%- if block.settings.addon_checkbox_label -%}
                {{ block.settings.addon_checkbox_label }}
              {%- else -%}
                {{ block.settings.addon_product.title }}
              {%- endif -%}
            {%- endcapture -%}

            {%- capture addon_unavailable_message -%}
              {%- if block.settings.addon_unavailable_message != blank -%}
                {{ block.settings.addon_unavailable_message }}
              {%- else -%}
                {{ 'product.form.sold_out' | t }}
              {%- endif -%}
            {%- endcapture -%}

            {%- assign addon_available = false -%}
            {%- if block.settings.addon_product.available == true -%}
              {%- assign addon_available = true -%}
            {%- endif -%}

            {% if block.settings.addon_disabled == true %}
              {%- assign addon_available = false -%}
            {% endif %}

            {%- capture addon_html -%}

              <div class="product-form__addon" {{ block.shopify_attributes }}>

                {%- if addon_has_options == true -%}

                  <product-addon class="product-addon checkbox-container hidden-phone" 
                    data-addon-id="{{ addon_id }}" 
                    data-addon-openable-id="{{ addon_drawer_id }}"
                    data-variant-id="{{ block.settings.addon_product.selected_or_first_available_variant.id }}"
                  >
                    <button class="checkbox" id="{{ addon_id }}-drawer-button" 
                      data-addon-checkbox-button
                      type="button" 
                      value="{{ block.settings.addon_product.title }}" 
                      {% if addon_available == false %}disabled{% endif %}></button>
                      <div class="product-addon__checkbox-label" {% if addon_available == false %}data-tooltip="{{ addon_unavailable_message }}"{% endif %}>
                        <label class="text--xsmall" for="{{ addon_id }}-drawer-button">
                          {{ addon_checkbox_label }} 
                          {% if block.settings.addon_show_price %}
                            <strong>+{{ block.settings.addon_product.price | money }}</strong>
                          {% endif %}
                        </label>
                        <button class="product-addon__details-link checkbox-label text--xsmall link text-right"
                          data-addon-details-button
                        >
                          {{ 'kyte.product.addons.edit_details' | t }}
                        </button>
                      </div>
                  </product-addon>

                  <product-addon class="product-addon checkbox-container hidden-tablet-and-up" 
                    data-addon-id="{{ addon_id }}"
                    data-addon-openable-id="{{ addon_popover_id }}"
                    data-variant-id="{{ block.settings.addon_product.selected_or_first_available_variant.id }}"
                  >
                    <button class="checkbox" id="{{ addon_id }}-popover-button" 
                      data-addon-checkbox-button
                      type="button" 
                      value="{{ block.settings.addon_product.title }}" 
                      {% if addon_available == false %}disabled{% endif %}></button>
                      <div class="product-addon__checkbox-label" {% if addon_available == false %}data-tooltip="{{ addon_unavailable_message }}"{% endif %}>
                        <label class="text--xsmall" for="{{ addon_id }}-popover-button">
                          {{ addon_checkbox_label }}
                          {% if block.settings.addon_show_price %}
                            <strong>+{{ block.settings.addon_product.price | money }}</strong>
                          {% endif %}
                        </label>
                        <button class="product-addon__details-link checkbox-label text--xsmall link text-right"
                          data-addon-details-button
                        >
                          {{ 'kyte.product.addons.edit_details' | t }}
                        </button>
                      </div>
                  </product-addon>

                  <product-addon-details data-addon-id="{{ addon_id }}" hidden>

                    {%- assign addon_attributes = "Embroidery Text, Font, Color" -%}
                    {%- assign addon_attributes_array = addon_attributes | split: "," -%}

                    {%- for addon_attribute in addon_attributes_array -%}
                      {%- assign addon_attribute_name = addon_attribute | strip -%}
                      {%- assign addon_attribute_handle = addon_attribute_name | handle -%}
                      <span 
                        addon-attribute="{{ addon_attribute_handle | handle }}" 
                        id="{{ addon_id }}--attribute-{{ addon_attribute_handle | handle }}" 
                        name="{{ addon_id }}--attribute-{{ addon_attribute_handle | handle }}" 
                        data-option-name="{{ addon_attribute_name }}"
                        data-option-value=""></span>
                    {%- endfor -%}

                  </product-addon-details>

                {%- else -%}

                  <product-addon class="product-addon checkbox-container" 
                    {% if addon_available == false %}data-tooltip="{{ addon_unavailable_message }}"{% endif %}
                    data-addon-id="{{ addon_id }}"
                    data-variant-id="{{ block.settings.addon_product.selected_or_first_available_variant.id }}"
                  >
                    <button class="checkbox" id="{{ addon_popover_id }}-button" 
                      data-add-product
                      data-addon-checkbox-button
                      type="button" 
                      value="{{ block.settings.addon_product.title }}" 
                      {% if addon_available == false %}disabled{% endif %} 
                    ></button>
                    <label class="text--xsmall" for="{{ addon_popover_id }}-button">
                      {{ addon_checkbox_label }} 
                      {% if block.settings.addon_show_price %}
                        <strong>+{{ block.settings.addon_product.price | money }}</strong>
                      {% endif %}
                    </label>
                  </product-addon>

                {%- endif -%}

              </div>

            {%- endcapture -%}

            {%- if addons != blank -%}
              {%- assign addons = addons | append: addon_html -%}
            {%- else -%}
              {%- assign addons = addon_html -%}
            {%- endif -%}

            {% comment %}
              Addon Drawers
            {% endcomment %}

            {%- capture addon_drawer_html -%}

              {%- if request.page_type != 'password' and product.available and block.settings.addon_enabled == true and block.settings.addon_product != blank -%}

                {%- if addon_has_options == true -%}

                  {%- capture addon_form -%}

                    {%- capture addon_option_id -%}{{ addon_id }}--embroidery-text{%- endcapture -%}
                    {%- capture addon_option_id_openable -%}{{ addon_option_id }}--$OPENABLETYPE{%- endcapture -%}

                      {% if block.settings.drawer_intro %}
                        <div class="note note--tertiary text--xsmall">
                          {{ block.settings.drawer_intro }}
                        </div>
                      {% endif %}

                      <product-addon-form class="product-addon-form" data-addon-id="{{ addon_id }}">

                        <form action="#" id="{{ addon_option_id_openable }}--options-form" hidden></form>

                        <addon-option class="product-addon-form__group addon-option">

                          {%- capture addon_option_name -%}
                            Embroidery Text
                          {%- endcapture -%}

                          {%- capture addon_option_description -%}
                            Enter the text you want embroidered on your item!
                          {%- endcapture -%}

                          <div class="addon-option__heading">
                            <span class="text--strong">{{ addon_option_name }}</span>
                            {% if block.settings.section_1_examples_drawer_id != blank %}
                              <button is="toggle-button" class="link text--subdued" aria-controls="{{ block.settings.section_1_examples_drawer_id }}" aria-expanded="false">See Examples</button>
                            {% endif %}
                          </div>

                          {%- if addon_option_description != blank -%}
                            <p class="addon-option__description text--xsmall">{{ addon_option_description }}</p>
                          {%- endif -%}

                          <div class="addon-option__input input">
                            <input id="{{ addon_option_id_openable }}" type="text" class="input__field input__field--text" name="{{ addon_option_id }}" minlength="1" maxlength="{{ block.settings.embroidery_max_characters }}" data-option-input data-option-name="{{ addon_option_name }}" required form="{{ addon_option_id_openable }}--options-form">
                            <label for="{{ addon_option_id_openable }}" class="input__label">Enter your Embroidery Text</label> 
                          </div>

                          <label for="{{ addon_option_id_openable }}" class="addon-option__hint text--xxsmall italic">1-{{ block.settings.embroidery_max_characters }} Characters</label>

                        </addon-option>

                        <addon-option class="product-addon-form__group addon-option">

                          {%- capture addon_option_name -%}
                            Font
                          {%- endcapture -%}

                          {%- capture addon_option_id -%}{{ addon_id }}--embroidery-font{%- endcapture -%}

                          {%- capture addon_option_description -%}
                            Choose a font for your personalized embroidery.
                          {%- endcapture -%}

                          {%- capture addon_values -%}
                            {{- block.settings.addon_option_2_values -}}
                          {%- endcapture -%}

                          {%- assign addon_values_array = addon_values | newline_to_br | split: "<br />" -%}

                          <div class="addon-option__heading">
                            <span class="text--strong">{{ addon_option_name }}</span>
                            {% if block.settings.section_2_examples_drawer_id != blank %}
                              <button is="toggle-button" class="link text--subdued" aria-controls="{{ block.settings.section_2_examples_drawer_id }}" aria-expanded="false">See Examples</button>
                            {% endif %}
                          </div>

                          {%- if addon_option_description != blank -%}
                            <p class="addon-option__description text--xsmall">{{ addon_option_description }}</p>
                          {%- endif -%}

                          <div class="addon-option__input input">
                            <div class="select-wrapper is-filled">
                              <select class="select" name="{{ addon_option_id }}" id="{{ addon_option_id_openable }}" data-option-input data-option-name="{{ addon_option_name }}" required form="{{ addon_option_id_openable }}--options-form">
                                <option value=""></option>
                                {% for value in addon_values_array %}
                                  <option value="{{ value | strip }}">{{ value | strip }}</option>
                                {% endfor %}
                              </select>
                              <svg focusable="false" width="12" height="8" class="icon icon--chevron   " viewBox="0 0 12 8">
                                <path fill="none" d="M1 1l5 5 5-5" stroke="currentColor" stroke-width="1"></path>
                              </svg>
                            </div>
                            <label for="{{ addon_option_id_openable }}" class="input__label">Select a Font</label>
                          </div>

                        </addon-option>

                        <addon-option class="product-addon-form__group addon-option">

                          {%- capture addon_option_name -%}
                            Color
                          {%- endcapture -%}

                          {%- capture addon_option_id -%}{{ addon_id }}--embroidery-colors{%- endcapture -%}

                          {%- capture addon_option_description -%}
                            We can embroider your item in any of these eye-catching colors.
                          {%- endcapture -%}

                          {%- capture addon_values -%}
                            {{- settings.embroidery_swatch_colors -}}
                          {%- endcapture -%}

                          <div class="addon-option__heading">
                            <span class="text--strong">{{ addon_option_name }}</span>
                            {% if block.settings.section_3_examples_drawer_id != blank %}
                              <button is="toggle-button" class="link text--subdued" aria-controls="{{ block.settings.section_3_examples_drawer_id }}" aria-expanded="false">See Examples</button>
                            {% endif %}
                          </div>

                          {%- if addon_option_description != blank -%}
                            <p class="addon-option__description text--xsmall">{{ addon_option_description }}</p>
                          {%- endif -%}

                          {%- assign lines = addon_values | newline_to_br | strip_newlines | split: '<br />' | sort -%}
                          {%- assign options = blank -%}
                          {%- for line in lines -%}
                            {%- assign line_split = line | split: ':' -%}
                            {%- assign swatch_name = line_split.first -%}
                            {%- assign swatch_value = line_split.last | strip -%}
                            {% capture options %}
                              {{ options }}
                              <option value="{{ swatch_name }}" data-swatch="{{ swatch_value }}">{{ swatch_name }}</option>
                            {% endcapture %}
                          {%- endfor -%}

                          <div class="addon-option__input input">
                            <div class="select-wrapper select-wrapper--color is-filled">
                              <select class="select" name="{{ addon_option_id }}" id="{{ addon_option_id_openable }}" data-option-input data-option-name="{{ addon_option_name }}" required form="{{ addon_option_id_openable }}--options-form">
                                {{ options }}
                                {%- comment -%}
                                  <option value=""></option>
                                  {% for value in addon_values_array %}
                                    <option value="{{ value | strip }}">{{ value | strip }}</option>
                                  {% endfor %}
                                {%- endcomment -%}
                              </select>
                              <svg focusable="false" width="12" height="8" class="icon icon--chevron   " viewBox="0 0 12 8">
                                <path fill="none" d="M1 1l5 5 5-5" stroke="currentColor" stroke-width="1"></path>
                              </svg>
                            </div>
                            <label for="{{ addon_option_id_openable }}" class="input__label">Select a Color</label>
                          </div>

                        </addon-option>

                        <div class="product-addon-form__footer">

                          <button type="submit" class="button button--primary button--full" form="{{ addon_option_id_openable }}--options-form">

                            <span class="loader-button__label">
                              Add Embroidery
                            </span>

                            <span class="product-item__prices-wrapper">
                              {{ block.settings.addon_product.price | money }}
                            </span>

                          </button>

                        </div>

                      </product-addon-form>

                  {%- endcapture -%}

                  {%- comment -%}Drawer is for tablet and desktop{%- endcomment -%}
                  <addon-drawer id="{{ addon_drawer_id }}" class="drawer drawer--large hidden-phone addon-{{ addon_id }}-openable">
                    <span class="drawer__overlay"></span>

                    <header class="drawer__header">
                      <p class="drawer__title heading h6 uppercase">
                        {%- if block.settings.drawer_title != blank -%}
                          {{- block.settings.drawer_title -}}
                        {%- else -%}
                          {{- addon_name -}}
                        {%- endif -%}
                      </p>

                      <button type="button" class="drawer__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
                        {%- render 'icon' with 'close' -%}
                      </button>
                    </header>

                    <div class="drawer__content drawer__content--padded-start">
                      {%- assign openable_content = addon_form | replace: "$OPENABLETYPE", "drawer" -%}
                      {{ openable_content }}
                    </div>
                  </addon-drawer>

                  {%- comment -%}Popover is for mobile{%- endcomment -%}
                  <addon-popover id="{{ addon_popover_id }}" class="popover hidden-lap-and-up addon-{{ addon_id }}-openable">
                    <span class="popover__overlay"></span>

                    <header class="popover__header">
                      <p class="popover__title heading h6 uppercase">
                        {{ addon_name }}
                      </p>

                      <button type="button" class="popover__close-button tap-area tap-area--large" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
                        {%- render 'icon' with 'close' -%}
                      </button>
                    </header>

                    <div class="popover__content">
                      <div class="rte">
                        {%- assign openable_content = addon_form | replace: "$OPENABLETYPE", "popover" -%}
                        {{ openable_content }}
                      </div>
                    </div>
                  </addon-popover>

                {%- endif -%}

              {%- endif -%}

            {%- endcapture -%}

            {%- if addon_drawers != blank -%}
              {%- assign addon_drawers = addon_drawers | append: addon_drawer_html -%}
            {%- else -%}
              {%- assign addon_drawers = addon_drawer_html -%}
            {%- endif -%}

            {%- comment -%}
              The "addon_drawers" HTML is printed to the page at the bottom of this file.
            {%- endcomment -%}
            
          {%- endif -%}

        {%- endif -%}

      {%- when 'quantity_atc' -%}

        {% comment %}
        ----------------------------------------------------------------------------------------------------------------
        QUANTITY + ATC
        ----------------------------------------------------------------------------------------------------------------
        hide_quantity_atc: false - Hides the Quantity Selector + Add to Cart button row.

        {% endcomment %}

        {%- if request.page_type != 'password' -%}

          <div class="quantity-atc {% if is_bundle == true %}quantity-atc--bundle{% endif %}" data-block-type="quantity-atc" data-block-id="{{ block.id }}">

          {% comment %} ----- Tiers -----{% endcomment %}
          
          {%- capture tier_welcome_banner -%}
  
            {%- if settings[tier_welcome_message_enable] == true -%}
  
              <div class="product-form-banner tier-welcome-message-banner">

                {%- if settings[tier_simple_icon] != blank -%}
                  {%- assign tier_icon = settings[tier_simple_icon] -%}
                {%- elsif settings[tier_icon] != blank -%}
                  {%- assign tier_icon = settings[tier_icon] -%}
                {%- endif -%}

                {% if tier_icon != blank %}
                  <div class="product-form-banner__icon tier-welcome-message-banner__icon">
                    {{- tier_icon | image_url: width: tier_icon.width | image_tag: loading: 'lazy', sizes: '(max-width: 740px) 64px, 72px', sizes: '64,72,128,144,192,216' -}}
                  </div>
                {% endif %}
  
                <div class="product-form-banner__content tier-welcome-message-banner__text">
                  
                  {% if settings[tier_welcome_message_title] %}
                    <h4 class="product-form-banner__heading no-margin heading heading--small">{{ settings[tier_welcome_message_title] }}</h4>
                  {% endif%}
  
                  {% if settings[tier_welcome_message_text] %}
                    <div class="product-form-banner__text">
                      {{ settings[tier_welcome_message_text] | replace: "<p", '<p class="text--xsmall"' }}
                    </div>
                  {% endif %}
                  
                  {% if settings[tier_welcome_message_cta_text] != "" %}
                    <div class="product-form-banner__actions">
                      <a target="_blank" class="link link--faded" href="{{ settings[tier_welcome_message_cta_link] }}">{{ settings[tier_welcome_message_cta_text] }}</a>
                    </div>
                  {% endif %}
  
                </div>
  
              </div>
  
            {%- endif -%}
              
          {%- endcapture -%}

          {% comment %} ----- Addons ----- {% endcomment %}

          {%- if addons != blank -%}

            <div class="product-form__addons loop--hide">

              {%- if section.settings.addon_show_title == true -%}
                <span class="heading heading--xsmall">{{ 'kyte.product.addons.addons' | t }}</span>
              {%- endif -%}

              <product-addon-container class="product-addon-container">
                {{ addons }}
              </product-addon-container>

            </div>

          {%- endif -%}

          {% comment %} ----- Preorders -----{% endcomment %}

          {%- if product_is_preorder == true and cart_contains_non_preorder == true and cart.items.size > 0 -%}
            <div class="note note--small note--danger">
              <span class="note__icon">{% render 'icon' with 'form-error' %}</span>
              <span class="note__text">{{ settings.pre_order_warning_message }}</span>
            </div>
          {% endif %}

          {%- if product_is_preorder == false and cart_contains_preorder == true and cart.items.size > 0 -%}
            <div class="note note--small note--danger">
              <span class="note__icon">{% render 'icon' with 'form-error' %}</span>
              <span class="note__text">{{ settings.non_pre_order_warning_message }}</span>
            </div>
          {% endif %}

          {% comment %} ----- Quantity and Add to Cart ----- {% endcomment %}

          <div id="MainPaymentContainer">

            {%- form 'product', product, is: 'product-form', id: product_form_id -%}

              {% comment %} Gift Card Recipient Fields {% endcomment %}

              {%- if product.gift_card? and block.settings.show_gift_card_recipient -%}
                {%- assign recipient_feature_active = true -%}
              {%- endif -%}

              {%- if recipient_feature_active -%}

                <div class="product-form__gift-card-recipient">

                  {%- assign message_maxlength = 198 -%}

                  <gift-card-recipient class="gift-card-recipient">
                    <div class="input input--checkbox">
                      <div class="checkbox-container">
                        <input type="checkbox" class="checkbox" name="properties[__shopify_send_gift_card_to_recipient]" id="product-{{ section.id }}-{{ product.id }}-send-gift-card-to-recipient">
                        <label for="product-{{ section.id }}-{{ product.id }}-send-gift-card-to-recipient" class="text--subdued">{{ 'gift_card.recipient.checkbox' | t }}</label>
                      </div>
                    </div>

                    <div class="gift-card-recipient__fields js:hidden">
                      <div class="input">
                        <input id="product-{{ section.id }}-{{ product.id }}-recipient-email" type="email" class="input__field input__field--text" name="properties[Recipient email]" required value="{{ form.email }}">
                        <label for="product-{{ section.id }}-{{ product.id }}-recipient-email" class="input__label">{{ 'gift_card.recipient.email_label' | t }}</label>
                      </div>

                      <div class="input">
                        <input id="product-{{ section.id }}-{{ product.id }}-recipient-name" type="text" class="input__field input__field--text" name="properties[Recipient name]" value="{{ form.name }}">
                        <label for="product-{{ section.id }}-{{ product.id }}-recipient-name" class="input__label">{{ 'gift_card.recipient.name_label' | t }}</label>
                      </div>

                      <div class="input">
                        <input type="hidden" name="properties[__shopify_offset]" value="" disabled>
                        <input id="product-{{ section.id }}-{{ product.id }}-send-on" type="date" class="input__field input__field--text" pattern="\d{4}-\d{2}-\d{2}" name="properties[Send on]" value="{{ form.send_on }}">
                        <label for="product-{{ section.id }}-{{ product.id }}-send-on" class="input__label">{{ 'gift_card.recipient.send_on_label' | t }}</label>
                      </div>

                      <div class="input">
                        <textarea id="product-{{ section.id }}-{{ product.id }}-recipient-message" rows="4" class="input__field input__field--textarea" name="properties[Message]" maxlength="{{ message_maxlength }}">{{ form.message }}</textarea>
                        <label for="product-{{ section.id }}-{{ product.id }}-recipient-message" class="input__label">{{ 'gift_card.recipient.message_label' | t }}</label>
                        <div class="hint">Our maximum message length is {{ message_maxlength }} characters.</div>
                      </div>

                    </div>
                  </gift-card-recipient>

                </div>

              {%- endif -%}

              {% comment %} Quantity Selector + ATC {% endcomment %}

              <div class="quantity-selector-atc-container {% if product.tags contains "Not For Sale" %}hidden{% endif %}" {{ block.shopify_attributes }}>

                <input type="hidden" class="pid" value="{{product.id}}">
                <input type="hidden" class="ptitle" value="{{product.title | remove: "'" | remove: '"'}}">
                <input type="hidden" class="pprice" value="{{product.variants.first.price | times: 0.01}}">
                <input type="hidden" class="pbrand" value="{{product.vendor | remove: "'" | remove: '"'}}">
                <input type="hidden" class="ptype" value="{{product.type | remove: "'" | remove: '"'}}">
                <input type="hidden" class="pcategory_id" value="{{ product.collections.first.id }}">
                <input type="hidden" class="pcategory_name" value="{{ product.collections.first.title | remove: "'" | remove: '"' }}">
                <input type="hidden" class="pvtitle" value="{{ product.variants.first.title | remove: "'" | remove: '"' }}">
                <input type="hidden" class="psku" value="{{ product.variants.first.sku }}">
                {% comment %} Awtomatic Subscriptions {% endcomment %}
                {% capture awtomatic-plan-selector %}{% render 'awtomatic-plan-selector', product: product %}{% endcapture %}
                {% unless awtomatic-plan-selector contains "Liquid error" %}
                    {{ awtomatic-plan-selector }}
                {% endunless %}

                <div class="quantity-selector-atc {% if unavailable_for_returns %}loop--hide{% endif %} {% if hide_quantity_atc == true %}hidden{% endif %}">

                  <input type="hidden" disabled name="id" value="{{ product.selected_or_first_available_variant.id }}">

                  <quantity-selector class="quantity-selector quantity-selector-atc__qty">
                    <button type="button" class="quantity-selector__button button button--tertiary">
                      <span class="visually-hidden">{{ 'cart.general.decrease_quantity' | t }}</span>
                      {%- render 'icon' with 'minus-big' -%}
                    </button>

                    <input type="text" form="{{ product_form_id }}" is="input-number" class="quantity-selector__input" inputmode="numeric" name="quantity" autocomplete="off" min="1" {% if max_allowed_quantity != "" %}max="{{ max_allowed_quantity }}"{% endif %} value="1" size="2" aria-label="{{ 'product.form.quantity' | t | escape }}">

                    <button type="button" class="quantity-selector__button button button--tertiary">
                      <span class="visually-hidden">{{ 'cart.general.increase_quantity' | t }}</span>
                      {%- render 'icon' with 'plus-big' -%}
                    </button>
                  </quantity-selector>

                  {% comment %} Add to Cart Settings {% endcomment %}

                  {% capture atc_button_label %}
                    {%- if product.selected_or_first_available_variant.available -%}
                      {%- if product.template_suffix == 'pre-order' -%}
                        {{- 'product.form.pre_order' | t -}}
                      {%- else -%}
                        {{- 'product.form.add_to_cart' | t -}}
                      {%- endif -%}
                    {%- else -%}
                      {{- 'product.form.sold_out' | t -}}
                    {%- endif -%}
                  {% endcapture %}

                  {% assign disable_atc = false %}

                  {% unless product.selected_or_first_available_variant.available %}
                    {% assign disable_atc = true %}
                  {% endunless %}

                  {%- if sale_stop != blank and sale_stop.enable == true -%}
                    {%- if sale_stop.atc_label != blank -%}
                      {% assign atc_button_label = sale_stop.atc_label %}
                    {%- endif -%}
                  {%- endif -%}

                  {%- if product_is_preorder == false and cart_contains_preorder == true -%}
                    {% assign disable_atc = true %}
                  {%- endif -%}

                  {% if product_has_tier_tag and settings[tier_disable_atc] == true %}
                    {%- assign disable_atc = true -%}
                  {% endif %}

                  {% if product_has_tier_tag == true and customer_has_tier_tag != true %}
                    {%- assign disable_atc = true -%}
                    
                    {%- if settings[tier_atc_disabled_label] != blank -%}
                      {% assign atc_button_label = settings[tier_atc_disabled_label] %}
                      {% assign hide_price = true %}
                    {%- endif -%}

                    {%- assign hide_wishlist = true -%}
                    {%- assign hide_registry = true -%}

                  {% endif %}

                  {% if sale_stop != blank and sale_stop.enable == true %}
                    {%- assign disable_atc = true -%}
                  {% endif %}

                  {% comment %} Add to Cart {% endcomment %}

                  <product-payment-container 
                    {% if update_url %}id="MainPaymentContainer"{% endif %}
                    form-id="{{ product_form_id }}" 
                    class="product-form__payment-container" 
                    price-class="" 
                    {% if product_is_preorder == false and cart_contains_preorder == true %}data-disable-for-preorder{% endif %}
                    {% if product_is_preorder == true %}data-is-preorder{% else %}data-is-non-preorder{% endif %}
                    {% if max_allowed_quantity != blank %}data-max-allowed-quantity="{{ max_allowed_quantity }}"{% endif %}
                    {{ block.shopify_attributes }}>

                    <button id="AddToCart"
                      type="submit"
                      is="loader-button" data-use-primary data-product-add-to-cart-button data-button-content="{% if product.template_suffix == 'pre-order' %}{{ 'product.form.pre_order' | t | escape }}{% else %}{{ 'product.form.add_to_cart' | t | escape }}{% endif %}"
                      class="
                        product-form__add-button
                        button
                        {% unless product.selected_or_first_available_variant.available %}button--ternary{% else %}button--primary{% endunless %}
                        button--full
                        {% if product.selected_or_first_available_variant.available == false %}button--out-of-stock{% endif %}
                      "

                      {%- if disable_atc == true -%}
                        disabled
                      {%- endif -%}

                      >

                      {% render 'icon', icon: 'custom-cart' %}

                      <span class="loader-button__label" data-product-add-to-cart-button-label>
                        {{ atc_button_label }}
                      </span>

                      {%- if price_position != 'default' and hide_price != true -%}
                        <span class="product-item__prices-wrapper" data-product-price-list>
                          {{ price_display }}
                        </span>
                      {%- endif -%}

                    </button>

                  </product-payment-container>

                </div>

                {% comment %} ----- Wishlist + Registry ----- {% endcomment %}

                {%- if block.settings.show_wishlist_button == true or block.settings.show_registry_button == true -%}
                  <div class="product-form__wishlist-buttons {% if hide_wishlist == true and hide_registry == true %} hidden {% endif %} {% if unavailable_for_returns %}loop--hide{% endif %}">

                    {%- if block.settings.show_wishlist_button -%}
                      <div class="swym-wishlist-button-bar {% if hide_wishlist == true %}hidden{% endif %}"></div>
                    {%- endif -%}

                    {%- if block.settings.show_registry_button -%}
                      <div class="registry-button-container {% if hide_registry == true %}hidden{% endif %}"></div>
                    {%- endif -%}

                  </div>
                {%- endif -%}

                {% comment %} ----- Tier Restricted Banner / Sale Stop Banner ----- {% endcomment %}

                {% if product_has_tier_tag == true and customer_has_tier_tag != true %}

                  {% comment %} {%- assign hide_quantity_atc = true -%} {% endcomment %}

                  {%- if settings[tier_simple_icon] != blank -%}
                    {%- assign product_form_icon = settings[tier_simple_icon] -%}
                  {%- elsif settings[tier_icon] != blank -%}
                    {%- assign product_form_icon = settings[tier_icon] -%}
                  {%- endif -%}

                  {%- capture product_form_banner_heading -%}
                    {{- settings[tier_message_heading] -}}
                  {%- endcapture -%}

                  {%- capture product_form_banner_text -%}
                    {{- settings[tier_message_text] -}}
                  {%- endcapture -%}

                  {%- capture product_form_banner_button_text -%}
                    {{- settings[tier_cta_text] -}}
                  {%- endcapture -%}

                  {%- capture product_form_banner_button_link -%}
                    {{- settings[tier_cta_link] -}}
                  {%- endcapture -%}

                  <div class="product-form-banner product-form__banner">

                    {% if product_form_icon != blank %}
                      <div class="product-form-banner__icon">
                        {{- product_form_icon | image_url: width: product_form_icon.width | image_tag: loading: 'lazy', sizes: '(max-width: 740px) 64px, 72px', sizes: '64,72,128,144,192,216', class: 'product-form-banner__icon-image' -}}
                      </div>
                    {% endif %}

                    <div class="product-form-banner__content minor">

                      {%- if product_form_banner_heading != blank -%}
                        <h4 class="product-form-banner__heading no-margin heading heading--small">{{ product_form_banner_heading }}</h4>
                      {%- endif -%}

                      {%- if product_form_banner_text != blank -%}
                        <div class="product-form-banner__text">{{ product_form_banner_text }}</div>
                      {%- endif -%}

                      {%- if product_form_banner_button_text != blank and product_form_banner_button_link != blank -%}
                        <div class="product-form-banner__actions">
                          <a href="{{ product_form_banner_button_link }}" target="_blank" class="link link--faded">{{ product_form_banner_button_text }}</a>
                        </div>
                      {%- endif -%}

                    </div>

                  </div>

                {% elsif sale_stop != blank and sale_stop.enable == true %}

                  {% comment %} {%- assign hide_quantity_atc = true -%} {% endcomment %}

                  {%- if sale_stop.icon != blank -%}
                    {%- assign product_form_icon = sale_stop.icon -%}
                  {%- endif -%}

                  {%- capture product_form_banner_heading -%}
                    {{- sale_stop.title | metafield_tag -}}
                  {%- endcapture -%}

                  {%- capture product_form_banner_text -%}
                    {{- sale_stop.message | metafield_tag -}}
                  {%- endcapture -%}

                  {%- capture product_form_banner_button_link -%}
                    {{- sale_stop.message_button_link -}}
                  {%- endcapture -%}

                  <div class="product-form-banner sale-stop-banner">

                    {% if product_form_icon != blank %}
                      <div class="product-form-banner__icon">
                        {{- product_form_icon | image_url: width: product_form_icon.width | image_tag: loading: 'lazy', sizes: '(max-width: 740px) 64px, 72px', sizes: '64,72,128,144,192,216', class: 'product-form-banner__icon-image' -}}
                      </div>
                    {% endif %}

                    <div class="product-form-banner__content minor">

                      {%- if product_form_banner_heading != blank -%}
                        <h4 class="product-form-banner__heading no-margin heading heading--small">{{ product_form_banner_heading }}</h4>
                      {%- endif -%}

                      {%- if product_form_banner_text != blank -%}
                        <div class="product-form-banner__text">{{ product_form_banner_text }}</div>
                      {%- endif -%}

                      {%- if sale_stop.message_button_link.value.text != blank and sale_stop.message_button_link.value.url != blank -%}
                        <div class="product-form-banner__actions">
                          <a href="{{ sale_stop.message_button_link.value.url }}" target="_blank" class="link link--faded">{{ sale_stop.message_button_link.value.text }}</a>
                        </div>
                      {%- endif -%}

                    </div>

                  </div>

                {% endif %}

                {% comment %} ----- Tier Welcome Banner ----- {% endcomment %}

                {% if product_has_tier_tag == true and customer_has_tier_tag == true %}
                  {{ tier_welcome_banner }}
                {% endif %}

                {% comment %} ----- Returns ----- {% endcomment %}

                {% if unavailable_for_returns %}

                  <div class="product-form-banner product-form-banner--danger product-form__banner loop--display-flex">
                    <div class="product-form-banner__icon">
                      {% render 'icon', icon: 'custom-returns' %}
                    </div>
                    <div class="product-form-banner__content">
                      <div class="product-form-banner__text text--xsmall">
                        <p class="heading h5 margin-bottom-10">{{ 'returns.no_exchanges_title' | t }}</p>
                        <p>{{ 'returns.no_exchanges_text' | t }}</p>
                      </div>
                    </div> 
                  </div>

                {% endif %}

              </div>

            {%- endform -%}

          </div>

          </div>

        {%- endif -%}

      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      PRODUCT DESCRIPTION (This has been moved to the product.info snippet for Kyte)
      ----------------------------------------------------------------------------------------------------------------

      {%- when 'description' -%}
        {%- if product.description != blank and context != 'quick-buy' -%}
          <div class="product-form__description rte" {{ block.shopify_attributes }}>
            {{- product.description -}}
          </div>
        {%- endif -%}

      {%- endcomment -%}
      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      USPs
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}

      {%- when 'tag_list' -%}

        <div class="product-form__tag-list" data-block-type="tag-list" data-block-id="{{ block.id }}" {{ block.shopify_attributes }}>

          {%- if block.settings.title -%}
            <p class="product-usps-title {{ block.settings.title_style }} text-center">
              {{ block.settings.title }}
            </p>
          {%- endif -%}

          {%- capture tag_labels -%}
            
            {%- if block.settings.tag_list != blank -%}

              {%- assign tag_list_array = block.settings.tag_list.tags.value -%}
              {%- for tag in tag_list_array -%}
                <span class="label {{ block.settings.label_style }}">{{ tag }}</span>
              {%- endfor -%}

            {%- elsif block.settings.tag_list_text != blank -%}

              {%- assign tag_list_array = block.settings.tag_list_text | newline_to_br | split: '<br />' -%}
              {%- for tag in tag_list_array -%}
                <span class="label {{ block.settings.label_style }}">{{ tag }}</span>
              {%- endfor -%}

            {%- endif -%}

          {%- endcapture -%}

          <div class="product-form__tag-list-list label-list label-list--flex align-items-start justify-content-center">
            {{- tag_labels -}}
          </div>

        </div>


      {%- when 'usps' -%}

      <native-carousel class="product-media-banner-carousel hidden-lap-and-up" {{ block.shopify_attributes }}>

        <div class="product-media-banner-carousel__list hide-scrollbar">

          {%- for i in (1..3) -%}

            {%- capture media_banner_content -%}

              {%- capture media_banner_icon_setting -%}media_banner_{{ i }}_icon {%- endcapture -%}
              {%- capture media_banner_title_setting -%}media_banner_{{ i }}_title {%- endcapture -%}
              {%- capture media_banner_text_setting -%}media_banner_{{ i }}_text {%- endcapture -%}
              {%- capture media_banner_link_setting -%}media_banner_{{ i }}_link {%- endcapture -%}

              {% if section.settings[media_banner_icon_setting] != blank %}
                <div class="product-media-banner__icon">
                  {{ section.settings[media_banner_icon_setting] }}
                </div>
              {% endif %}

              {% if section.settings[media_banner_title_setting] != blank %}
                <div class="product-media-banner__title heading h5">
                  {{ section.settings[media_banner_title_setting] }}
                </div>
              {% endif %}

              {% if section.settings[media_banner_text_setting] != blank %}
                <div class="product-media-banner__text text text--xsmall text--subdued">
                  {{ section.settings[media_banner_text_setting] }}
                </div>
              {% endif %}

              {% if section.settings[media_banner_link_setting] != blank %}
                <div class="product-media-banner__link text text--xsmall icon-text link">
                  {{ section.settings[media_banner_link_setting] }}
                </div>
              {% endif %}

            {%- endcapture -%}

            {%- if media_banner_content != blank -%}
              <native-carousel-item {% unless forloop.first %}hidden{% endunless %} id="{{ section.id }}--{{ block.id }}--usp--{{ i }}" class="product-media-banner-carousel__item" {{ block.shopify_attributes }}>
                <button class="product-media-banner" is="toggle-button" aria-controls="product-media-banner-{{ i }}-drawer" aria-expanded="false">
                  {{ media_banner_content }}
                </button>
              </native-carousel-item>
            {%- endif -%}
            
          {%- endfor -%}

        </div>

        {%- if section.blocks.size > 1 -%}
          <page-dots class="dots-nav dots-nav--centered hidden-lap-and-up">
            {%- for i in (1..3) -%}
              <button class="dots-nav__item tap-area" {% if forloop.first %}aria-current="true"{% endif%}>
                <span class="visually-hidden">{{ 'general.accessibility.go_to_slide' | t: num: forloop.index }}</span>
              </button>
            {%- endfor -%}
          </page-dots>
        {%- endif -%}

      </native-carousel>

      <div class="product-usps-container" {{ block.shopify_attributes }}>

        <h4 class="product-usps-title {{ block.settings.title_style }} text-center">
          {{ block.settings.title }}
        </h4>

        <div class="product-usps {% if block.settings.images_only == true %}product-usps--images-only{% endif %}">

          {%- for i in (1..3) -%}
            
            {%- capture product_usps_content -%}
  
              {%- capture usp_icon_image_setting -%}usp_{{ i }}_image {%- endcapture -%}
              {%- capture usp_icon_svg_setting -%}usp_{{ i }}_liquid {%- endcapture -%}
              {%- capture usp_title_setting -%}usp_{{ i }}_title {%- endcapture -%}

              {%- assign usp_title = block.settings[usp_title_setting] -%}
              {%- assign usp_icon_image = block.settings[usp_icon_image_setting] -%}
              {%- assign usp_icon_svg = block.settings[usp_icon_svg_setting] -%}

              {%- if usp_title != blank -%}
                {%- assign usp_alt = usp_title -%}
              {%- else -%}
                {%- assign usp_alt = usp_icon_image.alt -%}
              {%- endif -%}

              {%- if usp_title != blank or usp_icon_image != blank or usp_icon_svg != blank -%}

                {%- capture icon_content -%}
                  {%- if usp_icon_svg != blank -%}
                    {{ usp_icon_svg }}
                    <span class="visually-hidden">{{ usp_alt }}</span>
                  {%- elsif usp_icon_image != blank -%}
                    {{ usp_icon_image | image_url: width: 200 | image_tag: sizes: '200px', widths: '200,300,400', width: 50, height: 50, alt: usp_alt }}
                  {%- endif -%}
                {%- endcapture -%}

                {%- if icon_content != blank -%}
                  <div class="product-usp__image major">
                    {{- icon_content -}}
                  </div>
                {%- endif -%}

                {%- if usp_title != blank and block.settings.images_only != true -%}
                  <p class="product-usp__title">
                    {{- usp_title -}}
                  </p>
                {%- endif -%}
  
              {%- endif -%}
  
            {%- endcapture -%}

            {%- if product_usps_content != blank -%}
              <div class="product-usp {% if usp_title == blank %}product-usp--image-only{% endif %}">
                {{ product_usps_content }}
              </div>
            {%- endif -%}
            
          {%- endfor -%}

        </div>

      </div>

      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      INVENTORY
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      {%- when 'inventory' -%}
        {%- if product.template_suffix != 'pre-order' -%}
          <div class="product-form__inventory-block" data-block-type="inventory" data-block-id="{{ block.id }}" {{ block.shopify_attributes }}>
            
            {%- if product.selected_or_first_available_variant.available or product.selected_or_first_available_variant.incoming -%}

              {% comment %} Inventory {% endcomment %}

              <product-inventory class="product-form__inventory-wrapper">
                {%- if product.selected_or_first_available_variant.available -%}
                  {%- if product.selected_or_first_available_variant.inventory_management and product.selected_or_first_available_variant.inventory_policy == 'deny' and product.selected_or_first_available_variant.inventory_quantity <= block.settings.low_inventory_threshold and block.settings.low_inventory_threshold > 0 -%}
                    <span class="inventory inventory--low">{{ 'product.form.low_stock_with_quantity_count' | t: count: product.selected_or_first_available_variant.inventory_quantity }}</span>
                  {%- else -%}
                    {%- if product.selected_or_first_available_variant.inventory_policy == 'continue' and product.selected_or_first_available_variant.inventory_quantity <= 0 and product.selected_or_first_available_variant.requires_shipping -%}
                      {%- if product.selected_or_first_available_variant.incoming and product.selected_or_first_available_variant.next_incoming_date -%}
                        {%- capture next_incoming_date -%}{{ product.selected_or_first_available_variant.next_incoming_date | date: format: 'date' }}{%- endcapture -%}
                        <span class="inventory inventory--low">{{ 'product.form.incoming_stock' | t: next_incoming_date: next_incoming_date }}</span>
                      {%- else -%}
                        <span class="inventory inventory--low">{{ 'product.form.oversell_stock' | t }}</span>
                      {%- endif -%}
                    {%- else -%}
                      <span class="inventory inventory--high">{{ 'product.form.in_stock' | t }}</span>
                    {%- endif -%}
                  {%- endif -%}
                {%- elsif product.selected_or_first_available_variant.incoming -%}
                  {%- if product.selected_or_first_available_variant.next_incoming_date -%}
                    {%- capture next_incoming_date -%}{{ product.selected_or_first_available_variant.next_incoming_date | date: format: 'date' }}{%- endcapture -%}
                    <span class="inventory inventory--low">{{ 'product.form.incoming_stock' | t: next_incoming_date: next_incoming_date }}</span>
                  {%- else -%}
                    <span class="inventory inventory--low">{{ 'product.form.oversell_stock' | t }}</span>
                  {%- endif -%}
                {%- endif -%}
              </product-inventory>

              {% comment %} Delivery Estimate {% endcomment %}

              {%- if block.settings.delivery_estimate_enable and block.settings.delivery_estimate_text != blank and block.settings.delivery_estimate_tooltip -%}
                
                <div class="product-form__delivery-estimate">

                  {% capture delivery_estimate_tooltip %}
                    {{- block.settings.delivery_estimate_tooltip -}}
                  {% endcapture %}

                  <delivery-estimate class="delivery-estimate">

                    <div class="delivery-estimate__text">
                      
                      <button class="delivery-estimate__button link text--subdued text--xsmall" type="button">{{ block.settings.delivery_estimate_text }}</button>

                    </div>

                    <div class="delivery-estimate__tooltip">
                      {{ delivery_estimate_tooltip }}
                    </div>

                  </delivery-estimate>

                </div>

              {%- endif -%}

            {%- endif -%}

          </div>
        {%- endif -%}

      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      BUY BUTTONS
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      {%- when 'buy_buttons' -%}
        {%- if request.page_type != 'password' -%}
          {%- assign recipient_feature_active = false -%}

          {%- if product.gift_card? and block.settings.show_gift_card_recipient -%}
            {%- assign recipient_feature_active = true -%}
          {%- endif -%}

          <div data-block-type="buy-buttons" data-block-id="{{ block.id }}" class="product-form__buy-buttons" {{ block.shopify_attributes }}>
            {%- form 'product', product, is: 'product-form', id: product_form_id -%}
              {%- if recipient_feature_active -%}
                <gift-card-recipient class="gift-card-recipient">
                  <div class="input input--checkbox">
                    <div class="checkbox-container">
                      <input type="checkbox" class="checkbox" name="properties[__shopify_send_gift_card_to_recipient]" id="product-{{ section.id }}-{{ product.id }}-send-gift-card-to-recipient">
                      <label for="product-{{ section.id }}-{{ product.id }}-send-gift-card-to-recipient" class="text--subdued">{{ 'gift_card.recipient.checkbox' | t }}</label>
                    </div>
                  </div>

                  <div class="gift-card-recipient__fields js:hidden">
                    <div class="input">
                      <input id="product-{{ section.id }}-{{ product.id }}-recipient-email" type="email" class="input__field input__field--text" name="properties[Recipient email]" required value="{{ form.email }}">
                      <label for="product-{{ section.id }}-{{ product.id }}-recipient-email" class="input__label">{{ 'gift_card.recipient.email_label' | t }}</label>
                    </div>

                    <div class="input">
                      <input id="product-{{ section.id }}-{{ product.id }}-recipient-name" type="text" class="input__field input__field--text" name="properties[Recipient name]" value="{{ form.name }}">
                      <label for="product-{{ section.id }}-{{ product.id }}-recipient-name" class="input__label">{{ 'gift_card.recipient.name_label' | t }}</label>
                    </div>

                    <div class="input">
                      <input type="hidden" name="properties[__shopify_offset]" value="" disabled>
                      <input id="product-{{ section.id }}-{{ product.id }}-send-on" type="date" class="input__field input__field--text" pattern="\d{4}-\d{2}-\d{2}" name="properties[Send on]" value="{{ form.send_on }}">
                      <label for="product-{{ section.id }}-{{ product.id }}-send-on" class="input__label">{{ 'gift_card.recipient.send_on_label' | t }}</label>
                    </div>

                    <div class="input">
                      <textarea id="product-{{ section.id }}-{{ product.id }}-recipient-message" rows="4" class="input__field input__field--textarea" name="properties[Message]">{{ form.message }}</textarea>
                      <label for="product-{{ section.id }}-{{ product.id }}-recipient-message" class="input__label">{{ 'gift_card.recipient.message_label' | t }}</label>
                    </div>
                  </div>
                </gift-card-recipient>
              {%- endif -%}

              <input type="hidden" disabled name="id" value="{{ product.selected_or_first_available_variant.id }}">

              <product-payment-container {% if update_url %}id="MainPaymentContainer"{% endif %} class="product-form__payment-container" {{ block.shopify_attributes }}>
                <button id="AddToCart" type="submit" is="loader-button" {% unless block.settings.show_payment_button and template.suffix != 'quick-buy-popover' %}data-use-primary{% endunless %} data-product-add-to-cart-button data-button-content="{% if product.template_suffix == 'pre-order' %}{{ 'product.form.pre_order' | t | escape }}{% else %}{{ 'product.form.add_to_cart' | t | escape }}{% endif %}" class="product-form__add-button button {% unless product.selected_or_first_available_variant.available %}button--ternary{% else %}{% if block.settings.show_payment_button and template.suffix != 'quick-buy-popover' %}button--secondary{% else %}button--primary{% endif %}{% endunless %} button--full" {% unless product.selected_or_first_available_variant.available %}disabled{% endunless %}>
                  {%- if product.selected_or_first_available_variant == nil -%}
                    {{- 'product.form.unavailable' | t -}}
                  {%- elsif product.selected_or_first_available_variant.available -%}
                    {%- if product.template_suffix == 'pre-order' -%}
                      {{- 'product.form.pre_order' | t -}}
                    {%- else -%}
                      {{- 'product.form.add_to_cart' | t -}}
                    {%- endif -%}
                  {%- else -%}
                    {{- 'product.form.sold_out' | t -}}
                  {%- endif -%}
                </button>

                {%- if block.settings.show_payment_button and recipient_feature_active == false and template.suffix != 'quick-buy-popover' -%}
                  {{ form | payment_button }}

                  {%- unless product.selected_or_first_available_variant.available -%}
                    <style>
                      #shopify-section-{{ section.id }} .shopify-payment-button {
                        display: none;
                      }
                    </style>
                  {%- endunless -%}
                {%- endif -%}
              </product-payment-container>
            {%- endform -%}
          </div>
        {%- endif -%}

        {%- if context != 'quick-buy' -%}
          <store-pickup data-block-type="pickup-availability" data-block-id="{{ block.id }}" class="product-form__store-availability-container">
            {%- render 'store-availability', product_variant: product.selected_or_first_available_variant -%}
          </store-pickup>
        {%- endif -%}
    {%- endcase -%}
  {%- endfor -%}

  {%- comment -%}
  IMPLEMENTATION NOTE: under rare circumstances, merchant may want to show selectors but hide the add to cart button. This
  is however problematic as elements changed based on this. So if we detect there is no buy buttons block, we add an empty one
  {%- endcomment -%}

  {%- assign buy_buttons_block = section.blocks | where: 'type', 'buy_buttons' | first -%}
  {%- assign quantity_atc_block = section.blocks | where: 'type', 'quantity_atc' | first -%}

  {%- if buy_buttons_block == blank and quantity_atc_block == blank -%}
    {%- form 'product', product, is: 'product-form', id: product_form_id -%}
      <input type="hidden" disabled name="id" value="{{ product.selected_or_first_available_variant.id }}">
    {%- endform -%}
  {%- endif -%}
</div>

{%- comment -%}
IMPLEMENTATION NOTE: if during the iteration of the options we have found an option matching a size chart, we add it here
{%- endcomment -%}

{%- if found_size_option and size_chart_page != blank -%}
  {%- comment -%}Drawer is for tablet and desktop{%- endcomment -%}
  <drawer-content id="product-{{ section.id }}-{{ product.id }}-size-chart-drawer" class="drawer drawer--large hidden-phone">
    <span class="drawer__overlay"></span>

    <header class="drawer__header">
      <p class="drawer__title heading h6">{{ size_chart_page.title }}</p>

      <button type="button" class="drawer__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
        {%- render 'icon' with 'close' -%}
      </button>
    </header>

    <div class="drawer__content drawer__content--padded-start">
      <div class="rte">
        {{- size_chart_page.content -}}
      </div>
    </div>
  </drawer-content>

  {%- comment -%}Popover is for mobile{%- endcomment -%}
  <popover-content id="product-{{ section.id }}-{{ product.id }}-size-chart-popover" class="popover hidden-lap-and-up">
    <span class="popover__overlay"></span>

    <header class="popover__header">
      <p class="popover__title heading h6">{{ size_chart_page.title }}</p>

      <button type="button" class="popover__close-button tap-area tap-area--large" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
        {%- render 'icon' with 'close' -%}
      </button>
    </header>

    <div class="popover__content">
      <div class="rte">
        {{- size_chart_page.content -}}
      </div>
    </div>
  </popover-content>
{%- endif -%}

<!-- TOG -->
{%- if tog_rating_page != blank -%}
  {%- comment -%}Drawer is for tablet and desktop{%- endcomment -%}
  <drawer-content id="product-{{ section.id }}-{{ product.id }}-tog-rating-drawer" class="drawer drawer--large hidden-phone">
    <span class="drawer__overlay"></span>

    <header class="drawer__header">
      <p class="drawer__title heading h6">{{ tog_rating_page.title }}</p>

      <button type="button" class="drawer__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
        {%- render 'icon' with 'close' -%}
      </button>
    </header>

    <div class="drawer__content drawer__content--padded-start">
      <div class="rte">
        {{- tog_rating_page.content -}}
      </div>
    </div>
  </drawer-content>

  {%- comment -%}Popover is for mobile{%- endcomment -%}
  <popover-content id="product-{{ section.id }}-{{ product.id }}-tog-rating-popover" class="popover hidden-lap-and-up">
    <span class="popover__overlay"></span>

    <header class="popover__header">
      <p class="popover__title heading h6">{{ tog_rating_page.title }}</p>

      <button type="button" class="popover__close-button tap-area tap-area--large" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
        {%- render 'icon' with 'close' -%}
      </button>
    </header>

    <div class="popover__content">
      <div class="rte">
        {{- tog_rating_page.content -}}
      </div>
    </div>
  </popover-content>
{%- endif -%}

{%- if addon_drawers != blank -%}
  {{ addon_drawers }}
{%- endif -%}