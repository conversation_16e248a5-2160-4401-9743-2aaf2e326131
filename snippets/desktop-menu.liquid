<desktop-navigation>
  <ul class="header__linklist list--unstyled {% unless bottom_navigation %}hidden-pocket hidden-lap{% endunless %}" role="list">
    {%- for link in menu.links -%}
      {%- assign link_title_downcase = link.title | strip | downcase -%}
      {%- assign mega_menu_block = '' -%}
      {%- assign mega_menu_images = '' -%}

      {%- for block in section.blocks -%}
        {%- assign menu_item_downcase = block.settings.menu_item | strip | downcase -%}

        {%- if menu_item_downcase == link_title_downcase -%}
          {%- assign mega_menu_block = block -%}
          {%- break -%}
        {%- endif -%}
      {%- endfor -%}

      <li class="header__linklist-item {% if link.links.size > 0 or mega_menu_block != '' %}has-dropdown{% endif %}" data-item-title="{{ link.title | escape }}">
        <a class="header__linklist-link link--animated" {% render 'link-attributes', link: link %} href="{{ link.url }}" {% if link.links.size > 0 or mega_menu_block != '' %}aria-controls="desktop-menu-{{ forloop.index }}" aria-expanded="false"{% endif %}>
          {{- link.title -}}
        </a>

        {%- if mega_menu_block != '' -%}
          {%- assign images_count = 0 -%}

          {%- if mega_menu_block.type == "mega_menu_products" -%}

            {%- capture mega_menu_products -%}

              {%- assign products_count = 0 -%}

              {%- for i in (1..6) -%}
                
                {%- capture product_setting -%}product_{{ i }}{%- endcapture -%}
                {%- capture hide_market -%}product_{{ i }}_hide_market{%- endcapture -%}

                {%- if mega_menu_block.settings[hide_market] contains localization.country.iso_code -%}
                  {%- continue -%}
                {%- endif -%}

                {%- assign product = mega_menu_block.settings[product_setting] -%}
                
                {%- if product != blank -%}
                  
                  {%- assign products_count = products_count | plus: 1 -%}

                  {%- assign image_heading_style = mega_menu_block.settings.image_heading_style -%}
                  {%- assign image_text_style = mega_menu_block.settings.image_text_style -%}

                  {%- capture image_push -%}
                    <div class="mega-menu__image-wrapper">
                      {%- assign menu_image = product.featured_image -%}
                      {{ menu_image | image_url: width: menu_image.width | image_tag: loading: 'lazy', sizes: '240px', sizes: '240,480,720', class: 'mega-menu__image' }}
                    </div>

                    <p class="mega-menu__heading heading {{ image_heading_style }} product-item-meta__title">{{ product.title }}</p>

                    {%- if mega_menu_block.settings[image_text_setting] != '' -%}
                      <span class="mega-menu__price {{ image_text_style }}">
                        {% render 'price', product: product %}
                      </span>
                    {%- endif -%}
                  {%- endcapture -%}

                  <a href="{{ product.url }}" class="mega-menu__image-push image-zoom">
                    {{- image_push -}}
                  </a>

                {%- endif -%}

              {%- endfor -%}
              
            {%- endcapture -%}

          {%- else -%}

            {%- capture mega_menu_images -%}
              {%- for i in (1..6) -%}
                {%- capture image_setting -%}image_{{ i }}{%- endcapture -%}
                {%- capture hide_market -%}hide_markets_{{ i }}{%- endcapture -%}

                {%- if mega_menu_block.settings[image_setting] != blank -%}
                  {%- if mega_menu_block.settings[hide_market] contains localization.country.iso_code -%}
                    {%- continue -%}
                  {%- endif -%}
                  {%- assign images_count = images_count | plus: 1 -%}

                  {%- capture image_heading_setting -%}image_{{ i }}_heading{%- endcapture -%}
                  {%- capture image_text_setting -%}image_{{ i }}_text{%- endcapture -%}
                  {%- capture image_link_setting -%}image_{{ i }}_link{%- endcapture -%}

                  {%- assign image_heading_style = mega_menu_block.settings.image_heading_style -%}
                  {%- assign image_text_style = mega_menu_block.settings.image_text_style -%}

                  {%- capture image_push -%}
                    <div class="mega-menu__image-wrapper">
                      {%- assign menu_image = mega_menu_block.settings[image_setting] -%}
                      {{ menu_image | image_url: width: menu_image.width | image_tag: loading: 'lazy', sizes: '240px', sizes: '240,480,720', class: 'mega-menu__image' }}
                    </div>

                    {%- if mega_menu_block.settings[image_heading_setting] != '' -%}
                      <p class="mega-menu__heading heading {{ image_heading_style }} product-item-meta__title">{{ mega_menu_block.settings[image_heading_setting] }}</p>
                    {%- endif -%}

                    {%- if mega_menu_block.settings[image_text_setting] != '' -%}
                      <span class="mega-menu__text {{ image_text_style }}">{{ mega_menu_block.settings[image_text_setting] }}</span>
                    {%- endif -%}
                  {%- endcapture -%}

                  {%- if mega_menu_block.settings[image_link_setting] != blank -%}
                    <a href="{{ mega_menu_block.settings[image_link_setting] }}" class="mega-menu__image-push image-zoom">
                      {{- image_push -}}
                    </a>
                  {%- else -%}
                    <div class="mega-menu__image-push image-zoom">
                      {{- image_push -}}
                    </div>
                  {%- endif -%}
                {%- endif -%}

              {%- endfor -%}

              {%- if mega_menu_block.settings.colors_enable -%}

                {%- comment -%}
                {%- if link.type == "collection_link" -%}
                  {%- assign collection = link.object -%}
                  {%- assign filters = collection.filters -%}
                  {%- capture filters_content -%}
                    {%- if mega_menu_block.settings.colors_collection_filters == true -%}
                      {%- if filters.values.size > 0 -%}
                        Filters Content > 0
                      {%- endif -%}
                    {%- endif -%}
                  {%- endcapture -%}
                  {%- if filters_content != blank -%}

                  {%- endif -%}
                {%- endif -%}
                {%- endcomment -%}

                {%- assign color_link_group_handle = mega_menu_block.settings.colors_link_group_handle -%}
                {%- assign color_link_group = metaobjects.color_link_group[color_link_group_handle] -%}

                {%- capture color_link_group_title -%}
                  {%- if color_link_group.title -%}
                    {{ color_link_group.title }}
                  {%- endif -%}
                {%- endcapture -%}

                {%- capture color_links -%}

                  {%- for color_link in color_link_group.color_links.value -%}

                    {%- capture color_link_url -%}
                      {%- if color_link.link.value.url != blank -%}
                        {{ color_link.link.value.url }}
                      {%- else -%}
                        #
                      {%- endif -%}
                    {%- endcapture -%}

                    {%- capture color_link_text -%}
                      {%- if color_link.link.value.text != blank -%}
                        {{ color_link.link.value.text }}
                      {%- endif -%}
                    {%- endcapture -%}

                    {%- capture color_link_color -%}
                      {%- if color_link.color -%}
                        {{ color_link.color }}
                      {%- endif -%}
                    {%- endcapture -%}

                    {%- if color_link_text != blank -%}

                      <a class="color-link" href="{{ color_link_url }}" {% if color_link.new_tab %}target="_blank"{% endif %}>
                        {%- if color_link_color -%}
                          <span class="color-link__swatch" style="--swatch-color: {{ color_link.color }}"></span>
                        {%- endif -%}
                        <span class="color-link__title link--animated">{{ color_link_text }}</span>
                      </a>

                    {%- endif -%}

                  {%- endfor -%}

                {%- endcapture -%}

                <div class="mega-menu__shop-by-color">
                  <div class="shop-by-color">
                    {%- if color_link_group_title != blank -%}
                      <p class="shop-by-color__heading mega-menu__heading heading">{{ color_link_group_title }}</p>
                    {%- endif -%}
                    {%- if color_links != blank -%}
                      <div class="shop-by-color__content">
                        {{ color_links }}
                      </div>
                    {%- endif -%}
                  </div>
                </div>

              {%- endif -%}

            {%- endcapture -%}

          {%- endif -%}

          {% assign no_sublinks = true %}
          {% for sublink in link.links %}
            {% if sublink.links.size > 0 %}
              {% assign no_sublinks = false %}
            {% endif %}
          {% endfor %}

          {% comment %} Header {% endcomment %}

          {%- capture mega_menu_header_content -%}
            {%- if mega_menu_block.settings.header_title != blank -%}
              <p class="mega-menu-header__title {{ mega_menu_block.settings.header_title_style }}">{{ mega_menu_block.settings.header_title }}</p>
            {%- endif -%}
          {%- endcapture -%}

          {% comment %} Footer {% endcomment %}

          {%- capture mega_menu_footer_actions -%}
            {%- if mega_menu_block.settings.footer_button_text != blank -%}
              <div class="button-wrapper">
                <a href="{{ mega_menu_block.settings.footer_button_link }}" class="button {{ mega_menu_block.settings.footer_button_style }} {{ mega_menu_block.settings.footer_button_size }}">
                  <span class="button__text">{{ mega_menu_block.settings.footer_button_text | escape }}</span>
                  {% if mega_menu_block.settings.footer_button_icon != "" %}
                    <span class="button__icon">{%- render 'icon' with mega_menu_block.settings.footer_button_icon, direction_aware: true -%}</span>
                  {% endif %}
                </a>
              </div>
            {%- endif -%}
          {%- endcapture -%}

          {%- capture mega_menu_footer_content -%}
            {%- if mega_menu_footer_actions != blank -%}
              <div class="mega-menu-footer__actions">
                {{- mega_menu_footer_actions -}}
              </div>
            {%- endif -%}
          {%- endcapture -%}

          {%- if link.links.size > 0 or mega_menu_images != blank or mega_menu_header_content != blank or mega_menu_footer_content != blank -%}
            <div hidden id="desktop-menu-{{ forloop.index }}" class="mega-menu" {{ mega_menu_block.shopify_attributes }}>
              <div class="container">

                {%- if mega_menu_header_content != blank -%}
                  <div class="mega-menu-header">
                    {{- mega_menu_header_content -}}
                  </div>
                {%- endif -%}

                <div class="mega-menu__inner">

                  {% comment %} Content {% endcomment %}

                  {%- if mega_menu_block.type == "mega_menu_products" -%}
                    
                    {%- if mega_menu_products != blank -%}
                      <div class="mega-menu__images-wrapper {% if products_count >= 3 %}mega-menu__images-wrapper--tight{% endif %}">
                        {{- mega_menu_products -}}
                      </div>
                    {%- endif -%}

                  {%- else -%}
                    
                    {%- if mega_menu_images != blank -%}
                      <div class="mega-menu__images-wrapper {% if images_count >= 3 %}mega-menu__images-wrapper--tight{% endif %}">
                        {{- mega_menu_images -}}
                      </div>
                    {%- endif -%}

                  {%- endif -%}

                  {%- if link.links.size > 0 -%}
                    <div class="mega-menu__columns-wrapper {% if no_sublinks == true %}mega-menu__columns-wrapper--no-sublinks{% endif %}">
                      {%- for sub_link in link.links -%}
                        <div class="mega-menu__column">
                          {%- if sub_link.url == '#' -%}
                            <span class="mega-menu__title heading {% if no_sublinks == true %}product-item-meta__title{% else %}heading--small{% endif %}">{{ sub_link.title }}</span>
                          {%- else -%}
                            <a href="{{ sub_link.url }}" class="mega-menu__title heading {% if no_sublinks == true %}product-item-meta__title{% else %}heading--small{% endif %}">{{ sub_link.title }}</a>
                          {%- endif -%}

                          {%- if sub_link.links.size > 0 -%}
                            <ul class="linklist list--unstyled" role="list">
                              {%- for sub_sub_link in sub_link.links -%}
                                <li class="linklist__item">
                                  <a href="{{ sub_sub_link.url }}" class="link--faded {% if sub_sub_link.title contains 'Shop All' %}link--highlight{% endif %}">{{ sub_sub_link.title }}</a>
                                </li>
                              {%- endfor -%}
                            </ul>
                          {%- endif -%}
                        </div>
                      {%- endfor -%}
                    </div>
                  {%- endif -%}

                </div>

                {% comment %} Footer {% endcomment %}

                {%- if mega_menu_footer_content != blank -%}
                  <div class="mega-menu-footer">
                    {{ mega_menu_footer_content }}  
                  </div>
                {%- endif -%}
                
              </div>
            </div>
          {%- endif -%}
        {%- elsif link.links.size > 0 -%}
          <ul hidden id="desktop-menu-{{ forloop.index }}" class="nav-dropdown {% if link.levels == 1 %}nav-dropdown--restrict{% endif %} list--unstyled" role="list">
            {%- for sub_link in link.links -%}
              <li class="nav-dropdown__item {% if sub_link.links.size > 0 %}has-dropdown{% endif %}">
                <a class="nav-dropdown__link link--faded" href="{{ sub_link.url }}" {% if sub_link.links.size > 0 %}aria-controls="desktop-menu-{{ forloop.parentloop.index }}-{{ forloop.index }}" aria-expanded="false"{% endif %}>
                  {{- sub_link.title -}}

                  {%- if sub_link.links.size > 0 -%}
                    {% render 'icon' with 'dropdown-arrow-right', direction_aware: true %}
                  {%- endif -%}
                </a>

                {%- if sub_link.links.size > 0 -%}
                  <ul hidden id="desktop-menu-{{ forloop.parentloop.index }}-{{ forloop.index }}" class="nav-dropdown list--unstyled" role="list">
                    {%- for sub_sub_link in sub_link.links -%}
                      <li class="nav-dropdown__item">
                        <a class="nav-dropdown__link link--faded" href="{{ sub_sub_link.url }}">{{ sub_sub_link.title }}</a>
                      </li>
                    {%- endfor -%}
                  </ul>
                {%- endif -%}
              </li>
            {%- endfor -%}
          </ul>
        {%- endif -%}
      </li>
    {%- endfor -%}
  </ul>
</desktop-navigation>