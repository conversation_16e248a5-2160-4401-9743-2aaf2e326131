<product-item class="product-item" {% if reveal %}reveal{% endif %}>
  <div class="product-item__image-wrapper product-item__image-wrapper--placeholder">
    {{- product_image | placeholder_svg_tag: 'placeholder-background' -}}
  </div>

  <div class="product-item__info {% if reduced_font_size %}text--small{% endif %}">
    <div class="product-item-meta">
      {%- if settings.show_vendor -%}
        <a class="product-item-meta__vendor heading heading--xsmall" href="#">{{ 'general.onboarding.product_vendor' | t }}</a>
      {%- endif -%}

      <a href="#" class="product-item__title">{{ 'general.onboarding.product_title' | t }}</a>

      <div class="product-item__price-list-container">
        <div class="price-list price-list--centered">
          <span class="price">
            <span class="visually-hidden">{{ 'product.general.sale_price' | t }}</span>
            {{- 5000 | money -}}
          </span>
        </div>
      </div>

      {%- if settings.show_product_rating and reduced_content != true -%}
        <div class="product-item-meta__reviews-badge text--small">
          <div class="rating">
            <div class="rating__stars" role="img" aria-label="{{ 'general.accessibility.star_reviews_info' | t: rating_value: 5, rating_max: 5 }}">
              {%- for i in (1..5) -%}
                {%- render 'icon' with 'rating-star', class: 'rating__star rating__star--full' -%}
              {%- endfor -%}
            </div>

            <span class="rating__caption">{{ 'product.general.reviews_count' | t: count: 2 }}</span>
          </div>
        </div>
      {%- endif -%}
    </div>

    {%- if show_cta -%}
      <button type="button" class="product-item__cta button button--primary">{{ 'collection.product.add_to_cart_short' | t }}</button>
    {%- endif -%}
  </div>
</product-item>