{%- if usp != blank  -%}

  {%- assign title = usp.title  | metafield_tag -%}
  {%- assign description = usp.description | metafield_tag -%}
  {%- assign icon = usp.icon_image -%}
  {%- assign icon_svg = usp.icon_svg | metafield_tag -%}

{%- endif -%}

{%- if reveal == blank -%}
  {%- assign reveal = true -%}
{%- endif -%}

{%- capture content -%}

  {%- capture icon_content -%}
    {%- if icon_svg != blank -%}
      {{ icon_svg }}
    {%- elsif icon != blank -%}
      {{ icon | image_url: width: 200 | image_tag: sizes: '200px', widths: '200,300,400', width: 50, height: 50, alt: icon.alt }}
    {%- endif -%}
  {%- endcapture -%}

  {%- if icon_content != blank -%}
    <div class="usp-icon__icon">
      {{ icon_content }}
    </div>
  {%- endif -%}
  
  {%- capture usp_details -%}
    {%- if title != blank -%}
      <h5 class="usp-icon__title heading h5">{{ title }}</h5>
    {%- endif -%}
    {%- if description != blank and show_description == true -%}
      <div class="usp-icon__description text--xsmall">{{ description }}</div>
    {%- endif -%}
  {%- endcapture -%}
  
  {%- if usp_details != blank -%}
    <div class="usp-icon__details">
      {{ usp_details }}
    </div>
  {%- endif -%}

{%- endcapture -%}

{%- if content != blank -%}
  <div class="usp-icon" {% if reveal == true %}reveal{% endif %}>
    {{ content }}  
  </div>
{%- endif -%}

