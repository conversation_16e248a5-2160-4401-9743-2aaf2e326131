{%- assign color_label_list = 'general.label.color' | t | replace: ', ', ',' | downcase | split: ',' -%}
{%- assign color_white_label = 'general.label.white' | t | downcase -%}
{%- assign variant_picker_block = section.blocks | where: 'type', 'variant_picker' | first -%}
{%- assign buy_buttons_block = section.blocks | where: 'type', 'buy_buttons' | first -%}

{%- if buy_button_block == blank -%}
  {%- assign buy_buttons_block = section.blocks | where: 'type', 'quantity_atc' | first -%}
{%- endif -%}

{%- assign variant = product.selected_or_first_available_variant -%}

{%- capture pre_order_one_tag -%}
  {%- if settings.pre_order_one_tag != blank -%}
    {{- settings.pre_order_one_tag -}}
  {%- endif -%}
{%- endcapture -%}

{%- capture pre_order_two_tag -%}
  {%- if settings.pre_order_two_tag != blank -%}
    {{- settings.pre_order_two_tag -}}
  {%- endif -%}
{%- endcapture -%}

{%- assign product_is_preorder = false -%}
{% if product.tags contains pre_order_one_tag or product.tags contains pre_order_two_tag %}
  {%- assign product_is_preorder = true -%}
{% endif %}

{%- assign cart_contains_preorder = false -%}
{%- for item in cart.items -%}
  {% if item.product.tags contains pre_order_one_tag or product.tags contains pre_order_two_tag %}
    {%- assign cart_contains_preorder = true -%}
    {%- break -%}
  {% endif %}
{%- endfor -%}

{%- assign cart_contains_non_preorder = false -%}
{%- for item in cart.items -%}
  {% if item.product.tags contains pre_order_one_tag or product.tags contains pre_order_two_tag %}
    {% else %}
      {%- assign cart_contains_non_preorder = true -%}
      {%- break -%}
  {% endif %}
{%- endfor -%}


{% comment %}
  Product Limits
{% endcomment %}

{%- liquid

  assign product_limit = ""
  assign locale_limit = ""
  
  for tag in product.tags
    if tag contains '_per_customer'
      assign split_tag = tag | split: '_'
      assign product_limit_word = split_tag[0]
      case product_limit_word
        when 'one'
          assign product_limit = "1"
        when 'two'
          assign product_limit = "2"
        when 'three'
          assign product_limit = "3"
        when 'four'
          assign product_limit = "4"
        when 'five'
          assign product_limit = "5"
        when 'six'
          assign product_limit = "6"
        when 'seven'
          assign product_limit = "7"
        when 'eight'
          assign product_limit = "8"
        when 'nine'
          assign product_limit = "9"
        when 'ten'
          assign product_limit = "10"
        else
          assign product_limit = ""
      endcase
    endif
    if tag == "CA-only"
      assign locale_limit = "Only available in Canada"
    endif
  endfor

-%}

{%- assign max_allowed_quantity = '' -%}
{%- if product_limit != blank -%}
  {%- assign max_allowed_quantity = product_limit | plus: 0 -%}
{%- endif -%}

{% comment %} If the product has a tier metafield tag set, find this tag and store it to show/hide the product form later. {% endcomment %}

{% assign customer_has_tier_tag = false %}
{% assign product_tags_array_string = '' %}

{% comment %}
  This first forloop puts together an array of tags that the product has.
  If the customer is tagged with the same tag, the banner is shown.
{% endcomment %}

{%- for i in (0..7) -%}

  {% capture tier_tag %}tier_{{ i }}_tag{% endcapture %}

  {% if product.metafields.custom.exclusive_access.value contains settings[tier_tag] %}
    
    {%- assign product_has_tier_tag = true -%}

    {% if product_tags_array_string == '' %}
      {%- assign product_tags_array_string = product_tags_array_string | append: settings[tier_tag] -%}
    {% else %}
      {%- assign product_tags_array_string = product_tags_array_string | append: "," | append: settings[tier_tag] -%}
    {% endif %}

  {% endif %}

{%- endfor -%}

{% comment %}
  This second forloop captures the message to be shown in the banner.
  The FIRST MATCHING TAG's message is always shown.
{% endcomment %}

{%- for i in (0..7) -%}
  
  {% capture tier_icon %}tier_{{ i }}_icon{% endcapture %}
  {% capture tier_simple_icon %}tier_{{ i }}_simple_icon{% endcapture %}
  {% capture tier_tag %}tier_{{ i }}_tag{% endcapture %}
  {% capture tier_message_heading %}tier_{{ i }}_message_heading{% endcapture %}
  {% capture tier_message_text %}tier_{{ i }}_message_text{% endcapture %}
  {% capture tier_cta_text %}tier_{{ i }}_cta_text{% endcapture %}
  {% capture tier_cta_link %}tier_{{ i }}_cta_link{% endcapture %}
  {% capture tier_atc_disabled_label %}tier_{{ i }}_atc_disabled_label{% endcapture %}
  
  {% capture tier_welcome_message_enable %}tier_{{ i }}_welcome_message_enable{% endcapture %}
  {% capture tier_welcome_message_title %}tier_{{ i }}_welcome_message_title{% endcapture %}
  {% capture tier_welcome_message_text %}tier_{{ i }}_welcome_message_text{% endcapture %}
  {% capture tier_welcome_message_cta_text %}tier_{{ i }}_welcome_message_cta_text{% endcapture %}
  {% capture tier_welcome_message_cta_link %}tier_{{ i }}_welcome_message_cta_link{% endcapture %}

  {% if product.metafields.custom.exclusive_access.value contains settings[tier_tag] %}
    {%- break -%}
  {% endif %}

{%- endfor -%}

{%- assign product_tags_array = product_tags_array_string | split: "," -%}

{%- for tag in product_tags_array -%}

  {%- if customer.tags contains tag -%}
    {%- assign customer_has_tier_tag = true -%}
    {%- break -%}
  {%- else -%}
    {%- assign customer_has_tier_tag = false -%}
  {%- endif -%}

{%- endfor -%}

{%- if product_has_tier_tag == true -%}

{%- else -%}

  {% assign tier_tag = blank %}
  {% assign tier_message_heading = blank %}
  {% assign tier_icon = blank %}
  {% assign tier_simple_icon = blank %}
  {% assign tier_message_text = blank %}
  {% assign tier_cta_text = blank %}
  {% assign tier_cta_link = blank %}
  {% assign tier_atc_disabled_label = blank %}

{%- endif -%}

{% capture price_display %}

  {%- if variant.price < variant.compare_at_price -%}

    <span class="sale-price-container">
      <span class="visually-hidden">{{ 'product.general.sale_price' | t }}</span>
      {{- variant.price | money_without_trailing_zeros -}}
    </span>

    <span class="regular-price-container">
      <span class="visually-hidden">{{ 'product.general.regular_price' | t }}</span>
      {{- variant.compare_at_price | money_without_trailing_zeros -}}
    </span>

    {% comment %}

    <span class="price--highlight">
      <span class="visually-hidden">{{ 'product.general.sale_price' | t }}</span>

      {%- if settings.currency_code_enabled -%}
        {{- variant.price | money_with_currency -}}
      {%- else -%}
        {%- if settings.currency_no_trailing_zeroes == false -%}
          {{- variant.price | money -}}
        {%- else -%}
          {{- variant.price | money_without_trailing_zeros -}}
        {%- endif -%}
      {%- endif -%}
    </span>

    <span class="price--compare">
      <span class="visually-hidden">{{ 'product.general.regular_price' | t }}</span>
      {%- if settings.currency_code_enabled -%}
        {{- variant.compare_at_price | money_with_currency -}}
      {%- else -%}
        {%- if settings.currency_no_trailing_zeroes == false -%}
          {{- variant.compare_at_price | money -}}
        {%- else -%}
          {{- variant.compare_at_price | money_without_trailing_zeros -}}
        {%- endif -%}
      {%- endif -%}
    </span>

  {% endcomment %}

  {%- else -%}

    <span class="">
      <span class="visually-hidden">{{ 'product.general.sale_price' | t }}</span>

      {%- if settings.currency_code_enabled -%}
        {{- variant.price | money_with_currency -}}
      {%- else -%}
        {%- if settings.currency_no_trailing_zeroes == false -%}
          {{- variant.price | money -}}
        {%- else -%}
          {{- variant.price | money_without_trailing_zeros -}}
        {%- endif -%}
      {%- endif -%}
    </span>

  {%- endif -%}

{% endcapture %}

{% capture atc_button_label %}
  {%- if product.selected_or_first_available_variant.available -%}
    {%- if product.template_suffix == 'pre-order' -%}
      {{- 'product.form.pre_order' | t -}}
    {%- else -%}
      {{- 'product.form.add_to_cart' | t -}}
    {%- endif -%}
  {%- else -%}
    {{- 'product.form.sold_out' | t -}}
  {%- endif -%}
{% endcapture %}



{% if product_has_tier_tag == true and customer_has_tier_tag != true %}

  {% comment %} {%- assign hide_quantity_atc = true -%} {% endcomment %}

  {%- if settings[tier_simple_icon] != blank -%}
    {%- assign product_form_icon = settings[tier_simple_icon] -%}
  {%- elsif settings[tier_icon] != blank -%}
    {%- assign product_form_icon = settings[tier_icon] -%}
  {%- endif -%}

  {%- capture product_form_banner_heading -%}
    {{- settings[tier_message_heading] -}}
  {%- endcapture -%}

  {%- capture product_form_banner_text -%}
    {{- settings[tier_message_text] -}}
  {%- endcapture -%}

  {%- capture product_form_banner_button_link -%}
    {{- settings[tier_cta_link] -}}
  {%- endcapture -%}

{% elsif sale_stop != blank and sale_stop.enable == true %}

  {% comment %} {%- assign hide_quantity_atc = true -%} {% endcomment %}

  {%- if sale_stop.icon != blank -%}
    {%- assign product_form_icon = sale_stop.icon -%}
  {%- endif -%}

  {%- capture product_form_banner_heading -%}
    {{- sale_stop.title | metafield_tag -}}
  {%- endcapture -%}

  {%- capture product_form_banner_text -%}
    {{- sale_stop.message | metafield_tag -}}
  {%- endcapture -%}

  {%- capture product_form_banner_button_link -%}
    {{- sale_stop.message_button_link -}}
  {%- endcapture -%}


{% endif %}


{% comment %} ----- Tier Welcome Banner ----- {% endcomment %}

{% if product_has_tier_tag == true and customer_has_tier_tag == true %}
  {{ tier_welcome_banner }}
{% endif %}



{% assign disable_atc = false %}

{% unless product.selected_or_first_available_variant.available %}
  {% assign disable_atc = true %}
{% endunless %}

{%- if sale_stop != blank and sale_stop.enable == true -%}
  {%- if sale_stop.atc_label != blank -%}
    {% assign atc_button_label = sale_stop.atc_label %}
  {%- endif -%}
{%- endif -%}

{%- if product_is_preorder == false and cart_contains_preorder == true -%}
  {% assign disable_atc = true %}
{%- endif -%}

{% if product_has_tier_tag and settings[tier_disable_atc] == true %}
  {%- assign disable_atc = true -%}
{% endif %}

{% if product_has_tier_tag == true and customer_has_tier_tag != true %}
  {%- assign disable_atc = true -%}

  {%- if settings[tier_atc_disabled_label] != blank -%}
    {% assign atc_button_label = settings[tier_atc_disabled_label] %}
    {% assign hide_price = true %}
  {%- endif -%}

  {%- assign hide_wishlist = true -%}
  {%- assign hide_registry = true -%}

{% endif %}

{% if sale_stop != blank and sale_stop.enable == true %}
  {%- assign disable_atc = true -%}
{% endif %}



{%- if buy_buttons_block != blank -%}
<product-rerender id="product-sticky-form-{{ product.id }}-{{ section.id }}" observe-form="{{ product_form_id }}">
{%- if product.selected_or_first_available_variant != nil -%}
  <style>
    @media screen and (min-width: 1000px) {
      :root {
        --anchor-offset: 140px; /* When the sticky form is activate, every scroll must be offset by an extra value */
      }
    }
  </style>

  <product-sticky-form form-id="{{ product_form_id }}" hidden class="product-sticky-form product-sticky-form--custom">
    <div class="container">
      <div class="product-sticky-form__inner">
        <div class="product-sticky-form__content-wrapper hidden-pocket">
          {%- if product.selected_or_first_available_variant.featured_image != blank -%}
            <div class="product-sticky-form__image-wrapper">
              {%- assign featured_media = product.selected_or_first_available_variant.featured_image | default: product.featured_media -%}
              {{- featured_media | image_url: width: featured_media.width | image_tag: loading: 'lazy', sizes: '55px', widths: '55,110,165', class: 'product-sticky-form__image' -}}
            </div>
          {%- endif -%}

          <div class="product-sticky-form__info">
            <div class="product-sticky-form__bottom-info">
              <span class="product-sticky-form__title">{{ product.title }}</span>
              
              <span class="product-sticky-form__price">
                {%- if settings.currency_code_enabled -%}
                  {{- product.selected_or_first_available_variant.price | money_with_currency -}}
                {%- else -%}
                  {{- product.selected_or_first_available_variant.price | money -}}
                {%- endif -%}
              </span>

              <div class="product-sticky-form__unit-price text--xsmall text--subdued" {% unless product.selected_or_first_available_variant.unit_price_measurement %}style="display: none"{% endunless %}>
                <div class="unit-price-measurement">
                  <span class="unit-price-measurement__price">{{ product.selected_or_first_available_variant.unit_price | money }}</span>
                  <span class="unit-price-measurement__separator">/</span>

                  {%- if product.selected_or_first_available_variant.unit_price_measurement.reference_value != 1 -%}
                    <span class="unit-price-measurement__reference-value">{{ product.selected_or_first_available_variant.unit_price_measurement.reference_value }}</span>
                  {%- endif -%}

                  <span class="unit-price-measurement__reference-unit">{{ product.selected_or_first_available_variant.unit_price_measurement.reference_unit }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {%- comment -%}This form is a simplified representation of the main form, and therefore uses its own code path{%- endcomment -%}
        <div class="product-sticky-form__form">
          {%- unless product.has_only_default_variant -%}
            <div {% if variant_picker_block.settings.hide_sold_out_variants %}hide-sold-out-variants{% endif %} class="product-sticky-form__variants hidden-pocket">
              {%- for option in product.options_with_values -%}
                {%- assign option_downcase = option.name | downcase -%}
                {%- capture option_id -%}sticky-form-option-{{ section.id }}-{{ forloop.index }}{%- endcapture -%}
                {%- assign color_swatch_config = settings.color_swatch_config | newline_to_br | split: '<br />' -%}

                <div class="select-wrapper" data-selector-type="dropdown">
                  <combo-box fit-toggle initial-focus-selector="[aria-selected='true']" id="{{ option_id }}-combo-box" class="combo-box">
                    <span class="combo-box__overlay"></span>

                    <header class="combo-box__header">
                      <p class="combo-box__title heading h6">{{ option.name }}</p>

                      <button type="button" class="combo-box__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
                        {%- render 'icon' with 'close' -%}
                      </button>
                    </header>

                    <div class="combo-box__option-list" role="listbox">
                      {%- for value in option.values -%}
                        <button type="button" role="option" class="combo-box__option-item" value="{{ value.id }}" aria-selected="{% if value == option.selected_value %}true{% else %}false{% endif %}">
                          {%- if color_label_list contains option_downcase -%}
                            <span class="combo-box__color-swatch" aria-label="{{ value | escape }}" style="{% render 'color-swatch-style', swatch: value.swatch, color_swatch_config: color_swatch_config, value: value %}"></span>
                          {%- endif -%}

                          {{- value -}}
                        </button>
                      {%- endfor -%}
                    </div>

                    <select class="visually-hidden" data-option-position="{{ option.position }}" name="option{{ option.position }}" form="{{ product_form_id }}" aria-label="{{ option.name | escape }}">
                      {%- for value in option.values -%}
                        {%- assign replacement_title = '' -%}
                        {%- assign downcase_value = value | downcase -%}

                        {%- if color_label_list contains option_downcase -%}
                          {%- capture replacement_title -%}
                            <span class="select__color-swatch {% if downcase_value == color_white_label %}select__color-swatch--white{% endif %}" aria-label="{{ value | escape }}" style="{% render 'color-swatch-style', swatch: value.swatch, color_swatch_config: color_swatch_config, value: value %}"></span>
                            <span class="select__label">{{- value -}}</span>
                          {%- endcapture -%}
                        {%- endif -%}

                        <option value="{{ value.id }}" {% if replacement_title != '' %}title="{{ replacement_title | escape }}"{% endif %} {% if value == option.selected_value %}selected{% endif %}>{{ value }}</option>
                      {%- endfor -%}
                    </select>
                  </combo-box>

                  <button type="button" is="toggle-button" class="select" aria-expanded="false" aria-haspopup="listbox" aria-controls="{{ option_id }}-combo-box">
                    <span class="select__selected-value">
                      {%- if color_label_list contains option_downcase -%}
                        {%- assign downcase_value = option.selected_value | downcase -%}
                        <span class="select__color-swatch {% if downcase_value == color_white_label %}select__color-swatch--white{% endif %}" aria-label="{{ option.selected_value | escape }}" style="{% render 'color-swatch-style', swatch: option.selected_value.swatch, color_swatch_config: color_swatch_config, value: option.selected_value %}"></span>
                      {%- endif -%}

                      <span class="select__label">{{- option.selected_value -}}</span>
                    </span>

                    {%- render 'icon' with 'chevron' -%}
                  </button>
                </div>
              {%- endfor -%}
            </div>
          {%- endunless -%}

          <product-payment-container class="product-sticky-form__payment-container">
            {%- capture button_content -%}
              {{ atc_button_label }}
            {%- endcapture -%}

            <button id="StickyAddToCart" is="loader-button" form="{{ product_form_id }}" type="submit" data-product-add-to-cart-button {% unless buy_buttons_block.settings.show_payment_button %}data-use-primary{% endunless %} data-button-content="{{ button_content | escape }}" class="product-form__add-button button button--xxs {% unless product.selected_or_first_available_variant.available %}button--primary{% else %}{% if buy_buttons_block.settings.show_payment_button %}button--primary{% else %}button--primary{% endif %}{% endunless %}" 
              {%- if disable_atc == true -%}
                disabled
              {%- endif -%}
            >
              {%- if product.selected_or_first_available_variant.available -%}
                {{- button_content -}}
              {%- else -%}
                {{- 'product.form.sold_out' | t -}}
              {%- endif -%}
            </button>
          </product-payment-container>
        </div>


        {%- capture jump_links -%}
          {%- if section.settings.sticky_form_jumplinks != blank -%}

            {%- assign jumplinks = section.settings.sticky_form_jumplinks | newline_to_br | strip_newlines | split: '<br />' -%} 

            {%- for link in jumplinks -%}
              {%- assign link_split = link | split: ":" -%}
              {%- assign link_title = link_split | first | strip -%}
              {%- assign link_url = link_split | last | strip -%}
              <a href="{{ link_url }}" class="link link--faded">{{ link_title }}</a>
            {%- endfor -%}
            
          {%- endif -%}
        {%- endcapture -%}

        {%- if jump_links != blank -%}
          <div class="product-sticky-form__jump-links">
            {{ jump_links }}
          </div>
        {%- endif -%}

      </div>
    </div>
  </product-sticky-form>
{%- endif -%}
</product-rerender>
{%- endif -%}