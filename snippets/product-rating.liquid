{%- if product.metafields.reviews.rating.value != blank -%}
  {%- assign rating_decimal = 0 -%}
  {%- assign decimal = product.metafields.reviews.rating.value.rating | modulo: 1 -%}

  {%- if decimal >= 0.3 and decimal <= 0.7 -%}
    {%- assign rating_decimal = 0.5 -%}
  {%- elsif decimal > 0.7 -%}
    {%- assign rating_decimal = 1 -%}
  {%- endif -%}

  <div class="rating">
    <div class="rating__stars" role="img" aria-label="{{ 'general.accessibility.star_reviews_info' | t: rating_value: product.metafields.reviews.rating.value, rating_max: product.metafields.reviews.rating.value.scale_max }}">
      {%- assign rating_as_float = product.metafields.reviews.rating.value.rating | times: 1.0 -%}

      {%- for i in (product.metafields.reviews.rating.value.scale_min..product.metafields.reviews.rating.value.scale_max) -%}
        {%- if rating_as_float >= i -%}
          {%- render 'icon' with 'rating-star', class: 'rating__star rating__star--full' -%}
        {%- else -%}
          {%- if rating_decimal == 0.5 -%}
            {%- render 'icon' with 'rating-star-half', class: 'rating__star rating__star--half' -%}
          {%- elsif rating_decimal == 1 -%}
            {%- render 'icon' with 'rating-star', class: 'rating__star rating__star--full' -%}
          {%- else -%}
            {%- render 'icon' with 'rating-star', class: 'rating__star rating__star--empty' -%}
          {%- endif -%}

          {%- assign rating_decimal = false -%}
        {%- endif -%}
      {%- endfor -%}
    </div>

    <span class="rating__caption">{{ 'product.general.reviews_count' | t: count: product.metafields.reviews.rating_count.value }}</span>
  </div>
{%- else -%}
  <div class="rating">
    <div class="rating__stars" role="img" aria-label="{{ 'general.accessibility.star_reviews_info' | t: rating_value: 0, rating_max: 5 }}">
      {%- for i in (1..5) -%}
        {%- render 'icon' with 'rating-star', class: 'rating__star rating__star--empty' -%}
      {%- endfor -%}
    </div>

    <span class="rating__caption">{{ 'product.general.reviews_count' | t: count: 0 }}</span>
  </div>
{%- endif -%}