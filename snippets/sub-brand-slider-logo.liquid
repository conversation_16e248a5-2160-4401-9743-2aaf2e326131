{%- if sub_brand != blank -%}

  {%- comment -%}
  
  sub_brand.logo
  sub_brand.link
  sub_brand.title
  sub_brand.description

  sub_brand.text_color
  sub_brand.heading_color
  sub_brand.subheading_color
  sub_brand.button_color
  sub_brand.button_text_color
  sub_brand.colors
  sub_brand.background_color
  sub_brand.secondary_background_color
  sub_brand.custom_css

  {%- endcomment -%}

{%- endif -%}

{%- liquid

  # Sub-Brand

  if sub_brand != blank
    if sub_brand.logo != blank and sub_brand.link != blank and sub_brand.title != blank
      assign title = sub_brand.title
      assign link = sub_brand.link
      assign logo_image = sub_brand.logo
      assign logo_svg = sub_brand.logo_svg
    endif
  endif

  # Logo

  if logo_image != blank or logo_svg != blank
    assign has_logo = true
  endif

  # Link

  if sub_brand.landing_page != blank or sub_brand.link != blank or link != blank
    assign has_link = true
    if sub_brand.landing_page != blank
      assign link = sub_brand.landing_page.value.url
    elsif sub_brand.link != blank
      assign link = sub_brand.link.url
    elsif link != blank
      assign link = link
    endif
  endif

-%}

{%- if 
  title != blank and
  has_link == true and
  has_logo == true
  -%}

  {%- capture classes -%}
    {{ classes }}
    {% if active %}sub-brand-slider-logo--active{% endif %}
    {% if logo_image != blank and logo_svg == blank %}sub-brand-slider-logo--image{%- endif -%}
  {%- endcapture -%}

  <a class="sub-brand-slider-logo {{ classes }}" title="{{ title }}" href="{{ link }}">
    <span class="sub-brand-slider-logo__inner">
      <span class="sub-brand-slider-logo__logo">
        {%- if logo_svg != blank -%}
          {{ logo_svg }}
        {%- else -%}
          {{- logo_image | image_url: width: logo_image.width | image_tag: loading: 'lazy', widths: '800,1200,1400,1600', class: 'sub-brand-slider-logo__logo-image' -}}
        {%- endif -%}
      </span>
    </span>
  </a>
  
{%- endif -%}

