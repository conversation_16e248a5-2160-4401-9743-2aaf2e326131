<!-- 2025-04-04T16:30:07.5127265Z -->
<script type="text/javascript">(function(){ function waitForElement(e){return new Promise(s=>{const t=document.querySelector(e);if(t){s(t);return}const i=new MutationObserver(o=>{const r=document.querySelector(e);if(r){i.disconnect(),s(r);return}});i.observe(document.documentElement,{childList:!0,subtree:!0})})}function clearThemeBar(){waitForElement("#preview-bar-iframe, #PBarNextFrameWrapper").then(e=>e.remove()).catch(e=>console.error(e))}clearThemeBar(); })()</script>