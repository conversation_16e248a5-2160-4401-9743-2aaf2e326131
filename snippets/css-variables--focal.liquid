{% comment %}
  This file contains some CSS variables commonly used by UXForge.
  This extends the CSS variables found in the Prestige theme..
{% endcomment %}

{% style %}

:root {

/*  ==============================
    Layout
    ============================== */

---container-gutter--desktop: 30px;
---container-gutter--mobile: 20px;

/*  ==============================
    Type
    ============================== */
 
/*  ------------------------------
    Fonts
    ------------------------------ */

// ---font-family-body: var(--text-font-family);
---font-family-body: 'Lexend', Helvetica, Arial, sans-serif;
---font-style-body: normal;
---font-weight-body: 300;
---font-weight-body--bold: 600;
---font-weight-body--bolder: 700;

---font-family-body--alternate: 'Lexend Deca', Helvetica, Arial, sans-serif;;
---font-style-body--alternate: normal;
---font-weight-body--alternate: 300;
---font-weight-body--alternate--bold: 600;
---font-weight-body--alternate--bolder: 700;

---font-family-heading: 'Lexend', Helvetica, Arial, sans-serif;
---font-style-heading: normal;
---font-weight-heading: 700;
---font-weight-heading--bold: 600;
---font-weight-heading--bolder: 700;

---font-family-heading--alternate: {{ settings.text_font.family }}, {{ settings.text_font.fallback_families }};
---font-style-heading: normal;
---font-weight-heading: 700;
---font-weight-heading--bold: 600;
---font-weight-heading--bolder: 700;

---font-family-subheading: 'Lexend', Helvetica, Arial, sans-serif;
---font-style-subheading: normal;
---font-weight-subheading: 700;
---font-weight-subheading--bold: 600;
---font-weight-subheading--bolder: 700;

---font-style-heading-alternate: normal;
---font-weight-heading-alternate: 600;
---font-weight-heading-alternate--bold: 700;

---font-weight--product-title: 600;

---font-weight--product-price: 600;
---font-weight--product-price--compare: 600;
---font-weight--product-price--sale: 600;

/*  ------------------------------
    Typography
    ------------------------------ */

/* ----- Font Size ----- */

---font-size-h1--mobile: 32px;
---font-size-h1--desktop: 64px;
---font-size-h2--mobile: 28px;
---font-size-h2--desktop: 44px;
---font-size-h3--mobile: 18px;
---font-size-h3--desktop: 28px;
---font-size-h4--mobile: 18px;
---font-size-h4--desktop: 24px;
---font-size-h5--mobile: 14px;
---font-size-h5--desktop: 16px;
---font-size-h6--mobile: 12px;
---font-size-h6--desktop: 16px;

---font-size-subheading-small: 10px;
---font-size-subheading--mobile: 12px;
---font-size-subheading--desktop: 16px;

--font-size-body: 13px;
---font-size-body--mobile: 13px;
---font-size-body--desktop: 16px;

--font-size-xs: 10px;
---font-size-body-xs--mobile: 10px;
---font-size-body-xs--desktop: 12px;

--font-size-sm: 12px;
---font-size-body-small--mobile: 12px;
---font-size-body-small--desktop: 14px;

--font-size-lg: 14px;
---font-size-body-large--mobile: 14px;
---font-size-body-large--desktop: 18px;

---font-size-product-title--mobile: 14px;
---font-size-product-title--desktop: 16px;
---font-size-product-title--major--mobile: 24px;
---font-size-product-title--major--desktop: 28px;

---font-size-price--major--mobile: 14px;
---font-size-price--major--desktop: 16px;

---font-size-button--mobile: 16px;
---font-size-button--desktop: 16px;
---font-size-button-small--mobile: 12px;
---font-size-button-small--desktop: 12px;
---font-size-button-large--mobile: 14px;
---font-size-button-large--desktop: 14px;
---font-size-button-tiny--mobile: 12px;
---font-size-button-tiny--desktop: 12px;

---font-size-label--mobile: var(---font-size-body-small--mobile);
---font-size-label--desktop: var(---font-size-body-small--desktop);

/*
Line Height
*/

---line-height-heading--mobile: 1.4;
---line-height-heading--desktop: 1.4;
---line-height-body--mobile: 1.6;
---line-height-body--desktop: 1.6;

/* ----- Letter Spacing ----- */

---letter-spacing-heading--mobile: 0;
---letter-spacing-heading--desktop: 0;

---letter-spacing-subheading--mobile: 1px;
---letter-spacing-subheading--desktop: 1px;

---letter-spacing-body--mobile: inherit;
---letter-spacing-body--desktop: inherit;

---letter-spacing-title--mobile: inherit;
---letter-spacing-title--desktop: inherit;
---letter-spacing-product-name--mobile: inherit;
---letter-spacing-product-name--desktop: inherit;

/*  ==============================
    Colors
    ============================== */

---color--white: {{ settings.color__white }};
---color--black: {{ settings.color__black }};

/*  ------------------------------
    Brand
    ------------------------------ */

---color--brand-1: {{ settings.color__brand__1 }};
---color--brand-2: {{ settings.color__brand__2 }};
---color--brand-3: {{ settings.color__brand__3 }};
---color--brand-4: {{ settings.color__brand__4 }};
---color--brand-5: {{ settings.color__brand__5 }};
---color--brand-6: {{ settings.color__brand__6 }};
---color--brand-7: {{ settings.color__brand__7 }};

---color--brand-1--rgb: {{ settings.color__brand__1.red }}, {{ settings.color__brand__1.green }}, {{ settings.color__brand__1.blue }};
---color--brand-2--rgb: {{ settings.color__brand__2.red }}, {{ settings.color__brand__2.green }}, {{ settings.color__brand__2.blue }};
---color--brand-3--rgb: {{ settings.color__brand__3.red }}, {{ settings.color__brand__3.green }}, {{ settings.color__brand__3.blue }};
---color--brand-4--rgb: {{ settings.color__brand__4.red }}, {{ settings.color__brand__4.green }}, {{ settings.color__brand__4.blue }};
---color--brand-5--rgb: {{ settings.color__brand__5.red }}, {{ settings.color__brand__5.green }}, {{ settings.color__brand__5.blue }};
---color--brand-6--rgb: {{ settings.color__brand__6.red }}, {{ settings.color__brand__6.green }}, {{ settings.color__brand__6.blue }};
---color--brand-7--rgb: {{ settings.color__brand__7.red }}, {{ settings.color__brand__7.green }}, {{ settings.color__brand__7.blue }};

---color--kyte-black: var(---color--brand-1);
---color--kyte-dark-grey: var(---color--brand-2);
---color--kyte-light-grey: var(---color--brand-3);
---color--kyte-white: var(---color--brand-4);
---color--kyte-cream: var(---color--brand-5);
---color--kyte-dark-cream: var(---color--brand-6);
---color--kyte-6: var();

/*  ------------------------------
    Color System
    ------------------------------ */

/* ----- Base Colors ----- */

{%- assign color_system_string = "default,primary,secondary,tertiary,success,warning,danger,info" -%}
{%- assign color_system_array = color_system_string | split: ',' -%}

{% for color_name in color_system_array %}

  {%- assign color_stripped = color_name | strip -%}

  {%- capture color_stub -%}
    color__{{ color_stripped }}
  {%- endcapture -%}

  {%- capture color_stub_light -%}
    color__{{ color_stripped }}__light
  {%- endcapture -%}

  {%- capture color_stub_dark -%}
    color__{{ color_stripped }}__dark
  {%- endcapture -%}

  {%- assign color = settings[color_stub] -%}
  {%- assign color_light = settings[color_stub_light] -%}
  {%- assign color_dark = settings[color_stub_dark] -%}
  {%- assign color_disabled = color | color_desaturate: 30 -%}

  // {{ color_name | capitalize }}

  // color_stripped - {{ color_stripped }}
  // color_stub - {{ color_stub }}
  // color_stub_light - {{ color_stub_light }}
  // color_stub_dark - {{ color_stub_dark }}

  // disabled_color - {{ disabled_color }}

  // color - {{ color }}
  // color_light - {{ color_light }}
  // color_dark - {{ color_dark }}
  // color_disabled - {{ color_disabled }}

  ---color--{{ color_stripped }}: {{ color }};
  ---color--{{ color_stripped }}--light: {{ color_light }};
  ---color--{{ color_stripped }}--dark: {{ color_dark }};
  ---color--{{ color_stripped }}--disabled: {{ color_disabled }};

  ---color--{{ color_stripped }}--rgb: {{ color.red }}, {{ color.green }}, {{ color.blue }};
  ---color--{{ color_stripped }}--light--rgb: {{ color_light.red }}, {{ color_light.green }}, {{ color_light.blue }};
  ---color--{{ color_stripped }}--dark--rgb: {{ color_dark.red }}, {{ color_dark.green }}, {{ color_dark.blue }};
  ---color--{{ color_stripped }}--disabled--rgb: {{ color_disabled.red }}, {{ color_disabled.green }}, {{ color_disabled.blue }};

{% endfor %}

{% comment %}

---color--default: {{ settings.color__default }};
---color--default--light: {{ settings.color__default__light }};
---color--default--dark: {{ settings.color__default__dark }};
---color--default--disabled: {{ settings.color__default | color_desaturate: 30 }};

---color--primary: {{ settings.color__primary }};
---color--primary--light: {{ settings.color__primary__light }};
---color--primary--dark: {{ settings.color__primary__dark }};
---color--primary--disabled: {{ settings.color__default | color_desaturate: 30 }};

---color--secondary: {{ settings.color__secondary }};
---color--secondary--light: {{ settings.color__secondary__light }};
---color--secondary--dark: {{ settings.color__secondary__dark }};
---color--secondary--disabled: {{ settings.color__default | color_desaturate: 30 }};

---color--tertiary: {{ settings.color__tertiary }};
---color--tertiary--light: {{ settings.color__tertiary__light }};
---color--tertiary--dark: {{ settings.color__tertiary__dark }};
---color--tertiary--disabled: {{ settings.color__default | color_desaturate: 30 }};

---color--success: {{ settings.color__success }};
---color--success--light: {{ settings.color__success__light }};
---color--success--dark: {{ settings.color__success__dark }};
---color--success--disabled: {{ settings.color__success | color_desaturate: 30  }};

---color--warning: {{ settings.color__warning }};
---color--warning--light: {{ settings.color__warning__light }};
---color--warning--dark: {{ settings.color__warning__dark }};
---color--warning--disabled: {{ settings.color__warning | color_desaturate: 30  }};

---color--danger: {{ settings.color__danger }};
---color--danger--light: {{ settings.color__danger__light }};
---color--danger--dark: {{ settings.color__danger__dark }};
---color--danger--disabled: {{ settings.color__danger | color_desaturate: 30  }};

---color--info: {{ settings.color__info }};
---color--info--light: {{ settings.color__info__light }};
---color--info--dark: {{ settings.color__info__dark }};
---color--info--disabled: {{ settings.color__info | color_desaturate: 30  }};



---color--default--rgb: {{ settings.color__default.red }}, {{ settings.color__default.green }}, {{ settings.color__default.blue }};
---color--default--light--rgb: {{ settings.color__default__light.red }}, {{ settings.color__default__light.green }}, {{ settings.color__default__light.blue }};
---color--default--dark--rgb: {{ settings.color__default__dark.red }}, {{ settings.color__default__dark.green }}, {{ settings.color__default__dark.blue }};

---color--primary--rgb: {{ settings.color__primary.red }}, {{ settings.color__primary.green }}, {{ settings.color__primary.blue }};
---color--primary--light--rgb: {{ settings.color__primary__light.red }}, {{ settings.color__primary__light.green }}, {{ settings.color__primary__light.blue }};
---color--primary--dark--rgb: {{ settings.color__primary__dark.red }}, {{ settings.color__primary__dark.green }}, {{ settings.color__primary__dark.blue }};

---color--secondary--rgb: {{ settings.color__secondary.red }}, {{ settings.color__secondary.green }}, {{ settings.color__secondary.blue }};
---color--secondary--light--rgb: {{ settings.color__secondary__light.red }}, {{ settings.color__secondary__light.green }}, {{ settings.color__secondary__light.blue }};
---color--secondary--dark--rgb: {{ settings.color__secondary__dark.red }}, {{ settings.color__secondary__dark.green }}, {{ settings.color__secondary__dark.blue }};

---color--tertiary--rgb: {{ settings.color__tertiary.red }}, {{ settings.color__tertiary.green }}, {{ settings.color__tertiary.blue }};
---color--tertiary--light--rgb: {{ settings.color__tertiary__light.red }}, {{ settings.color__tertiary__light.green }}, {{ settings.color__tertiary__light.blue }};
---color--tertiary--dark--rgb: {{ settings.color__tertiary__dark.red }}, {{ settings.color__tertiary__dark.green }}, {{ settings.color__tertiary__dark.blue }};

---color--disabled--rgb: {{ settings.color__disabled.red }}, {{ settings.color__disabled.green }}, {{ settings.color__disabled.blue }};
---color--disabled--light--rgb: {{ settings.color__disabled__light.red }}, {{ settings.color__disabled__light.green }}, {{ settings.color__disabled__light.blue }};
---color--disabled--dark--rgb: {{ settings.color__disabled__dark.red }}, {{ settings.color__disabled__dark.green }}, {{ settings.color__disabled__dark.blue }};

---color--success--rgb: {{ settings.color__success.red }}, {{ settings.color__success.green }}, {{ settings.color__success.blue }};
---color--success--light--rgb: {{ settings.color__success__light.red }}, {{ settings.color__success__light.green }}, {{ settings.color__success__light.blue }};
---color--success--dark--rgb: {{ settings.color__success__dark.red }}, {{ settings.color__success__dark.green }}, {{ settings.color__success__dark.blue }};

---color--warning--rgb: {{ settings.color__warning.red }}, {{ settings.color__warning.green }}, {{ settings.color__warning.blue }};
---color--warning--light--rgb: {{ settings.color__warning__light.red }}, {{ settings.color__warning__light.green }}, {{ settings.color__warning__light.blue }};
---color--warning--dark--rgb: {{ settings.color__warning__dark.red }}, {{ settings.color__warning__dark.green }}, {{ settings.color__warning__dark.blue }};

---color--danger--rgb: {{ settings.color__danger.red }}, {{ settings.color__danger.green }}, {{ settings.color__danger.blue }};
---color--danger--light--rgb: {{ settings.color__danger__light.red }}, {{ settings.color__danger__light.green }}, {{ settings.color__danger__light.blue }};
---color--danger--dark--rgb: {{ settings.color__danger__dark.red }}, {{ settings.color__danger__dark.green }}, {{ settings.color__danger__dark.blue }};

---color--info--rgb: {{ settings.color__info.red }}, {{ settings.color__info.green }}, {{ settings.color__info.blue }};
---color--info--light--rgb: {{ settings.color__info__light.red }}, {{ settings.color__info__light.green }}, {{ settings.color__info__light.blue }};
---color--info--dark--rgb: {{ settings.color__info__dark.red }}, {{ settings.color__info__dark.green }}, {{ settings.color__info__dark.blue }};

---color--disabled: {{ settings.color__disabled }};
---color--disabled--light: {{ settings.color__disabled__light }};
---color--disabled--dark: {{ settings.color__disabled__dark }};
---color--disabled--disabled: {{ settings.color__disabled | color_desaturate: 30 }};

{% endcomment %}

/* ----- Background Colors ----- */

---background-color--primary: {{ settings.background_color__primary }};
---background-color--secondary: {{ settings.background_color__secondary }};
---background-color--tertiary: {{ settings.background_color__tertiary }};

---background-color--default: {{ settings.background_color__default }};
---background-color--disabled: {{ settings.background_color__disabled }};
---background-color--success: {{ settings.background_color__success }};
---background-color--warning: {{ settings.background_color__warning }};
---background-color--danger: {{ settings.background_color__danger }};
---background-color--info: {{ settings.background_color__info }};

---background-color--primary--rgb: {{ settings.background_color__primary.red }}, {{ settings.background_color__primary.green }}, {{ settings.background_color__primary.blue }};
---background-color--secondary--rgb: {{ settings.background_color__secondary.red }}, {{ settings.background_color__secondary.green }}, {{ settings.background_color__secondary.blue }};
---background-color--tertiary--rgb: {{ settings.background_color__tertiary.red }}, {{ settings.background_color__tertiary.green }}, {{ settings.background_color__tertiary.blue }};

---background-color--default--rgb: {{ settings.background_color__default.red }}, {{ settings.background_color__default.green }}, {{ settings.background_color__default.blue }};
---background-color--disabled--rgb: {{ settings.background_color__disabled.red }}, {{ settings.background_color__disabled.green }}, {{ settings.background_color__disabled.blue }};
---background-color--success--rgb: {{ settings.background_color__success.red }}, {{ settings.background_color__success.green }}, {{ settings.background_color__success.blue }};
---background-color--warning--rgb: {{ settings.background_color__warning.red }}, {{ settings.background_color__warning.green }}, {{ settings.background_color__warning.blue }};
---background-color--danger--rgb: {{ settings.background_color__danger.red }}, {{ settings.background_color__danger.green }}, {{ settings.background_color__danger.blue }};
---background-color--info--rgb: {{ settings.background_color__info.red }}, {{ settings.background_color__info.green }}, {{ settings.background_color__info.blue }};


/*  ------------------------------
    Content Colors
    ------------------------------ */

---background-color--body: {{ settings.background_color_body }};

---background-color--content-1: {{ settings.background_color_content_1 }};
---background-color--content-2: {{ settings.background_color_content_2 }};
---background-color--content-3: {{ settings.background_color_content_3 }};

---background-color--content-1--rgb: {{ settings.background_color_content_1.red }}, {{ settings.background_color_content_1.green }}, {{ settings.background_color_content_1.blue }};
---background-color--content-2--rgb: {{ settings.background_color_content_2.red }}, {{ settings.background_color_content_2.green }}, {{ settings.background_color_content_2.blue }};
---background-color--content-3--rgb: {{ settings.background_color_content_3.red }}, {{ settings.background_color_content_3.green }}, {{ settings.background_color_content_3.blue }};

---background-color--content-reversed-1: {{ settings.background_color_content_reversed_1 }};
---background-color--content-reversed-2: {{ settings.background_color_content_reversed_2 }};



---color-text: {{ settings.color_text }};
---color-text--dark: {{ settings.color_text_dark }};
---color-text--light: {{ settings.color_body_light }};
---color-text--reversed: {{ settings.color_body_reversed }};
---color-text--reversed-strong: {{ settings.color_body_reversed_strong }};

---color-text--rgb: {{ settings.color_text.red }}, {{ settings.color_text.green }}, {{ settings.color_text.blue }};
---color-text--dark--rgb: {{ settings.color_text_dark.red }}, {{ settings.color_text_dark.green }}, {{ settings.color_text_dark.blue }};
---color-text--light--rgb: {{ settings.color_body_light.red }}, {{ settings.color_body_light.green }}, {{ settings.color_body_light.blue }};
---color-text--reversed--rgb: {{ settings.color_body_reversed.red }}, {{ settings.color_body_reversed.green }}, {{ settings.color_body_reversed.blue }};
---color-text--reversed-strong--rgb: {{ settings.color_body_reversed_strong.red }}, {{ settings.color_body_reversed_strong.green }}, {{ settings.color_body_reversed_strong.blue }};

---color-heading-1: var(---color--primary);
---color-heading-2: var(---color--secondary);
---color-heading-3: var(---color--tertiary);

---color-heading-1--rgb: var(---color--primary--rgb);
---color-heading-2--rgb: var(---color--secondary--rgb);
---color-heading-3--rgb: var(---color--tertiary--rgb);

---color-products-title: {{ settings.color_product_title }};

---color-price: {{ settings.color_prices }};
---color-price--sale: {{ settings.color_prices_sale }};
---color-price--compare: {{ settings.color_prices_compare }};

---color-link: {{ settings.color_link }};
---color-link--rgb: {{ settings.color_link.red }}, {{ settings.color_link.green }}, {{ settings.color_link.blue }};
---color-link--hover: {{ settings.color_link | lighten: 10 }};

---color-reviews: {{ settings.color_reviews }};
---color-reviews--rgb: {{ settings.color_reviews.red }}, {{ settings.color_reviews.green }}, {{ settings.color_reviews.blue }};

---banner-overlay-background: {{ settings.overlay_color }};


/*  ==============================
    Components
    ============================== */

---border-radius--general: 0px;
---border-radius--inputs: 4px;

---color-line: {{ settings.color_line }};
---color-line--light: {{ settings.color_line_light }};
---color-line--dark: {{ settings.color_line_dark }};

---color-line--rgb: {{ settings.color_line.red }}, {{ settings.color_line.green }}, {{ settings.color_line.blue }};
---color-line--light--rgb: {{ settings.color_line_light.red }}, {{ settings.color_line_light.green }}, {{ settings.color_line_light.blue }};
---color-line--dark--rgb: {{ settings.color_line_dark.red }}, {{ settings.color_line_dark.green }}, {{ settings.color_line_dark.blue }};

---color-icon: var(---color--primary);

---stamp-width--mobile: 60px;
---stamp-width--desktop: 150px;

/*  ------------------------------
    Form Elements
    ------------------------------ */

---button-color: #F5FBFD;
---button-radius: var(--button-border-radius);
---button-min-width: 120px;
---button-letter-spacing: 0.05em;

---input-color: var(---color-text--dark);
---input-background: var(---background-color--content-1);
---input-border-radius: {{ settings.input_border_radius | append: 'px' }};
---input-min-width: {{ settings.input_min_width | append: 'px' }};
---input-letter-spacing: {{ settings.input_letter_spacing | append: 'em' }};

---label-color: var(---color-text--dark);
---label-font-weight: var(---font-weight-body);
---label-letter-spacing: {{ settings.input_letter_spacing | append: 'em' }};

/*  ------------------------------
    Effects
    ------------------------------` */

// ---shadow--banner-title: 0px 4px 17px rgba(0, 0, 0, 0.25);

---shadow--modal: 0px 4px 30px rgba(0, 0, 0, 0.1);
---shadow--section: 0px 0px 7px rgba(0, 0, 0, 0.1);
---shadow--section-inner: inset 0px 0px 10px rgba(0, 0, 0, 0.1);

---transition-duration--general: 0.25s;

--section-shadow: 0px 0px 7px rgba(0, 0, 0, 0.05);
--section-shadow-inner: inset 0px 0px 20px rgba(0, 0, 0, 0.05);


--block-shadow: 0px 0px 4px rgba(0, 0, 0, 0.1);
--block-shadow--card: 0px 8px 16px rgba(0, 0, 0, 0.04);;


/*  ==============================
    Vectors
    ============================== */

// ---logo-header: url('{{ "logo_header.svg" | asset_url }}');
// ---logo-footer: url('{{ "logo_footer.svg" | asset_url }}');


/*  ==============================
    Images
    ============================== */

---pattern-wood: url('{{ "wood-texture--tiled.jpg" | file_url }}');
// ---pattern-blue-size: 150px;

// ---illustration-bio: url('{{ "illustration-bio.png" | file_url }}');
---icon--chevron: url('{{ "icon-chevron.png" | asset_url }}');

/*  ==============================
    Animations
    ============================== */

---transition-hover: cubic-bezier(0.4, 0, 0.22, 1);


/*  ==============================
    HEADER
    ============================== */

    {% comment %}
    --tabbed-header-color-background-1: {{ settings.tabbed_header_background_1.rgb }};
    --tabbed-header-color-background-2: {{ settings.tabbed_header_background_2.rgb }};
    --tabbed-header-color-logo: {{ settings.tabbed_header_logo_color.rgb }};
    --tabbed-header-color-icons: {{ settings.tabbed_header_icon_color.rgb }};
    --tabbed-header-color-border: {{ settings.tabbed_header_border_color.rgb }};
    --tabbed-header-color-button: var(--sub-brand--button-color--rgb, {{ settings.tabbed_header_button_color.rgb }});
    --tabbed-header-color-link: {{ settings.tabbed_header_link_color.rgb }};
    --tabbed-header-color-heading: var(--sub-brand--heading-color--rgb, {{ settings.tabbed_header_heading_color.rgb }});
    --tabbed-header-color-text: var(--sub-brand--text-color--rgb, {{ settings.tabbed_header_text_color.rgb }});
    --tabbed-header-color-text--hover: {{ settings.tabbed_header_text_hover_color.rgb }};
    --tabbed-header-color-bubble: {{ settings.tabbed_header_bubble_color.rgb }};
    {% endcomment %}

    --enable-sticky-product-form: 0;
    --product-sticky-form-height: 0px;
    --sticky-product-form-spacer: 0px;

    --tabbed-header-color-background-1: {{ settings.tabbed_header_background_1.red }}, {{ settings.tabbed_header_background_1.green }}, {{ settings.tabbed_header_background_1.blue }};
    --tabbed-header-color-background-2: {{ settings.tabbed_header_background_2.red }}, {{ settings.tabbed_header_background_2.green }}, {{ settings.tabbed_header_background_2.blue }};
    --tabbed-header-color-logo: {{ settings.tabbed_header_logo_color.red }}, {{ settings.tabbed_header_logo_color.green }}, {{ settings.tabbed_header_logo_color.blue }};
    --tabbed-header-color-icons: {{ settings.tabbed_header_icon_color.red }}, {{ settings.tabbed_header_icon_color.green }}, {{ settings.tabbed_header_icon_color.blue }};
    --tabbed-header-color-border: {{ settings.tabbed_header_border_color.red }}, {{ settings.tabbed_header_border_color.green }}, {{ settings.tabbed_header_border_color.blue }};
    --tabbed-header-color-button: {{ settings.tabbed_header_button_color.red }}, {{ settings.tabbed_header_button_color.green }}, {{ settings.tabbed_header_button_color.blue }};
    --tabbed-header-color-link: {{ settings.tabbed_header_link_color.red }}, {{ settings.tabbed_header_link_color.green }}, {{ settings.tabbed_header_link_color.blue }};
    --tabbed-header-color-heading: {{ settings.tabbed_header_heading_color.red }}, {{ settings.tabbed_header_heading_color.green }}, {{ settings.tabbed_header_heading_color.blue }};
    --tabbed-header-color-text: {{ settings.tabbed_header_text_color.red }}, {{ settings.tabbed_header_text_color.green }}, {{ settings.tabbed_header_text_color.blue }};
    --tabbed-header-color-text--hover: {{ settings.tabbed_header_text_hover_color.red }}, {{ settings.tabbed_header_text_hover_color.green }}, {{ settings.tabbed_header_text_hover_color.blue }};
    --tabbed-header-color-bubble: {{ settings.tabbed_header_bubble_color.red }}, {{ settings.tabbed_header_bubble_color.green }}, {{ settings.tabbed_header_bubble_color.blue }};

    --tabbed-header-gap: 15px;
    --tabbed-header-input-radius: 100px;

/*  ==============================
    THEME OVERRIDES
    ============================== */

    /* Typography */

    --heading-font-family: var(---font-family-heading);
    --heading-font-weight: var(---font-weight-heading);
    --heading-font-style: var(---font-style-heading);
    --heading-font-family--alternate: 'Libre Baskerville', serif;

    --subheading-color: {{ settings.subheading_color.red }}, {{ settings.subheading_color.green }}, {{ settings.subheading_color.blue }};

    --button-background: {{ settings.primary_button_background.red }}, {{ settings.primary_button_background.green }}, {{ settings.primary_button_background.blue }};
    --button-text-color: {{ settings.primary_button_text_color.red }}, {{ settings.primary_button_text_color.green }}, {{ settings.primary_button_text_color.blue }};

    --heading-xxsmall-font-size: {% if settings.heading_font_size == 'small' %}10px{% else %}11px{% endif %};
    --heading-xsmall-font-size: {% if settings.heading_font_size == 'small' %}10px{% else %}11px{% endif %};
    --heading-small-font-size: {% if settings.heading_font_size == 'small' %}11px{% elsif settings.heading_font_size == 'medium' %}12px{% else %}13px{% endif %};
    --heading-large-font-size: {% if settings.heading_font_size == 'small' %}32px{% elsif settings.heading_font_size == 'medium' %}36px{% else %}40px{% endif %};
    --heading-h1-font-size: {% if settings.heading_font_size == 'small' %}32px{% elsif settings.heading_font_size == 'medium' %}36px{% else %}40px{% endif %};
    --heading-h2-font-size: {% if settings.heading_font_size == 'small' %}28px{% elsif settings.heading_font_size == 'medium' %}30px{% else %}32px{% endif %};
    --heading-h3-font-size: {% if settings.heading_font_size == 'small' %}26px{% elsif settings.heading_font_size == 'medium' %}26px{% else %}28px{% endif %};
    --heading-h4-font-size: {% if settings.heading_font_size == 'small' %}22px{% elsif settings.heading_font_size == 'medium' %}24px{% else %}26px{% endif %};
    --heading-h5-font-size: {% if settings.heading_font_size == 'small' %}18px{% elsif settings.heading_font_size == 'medium' %}20px{% else %}22px{% endif %};
    --heading-h6-font-size: {% if settings.heading_font_size == 'small' %}16px{% elsif settings.heading_font_size == 'medium' %}16px{% else %}18px{% endif %};

    --font-size-h1: 32px;
    --font-size-h2: 28px;
    --font-size-h3: 18px;
    --font-size-h4: 18px;
    --font-size-h5: 14px;
    --font-size-h6: 12px;
    
    --text-font-family: var(---font-family-body);
    --text-font-weight: var(---font-weight-body);
    --text-font-style: var(---font-style-body);
    --text-font-bold-weight: var(---font-weight-body--bold);
    
    --text-font-family--alternate: 'Lexend Deca', sans-serif;
    --text-font-weight: var(---font-weight-body--alternate);
    --text-font-style: var(---font-style-body--alternate);
    --text-font-bold-weight: var(---font-weight-body--alternate--bold);

    --border-color: var(---color-line--rgb);
    --border-color-darker: var(---color-line--dark--rgb);

    --product-low-stock-text-color: var(---color--warning--rgb);
    --product-no-stock-text-color: var(---color--danger--rgb);
    --product-in-stock-text-color: var(---color--success--rgb);
    
    --block-border-radius: 12px;
    --block-border-radius-sm: 8px;

    --vertical-breather-large: {% if settings.vertical_spacing == 'small' %}28px{% elsif settings.vertical_spacing == 'medium' %}36px{% else %}48px{% endif %};

}

@media screen and (min-width: 741px) {
  :root {

    --vertical-breather-large: {% if settings.vertical_spacing == 'small' %}40px{% elsif settings.vertical_spacing == 'medium' %}64px{% else %}80px{% endif %};
    ---font-size-subheading-small: 13px;

    --font-size-body: 16px;
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-lg: 18px;

    --font-size-h1: 64px;
    --font-size-h2: 44px;
    --font-size-h3: 28px;
    --font-size-h4: 24px;
    --font-size-h5: 16px;
    --font-size-h6: 16px;

  }
}

@media screen and (min-width: 1200px) {
  :root {
    --vertical-breather-large: {% if settings.vertical_spacing == 'small' %}48px{% elsif settings.vertical_spacing == 'medium' %}100px{% else %}120px{% endif %};
  }
}

@media screen and (min-width: 1600px) {
  :root {
    --vertical-breather-large: {% if settings.vertical_spacing == 'small' %}48px{% elsif settings.vertical_spacing == 'medium' %}120px{% else %}140px{% endif %};
  }
}

/*  ==============================
    @Font-Face Rules
    ============================== */

  @font-face {
    font-family: 'Lexend';
    src:
      url({{ 'Lexend-VariableFont_wght.ttf' | asset_url }}) format('woff');
    font-weight: 100 1000;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Lexend Deca';
    src:
      url({{ 'LexendDeca-VariableFont_wght.ttf' | asset_url }}) format('woff');
    font-weight: 100 1000;
    font-style: normal;
    font-display: swap;
  }

{% endstyle %}