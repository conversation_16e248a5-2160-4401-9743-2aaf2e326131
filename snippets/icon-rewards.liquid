{%- capture icon_class -%}icon icon--loyalty icon--{{ icon }} {% if inline %}icon--inline{% endif %} {% if direction_aware %}icon--direction-aware{% endif %} {{ class }}{%- endcapture -%}

  {%- case icon -%}
    {%- when 'points' -%}
      <svg focusable="false" width="{{ width | default: 100 }}" height="{{ height | default: 100 }}" viewBox="0 0 100 100" fill="none" class="{{ icon_class }}" role="presentation">
        <path d="M39.7727 19.0752L50.515 48.6091L79.5455 59.5376L50.515 70.4661L39.7727 100L29.0305 70.4661L0 59.5376L29.0305 48.6091L39.7727 19.0752Z" fill="currentColor"/>
        <path d="M78.125 0L84.0332 16.2436L100 22.2543L84.0332 28.265L78.125 44.5087L72.2168 28.265L56.25 22.2543L72.2168 16.2436L78.125 0Z" fill="currentColor"/>
      </svg>
    
    {%- when 'points-pretty' -%}
      <svg focusable="false" width="{{ width | default: 31 }}" height="{{ width | default: 31 }}" viewBox="0 0 {{ width | default: 31 }} {{ width | default: 31 }}" fill="none" class="{{ icon_class }}" role="presentation">
        <circle cx="15" cy="15" r="13" fill="#E3DED2" />
        <path
          d="M12.3295 7.37604L15.1898 15.2397L15.2683 15.4557L15.4835 15.5367L23.2399 18.4566L15.4835 21.3765L15.2683 21.4575L15.1898 21.6736L12.3295 29.5372L9.46933 21.6736L9.39075 21.4575L9.17561 21.3765L1.41919 18.4566L9.17561 15.5367L9.39075 15.4557L9.46933 15.2397L12.3295 7.37604Z"
          stroke="currentColor" />
        <path
          d="M24.2188 1.46277L25.5804 5.20644L25.659 5.42248L25.8741 5.50347L29.5808 6.89884L25.8741 8.29422L25.659 8.37521L25.5804 8.59125L24.2188 12.3349L22.8571 8.59125L22.7785 8.37521L22.5634 8.29422L18.8567 6.89884L22.5634 5.50347L22.7785 5.42248L22.8571 5.20644L24.2188 1.46277Z"
          stroke="currentColor" />
      </svg>

{%- endcase -%}