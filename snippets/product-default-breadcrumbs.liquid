{%- capture breadcrumbs -%}

  {%- capture level_1_handle -%}baby{%- endcapture -%}
  {%- capture level_2_handle -%}baby-sleepwear{%- endcapture -%}
  {%- capture level_3_handle -%}snap-zippered-rompers{%- endcapture -%}

  {%- assign level_1_collection = collections[level_1_handle] -%}
  {%- assign level_2_collection = collections[level_2_handle] -%}
  {%- assign level_3_collection = collections[level_3_handle] -%}

  {% if collections[level_1_collection] %}
    {%- assign level_2_collection = collections[level_1_collection] -%}
  {% else %}
    {%- assign level_2_collection = collections[level_1_collection] -%}
  {% endif %}

  {% if collections[level_2_handle] %}
    {%- assign level_2_collection = collections[level_2_handle] -%}
  {% else %}
    {%- assign level_2_collection = collections[level_2_handle] -%}
  {% endif %}

  {% if product.collection %}
    {%- assign level_3_collection = product.collection -%}
  {% else %}
    {%- assign level_3_collection = collections[level_3_handle] -%}
  {% endif %}

  
  {% comment %} LEVEL 1 {% endcomment %}

  {%- capture level_1_link -%}
    <a class="product-collection-breadcrumbs__link link text--subdued text--xxsmall" href="{{ routes.root_url }}">{{ 'general.breadcrumb.home' | t }}</a>
  {%- endcapture -%}


  {% comment %} LEVEL 2 {% endcomment %}

  {%- capture level_2_link -%}
    {%- if collection -%}
      <a class="product-collection-breadcrumbs__link link text--subdued text--xxsmall" href="{{ collection.url }}">{{- collection.title -}}</a>
    {%- endif -%}
  {%- endcapture -%}

  
  {% comment %} LEVEL 3 {% endcomment %}

  {%- capture level_3_link -%}
    <span class="product-collection-breadcrumbs__link text--subdued text--xxsmall" aria-current="page">{{ product.title }}</span>
  {%- endcapture -%}



  {% comment %} BREADCRUMBS {% endcomment %}

  {%- if level_1_link -%}
    {{ level_1_link }}
  {%- endif -%}

  {% if level_1_link != blank %}
    <span class="product-collection-breadcrumbs__divider text--subdued">/</span>
  {% endif %}

  {%- if level_1_link != blank and level_2_link != blank -%}
    {{ level_2_link }}
  {%- endif -%}

  {% if level_2_link != blank and level_3_link != blank %}
    <span class="product-collection-breadcrumbs__divider text--subdued">/</span>
  {% endif %}

  {%- if level_3_link != blank -%}
    {{ level_3_link }}
  {%- endif -%}


{%- endcapture -%}

{%- if breadcrumbs -%}
  <nav class="product-collection-breadcrumbs hidden-pocket" aria-label="{{ 'general.breadcrumb.title' | t }}">
    {{ breadcrumbs }}
  </nav>
{%- endif -%}