{%- assign product_title = product.title -%}

{%- assign color_label_list = 'general.label.color' | t | replace: ', ', ',' | downcase | split: ',' -%}
{%- assign color_white_label = 'general.label.white' | t | downcase -%}
{%- assign quick_buy_icon_name = 'quick-buy-' | append: settings.cart_icon | replace: '_', '-' -%}

{%- capture product_url -%}
  {%- if settings.products_in_collection -%}
    {{- product.url | within: collection -}}
  {%- else -%}
    {{- product.url -}}
  {%- endif -%}
{%- endcapture -%}

{%- if product_url contains '?' -%}
  {%- assign product_url_contains_query = true -%}
{%- else -%}
  {%- assign product_url_contains_query = false -%}
{%- endif -%}

{%- if collection != blank -%}
  {%- assign product_url = product.url | within: collection -%}
{%- else -%}
  {%- assign product_url = product.url -%}
{%- endif -%}

{% comment %} 
--------------------------------------------------------------------------------
Preorder
--------------------------------------------------------------------------------
{% endcomment %}

{%- capture pre_order_one_tag -%}
  {%- if settings.pre_order_one_tag != blank -%}
    {{- settings.pre_order_one_tag -}}
  {%- endif -%}
{%- endcapture -%}

{%- capture pre_order_two_tag -%}
  {%- if settings.pre_order_two_tag != blank -%}
    {{- settings.pre_order_two_tag -}}
  {%- endif -%}
{%- endcapture -%}

{%- assign product_is_preorder = false -%}
{% if product.tags contains pre_order_one_tag or product.tags contains pre_order_two_tag %}
  {%- assign product_is_preorder = true -%}
{% endif %}

{%- assign unavailable_for_returns = false -%}
{% if product.tags contains settings.loop_returns_unavailable_tag and settings.loop_returns_unavailable_message %}
  {%- assign unavailable_for_returns = true -%}
{% endif %}

{% comment %} 
  
  --------------------------------------------------------------------------------
  Sub-Brand
  --------------------------------------------------------------------------------
  page_sub_brand - Sub-brand assigned to the current Page.
  product_sub_brand - Sub-brand assigned to the Product.
  collection_sub_brand - Sub-brand assigned to the Collection the Product is in.
  sub_brand - Stores the sub-brand Metaobject.

{% endcomment %}

{%- if page.metafields.sub_brand.sub_brand != blank -%}
  {%- assign page_sub_brand = page.metafields.sub_brand.sub_brand %}
{%- endif -%}
 
{%- if product.metafields.sub_brand.sub_brand != blank -%}
  {%- assign product_sub_brand = product.metafields.sub_brand.sub_brand %}
{%- endif -%}

{%- if collection.metafields.sub_brand.sub_brand != blank -%}
  {%- assign collection_sub_brand = collection.metafields.sub_brand.sub_brand -%}
{%- endif -%}

{%- if page_sub_brand -%}
  {% comment %} {%- assign sub_brand = page_sub_brand -%} {% endcomment %}
{%- endif -%}

{%- if collection_sub_brand -%}
  {%- assign sub_brand = collection_sub_brand -%}
{%- endif -%}

{%- if sub_brand != blank -%}
  {%- if product.metafields.kyte_living.alternate_title != blank -%}
    {%- assign product_title = product.metafields.kyte_living.alternate_title -%}
  {%- endif -%}
{%- endif -%}

{% comment %} 
--------------------------------------------------------------------------------
TIERS
--------------------------------------------------------------------------------
If the product has a tier metafield tag set, find this tag and store it to show/hide the product form later.
{% endcomment %}

{% assign customer_has_tier_tag = false %}

{%- for i in (0..7) -%}

  {% capture tier_tag %}tier_{{ i }}_tag{% endcapture %}
  {% capture tier_icon %}tier_{{ i }}_icon{% endcapture %}
  {% capture tier_simple_icon %}tier_{{ i }}_simple_icon{% endcapture %}
  {% capture tier_icon_enabled_product %}tier_{{ i }}_icon_enabled_product{% endcapture %}
  {% capture tier_icon_enabled_collection %}tier_{{ i }}_icon_enabled_collection{% endcapture %}
  {% capture tier_message_heading %}tier_{{ i }}_message_heading{% endcapture %}
  {% capture tier_message_text %}tier_{{ i }}_message_text{% endcapture %}
  {% capture tier_cta_text %}tier_{{ i }}_cta_text{% endcapture %}
  {% capture tier_cta_link %}tier_{{ i }}_cta_link{% endcapture %}
  
  {% if product.metafields.custom.exclusive_access.value contains settings[tier_tag] %}
    {%- assign product_has_tier_tag = true -%}
    {%- break -%}
  {% endif %}

{%- endfor -%}

{% if customer.tags contains settings[tier_tag] %}
  {% assign customer_has_tier_tag = true %}
{% else %}
  {% assign customer_has_tier_tag = false %}
{% endif %}

{%- if product_has_tier_tag == true -%}

{%- else -%}

  {% assign tier_tag = blank %}
  {% assign tier_icon = blank %}
  {% assign tier_simple_icon = blank %}
  {% assign tier_icon_enabled_product = blank %}
  {% assign tier_icon_enabled_collection = blank %}
  {% assign tier_message_heading = blank %}
  {% assign tier_message_text = blank %}
  {% assign tier_cta_text = blank %}
  {% assign tier_cta_link = blank %}

{%- endif -%}

{% comment %} Tier Display - how the tier is displayed on the product card {% endcomment %}

{%- if section.settings.tier_display != settings.tier_display -%}
  {%- assign tier_display = section.settings.tier_display -%}
{%- else -%}
  {%- assign tier_display = settings.tier_display -%}
{%- endif -%}

{% assign show_quick_add = false %}
{% if product.available == true and settings.show_product_grid_swatches == true %}
  
  {% assign show_quick_add = true %}

  {% if product_has_tier_tag == true %}
    {% assign show_quick_add = false %}
    {% if customer_has_tier_tag == true %}
      {% assign show_quick_add = true %}
    {% endif %}
  {% endif %}

  {%- assign quick_add_disable_tags = settings.tag_disable_quickadd -%}
  {%- assign quick_add_disable_tags_array = quick_add_disable_tags | split: "," -%}

  {%- assign stop_quick_add_hide = false -%}

  {%- for quick_add_hide_tag in quick_add_disable_tags_array -%}
    {%- assign quick_add_hide_tag_strip = quick_add_hide_tag | strip -%}
    {%- for tag in product.tags -%}
      {%- if quick_add_hide_tag_strip == tag -%}
        {%- assign show_quick_add = false -%}
        {%- assign stop_quick_add_hide = true -%}
        {%- break -%}
      {%- endif -%}
    {%- endfor -%}
    {%- if stop_quick_add_hide == true -%}
      {%- break -%}
    {%- endif -%}
  {%- endfor -%}

{% endif %}


{% comment %} 
--------------------------------------------------------------------------------
Sale Stop
--------------------------------------------------------------------------------
{% endcomment %}

{%- if collection.metafields.custom.sale_stop != blank -%}
  {%- assign sale_stop = collection.metafields.custom.sale_stop.value -%}
{%- elsif product.metafields.custom.sale_stop != blank -%}
  {%- assign sale_stop = product.metafields.custom.sale_stop.value -%}
{%- endif -%}

{%- if sale_stop != blank -%}
  {%- if sale_stop.enable == true -%}
    {%- assign show_quick_add = false -%}
  {%- endif -%}
{%- endif -%}

{% comment %} 
--------------------------------------------------------------------------------
Additional text Metafields
--------------------------------------------------------------------------------
{% endcomment %}

{%- if show_additional_text_fields == true and reduced_content != true -%}
  {%- capture additional_text_fields -%}
    {%- render 'product-item-additional-text-field', field: product.metafields.custom.fabric -%}
  {%- endcapture -%}
{%- endif -%}

{% comment %} 
--------------------------------------------------------------------------------
Aspect Ratio
--------------------------------------------------------------------------------
Will auto-detect the aspect ratio from the product's sub-brand.
In sections where different sub-brand products can be mixed together,
use the setting on the section to set the aspect ratio for all.
{% endcomment %}

{%- capture aspect_ratio_class -%}
  {%- render 'aspect-ratio-class', sub_brand: sub_brand, aspect_ratio: aspect_ratio -%}
{%- endcapture -%}

<product-item class="product-item {% unless product.available %}product-item--sold-out{% endunless %} product-item--swatch-buttons {% if unavailable_for_returns %}loop--hide{% endif %}" {% if reveal %}reveal{% endif %}>

{% capture product_item_content %}

  {%- capture product_labels -%}
    {% 
      render 'product-label-list', 
      classes: "product-item__label-list", 
      product: product, 
      product_has_tier_tag: product_has_tier_tag,
      tier_message_heading: settings[tier_message_heading],
      sale_stop: sale_stop,
      section: section
    %}
  {%- endcapture -%}

<div class="product-item__inner">
  <div class="product-item__image-wrapper {% if settings.show_secondary_image and product.media.size > 1 and hide_secondary_image != true %}product-item__image-wrapper--multiple{% endif %}">

    {%- comment -%} 
      Product Badge
    {%- endcomment -%}

    {%- capture badge_sizes_attribute -%}{% if block.settings.content != blank %}50vw{% else %}100vw{% endif %}{%- endcapture -%}

    {%- assign product_badge = blank -%}

    {% if settings[tier_icon_enabled_collection] == true and product.metafields.custom.exclusive_access.value contains settings[tier_tag] and settings[tier_icon] != blank %}
      {%- assign product_badge = settings[tier_icon] -%}
    {% endif %}

    {% if product_badge != blank and tier_display == "badge" %}
      {{ product_badge | image_url: width: 160 | image_tag: loading: 'lazy', sizes: badge_sizes_attribute, widths: '160', class: 'product-item__badge', data-media-id: product.featured_media.id }}
    {% endif %} 
      
    {%- if product_labels != blank and reduced_content != true -%}
      {{- product_labels -}}
    {%- endif -%}

    <a href="{{ product_url }}" data-instant class="product-item__aspect-ratio aspect-ratio {{ aspect_ratio_class }}" style="padding-bottom: {{ 100.0 | divided_by: product.featured_media.preview_image.aspect_ratio }}%; --aspect-ratio: {{ product.featured_media.preview_image.aspect_ratio }}">
      {%- if product.featured_media -%}
        {{ product.featured_media | image_url: width: product.featured_media.width | image_tag: loading: 'lazy', sizes: sizes_attribute, widths: '200,300,400,500,600,700,800,900,1000,1100,1200', class: 'product-item__primary-image', data-media-id: product.featured_media.id }}
      {%- endif -%}

      {%- if settings.product_color_display == 'swatch' -%}
        {%- for color_label in color_label_list -%}
          {%- if product.options_by_name[color_label] != blank -%}
			      {%- for color_option in product.options_by_name[color_label].values -%}
       		    {%- for variant in product.variants -%}
      			    {%- if variant.options contains color_option and variant.featured_media != blank -%}
          		    {%- unless variant.featured_media == product.featured_media -%}
                    {{ variant.featured_media | image_url: width: variant.featured_media.width | image_tag: loading: 'lazy', sizes: sizes_attribute, widths: '200,300,400,500,600,700,800,900,1000,1100,1200', class: 'product-item__primary-image', data-media-id: variant.featured_media.id, hidden: true }}
      				      {% break %}
          		    {%- endunless -%}
      			    {%- endif -%}
              {%- endfor -%}
	          {%- endfor -%}
          {%- endif -%}
        {%- endfor -%}
      {%- endif -%}

      {%- if settings.show_secondary_image and product.media.size > 1 and hide_secondary_image != true -%}
        {%- assign next_media = product.media[product.featured_media.position] | default: product.media[1] -%}

        {%- case next_media.media_type -%}

          {%- when 'image' -%}
            
            {{- next_media | image_url: width: next_media.width | image_tag: loading: 'lazy', sizes: sizes_attribute, widths: '200,300,400,500,600,700,800,900,1000,1100,1200', class: 'product-item__secondary-image' -}}
          
          {%- when 'video' -%}
            
            <native-video class="product-item__secondary-image video-wrapper video-wrapper--native" style="--aspect-ratio: {{ next_media.aspect_ratio }}">
              {{- next_media | video_tag: image_size: '1024x', controls: false, autoplay: true, muted: true, loop: true -}}
            </native-video>

        {%- endcase -%}
        
      {%- endif -%}
    </a>

    {%- if request.page_type != 'password' and settings.product_add_to_cart and product.available and reduced_content != true and show_cta != true -%}
      {%- if product.variants.size == 1 -%}
        {%- capture form_id -%}product_form_{{ section.id }}_{{ block.id }}_{{ product.id }}_{% increment product_form_index %}{%- endcapture -%}
        {%- form 'product', product, is: 'product-form', id: form_id, class: 'product-item__quick-form' -%}
          <input type="hidden" name="quantity" value="1">
          <input type="hidden" name="id" value="{{ product.first_available_variant.id }}">
          <button is="loader-button" type="submit" class="button button--outline button--text button--full {% if section.settings.desktop_products_per_row >= 5 %}button--small{% endif %} hidden-touch">{{ 'collection.product.add_to_cart_short' | t }}</button>
          <button type="submit" class="product-item__quick-buy-button hidden-no-touch">
            <span class="visually-hidden">{{ 'collection.product.add_to_cart_short' | t }}</span>
            {%- render 'icon' with quick_buy_icon_name -%}
          </button>
        {%- endform -%}
      {%- else -%}
        {%- comment -%}
        IMPLEMENTATION NOTE: Depending on the device we show a different icon or open a different mode (either popover or drawer)
        {%- endcomment -%}

        <div class="product-item__quick-form">
          {% comment %}
          <button is="toggle-button" loader aria-controls="product-{{ section.id }}-{{ block.id }}-{{ product.id }}-drawer" aria-expanded="false" class="button button--outline button--text button--full {% if section.settings.desktop_products_per_row >= 5 %}button--small{% endif %} hidden-touch hidden-phone">{{ 'collection.product.quick_view' | t }}</button>
          <button is="toggle-button" aria-controls="product-{{ section.id }}-{{ block.id }}-{{ product.id }}-drawer" aria-expanded="false" class="product-item__quick-buy-button hidden-no-touch hidden-phone">
            <span class="visually-hidden">{{ 'collection.product.quick_view' | t }}</span>
            {%- render 'icon' with quick_buy_icon_name -%}
          </button>
          {% endcomment %}

          <button is="toggle-button" aria-controls="product-{{ section.id }}-{{ block.id }}-{{ product.id }}-popover" aria-expanded="false" class="product-item__quick-buy-button hidden-tablet-and-up">
            <span class="visually-hidden">{{ 'collection.product.quick_view' | t }}</span>
            {%- render 'icon' with quick_buy_icon_name -%}
          </button>
        </div>

        <quick-buy-popover id="product-{{ section.id }}-{{ block.id }}-{{ product.id }}-popover" href="{{ product_url }}" class="popover popover--quick-buy hidden-tablet-and-up"></quick-buy-popover>
        {% comment %} <quick-buy-drawer id="product-{{ section.id }}-{{ block.id }}-{{ product.id }}-drawer" href="{{ product_url }}" class="drawer drawer--large drawer--quick-buy hidden-phone"></quick-buy-drawer> {% endcomment %}
      {%- endif -%}
    {%- endif -%}
  </div>

  <div class="product-item__info {% if show_cta %}product-item__info--with-button{% endif %} {% if reduced_font_size %}text--small{% endif %}">
    <div class="product-item-meta">
      {%- if settings.show_vendor -%}
        {%- assign vendor_handle = product.vendor | handle -%}
        {%- assign collection_for_vendor = collections[vendor_handle] -%}

        {%- unless collection_for_vendor.empty? -%}
          <a class="product-item-meta__vendor heading heading--xsmall" href="{{ collection_for_vendor.url }}" data-instant>{{ product.vendor }}</a>
        {%- else -%}
          <a class="product-item-meta__vendor heading heading--xsmall" href="{{ product.vendor | url_for_vendor }}" data-instant>{{ product.vendor }}</a>
        {%- endunless -%}
      {%- endif -%}

      <a href="{{ product_url }}" data-instant class="product-item-meta__title">{{ product_title }}</a>

      {%- if additional_text_fields != blank -%}
        <div class="product-item-meta__additional-text-fields">
          {{ additional_text_fields }}
        </div>
      {%- endif -%}

      {%- if settings.show_product_rating and reduced_content != true -%}
        <a class="product-item-meta__reviews-badge text--small" href="{{ product_url }}" data-instant>
          {% render 'product-rating-kyte', product: product %}
        </a>
      {%- endif -%}

      <div class="product-item-meta__price-list-container">
        <div class="price-list price-list--centered">
          {%- if product.price_varies and product.compare_at_price -%}
            {%- assign cheapest_variant = product.variants | sort: 'price' | first -%}

            {%- capture price_min -%}
              {%- if settings.currency_code_enabled -%}
                {{- cheapest_variant.price | money_with_currency -}}
              {%- else -%}
                {{- cheapest_variant.price | money -}}
              {%- endif -%}
            {%- endcapture -%}

            {%- if cheapest_variant.price < cheapest_variant.compare_at_price -%}
              <span class="price price--highlight">
                <span class="visually-hidden">{{ 'product.general.sale_price' | t }}</span>
                {{- 'collection.product.from_price_html' | t: price_min: price_min -}}
              </span>

              <span class="price price--compare">
                <span class="visually-hidden">{{ 'product.general.regular_price' | t }}</span>

                {%- if settings.currency_code_enabled -%}
                  {{- cheapest_variant.compare_at_price | money_with_currency -}}
                {%- else -%}
                  {{- cheapest_variant.compare_at_price | money -}}
                {%- endif -%}
              </span>
            {%- else -%}
              <span class="price">
                <span class="visually-hidden">{{ 'product.general.sale_price' | t }}</span>
                {{- 'collection.product.from_price_html' | t: price_min: price_min -}}
              </span>
            {%- endif -%}
          {%- elsif product.price < product.compare_at_price -%}
            <span class="price price--highlight">
              <span class="visually-hidden">{{ 'product.general.sale_price' | t }}</span>

              {%- if settings.currency_code_enabled -%}
                {{- product.price | money_with_currency -}}
              {%- else -%}
                {{- product.price | money -}}
              {%- endif -%}
            </span>

            <span class="price price--compare">
              <span class="visually-hidden">{{ 'product.general.regular_price' | t }}</span>
              {%- if settings.currency_code_enabled -%}
                {{- product.compare_at_price | money_with_currency -}}
              {%- else -%}
                {{- product.compare_at_price | money -}}
              {%- endif -%}
            </span>
          {%- elsif product.price_varies -%}
            {%- capture price_min -%}
              {%- if settings.currency_code_enabled -%}
                {{ product.price_min | money_with_currency }}
              {%- else -%}
                {{ product.price_min | money }}
              {%- endif -%}
            {%- endcapture -%}

            {%- capture price_max -%}
              {%- if settings.currency_code_enabled -%}
                {{- product.price_max | money_with_currency -}}
              {%- else -%}
                {{- product.price_max | money -}}
              {%- endif -%}
            {%- endcapture -%}

            <span class="price">
              <span class="visually-hidden">{{ 'product.general.sale_price' | t }}</span>
              {{- 'collection.product.from_price_html' | t: price_min: price_min, price_max: price_max -}}
            </span>
          {%- else -%}
            <span class="price">
              <span class="visually-hidden">{{ 'product.general.sale_price' | t }}</span>

              {%- if settings.currency_code_enabled -%}
                {{- product.price | money_with_currency -}}
              {%- else -%}
                {{- product.price | money -}}
              {%- endif -%}
            </span>
          {%- endif -%}

          {%- if product.selected_or_first_available_variant.unit_price_measurement -%}
            <div class="price price--block text--xsmall text--subdued">
              <div class="unit-price-measurement">
                <span class="unit-price-measurement__price">{{ product.selected_or_first_available_variant.unit_price | money }}</span>
                <span class="unit-price-measurement__separator">/</span>

                {%- if product.selected_or_first_available_variant.unit_price_measurement.reference_value != 1 -%}
                  <span class="unit-price-measurement__reference-value">{{ product.selected_or_first_available_variant.unit_price_measurement.reference_value }}</span>
                {%- endif -%}

                <span class="unit-price-measurement__reference-unit">{{ product.selected_or_first_available_variant.unit_price_measurement.reference_unit }}</span>
              </div>
            </div>
          {%- endif -%}
        </div>
      </div>

      {% comment %}
      {%- if settings.show_product_rating and reduced_content != true -%}
        <a class="product-item-meta__reviews-badge text--small" href="{{ product_url }}" data-instant>
          {%- render 'product-rating', product: product -%}
        </a>
      {%- endif -%}
      {% endcomment %}

      {%- if settings.product_color_display != 'hide' and reduced_content != true -%}
        {%- for color_label in color_label_list -%}
          {%- if product.options_by_name[color_label] != blank -%}
            {%- assign product_option = product.options_by_name[color_label] -%}

            {%- case settings.product_color_display -%}
              {%- when 'count' -%}
                <p class="product-item-meta__color-count text--small text--subdued">{{- 'collection.product.available_colors_count' | t: count: product_option.values.size -}}</p>

              {%- when 'swatch' -%}
                <div class="product-item-meta__swatch-list color-swatch-list color-swatch-list--mini">
                  {%- assign variant_option = 'option' | append: product_option.position -%}
                  {%- assign color_swatch_config = settings.color_swatch_config | newline_to_br | split: '<br />' -%}

                  {%- capture color_name -%}{{ section.id }}-{{ block.id }}-{{ product.id }}{%- endcapture -%}

                  {%- for value in product_option.values -%}
                    {%- capture color_id -%}{{ color_name }}-{{ forloop.index }}{%- endcapture -%}
                    {%- assign color_value_downcase = value | downcase -%}
                    {%- assign variant_for_value = product.variants | where: variant_option, value | first -%}

                    <div class="color-swatch {% if color_white_label == color_value_downcase %}color-swatch--white{% endif %}">
                      <input class="color-swatch__radio visually-hidden" type="radio" name="{{ color_name }}" id="{{ color_id }}" value="{{ value | escape }}" {% if product_option.selected_value == value %}checked="checked"{% endif %} data-variant-id="{{ variant_for_value.id }}" {% if variant_for_value.featured_media %}data-variant-featured-media="{{ variant_for_value.featured_media.id }}"{% endif %}>
                      <label class="color-swatch__item" for="{{ color_id }}" style="{% render 'color-swatch-style', swatch: value.swatch, color_swatch_config: color_swatch_config, value: value %}">
                        <span class="visually-hidden">{{ value }}</span>
                      </label>
                    </div>
                  {%- endfor -%}
                </div>
              {%- endcase -%}
            {%- break -%}
          {%- endif -%}
        {%- endfor -%}
      {%- endif -%}
    </div>
  </div>
</div>

  {%- if show_quick_add == true -%}
      
    <div class="product-item__quick-add hidden-phone {% if product.tags contains "Not For Sale" %}hidden{% endif %}">

      {%- if product.variants.size == 1 -%}
        {%- capture form_id -%}product_form_{{ section.id }}_{{ block.id }}_{{ product.id }}_{% increment product_form_index %}{%- endcapture -%}
        {%- form 'product', product, is: 'product-form', id: form_id -%}
          <input type="hidden" name="quantity" value="1">
          <input type="hidden" name="id" value="{{ product.first_available_variant.id }}">

          <button 
            type="submit" 
            is="loader-button"
            class="button button--tiny button--full button--secondary product-item__cta product-item__cta--single-variant {% if variant.available == false %}is-disabled{% endif %}"
            {% if variant.available == false %}disabled{% endif %}
            >
                    
              <span class="button__text">{{ 'collection.product.add_to_cart_short' | t }}</span>
              <span class="button__icon">{%- render 'icon' with 'plus' -%}</span>
                    
          </button>

          {% comment %} Bundles  {% endcomment %}

          {%- if product.metafields.bundle.bundle_product != blank -%}
            {%- assign bundle_components = product.metafields.bundle.bundle_product.value.metafields.bundle.components.value -%}
            {%- assign bundle_product = product.metafields.bundle.bundle_product.value -%}
          {%- elsif product.metafields.bundle.components != blank -%}
            {%- assign bundle_components = product.metafields.bundle.components.value -%}
          {%- endif -%}

          {%- for component_product in bundle_components -%}
            <input type='hidden' name='properties[Item {{ forloop.index }}]' value='{{ component_product.title | escape }}'>
          {%- endfor -%}

          {% comment %} End Bundles  {% endcomment %}
          
        {%- endform -%}
      {%- else -%}

        {% capture product_item_buttons %}

          {% for option in product.options_with_values %}
            {% if option.name == "Size" or option.name == "size" %}
              {% assign product_option_for_swatches = option %}
            {% endif %}
          {% endfor %}

          {%- assign variant_option = 'option' | append: product_option_for_swatches.position -%}
          {%- assign variant_for_value = product.variants | where: variant_option, value | first -%}

          <div class="block-swatch-list block-swatch-list--small {% if product.tags contains "Not For Sale" %}hidden{% endif %}">

            {%- for variant in product.variants -%}

              {%- form 'product', product, is: 'product-form', id: form_id, class: 'product-item__quick-add-form' -%}
                <input type="hidden" class="pid" value="{{product.id}}">
                <input type="hidden" class="ptitle" value="{{product_title | remove: "'" | remove: '"'}}">
                <input type="hidden" class="pprice" value="{{variant.price | times: 0.01}}">
                <input type="hidden" class="pbrand" value="{{product.vendor | remove: "'" | remove: '"'}}">
                <input type="hidden" class="ptype" value="{{product.type | remove: "'" | remove: '"'}}">
                <input type="hidden" class="pcategory_id" value="{{ product.collections.first.id }}">
                <input type="hidden" class="pcategory_name" value="{{ product.collections.first.title | remove: "'" | remove: '"' }}">
                <input type="hidden" class="pvtitle" value="{{ variant.title | remove: "'" | remove: '"' }}">
                <input type="hidden" class="psku" value="{{ variant.sku }}">
                <input type="hidden" name="quantity" value="1">
                <input type="hidden" name="id" value="{{ variant.id }}">

                {% comment %} Bundles  {% endcomment %}

                {%- if product.metafields.bundle.bundle_product != blank -%}
                  {%- assign bundle_components = product.metafields.bundle.bundle_product.value.metafields.bundle.components.value -%}
                  {%- assign bundle_product = product.metafields.bundle.bundle_product.value -%}
                {%- elsif product.metafields.bundle.components != blank -%}
                  {%- assign bundle_components = product.metafields.bundle.components.value -%}
                {%- endif -%}

                {%- for component_product in bundle_components -%}
                  <input type='hidden' name='properties[Item {{ forloop.index }}]' value='{{ component_product.title | escape }}'>
                {%- endfor -%}

                {% comment %} End Bundles  {% endcomment %}

                <button 
                  type="submit" 
                  is="loader-button"
                  class="block-swatch ee_quick_add_to_cart {% if variant.available == false %}is-disabled{% endif %}"
                  {% if variant.available == false %}disabled{% endif %}
                  >
                      
                  <div
                    class="block-swatch__radio visually-hidden"
                    type="radio"
                    data-variant-id="{{ variant.id }}"
                    {% if variant.available == false %}disabled{% endif %}
                    ></div>

                  <span class="block-swatch__item loader-button__text">
                    {{ variant[variant_option] }}
                  </span>
                      
                </button>

              {%- endform -%}

            {%- endfor -%}

          </div>
              
        {% endcapture %}

        {% if product_option_for_swatches != blank %}

          <p class="heading heading--xsmall text--subdued">{{ 'collection.product.quick_add' | t }}</p>
          {{ product_item_buttons }}
              
        {% endif %}

      {%- endif -%}
    </div>

    <div class="product-item__quick-add--mobile hidden-lap-and-up {% if product.tags contains "Not For Sale" %}hidden{% endif %}">
      <button type="button" is="toggle-button" aria-controls="product-{{ section.id }}-{{ block.id }}-{{ product.id }}-popover" aria-expanded="false" class="collapsible-toggle {% if reduced_content %}product-item__link text--strong{% else %}product-item__cta button button--primary{% endif %}">
        <span class="button__text">{{ 'collection.product.select_a_size' | t }}</span>
        <span class="button__icon">{%- render 'icon' with 'plus' -%}</span>
      </button>
    </div>

    {% if product_is_preorder == true %}
      <input type="hidden" form="{{ form_id }}" name="properties[Pre-order]" value="{{ 'product.general.special_messaging.pre_order_html' | t: date: settings.pre_order_one_date | strip_html }}">
    {% endif %}
  
  {%- endif -%} 

{% endcapture %}
<div class="product-item__content">
  {{ product_item_content }}
</div>

<div class="product-item__content-hover">
  <div class="product-item__content-hover__inner">
    {{ product_item_content }}
  </div>
</div>

  <quick-buy-popover id="product-{{ section.id }}-{{ block.id }}-{{ product.id }}-popover" href="{{ product_url }}" class="popover popover--quick-buy hidden-tablet-and-up"></quick-buy-popover>
  {% comment %} <quick-buy-drawer id="product-{{ section.id }}-{{ block.id }}-{{ product.id }}-drawer" href="{{ product_url }}" class="drawer drawer--large drawer--quick-buy hidden-phone"></quick-buy-drawer> {% endcomment %}
</product-item>