{%- if product.price_varies and product.compare_at_price -%}
  {%- assign cheapest_variant = product.variants | sort: 'price' | first -%}

  {%- capture price_min -%}
    {%- if settings.currency_code_enabled -%}
      {{- cheapest_variant.price | money_with_currency -}}
    {%- else -%}
      {{- cheapest_variant.price | money -}}
    {%- endif -%}
  {%- endcapture -%}

  {%- if cheapest_variant.price < cheapest_variant.compare_at_price -%}
    <span class="price price--highlight">
      <span class="visually-hidden">{{ 'product.general.sale_price' | t }}</span>
      {{- 'collection.product.from_price_html' | t: price_min: price_min -}}
    </span>

    <span class="price price--compare">
      <span class="visually-hidden">{{ 'product.general.regular_price' | t }}</span>

      {%- if settings.currency_code_enabled -%}
        {{- cheapest_variant.compare_at_price | money_with_currency -}}
      {%- else -%}
        {{- cheapest_variant.compare_at_price | money -}}
      {%- endif -%}
    </span>
  {%- else -%}
    <span class="price">
      <span class="visually-hidden">{{ 'product.general.sale_price' | t }}</span>
      {{- 'collection.product.from_price_html' | t: price_min: price_min -}}
    </span>
  {%- endif -%}
{%- elsif product.price < product.compare_at_price -%}
  <span class="price price--highlight">
    <span class="visually-hidden">{{ 'product.general.sale_price' | t }}</span>

    {%- if settings.currency_code_enabled -%}
      {{- product.price | money_with_currency -}}
    {%- else -%}
      {{- product.price | money -}}
    {%- endif -%}
  </span>

  <span class="price price--compare">
    <span class="visually-hidden">{{ 'product.general.regular_price' | t }}</span>
    {%- if settings.currency_code_enabled -%}
      {{- product.compare_at_price | money_with_currency -}}
    {%- else -%}
      {{- product.compare_at_price | money -}}
    {%- endif -%}
  </span>
{%- elsif product.price_varies -%}
  {%- capture price_min -%}
    {%- if settings.currency_code_enabled -%}
      {{ product.price_min | money_with_currency }}
    {%- else -%}
      {{ product.price_min | money }}
    {%- endif -%}
  {%- endcapture -%}

  {%- capture price_max -%}
    {%- if settings.currency_code_enabled -%}
      {{- product.price_max | money_with_currency -}}
    {%- else -%}
      {{- product.price_max | money -}}
    {%- endif -%}
  {%- endcapture -%}

  <span class="price">
    <span class="visually-hidden">{{ 'product.general.sale_price' | t }}</span>
    {{- 'collection.product.from_price_html' | t: price_min: price_min, price_max: price_max -}}
  </span>
{%- else -%}
  <span class="price">
    <span class="visually-hidden">{{ 'product.general.sale_price' | t }}</span>

    {%- if settings.currency_code_enabled -%}
      {{- product.price | money_with_currency -}}
    {%- else -%}
      {{- product.price | money -}}
    {%- endif -%}
  </span>
{%- endif -%}