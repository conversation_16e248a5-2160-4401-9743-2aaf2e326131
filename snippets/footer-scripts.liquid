<!-- 2. Loop -->
<script src="https://unpkg.com/@loophq/onstore-sdk@latest/dist/loop-onstore-sdk.js"></script>
<script> 
	LoopOnstore.init({ 
		key: "d43aa65fb868f5a30f9ac1cde150376cf9b87e67", 
		attach: ".checkout-button" 
	});
</script>

<!-- 3. Afterpay JavaScript Snippet (v1.0.2) -->
{% if template contains 'product' %}
  <script type="text/javascript">
  // Editable fields:
  var afterpay_min = 0.04;            // As per your Afterpay contract.
  var afterpay_max = 1000.00;         // As per your Afterpay contract.
  var afterpay_logo_theme = 'black'; // Can be 'colour', 'black' or 'white'.

  // Overrides:
  var afterpay_product_selector = '.product-meta__price-list-container .price-list';
  var afterpay_cart_integration_enabled = false;
  // var afterpay_cart_static_selector = '#cart-subtotal-selector';
  // var afterpay_variable_price_fallback_selector = '#ProductPrice';
  // var afterpay_variable_price_fallback = true; // Requires afterpay_product_selector
  var afterpay_show_currency_code = false;
  var afterpay_modal_responsive = true;
  // var afterpay_hide_range_decimals = true;
  // var afterpay_hide_lower_limit = false;
    var afterpay_hide_upper_limit = true;
   var afterpay_modal_open_icon = true; 

  //show an 'i' with circle instead of learn more to open the modal - to remove the underline add .afterpay-link-inner { text-decoration: none;}

  // Non-editable fields:
  var afterpay_shop_currency = {{ shop.currency | json }};
  var afterpay_shop_money_format = {{ shop.money_format | json }};
  var afterpay_shop_permanent_domain = {{ shop.permanent_domain | json }};
  var afterpay_theme_name = {{ theme.name | json }};
  var afterpay_product = {{ product | json }};
  var afterpay_current_variant = {{ product.selected_or_first_available_variant | json }};
  var afterpay_cart_total_price = {{ cart.total_price | json }};
  var afterpay_js_snippet_version = '1.0.2';
  </script>
  <script type="text/javascript" src="https://static.afterpay.com/shopify-afterpay-javascript.js"></script>
{% endif %}
<!-- End Shopify-Afterpay JavaScript Snippet (v1.0.2) -->