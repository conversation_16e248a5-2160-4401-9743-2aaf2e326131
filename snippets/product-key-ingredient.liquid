{%- if key_ingredient != blank  -%}

  {%- assign title = key_ingredient.title  | metafield_tag -%}
  {%- assign description = key_ingredient.description | metafield_tag -%}
  {%- assign icon = key_ingredient.icon -%}
  {%- assign icon_svg = key_ingredient.icon_svg | metafield_tag -%}

{%- endif -%}

{%- if reveal == blank -%}
  {%- assign reveal = true -%}
{%- endif -%}

{%- capture content -%}

  {%- capture icon_content -%}
    {%- if icon_svg != blank -%}
      {{ icon_svg }}
    {%- elsif icon != blank -%}
      {{ icon | image_url: width: 200 | image_tag: sizes: '200px', widths: '200,300,400', width: 50, height: 50, alt: icon.alt }}
    {%- endif -%}
  {%- endcapture -%}

  {%- if icon_content != blank -%}
    <div class="product-key-ingredient__icon">
      {{ icon_content }}
    </div>
  {%- endif -%}
  
  {%- capture key_ingredient_details -%}
    {%- if title != blank -%}
      <p class="product-key-ingredient__title">{{ title }}</p>
    {%- endif -%}
    {%- if description != blank -%}
      <div class="product-key-ingredient__description text--xsmall">{{ description }}</div>
    {%- endif -%}
  {%- endcapture -%}
  
  {%- if key_ingredient_details != blank -%}
    <div class="product-key-ingredient__details">
      {{ key_ingredient_details }}
    </div>
  {%- endif -%}

{%- endcapture -%}

{%- if content != blank -%}
  <div class="product-key-ingredient">
    <div class="product-key-ingredient__inner">
      {{ content }}  
    </div>
  </div>
{%- endif -%}

