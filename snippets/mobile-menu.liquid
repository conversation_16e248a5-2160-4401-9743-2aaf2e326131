{%- assign direction = 'ltr' -%}
{%- case request.locale.iso_code -%}
  {%- when 'ar' or 'arc' or 'dv' or 'fa' or 'ha' or 'he' or 'kwh' or 'ks' or 'ku' or 'ps' or 'ur' or 'yi' -%}
    {%- assign direction = 'rtl' -%}
{%- endcase -%}

{%- liquid

  assign sub_brands = metaobjects['sub_brand'].values

  for sub_brand in sub_brands

    # Hides this sub-brand in the menu if it's not active.
    if settings.subbrands_hide_inactive == true and sub_brand.active != true
      continue
    endif

    if request.page_type == "page"
      if page.metafields.sub_brand.sub_brand
        assign current_sub_brand = page.metafields.sub_brand.sub_brand.value
      endif
    elsif request.page_type == "collection"
      if collection.metafields.sub_brand.sub_brand
        assign current_sub_brand = collection.metafields.sub_brand.sub_brand.value
      endif
    elsif request.page_type == "product"
      if collection.metafields.sub_brand.sub_brand
        assign current_sub_brand = collection.metafields.sub_brand.sub_brand.value
      elsif product.metafields.sub_brand.sub_brand
        assign current_sub_brand = product.metafields.sub_brand.sub_brand.value
      endif
    endif
    if sub_brand == current_sub_brand
      assign active_sub_brand = sub_brand
      break
    endif
  endfor

  if active_sub_brand != blank
    assign logo_image = active_sub_brand.logo
    assign logo_svg = active_sub_brand.logo_svg
    assign logo_title = active_sub_brand.title
    if active_sub_brand.landing_page != blank
      assign logo_link = active_sub_brand.landing_page.value.url
    elsif active_sub_brand.link != blank
      assign logo_link = active_sub_brand.link.url
    endif
  else
    assign logo_image = settings.logo_image
    assign logo_svg = settings.logo_svg
    assign logo_link = routes.root_url
    assign logo_title = shop.name
  endif
  
-%}

<mobile-navigation append-body id="mobile-menu-drawer" class="drawer {% if direction == 'ltr' %}drawer--from-left{% endif %}">
  <span class="drawer__overlay"></span>

  <div class="drawer__header drawer__header--shadowed">

    <button type="button" class="drawer__close-button drawer__close-button--block tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
      {%- render 'icon' with 'close' -%}
    </button>

    {%- if logo_image != blank -%}
      <a class="drawer__logo-link" href="{{ logo_link }}">
        {%- if logo_svg != blank -%}
          {{ logo_svg }}
        {%- elsif logo_image != blank -%}
          {{- logo_image | image_url: width: logo_image.width | image_tag: loading: 'lazy', widths: '800,1200,1400,1600', class: 'drawer__logo-image' -}}
        {%- else -%}
          <span class="h3">{{ logo_title }}</span>
        {%- endif -%}
      </a>
    {%- endif -%}

  </div>

  <div class="drawer__content">
    <ul class="mobile-nav list--unstyled" role="list">
      {%- for link in menu.links -%}
        
        {%- assign link_title_downcase = link.title | strip | downcase -%}
        {%- assign mega_menu_block = '' -%}
        {%- assign mega_menu_images = '' -%}

        {%- assign color_link_group_handle = blank -%}
        {%- assign color_link_group = blank -%}
        {%- assign color_link_group_title = blank -%}

        {%- for block in section.blocks -%}
          {%- assign menu_item_downcase = block.settings.menu_item | strip | downcase -%}

          {%- if menu_item_downcase == link_title_downcase -%}
            {%- assign mega_menu_block = block -%}
            {%- break -%}
          {%- endif -%}
        {%- endfor -%}

        {%- if mega_menu_block != '' -%}

          {%- if mega_menu_block.settings.colors_enable -%}

            {%- assign color_link_group_handle = mega_menu_block.settings.colors_link_group_handle -%}
            {%- assign color_link_group = metaobjects.color_link_group[color_link_group_handle] -%}

            {%- capture color_link_group_title -%}
              {%- if color_link_group.title -%}
                {{ color_link_group.title }}
              {%- endif -%}
            {%- endcapture -%}

          {%- endif -%}

          {%- assign images_count = 0 -%}

          {%- if mega_menu_block.type == "mega_menu_products" -%}

            {%- capture mega_menu_images -%}
              {%- for i in (1..6) -%}
                
                {%- capture product_setting -%}product_{{ i }}{%- endcapture -%}
                {%- capture hide_market -%}product_{{ i }}_hide_market{%- endcapture -%}

                {%- assign product = mega_menu_block.settings[product_setting] -%}

                {%- if mega_menu_block.settings[hide_market] contains localization.country.iso_code -%}
                  {%- continue -%}
                {%- endif -%}

                {%- if product != blank -%}

                  {%- assign products_count = products_count | plus: 1 -%}

                  {%- capture image_push -%}
                    {%- assign menu_image = product.featured_image -%}
                    {{- menu_image | image_url: width: menu_image.width | image_tag: loading: 'lazy', sizes: '270px', sizes: '270,540,810', class: 'mobile-nav__image' -}}

                    <p class="mobile-nav__image-heading heading heading--xsmall">{{ product.title }}</p>

                    <span class="mobile-nav__image-text text--xsmall">
                      {% render 'price', product: product %}
                    </span>

                  {%- endcapture -%}

                  <a href="{{ product.url }}" class="mobile-nav__image-push">
                    {{- image_push -}}
                  </a>

                {%- endif -%}
              {%- endfor -%}
            {%- endcapture -%}

          {%- else -%}

            {%- capture mega_menu_images -%}
              {%- for i in (1..6) -%}
                {%- capture image_setting -%}image_{{ i }}{%- endcapture -%}

                {%- if mega_menu_block.settings[image_setting] != blank -%}
                  {%- assign images_count = images_count | plus: 1 -%}

                  {%- capture image_heading_setting -%}image_{{ i }}_heading{%- endcapture -%}
                  {%- capture image_text_setting -%}image_{{ i }}_text{%- endcapture -%}
                  {%- capture image_link_setting -%}image_{{ i }}_link{%- endcapture -%}

                  {%- capture image_push -%}
                    {%- assign menu_image = mega_menu_block.settings[image_setting] -%}
                    {{- menu_image | image_url: width: menu_image.width | image_tag: loading: 'lazy', sizes: '270px', sizes: '270,540,810', class: 'mobile-nav__image' -}}

                    {%- if mega_menu_block.settings[image_heading_setting] != '' -%}
                      <p class="mobile-nav__image-heading heading heading--xsmall">{{ mega_menu_block.settings[image_heading_setting] }}</p>
                    {%- endif -%}

                    {%- if mega_menu_block.settings[image_text_setting] != '' -%}
                      <span class="mobile-nav__image-text text--xsmall">{{ mega_menu_block.settings[image_text_setting] }}</span>
                    {%- endif -%}
                  {%- endcapture -%}

                  {%- if mega_menu_block.settings[image_link_setting] != blank -%}
                    <a href="{{ mega_menu_block.settings[image_link_setting] }}" class="mobile-nav__image-push">
                      {{- image_push -}}
                    </a>
                  {%- else -%}
                    <div class="mobile-nav__image-push">
                      {{- image_push -}}
                    </div>
                  {%- endif -%}
                {%- endif -%}
              {%- endfor -%}
            {%- endcapture -%}

          {%- endif -%}
          
        {%- endif -%}

        <li class="mobile-nav__item" data-level="1">
          {%- if link.links.size > 0 or mega_menu_images != blank or mega_menu_products != blank -%}
            <button is="toggle-button" class="mobile-nav__link heading {% if settings.heading_text_transform == 'uppercase' %}h6{% else %}h5{% endif %}" aria-controls="mobile-menu-{{ forloop.index }}" aria-expanded="false">
              {{- link.title -}}
              <span class="animated-plus"></span>
            </button>

            <collapsible-content id="mobile-menu-{{ forloop.index }}" class="collapsible">

              {%- if link.links.size > 0 or color_link_group != blank -%}
                <ul class="mobile-nav list--unstyled" role="list">
                  
                  {%- for sub_link in link.links -%}
                    <li class="mobile-nav__item" data-level="2">
                      {%- if sub_link.links.size > 0 -%}
                        <button is="toggle-button" class="mobile-nav__link" aria-controls="mobile-menu-{{ forloop.parentloop.index }}-{{ forloop.index }}" aria-expanded="false">
                          {{- sub_link.title -}}
                          <span class="animated-plus"></span>
                        </button>

                        <collapsible-content id="mobile-menu-{{ forloop.parentloop.index }}-{{ forloop.index }}" class="collapsible">
                          <ul class="mobile-nav list--unstyled" role="list">
                            {%- for sub_sub_link in sub_link.links -%}
                              <li class="mobile-nav__item" data-level="3">
                                <a href="{{ sub_sub_link.url }}" class="mobile-nav__link">{{ sub_sub_link.title }}</a>
                              </li>
                            {%- endfor -%}
                          </ul>
                        </collapsible-content>
                      {%- else -%}
                        <a href="{{ sub_link.url }}" class="mobile-nav__link">{{ sub_link.title }}</a>
                      {%- endif -%}
                    </li>
                  {%- endfor -%}

                  {% comment %} Shop by Color {% endcomment %}

                  {%- if color_link_group != blank -%}

                    <li class="mobile-nav__item" data-level="2" data-test="{{ color_link_group }}">

                      <button is="toggle-button" class="mobile-nav__link" aria-controls="mobile-menu-{{ forloop.parentloop.index }}-{{ forloop.index }}--colors" aria-expanded="false">
                        {{- color_link_group_title -}}
                        <span class="animated-plus"></span>
                      </button>

                      <collapsible-content id="mobile-menu-{{ forloop.parentloop.index }}-{{ forloop.index }}--colors" class="collapsible">
                        <ul class="mobile-nav list--unstyled" role="list">

                          {%- for color_link in color_link_group.color_links.value -%}

                            {%- capture color_link_url -%}
                              {%- if color_link.link.value.url != blank -%}
                                {{ color_link.link.value.url }}
                              {%- else -%}
                                #
                              {%- endif -%}
                            {%- endcapture -%}

                            {%- capture color_link_text -%}
                              {%- if color_link.link.value.text != blank -%}
                                {{ color_link.link.value.text }}
                              {%- endif -%}
                            {%- endcapture -%}

                            {%- capture color_link_color -%}
                              {%- if color_link.color -%}
                                {{ color_link.color }}
                              {%- endif -%}
                            {%- endcapture -%}

                            {%- if color_link_text != blank -%}
                              <li class="mobile-nav__item" data-level="3">
                                <a class="color-link mobile-nav__link" href="{{ color_link_url }}" {% if color_link.new_tab %}target="_blank"{% endif %}>
                                  {%- if color_link_color -%}
                                    <span class="color-link__swatch" style="--swatch-color: {{ color_link.color }}"></span>
                                  {%- endif -%}
                                  <span class="color-link__title link--animated">{{ color_link_text }}</span>
                                </a>
                              </li>
                            {%- endif -%}

                          {%- endfor -%}

                        </ul>
                      </collapsible-content>

                    </li>

                  {%- endif -%}

                </ul>
              {%- endif -%}

              {%- if mega_menu_images != blank -%}
                <div class="mobile-nav__images-wrapper {% if images_count >= 3 %}mobile-nav__images-wrapper--tight{% endif %} hide-scrollbar">
                  <div class="mobile-nav__images-scroller">
                    {{- mega_menu_images -}}
                  </div>
                </div>
              {%- endif -%}

            </collapsible-content>
          {%- else -%}
            <a href="{{ link.url }}" class="mobile-nav__link heading {% if settings.heading_text_transform == 'uppercase' %}h6{% else %}h5{% endif %}">{{ link.title }}</a>
          {%- endif -%}
        </li>
      {%- endfor -%}

      {%- capture return_link_content -%}
        
        {%- if section.settings.logo_return_link != blank -%}
          {%- assign return_link = section.settings.logo_return_link.url -%}
        {%- else -%}
          {%- assign return_link = routes.root_url -%}
        {%- endif -%}

        {%- if section.settings.logo_return_text != blank -%}
          <li class="mobile-nav__item" data-level="1">
            <a href="{{ return_link }}" class="mobile-nav__link heading {% if settings.heading_text_transform == 'uppercase' %}h6{% else %}h5{% endif %}" target="_blank">
              <span class="mobile-nav__link-inner">
                <span class="mobile-nav__link-inner-text">{{ section.settings.logo_return_text }}</span>
                <span class="mobile-nav__link-inner-icon">{% render 'icon', icon: 'custom-external' %}</span>
              </span>
            </a>
          </li>
        {%- endif -%}

      {%- endcapture -%}

      {%- if return_link_content != blank -%}
        {{ return_link_content }}
      {%- endif -%}

      {% comment %} Sub Brand Links {% endcomment %}

      {%- assign sub_brand_menu = section.settings.sub_brand_links -%}

      {%- capture sub_brand_link_content -%}

        {%- for link in sub_brand_menu.links -%}

          {%- assign external_link = false -%}
          {%- if link.url contains "https://" or link.url contains "http://" -%}
            {%- assign external_link = true -%}
          {%- elsif link.url == '/' -%}
            {%- assign external_link = true -%}
          {%- elsif link.object.metafields.sub_brand.sub_brand != sub_brand -%}
            {%- assign external_link = true -%}
          {%- endif -%}

          <li class="mobile-nav__item" data-level="1">
            <a href="{{ link.url }}" class="mobile-nav__link heading {% if settings.heading_text_transform == 'uppercase' %}h6{% else %}h5{% endif %}" {% if external_link == true and section.settings.sub_brand_links_external_open_tab == true %}target="_blank"{% endif %}>
              <span class="mobile-nav__link-inner">
                <span class="mobile-nav__link-inner-text">{{- link.title -}}</span>
                {% if external_link == true and section.settings.sub_brand_links_external_icon == true %}
                  <span class="mobile-nav__link-inner-icon">{% render 'icon', icon: 'custom-external' %}</span>
                {% endif %}
              </span>
            </a>
          </li>

          <li class="header__linklist-item" data-item-title="{{ link.title | escape }}">
            <a class="header__linklist-link link--animated" {% render 'link-attributes', link: link %} href="{{ link.url }}" {% if link.links.size > 0 or mega_menu_block != '' %}aria-controls="desktop-menu-{{ forloop.index }}" aria-expanded="false"{% endif %} {% if external_link == true and section.settings.sub_brand_links_external_open_tab == true %}target="_blank"{% endif %}>
            </a>
          </li>
        {%- endfor -%}
        
      {%- endcapture -%}

      {%- if sub_brand_link_content != blank -%}
        {{ sub_brand_link_content }}
      {%- endif -%}

    </ul>

    {%- capture sub_brand_slider_content -%}

      {%- for sub_brand in sub_brands -%}

        {%- assign active = false -%}
        {% if active_sub_brand == sub_brand %}
          {%- assign active = true -%}
        {% endif %}

        {%- render 'sub-brand-slider-logo', sub_brand: sub_brand, active: active -%}

      {%- endfor -%}

      {%
        render 'sub-brand-slider-logo',
        title: shop.name,
        link: routes.root_url,
        logo_image: settings.logo_image,
        logo_svg: settings.logo_svg,
        active: main_logo_active
      %}

    {%- endcapture -%}

    <div class="sub-brand-slider">

      <div class="sub-brand-slider__title text-center">
        <span class="h5">{{ 'header.general.sub_brand_slider' | t }}</span>
      </div>

      {%- if sub_brand_slider_content != blank -%}
        <div class="sub-brand-slider__images-wrapper hide-scrollbar">
          <div class="sub-brand-slider__images-scroller">
            {{- sub_brand_slider_content -}}
          </div>
        </div>
      {%- endif -%}

    </div>

  {% if section.settings.secondary_menu != blank %}

    {% assign secondary_menu = section.settings.secondary_menu %}

    <ul class="mobile-secondary-menu mobile-nav list--unstyled">
      {% for link in secondary_menu.links %}
        <li class="mobile-secondary_menu__item mobile-nav__item" data-level="1">
          <a href="{{ link.url }}" class="mobile-secondary_menu__link mobile-nav__link">{{ link.title }}</a>
        </li>
      {% endfor %}
    </ul>
    
  {% endif %}

  </div>

  {%- if section.settings.show_locale_selector and shop.published_locales.size > 1 -%}
    {%- assign locale_selector = true -%}
  {%- endif -%}

  {%- if section.settings.show_country_selector and localization.available_countries.size > 1 -%}
    {%- assign country_selector = true -%}
  {%- endif -%}

  {%- if shop.customer_accounts_enabled or locale_selector or country_selector -%}
    <div class="drawer__footer drawer__footer--tight drawer__footer--bordered">
      <div class="mobile-nav__footer">
        {%- if shop.customer_accounts_enabled -%}
          <a class="icon-text" href="{% if customer %}{{ routes.account_url }}{% else %}{{ routes.account_login_url }}{% endif %}">
            {%- render 'icon' with 'custom-account' -%}
            {{- 'header.general.account' | t -}}
          </a>
        {%- endif -%}

        {%- if locale_selector or country_selector -%}
          {%- form 'localization', id: 'header-sidebar-localization-form', class: 'header__cross-border' -%}
            {%- if country_selector -%}
              <div class="popover-container">
                <input type="hidden" name="country_code" value="{{ localization.country.iso_code }}">
                <span class="visually-hidden">{{ 'header.general.country' | t }}</span>

                <button type="button" is="toggle-button" class="popover-button text--xsmall tap-area" aria-expanded="false" aria-controls="header-sidebar-localization-form-currency">
                  {{- localization.country.name }} ({{ localization.country.currency.iso_code }} {% if localization.country.currency.symbol %}{{ localization.country.currency.symbol }}{%- endif -%})
                  {%- render 'icon' with 'chevron', width: 9, height: 6, inline: true -%}
                </button>

                <popover-content id="header-sidebar-localization-form-currency" class="popover popover--top popover--small">
                  <span class="popover__overlay"></span>

                  <header class="popover__header">
                    <span class="popover__title heading h6">{{- 'header.general.country' | t -}}</span>

                    <button type="button" class="popover__close-button tap-area tap-area--large" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
                      {%- render 'icon' with 'close' -%}
                    </button>
                  </header>

                  <div class="popover__content">
                    <div class="popover__choice-list">
                      {%- for country in localization.available_countries -%}
                        <button type="submit" name="country_code" value="{{ country.iso_code }}" class="popover__choice-item">
                          <span class="popover__choice-label" {% if country.iso_code == localization.country.iso_code %}aria-current="true"{% endif %}>
                            {{- country.name }} ({{ country.currency.iso_code }} {% if country.currency.symbol %}{{ country.currency.symbol }}{%- endif -%})
                          </span>
                        </button>
                      {%- endfor -%}
                    </div>
                  </div>
                </popover-content>
              </div>
            {%- endif -%}

            {%- if locale_selector -%}
              <div class="popover-container">
                <input type="hidden" name="locale_code" value="{{ form.current_locale.iso_code }}">
                <span class="visually-hidden">{{ 'header.general.language' | t }}</span>

                <button type="button" is="toggle-button" class="popover-button text--xsmall tap-area" aria-expanded="false" aria-controls="header-sidebar-localization-form-locale">
                  {{- form.current_locale.endonym_name | capitalize -}}
                  {%- render 'icon' with 'chevron', width: 9, height: 6, inline: true -%}
                </button>

                <popover-content id="header-sidebar-localization-form-locale" class="popover popover--top popover--small">
                  <span class="popover__overlay"></span>

                  <header class="popover__header">
                    <span class="popover__title heading h6">{{- 'header.general.language' | t -}}</span>

                    <button type="button" class="popover__close-button tap-area tap-area--large" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
                      {%- render 'icon' with 'close' -%}
                    </button>
                  </header>

                  <div class="popover__content">
                    <div class="popover__choice-list">
                      {%- for locale in form.available_locales -%}
                        <button type="submit" name="locale_code" value="{{ locale.iso_code }}" class="popover__choice-item">
                          <span class="popover__choice-label" {% if locale.iso_code == form.current_locale.iso_code %}aria-current="true"{% endif %}>
                            {{- locale.endonym_name | capitalize -}}
                          </span>
                        </button>
                      {%- endfor -%}
                    </div>
                  </div>
                </popover-content>
              </div>
            {%- endif -%}
          {%- endform -%}
        {%- endif -%}
      </div>
    </div>
  {%- endif -%}
</mobile-navigation>