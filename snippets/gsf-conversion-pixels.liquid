<script>
    {% assign gsf_product_data_array = '' %}
    {% assign gsf_product = '' %}
    {% assign shop_currency = '' %}
    {% if template contains 'product' %}
    var gsf_conversion_data = {page_type : 'product', event : 'view_item', data : {product_data : [{variant_id : {{ product.selected_or_first_available_variant.id }}, product_id : {{ product.id }}, name : "{{ product.title | strip_html | escape | strip_newlines }}", price : {%if shop.money_format contains 'comma_separator'%}"{{ product.selected_or_first_available_variant.price | money_without_currency | replace:',','-' |  replace:'.',',' | replace:'-','.' | remove: ',' }}"{%elsif shop.money_format contains 'apostrophe_separator'%}"{{ product.selected_or_first_available_variant.price | money_without_currency | remove: "'" }}"{%else%}"{{ product.selected_or_first_available_variant.price | money_without_currency | remove: ',' }}"{%endif%}, currency : "{{ shop.currency }}", sku : "{{ product.selected_or_first_available_variant.sku | strip_html | escape }}", brand : "{{ product.vendor }}", variant : "{{ product.selected_or_first_available_variant.title | strip_html | escape }}", category : "{{ product.type | strip_html | escape }}", quantity : "{{ product.selected_or_first_available_variant.inventory_quantity }}" }], total_price : {%if shop.money_format contains 'comma_separator'%}"{{ product.selected_or_first_available_variant.price | money_without_currency | replace:',','-' |  replace:'.',',' | replace:'-','.' | remove: ',' }}"{%elsif shop.money_format contains 'apostrophe_separator'%}"{{ product.selected_or_first_available_variant.price | money_without_currency | remove: "'" }}"{%else%}"{{ product.selected_or_first_available_variant.price | money_without_currency | remove: ',' }}"{%endif%}, shop_currency : {% for currency in shop.enabled_currencies %}{% if currency == cart.currency %}{% assign shop_currency = currency.iso_code %}{% endif %}{% endfor %}{% if shop_currency != '' %}"{{ shop_currency }}"{% else %}"{{ shop.currency }}"{% endif %}}};
    {% elsif template contains 'cart' and cart.item_count > 0 %}
    var gsf_conversion_data = {page_type : 'cart', event : 'add_to_cart', data : {product_data : [{% for item in cart.items %}{variant_id : {{ item.variant_id }}, product_id : {{ item.product_id }}, name : "{{ item.title | strip_html | escape | strip_newlines }}", price : {%if shop.money_format contains 'comma_separator'%}"{{ item.variant.price | money_without_currency | replace:',','-' |  replace:'.',',' | replace:'-','.' | remove: ',' }}"{%elsif shop.money_format contains 'apostrophe_separator'%}"{{ item.variant.price | money_without_currency | remove: "'" }}"{%else%}"{{ item.variant.price | money_without_currency | remove: ',' }}"{%endif%}, currency : "{{ shop.currency }}", sku : "{{ item.variant.sku | strip_html | escape }}", brand : "{{ item.vendor }}", variant : "{{ item.variant.title | strip_html | escape }}", quantity : "{{ item.quantity }}", category : "{{ item.product.type | strip_html | escape }}"}{% if forloop.last != true %}, {% endif %}{% endfor %}], total_price : {%if shop.money_format contains 'comma_separator'%}"{{ cart.total_price | money_without_currency | replace:',','-' |  replace:'.',',' | replace:'-','.' | remove: ',' }}"{%elsif shop.money_format contains 'apostrophe_separator'%}"{{ cart.total_price | money_without_currency | remove: "'" }}"{%else%}"{{ cart.total_price | money_without_currency | remove: ',' }}"{%endif%}, shop_currency : {% for currency in shop.enabled_currencies %}{% if currency == cart.currency %}{% assign shop_currency = currency.iso_code %}{% endif %}{% endfor %}{% if shop_currency != '' %}"{{ shop_currency }}"{% else %}"{{ shop.currency }}"{% endif %}, num_items : "{{ cart.item_count }}"}};
    {% elsif template contains 'index' %}
    {% if collections.best_selling.products.size > 0 %}{% assign gsf_product = collections.best_selling.products | sort: 'best-sellers' %}
    {% elsif collections.best-selllers.products.size > 0 %}{% assign gsf_product = collections.best-selllers.products | sort: 'best-sellers' %}
    {% else collections.all.products.size > 0 %}{% assign gsf_product = collections.all.products   | sort: 'best-sellers' %}
    {% endif %}
    {% if gsf_product.size > 0 %}{% assign limit = gsf_product.size %}{% if limit > 2 %}{% assign limit = 2 %}{% endif %}{% for product in gsf_product limit:limit %}{% capture gsf_product_data_array %}{{ gsf_product_data_array  }}{variant_id : {{ product.selected_or_first_available_variant.id }}, product_id : {{ product.id }}, name : "{{ product.title | strip_html | escape | strip_newlines }}", price : {%if shop.money_format contains 'comma_separator'%}"{{ product.selected_or_first_available_variant.price | money_without_currency | replace:',','-' |  replace:'.',',' | replace:'-','.' | remove: ',' }}"{%elsif shop.money_format contains 'apostrophe_separator'%}"{{ product.selected_or_first_available_variant.price | money_without_currency | remove: "'" }}"{%else%}"{{ product.selected_or_first_available_variant.price | money_without_currency | remove: ',' }}"{%endif%}, currency : "{{ shop.currency }}", sku : "{{ product.selected_or_first_available_variant.sku | strip_html | escape }}", brand : "{{ product.vendor }}", variant : "{{ product.selected_or_first_available_variant.title | strip_html | escape }}", category : "{{ product.type | strip_html | escape }}"}{% if forloop.last != true %}, {% endif %}{% endcapture %}{% assign get_price = product.selected_or_first_available_variant.price  %}{% assign total_value_arr = total_value_arr | plus: get_price %}{% endfor %}
    var gsf_conversion_data = {page_type : 'home', event : 'page_view', data : {product_data : [{{ gsf_product_data_array }}], total_price :{%if shop.money_format contains 'comma_separator'%}"{{ total_value_arr | money_without_currency | replace:',','-' |  replace:'.',',' | replace:'-','.' | remove: ',' }}"{%elsif shop.money_format contains 'apostrophe_separator'%}"{{ total_value_arr | money_without_currency | remove: "'" }}"{%else%}"{{ total_value_arr | money_without_currency | remove: ',' }}"{%endif%}, shop_currency : {% for currency in shop.enabled_currencies %}{% if currency == cart.currency %}{% assign shop_currency = currency.iso_code %}{% endif %}{% endfor %}{% if shop_currency != '' %}"{{ shop_currency }}"{% else %}"{{ shop.currency }}"{% endif %}}};
    {% else %}
    var gsf_conversion_data = {page_type : 'home', event : 'page_view', data : {product_data : [], total_price : 0, shop_currency : {% for currency in shop.enabled_currencies %}{% if currency == cart.currency %}{% assign shop_currency = currency.iso_code %}{% endif %}{% endfor %}{% if shop_currency != '' %}"{{ shop_currency }}"{% else %}"{{ shop.currency }}"{% endif %}}};
    {% endif %}
    {% elsif template contains 'collection' and collection.products.size > 0%}
    {% assign limit = collection.products.size %}{% if limit > 4 %}{% assign limit = 4 %}{% endif %}{% for product in collection.products limit:limit %}{% capture gsf_product_data_array %}{{ gsf_product_data_array  }}{variant_id : {{ product.selected_or_first_available_variant.id }}, product_id : {{ product.id }}, name : "{{ product.title | strip_html | escape | strip_newlines }}", price : {%if shop.money_format contains 'comma_separator'%}"{{ product.selected_or_first_available_variant.price | money_without_currency | replace:',','-' |  replace:'.',',' | replace:'-','.' | remove: ',' }}"{%elsif shop.money_format contains 'apostrophe_separator'%}"{{ product.selected_or_first_available_variant.price | money_without_currency | remove: "'" }}"{%else%}"{{ product.selected_or_first_available_variant.price | money_without_currency | remove: ',' }}"{%endif%}, currency : "{{ shop.currency }}", sku : "{{ product.selected_or_first_available_variant.sku | strip_html | escape }}", brand : "{{ product.vendor }}", variant : "{{ product.selected_or_first_available_variant.title | strip_html | escape }}", category : "{{ product.type | strip_html | escape }}", quantity : "{{ product.selected_or_first_available_variant.inventory_quantity }}" }{% if forloop.last != true %}, {% endif %}{% endcapture %}{% assign get_price = product.selected_or_first_available_variant.price  %}{% assign total_value_arr = total_value_arr | plus: get_price %}{% endfor %}
    var gsf_conversion_data = {page_type : 'category', event : 'view_item_list', data : {product_data : [{{ gsf_product_data_array }}], total_price :{%if shop.money_format contains 'comma_separator'%}"{{ total_value_arr | money_without_currency | replace:',','-' |  replace:'.',',' | replace:'-','.' | remove: ',' }}"{%elsif shop.money_format contains 'apostrophe_separator'%}"{{ total_value_arr | money_without_currency | remove: "'" }}"{%else%}"{{ total_value_arr | money_without_currency | remove: ',' }}"{%endif%}, shop_currency : {% for currency in shop.enabled_currencies %}{% if currency == cart.currency %}{% assign shop_currency = currency.iso_code %}{% endif %}{% endfor %}{% if shop_currency != '' %}"{{ shop_currency }}"{% else %}"{{ shop.currency }}"{% endif %}, collection_id : "{{ collection.id }}", collection_name : "{{ collection.title | strip_html | escape }}" }};
    {% elsif template contains 'search' and search.performed %}
    {% assign limit = search.results_count %}{% if limit > 5 %}{% assign limit = 5 %}{% endif %}{% for item in search.results limit:limit %}{% capture gsf_product_data_array %}{{ gsf_product_data_array  }}{variant_id : {% if item.selected_or_first_available_variant.id %}{{ item.selected_or_first_available_variant.id }}{% else %}''{% endif %}, product_id : {{ item.id }}, name : "{{ item.title | strip_html | escape | strip_newlines }}", price : {%if shop.money_format contains 'comma_separator'%}"{{ item.selected_or_first_available_variant.price | money_without_currency | replace:',','-' |  replace:'.',',' | replace:'-','.' | remove: ',' }}"{%elsif shop.money_format contains 'apostrophe_separator'%}"{{ item.selected_or_first_available_variant.price | money_without_currency | remove: "'" }}"{%else%}"{{ item.selected_or_first_available_variant.price | money_without_currency | remove: ',' }}"{%endif%}
    , currency : "{{ shop.currency }}", sku : "{{ item.selected_or_first_available_variant.sku | strip_html | escape }}", brand : "{{ item.vendor }}", variant : "{{ item.selected_or_first_available_variant.title | strip_html | escape }}", category : "{{ item.type | strip_html | escape }}"}{% if forloop.last != true %}, {% endif %}{% endcapture %}{% assign get_price = item.selected_or_first_available_variant.price  %}{% assign total_value_arr = total_value_arr | plus: get_price %}{% endfor %}
    var gsf_conversion_data = {page_type : 'searchresults', event : 'view_search_results', data : {product_data : [{{ gsf_product_data_array }}], total_price :{%if shop.money_format contains 'comma_separator'%}"{{ total_value_arr | money_without_currency | replace:',','-' |  replace:'.',',' | replace:'-','.' | remove: ',' }}"{%elsif shop.money_format contains 'apostrophe_separator'%}"{{ total_value_arr | money_without_currency | remove: "'" }}"{%else%}"{{ total_value_arr | money_without_currency | remove: ',' }}"{%endif%}, search_string : "{{ search.terms }}", shop_currency : {% for currency in shop.enabled_currencies %}{% if currency == cart.currency %}{% assign shop_currency = currency.iso_code %}{% endif %}{% endfor %}{% if shop_currency != '' %}"{{ shop_currency }}"{% else %}"{{ shop.currency }}"{% endif %}}};
    {% elsif template contains 'page' and page.handle == 'search-results-page' %}
    var gsf_url_search_params = new URLSearchParams(window.location.search);
    var gsf_search_terms = gsf_url_search_params.get('q') || '';
    var gsf_conversion_data = {page_type : 'searchresults', event : 'view_search_results', data : {product_data : "", search_string : gsf_search_terms, shop_currency : {% for currency in shop.enabled_currencies %}{% if currency == cart.currency %}{% assign shop_currency = currency.iso_code %}{% endif %}{% endfor %}{% if shop_currency != '' %}"{{ shop_currency }}"{% else %}"{{ shop.currency }}"{% endif %}}};
    {% else %}
    var gsf_conversion_data = {page_type : '', event : '', data : {shop_currency : {% for currency in shop.enabled_currencies %}{% if currency == cart.currency %}{% assign shop_currency = currency.iso_code %}{% endif %}{% endfor %}{% if shop_currency != '' %}"{{ shop_currency }}"{% else %}"{{ shop.currency }}"{% endif %}}};
    {% endif %}
</script>