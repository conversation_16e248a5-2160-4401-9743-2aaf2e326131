{% comment %} 

--------------------------------------------------------------------------------
SUB BRAND
--------------------------------------------------------------------------------
page_sub_brand - Sub-brand assigned to the current Page.
product_sub_brand - Sub-brand assigned to the Product.
collection_sub_brand - Sub-brand assigned to the Collection the Product is in.
sub_brand - Stores the sub-brand Metaobject.

{% endcomment %}

{%- if page.metafields.sub_brand.sub_brand != blank -%}
  {%- assign page_sub_brand = page.metafields.sub_brand.sub_brand %}
{%- endif -%}
 
{%- if product.metafields.sub_brand.sub_brand != blank -%}
  {%- assign product_sub_brand = product.metafields.sub_brand.sub_brand %}
{%- endif -%}

{%- if collection.metafields.sub_brand.sub_brand != blank -%}
  {%- assign collection_sub_brand = collection.metafields.sub_brand.sub_brand -%}
{%- endif -%}

{%- if page_sub_brand -%}
  {% comment %} {%- assign sub_brand = page_sub_brand -%} {% endcomment %}
{%- endif -%}

{%- if collection_sub_brand -%}
  {%- assign sub_brand = collection_sub_brand -%}
{%- endif -%}

{%- if sub_brand != blank -%}
  {%- if product.metafields.kyte_living.alternate_title != blank -%}
    {%- assign product_title = product.metafields.kyte_living.alternate_title -%}
  {%- endif -%}
{%- endif -%}

{%- assign product_title = product.title -%}
{%- assign product_description = product.description -%}

{%- if sub_brand != blank -%}
  {%- if product.metafields.kyte_living.alternate_title != blank -%}
    {%- assign product_description = product.metafields.kyte_living.alternate_description | metafield_tag -%}
  {%- endif -%}
{%- endif -%}

{% comment %} 

 --------------------------------------------------------------------------------
 TIERS
 --------------------------------------------------------------------------------
 If the product has a tier metafield tag set, find this tag and store it to show/hide the product form later.

{% endcomment %}

{% assign customer_has_tier_tag = false %}

{%- for i in (1..3) -%}

  {% capture tier_tag %}tier_{{ i }}_tag{% endcapture %}
  {% capture tier_message_heading %}tier_{{ i }}_message_heading{% endcapture %}
  {% capture tier_message_text %}tier_{{ i }}_message_text{% endcapture %}
  {% capture tier_cta_text %}tier_{{ i }}_cta_text{% endcapture %}
  {% capture tier_cta_link %}tier_{{ i }}_cta_link{% endcapture %}
  
  {% if product.metafields.custom.exclusive_access.value contains settings[tier_tag] %}
    {%- assign product_has_tier_tag = true -%}
    {%- break -%}
  {% endif %}

{%- endfor -%}

{% if customer.tags contains settings[tier_tag] %}
  {% assign customer_has_tier_tag = true %}
{% else %}
  {% assign customer_has_tier_tag = false %}
{% endif %}

{%- if product_has_tier_tag == true -%}

{%- else -%}

  {% assign tier_tag = blank %}
  {% assign tier_message_heading = blank %}
  {% assign tier_message_text = blank %}
  {% assign tier_cta_text = blank %}
  {% assign tier_cta_link = blank %}

{%- endif -%}


{% comment %} 

--------------------------------------------------------------------------------
SALE STOP - Stops sale of product by disabling/hiding add to cart buttons and displaying messaging.
--------------------------------------------------------------------------------
page_sub_brand - Sub-brand assigned to the current Page.
product_sub_brand - Sub-brand assigned to the Product.
collection_sub_brand - Sub-brand assigned to the Collection the Product is in.
sub_brand - Stores the sub-brand Metaobject.

{% endcomment %}

<div class="product__info">

  <!-- PRODUCT META -->
  <product-meta price-class="price--large" class="product-meta" data-block-type="meta" data-block-id="{{ section.id }}"> {%- comment -%} Removed block type and ID because AfterPay + Reviews were disappearing {%- endcomment -%}
    {%- if section.settings.show_vendor -%}
      <h2 class="product-meta__vendor heading heading--small">
        {%- assign vendor_handle = product.vendor | handle -%}
        {%- assign vendor_collection = collections[vendor_handle] -%}

        {%- if vendor_collection != blank -%}
          <a href="{{ vendor_collection.url }}">{{ product.vendor }}</a>
        {%- else -%}
          <a href="{{ product.vendor | url_for_vendor }}">{{ product.vendor }}</a>
        {%- endif -%}
      </h2>
    {%- endif -%}

    {% comment %} Product Meta {% endcomment %}

    {%- capture product_meta_top -%}

      {%- if section.settings.show_product_collection_breadcrumbs == true -%}
        {%- if section.settings.breadcrumbs_type == "sub-collections" -%}
          {% render 'product-collection-breadcrumbs' %}
        {%- else -%}
          {% render 'product-default-breadcrumbs' %}
        {%- endif -%}
      {%- endif -%}

      {%- if section.settings.show_product_rating -%}
        {%- render 'okendo-reviews-product-rating-summary', product: product -%}
      {%- endif -%}

    {%- endcapture -%}

    {%- if product_meta_top != blank -%}
      <div class="product-meta__top">
        {{ product_meta_top }}
      </div>
    {%- endif -%}

    {% comment %} Product Labels {% endcomment %}

    {%- if section.settings.label_list_location == "meta" -%}
      {% 
        render 'product-label-list', 
        classes: "product-meta__label-list", 
        product: product, 
        product_has_tier_tag: product_has_tier_tag,
        tier_message_heading: settings[tier_message_heading],
        sale_stop: sale_stop
      %}
    {%- endif -%}

    {% comment %} Title {% endcomment %}

    <div class="product-meta__title-container">
      {% unless featured %}
        <h1 class="product-meta__title heading h3">{{ product_title }}</h1>
      {% else %}
        <h2 class="product-meta__title heading h3">
          <a href="{{ product.url }}">{{ product_title }}</a>
        </h2>
      {% endunless %}

      {% if product.metafields.custom.active_ingredients.value != blank %}

        {%- assign active_ingredients = product.metafields.custom.active_ingredients.value-%}

        <div class="product-meta__active_ingredients text--xsmall text--subdued">
          {{ 'product.ingredients.with' | t }}
          {%- for ingredient in active_ingredients -%}
            {%- assign second_last = forloop.length | minus: 1 -%}
            
            {{ ingredient.title -}}

            {%- if forloop.last -%}
              .
            {%- elsif forloop.last == false and forloop.index != second_last -%}
              ,&nbsp;
            {%- elsif forloop.index == second_last %}
              {{ 'product.ingredients.and' | t }}
            {%- endif -%}

          {%- endfor -%}
        </div>

      {% endif %}

    </div>

    {% if product.metafields.product.intro_text %}
      <div class="product-meta__short_description text--xsmall">
        {{ product.metafields.product.intro_text }}
      </div>
    {% endif %}

    <div class="product-meta__price-list-container">
      {%- if product.selected_or_first_available_variant != nil -%}
        <div class="price-list">
          {%- if product.selected_or_first_available_variant.compare_at_price > product.selected_or_first_available_variant.price -%}
            <span class="price price--highlight price--large">
              <span class="visually-hidden">{{ 'product.general.sale_price' | t }}</span>

              {%- if settings.currency_code_enabled -%}
                {{- product.selected_or_first_available_variant.price | money_with_currency -}}
              {%- else -%}
                {{- product.selected_or_first_available_variant.price | money -}}
              {%- endif -%}
            </span>

            <span class="price price--compare">
              <span class="visually-hidden">{{ 'product.general.regular_price' | t }}</span>

              {%- if settings.currency_code_enabled -%}
                {{- product.selected_or_first_available_variant.compare_at_price | money_with_currency -}}
              {%- else -%}
                {{- product.selected_or_first_available_variant.compare_at_price | money -}}
              {%- endif -%}
            </span>
          {%- else -%}
            <span class="price price--large">
              <span class="visually-hidden">{{ 'product.general.sale_price' | t }}</span>
              {%- if settings.currency_code_enabled -%}
                {{- product.selected_or_first_available_variant.price | money_with_currency -}}
              {%- else -%}
                {{- product.selected_or_first_available_variant.price | money -}}
              {%- endif -%}
            </span>
          {%- endif -%}

          {%- if product.selected_or_first_available_variant.unit_price_measurement -%}
            <div class="price text--subdued">
              <div class="unit-price-measurement">
                <span class="unit-price-measurement__price">{{ product.selected_or_first_available_variant.unit_price | money }}</span>
                <span class="unit-price-measurement__separator">/</span>

                {%- if product.selected_or_first_available_variant.unit_price_measurement.reference_value != 1 -%}
                  <span class="unit-price-measurement__reference-value">{{ product.selected_or_first_available_variant.unit_price_measurement.reference_value }}</span>
                {%- endif -%}

                <span class="unit-price-measurement__reference-unit">{{ product.selected_or_first_available_variant.unit_price_measurement.reference_unit }}</span>
              </div>
            </div>
          {%- endif -%}
        </div>
      {%- endif -%}

      {%- comment -%}
      {%- if product.selected_or_first_available_variant != nil -%}
        <div class="product-meta__label-list label-list">
          {%- unless product.selected_or_first_available_variant.available -%}
            <span class="label label--subdued">{{ 'collection.product.sold_out' | t }}</span>
          {%- elsif product.selected_or_first_available_variant.compare_at_price > product.selected_or_first_available_variant.price -%}
            {%- if settings.discount_mode == 'percentage' -%}
              {%- assign savings = product.selected_or_first_available_variant.compare_at_price | minus: product.selected_or_first_available_variant.price | times: 100.0 | divided_by: product.selected_or_first_available_variant.compare_at_price | round | append: '%' -%}
            {%- else -%}
              {%- capture savings -%}{{ product.selected_or_first_available_variant.compare_at_price | minus: product.selected_or_first_available_variant.price | money }}{%- endcapture -%}
            {%- endif -%}

            <span class="label label--highlight">{{ 'collection.product.discount_html' | t: savings: savings }}</span>
          {%- endunless -%}
        </div>
      {%- endif -%}
      {%- endcomment -%}
    </div>

    {%- if section.settings.show_taxes_included -%}
      {%- if cart.taxes_included or shop.shipping_policy.body != blank -%}
        <p class="product-meta__taxes-included text--small">
          {%- if cart.taxes_included -%}
            {{ 'product.general.include_taxes' | t }}
          {%- endif -%}

          {%- if shop.shipping_policy.body != blank -%}
            {{ 'product.general.shipping_policy_html' | t: link: shop.shipping_policy.url }}
          {%- endif -%}
        </p>
      {%- endif -%}
    {%- endif -%}

    <product-payment-terms data-block-type="payment-terms" data-block-id="{{ section.id }}" form-id="{{ product_form_id }}">
      {%- assign product_installment_form_id = 'product-installment-form-' | append: section.id | append: '-' | append: product.id -%}

      {%- form 'product', product, id: product_installment_form_id -%}
        <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
        {{- form | payment_terms -}}
      {%- endform -%}
    </product-payment-terms>

    {%- if section.settings.show_sku -%}
      
      <div class="product-meta__reference">
        {%- if section.settings.show_sku -%}
          <span class="product-meta__sku text--subdued text--xxsmall" {% if product.selected_or_first_available_variant.sku == blank %}style="display: none"{% endif %} data-product-sku-container>
            {{- 'product.general.sku' | t }}
            <span class="product-meta__sku-number" data-product-sku-number>{{ product.selected_or_first_available_variant.sku }}</span>
          </span>
        {%- endif -%}
      </div>

    {%- endif -%}

  </product-meta>

  {%- render 'product-form', product: product, product_form_id: product_form_id, update_url: update_url, sub_brand: sub_brand, sale_stop: sale_stop -%}

  {%- capture product_tabs -%}

    {%- capture product_tabs_nav_items -%}
      {%- assign processed_blocks = 0 -%}

      {%- for block in section.blocks -%}
        {%- case block.type -%}

          {%- comment -%}
          ----------------------------------------------------------------------------------------------------------------
          PRODUCT DESCRIPTION
          ----------------------------------------------------------------------------------------------------------------
          {%- endcomment -%}
          {%- when 'description' -%}

            <button type="button" class="tabs-nav__item heading heading--small" aria-expanded="{% if processed_blocks == 0 %}true{% else %}false{% endif %}" aria-controls="product-tab--product-{{ product.id }}-block-{{ section.id }}-{{ block.id }}" {{ block.shopify_attributes }}>
              {{- 'product.general.description' | t -}}
            </button>

            {%- assign processed_blocks = processed_blocks | plus: 1 -%}
            
          {%- when 'special_description' -%}
            
            {%- if block.settings.long_description_title != blank or block.settings.long_description != blank -%}
            
              <button type="button" class="tabs-nav__item heading heading--small" aria-expanded="{% if processed_blocks == 0 %}true{% else %}false{% endif %}" aria-controls="product-tab--product-{{ product.id }}-block-{{ section.id }}-{{ block.id }}" {{ block.shopify_attributes }}>
                {{- 'product.general.description' | t -}}
              </button>

              {%- assign processed_blocks = processed_blocks | plus: 1 -%}

            {%- endif -%}
          
          {%- when 'page_tabs' -%}

            {% for tag in product.tags %}

              {%- if tag contains '_tab' -%}
                {%- assign include_page_handle = tag | split: '_' -%}
                {%- assign include_page = pages[include_page_handle.last] -%}
                {%- if include_page.title != blank -%}
                  <button type="button" class="tabs-nav__item heading heading--small" aria-expanded="{% if processed_blocks == 0 %}true{% else %}false{% endif %}" aria-controls="product-tab--product-{{ product.id }}-block-{{ section.id }}-{{ block.id }}-page-{{ forloop.index }}" {{ block.shopify_attributes }}>
                    {{ include_page.title }}
                  </button>
                {%- endif -%}
              {%- endif -%}

              {%- assign processed_blocks = processed_blocks | plus: 1 -%}

            {% endfor %}
            
          {%- when 'liquid' -%}
            
            {%- if block.settings.title != blank and block.settings.liquid != blank -%}
              <button type="button" class="tabs-nav__item heading heading--small" aria-expanded="{% if processed_blocks == 0 %}true{% else %}false{% endif %}" aria-controls="block-{{ section.id }}-{{ block.id }}" {{ block.shopify_attributes }}>
                {{- block.settings.title | escape -}}
              </button>

              {%- assign processed_blocks = processed_blocks | plus: 1 -%}
            {%- endif -%}

          {%- when 'content' -%}
          
            {%- if block.settings.page != blank -%}
              {%- assign title = block.settings.page.title -%}
              {%- assign content = block.settings.page.content -%}
            {%- else -%}
              {%- assign title = block.settings.title -%}
              {%- assign content = block.settings.content -%}
            {%- endif -%}

            {%- if title != blank and content != blank -%}
              <button type="button" class="tabs-nav__item heading heading--small" aria-expanded="{% if processed_blocks == 0 %}true{% else %}false{% endif %}" aria-controls="product-tab--product-{{ product.id }}-block-{{ section.id }}-{{ block.id }}" {{ block.shopify_attributes }}>
                {{- title -}}
              </button>

              {%- assign processed_blocks = processed_blocks | plus: 1 -%}

            {%- endif -%}

            {%- endcase -%}
      {%- endfor -%}
    {%- endcapture -%}

    {%- if product_tabs_nav_items != blank -%}

      <tabs-nav arrows class="tabs-nav">
        <scrollable-content class="tabs-nav__scroller hide-scrollbar">
          <div class="tabs-nav__scroller-inner">
            <div class="tabs-nav__item-list">
              {{- product_tabs_nav_items -}}
            </div>
          </div>
        </scrollable-content>

        <div class="tabs-nav__arrows">

          <button class="tabs-nav__arrow-item">
            <span class="visually-hidden">{{ 'general.accessibility.previous' | t }}</span>
            {%- render 'icon' with 'product-tab-left', direction_aware: true -%}
          </button>

          <button class="tabs-nav__arrow-item">
            <span class="visually-hidden">{{ 'general.accessibility.next' | t }}</span>
            {%- render 'icon' with 'product-tab-right', direction_aware: true -%}
          </button>
        </div>
      </tabs-nav>
      
    {%- endif -%}

    {% assign trust_block = section.blocks | where: 'type', 'trust' | first %}

    {%- if trust_block != blank -%}
      {%- capture trust_content -%}
        {%- for i in (1..3) -%}
          {%- capture icon_setting -%}icon_{{ i }}{%- endcapture -%}
          {%- capture custom_icon_setting -%}custom_icon_{{ i }}{%- endcapture -%}
          {%- capture title_setting -%}title_{{ i }}{%- endcapture -%}
          {%- capture content_setting -%}content_{{ i }}{%- endcapture -%}

          {%- if trust_block.settings[title_setting] != blank -%}
            {%- assign custom_icon = trust_block.settings[custom_icon_setting] -%}

            {%- capture trust_icon -%}
              {%- if custom_icon != blank -%}
                <img class="product-tabs__trust-icon" loading="lazy" sizes="24px" {% render 'image-attributes', image: custom_icon, sizes: '24,48,72,96' %}>
              {%- else -%}
                {%- render 'icon' with trust_block.settings[icon_setting], class: 'product-tabs__trust-icon' -%}
              {%- endif -%}
            {%- endcapture -%}

            {%- if trust_block.settings[content_setting] != blank -%}
              <button is="toggle-button" class="product-tabs__trust-title subheading subheading--bold subheading--small icon-text link hidden-phone" aria-controls="product-{{ product.id }}-block-{{ section.id }}-{{ trust_block.id }}-trust-{{ forloop.index }}-drawer" aria-expanded="false">
                {{- trust_icon -}} {{- trust_block.settings[title_setting] | escape -}}
              </button>

              <button is="toggle-button" class="product-tabs__trust-title subheading subheading--bold subheading--small icon-text link hidden-tablet-and-up" aria-controls="product-{{ section.id }}-{{ trust_block.id }}-trust-{{ forloop.index }}-popover" aria-expanded="false">
                {{- trust_icon -}} {{- trust_block.settings[title_setting] | escape -}}
              </button>
            {%- else -%}
              <span class="product-tabs__trust-title subheading subheading--bold subheading--small icon-text">
                {{- trust_icon -}} {{- trust_block.settings[title_setting] | escape -}}
              </span>
            {%- endif -%}
          {%- endif -%}
        {%- endfor -%}
      {%- endcapture -%}

      {%- capture trust_drawer_content -%}
        {%- for i in (1..3) -%}
          {%- capture icon_setting -%}icon_{{ i }}{%- endcapture -%}
          {%- capture custom_icon_setting -%}custom_icon_{{ i }}{%- endcapture -%}
          {%- capture title_setting -%}title_{{ i }}{%- endcapture -%}
          {%- capture content_setting -%}content_{{ i }}{%- endcapture -%}

          {%- if trust_block.settings[title_setting] != blank and trust_block.settings[content_setting] != blank -%}
            {%- assign custom_icon = trust_block.settings[custom_icon_setting] -%}

            {%- capture trust_icon -%}
              {%- if custom_icon != blank -%}
                <img class="product-tabs__trust-icon" loading="lazy" sizes="24px" {% render 'image-attributes', image: custom_image, sizes: '24,48,72,96' %}>
              {%- else -%}
                {%- render 'icon' with trust_block.settings[icon_setting], class: 'product-tabs__trust-icon' -%}
              {%- endif -%}
            {%- endcapture -%}

            {%- comment -%}Drawer is for tablet and desktop{%- endcomment -%}
            <drawer-content id="product-{{ product.id }}-block-{{ section.id }}-{{ trust_block.id }}-trust-{{ forloop.index }}-drawer" class="drawer drawer--large hidden-phone">
              <span class="drawer__overlay"></span>

              <header class="drawer__header">
                <p class="drawer__title heading h6">{{- trust_icon -}} {{- trust_block.settings[title_setting] | escape -}}</p>

                <button type="button" class="drawer__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
                  {%- render 'icon' with 'close' -%}
                </button>
              </header>

              <div class="drawer__content drawer__content--padded-start">
                <div class="rte">
                  {{- trust_block.settings[content_setting] -}}
                </div>
              </div>
            </drawer-content>

            {%- comment -%}Popover is for mobile{%- endcomment -%}
            <popover-content id="product-{{ section.id }}-{{ trust_block.id }}-trust-{{ forloop.index }}-popover" class="popover hidden-tablet-and-up">
              <span class="popover__overlay"></span>

              <header class="popover__header">
                <p class="popover__title heading h3">{{- trust_icon -}} {{- trust_block.settings[title_setting] | escape -}}</p>

                <button type="button" class="popover__close-button tap-area tap-area--large" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
                  {%- render 'icon' with 'close' -%}
                </button>
              </header>

              <div class="popover__content">
                <div class="rte">
                  {{- trust_block.settings[content_setting] -}}
                </div>
              </div>
            </popover-content>
          {%- endif -%}
        {%- endfor -%}
      {%- endcapture -%}
    {%- endif -%}

    {%- if product_tabs_nav_items != blank -%}
      <div class="product-tabs__content">
        {%- assign processed_blocks = 0 -%}

        {%- for block in section.blocks -%}

          {%- case block.type -%}

            {%- when 'description' -%}

              {%- if product.description != blank -%}
                <div {% unless processed_blocks == 0 %}hidden{% endunless %} class="product-form__description rte" id="product-tab--product-{{ product.id }}-block-{{ section.id }}-{{ block.id }}" class="product-tabs__tab-item-wrapper" {{ block.shopify_attributes }}>
                  
                  <div class="product-tabs__tab-item-content rte">
                    {{- product_description -}}
                  </div>

                  {%- if trust_content != blank and processed_blocks == 0 -%}
                    <div class="product-tabs__trust-list hidden-pocket">
                      <div class="product-tabs__trust-list-inner">
                        {{- trust_content -}}
                      </div>
                    </div>
                  {%- endif -%}

                </div>
              {%- endif -%}

              {%- assign processed_blocks = processed_blocks | plus: 1 -%}

            {%- when 'special_description' -%}
            
              {%- if block.settings.long_description_title != blank or block.settings.long_description != blank -%}
            
                <div {% unless processed_blocks == 0 %}hidden{% endunless %} id="product-tab--product-{{ product.id }}-block-{{ section.id }}-{{ block.id }}" class="product-tabs__tab-item-wrapper">

                  <div class="product-tabs__tab-item-content rte">
                    {%- if block.settings.long_description_title != blank -%}
                      <h3>{{ block.settings.long_description_title }}</h3>
                    {%- endif -%}
                    {%- if block.settings.long_description != blank -%}
                      <div>{{ block.settings.long_description }}</div>
                    {%- endif -%}
                  </div>

                  {%- if trust_content != blank and processed_blocks == 0 -%}
                    <div class="product-tabs__trust-list hidden-pocket">
                      <div class="product-tabs__trust-list-inner">
                        {{- trust_content -}}
                      </div>
                    </div>
                  {%- endif -%}

                </div>

                {%- assign processed_blocks = processed_blocks | plus: 1 -%}

              {%- endif -%}

            {%- when 'page_tabs' -%}

              {% for tag in product.tags %}

                {%- if tag contains '_tab' -%}
                  {%- assign include_page_handle = tag | split: '_' -%}
                  {%- assign include_page = pages[include_page_handle.last] -%}
                  {%- if include_page.title != blank -%}
                    <div class="product-tabs__tab-item-wrapper" {% unless processed_blocks == 0 %}hidden{% endunless %} aria-expanded="{% if processed_blocks == 0 %}true{% else %}false{% endif %}" id="product-tab--product-{{ product.id }}-block-{{ section.id }}-{{ block.id }}-page-{{ forloop.index }}">

                      <div class="product-tabs__tab-item-content rte">
                        {{ include_page.content }}
                      </div>

                    </div>
                  {%- endif -%}
                {%- endif -%}

                {%- assign processed_blocks = processed_blocks | plus: 1 -%}

              {% endfor %}

            {%- when 'liquid' -%}
              
            {%- if block.settings.title != blank and block.settings.liquid != blank -%}
              
                <div {% unless processed_blocks == 0 %}hidden{% endunless %} id="block-{{ section.id }}-{{ block.id }}" class="product-tabs__tab-item-wrapper" {{ block.shopify_attributes }}>

                  <div class="product-tabs__tab-item-content rte">
                    {{- block.settings.liquid -}}
                  </div>

                </div>

                {%- assign processed_blocks = processed_blocks | plus: 1 -%}
              {%- endif -%}  

            {%- when 'content' -%}

              {%- if block.settings.page != blank -%}
                {%- assign title = block.settings.page.title -%}
                {%- assign content = block.settings.page.content -%}
              {%- else -%}
                {%- assign title = block.settings.title -%}
                {%- assign content = block.settings.content -%}
              {%- endif -%}

              {%- if title != blank and content != blank -%}

                <div {% unless processed_blocks == 0 %}hidden{% endunless %} id="product-tab--product-{{ product.id }}-block-{{ section.id }}-{{ block.id }}" class="product-tabs__tab-item-wrapper" {{ block.shopify_attributes }}>

                  <div class="product-tabs__tab-item-content rte">
                    {{- content -}}
                  </div>

                  {%- if trust_content != blank and processed_blocks == 0 -%}
                    <div class="product-tabs__trust-list hide-scrollbar hidden-pocket">
                      <div class="product-tabs__trust-list-inner">
                        {{- trust_content -}}
                      </div>
                    </div>
                  {%- endif -%}

                </div>

                {%- assign processed_blocks = processed_blocks | plus: 1 -%}

              {%- endif -%}

          {%- endcase -%}

        {%- endfor -%}

      </div>
    {%- endif -%}

    {%- if trust_content != blank -%}
      <div class="product-tabs__trust-list container {% if product_tabs_nav_items != blank %}hidden-lap-and-up{% endif %}">
        <div class="product-tabs__trust-list-inner hide-scrollbar">
          {{- trust_content -}}
        </div>
      </div>
    {%- endif -%}

    {%- comment -%}Output the various drawers for the trust badges, only once (if they exist){%- endcomment -%}
    {{- trust_drawer_content -}}
  {%- endcapture -%}

  {%- if product_tabs != blank -%}
    <div class="product-content">
      <div class="product-content__tabs-new anchor" id="product-{{ product.id }}-tabs">
        <div class="product-tabs">
          {{- product_tabs -}}
        </div>
      </div>
    </div>
  {%- endif -%}

  {%- assign featured_products_block = section.blocks | where: 'type', 'featured_products' | first -%}

  {%- if featured_products_block != blank -%}

    {%- assign product_1 = featured_products_block.settings.product_1 -%}
    {%- assign product_2 = featured_products_block.settings.product_2 -%}

    {%- if product_1 != blank or product_2 != blank -%}
      <div class="product-content__featured-products">
        {%- if featured_products_block.settings.title != blank -%}
          <p class="product-content__featured-products-title heading heading--small">{{ featured_products_block.settings.title }}</p>
        {%- endif -%}

        <div class="scroller">
          <div class="scroller__inner">
            <div class="product-content__featured-products-list">
              {% if product_1 != blank %}
                <div>{%- render 'product-item--upsell', product: product_1, reduced_content: true, reduced_font_size: true, sizes_attribute: '(max-width: 740px) 105px, 215px' -%}</div>
              {% endif %}
              {% if product_2 != blank %}
                <div>{%- render 'product-item--upsell', product: product_2, reduced_content: true, reduced_font_size: true, sizes_attribute: '(max-width: 740px) 105px, 215px' -%}</div>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
    {%- endif -%}
  {%- endif -%}

  {%- assign featured_products_upsells_block = section.blocks | where: 'type', 'featured_products_upsells' | first -%}

  {%- if featured_products_upsells_block != blank -%}

    {%- assign product_1 = all_products[product.metafields.upsells.upsell_1] -%}
    {%- assign product_2 = all_products[product.metafields.upsells.upsell_2] -%}

    {%- if product_1 != blank or product_2 != blank -%}
      <div class="product-content__featured-products">
        {%- if featured_products_upsells_block.settings.title != blank -%}
          <p class="product-content__featured-products-title heading heading--small">{{ featured_products_upsells_block.settings.title }}</p>
        {%- endif -%}

        <div class="scroller">
          <div class="scroller__inner">

            <div class="product-content__featured-products-list">

              {%- if product_1 != blank and product_1 != product -%}
                <div>{%- render 'product-item--upsell', product: product_1, reduced_content: true, reduced_font_size: true, sizes_attribute: '(max-width: 740px) 105px, 215px' -%}</div>
              {%- endif -%}

              {%- if product_2 != blank and product_2 != product -%}
                <div>{%- render 'product-item--upsell', product: product_2, reduced_content: true, reduced_font_size: true, sizes_attribute: '(max-width: 740px) 105px, 215px' -%}</div>
              {%- endif -%}

            </div>

          </div>
        </div>

      </div>
    {%- endif -%}
  {%- endif -%}

  {%- assign help_page = section.settings.help_page -%}

  {%- if section.settings.show_share_buttons or help_page != blank -%}
    <div class="product-meta__aside">
      {%- if section.settings.show_share_buttons -%}
        <div class="product-meta__share text--subdued">
          {%- assign share_url = shop.url | append: product.url -%}
          {%- assign twitter_text = product_title | url_param_escape -%}
          {%- assign pinterest_description = product.description | strip_html | truncatewords: 15 | url_param_escape -%}
          {%- assign pinterest_image = product.featured_image | image_url: width: 800 | prepend: 'https:' -%}

          <button is="share-toggle-button" share-url="{{ share_url | escape }}" share-title="{{ product_title | escape }}" class="product-meta__share-label link hidden-tablet-and-up" aria-controls="mobile-share-buttons-{{ section.id }}" aria-expanded="false">{{ 'product.general.share' | t }}</button>
          <div class="product-meta__share-label hidden-phone">{{ 'product.general.share' | t }}</div>

          <popover-content id="mobile-share-buttons-{{ section.id }}" class="popover hidden-tablet-and-up">
            <span class="popover__overlay"></span>

            <header class="popover__header">
              <span class="popover__title heading h6">{{- 'article.general.share' | t -}}</span>

              <button type="button" class="popover__close-button tap-area tap-area--large" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
                {%- render 'icon' with 'close' -%}
              </button>
            </header>

            <div class="mobile-share-buttons">
              <a class="mobile-share-buttons__item mobile-share-buttons__item--facebook" href="https://www.facebook.com/sharer.php?u={{ share_url }}" target="_blank" rel="noopener" aria-label="{{ 'general.social.facebook_share' | t }}">
                {%- render 'icon' with 'facebook-share-mobile' -%} Facebook
              </a>

              <a class="mobile-share-buttons__item mobile-share-buttons__item--pinterest" href="https://pinterest.com/pin/create/button/?url={{ share_url }}{% if pinterest_image != blank %}&media={{ pinterest_image }}{% endif %}&description={{ pinterest_description }}" target="_blank" rel="noopener" aria-label="{{ 'general.social.pinterest_pin' | t }}">
                {%- render 'icon' with 'pinterest-share-mobile' -%} Pinterest
              </a>

              <a class="mobile-share-buttons__item mobile-share-buttons__item--twitter" href="https://twitter.com/intent/tweet?{% if twitter_text != blank %}text={{twitter_text}}&{% endif %}url={{ share_url }}" target="_blank" rel="noopener" aria-label="{{ 'general.social.twitter_tweet' | t }}">
                {%- render 'icon' with 'twitter-share-mobile' -%} Twitter
              </a>

              <a class="mobile-share-buttons__item mobile-share-buttons__item--mail" href="mailto:?&subject={{ article.title | escape }}&body={{ share_url }}" aria-label="{{ 'general.social.email_share' | t }}">
                {%- render 'icon' with 'email-share-mobile' -%} {{ 'general.social.email_label' | t }}
              </a>
            </div>
          </popover-content>

          <div class="product-meta__share-button-list hidden-phone">
            <a class="product-meta__share-button-item product-meta__share-button-item--facebook link tap-area" href="https://www.facebook.com/sharer.php?u={{ share_url }}" target="_blank" rel="noopener" aria-label="{{ 'general.social.facebook_share' | t }}">
              {%- render 'icon' with 'facebook', width: 8, height: 14 -%}
            </a>

            <a class="product-meta__share-button-item product-meta__share-button-item--pinterest link tap-area" href="https://pinterest.com/pin/create/button/?url={{ share_url }}{% if pinterest_image != blank %}&media={{ pinterest_image }}{% endif %}&description={{ pinterest_description }}" target="_blank" rel="noopener" aria-label="{{ 'general.social.pinterest_pin' | t }}">
              {%- render 'icon' with 'pinterest', width: 10, height: 14 -%}
            </a>

            <a class="product-meta__share-button-item product-meta__share-button-item--twitter link tap-area" href="https://twitter.com/intent/tweet?{% if twitter_text != blank %}text={{ twitter_text }}&{% endif %}url={{ share_url }}" target="_blank" rel="noopener" aria-label="{{ 'general.social.twitter_tweet' | t }}">
              {%- render 'icon' with 'twitter', width: 17, height: 14 -%}
            </a>

            <a class="product-meta__share-button-item product-meta__share-button-item--mail link tap-area" href="mailto:?&subject={{ product_title | escape }}&body={{ share_url }}" aria-label="{{ 'general.social.email_share' | t }}">
              {%- render 'icon' with 'share', width: 13, height: 13 -%}
            </a>
          </div>
        </div>
      {%- endif -%}

      {%- if help_page != blank -%}
        <button is="toggle-button" class="product-meta__help link text--subdued hidden-tablet-and-up" aria-controls="product-{{ section.id }}-help-popover" aria-expanded="false">{{ 'product.general.need_help' | t }}</button>
        <button is="toggle-button" class="product-meta__help link text--subdued hidden-phone" aria-controls="product-{{ section.id }}-help-drawer" aria-expanded="false">{{ 'product.general.need_help' | t }}</button>
      {%- endif -%}
    </div>
  {%- endif -%}

  {%- if help_page != blank -%}
    {%- comment -%}Drawer for tablet and higher{%- endcomment -%}
    <drawer-content id="product-{{ section.id }}-help-drawer" class="drawer drawer--large hidden-phone">
      <span class="drawer__overlay"></span>

      <header class="drawer__header">
        <p class="drawer__title heading h6">{{ help_page.title }}</p>

        <button type="button" class="drawer__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
          {%- render 'icon' with 'close' -%}
        </button>
      </header>

      <div class="drawer__content drawer__content--padded-start">
        <div class="rte">
          {{- help_page.content -}}
        </div>
      </div>
    </drawer-content>

    {%- comment -%}Popover for mobile{%- endcomment -%}
    <popover-content hidden id="product-{{ section.id }}-help-popover" class="popover hidden-lap-and-up">
      <span class="popover__overlay"></span>

      <header class="popover__header">
        <p class="popover__title heading h6">{{ help_page.title }}</p>

        <button type="button" class="popover__close-button tap-area tap-area--large" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
          {%- render 'icon' with 'close' -%}
        </button>
      </header>

      <div class="popover__content">
        <div class="rte">
          {{- help_page.content -}}
        </div>
      </div>
    </popover-content>
  {%- endif -%}
</div>