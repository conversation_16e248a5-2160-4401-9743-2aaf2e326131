{% comment %} 

------------------------------------------------------------------------------------------
Product Label List
------------------------------------------------------------------------------------------
classes: "product-media__label-list", 
product: product, 
product_has_tier_tag: false,
sale_stop: blank

{% endcomment %}

{% assign tag_number = 0 %}
{% assign tag_max = tag_max | default: 5 %}

{%- if section.settings.tier_display != settings.tier_display -%}
  {%- assign tier_display = section.settings.tier_display -%}
{%- else -%}
  {%- assign tier_display = settings.tier_display -%}
{%- endif -%}

{%- capture labels_content -%}
  
  {% comment %}
  ------------------------------
  Sold Out
  ------------------------------
  {% endcomment %}

  {%- unless product.available -%}
    {%- if tag_number <= tag_max and sale_stop == blank -%}
      <span class="label label--subdued label--sold-out">{{ 'collection.product.sold_out' | t }}</span>
      {% assign tag_number = tag_number | plus: 1 %}
    {%- endif -%}
  {%- endunless -%}

  {% comment %}
  ------------------------------
  Savings
  ------------------------------
  {% endcomment %}

  {%- if product.selected_or_first_available_variant.compare_at_price > product.selected_or_first_available_variant.price -%}
    {%- if tag_number <= tag_max -%}

    {%- if settings.discount_mode == 'percentage' -%}
      {%- assign savings = product.selected_or_first_available_variant.compare_at_price | minus: product.selected_or_first_available_variant.price | times: 100.0 | divided_by: product.selected_or_first_available_variant.compare_at_price | round | append: '%' -%}
    {%- else -%}
      {%- capture savings -%}{{ product.selected_or_first_available_variant.compare_at_price | minus: product.selected_or_first_available_variant.price | money }}{%- endcapture -%}
    {%- endif -%}

    <span class="label label--highlight label--on-sale">{{ 'collection.product.discount_html' | t: savings: savings }}</span>

    {% assign tag_number = tag_number | plus: 1 %}

    {%- endif -%}
  {%- endif -%}

  {% comment %}
  ------------------------------
  Custom Badges
  ------------------------------
  {% endcomment %}

  {% comment %} Legacy {% endcomment %}
  {%- for tag in product.tags -%}
    {%- if tag contains '__label' -%}
      {%- if tag_number <= tag_max -%}
        <span class="label label--custom{% if tag contains '__label2' %}2{% endif %} label--custom-legacy">{{ tag | split: ':' | last }}</span>
        {% assign tag_number = tag_number | plus: 1 %}
      {%- endif -%}
    {%- endif -%}
  {%- endfor -%}

  {% comment %} Metafields {% endcomment %}
  {%- for custom_badge in product.metafields.custom.badges.value -%}
    {%- if tag_number <= tag_max -%}
      <span class="label {% if forloop.index == 1 %}label--custom{% else %}label--custom2{% endif %} label--custom-metafield">{{ custom_badge }}</span>
      {% assign tag_number = tag_number | plus: 1 %}
    {%- endif -%}
  {%- endfor -%}

  {% comment %}
  ------------------------------
  Tier Tags
  ------------------------------
  {% endcomment %}

  {%- if product_has_tier_tag == true and tier_display == 'label' -%}
    {%- if tag_number <= tag_max -%}
      <span class="label label--custom label--tier">{{ tier_message_heading }}</span>
      {% assign tag_number = tag_number | plus: 1 %}
    {%- endif -%}
  {%- endif -%}

  {% comment %}
  ------------------------------
  Sale Stop
  ------------------------------
  {% endcomment %}

  {%- if sale_stop != blank -%}
    {%- if sale_stop.enable == true -%}
      {%- if tag_number <= tag_max -%}
        <span class="label label--custom2 label--sale-stop">{{ sale_stop.title }}</span>
        {% assign tag_number = tag_number | plus: 1 %}
      {%- endif -%}
    {%- endif -%}
  {%- endif -%}

{%- endcapture -%}

{%- if labels_content != blank -%}
  <div class="{{ classes }} label-list" data-product-label-list>
    <!-- 
    Tag Max: {{ tag_max }}
    Tags: {{ tag_number }}

    -----
    PRODUCT
    {{ product }}

    -----
    SALE STOP
    {{ sale_stop }}
    enable - {{ sale_stop.enable }}
    title - {{ sale_stop.title }}

    -->
    {{ labels_content }}
  </div>
{%- endif -%}
