{% comment %} If the product has a tier metafield tag set, find this tag and store it to show/hide the product form later. {% endcomment %}

{% assign product_tags_array_string = '' %}

{% comment %}
  This first forloop puts together an array of tags that the product has.
  If the customer is tagged with the same tag, the banner is shown.
{% endcomment %}

{%- for i in (0..8) -%}

  {% capture tier_tag %}tier_{{ i }}_tag{% endcapture %}
  {% capture tier_icon %}tier_{{ i }}_icon{% endcapture %}
  {% capture tier_message_heading %}tier_{{ i }}_message_heading{% endcapture %}

  {% if product.metafields.custom.exclusive_access.value contains settings[tier_tag] %}
    
    {%- assign product_has_tier_tag = true -%}
    {%- break -%}

  {% endif %}

{%- endfor -%}

{%- if product.media.size > 0 -%}
  {%- assign selected_media = product.selected_variant.featured_media | default: product.featured_media -%}

  {%- comment -%}
  IMPLEMENTATION NOTE: Shopify does not natively offers a way to create a set of images per variant. This is often
    pretty limited when you have a lot of colors and would like to only see the images specific to a given color. To
    goes around that, Focal offers a hack using alt tags whose usage is described here: https://support.maestrooo.com/article/187-product-creating-variant-image-set
    The implementation is rather complex and inefficient due to lot of string parsings, but it is, unfortunately, the
    only way to do it on Shopify right now.
  {%- endcomment -%}

  {% assign filtered_media_ids = '' %}

  {%- for media in product.media -%}

    {%- if media.alt contains '#' -%}

      {%- assign alt_parts = media.alt | replace_first: '#', "&" -%}
      {%- assign alt_parts = alt_parts | split: '&' -%}

      {%- assign filter_tag_part = alt_parts.last -%}
      {%- assign filter_tag_part_array = filter_tag_part | split: "#" -%}

      {%- assign total_filter_tags = filter_tag_part_array.size -%}

      {% assign filter_tag_match_count = 0 %}

      {%- for filter_tag in filter_tag_part_array -%}

        {%- assign filter_tag_processed = filter_tag | replace_first: '_', '&' -%}
        {%- assign filter_tag_processed_array = filter_tag_processed | split: '&' -%}
        {%- assign filter_tag_name = filter_tag_processed_array | first -%}
        {%- assign filter_tag_value = filter_tag_processed_array | last -%}

        {%- assign option = product.options_by_name[filter_tag_name] -%}
        {%- assign option_value = option.selected_value -%}

        {%- assign media_variant_name = filter_tag_value  | replace: "_", " " | strip -%}

        {%- if option_value == media_variant_name -%}
          {%- assign filter_tag_match_count = filter_tag_match_count | plus: 1 -%}
        {%- endif -%}

      {%- endfor -%}

      {%- if filter_tag_match_count == total_filter_tags -%}

      {%- else -%}
        {%- assign filtered_media_ids = filtered_media_ids | append: media.id | append: ',' -%}
      {%- endif -%}

    {%- endif -%}
  {%- endfor -%}

  {%- assign filtered_media_ids = filtered_media_ids | split: ',' | compact -%}

  {%- assign max_width = 0 -%}
  {%- assign largest_image_aspect_ratio = 999 -%}

  {%- for media in product.media -%}
    {%- assign max_width = max_width | at_least: media.preview_image.width -%}
    {%- assign largest_image_aspect_ratio = largest_image_aspect_ratio | at_most: media.preview_image.aspect_ratio -%}
  {%- endfor -%}

  {%- if product.media.size < 3 -%}
    {%- assign product_images_stacked = true -%}  
    {%- assign trustmark_placement_index = 1 -%}  
  {%- elsif product.media.size <= 3 -%}
    {%- assign product_images_stacked = false -%}  
    {%- assign product_images_stacked_first = true -%}  
    {%- assign trustmark_placement_index = 1 -%}  
  {%- else -%}
    {%- assign product_images_stacked = false -%}  
    {%- assign trustmark_placement_index = 2 -%}
  {%- endif -%}

  <product-media-tiled stagger-apparition form-id="{{ product_form_id }}"  {% if section.settings.enable_video_autoplay %}autoplay-video{% endif %} thumbnails-position="{{ section.settings.desktop_thumbnails_position }}" {% if settings.reveal_product_media %}reveal-on-scroll{% endif %} product-handle="{{ product.handle }}" class="product__media product-media--tiled {% if product_images_stacked == true %}product-media--tiled-full-width{% endif %} {% if product_images_stacked_first == true %}product-media--tiled-large-first{% endif %} hidden-pocket" style="--largest-image-aspect-ratio: {{ largest_image_aspect_ratio }}">

    {%- capture product_media_badges -%}
      {%- if product_has_tier_tag == true -%}
        {% if settings[tier_icon] != blank %}
          <div class="product-media-badge product-media-badge--{{ tier_tag | handle }}">
            {{- settings[tier_icon] | image_url: width: settings[tier_icon].width | image_tag: loading: 'lazy', sizes: '(max-width: 740px) 64px, 72px', sizes: '64,72,128,144,192,216', class: 'product-media-badge__image' -}}
          </div>
        {% endif %}
      {%- endif -%}
    {%- endcapture -%}

    {%- if product_media_badges != blank -%}
      <div class="product-media-badges">
        {{ product_media_badges }}
      </div>
    {%- endif -%}

    {% comment %} Product Labels {% endcomment %}
    {%- if section.settings.label_list_location == "media" -%}
      {% 
        render 'product-label-list', 
        classes: "product-media__label-list", 
        product: product, 
        product_has_tier_tag: product_has_tier_tag,
        tier_message_heading: settings[tier_message_heading],
        sale_stop: sale_stop
      %}
    {%- endif -%}

    <div class="product__media-list-wrapper" style="max-width: {{ max_width }}px">

      {%- for media in product.media -%}

        {%- assign alt = media.alt -%}
        {%- assign is_media_filtered = false -%}
        {%- assign media_id_as_string = media.id | append: '' -%}

        {% assign caption = blank %}

        {% if alt contains '+caption_' %}

          {%- capture caption -%}
            {%- assign caption_array = alt | split: "+caption_" -%}
            {%- assign caption_fragment = caption_array | last -%}
            {%- if caption_fragment contains '#shade_' -%}
              {{- caption_fragment | split: "#shade_" | first -}}
            {%- else -%}
              {{- caption_fragment -}}
            {%- endif -%}
          {%- endcapture -%}

        {%- endif -%}

        {% comment %}
        {% if media.alt contains '#' %}
          {%- assign alt = product.title -%}
        {%- endif -%}
        {% endcomment %}

        {%- if filtered_media_ids contains media_id_as_string and media.alt != product.title -%}
          {%- assign is_media_filtered = true -%}
        {%- endif -%}

        <div id="product-{{ section.id }}-{{ media.id }}" class="product__media-item
        {% if is_media_filtered %}is-filtered{% endif %}
        {% if selected_media.id == media.id %}is-initial-selected is-selected{% endif %}"
        data-media-type="{{ media.media_type }}"
        data-media-id="{{ media.id }}"
        data-original-position="{{ forloop.index0 }}"
        >
          {%- case media.media_type -%}
            {%- when 'image' -%}
              <div class="product__media-image-wrapper aspect-ratio aspect-ratio--natural" style="padding-bottom: {{ 100.0 | divided_by: media.aspect_ratio }}%; --aspect-ratio: {{ media.aspect_ratio }}">
                <img {% if selected_media.id != media.id or request.page_type != 'product' %}loading="lazy"{% endif %} sizes="(max-width: 999px) calc(100vw - 48px), 640px" {% render 'image-attributes', image: media, alt: alt, sizes: '400,500,600,700,800,900,1000,1100,1200,1300,1400,1500,1600,1700,1800' %}>
                <!-- <img {% if selected_media.id == media.id and settings.reveal_product_media %}reveal{% endif %} {% if selected_media.id != media.id or request.page_type != 'product' %}loading="lazy"{% endif %} sizes="(max-width: 999px) calc(100vw - 48px), 640px" {% render 'image-attributes', image: media, alt: alt, sizes: '400,500,600,700,800,900,1000,1100,1200,1300,1400,1500,1600,1700,1800' %}> -->
                {%- if caption != blank -%}
                  <span class="media-caption">{{ caption }}</span>
                {%- endif -%}
              </div>

            {%- when 'video' -%}
              <native-video class="video-wrapper video-wrapper--native" style="--aspect-ratio: {{ media.aspect_ratio }}">
                {%- unless section.settings.enable_video_autoplay -%}
                  <div class="video-wrapper__poster">
                    <img class="video-wrapper__poster-image" {% if selected_media.id != media.id or request.page_type != 'product' %}loading="lazy"{% endif %} sizes="(max-width: 999px) calc(100vw - 48px), 640px" {% render 'image-attributes', image: media.preview_image, sizes: '400,500,600,700,800,900,1000,1200,1400,1600,1800,2000,2200' %}>

                    <div class="video-wrapper__poster-content">
                      <button type="button" class="video-wrapper__play-button" title="{{ 'general.accessibility.play_video' | t | escape }}">
                        {%- render 'icon' with 'play', width: 72, height: 72 -%}
                      </button>
                    </div>
                  </div>
                {%- endunless -%}

                <template>
                  {{- media | video_tag: image_size: '1024x', controls: true, autoplay: true, muted: section.settings.enable_video_autoplay, loop: section.settings.enable_video_looping -}}
                </template>

                {%- if caption != blank -%}
                  <span class="media-caption">{{ caption }}</span>
                {%- endif -%}

              </native-video>

            {%- when 'external_video' -%}
              <external-video provider="{{ media.host | escape }}" class="video-wrapper">
                {%- unless section.settings.enable_video_autoplay -%}
                  <div class="video-wrapper__poster">
                    <img class="video-wrapper__poster-image" {% if selected_media.id != media.id or request.page_type != 'product' %}loading="lazy"{% endif %} sizes="(max-width: 999px) calc(100vw - 48px), 640px" {% render 'image-attributes', image: media.preview_image, sizes: '400,500,600,700,800,900,1000,1200,1400,1600,1800,2000,2200' %}>

                    <div class="video-wrapper__poster-content">
                      <button type="button" class="video-wrapper__play-button" title="{{ 'general.accessibility.play_video' | t | escape }}">
                        {%- render 'icon' with 'play', width: 72, height: 72 -%}
                      </button>
                    </div>
                  </div>
                {%- endunless -%}

                <template>
                  {%- if media.host == 'youtube' -%}
                    {{- media | external_video_url: enablejsapi: true, loop: section.settings.enable_video_looping, mute: section.settings.enable_video_autoplay, autoplay: true, playlist: media.external_id | external_video_tag -}}
                  {%- elsif media.host == 'vimeo' -%}
                    {{- media | external_video_url: autopause: true, loop: section.settings.enable_video_looping, muted: section.settings.enable_video_autoplay, autoplay: true | external_video_tag -}}
                  {%- else -%}
                    {{- media | external_video_tag: image_size: '1024x' -}}
                  {%- endif -%}
                </template>
              </external-video>

            {%- when 'model' -%}
              <model-media product-handle="{{ product.handle }}" class="model-wrapper">
                {{- media | model_viewer_tag: image_size: '1024x', reveal: 'interaction', toggleable: true -}}
              </model-media>
          {%- endcase -%}

          {%- if section.settings.enable_image_zoom -%}
            <button {% if selected_media.media_type != 'image' %}hidden{% endif %} is="toggle-button" aria-controls="product-{{ section.id }}-{{ product.id }}-{{ forloop.index }}-zoom" aria-expanded="false" class="tap-area product__zoom-button">
              <span class="visually-hidden">{{ 'product.general.zoom' | t }}</span>
              {%- render 'icon' with 'image-zoom' -%}
            </button>
          {%- endif -%}

          {%- if section.settings.enable_image_zoom -%}
            {%- comment -%}This code MUST NOT be changed, and is part of the PhotoSwipe required interface{%- endcomment -%}
            <product-image-zoom product-handle="{{ product.handle }}" id="product-{{ section.id }}-{{ product.id }}-{{ forloop.index }}-zoom" class="pswp" tabindex="-1" role="dialog">
              <div class="pswp__bg"></div>

              <div class="pswp__scroll-wrap">
                <div class="pswp__container">
                  <div class="pswp__item"></div>
                  <div class="pswp__item"></div>
                  <div class="pswp__item"></div>
                </div>

                <div class="pswp__ui pswp__ui--hidden">
                  <div class="pswp__top-bar">
                    <button class="pswp__button pswp__button--close prev-next-button" data-action="pswp-close" title="{{ 'general.accessibility.close' | t | escape }}">{% render 'icon' with 'close' %}</button>
                  </div>

                  <div class="pswp__prev-next-buttons hidden-pocket">
                    <button class="pswp__button prev-next-button prev-next-button--prev" data-action="pswp-prev" title="{{ 'general.accessibility.previous' | t | escape }}">{% render 'icon' with 'nav-arrow-left', direction_aware: true %}</button>
                    <button class="pswp__button prev-next-button prev-next-button--next" data-action="pswp-next" title="{{ 'general.accessibility.next' | t | escape }}">{% render 'icon' with 'nav-arrow-right', direction_aware: true %}</button>
                  </div>

                  <div class="pswp__dots-nav-wrapper hidden-lap-and-up">
                    <button class="tap-area tap-area--large" data-action="pswp-prev">
                      <span class="visually-hidden">{{ 'general.accessibility.previous' | t }}</span>
                      {%- render 'icon' with 'nav-arrow-left', direction_aware: true -%}
                    </button>

                    <div class="dots-nav dots-nav--centered">
                      <!-- This will be fill at runtime as the number of items will be dynamic -->
                    </div>

                    <button class="tap-area tap-area--large" data-action="pswp-next">
                      <span class="visually-hidden">{{ 'general.accessibility.next' | t }}</span>
                      {%- render 'icon' with 'nav-arrow-right', direction_aware: true -%}
                    </button>
                  </div>
                </div>
              </div>
            </product-image-zoom>
          {%- endif -%}
          
        </div>

        {% if forloop.index == trustmark_placement_index %}

          <div class="product-media-banners">

            <button class="product-media-banner" is="toggle-button" aria-controls="product-media-banner-1-drawer" aria-expanded="false">
              
              {% if section.settings.media_banner_1_icon != blank %}
                <div class="product-media-banner__icon">
                  {{ section.settings.media_banner_1_icon }}
                </div>
              {% endif %}
              
              {% if section.settings.media_banner_1_title != blank %}
                <div class="product-media-banner__title heading h5">
                  {{ section.settings.media_banner_1_title }}
                </div>
              {% endif %}
              
              {% if section.settings.media_banner_1_text != blank %}
                <div class="product-media-banner__text text text--xsmall text--subdued">
                  {{ section.settings.media_banner_1_text }}
                </div>
              {% endif %}

              {% if section.settings.media_banner_1_link_text != blank %}
                <div class="product-media-banner__link text text--xsmall icon-text link">
                  {{ section.settings.media_banner_1_link_text }}
                </div>
              {% endif %}

            </button>

            <button class="product-media-banner" is="toggle-button" aria-controls="product-media-banner-2-drawer" aria-expanded="false">
              
              {% if section.settings.media_banner_2_icon != blank %}
                <div class="product-media-banner__icon">
                  {{ section.settings.media_banner_2_icon }}
                </div>
              {% endif %}
              
              {% if section.settings.media_banner_2_title != blank %}
                <div class="product-media-banner__title heading h5">
                  {{ section.settings.media_banner_2_title }}
                </div>
              {% endif %}
              
              {% if section.settings.media_banner_2_text != blank %}
                <div class="product-media-banner__text text text--xsmall text--subdued">
                  {{ section.settings.media_banner_2_text }}
                </div>
              {% endif %}

              {% if section.settings.media_banner_2_link_text != blank %}
                <div class="product-media-banner__link text text--xsmall icon-text link">
                  {{ section.settings.media_banner_2_link_text }}
                </div>
              {% endif %}

            </button>

            <button class="product-media-banner" is="toggle-button" aria-controls="product-media-banner-3-drawer" aria-expanded="false">
              
              {% if section.settings.media_banner_3_icon != blank %}
                <div class="product-media-banner__icon">
                  {{ section.settings.media_banner_3_icon }}
                </div>
              {% endif %}
              
              {% if section.settings.media_banner_3_title != blank %}
                <div class="product-media-banner__title heading h5">
                  {{ section.settings.media_banner_3_title }}
                </div>
              {% endif %}
              
              {% if section.settings.media_banner_3_text != blank %}
                <div class="product-media-banner__text text text--xsmall text--subdued">
                  {{ section.settings.media_banner_3_text }}
                </div>
              {% endif %}

              {% if section.settings.media_banner_3_link_text != blank %}
                <div class="product-media-banner__link text text--xsmall icon-text link">
                  {{ section.settings.media_banner_3_link_text }}
                </div>
              {% endif %}

            </button>

            {% comment %}
              Drawers
            {% endcomment %}

            <drawer-content id="product-media-banner-1-drawer" class="drawer drawer--large hidden-phone">
              <span class="drawer__overlay"></span>

              <header class="drawer__header">
                <p class="drawer__title heading h6">
                  {{ section.settings.media_banner_1_icon }}
                  {{ section.settings.media_banner_1_title }}
                </p>

                <button type="button" class="drawer__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
                  {%- render 'icon' with 'close' -%}
                </button>
              </header>

              <div class="drawer__content drawer__content--padded-start">
                <div class="rte">
                  {{ section.settings.media_banner_1_link_content_text }}
                </div>
              </div>
            </drawer-content>

            <drawer-content id="product-media-banner-2-drawer" class="drawer drawer--large hidden-phone">
              <span class="drawer__overlay"></span>

              <header class="drawer__header">
                <p class="drawer__title heading h6">
                  {{ section.settings.media_banner_2_icon }}
                  {{ section.settings.media_banner_2_title }}
                </p>

                <button type="button" class="drawer__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
                  {%- render 'icon' with 'close' -%}
                </button>
              </header>

              <div class="drawer__content drawer__content--padded-start">
                <div class="rte">
                  {{ section.settings.media_banner_2_link_content_text }}
                </div>
              </div>
            </drawer-content>

            <drawer-content id="product-media-banner-3-drawer" class="drawer drawer--large hidden-phone">
              <span class="drawer__overlay"></span>

              <header class="drawer__header">
                <p class="drawer__title heading h6">
                  {{ section.settings.media_banner_3_icon }}
                  {{ section.settings.media_banner_3_title }}
                </p>

                <button type="button" class="drawer__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
                  {%- render 'icon' with 'close' -%}
                </button>
              </header>

              <div class="drawer__content drawer__content--padded-start">
                <div class="rte">
                  {{ section.settings.media_banner_3_link_content_text }}
                </div>
              </div>
            </drawer-content>

          </div>
            
        {% endif %}
        
      {%- endfor -%}

    </div>

    {%- comment -%}Add the "view in your space" button, which allows to show the product in customer's environment (if the device supports it){%- endcomment -%}
    {%- assign first_3d_model = product.media | where: 'media_type', 'model' | first -%}

    {%- if first_3d_model -%}
      <button class="product__view-in-space button button--ternary button--full" data-shopify-xr data-shopify-model3d-id="{{ first_3d_model.id }}" data-shopify-model3d-default-id="{{ first_3d_model.id }}" data-shopify-title="{{ product.title | escape }}" data-shopify-xr-hidden>
        {%- render 'icon', icon: 'media-view-in-space' -%} {{- 'product.general.view_in_space' | t -}}
      </button>
    {%- endif -%}

  </product-media-tiled>
{%- endif -%}
