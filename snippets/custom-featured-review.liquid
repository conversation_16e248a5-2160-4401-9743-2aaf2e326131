{%- liquid

  if rich_review == blank and block != blank
    assign rich_review = block.settings
  endif

  if section.settings.image_ratio == "natural"
    if rich_review.image != blank
      assign image_ratio = rich_review.image.aspect_ratio
    else
      assign image_ratio = 1.77
    endif
  endif

  comment
  Metaobject Property Parsing
  endcomment

  if rich_review.image.value
    assign rich_review_image = rich_review.image.value
  else
    assign rich_review_image = block.settings.image
  endif

  if rich_review.shopify_video.value
    assign rich_review_video = rich_review.shopify_video
  else
    assign rich_review_video = block.settings.shopify_video
  endif

  if rich_review.text.value
    assign rich_review_text = rich_review.text | metafield_tag
  else
    assign rich_review_text = block.settings.text
  endif

  if rich_review.author.value
    assign rich_review_author = rich_review.author.value
  else
    assign rich_review_author = block.settings.author 
  endif

  unless product
    if rich_review.product.value
      assign product = rich_review.product.value
    else
      assign product = block.settings.product
    endif
  endunless

-%}

<li
  class="rich-review-container grow w-full max-w-none{% if carousel %} slider__item{% endif %}"
  {{ block.shopify_attributes -}}
  {% if settings.animations_enabled != 'disabled' %}
    data-cc-animate data-cc-animate-delay="{{ forloop.index | times: 0.07 }}s"
  {% endif %}
>
  
  <div class="rich-review card border-radius--overlay {% if rich_review.video_shopify != blank %} video-section{% endif %} relative flex flex-col h-full {% unless section.settings.card_color_scheme == 'none' %} color-scheme color-scheme--{{ section.settings.card_color_scheme }}{% endunless %}">
    
    {%- liquid
      if section.settings.column_size == 'small'
        assign cols = 3
      else
        assign cols = 2
      endif
      capture sizes
        render 'sizes-attribute', grid: true, min: 1, md: cols
      endcapture
    -%}

    {%- capture card_media_content -%}

      {%- if rich_review_image != blank or rich_review.video_shopify != blank -%}

        {%- if rich_review.video_shopify.value -%}
          {%- assign video = rich_review.video_shopify.value -%}
        {%- else -%}
          {%- assign video = rich_review.video_shopify -%}
        {%- endif -%}

        {%- if video != blank -%}
          <deferred-media class="media absolute top-0 left-0 w-full h-full">
            <template>
              <video-component
                class="has-iframe absolute top-0 left-0 w-full h-full no-js-hidden"
                data-autoplay="true"
                data-background="true"
              >
                {{
                  video
                  | video_tag:
                    class: 'img-fit promo__mp4 pointer-events-none',
                    playsinline: true,
                    autoplay: true,
                    muted: true,
                    loop: true,
                    controls: false,
                    poster: ''
                  | replace: '<img ', '<img loading="lazy" hidden '
                }}
              </video-component>
            </template>
          </deferred-media>
        {%- endif -%}

        {%- if rich_review_image != blank %}
          {%- liquid
            assign image_class = 'video-played-hidden'
            unless section.settings.image_ratio == 'natural'
              assign image_class = image_class | append: ' img-fit'
            endunless
            if section.settings.image_ratio == 'natural' and video != blank
              assign image_class = image_class | append: ' img-fit'
            endif
          -%}
          {% render 'image',
            image: rich_review_image,
            widths: '460, 700, 860, 1296',
            src_width: 700,
            sizes: sizes,
            section_index: section.index,
            class: image_class
          %}
        {%- endif -%}

      {%- endif -%}

    {%- endcapture -%}

    {%- if card_media_content != blank -%}
      <div
        class="card__media media relative w-full{% if section.settings.image_ratio == "natural" %} {{ section.settings.media_align }}{% endif %}"
        style="{% unless section.settings.image_ratio == "natural" %}padding-top: {{ 1 | divided_by: image_ratio | times: 100 }}%;{% endunless %}{% if section.settings.image_ratio == "natural" and rich_review.video_shopify %}padding-top: 56.25%;{% endif %}{% if rich_review.media_scale < 100 %} transform: scale({{ rich_review.media_scale | divided_by: 100.0 }});{% endif %}"
      >
        {{ card_media_content }}      
      </div>
    {%- endif -%}

    <div class="card__info flex flex-col flex-auto items-start pt-6{% if section.settings.column_align == 'center' %} text-center items-center{% endif %}">

      {%- if rich_review.rating != blank -%}
        {%- assign rating = rich_review.rating -%}
        {% render 'rating--static',
          rating_value: rating
        %}
      {%- endif -%}

      {%- if rich_review.heading != blank -%}
        <h3 class="h4 rich-review__title card__title">{{ rich_review.heading | escape }}</h3>
      {%- endif -%}

      {%- if product != blank -%}
        <a href="{{ product.url }}" class="rich-review__product">
          <span class="rich-review__product-image">
            {% render 'image',
              image: product.featured_media.preview_image,
              widths: '320',
              src_width: 100,
              src_placeholder: true,
              disable_focal_point: true
            %}
          </span>
          <span class="rich-review__product-title">
            {{ product.title | escape }}
          </span>
        </a>
      {%- endif -%}

      {%- capture review_content -%}

        {%- if rich_review_text != blank -%}
          <span class="rich-review__text rte">{{ rich_review_text }}</span>
        {%- endif -%}

        {%- if rich_review_author != blank -%}
          <span class="rich-review__author h6">{{ rich_review_author }}</span>
        {%- endif -%}
        
      {%- endcapture -%}

      {%- if review_content != blank -%}
        {{ review_content }}
      {%- endif -%}

      {% if rich_review.button_link %}
        <div class="mt-8{% if section.settings.color_scheme == 'none' and carousel == false %} mb-8{% endif %}">
          <a
            class="card__button{% if rich_review.button_label == blank %}{% if section.settings.button_style == 'link' %} btn--primary{% else %} {{ section.settings.button_style }}{% endif %} btn btn--icon btn--lg has-ltr-icon{% else %} {{ section.settings.button_style }}{% endif %}"
            {% if rich_review.button_link != blank %}
              href="{{ rich_review.button_link }}"
            {% else %}
              role="link" aria-disabled="true"
            {% endif %}
          >
            {%- if rich_review.button_label != blank -%}
              {{- rich_review.button_label | escape -}}
            {%- else -%}
              <span class="visually-hidden">
                {{- 'general.labels.please_select' | t -}}
                {%- if rich_review.heading != blank %} {{ rich_review.heading }}{% endif -%}
              </span>
              {%- render 'icon-arrow-right' -%}
            {%- endif -%}
          </a>
        </div>
      {% endif %}
    </div>

  </div>
</li>
