<div class="collection-grid-banner collection-grid-banner--span-{{ block.settings.column_span }} {% if block.settings.column_span >= 2 %}collection-grid-banner--mobile-fullwidth{% endif %} collection-grid-banner--{{ block.settings.media_size }}" id="block-{{ section.id }}-{{ block.id }}" {% if reveal %}reveal{% endif %} {{ block.shopify_attributes }}>

  {%- if block.settings.image != blank or block.settings.video_url != blank -%}

    {% assign image_size_string = 350 | times: block.settings.column_span | append: "x" %}

    {%- if block.settings.link_url != blank -%}
      <a class="collection-grid-banner__media" href="{{ block.settings.link_url }}">
    {%- else -%}
      <div class="collection-grid-banner__media">
    {%- endif -%}

      {%- if block.settings.overlay_subheading != blank or block.settings.overlay_title != blank or block.settings.overlay_text != blank -%}

        <div class="collection-grid-banner__overlay">
        
          <div class="collection-grid-banner__overlay-inner">

            {%- if block.settings.overlay_subheading != blank -%}
              <p class="collection-grid-banner__overlay-subheading heading heading--xsmall">{{ block.settings.overlay_subheading }}</p>
            {%- endif -%}

            {%- if block.settings.overlay_title != blank -%}
              <p class="collection-grid-banner__overlay_title heading h3">{{ block.settings.overlay_title }}</p>
            {%- endif -%}

            {%- if block.settings.overlay_text != blank -%}
              <div class="collection-grid-banner__overlay_text rte">{{ block.settings.overlay_text }}</div>
            {%- endif -%}

            {%- if block.settings.overlay_link_text != blank -%}
              <div class="button-wrapper">
                <div class="link link--animted">{{ block.settings.overlay_link_text }}</div>
              </div>
            {%- endif -%}

          </div>

        </div>

      {%- endif -%}

      {%- if block.settings.video_filename != blank -%}

        <video
          playsinline
          loop
          muted
          autoplay
          {% if block.settings.image != blank %}
          poster="{{ block.settings.image | img_url: image_size_string }}"
          {% endif %}
          id="CollectionGridVideo-{{ block.id }}"
          class="collection-grid-banner__video">
            <source src="{{ block.settings.video_filename | file_url }}" type="{{ source.mime_type }}">
            Your browser does not support the video tag.
        </video>

      {%- elsif block.settings.video_url != blank -%}

        <external-video autoplay provider="{{ block.settings.video_url.type | escape }}" class="video-wrapper video-wrapper--{{ block.settings.media_size }}">
          <template>
            {%- if block.settings.video_url.type == 'youtube' -%}
              <iframe title="Video" id="player-{{ section.id }}" src="https://www.youtube.com/embed/{{ block.settings.video_url.id }}?playsinline=1&autoplay=1&mute=1&loop=1&playlist={{ block.settings.video_url.id }}&enablejsapi=1&controls=0&rel=0&modestbranding=1&origin=https://{{ request.host }}" allow="autoplay; fullscreen"></iframe>
            {%- elsif block.settings.video_url.type == 'vimeo' -%}
              <iframe title="Video" id="player-{{ section.id }}" src="https://player.vimeo.com/video/{{ block.settings.video_url.id }}?background=1&loop=1&transparent=0&responsive=1&portrait=0&title=0&byline=0&color={{ settings.text_color | remove_first: '#' }}" allow="autoplay; fullscreen"></iframe>
            {%- endif -%}
          </template>
        </external-video>

      {%- elsif block.settings.image != blank -%}

        <img 
          {% render 'image-attributes', image: block.settings.image, sizes: '200,300,400,500,600,700,800,900,1000,1100,1200,1300,1400,1500,1600' %}
          alt="{{ block.settings.image.alt }}">

      {%- endif -%}

    {%- if block.settings.link_url != blank -%}
      </a>
    {%- else -%}
      </div>
    {%- endif -%}

  {%- endif -%}

</div>
