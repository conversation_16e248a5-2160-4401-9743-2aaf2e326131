{%- comment -%}
  Parameters:
  - value_name {string} - Name of swatch color in embroidery settings.

  Usage:
  {% render 'addons-swatch-value', value_name: 'Mahogany' %}
{%- endcomment -%}

{%- liquid

  assign swatch_lines = settings.embroidery_swatch_colors | newline_to_br | strip_newlines | split: '<br />'
  assign swatch_value = blank
  assign value_name_stripped = value_name | strip

  for line in swatch_lines
  
    assign line_split = line | split: ':'
    
    if value_name_stripped == line_split.first

      if line_split.last contains '#'
        assign swatch_value = line_split.last
      else
        assign url = line_split.last | strip | file_img_url: '250x250'
        assign image_url = "url(" | append: url | append: ')'
        assign swatch_value = image_url
      endif
      
      break

    endif

  endfor

 -%}

{% if swatch_value != blank %}style="background: {{ swatch_value }}"{% endif %}