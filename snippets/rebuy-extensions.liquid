<!-- Rebuy Speed Load Script -->
<script async src="https://cdn.rebuyengine.com/onsite/js/rebuy.js?shop={{ shop.permanent_domain }}"></script>
<!-- General Theme / Rebuy Styling -->

{% if customer and (customer.tags contains "tier: Platinum" or customer.tags contains "tier: Gold" or customer.tags contains "tier: Silver") %}

<script>
function adjustTPB() {
        console.log('Rebuy SmartCart loaded');
        Rebuy.SmartCart.progressBar.tiers.forEach((tier) => {
            if (tier.type === "shipping" && Shopify.country === 'US') {
                tier.minimum = 60;
                console.log('Set USD shipping minimum to 60');
            } else if (tier.type === "shipping" && Shopify.country === 'CA') {
                tier.minimum = 90;
                console.log('Set CAD shipping minimum to 90');
            }
        });
    }


// Listen for the rebuy:ready event
document.addEventListener('rebuy:smartcart.ready', function() {
    console.log('Rebuy ready event fired');
    adjustTPB();
});
</script>
{% endif %}

<!-- Custom helper functions for custom templates -->
<script>
window.toggleProductOptions = function(productId, widgetId) {
    const accordion = document.querySelector(`#rebuy-widget-${widgetId} .rebuy-product-block.product-id-${productId} .rebuy-accordion`);
    if (accordion) {
        accordion.classList.toggle('open');
        console.log(`Toggled accordion for product-id-${productId} in widget-id-${widgetId}`);
    } else {
        console.log(`Accordion not found for product-id-${productId} in widget-id-${widgetId}`);
    }
};


  window.handleSizeChange = function(product, size, productIndex) {
    selectVariantBySize(product, size, productIndex);
    setTimeout(() => {
      const selectedVariant = product.selected_variant || product.variants[0];
      console.log('Selected variant:', selectedVariant);
      addToCart1(selectedVariant);
    }, 0);
  };

  window.selectVariantBySize = function(product, size, productIndex) {
    const variant = product.variants.find(v => v.option1 === size || v.title.includes(size));
    if (variant) {
      product.selected_variant_id = variant.id;
      product.selected_variant = variant;
      console.log('Updated selected_variant:', product.selected_variant);
    } else {
      console.log('No variant found for size:', size);
    }
  };

  window.addToCart1 = function(variant) {
    if (window.Rebuy && window.Rebuy.Cart && variant) {
      window.Rebuy.Cart.addItem({
        quantity: 1, // Default quantity; adjust as needed
        id: variant.id
      });
      console.log('Added to cart: Variant ID', variant.id, 'Quantity:', 1);
    } else {
      console.log('Error: Rebuy.Cart not available or variant is invalid');
    }
  };
</script>

<!--  Manual Widget Installs BELOW -->
{% if product %}
<!-- PDP Complete The Look Widget Install -->
<div data-rebuy-id="201560" data-rebuy-shopify-product-ids="{{ product.id }}"></div>
<!-- PDP Carousel Widget Install -->
<div data-rebuy-id="201563" data-rebuy-shopify-product-ids="{{ product.id }}"></div>
<!-- PDP Recently Viewed Widget Install -->
<div data-rebuy-id="201565" data-rebuy-shopify-product-ids="{{ product.id }}"></div>
<!-- PDP Dynamic Bundle Widget Install -->
<div data-rebuy-id="201567" data-rebuy-shopify-product-ids="{{ product.id }}"></div>
{% endif %}

<!-- Dynamic Bundle Template -->
{% raw %}
<script id="rebuy-widget-201567" type="text/template">
  <div class="rebuy-widget"
    v-cloak
    v-on:click="stopPropagation($event)"
    v-bind:id="'rebuy-widget-' + id"
    v-bind:class="['widget-type-' + config.type.replace('_','-'), products.length > 0 ? 'is-visible' : 'is-hidden']">
    
    <div
      class="rebuy-widget-container"
      v-bind:class="['widget-display-' + config.display_type, visible ? 'is-visible' : 'is-hidden' ]">
      <div class="rebuy-widget-content">
        <div class="rebuy-timer" v-if="hasTimer()">
          <h5 class="rebuy-timer-title" v-if="config.language.timer_title">
            <span v-html="config.language.timer_title"></span> <span class="rebuy-timer-minutes" v-html="config.timer.duration_minutes"></span>:<span class="rebuy-timer-seconds" v-html="config.timer.duration_seconds"></span>
          </h5>
        </div>
        
        <h4 class="super-title" v-if="config.language.super_title != ''" v-html="config.language.super_title"></h4>

        <h3 class="primary-title" v-if="config.language.title != ''" v-html="config.language.title"></h3>
              
        <div class="description" v-if="config.language.description != ''" v-html="config.language.description"></div>
        
        <div class="rebuy-bundle">
        
          <div class="rebuy-bundle__images">

            <div class="rebuy-bundle__image" v-for="(product, index) in products" v-if="product.selected" v-bind:class="[product.handle, 'product-id-' + product.id, cartHasProduct(product) ? 'cart-has-item' : '', productTagClasses(product)]">
              <a class="rebuy-bundle__image-item" v-bind:href="learnMoreURL(product)" v-bind:style="imageStyles" v-on:click="learnMore(product);" v-bind:class="[hasLearnMore() && !isInputProduct(product) ? 'clickable' : '' ]">
                <img v-bind:src="itemImage(product, product.selected_variant, '400x400')" v-bind:alt="product.title">
              </a>
              <div class="rebuy-bundle__image-divider">
                <i class="far fa-plus"></i>
              </div>
            </div>
            
          </div>

                    <div class="rebuy-bundle__actions rebuy-mobile" v-if="subtotal() > 0">
                        
                        <div class="rebuy-bundle__actions-price">
                            <strong class="rebuy-bundle__actions-price-label" v-html="config.language.total_price_label"></strong>
                            
                            <div class="rebuy-bundle__actions-price-value" v-if="bundleOnSale()">
                                <span class="rebuy-money sale" v-html="formatMoney(subtotal()).replaceAll('.00', ' ') + Shopify.currency.active"></span>
                                <span class="rebuy-money compare-at" v-html="formatMoney(compareAtSubtotal()).replaceAll('.00', ' ') + Shopify.currency.active"></span>
                            </div>
                            <div class="rebuy-bundle__actions-price-value" v-if="!(bundleOnSale())">
                                <span class="rebuy-money" v-html="formatMoney(subtotal()).replaceAll('.00', ' ') + Shopify.currency.active"></span>
                            </div>
                        </div>
                        
                        <div class="rebuy-bundle__actions-buttons">
                            <button
                                class="rebuy-button"
                                v-on:click="addSelectedProductsToCart()"
                                type="button">
                                    <span v-html="buttonWidgetLabel()"></span>
                            </button>
                        </div>
                    </div>
          
          <div class="rebuy-bundle__items">
            

              <div class="rebuy-product-block" v-for="(product, product_index) in products" v-bind:class="[product.handle, 'product-id-' + product.id, cartHasProduct(product) ? 'cart-has-item' : '', productTagClasses(product), isInputProduct(product) ? 'is-input-product' : '' ,product.selected ? 'is-selected': '']">
              
              <div class="rebuy-product-info">
                              <a class="rebuy-bundle__image-item bundle-single-image" v-bind:href="learnMoreURL(product)" v-bind:style="imageStyles" v-on:click="learnMore(product);" v-bind:class="[hasLearnMore() && !isInputProduct(product) ? 'clickable' : '' ]">
                  <img v-bind:src="itemImage(product, product.selected_variant)" v-bind:alt="product.title">
                </a>
                                <div class="rebuy-product-checkbox">
                                    <label class="rebuy-checkbox-label">
                                        <input
                                            class="checkbox-input rebuy-checkbox"
                                            v-model="product.selected"
                                            type="checkbox" />
                                    </label>                                  
                                </div>
                                <strong class="rebuy-product-label" v-if="isInputProduct(product)" v-html=""></strong>
                <a class="rebuy-product-title" v-bind:href="learnMoreURL(product)" v-on:click="learnMore(product);" v-html="product.title" v-bind:class="[hasLearnMore() && !isInputProduct(product) ? 'clickable' : '']"></a>
                <div class="rebuy-variant-title" v-if="showVariantTitle(product)" v-html="product.selected_variant.title"></div>
                <div class="rebuy-product-review" v-if="hasProductReviews(product)">
                  <span class="rebuy-star-rating">
                    <span class="rebuy-star-rating-background"></span>
                    <span class="rebuy-star-rating-foreground" v-bind:style="{ width: productReviewRatingPercentage(product) }"></span>
                  </span>
                  <span class="rebuy-review-count" v-html="productReviewCount(product)"></span>
                </div>

                <div class="rebuy-product-price">
                  <div v-if="variantOnSale(product, product.selected_variant)">
                    <span class="rebuy-money sale" v-html="formatMoney(variantPrice(product, product.selected_variant)).replaceAll('.00', ' ') + Shopify.currency.active"></span>
                    <span class="rebuy-money compare-at" v-html="formatMoney(variantCompareAtPrice(product, product.selected_variant)).replaceAll('.00', ' ') + Shopify.currency.active"></span>
                  </div>
                  <div v-if="!(variantOnSale(product, product.selected_variant))">
                    <span class="rebuy-money" v-html="formatMoney(variantPrice(product, product.selected_variant)).replaceAll('.00', ' ') + Shopify.currency.active"></span>
                  </div>
                </div>
                <div class="rebuy-product-description" v-if="showProductDescription(product)" v-html="text(product.body_html)"></div>
              </div>
              <div class="rebuy-product-options" v-if="showVariantSelect(product)">
                <select
                  :id="id + '-' + 'select' + '-' + product_index"
                  :class="{ hide : settings.view_options.variant_selector == 'buttons' }"
                  class="rebuy-select"
                  v-model="product.selected_variant_id"
                  v-on:change="selectVariant(product)">
                  <option v-for="variant in product.variants" v-bind:value="variant.id">{{ variant.title }}</option>
                </select>

                <div v-if="option.name == 'Color' && displayColorSwatches()" v-for="(option, option_index) in product.options" class="rebuy-color-swatches">
                  <div v-for="(value, value_index) in option.values" class="rebuy-color-swatch">
                    <input
                      :name="id + '-color-' + product_index" 
                      :id="id + '-color-' + option_index + '-' + value + '-' + value_index"
                      :checked="value_index == 0" 
                      :value="value"
                      type="radio" 
                      class="rebuy-color-input hide"
                      v-on:change="selectVariantByColor(product, value, product_index)">
                    <label
                      :for="id + '-color-' + option_index + '-' + value + '-' + value_index"
                      :style="{ backgroundColor: value }"
                      :title="value"
                      class="rebuy-color-label"></label>
                  </div>
                </div>

                <div v-if="option.name == 'Size' && settings.view_options.variant_selector == 'buttons'" v-for="(option, option_index) in product.options" class="mt-10 rebuy-size-swatches">
                  <div v-for="(value, value_index) in option.values" class="rebuy-size-swatch">
                    <input
                      :name="id + '-size-' + product_index" 
                      :id="id + '-size-' + product_index + '-' + value"
                      :checked="value_index == 0" 
                      :value="value"
                      type="radio" 
                      class="rebuy-size-input hide"
                      v-on:change="selectVariantBySize(product, value, product_index)">
                    <label
                      :for="id + '-size-' + product_index + '-' + value"
                      class="rebuy-size-label">{{ value }}</label>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
        
      </div>
    </div>
              
        <div class="rebuy-bundle__actions rebuy-desktop" v-if="subtotal() > 0">
            
            <div class="rebuy-bundle__actions-price">
                <strong class="rebuy-bundle__actions-price-label" v-html="config.language.total_price_label"></strong>
                
                <div class="rebuy-bundle__actions-price-value" v-if="bundleOnSale()">
                    <span class="rebuy-money sale" v-html="formatMoney(subtotal()).replaceAll('.00', ' ') + Shopify.currency.active"></span>
                    <span class="rebuy-money compare-at" v-html="formatMoney(compareAtSubtotal()).replaceAll('.00', ' ') + Shopify.currency.active"></span>
                </div>
                <div class="rebuy-bundle__actions-price-value" v-if="!(bundleOnSale())">
                    <span class="rebuy-money" v-html="formatMoney(subtotal()).replaceAll('.00', ' ') + Shopify.currency.active"></span>
                </div>
            </div>
            
            <div class="rebuy-bundle__actions-buttons">
                <button
                    class="rebuy-button"
                    v-on:click="addSelectedProductsToCart()"
                    type="button">
                        <span v-html="buttonWidgetLabel()"></span>
                </button>
            </div>
        </div>
          
  </div>
  </div>
</script>
{% endraw %}

<!-- PDP Recently Viewed Carousel Template -->
{% raw %}
<script id="rebuy-widget-201565" type="text/template">
        <div class="rebuy-widget"
        v-cloak
        v-on:click="stopPropagation($event)"
        v-bind:id="'rebuy-widget-' + id"
        v-bind:class="['widget-type-' + config.type.replace('_','-'), 'widget-display-' + config.display_type, products.length > 0 ? 'is-visible' : 'is-hidden', 'widget-layout-' + currentLayout()]">

        <div
            class="rebuy-widget-container"
            v-cloak
            v-bind:class="['widget-display-' + config.display_type, visible ? 'is-visible' : 'is-hidden' ]"
            v-on:click.self="hide()">

            <div class="rebuy-widget-content">
                <div class="rebuy-modal-close" v-on:click="hide()">
                    <i class="fas fa-times"></i>
                </div>

                <div class="rebuy-timer" v-if="hasTimer()">
                    <h5 class="rebuy-timer-title" v-if="config.language.timer_title">
                        <span v-html="config.language.timer_title"></span> <span class="rebuy-timer-minutes" v-html="config.timer.duration_minutes"></span>:<span class="rebuy-timer-seconds" v-html="config.timer.duration_seconds"></span>
                    </h5>
                </div>

                <h4 class="super-title" v-if="config.language.super_title != ''" v-html="config.language.super_title"></h4>

                <h3 class="primary-title" v-if="config.language.title != ''" v-html="config.language.title"></h3>

                <div class="description" v-if="config.language.description != ''" v-html="config.language.description"></div>

                <div class="rebuy-product-grid" v-bind:class="layoutClasses">

                    <div class="rebuy-product-block" v-for="(product, product_index) in products" v-bind:class="[product.handle, 'product-id-' + product.id, cartHasProduct(product) ? 'cart-has-item' : '', productTagClasses(product)]">
<div class="rebuy-product-block-holder">
<div class="rebuy-product-media">
    <a class="rebuy-product-image" 
       v-bind:href="learnMoreURL(product)" 
       v-bind:style="imageStyles" 
       v-on:click="learnMore(product);" 
       v-bind:class="[hasLearnMore() ? 'clickable' : '']">
       
        <!-- Primary image -->
        <img class="rebuy-product-img" 
             v-if="product?.images[0]?.src" 
             v-bind:src="itemImage(product, product.selected_variant, '500x500')" 
             v-bind:alt="'View ' + product.title">
             
        <!-- Secondary image (hover image), only rendered if it exists -->
        <img class="rebuy-product-hover-img" 
             v-if="product?.images[1]?.src" 
             v-bind:src="product.images[1].src.replace(/(\.jpg)/, '_500x500$1')" 
             v-bind:alt="'View ' + product.title">
    </a>
</div>

                        <div class="rebuy-product-info">
                            <a class="rebuy-product-title" v-bind:href="learnMoreURL(product)" v-on:click="learnMore(product);" v-html="product.title" v-bind:class="[hasLearnMore() ? 'clickable' : '']" v-bind:alt="'View ' + product.title"></a>
                            <div class="rebuy-variant-title" v-if="showVariantTitle(product)" v-html="product.selected_variant.title"></div>
                            <div class="rebuy-product-review" v-if="hasProductReviews(product)">
                                <span class="rebuy-star-rating">
                                    <span class="rebuy-star-rating-background"></span>
                                    <span class="rebuy-star-rating-foreground" v-bind:style="{ width: productReviewRatingPercentage(product) }"></span>
                                </span>
                                <span class="rebuy-review-count" v-html="productReviewCount(product)"></span>
                            </div>

                            <div class="rebuy-product-price">
                                <div v-if="variantOnSale(product, product.selected_variant)">
                                    <span class="rebuy-money sale" v-html="formatMoney(variantPrice(product, product.selected_variant)).replaceAll('.00', ' ') + Shopify.currency.active"></span>
                                    <span class="rebuy-money compare-at" v-html="formatMoney(variantCompareAtPrice(product, product.selected_variant)).replaceAll('.00', ' ') + Shopify.currency.active"></span>
                                </div>
                                <div v-if="!(variantOnSale(product, product.selected_variant))">
                                    <span class="rebuy-money" v-html="formatMoney(variantPrice(product, product.selected_variant)).replaceAll('.00', ' ') + Shopify.currency.active"></span>
                                </div>
                            </div>
                            <div class="rebuy-product-description" v-if="showProductDescription(product)" v-html="text(product.body_html)"></div>
                        </div>
                        <div class="rebuy-product-options" v-if="showVariantSelect(product)">
                            <select
                                :id="id + '-' + 'select' + '-' + product_index"
                                :class="{ hide : settings.view_options.variant_selector == 'buttons' }"
                                class="rebuy-select"
                                v-model="product.selected_variant_id"
                                v-on:change="selectVariant(product)">
                                <option v-for="variant in product.variants" v-bind:value="variant.id">{{ variant.title }}</option>
                            </select>


                            <div v-if="displaySizeSwatches(option)" v-for="option in product.options" class="mt-10 rebuy-size-swatches">
                                <div v-for="(value, value_index) in option.values" class="rebuy-size-swatch">
                                    <input
                                        :name="id + '-size-' + product_index"
                                        :id="id + '-size-' + product_index + '-' + value"
                                        :checked="value_index == 0"
                                        :value="value"
                                        type="radio"
                                        class="rebuy-size-input hide"
                                        v-on:change="selectVariantBySize(product, value, product_index)">
                                    <label
                                        :for="id + '-size-' + product_index + '-' + value"
                                        class="rebuy-size-label">{{ value }}</label>
                                </div>
                            </div>
                        </div>
                        <div class="rebuy-product-actions">

                            <div class="subscription-checkbox" v-if="showSubscriptionOptions(product)">
                                <label class="rebuy-checkbox-label">
                                    <input
                                        class="checkbox-input rebuy-checkbox"
                                        v-model="product.subscription"
                                        v-on:change="toggleSubscription(product)"
                                        type="checkbox" />
                                    <span class="checkbox-label" v-html="upgradeToSubscriptionLabel(product)"></span>
                                </label>
                            </div>

                            <div class="subscription-frequency" v-if="showSubscriptionFrequency(product)">
                                <select
                                    class="rebuy-select"
                                    v-model="product.subscription_frequency"
                                    v-on:change="updateSubscriptionFrequency(product)">
                                    <option v-for="frequency in product.subscription_frequencies" v-bind:value="frequency">{{ frequencyLabel(frequency, product.subscription_interval) }}</option>
                                </select>
                            </div>

                            <div class="product-quantity" v-if="hasQuantityInputEnabled()">
                                <div class="rebuy-select-wrapper">
                                    <label class="rebuy-label">Quantity</label>
                                    <select
                                        class="rebuy-select"
                                        v-model="product.quantity">
                                        <option v-for="n in maxQuantityInputValue()" v-bind:value="n">{{ n }}</option>
                                    </select>
                                </div>
                            </div>

                            <button
                                class="rebuy-button"
                                v-bind:class="{ working: (product.status != 'ready' && product.status != 'selecting') }"
                                v-bind:disabled="!(variantAvailable(product.selected_variant)) || (product.status != 'ready' && product.status != 'selecting')"
                                v-bind:alt="'Add ' + product.title + ' to Cart'"
                                v-on:click="addToCart(product)"
                                type="button">
                                    <span v-html="buttonLabel(product)"></span>
                            </button>
                        </div>
                    </div>
</div>
                </div>

                <div class="rebuy-modal-actions" v-if="showContinueButton()">
                    <button
                        class="rebuy-button decline"
                        v-on:click="hide()"
                        type="button">
                            <span v-html="continueLabel()"></span>
                    </button>
                </div>

                <div class="powered-by-rebuy">
                    <a v-bind:href="'https://rebuyengine.com/?shop=' + config.shop.myshopify_domain" target="_blank" rel="noopener">
                        Powered by Rebuy
                    </a>
                </div>

            </div>

        </div>
    </div>
</script>
{% endraw %}

<!-- PDP Carousel Template -->
{% raw %}
<script id="rebuy-widget-201563" type="text/template">
        <div class="rebuy-widget"
        v-cloak
        v-on:click="stopPropagation($event)"
        v-bind:id="'rebuy-widget-' + id"
        v-bind:class="['widget-type-' + config.type.replace('_','-'), 'widget-display-' + config.display_type, products.length > 0 ? 'is-visible' : 'is-hidden', 'widget-layout-' + currentLayout()]">

        <div
            class="rebuy-widget-container"
            v-cloak
            v-bind:class="['widget-display-' + config.display_type, visible ? 'is-visible' : 'is-hidden' ]"
            v-on:click.self="hide()">

            <div class="rebuy-widget-content">
                <div class="rebuy-modal-close" v-on:click="hide()">
                    <i class="fas fa-times"></i>
                </div>

                <div class="rebuy-timer" v-if="hasTimer()">
                    <h5 class="rebuy-timer-title" v-if="config.language.timer_title">
                        <span v-html="config.language.timer_title"></span> <span class="rebuy-timer-minutes" v-html="config.timer.duration_minutes"></span>:<span class="rebuy-timer-seconds" v-html="config.timer.duration_seconds"></span>
                    </h5>
                </div>

                <h4 class="super-title" v-if="config.language.super_title != ''" v-html="config.language.super_title"></h4>

                <h3 class="primary-title" v-if="config.language.title != ''" v-html="config.language.title"></h3>

                <div class="description" v-if="config.language.description != ''" v-html="config.language.description"></div>

                <div class="rebuy-product-grid" v-bind:class="layoutClasses">

                    <div class="rebuy-product-block" v-for="(product, product_index) in products" v-bind:class="[product.handle, 'product-id-' + product.id, cartHasProduct(product) ? 'cart-has-item' : '', productTagClasses(product)]">
<div class="rebuy-product-block-holder">
<div class="rebuy-product-media">
    <a class="rebuy-product-image" 
       v-bind:href="learnMoreURL(product)" 
       v-bind:style="imageStyles" 
       v-on:click="learnMore(product);" 
       v-bind:class="[hasLearnMore() ? 'clickable' : '']">
       
        <!-- Primary image -->
        <img class="rebuy-product-img" 
             v-if="product?.images[0]?.src" 
             v-bind:src="itemImage(product, product.selected_variant, '500x500')" 
             v-bind:alt="'View ' + product.title">
             
        <!-- Secondary image (hover image), only rendered if it exists -->
        <img class="rebuy-product-hover-img" 
             v-if="product?.images[1]?.src" 
             v-bind:src="product.images[1].src.replace(/(\.jpg)/, '_500x500$1')" 
             v-bind:alt="'View ' + product.title">
    </a>
</div>

                        <div class="rebuy-product-info">
                            <a class="rebuy-product-title" v-bind:href="learnMoreURL(product)" v-on:click="learnMore(product);" v-html="product.title" v-bind:class="[hasLearnMore() ? 'clickable' : '']" v-bind:alt="'View ' + product.title"></a>
                            <div class="rebuy-variant-title" v-if="showVariantTitle(product)" v-html="product.selected_variant.title"></div>
                            <div class="rebuy-product-review" v-if="hasProductReviews(product)">
                                <span class="rebuy-star-rating">
                                    <span class="rebuy-star-rating-background"></span>
                                    <span class="rebuy-star-rating-foreground" v-bind:style="{ width: productReviewRatingPercentage(product) }"></span>
                                </span>
                                <span class="rebuy-review-count" v-html="productReviewCount(product)"></span>
                            </div>

                            <div class="rebuy-product-price">
                                <div v-if="variantOnSale(product, product.selected_variant)">
                                    <span class="rebuy-money sale" v-html="formatMoney(variantPrice(product, product.selected_variant)).replaceAll('.00', ' ') + Shopify.currency.active"></span>
                                    <span class="rebuy-money compare-at" v-html="formatMoney(variantCompareAtPrice(product, product.selected_variant)).replaceAll('.00', ' ') + Shopify.currency.active"></span>
                                </div>
                                <div v-if="!(variantOnSale(product, product.selected_variant))">
                                    <span class="rebuy-money" v-html="formatMoney(variantPrice(product, product.selected_variant)).replaceAll('.00', ' ') + Shopify.currency.active"></span>
                                </div>
                            </div>
                            <div class="rebuy-product-description" v-if="showProductDescription(product)" v-html="text(product.body_html)"></div>
                        </div>
                        <div class="rebuy-product-options" v-if="showVariantSelect(product)">
                            <select
                                :id="id + '-' + 'select' + '-' + product_index"
                                :class="{ hide : settings.view_options.variant_selector == 'buttons' }"
                                class="rebuy-select"
                                v-model="product.selected_variant_id"
                                v-on:change="selectVariant(product)">
                                <option v-for="variant in product.variants" v-bind:value="variant.id">{{ variant.title }}</option>
                            </select>


                            <div v-if="displaySizeSwatches(option)" v-for="option in product.options" class="mt-10 rebuy-size-swatches">
                                <div v-for="(value, value_index) in option.values" class="rebuy-size-swatch">
                                    <input
                                        :name="id + '-size-' + product_index"
                                        :id="id + '-size-' + product_index + '-' + value"
                                        :checked="value_index == 0"
                                        :value="value"
                                        type="radio"
                                        class="rebuy-size-input hide"
                                        v-on:change="selectVariantBySize(product, value, product_index)">
                                    <label
                                        :for="id + '-size-' + product_index + '-' + value"
                                        class="rebuy-size-label">{{ value }}</label>
                                </div>
                            </div>
                        </div>
                        <div class="rebuy-product-actions">

                            <div class="subscription-checkbox" v-if="showSubscriptionOptions(product)">
                                <label class="rebuy-checkbox-label">
                                    <input
                                        class="checkbox-input rebuy-checkbox"
                                        v-model="product.subscription"
                                        v-on:change="toggleSubscription(product)"
                                        type="checkbox" />
                                    <span class="checkbox-label" v-html="upgradeToSubscriptionLabel(product)"></span>
                                </label>
                            </div>

                            <div class="subscription-frequency" v-if="showSubscriptionFrequency(product)">
                                <select
                                    class="rebuy-select"
                                    v-model="product.subscription_frequency"
                                    v-on:change="updateSubscriptionFrequency(product)">
                                    <option v-for="frequency in product.subscription_frequencies" v-bind:value="frequency">{{ frequencyLabel(frequency, product.subscription_interval) }}</option>
                                </select>
                            </div>

                            <div class="product-quantity" v-if="hasQuantityInputEnabled()">
                                <div class="rebuy-select-wrapper">
                                    <label class="rebuy-label">Quantity</label>
                                    <select
                                        class="rebuy-select"
                                        v-model="product.quantity">
                                        <option v-for="n in maxQuantityInputValue()" v-bind:value="n">{{ n }}</option>
                                    </select>
                                </div>
                            </div>

                            <button
                                class="rebuy-button"
                                v-bind:class="{ working: (product.status != 'ready' && product.status != 'selecting') }"
                                v-bind:disabled="!(variantAvailable(product.selected_variant)) || (product.status != 'ready' && product.status != 'selecting')"
                                v-bind:alt="'Add ' + product.title + ' to Cart'"
                                v-on:click="addToCart(product)"
                                type="button">
                                    <span v-html="buttonLabel(product)"></span>
                            </button>
                        </div>
                    </div>
</div>
                </div>

                <div class="rebuy-modal-actions" v-if="showContinueButton()">
                    <button
                        class="rebuy-button decline"
                        v-on:click="hide()"
                        type="button">
                            <span v-html="continueLabel()"></span>
                    </button>
                </div>

                <div class="powered-by-rebuy">
                    <a v-bind:href="'https://rebuyengine.com/?shop=' + config.shop.myshopify_domain" target="_blank" rel="noopener">
                        Powered by Rebuy
                    </a>
                </div>

            </div>

        </div>
    </div>
</script>
{% endraw %}


<!-- Complete The Look Template -->
{% raw %}
<script id="rebuy-widget-201560" type="text/template">
    <div class="rebuy-widget"
         v-cloak
         v-on:click="stopPropagation($event)"
         v-bind:id="'rebuy-widget-' + id"
         v-bind:class="['widget-type-' + config.type.replace('_','-'), 'widget-display-' + config.display_type, products.length > 0 ? 'is-visible' : 'is-hidden', 'widget-layout-' + currentLayout()]">

        <div class="rebuy-widget-container"
             v-cloak
             v-bind:class="['widget-display-' + config.display_type, visible ? 'is-visible' : 'is-hidden' ]"
             v-on:click.self="hide()">

            <div class="rebuy-widget-content">
                <div class="rebuy-modal-close" v-on:click="hide()">
                    <i class="fas fa-times"></i>
                </div>

                <div class="rebuy-timer" v-if="hasTimer()">
                    <h5 class="rebuy-timer-title" v-if="config.language.timer_title">
                        <span v-html="config.language.timer_title"></span> 
                        <span class="rebuy-timer-minutes" v-html="config.timer.duration_minutes"></span>:<span class="rebuy-timer-seconds" v-html="config.timer.duration_seconds"></span>
                    </h5>
                </div>

                <h4 class="super-title" v-if="config.language.super_title != ''" v-html="config.language.super_title"></h4>

                <h3 class="primary-title" v-if="config.language.title != ''" v-html="config.language.title"></h3>

                <div class="description" v-if="config.language.description != ''" v-html="config.language.description"></div>

                <div class="rebuy-product-grid" v-bind:class="layoutClasses">
                    <div class="rebuy-product-block" v-for="(product, product_index) in products" v-bind:class="[product.handle, 'product-id-' + product.id, cartHasProduct(product) ? 'cart-has-item' : '', productTagClasses(product)]">
                    <!-- Product Block holder start -->
                    <div class="rebuy-product-block-holder">

                        <div class="rebuy-product-media">
                            <a class="rebuy-product-image" 
                               v-bind:href="learnMoreURL(product)" 
                               v-bind:style="imageStyles" 
                               v-on:click="learnMore(product);" 
                               v-bind:class="[hasLearnMore() ? 'clickable' : '']">
                                <!-- Primary image -->
                                <img class="rebuy-product-img" 
                                     v-if="product?.images[0]?.src" 
                                     v-bind:src="itemImage(product, product.selected_variant, '500x500')" 
                                     v-bind:alt="'View ' + product.title">
                                <!-- Secondary image (hover image), only rendered if it exists -->
                                <img class="rebuy-product-hover-img" 
                                     v-if="product?.images[1]?.src" 
                                     v-bind:src="product.images[1].src.replace(/(\.jpg)/, '_500x500$1')" 
                                     v-bind:alt="'View ' + product.title">
                            </a>
                        </div>

                        <div class="rebuy-product-info">
                            <a class="rebuy-product-title" 
                               v-bind:href="learnMoreURL(product)" 
                               v-on:click="learnMore(product);" 
                               v-html="product.title" 
                               v-bind:class="[hasLearnMore() ? 'clickable' : '']" 
                               v-bind:alt="'View ' + product.title"></a>
                            <div class="rebuy-variant-title" v-if="showVariantTitle(product)" v-html="product.selected_variant.title"></div>
                            <div class="rebuy-product-review" v-if="hasProductReviews(product)">
                                <span class="rebuy-star-rating">
                                    <span class="rebuy-star-rating-background"></span>
                                    <span class="rebuy-star-rating-foreground" v-bind:style="{ width: productReviewRatingPercentage(product) }"></span>
                                </span>
                                <span class="rebuy-review-count" v-html="productReviewCount(product)"></span>
                            </div>

                            <div class="rebuy-product-price">
                                <div v-if="variantOnSale(product, product.selected_variant)">
                                    <span class="rebuy-money sale" v-html="formatMoney(variantPrice(product, product.selected_variant)).replaceAll('.00', ' ') + Shopify.currency.active"></span>
                                    <span class="rebuy-money compare-at" v-html="formatMoney(variantCompareAtPrice(product, product.selected_variant)).replaceAll('.00', ' ') + Shopify.currency.active"></span>
                                </div>
                                <div v-if="!(variantOnSale(product, product.selected_variant))">
                                    <span class="rebuy-money" v-html="formatMoney(variantPrice(product, product.selected_variant)).replaceAll('.00', ' ') + Shopify.currency.active"></span>
                                </div>
                            </div>
                            <div class="rebuy-product-description" v-if="showProductDescription(product)" v-html="text(product.body_html)"></div>
                        </div>

<!-- Accordion with rebuy-product-interactive -->
<div class="rebuy-accordion" v-bind:id="'accordion-' + product_index">
<div class="rebuy-accordion-header" 
     v-on:click="product.variants.length > 1 || product.options.length > 1 ? toggleProductOptions(product.id, id) : addToCart(product.selected_variant || product.variants[0])">


      <span class="rebuy-accordion-text">
            {{ product.variants.length > 1 || product.options.length > 1 ? 'Select a ' + (product.options.find(opt => opt.name === 'Size')?.name || product.options[0]?.name || 'Variant') : 'Add to Cart' }}
        </span>
        <span class="rebuy-accordion-icon" 
              v-if="product.variants.length > 1 || product.options.length > 1">+</span>
    </div>
    <div class="rebuy-accordion-content" 
         v-if="product.variants.length >= 1 || product.options.length >= 1">
        <div class="rebuy-product-options" v-if="product.variants.length >= 1">
            <select title="Select product variant" 
                    :id="id + '-' + 'select' + '-' + product_index"
                    :class="{ hide : settings.view_options.variant_selector == 'buttons' }" 
                    class="rebuy-select"
                    v-bind:aria-label="'variant of ' + product.title" 
                    v-model="product.selected_variant_id"
                    v-on:change="product.selected_variant = product.variants.find(v => v.id == product.selected_variant_id) || product.variants[0]">
                <option v-for="variant in product.variants" v-bind:value="variant.id">
                    {{ variant.title.split('/').shift().trim() }}
                </option>
            </select>

<div v-if="displaySizeSwatches(option)" v-for="option in product.options" class="rebuy-size-swatches">
                              <div v-for="(value, value_index) in filterOOSOptions(option, product)" class="rebuy-size-swatch">

                                 <input
    :name="id + '-size-' + product_index"
    :id="id + '-size-' + product_index + '-' + value"
    :checked="value_index == 0"
    :value="value"
    type="radio"
    class="rebuy-size-input hide"
    v-on:change="handleSizeChange(product, value, product_index)">
                                  <label
                                      :for="id + '-size-' + product_index + '-' + value"
                                      class="rebuy-size-label">{{ value }}</label>
                              </div>
                          </div>
</div>
    </div>
</div>



                <div class="rebuy-modal-actions" v-if="showContinueButton()">
                    <button class="rebuy-button decline"
                            v-on:click="hide()"
                            type="button">
                        <span v-html="continueLabel()"></span>
                    </button>
                </div>

                <div class="powered-by-rebuy">
                    <a v-bind:href="'https://rebuyengine.com/?shop=' + config.shop.myshopify_domain" 
                       target="_blank" 
                       rel="noopener">
                        Powered by Rebuy
                    </a>
                </div>
            </div>
        </div>
    </div>
</script>

{% endraw %}


<!-- Secondary Image on Hover for Recommended Widgets -->
{% raw %}
<script id="rebuy-recommended-template" type="text/template">
    <div class="rebuy-widget"
         v-cloak
         v-on:click="stopPropagation($event)"
         v-bind:id="'rebuy-widget-' + id"
         v-bind:class="['widget-type-' + config.type.replace('_','-'), 'widget-display-' + config.display_type, products.length > 0 ? 'is-visible' : 'is-hidden', 'widget-layout-' + currentLayout()]">

        <div class="rebuy-widget-container"
             v-cloak
             v-bind:class="['widget-display-' + config.display_type, visible ? 'is-visible' : 'is-hidden' ]"
             v-on:click.self="hide()">

            <div class="rebuy-widget-content">
                <div class="rebuy-modal-close" v-on:click="hide()">
                    <i class="fas fa-times"></i>
                </div>

                <div class="rebuy-timer" v-if="hasTimer()">
                    <h5 class="rebuy-timer-title" v-if="config.language.timer_title">
                        <span v-html="config.language.timer_title"></span> 
                        <span class="rebuy-timer-minutes" v-html="config.timer.duration_minutes"></span>:<span class="rebuy-timer-seconds" v-html="config.timer.duration_seconds"></span>
                    </h5>
                </div>

                <h4 class="super-title" v-if="config.language.super_title != ''" v-html="config.language.super_title"></h4>

                <h3 class="primary-title" v-if="config.language.title != ''" v-html="config.language.title"></h3>

                <div class="description" v-if="config.language.description != ''" v-html="config.language.description"></div>

                <div class="rebuy-product-grid" v-bind:class="layoutClasses">
                    <div class="rebuy-product-block" v-for="(product, product_index) in products" v-bind:class="[product.handle, 'product-id-' + product.id, cartHasProduct(product) ? 'cart-has-item' : '', productTagClasses(product)]">

                        <div class="rebuy-product-media">
                            <a class="rebuy-product-image" 
                               v-bind:href="learnMoreURL(product)" 
                               v-bind:style="imageStyles" 
                               v-on:click="learnMore(product);" 
                               v-bind:class="[hasLearnMore() ? 'clickable' : '']">
                                <!-- Primary image -->
                                <img class="rebuy-product-img" 
                                     v-if="product?.images[0]?.src" 
                                     v-bind:src="itemImage(product, product.selected_variant, '500x500')" 
                                     v-bind:alt="'View ' + product.title">
                                <!-- Secondary image (hover image), only rendered if it exists -->
                                <img class="rebuy-product-hover-img" 
                                     v-if="product?.images[1]?.src" 
                                     v-bind:src="product.images[1].src.replace(/(\.jpg)/, '_500x500$1')" 
                                     v-bind:alt="'View ' + product.title">
                            </a>
                        </div>

                        <div class="rebuy-product-info">
                            <a class="rebuy-product-title" 
                               v-bind:href="learnMoreURL(product)" 
                               v-on:click="learnMore(product);" 
                               v-html="product.title" 
                               v-bind:class="[hasLearnMore() ? 'clickable' : '']" 
                               v-bind:alt="'View ' + product.title"></a>
                            <div class="rebuy-variant-title" v-if="showVariantTitle(product)" v-html="product.selected_variant.title"></div>
                            <div class="rebuy-product-review" v-if="hasProductReviews(product)">
                                <span class="rebuy-star-rating">
                                    <span class="rebuy-star-rating-background"></span>
                                    <span class="rebuy-star-rating-foreground" v-bind:style="{ width: productReviewRatingPercentage(product) }"></span>
                                </span>
                                <span class="rebuy-review-count" v-html="productReviewCount(product)"></span>
                            </div>

                            <div class="rebuy-product-price">
                                <div v-if="variantOnSale(product, product.selected_variant)">
                                    <span class="rebuy-money sale" v-html="formatMoney(variantPrice(product, product.selected_variant)).replaceAll('.00', ' ') + Shopify.currency.active"></span>
                                    <span class="rebuy-money compare-at" v-html="formatMoney(variantCompareAtPrice(product, product.selected_variant)).replaceAll('.00', ' ') + Shopify.currency.active"></span>
                                </div>
                                <div v-if="!(variantOnSale(product, product.selected_variant))">
                                    <span class="rebuy-money" v-html="formatMoney(variantPrice(product, product.selected_variant)).replaceAll('.00', ' ') + Shopify.currency.active"></span>
                                </div>
                            </div>
                            <div class="rebuy-product-description" v-if="showProductDescription(product)" v-html="text(product.body_html)"></div>
                        </div>

<!-- Accordion with rebuy-product-interactive -->
<div class="rebuy-accordion" v-bind:id="'accordion-' + product_index">
<div class="rebuy-accordion-header" 
     v-on:click="product.variants.length > 1 || product.options.length > 1 ? toggleProductOptions(product.id, id) : addToCart(product.selected_variant || product.variants[0])">


        <span class="rebuy-accordion-text">
            {{ product && product.options && product.options.some(opt => opt.name === 'Size') ? 'Select a Size' : 'Add to Cart' }}
        </span>
        <span class="rebuy-accordion-icon" 
              v-if="product && product.options && product.options.some(opt => opt.name === 'Size')">+</span>
    </div>
    <div class="rebuy-accordion-content" 
         v-if="product && (product.variants.length >= 1 || product.options.length >= 1)">
        <div class="rebuy-product-options" v-if="product && product.variants.length >= 1">
            <select title="Select product variant" 
                    :id="id + '-' + 'select' + '-' + product_index"
                    :class="{ hide : settings.view_options.variant_selector == 'buttons' }" 
                    class="rebuy-select"
                    v-bind:aria-label="'variant of ' + product.title" 
                    v-model="product.selected_variant_id"
                    v-on:change="product.selected_variant = product.variants.find(v => v.id == product.selected_variant_id) || product.variants[0]">
                <option v-for="variant in product.variants" v-bind:value="variant.id">
                    {{ variant.title.split('/').shift().trim() }}
                </option>
            </select>

    <!-- Updated size swatch -->
<div v-if="displaySizeSwatches(option)" v-for="option in product.options" class="rebuy-size-swatches">
    <div v-for="(value, value_index) in filterOOSOptions(option, product)" class="rebuy-size-swatch">
        <input
            :name="id + '-size-' + product_index"
            :id="id + '-size-' + product_index + '-' + value"
            :checked="value_index === 0"
            :value="value"
            type="radio"
            class="rebuy-size-input hide"
            v-on:click="handleSizeChange(product, value, product_index)">
        <label
            :for="id + '-size-' + product_index + '-' + value"
            class="rebuy-size-label">{{ value }}</label>
    </div>
</div>
        </div>
    </div>
</div>



                <div class="rebuy-modal-actions" v-if="showContinueButton()">
                    <button class="rebuy-button decline"
                            v-on:click="hide()"
                            type="button">
                        <span v-html="continueLabel()"></span>
                    </button>
                </div>

                <div class="powered-by-rebuy">
                    <a v-bind:href="'https://rebuyengine.com/?shop=' + config.shop.myshopify_domain" 
                       target="_blank" 
                       rel="noopener">
                        Powered by Rebuy
                    </a>
                </div>
            </div>
        </div>
    </div>
</script>

{% endraw %}

<style>
.rebuy-widget .rebuy-product-image {
  position: relative;
  overflow: hidden;
}
.rebuy-widget .rebuy-product-hover-img {
  position: absolute;
  top: 0;
  left: 0;

}
.rebuy-widget .rebuy-product-hover-img,
.rebuy-widget .rebuy-product-media:hover .rebuy-product-image .rebuy-product-img {
  pointer-events: none;
  opacity: 0;
transition: opacity .25s ease-in-out;
}
.rebuy-widget .rebuy-product-media:hover .rebuy-product-image .rebuy-product-hover-img {
  pointer-events: all;
  opacity: 1;
transition: opacity .25s ease-in-out;
  z-index: 10;
}
.rebuy-widget .rebuy-product-media:not(:has(.rebuy-product-hover-img)) .rebuy-product-image:hover .rebuy-product-img {
  opacity: 1 !important; /* Ensure the primary image remains visible */
  pointer-events: all !important;
}
</style>
