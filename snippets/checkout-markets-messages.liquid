{% comment %} Content {% endcomment %}

{%- assign market = market.metafields -%}

{%- capture message_heading -%}
  {%- if market.message_heading -%}
    {{ market.checkout.markets_message_heading }}
  {%- else -%}
    {{ settings.checkout_markets_message_heading }}
  {%- endif -%}
{%- endcapture -%}

{%- capture message_text -%}
  {%- if market.message_text -%}
    {{ market.checkout.markets_message_text }}
  {%- else -%}
    {{ settings.checkout_markets_message_text }}
  {%- endif -%}
{%- endcapture -%}


{% comment %} Construct Message {% endcomment %}

{%- capture header_content -%}
  {%- if settings.checkout_markets_header_text -%}
    <div class="checkout-markets-message__header">
      {{ settings.checkout_markets_header_text }}
    </div>
  {%- endif -%}
{%- endcapture -%}

{%- capture body_content -%}
  {%- if message_heading -%}
    <div class="checkout-markets-message__message-heading section__header">
      <h2 class="section__title">{{ message_heading }}</h2>
    </div>
  {%- endif -%}
  {%- if message_text -%}
    <div class="checkout-markets-message__message-text section__body">
      {{ message_text }}
    </div>
  {%- endif -%}
{%- endcapture -%}

{%- capture markets_message -%}
  {% comment %}
  {%- if header_content != blank -%}
    <div class="checkout-markets-message__header">
      {{ header_content }}
    </div>
  {%- endif -%}
{% endcomment %}
  {%- if body_content != blank -%}
    <div class="checkout-markets-message__body section">
      {{ body_content }}
    </div>
  {%- endif -%}
{%- endcapture -%}


{% comment %} Display Message {% endcomment %}

{%- if markets_message and message_text != blank -%}
  <div class="checkout-markets-message section">
    {{ markets_message }}
  </div>
{%- endif -%}