<script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>

<style>
  *,::before,::after {
    box-sizing: border-box;
    margin: 0px;
    padding: 0px;
  }

  @font-face {
    font-family: 'Montserrat';
    src: url('https://cdn.shopify.com/s/files/1/0019/7106/0847/files/Montserrat-Bold.woff2?v=1650696351') format('woff2'),
      url('https://cdn.shopify.com/s/files/1/0019/7106/0847/files/Montserrat-Bold.woff?v=1650696351') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Montserrat';
    src: url('https://cdn.shopify.com/s/files/1/0019/7106/0847/files/Montserrat-Regular.woff2?v=1650696351') format('woff2'),
      url('https://cdn.shopify.com/s/files/1/0019/7106/0847/files/Montserrat-Regular.woff?v=1650696351') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Montserrat';
    src: url('https://cdn.shopify.com/s/files/1/0019/7106/0847/files/Montserrat-Medium.woff2?v=1650696351') format('woff2'),
      url('https://cdn.shopify.com/s/files/1/0019/7106/0847/files/Montserrat-Medium.woff?v=1650696351') format('woff');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
  }

  .container-fluid {
    max-width: 100%;
    margin: 0px auto;
  }

  .container {
    max-width: 1250px;
    margin: 0px auto;
    padding: 0px 30px;
  }

  .row {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    box-sizing: border-box;
  }

  .col-12 {
    flex: 0 0 100%;
    -webkit-box-flex: 0 0 100%;
    -moz-box-flex: 0 0 100%;
    -webkit-flex: 0 0 100%;
    -ms-flex: 0 0 100%;
    max-width: 100%;
  }

  .col-6 {
    flex: 0 0 50%;
    -webkit-box-flex: 0 0 50%;
    -moz-box-flex: 0 0 50%;
    -webkit-flex: 0 0 50%;
    -ms-flex: 0 0 50%;
    max-width: 50%;
  }

  .col-4 {
    flex: 0 0 33.33%;
    -webkit-box-flex: 0 0 33.33%;
    -moz-box-flex: 0 0 33.33%;
    -webkit-flex: 0 0 33.33%;
    -ms-flex: 0 0 33.33%;
    max-width: 33.33%;
  }

  .col-3 {
    flex: 0 0 25%;
    -webkit-box-flex: 0 0 25%;
    -moz-box-flex: 0 0 25%;
    -webkit-flex: 0 0 25%;
    -ms-flex: 0 0 25%;
    max-width: 25%;
  }

  /******* Media Query Start *******/
  @media only screen and (max-width: 768px) {
    .container {
      padding: 0px 25px;
    }

    .col-6 {
      flex: 0 0 100%;
      -webkit-box-flex: 0 0 100%;
      -moz-box-flex: 0 0 100%;
      -webkit-flex: 0 0 100%;
      -ms-flex: 0 0 100%;
      max-width: 100%;
    }

    .col-4 {
      flex: 0 0 50%;
      -webkit-box-flex: 0 0 50%;
      -moz-box-flex: 0 0 50%;
      -webkit-flex: 0 0 50%;
      -ms-flex: 0 0 50%;
      max-width: 50%;
    }

    .col-3 {
      flex: 0 0 50%;
      -webkit-box-flex: 0 0 50%;
      -moz-box-flex: 0 0 50%;
      -webkit-flex: 0 0 50%;
      -ms-flex: 0 0 50%;
      max-width: 50%;
    }
  }

  @media only screen and (max-width: 480px) { 
    .col-4, 
    .col-3 {
      flex: 0 0 100%;
      -webkit-box-flex: 0 0 100%;
      -moz-box-flex: 0 0 100%;
      -webkit-flex: 0 0 100%;
      -ms-flex: 0 0 100%;
      max-width: 100%;
    }
  }
  /******* Base Media Query End *******/


  /*  Main Section CSS Start  */
  .main-section {
    margin-bottom: 65px;
  }

  .main-section .container-fluid:first-child {
    background: url('https://cdn.shopify.com/s/files/1/0019/7106/0847/files/main-background.png?v=1650695679');
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .main-section .col-6:first-child {
    flex: 0 0 55.25%;
    max-width: 55.25%;
    align-self: center;
  }

  .main-section .col-6:first-child .col-wrapper {
    padding: 50px 0px;
  }

  .main-section .col-6:first-child .image-wrapper {
    position: relative;
    padding-bottom: 0%;
    max-width: 260px;
    margin: 0px auto 8px;
  }

  .main-section .col-6:first-child .image-wrapper .image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    max-width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .main-section .descriptions {
    font-family: 'Montserrat';
    font-size: 20px;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: 0.1em;
    text-align: center;
    text-transform: uppercase;
    color: #696766;
  }

  .main-section .col-6:last-child {
    flex: 0 0 43.75%;
    max-width: 43.75%;
    margin-left: auto;
  }

  .main-section .col-6:last-child .image-wrapper {
    position: relative;
    padding-bottom: 79.367%;
    height: 100%;
  }

  .main-section .col-6:last-child .image-wrapper .image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    max-width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .main-section .container-fluid:last-child p {
    font-family: 'Montserrat';
    font-size: 16px;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: 0.1em;
    text-align: center;
    margin: 6px 0px;
    padding: 0px 18px;
    text-transform: uppercase;
  }

  @media only screen and (max-width: 1024px) {
    .main-section .col-6:first-child,
    .main-section .col-6:last-child {
      flex: 0 0 50%;
      max-width: 50%;
    }
  }

  @media only screen and (max-width: 768px) {
    .main-section {
      margin-bottom: 40px;
    }

    .main-section .container-fluid:first-child {
      background: url('https://cdn.shopify.com/s/files/1/0019/7106/0847/files/main-background.png?v=1650695679');
      background-position: center;
      background-repeat: no-repeat;
      background-size: cover;
    }

    .main-section .col-6:first-child,
    .main-section .col-6:last-child {
      flex: 0 0 100%;
      max-width: 100%;
    }

    .main-section .col-6:first-child .col-wrapper {
      padding: 65px 0px 45px;
    }

    .main-section .col-6:first-child .image-wrapper {
      max-width: 200px;
    }

    .main-section .descriptions {
      font-size: 16px;
      line-height: 22px;
      margin: 0;
    }
  }
  /*  Main Section CSS End  */

  /*  Reward Section CSS Start  */
  .reward-section {
    padding: 50px 0px 60px;
    background-color: #fff;
  }

  .reward-section .container {
    max-width: 1256px;
    padding: 0px 32px;
  }

  .reward-section .row {
    margin: 0px -16px;
    display: flex;
    justify-content: center;
    
  }

  .reward-section .description {
    font-family: 'Montserrat';
    font-size: 16px;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: 0.02em;
    text-align: center;
    margin-bottom: 6px;
  }

  .reward-section .heading {
    font-family: 'Montserrat';
    font-size: 20px;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: 0.1em;
    text-align: center;
    margin-bottom: 62px;
  }

  .reward-section .col-4 {
    margin: 0 37.5px 62px 37.5px;
  }

  .reward-section .col-4 .col-wrapper {
    margin: 0px 16px;
    background-color: rgb(251 246 243 / 1);
    border-radius: 10px;
    padding: 34px 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .reward-section .post-tier {
    font-family: 'Montserrat';
    font-size: 1.5rem;
    font-weight: 600;
    text-align: center;
  }

  .reward-section .post-title {
    font-family: 'Montserrat';
    font-size: 20px;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: 0.1em;
    text-align: center;
    margin-bottom: 18px;
  }

  .reward-section .post-text {
    font-family: 'Montserrat';
    font-size: 16px;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: 0.02em;
    text-align: center;
    margin-bottom: 18px;
  }

  .reward-section .image-wrapper {
    position: relative;
    padding-bottom: 200px;
    width: 200px;
    margin: 0px auto;
  }

  .reward-section .image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    max-width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 20px;
  }

  .signup-button_lcButtonContainer__1Ws4J {
    padding: 0px;
    margin-top: 40px;
  }

  .reward-section .signup-button_lcButtonContainer__1Ws4J {
    margin-top: 0px;
  }

  .signup-button_lcButton__3PS4y {
    width: auto;
    height: auto;
    border-radius: 0px;
    padding: 14px 22px;
  }

  .signup-button_lcButtonText__1Qqcl {
    margin: 0px;
    font-family: 'Montserrat';
    font-size: 16px;
    font-weight: 700;
    line-height: 20px;
    letter-spacing: 0.1em;
    text-align: center;
  }

  @media only screen and (max-width: 1024px) {
    .reward-section .col-4 .col-wrapper {
      padding: 25px 20px;
    }

    .reward-section .image-wrapper {
      padding-bottom: 100%;
      width: 100%;
    }
  }

  @media only screen and (max-width: 768px) {
    .reward-section {
      padding: 40px 0px;
    }

    .reward-section .container {
      padding: 0px 20px;
    }

    .reward-section .row {
      margin: 0px;
    }

    .reward-section .col-4 {
      margin-bottom: 30px;
    }

    .reward-section .heading {
      margin-bottom: 40px;
    }

    .reward-section .col-4 .col-wrapper {
      max-width: 375px;
      margin: 0px 15px 20px;
    }

    .reward-section .post-text {
      min-height: 40px;
    }
  }

  @media only screen and (max-width: 480px) {
    .reward-section .container {
      padding: 0px 12px;
    }

    .reward-section .col-4 {
      flex: 0 0 80%;
      max-width: 80%;
      margin-bottom: 20px;
    }

    .reward-section .image {
      padding: 25px;
    }

    .reward-section .description {
      font-size: 16px;
      line-height: 22px;
    }

    .reward-section .heading {
      font-size: 20px;
      line-height: 30px;
    }

    .reward-section .col-4 .col-wrapper {
      margin: 0px 12px 20px;
      padding: 8px;
    }

    .reward-section .post-tier {
      font-size: 20px;
    }

    .reward-section .post-title {
      font-size: 20px;
      line-height: 26px;
      margin-bottom: 2px;
    }

    .reward-section .post-text {
      font-size: 15px;
      line-height: 20px;
      min-height: 40px;
      margin: 0;
    }
  }

  @media only screen and (max-width: 375px) { 
    .reward-section .col-4 .col-wrapper {
      margin-bottom: 20px;
    }
  }
  /*  Reward Section CSS End  */

  
  /*  How it Works Section CSS Start  */
  .how-works-section {
    padding: 35px 0px 130px;
  }

  .how-works-section .container {
    max-width: 1256px;
    padding: 0px 32px;
  }

  .how-works-section .row {
    margin: 0px -16px;
    display: flex;
    justify-content: space-around;
  }

  .how-works-section .heading {
    font-family: 'Montserrat';
    font-size: 24px;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: 0.1em;
    text-align: center;
    margin-bottom: 74px;
  }

  .how-works-section .col-4 .col-wrapper {
    margin: 0px -59px;
    background-color: rgb(251 246 243 / 1);
    border-radius: 10px;
    padding: 28px;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 350px;
  }

  .how-work-svg {
    width: 45px;
    height: 45px;
    display: flex;
    margin: 38px 0px 23px;
  }

  .how-works-section .post-title {
    font-family: 'Montserrat';
    font-size: 20px;
    font-weight: 500;
    line-height: 30px;
    letter-spacing: 0.1em;
    text-align: left;
    margin-bottom: 16px;
  }

  .how-works-section .post-text {
    font-family: 'Montserrat';
    font-size: 16px;
    font-weight: 400;
    line-height: 32px;
    letter-spacing: 0.02em;
    text-align: left;
  }

  @media only screen and (max-width: 1024px) {
    .how-works-section .col-4 .col-wrapper {
      padding: 20px;
    }

    .how-work-svg {
      margin: 17px 0px;
    }
  }

  @media only screen and (max-width: 768px) { 
    .how-works-section {
      padding: 50px 0px;
    }

    .how-works-section .container {
      padding: 0px 20px;
    }

    .how-works-section .row {
      margin: 0px;
    }

    .how-works-section .col-4 {
      flex: 0 0 100%;
      max-width: 100%;
    }

    .how-works-section .heading {
      margin-bottom: 40px;
    }

    .how-works-section .col-4 .col-wrapper {
      max-width: 375px;
      min-height: 250px;
      margin: 0px auto 30px;
      height: auto;
    }

    .how-works-section .col-4:last-child .col-wrapper {
      margin-bottom: 0px;
    }
  }

  @media only screen and (max-width: 480px) { 
    .how-works-section .post-title {
      font-size: 16px;
      line-height: 26px;
    }

    .how-works-section .post-text {
      font-size: 15px;
      line-height: 24px;
    }
  }
  /*  How it Works Section CSS End  */

  /*  Post Terms Section CSS Start  */
  .post-terms-section {
    padding: 82px 0px 102px;
    background-color: rgb(251 246 243 / 1);
    margin-bottom: 62px;
  }

  .post-terms-section .col-6:first-child {
    border-right: 2px solid #000;
  }

  .post-terms-section .col-wrapper {
    height: 100%;
    padding: 0px 60px;
  }

  .post-terms-section .heading {
    font-family: 'Montserrat';
    font-size: 20px;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: 0.1em;
    text-align: left;
    margin-bottom: 52px;
  }

  .post-terms-section ul {
    margin: 0px;
  }

  .post-terms-section .item {
    font-family: 'Montserrat';
    font-size: 16px;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: 0.02em;
    text-align: left;
    margin-bottom: 24px;
  }

  .post-terms-section ul .item:last-child {
    margin-bottom: 0px;
  }

  .post-terms-section .container-fluid {
    margin-top: 66px;
    padding: 0px 42px;
  }

  .post-terms-section .container-fluid .col-3 {
    padding-left: 7px;
    padding-right: 7px;
  }

  .post-terms-section .image-wrapper {
    position: relative;
    padding-bottom: 100%;
    width: 100%;
    height: 100%;
  }

  .post-terms-section .image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    max-width: 100%;
    height: 100%;
    object-fit: cover;
  }

  @media only screen and (max-width: 768px) {
    .post-terms-section {
      padding: 60px 0px;
    }

    .post-terms-section .col-6:first-child {
      border-right: none;
      border-bottom: 1px solid #000;
      margin-bottom: 60px;
    }

    .post-terms-section .col-6:first-child .col-wrapper {
      padding-bottom: 60px;
    }

    .post-terms-section .heading {
      margin-bottom: 40px;
    }

    .post-terms-section .item {
      margin-bottom: 18px;
    }

    .post-terms-section .container-fluid {
      margin-top: 60px;
      padding: 0px 30px;
    }

    .post-terms-section .container-fluid .col-3 {
      margin-bottom: 14px;
    }
  }

  @media only screen and (max-width: 480px) {
    .post-terms-section {
      margin-bottom: 40px;
      padding-bottom: 24px;
    }
    .post-terms-section .col-3 {
      flex: 0 0 50%;
      max-width: 50%;
    }

    .post-terms-section .col-wrapper {
      padding: 0px 20px;
    }

    .post-terms-section .item {
      font-size: 15px;
    }

    .post-terms-section .container-fluid {
      padding: 0px 12px;
    }

    .post-terms-section .container-fluid .col-3 {
      padding-left: 13px;
      padding-right: 13px;
      margin-bottom: 26px;
    }
  }
  /*  Post Terms Section CSS End  */

  /*  FAQs Section CSS Start  */
  .faq-section {
    padding: 62px 0px;
  }

  .faq-section .container {
    max-width: 1286px;
    padding: 0px 30px;
  }

  .faq-section .heading {
    font-family: 'Montserrat';
    font-size: 20px;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: 0.1em;
    text-align: center;
    text-transform: uppercase;
    margin-bottom: 70px;
    color: #000000;
  }

  .faq-section .col-12 {
    margin-bottom: 30px;
    background: #FFFFFF;
    box-shadow: 0px 6px 20px rgba(0, 0, 0, 0.08);
    border-radius: 10px;   
    user-select: none;
    -webkit-user-select: none;
    transition: all 0.4s ease;
    -webkit-tap-highlight-color: transparent;
  }

  .faq-section .col-12:last-child {
    margin-bottom: 0px;
  }

  .faq-section .question-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 38px 50px;
    cursor: pointer;
  }

  .faq-section .question {
    font-family: 'Montserrat';
    font-size: 16px;
    font-weight: 500;
    line-height: 26px;
    letter-spacing: 0.02em;
    text-align: left;
    text-transform: none;
    margin: 0px;
    color: #000000;
    width: 90%;
  }

  .detail-open-arrow {
    width: 25px;
    height: 25px;
    display: flex;
    transition: all 0.4s ease;
    margin-left: 10px;
  }

  .faq-section .col-wrapper .bottom-wrapper {
    transition: all 0.3s;
    overflow: hidden;
  }

  .faq-section .col-wrapper:not(.top-click) .bottom-wrapper {
    height: 0px !important;
  }

  .answer-wrapper {
    padding: 0px 50px 50px;    
  }

  .faq-section .answer-wrapper .answer {
    font-family: 'Montserrat';
    font-size: 16px;
    font-weight: 400;
    line-height: 26px;
    letter-spacing: 0.02em;
    text-align: left;
    padding: 9.5px 27px;
    border-left: 8px solid #EBD7D5;
    transform: translateX(-20px);
    transition: all 0.3s ease;
    opacity: 1;
    margin: 0px;
  }

  .faq-section .col-wrapper.top-click .answer-wrapper .answer {
    transform: translateX(0px);
    transition: all 0.4s ease-in-out;
    opacity: 1;
  }

  .faq-section .col-wrapper.top-click .detail-open-arrow {
    transform: rotate(90deg);
  }

  @media only screen and (max-width: 768px) {
    .faq-section {
      padding: 70px 0px;
    }

    .faq-section .heading {
      margin-bottom: 50px;
    }

    .faq-section .question-wrapper {
      padding: 24px;
    }

    .detail-open-arrow {
      width: 16px;
      height: 16px;
    }

    .answer-wrapper {
      padding: 0px 24px 24px;
    }

    .faq-section .answer {
      padding: 4px 20px;
      border-left: 4px solid #000;
    }
  }

  @media only screen and (max-width: 475px) {
    .faq-section {
      padding: 30px 0px;
    }

    .faq-section .container {
      padding: 0px 25px;
    }

    .faq-section .heading {
      font-size: 16px;
      margin-bottom: 36px;
    }

    .faq-section .col-12 {
      margin-bottom: 18px;
    }

    .faq-section .question-wrapper {
      padding: 20px 16px;
    }

    .answer-wrapper {
      padding: 0px 12px 20px;
    }

    .faq-section .answer-wrapper .answer {
      padding: 4px 24px 4px 12px;
      border-left: 4px solid #EBD7D5;
    }
  }
  /*  FAQs Section CSS End  */
  
  .signup-button_lcButton__3PS4y {
    display: block;
  }
  
  .reward-section .signup-button_lcButtonContainer__1Ws4J .signup-button_lcButton__3PS4y:nth-child(2) {
    display: flex;
  }
  
</style>


<section class="main-section">
  <div class="container-fluid">
    <div class="row">
      <div class="col-6">
        <div class="col-wrapper">
          <div class="image-wrapper">
            <img class="main-logo-image" src="https://cdn.shopify.com/s/files/1/0019/7106/0847/files/logo-black-primary.png?v=1662046585" alt="Kyte Baby Logo with a tilted kite"/>
          </div>
          <p class="descriptions"><b>Become a Kyte Baby Influencer</b></p>
          <p class="descriptions">Join our influencer program and earn free product and rewards by sharing Kyte Baby with your audience</p>
          <div class="apply-button-wrapper">
      <div 
        class="lc-signup-container" 
        data-lc-button-text="Apply Now" 
        data-lc-button-text-color="white" 
        data-lc-button-bg-color="black" 
        data-lc-description="If you are accepted to the program, you'll receive a Welcome Email in your inbox from us. Otherwise, we encourage you to join the Kyte Baby Ambassador program!">
        <script type="text/javascript" src="https://pub.loudcrowd.com/launch.js?hid=cHJvZ3JhbV9ob3N0OjE4Mg"></script>
      </div>
    </div>

    <div class="login-wrapper" style="margin-top: 15px;">
            <p class="descriptions">
              Already a member? <a href="https://creators.loudcrowd.com/s/kyte-baby/login?intent=view-dashboard" class="login-link" style="text-decoration: underline; color: black;">Login</a>
            </p>
    </div>
          
        </div>
      </div>
      <div class="col-6">
        <div class="image-wrapper">
          <img class="image" src="https://cdn.shopify.com/s/files/1/0019/7106/0847/files/main-right-image.png?v=1650697603" alt="Three infants laying down side by side in Kyte Baby sleepbags"/>
        </div>
      </div>
    </div>
  </div>
</section>

<section class="reward-section">
  <div class="container">
    <p class="description">Tag @kytebaby on Instagram and TikTok</p>
    <h2 class="heading">Earn Monthly Rewards</h2>
    <div class="row">
      <div class="col-4">
        <div class="col-wrapper">
          <h2 class="post-tier">Tier 1</h2>
          <h3 class="post-title">1 Post + 2 Stories</h3>
          <p class="post-text">Free exclusive product</p>
          <div class="image-wrapper">
            <img class="image" src="https://cdn.shopify.com/s/files/1/0019/7106/0847/files/Cloud_Swaddle_-_200x200_bf9d28ef-cf4a-4310-bfe8-9ced2773e9d2.jpg?v=1683640915" alt="A baby quietly sleeping, swaddled in a Kyte Baby Cloud Swaddle"/>
          </div>
        </div>
      </div>
      <div class="col-4">
        <div class="col-wrapper">
          <h2 class="post-tier">Tier 2</h2>
          <h3 class="post-title">2 Posts + 4 Stories</h3>
          <p class="post-text">Tier 1 + 10,000 rewards points<br>(points value = $50 in store credit)</p>
          <div class="image-wrapper">
            <img class="image" src="https://cdn.shopify.com/s/files/1/0019/7106/0847/files/Toddler_PJ_-_200x200_841dc89f-0a8b-4e5e-86ff-a8cc97dbe528.jpg?v=1683640916" alt="Two toddler aged girls holding hands, dressed in Kyte Baby's Emerald and Sage Long Sleeve Pajamas"/>
          </div>
        </div>
      </div>    
    </div>
  </div>
</section>

<section class="how-works-section">
  <div class="container">
    <h2 class="heading">How It Works</h2>
    <div class="row">
      <div class="col-4">
        <div class="col-wrapper">
          <div class="image-wrapper">
            <svg class="camera-svg how-work-svg" viewBox="0 0 48 42" fill="none">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M37.1553 7.8294C36.964 7.8294 36.789 7.71752 36.6233 7.37022C35.9607 5.9647 35.1487 4.24685 34.661 3.28653C33.5923 1.18408 31.7583 0.0163161 29.4787 0H18.5073C16.244 0.0163161 14.4077 1.18408 13.339 3.28886C12.8653 4.22121 12.0837 5.87613 11.4327 7.25834L11.295 7.54737C11.2133 7.71985 11.0407 7.8294 10.847 7.8294C5.23301 7.8294 0.666672 12.3909 0.666672 17.9967V31.8327C0.666672 37.4385 5.23301 42 10.847 42H12.5853C13.5513 42 14.3353 41.2168 14.3353 40.2518C14.3353 39.2869 13.5513 38.5037 12.5853 38.5037H10.847C7.16267 38.5037 4.16667 35.5108 4.16667 31.8327V17.9967C4.16667 14.3186 7.16267 11.3257 10.847 11.3257C12.3847 11.3257 13.8033 10.4283 14.459 9.04146L14.5967 8.7501C15.2337 7.39819 15.999 5.78056 16.461 4.86919C16.9393 3.92752 17.581 3.5033 18.5237 3.49631H29.4647C30.4213 3.5033 31.0607 3.92752 31.539 4.86686C32.015 5.80854 32.8107 7.48909 33.5433 9.04146C34.199 10.4283 35.6177 11.3257 37.1553 11.3257C40.8373 11.3257 43.8333 14.3186 43.8333 17.9967V31.8327C43.8333 35.5108 40.8373 38.5037 37.1553 38.5037H20.6867C19.7207 38.5037 18.9367 39.2869 18.9367 40.2518C18.9367 41.2168 19.7207 42 20.6867 42H37.1553C42.767 42 47.3333 37.4385 47.3333 31.8327V17.9967C47.3333 12.3909 42.767 7.8294 37.1553 7.8294ZM24.0019 33.2595C26.4542 33.2595 28.7642 32.2945 30.5072 30.5417C32.2409 28.7982 33.1882 26.5046 33.1672 24.0968C33.1695 21.654 32.2129 19.3488 30.4745 17.6123C28.7385 15.8781 26.4402 14.9248 24.0019 14.9248H23.9972C23.3252 14.9248 22.6532 14.9971 22.0022 15.1462C21.0595 15.3537 20.4645 16.2907 20.6769 17.2324C20.8869 18.1717 21.8202 18.7731 22.7652 18.554C23.1689 18.4654 23.5819 18.4211 23.9995 18.4211H24.0019C25.5069 18.4211 26.9255 19.0132 27.9989 20.0854C29.0769 21.1622 29.6695 22.5841 29.6672 24.1085C29.6789 25.5909 29.0955 26.9987 28.0245 28.0779C26.9442 29.1641 25.5139 29.7632 24.0019 29.7632H23.9972C22.4852 29.7608 21.0642 29.1688 19.9932 28.0942C18.9199 27.0197 18.3319 25.5956 18.3342 24.0805C18.3342 23.5001 18.4252 22.9244 18.6025 22.372C18.8942 21.4536 18.3855 20.47 17.4639 20.1763C16.5375 19.8872 15.5575 20.3907 15.2659 21.3114C14.9812 22.2088 14.8342 23.1365 14.8342 24.0782C14.8295 26.5209 15.7815 28.8261 17.5129 30.5626C19.2442 32.2991 21.5449 33.2571 23.9925 33.2595H24.0019ZM37.9536 18.0883C37.7902 18.2514 37.6036 18.3913 37.3936 18.4845C37.1836 18.5778 36.9502 18.6034 36.7169 18.6034C36.2736 18.6034 35.8302 18.4379 35.4802 18.0883C35.1536 17.762 34.9669 17.3191 34.9669 16.8552C34.9669 16.6431 35.0136 16.41 35.1069 16.2026C35.2002 15.9928 35.3402 15.783 35.4802 15.6199C35.6436 15.4777 35.8536 15.3378 36.0636 15.2446C36.4836 15.0814 36.9736 15.0814 37.3936 15.2446C37.6036 15.3378 37.8136 15.4777 37.9536 15.6199C38.1169 15.783 38.2569 15.9928 38.3502 16.2026C38.4436 16.41 38.4902 16.6431 38.4902 16.8552C38.4902 17.3191 38.3036 17.762 37.9536 18.0883Z" fill="#BFD5D2"/>
            </svg>
          </div>
          <h3 class="post-title">Post On Instagram and TikTok</h3>
          <p class="post-text">Take Photos and videos featuring Kyte BABY products and post on Instagram or Tiktok. Don’t forget to tag or mention us in everypost - <br><strong>@kytebaby</strong></p>
        </div>
      </div>
      <div class="col-4">
        <div class="col-wrapper">
          <div class="image-wrapper">
            <svg class="star-svg how-work-svg" viewBox="0 0 48 48" fill="none">
              <path d="M36.8284 47.1453C36.5414 47.1523 36.2568 47.08 36.0071 46.9353L24.0184 40.6213L13.4624 46.3053C11.7404 47.2386 9.58444 46.5993 8.65111 44.875C8.63944 44.854 8.62777 44.833 8.61611 44.8096C8.24977 44.0886 8.12144 43.2743 8.24744 42.4763L10.3008 30.357L1.71877 21.6676C0.316439 20.221 0.316439 17.9203 1.71877 16.4713C2.26477 15.8973 2.98811 15.5263 3.77211 15.419L15.5391 13.6316L20.7728 2.72796C21.5871 0.95463 23.6848 0.175296 25.4604 0.98963C26.2304 1.34196 26.8464 1.9603 27.1988 2.72796L32.4558 13.669L44.2624 15.4773C45.2168 15.6196 46.0731 16.147 46.6331 16.9333C47.6784 18.436 47.5198 20.466 46.2551 21.789L37.7128 30.3756L38.9028 36.9066C39.0684 37.8866 38.4221 38.8223 37.4444 39.0066C36.4831 39.1793 35.5638 38.54 35.3911 37.5763L34.1801 31.0453C33.9771 29.8483 34.3574 28.6256 35.2068 27.76L43.7701 19.1126L31.9634 17.281C30.7664 17.0873 29.7514 16.294 29.2731 15.181L24.0184 4.28663L18.6984 15.3536C18.2201 16.469 17.2051 17.26 16.0081 17.4536L4.24111 19.241L12.7414 27.8743C13.5884 28.726 13.9688 29.9323 13.7681 31.1153L11.7148 43.2346L22.2684 37.574C23.3138 36.986 24.5901 36.986 25.6354 37.574L37.6078 43.888C38.3381 44.287 38.7091 45.1293 38.5108 45.939C38.3288 46.7486 37.6148 47.325 36.7864 47.339L36.8284 47.1453Z" fill="#BFD5D2"/>
            </svg>
          </div>
          <h3 class="post-title">Get Rewarded</h3>
          <p class="post-text">At the end of every month, you’ll recieve an email with the highest reward tier you have achieved.</p>
        </div>
      </div>
    </div>
  </div>
</section>

<section class="post-terms-section">
  <div class="container">
    <div class="row">
      <div class="col-6">
        <div class="col-wrapper">
          <h2 class="heading">Post Rules</h2>
          <ul>
            <li class="item">You must tag and mention <strong>@kytebaby</strong> in your post. Tags must be visible and clickable.</li>
            <li class="item">This is an invite-only program based on follower size and quality of content - we expect you to maintain at least 10,000 followers to take part in this program.</li>
            <li class="item">Your posts must clearly show Kyte Baby items, with no other competing logos or brands featured.</li>
            <li class="item">No more than 3 other tags in the post (excluding people).</li>
          </ul>
        </div>
      </div>
      <div class="col-6">
        <div class="col-wrapper">
          <h2 class="heading">Terms & Conditions</h2>
          <ul>
            <li class="item">This rewards program may be subject to change at any time.</li>
            <li class="item">Posts must follow the post rules above in order to be eligible for rewards.</li>
            <li class="item">Kyte Baby may use rewards member photos for social media and marketing purposes.</li>
            <li class="item">Kyte Baby may update the rewards program at any time.</li>
            <li class="item">Only one feed and story post per day will count towards rewards.</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
  <div class="container-fluid">
    <div class="row">
      <div class="col-3">
        <div class="image-wrapper">
          <img class="image" src="https://cdn.shopify.com/s/files/1/0019/7106/0847/files/Womens-PJ_1000x1000_dc85e2d5-0883-4091-9577-af849f193769.jpg?v=1683810407" alt="A woman standing alone smiling in a Kyte Baby Blush Long Sleeve Pajama Set"/>
        </div>
      </div>
      <div class="col-3">
        <div class="image-wrapper">
          <img class="image" src="https://cdn.shopify.com/s/files/1/0019/7106/0847/files/LS-Toddler-PJ_1000x1000_f8766d27-49cf-4846-96dd-74dfef3119d6.jpg?v=1683810408" alt="Four toddler aged children standing side by side wearing Kyte Baby Slate, Cloud, Sage, and Storm colored Long Sleeve Pajamas"/>
        </div>
      </div>
      <div class="col-3">
        <div class="image-wrapper">
          <img class="image" src="https://cdn.shopify.com/s/files/1/0019/7106/0847/files/IMG_6254.jpg?v=1715304852" alt="A woman dressed in a Kyte Baby Taro Bamboo Jersey smiling while holding a happy infant baby dressed in Taro Long Sleeve Hoodie"/>
        </div>
      </div>
      <div class="col-3">
        <div class="image-wrapper">
          <img class="image" src="https://cdn.shopify.com/s/files/1/0019/7106/0847/files/Adult-Blanket_1000x1000_3026508d-b20a-46c0-8d28-036031d4d76f.jpg?v=1683810408" alt="Photo of a couch with a Slate colored Kyte Baby Adult Blanket draped over top"/>
        </div>
      </div>
    </div>
  </div>
</section>

<section class="faq-section">
  <div class="container">
    <h2 class="heading">Frequently Asked Questions</h2>
    <div class="row">
      <div class="col-12">
        <div class="col-wrapper">
          <div class="top-wrapper">
            <div class="question-wrapper">
              <h5 class="question">What counts as a post?</h5>
              <svg class="detail-open-arrow" width="25" height="25" viewBox="0 0 16 26" fill="none">
                <path d="M2.75 2.5L13.25 13L2.75 23.5" stroke="#EBD7D5" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div class="bottom-wrapper">
            <div class="answer-wrapper">
              <p class="answer">Instagram static posts, Instagram Reels and TikTok Videos are considered posts.</p>
            </div>
          </div>
        </div>
      </div>
      <div class="col-12">
        <div class="col-wrapper">
          <div class="top-wrapper">
            <div class="question-wrapper">
              <h5 class="question">How and when will I be rewarded?</h5>
              <svg class="detail-open-arrow" width="25" height="25" viewBox="0 0 16 26" fill="none">
                <path d="M2.75 2.5L13.25 13L2.75 23.5" stroke="#EBD7D5" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div class="bottom-wrapper">
            <div class="answer-wrapper">
              <p class="answer">At the end of each month, you will receive a reward for the highest tier achieved in your inbox of the email address you’ve registered with!</p>
            </div>
          </div>
        </div>
      </div>
      <div class="col-12">
        <div class="col-wrapper">
          <div class="top-wrapper">
            <div class="question-wrapper">
              <h5 class="question">What are my reward credits eligible towards?</h5>
              <svg class="detail-open-arrow" width="25" height="25" viewBox="0 0 16 26" fill="none">
                <path d="M2.75 2.5L13.25 13L2.75 23.5" stroke="#EBD7D5" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div class="bottom-wrapper">
            <div class="answer-wrapper">
              <p class="answer">All reward credits are single use and redeemable on orders placed at <a href="http://kytebaby.com/">kytebaby.com</a></p>
            </div>
          </div>
        </div>
      </div>
      <div class="col-12">
        <div class="col-wrapper">
          <div class="top-wrapper">
            <div class="question-wrapper">
              <h5 class="question">Can I announce this on my social media?</h5>
              <svg class="detail-open-arrow" width="25" height="25" viewBox="0 0 16 26" fill="none">
                <path d="M2.75 2.5L13.25 13L2.75 23.5" stroke="#EBD7D5" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div class="bottom-wrapper">
            <div class="answer-wrapper">
              <p class="answer">If you’d like to let your audience know about the program, feel free to use the hashtag #kytebabyinfluencer and/or add it to your bio! </p>
            </div>
          </div>
        </div>
      </div>
      <div class="col-12">
        <div class="col-wrapper">
          <div class="top-wrapper">
            <div class="question-wrapper">
              <h5 class="question">What if I didn't receive my reward?</h5>
              <svg class="detail-open-arrow" width="25" height="25" viewBox="0 0 16 26" fill="none">
                <path d="M2.75 2.5L13.25 13L2.75 23.5" stroke="#EBD7D5" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div class="bottom-wrapper">
            <div class="answer-wrapper">
              <p class="answer">Please contact <NAME_EMAIL> with subject line "Influencer Program Rewards".</p>
            </div>
          </div>
        </div>
      </div>
      <div class="col-12">
        <div class="col-wrapper">
          <div class="top-wrapper">
            <div class="question-wrapper">
              <h5 class="question">How do I ensure my post is valid?</h5>
              <svg class="detail-open-arrow" width="25" height="25" viewBox="0 0 16 26" fill="none">
                <path d="M2.75 2.5L13.25 13L2.75 23.5" stroke="#EBD7D5" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div class="bottom-wrapper">
            <div class="answer-wrapper">
              <p class="answer">Ensure you are posting from a public account, tagging the correct brand, and following the post guidelines.</p>
            </div>
          </div>
        </div>
      </div>
      <div class="col-12">
        <div class="col-wrapper">
          <div class="top-wrapper">
            <div class="question-wrapper">
              <h5 class="question">Are there order minimums required to use the rewards?</h5>
              <svg class="detail-open-arrow" width="25" height="25" viewBox="0 0 16 26" fill="none">
                <path d="M2.75 2.5L13.25 13L2.75 23.5" stroke="#EBD7D5" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div class="bottom-wrapper">
            <div class="answer-wrapper">
              <p class="answer">Currently, there are no order minimums, but we reserve the right to update the rewards program at any time. When you complete a rewards tier, you will receive an email confirming your reward, and your rewards balance will be updated in your account.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
                  
<script>
    $(document).ready ( function () {
      var video = $('.tiktok-reward-section .video');
      $(video).mouseenter(function () {
        $(this).get(0).play();
      }).mouseleave(function () {
        $(this).get(0).pause();
      })
  
      $('.video').each(function (){
        this.onplay = function() {
          $('.video').not(this).each(function (){
            $(this).get(0).pause();
          });
        };
      })
  
      resizeFun ();
  
      $(window).resize( function () {
        resizeFun ();
      });
  
      $(document).on('click', '.top-wrapper', function () {
        if (!$(this).parent().hasClass('top-click')) {
          resizeFun ();
        }
        $(this).parent().toggleClass('top-click').parents().siblings().children().removeClass('top-click');
      });
    });
    
    function resizeFun () {
      $('.answer-wrapper').each(function( index ) {
        var contentHeight = $(this).innerHeight();
        $(this).parent('.bottom-wrapper').css('height', contentHeight)
      });
    }
</script>