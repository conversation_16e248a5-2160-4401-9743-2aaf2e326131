/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-page",
      "disabled": true,
      "settings": {
        "show_title": true,
        "page_width": "large"
      }
    },
    "1662080609688fc9dd": {
      "type": "custom-image-with-text-overlay",
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "<div class=\"kyte-text-animation-container\">\n  <svg class=\"kyte-text-animation kyte-text-animation--1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1356 1356\">\n    <path id=\"wavepath\" fill-rule=\"even\" clip-rule=\"evenodd\"\n      d=\"M250 0H1106C1244.07 0 1356 111.929 1356 250V1106C1356 1244.07 1244.07 1356 1106 1356H250C111.929 1356 0 1244.07 0 1106V250C0 111.929 111.929 0 250 0Z\" />\n    <text text-anchor=\"middle\">\n      <textPath class=\"my-text\" href=\"#wavepath\" startOffset=\"150%\">\n        Go ahead, get cozy. Lounge in luxury.\n        <animate attributeName=\"startOffset\" from=\"150%\" to=\"-50%\" begin=\"0s\" dur=\"30s\" repeatCount=\"indefinite\">\n        </animate>\n      </textPath>\n    </text>\n  </svg>\n</div>",
        "reveal_on_scroll": true,
        "section_height": "large",
        "video_filename": "",
        "video_url": "",
        "image": "shopify://shop_images/MAIN2271.jpg",
        "mobile_image_position": "",
        "subheading": "",
        "title": "A softer way to live",
        "heading_style": "h1",
        "content": "<p>Bamboo essentials that bring comfort and luxury to your everyday.</p>",
        "content_style": "text--large",
        "button_text": "Shop Now",
        "button_link": "shopify://collections/kyte-living-best-sellers",
        "button_icon": "",
        "button_style": "",
        "link_style": "button",
        "background_type": "full_width",
        "text_width": "medium",
        "text_position": "bottom_left",
        "image_border_radius": false,
        "subheading_color": "rgba(0,0,0,0)",
        "heading_color": "#ffffff",
        "text_color": "#ffffff",
        "button_background": "#f2eae3",
        "button_text_color": "#604e47",
        "overlay_color": "#000000",
        "overlay_opacity": 20
      }
    },
    "custom_logo_list_XExMWw": {
      "type": "custom-logo-list",
      "blocks": {
        "logo_9XAFjY": {
          "type": "logo",
          "settings": {
            "logo": "shopify://shop_images/time-logo-black-transparent.png",
            "link": ""
          }
        },
        "logo_GtWEEL": {
          "type": "logo",
          "settings": {
            "logo": "shopify://shop_images/forbes.png",
            "link": ""
          }
        },
        "logo_yPjaGz": {
          "type": "logo",
          "settings": {
            "logo": "shopify://shop_images/tmb_logo-readers-digest.webp",
            "link": ""
          }
        },
        "logo_WHqLKh": {
          "type": "logo",
          "settings": {
            "logo": "shopify://shop_images/MSN-Logo-2014.png",
            "link": ""
          }
        }
      },
      "block_order": [
        "logo_9XAFjY",
        "logo_GtWEEL",
        "logo_yPjaGz",
        "logo_WHqLKh"
      ],
      "name": "🪁 Logo list",
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "",
        "reveal_on_scroll": true,
        "subheading": "",
        "title": "",
        "button_text": "",
        "button_link": "",
        "stack_logos": false,
        "background": "#fbf6f3",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)",
        "logo_background": "rgba(0,0,0,0)"
      }
    },
    "custom_featured_collections_ayGFtT": {
      "type": "custom-featured-collections",
      "blocks": {
        "collection_xqKMCj": {
          "type": "collection",
          "settings": {
            "collection": "adultbfcm",
            "label": "",
            "collection_heading": "",
            "collection_description": "",
            "text_width": "medium",
            "text_position": "center",
            "text_alignment": "center",
            "text_style": "",
            "button_text": "Shop Sale",
            "button_icon": "",
            "button_style": "button--primary",
            "button_size": "",
            "button_url": "",
            "video_filename": "",
            "video_url": "",
            "image_position": "",
            "link_url": "",
            "overlay_subheading": "",
            "overlay_title": "",
            "overlay_link_text": "",
            "overlay_text": "",
            "overlay_color": "rgba(0,0,0,.2)",
            "text_color": "#ffffff"
          }
        }
      },
      "block_order": [
        "collection_xqKMCj"
      ],
      "disabled": true,
      "name": "🪁 Featured collections",
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "",
        "aspect_ratio": "",
        "show_additional_text_fields": true,
        "product_in_collection": false,
        "hide_markets": "",
        "horizontal_header": false,
        "horizontal_layout": false,
        "bottom_border": true,
        "content_style": "",
        "button_text": "",
        "button_icon": "",
        "button_style": "",
        "button_size": "",
        "button_url": "",
        "subheading": "up to 50% off",
        "title": "Our Sale Favorites",
        "content": "",
        "products_count": 8,
        "products_per_row": 4,
        "stack_products": false,
        "show_cta": false,
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)"
      }
    },
    "166208140897190502": {
      "type": "custom-featured-collections",
      "blocks": {
        "collection_GU6L4x": {
          "type": "collection",
          "settings": {
            "collection": "kyte-living-best-sellers",
            "label": "Women",
            "collection_heading": "",
            "collection_description": "",
            "text_width": "medium",
            "text_position": "left",
            "text_alignment": "left",
            "text_style": "",
            "button_text": "Shop All",
            "button_icon": "",
            "button_style": "button--primary",
            "button_size": "",
            "button_url": "",
            "video_filename": "",
            "video_url": "",
            "image_position": "",
            "link_url": "",
            "overlay_subheading": "The New Range",
            "overlay_title": "Kyte Intimates",
            "overlay_link_text": "Shop Now",
            "overlay_text": "Lorem ipsum dolor sit amet",
            "overlay_color": "rgba(0,0,0,.2)",
            "text_color": "#ffffff"
          }
        }
      },
      "block_order": [
        "collection_GU6L4x"
      ],
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "",
        "aspect_ratio": "",
        "show_additional_text_fields": true,
        "product_in_collection": true,
        "hide_markets": "",
        "horizontal_header": false,
        "horizontal_layout": false,
        "bottom_border": true,
        "content_style": "",
        "button_text": "",
        "button_icon": "",
        "button_style": "",
        "button_size": "",
        "button_url": "",
        "subheading": "",
        "title": "Best Sellers",
        "content": "",
        "products_count": 10,
        "products_per_row": 5,
        "stack_products": false,
        "show_cta": false,
        "background": "#ffffff",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)"
      }
    },
    "slideshow_A378rM": {
      "type": "slideshow",
      "blocks": {
        "image_byeazY": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/Wide_Shot_Legging_Banner-03.jpg",
            "mobile_image": "shopify://shop_images/Espresso_Leggings-15-2.jpg",
            "text_position": "bottom_left",
            "subheading": "",
            "title": "Movement made easy in super-soft, high-stretch Bamboo Flex",
            "button_1_text": "Shop Leggings",
            "button_1_link": "shopify://collections/bamboo-flex-women-s-leggings-all",
            "button_2_text": "",
            "button_2_link": "",
            "text_color": "#ffffff",
            "button_background": "#ffffff",
            "button_text_color": "#000000",
            "overlay_color": "#000000",
            "overlay_opacity": 20
          }
        }
      },
      "block_order": [
        "image_byeazY"
      ],
      "name": "Slideshow",
      "settings": {
        "section_height": "auto",
        "transition_type": "sweep",
        "show_initial_transition": false,
        "autoplay": true,
        "cycle_speed": 5,
        "background": "rgba(0,0,0,0)"
      }
    },
    "custom_image_with_text_overlay_cbjfcK": {
      "type": "custom-image-with-text-overlay",
      "disabled": true,
      "name": "🪁 Image with text overlay",
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "",
        "reveal_on_scroll": true,
        "section_height": "large",
        "video_filename": "",
        "video_url": "",
        "image": "shopify://shop_images/Untitled_design_-_2025-06-03T225827.547.png",
        "mobile_image_position": "",
        "subheading": "",
        "title": "Impossibly soft, effortlessly stylish",
        "heading_style": "h1",
        "content": "",
        "content_style": "",
        "button_text": "Shop Sets",
        "button_link": "shopify://collections/womens-biker-short-sets",
        "button_icon": "",
        "button_style": "",
        "link_style": "button",
        "background_type": "full_width",
        "text_width": "medium",
        "text_position": "bottom_left",
        "image_border_radius": false,
        "subheading_color": "#ffffff",
        "heading_color": "#ffffff",
        "text_color": "#ffffff",
        "button_background": "#f2eae3",
        "button_text_color": "#604e47",
        "overlay_color": "#000000",
        "overlay_opacity": 30
      }
    },
    "collection_list_zQWLgX": {
      "type": "custom-collection-list",
      "blocks": {
        "collection_PHjxPM": {
          "type": "collection",
          "settings": {
            "collection": "",
            "image": "shopify://shop_images/Ski_Women_s_LS_Pajamas-01_2x3_f3cba6e6-c2af-43c1-a90e-835e2d98225d.jpg",
            "subheading": "",
            "title": "Pajamas",
            "link_text": "",
            "link_url": "shopify://collections/womens-pajamas-kyte-living",
            "text_color": "rgba(0,0,0,0)",
            "overlay_color": "#000000",
            "overlay_opacity": 30
          }
        },
        "collection_kaiweE": {
          "type": "collection",
          "settings": {
            "collection": "",
            "image": "shopify://shop_images/midnight_biker_set-04.jpg",
            "subheading": "",
            "title": "Biker Short Sets",
            "link_text": "",
            "link_url": "shopify://collections/womens-biker-short-sets",
            "text_color": "rgba(0,0,0,0)",
            "overlay_color": "#000000",
            "overlay_opacity": 30
          }
        },
        "collection_eCaGa4": {
          "type": "collection",
          "settings": {
            "collection": "chunky-knit-womens-oversized-cardigan-seasonal",
            "image": "shopify://shop_images/Atlantic_in_Leggings-21.jpg",
            "subheading": "",
            "title": "Chunky Knit",
            "link_text": "",
            "link_url": "",
            "text_color": "rgba(0,0,0,0)",
            "overlay_color": "#000000",
            "overlay_opacity": 30
          }
        },
        "collection_qfjGxa": {
          "type": "collection",
          "settings": {
            "collection": "",
            "image": "shopify://shop_images/Group_Shot_Womens_Bras_Undies_74a097fe-afaa-42df-a4f6-83f8e114476d.jpg",
            "subheading": "",
            "title": "Intimates",
            "link_text": "",
            "link_url": "shopify://collections/intimates-kyte-living",
            "text_color": "rgba(0,0,0,0)",
            "overlay_color": "#000000",
            "overlay_opacity": 30
          }
        },
        "collection_yhkLGA": {
          "type": "collection",
          "settings": {
            "collection": "",
            "image": "shopify://shop_images/Latte_Leopard_Lounge_Robe_Midnight_Tank_set-11_2f51afe8-beec-4580-8110-ba8141a7398d.jpg",
            "subheading": "",
            "title": "Robes",
            "link_text": "",
            "link_url": "shopify://collections/womens-robes-kyte-living",
            "text_color": "rgba(0,0,0,0)",
            "overlay_color": "#000000",
            "overlay_opacity": 30
          }
        },
        "collection_RM9A6A": {
          "type": "collection",
          "settings": {
            "collection": "",
            "image": "shopify://shop_images/Fir_Adult_Blanket-02.jpg",
            "subheading": "",
            "title": "Blankets",
            "link_text": "",
            "link_url": "shopify://collections/home-blankets-kyte-living",
            "text_color": "rgba(0,0,0,0)",
            "overlay_color": "#000000",
            "overlay_opacity": 30
          }
        },
        "collection_NbgzYg": {
          "type": "collection",
          "settings": {
            "collection": "",
            "image": "shopify://shop_images/Women_s_Nursing_Bra_in_Espresso-01.jpg",
            "subheading": "",
            "title": "Nursing",
            "link_text": "",
            "link_url": "shopify://collections/intimates-nursing-kyte-living",
            "text_color": "rgba(0,0,0,0)",
            "overlay_color": "#000000",
            "overlay_opacity": 30
          }
        }
      },
      "block_order": [
        "collection_PHjxPM",
        "collection_kaiweE",
        "collection_eCaGa4",
        "collection_qfjGxa",
        "collection_yhkLGA",
        "collection_RM9A6A",
        "collection_NbgzYg"
      ],
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": true,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "",
        "reveal_on_scroll": false,
        "layout": "carousel",
        "subheading": "What we're loving",
        "title": "Featured",
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)"
      }
    },
    "custom_image_with_text_overlay_zaNMmf": {
      "type": "custom-image-with-text-overlay",
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "",
        "reveal_on_scroll": true,
        "section_height": "large",
        "video_filename": "",
        "video_url": "",
        "image": "shopify://shop_images/Kytelivinglifestyles-03.jpg",
        "mobile_image_position": "",
        "subheading": "",
        "title": "Bamboo Bedding",
        "heading_style": "h1",
        "content": "<p>A luxurious sleep experience that will have you feeling like royalty.</p>",
        "content_style": "",
        "button_text": "Shop Bedding",
        "button_link": "shopify://collections/home-kyte-living",
        "button_icon": "",
        "button_style": "",
        "link_style": "button",
        "background_type": "full_width",
        "text_width": "medium",
        "text_position": "middle_left",
        "image_border_radius": false,
        "subheading_color": "#ffffff",
        "heading_color": "#ffffff",
        "text_color": "#ffffff",
        "button_background": "#f2eae3",
        "button_text_color": "#604e47",
        "overlay_color": "#000000",
        "overlay_opacity": 20
      }
    },
    "image-with-text": {
      "type": "custom-image-with-text",
      "blocks": {
        "item-1": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Twinkle_Tree_LS_Pajama_Family_Matching_1x1_42f55f2e-1afc-418e-9d3e-bb8dc4e1bee0.jpg",
            "tab_label": "",
            "subheading": "",
            "title": "For the whole family",
            "content": "<p>Only the best for your babies—and for you, too.  Experience the buttery softness and cooling comfort of our signature bamboo rayon with matching pajamas and loungewear sets for the whole family.</p>",
            "button_text": "Shop Family Matching",
            "button_link": "shopify://collections/family-pajamas",
            "button_size": "",
            "button_style": "button--primary",
            "button_icon": "",
            "button_extra_attributes": "",
            "drawer_button_text": "",
            "drawer_button_size": "",
            "drawer_button_style": "",
            "drawer_button_icon": "",
            "drawer_button_page": ""
          }
        }
      },
      "block_order": [
        "item-1"
      ],
      "settings": {
        "use_padding": true,
        "box_shadow": false,
        "section_extra_padding": false,
        "reveal_on_scroll": true,
        "background_type": "full_width",
        "background_overlap": "image",
        "image_position": "left",
        "text_alignment": "left",
        "cycle_speed": 8,
        "background": "#f2eae3",
        "accent_background": "#604e47",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)",
        "styling_class": "",
        "anchor": "",
        "contain": false,
        "override_blend": false,
        "extra_code": ""
      }
    },
    "1740192382b9977c47": {
      "type": "apps",
      "blocks": {
        "tolstoy_shoppable_video_quiz_carousel_block_n6QaKh": {
          "type": "shopify://apps/tolstoy-shoppable-video-quiz/blocks/carousel-block/06fa8282-42ff-403e-b67c-1936776aed11",
          "settings": {
            "publishId": "zf9ba6hwuj54w"
          }
        }
      },
      "block_order": [
        "tolstoy_shoppable_video_quiz_carousel_block_n6QaKh"
      ],
      "settings": {
        "text_width": "medium",
        "text_position": "center",
        "text_alignment": "center",
        "background_type": "full_width",
        "subheading": "",
        "subheading_style": "heading--small",
        "title": "",
        "heading_style": "",
        "intro_text": "",
        "intro_text_style": "",
        "content": "",
        "content_style": "",
        "buttons_text": "",
        "buttons_text_style": "heading--small",
        "button_text": "",
        "button_link": "",
        "button_size": "",
        "button_style": "",
        "button_icon": "",
        "button_extra_attributes": "",
        "inner_background": "rgba(0,0,0,0)",
        "background": "",
        "heading_color": "",
        "text_color": "",
        "button_background": "",
        "button_text_color": "",
        "mobile_no_horizontal_padding": false,
        "include_horizontal_margins": true,
        "include_vertical_margins": true,
        "styling_class": "",
        "anchor": "",
        "use_padding": true,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": ""
      }
    },
    "multi_column_rQJmnC": {
      "type": "custom-multi-column",
      "blocks": {
        "item_xLJ4J6": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Nursery_Home_Flatlay.jpg",
            "image_width": 100,
            "title": "New + Featured",
            "content": "<p>Don't miss these!</p>",
            "link_text": "Shop All New + Featured",
            "link_url": "shopify://collections/new-featured-kyte-living",
            "text_alignment": "",
            "vertical_alignment": "start",
            "overlay_opacity": 0,
            "overlay_color": "rgba(0,0,0,0)",
            "image_border": "rgba(0,0,0,0)"
          }
        },
        "item_Nzg7Eq": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/women-dresses.jpg",
            "image_width": 100,
            "title": "Women",
            "content": "<p><a href=\"/collections/women-sleep-lounge-sets-kyte-living\" title=\"Women's Sleep + Lounge Sets\">Sleep + Lounge Sets</a><br/><a href=\"/collections/womens-tops-kyte-living\" title=\"Women's Tops\">Tops</a><br/><a href=\"/collections/womens-bottoms-kyte-living\" title=\"Women's Bottoms\">Bottoms</a><br/><a href=\"/collections/womens-dresses-jumpsuits-kyte-living\" title=\"Women's Dresses + Jumpsuits\">Dresses + Jumpsuits</a><br/><a href=\"/collections/womens-accessories-kyte-living\" title=\"Women's Accessories\">Accessories</a></p>",
            "link_text": "Shop All  Women",
            "link_url": "shopify://collections/women-kyte-living",
            "text_alignment": "",
            "vertical_alignment": "start",
            "overlay_opacity": 0,
            "overlay_color": "rgba(0,0,0,0)",
            "image_border": "rgba(0,0,0,0)"
          }
        },
        "item_piqpf8": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/men-bottoms.jpg",
            "image_width": 100,
            "title": "Men",
            "content": "<p><a href=\"/collections/mens-sleep-lounge-sets-kyte-living\" title=\"Men's Sleep + Lounge Sets\">Sleep + Lounge Sets</a><br/><a href=\"/collections/mens-tops-kyte-living\" title=\"Men's Tops\">Tops</a><br/><a href=\"/collections/mens-bottoms-kyte-living\" title=\"Men's Bottoms\">Bottoms</a><br/><a href=\"/collections/mens-accessories-kyte-living\" title=\"Men's Accessories\">Accessories</a></p>",
            "link_text": "Shop All Men",
            "link_url": "shopify://collections/men-kyte-living",
            "text_alignment": "",
            "vertical_alignment": "start",
            "overlay_opacity": 0,
            "overlay_color": "rgba(0,0,0,0)",
            "image_border": "rgba(0,0,0,0)"
          }
        },
        "item_QNiAkx": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/intimates-nursing.jpg",
            "image_width": 100,
            "title": "Intimates",
            "content": "<p><a href=\"/collections/intimates-bras-kyte-living\" title=\"Intimates - Bras\">Bras</a><br/><a href=\"/collections/intimates-underwear-kyte-living\" title=\"Intimates - Underwear\">Underwear</a><br/><a href=\"/collections/intimates-nursing-kyte-living\" title=\"Nursing\">Nursing</a><br/><a href=\"/collections/intimates-lounge-robes-kyte-living\" title=\"Intimates - Lounge Robes\">Lounge Robes</a><br/><a href=\"/collections/intimates-socks-kyte-living\" title=\"Intimates - Socks\">Socks</a></p>",
            "link_text": "Shop All Intimates",
            "link_url": "shopify://collections/intimates-kyte-living",
            "text_alignment": "",
            "vertical_alignment": "start",
            "overlay_opacity": 0,
            "overlay_color": "rgba(0,0,0,0)",
            "image_border": "rgba(0,0,0,0)"
          }
        },
        "item_nzqkw7": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/home-blankets.jpg",
            "image_width": 100,
            "title": "Home",
            "content": "<p><a href=\"/collections/home-blankets-kyte-living\" title=\"Blankets\">Blankets</a><br/><a href=\"/collections/home-sheets-kyte-living\" title=\"Sheets\">Sheets</a><br/><a href=\"/collections/home-pillowcases-kyte-living\" title=\"Pillowcases\">Pillowcases</a><br/><a href=\"/collections/home-bath-kyte-living\" title=\"Bath\">Bath</a></p>",
            "link_text": "Shop All Home",
            "link_url": "shopify://collections/home-kyte-living",
            "text_alignment": "",
            "vertical_alignment": "start",
            "overlay_opacity": 0,
            "overlay_color": "rgba(0,0,0,0)",
            "image_border": "rgba(0,0,0,0)"
          }
        }
      },
      "block_order": [
        "item_xLJ4J6",
        "item_Nzg7Eq",
        "item_piqpf8",
        "item_QNiAkx",
        "item_nzqkw7"
      ],
      "disabled": true,
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "",
        "subheading": "The family is getting bigger",
        "title": "Live better with Kyte",
        "heading_alignment": "center",
        "content": "<p>Our sleep wear, leisure wear, and house and home range. Brought to you with love by the same team at Kyte Baby.</p>",
        "content_style": "text--large",
        "link_style": "heading heading--small link",
        "button_style": "",
        "reveal_on_scroll": true,
        "stack_items": false,
        "column_alignment": "center",
        "mobile_item_size": "medium",
        "desktop_item_size": "medium",
        "spacing": "tight",
        "block_content_style": "",
        "heading_style": "h4",
        "block_text_alignment": "center",
        "heading_over_image": true,
        "content_over_image": false,
        "button_over_image": false,
        "overlay_bottom_offset": 15,
        "background": "#e6d5c8",
        "subheading_color": "#604e47",
        "heading_color": "rgba(0,0,0,0)",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)",
        "block_heading_color": "#ffffff",
        "block_subheading_color": "#ffffff",
        "block_text_color": "rgba(0,0,0,0)",
        "link_color": "#6c5046",
        "overlay_color": "#6c5046",
        "overlay_opacity": 90
      }
    },
    "1662078220870a583f": {
      "type": "custom-logo-list",
      "blocks": {
        "16620782205e1be006-0": {
          "type": "logo",
          "settings": {
            "logo": "shopify://shop_images/the-bump-logo.png",
            "link": ""
          }
        },
        "b5f44458-c887-4c73-af1b-39058675af8c": {
          "type": "logo",
          "settings": {
            "logo": "shopify://shop_images/IMG_2227-transformed.png",
            "link": ""
          }
        },
        "16620782205e1be006-2": {
          "type": "logo",
          "settings": {
            "logo": "shopify://shop_images/IMG_2225-transformed.png",
            "link": ""
          }
        },
        "16620782205e1be006-4": {
          "type": "logo",
          "settings": {
            "logo": "shopify://shop_images/The-Everymom-badge.webp",
            "link": ""
          }
        },
        "16620782205e1be006-1": {
          "type": "logo",
          "settings": {
            "logo": "shopify://shop_images/glamour-logo-black-and-white.png",
            "link": ""
          }
        },
        "16620782205e1be006-3": {
          "type": "logo",
          "settings": {
            "logo": "shopify://shop_images/Untitled_design_2_b015b489-a6e7-4a6e-8588-6518c167cd28.png",
            "link": ""
          }
        },
        "16620782205e1be006-5": {
          "type": "logo",
          "settings": {
            "logo": "shopify://shop_images/Untitled_design_7_99394c7a-85e7-41c7-aef5-83597160e157.png",
            "link": ""
          }
        }
      },
      "block_order": [
        "16620782205e1be006-0",
        "b5f44458-c887-4c73-af1b-39058675af8c",
        "16620782205e1be006-2",
        "16620782205e1be006-4",
        "16620782205e1be006-1",
        "16620782205e1be006-3",
        "16620782205e1be006-5"
      ],
      "disabled": true,
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "",
        "reveal_on_scroll": true,
        "subheading": "",
        "title": "",
        "button_text": "",
        "button_link": "",
        "stack_logos": false,
        "background": "#e6d5c8",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)",
        "logo_background": "rgba(0,0,0,0)"
      }
    },
    "1662257261f2889fd0": {
      "type": "custom-special-promotions",
      "blocks": {
        "770f56ee-5276-4531-aab6-a3f34ea00ce4": {
          "type": "promotion",
          "settings": {
            "image": "shopify://shop_images/Sleep_Bag_in_Burgundy-02_1.jpg",
            "tab": "Women",
            "title": "Sleep and Lounge Sets",
            "link": "#",
            "icon_title": "Soft wearable blanket"
          }
        },
        "82c51b92-89bb-4eeb-a542-437e06627b26": {
          "type": "promotion",
          "settings": {
            "image": "shopify://shop_images/kyte-baby-toddler-long-sleeve-pajamas-long-sleeve-pajamas-in-bee-mine-35550423253103.jpg",
            "tab": "Women",
            "title": "Tops",
            "link": "#",
            "icon": "shopify://shop_images/kyte_icon-toddler-pajama.png",
            "icon_title": "Perfect for bedtime snuggles"
          }
        },
        "fec53371-e4f6-400f-8674-f94505cbf88b": {
          "type": "promotion",
          "settings": {
            "image": "shopify://shop_images/LVBG_footie_6.jpg",
            "tab": "Women",
            "title": "Bottoms",
            "link": "#",
            "icon": "shopify://shop_images/kyte_icon-footie_9dd2e6be-068d-4d09-b4ae-1e17d2e39974.png",
            "icon_title": "Designed for comfort"
          }
        },
        "promotion_kkh3jh": {
          "type": "promotion",
          "settings": {
            "image": "shopify://shop_images/LVBG_footie_6.jpg",
            "tab": "Women",
            "title": "Dresses + Jumpsuits",
            "link": "#",
            "icon": "shopify://shop_images/kyte_icon-footie_9dd2e6be-068d-4d09-b4ae-1e17d2e39974.png",
            "icon_title": "Designed for comfort"
          }
        },
        "promotion_37AhAN": {
          "type": "promotion",
          "settings": {
            "image": "shopify://shop_images/LVBG_footie_6.jpg",
            "tab": "Women",
            "title": "Accessories",
            "link": "#",
            "icon": "shopify://shop_images/kyte_icon-footie_9dd2e6be-068d-4d09-b4ae-1e17d2e39974.png",
            "icon_title": "Designed for comfort"
          }
        },
        "promotion_dirCaT": {
          "type": "promotion",
          "settings": {
            "image": "shopify://shop_images/Sleep_Bag_in_Burgundy-02_1.jpg",
            "tab": "Men",
            "title": "Sleep and Lounge Sets",
            "link": "#",
            "icon_title": "Soft wearable blanket"
          }
        },
        "promotion_8PEYVN": {
          "type": "promotion",
          "settings": {
            "image": "shopify://shop_images/kyte-baby-toddler-long-sleeve-pajamas-long-sleeve-pajamas-in-bee-mine-35550423253103.jpg",
            "tab": "Men",
            "title": "Tops",
            "link": "#",
            "icon": "shopify://shop_images/kyte_icon-toddler-pajama.png",
            "icon_title": "Perfect for bedtime snuggles"
          }
        },
        "promotion_qxcD38": {
          "type": "promotion",
          "settings": {
            "image": "shopify://shop_images/LVBG_footie_6.jpg",
            "tab": "Men",
            "title": "Bottoms",
            "link": "#",
            "icon": "shopify://shop_images/kyte_icon-footie_9dd2e6be-068d-4d09-b4ae-1e17d2e39974.png",
            "icon_title": "Designed for comfort"
          }
        },
        "promotion_gQagic": {
          "type": "promotion",
          "settings": {
            "image": "shopify://shop_images/LVBG_footie_6.jpg",
            "tab": "Men",
            "title": "Accessories",
            "link": "#",
            "icon": "shopify://shop_images/kyte_icon-footie_9dd2e6be-068d-4d09-b4ae-1e17d2e39974.png",
            "icon_title": "Designed for comfort"
          }
        },
        "promotion_hchi4B": {
          "type": "promotion",
          "settings": {
            "image": "shopify://shop_images/LVBG_footie_6.jpg",
            "tab": "Intimates",
            "title": "Bras",
            "link": "#",
            "icon_title": "Designed for comfort"
          }
        },
        "promotion_p63ytW": {
          "type": "promotion",
          "settings": {
            "image": "shopify://shop_images/LVBG_footie_6.jpg",
            "tab": "Intimates",
            "title": "Underwear",
            "link": "#",
            "icon_title": "Designed for comfort"
          }
        },
        "promotion_YTTEg3": {
          "type": "promotion",
          "settings": {
            "image": "shopify://shop_images/LVBG_footie_6.jpg",
            "tab": "Intimates",
            "title": "Nursing",
            "link": "#",
            "icon_title": "Designed for comfort"
          }
        },
        "promotion_WdDnzz": {
          "type": "promotion",
          "settings": {
            "image": "shopify://shop_images/LVBG_footie_6.jpg",
            "tab": "Intimates",
            "title": "Loungewear",
            "link": "#",
            "icon_title": "Designed for comfort"
          }
        },
        "promotion_8f4kw9": {
          "type": "promotion",
          "settings": {
            "image": "shopify://shop_images/LVBG_footie_6.jpg",
            "tab": "Intimates",
            "title": "Socks",
            "link": "#",
            "icon_title": "Designed for comfort"
          }
        },
        "promotion_HCeqaH": {
          "type": "promotion",
          "settings": {
            "image": "shopify://shop_images/LVBG_footie_6.jpg",
            "tab": "Home",
            "title": "Blankets",
            "link": "#",
            "icon_title": "Designed for comfort"
          }
        },
        "promotion_eRAbLF": {
          "type": "promotion",
          "settings": {
            "image": "shopify://shop_images/LVBG_footie_6.jpg",
            "tab": "Home",
            "title": "Sheets",
            "link": "#",
            "icon_title": "Designed for comfort"
          }
        },
        "promotion_pKgC3e": {
          "type": "promotion",
          "settings": {
            "image": "shopify://shop_images/LVBG_footie_6.jpg",
            "tab": "Home",
            "title": "Pillowcases",
            "link": "#",
            "icon_title": "Designed for comfort"
          }
        },
        "promotion_hCANg9": {
          "type": "promotion",
          "settings": {
            "image": "shopify://shop_images/LVBG_footie_6.jpg",
            "tab": "Home",
            "title": "Bath",
            "link": "#",
            "icon_title": "Designed for comfort"
          }
        }
      },
      "block_order": [
        "770f56ee-5276-4531-aab6-a3f34ea00ce4",
        "82c51b92-89bb-4eeb-a542-437e06627b26",
        "fec53371-e4f6-400f-8674-f94505cbf88b",
        "promotion_kkh3jh",
        "promotion_37AhAN",
        "promotion_dirCaT",
        "promotion_8PEYVN",
        "promotion_qxcD38",
        "promotion_gQagic",
        "promotion_hchi4B",
        "promotion_p63ytW",
        "promotion_YTTEg3",
        "promotion_WdDnzz",
        "promotion_8f4kw9",
        "promotion_HCeqaH",
        "promotion_eRAbLF",
        "promotion_pKgC3e",
        "promotion_hCANg9"
      ],
      "disabled": true,
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "",
        "horizontal_header": false,
        "subheading": "",
        "title": "Live life with Kyte 🪁",
        "content": "",
        "products_count": 8,
        "products_per_row": 4,
        "stack_products": false,
        "show_cta": false,
        "background": "#fbf6f3",
        "subheading_color": "",
        "heading_color": "",
        "text_color": "#d9c5a7",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)"
      }
    },
    "rich_text_8FETVg": {
      "type": "custom-rich-text",
      "disabled": true,
      "settings": {
        "use_padding": false,
        "box_shadow": true,
        "background_type": "boxed",
        "text_width": "large",
        "text_position": "center",
        "text_alignment": "center",
        "subheading": "",
        "subheading_style": "heading--small",
        "title": "We made this for you.",
        "heading_style": "",
        "intro_text": "",
        "intro_text_style": "",
        "content": "<p>Our sleep wear, leisure wear, and house and home range. Brought to you with love by the same team at Kyte Baby.</p>",
        "content_style": "",
        "buttons_text": "",
        "buttons_text_style": "heading--small",
        "button_text": "",
        "button_link": "",
        "button_size": "",
        "button_style": "button--primary",
        "button_icon": "nav-arrow-right",
        "button_extra_attributes": "",
        "inner_background": "#604e47",
        "background": "#fbf6f3",
        "heading_color": "#d9c5a7",
        "text_color": "#ffffff",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)",
        "styling_class": "",
        "anchor": "",
        "contain": false,
        "override_blend": false,
        "extra_code": ""
      }
    },
    "16624538445447d122": {
      "type": "custom-content-with-carousel",
      "blocks": {
        "19c2d25d-77c1-4aee-9ce2-7f4d75d4a35c": {
          "type": "item",
          "settings": {
            "icon": "shopify://shop_images/kyte_icon-baby-sleep.png",
            "icon_code": "",
            "title": "Get your baby on a sleep routine",
            "content": "<p>We all sleep better with good sleep hygiene. Just like dimming the lights, zipping your littles into their sleep bags lets them know it’s time to go to sleep. Melatonin, activate.</p>"
          }
        },
        "080e77a2-faa2-4ab3-862b-351ee0fe18d4": {
          "type": "item",
          "settings": {
            "icon": "shopify://shop_images/kyte_icon-temp-control.png",
            "icon_code": "",
            "title": "Regulate your body temperature",
            "content": "<p>Bamboo is breathable and 3º cooler than its cotton counterpart. No more night sweats or tossing and turning from overheating. You and your babies will sleep better in bamboo.</p>"
          }
        },
        "43bbd58d-c606-4174-a262-18dbaaad1f30": {
          "type": "item",
          "settings": {
            "icon": "shopify://shop_images/kyte_icon-sleep.png",
            "icon_code": "",
            "title": "Learn from parents who like to sleep",
            "content": "<p>We’re parents too, so we’re obsessed with sleep, safety and comfort. Our products are informed by the latest sleep safety research and continuously improved by your feedback.</p>"
          }
        },
        "f455f180-7920-41f0-b27b-5fc1ddf2c9c3": {
          "type": "item",
          "settings": {
            "icon": "shopify://shop_images/kyte_icon-planet.png",
            "icon_code": "",
            "title": "Save the planet",
            "content": "<p>No, seriously. Unlike cotton, bamboo is a sustainable resource. It uses 30% less water, consumes 45% more carbon dioxide, generates 35% more oxygen, and uses no pesticides.</p>"
          }
        }
      },
      "block_order": [
        "19c2d25d-77c1-4aee-9ce2-7f4d75d4a35c",
        "080e77a2-faa2-4ab3-862b-351ee0fe18d4",
        "43bbd58d-c606-4174-a262-18dbaaad1f30",
        "f455f180-7920-41f0-b27b-5fc1ddf2c9c3"
      ],
      "disabled": true,
      "settings": {
        "background_type": "boxed",
        "text_width": "fill",
        "text_position": "center",
        "text_alignment": "left",
        "layout": "horizontal",
        "mobile_reduce_padding": true,
        "use_padding": true,
        "box_shadow": false,
        "subheading": "Why Kyte baby?",
        "subheading_style": "heading--small",
        "title": "Because we all need  a good night’s sleep",
        "heading_style": "h2",
        "intro_text": "",
        "intro_text_style": "text--large",
        "content": "<p>It started with the Sleep Bag: A wearable blanket made to help babies fall asleep (and stay asleep) comfortably and safely—because, let’s be honest, nobody wants to sleep without a blanket, babies included. Thanks to the comforting effects of bamboo (and years of product research and development), our sleep bags were just the beginning of better sleep for the whole family.</p>",
        "content_style": "",
        "button_text": "",
        "button_link": "",
        "button_size": "",
        "button_style": "",
        "button_icon": "",
        "button_extra_attributes": "",
        "buttons_text": "",
        "buttons_text_style": "heading--small",
        "background": "#fbf6f3",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)",
        "styling_class": "",
        "anchor": "",
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": ""
      }
    },
    "1662078848f9fc7430": {
      "type": "custom-rich-text",
      "disabled": true,
      "settings": {
        "use_padding": false,
        "box_shadow": false,
        "background_type": "boxed",
        "text_width": "medium",
        "text_position": "center",
        "text_alignment": "center",
        "subheading": "",
        "subheading_style": "heading--small",
        "title": "Ready to live life with Kyte? 🪁",
        "heading_style": "",
        "intro_text": "",
        "intro_text_style": "",
        "content": "",
        "content_style": "",
        "buttons_text": "",
        "buttons_text_style": "heading--small",
        "button_text": "Shop Now",
        "button_link": "shopify://pages/the-original-bamboo-sleep-bag",
        "button_size": "",
        "button_style": "",
        "button_icon": "nav-arrow-right",
        "button_extra_attributes": "",
        "inner_background": "#604e47",
        "background": "rgba(0,0,0,0)",
        "heading_color": "#ffffff",
        "text_color": "#ffffff",
        "button_background": "#d9c5a7",
        "button_text_color": "rgba(0,0,0,0)",
        "styling_class": "",
        "anchor": "",
        "contain": false,
        "override_blend": false,
        "extra_code": ""
      }
    }
  },
  "order": [
    "main",
    "1662080609688fc9dd",
    "custom_logo_list_XExMWw",
    "custom_featured_collections_ayGFtT",
    "166208140897190502",
    "slideshow_A378rM",
    "custom_image_with_text_overlay_cbjfcK",
    "collection_list_zQWLgX",
    "custom_image_with_text_overlay_zaNMmf",
    "image-with-text",
    "1740192382b9977c47",
    "multi_column_rQJmnC",
    "1662078220870a583f",
    "1662257261f2889fd0",
    "rich_text_8FETVg",
    "16624538445447d122",
    "1662078848f9fc7430"
  ]
}
