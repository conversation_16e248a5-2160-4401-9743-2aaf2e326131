/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "collection-banner": {
      "type": "collection-banner",
      "settings": {
        "show_collection_image": true,
        "show_collection_description": true,
        "text_width": "medium",
        "section_height": "small",
        "image_text_color": "#ffffff",
        "image_overlay_color": "#35464c",
        "image_overlay_opacity": 88,
        "quick_links": ""
      }
    },
    "main": {
      "type": "main-collection",
      "settings": {
        "show_sort_by": true,
        "products_per_page": 24,
        "mobile_products_per_row": "2",
        "desktop_products_per_row": 4,
        "show_filters": true,
        "show_filter_group_name": false,
        "show_color_swatch": true,
        "open_first_filter_group": false,
        "filter_position": "always_visible",
        "promotion_position": "top",
        "promotion_height": "small"
      }
    }
  },
  "order": [
    "collection-banner",
    "main"
  ]
}
