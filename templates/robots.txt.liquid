{% for group in robots.default_groups %}
  {{- group.user_agent }}

  {%- for rule in group.rules -%}
    {{ rule }}
  {%- endfor -%}

  {%- if group.user_agent.value == '*' -%}
    {{ 'Disallow: /*?*section_id=*' }}
    {{- 'Disallow: /*&section_id=*' }}
    {{- 'Disallow: /*?*option_values=*' }}
    {{- 'Disallow: /*&option_values=*' }}
    {{- 'Disallow: /*.js$' }}
  {%- endif -%}

  {%- if group.sitemap != blank -%}
    {{ group.sitemap }}
  {%- endif -%}
{% endfor %}
