{% layout none %}
<!-- <meta name="viewport" content="width=device-width, initial-scale=1.0">
<script src="//code.jquery.com/jquery-3.1.0.min.js" type="text/javascript"></script>
<script type="text/javascript" async="" src="https://app.backinstock.org/widget/37610_1619623437.js?v=6&shop=the-king-mcneal-collection.myshopify.com"></script>
{% comment %}
Don't edit this file.
This snippet is auto generated and will be overwritten.
{% endcomment %}

<script id="back-in-stock-helper">
  var _BISConfig = _BISConfig || {};

{% if product %}
  _BISConfig.product = {{ product | json }};

  {% for variant in product.variants %}
    _BISConfig.product.variants[{{forloop.index | minus: 1 }}]['inventory_quantity'] = {{ variant.inventory_quantity }};
  {% endfor %}
{% endif %}

{% if customer %}
  _BISConfig.customer = { email: {{ customer.email | json }} };
{% endif %}
</script>


 -->

<html><!--<![endif]-->
  <head>
<!-- Start of Shoplift scripts -->
{% render 'shoplift' %}
<!-- End of Shoplift scripts -->
 
    <meta name="viewport" content="width=device-width"> 
    <style> 
      body, html { 
        background: transparent;
        -webkit-font-smoothing: antialiased;
        height: 100%;
      }

      html {
        font-family: sans-serif;
        -ms-text-size-adjust: 100%;
        -webkit-text-size-adjust: 100%;
      }
      body {
        margin: 0;
      }
      a {
        background-color: transparent;
      }
      a:active,
      a:hover {
        outline: 0;
      }
      h1 {
        font-size: 2em;
        margin: 0.67em 0;
      }
      hr {
        -moz-box-sizing: content-box;
        -webkit-box-sizing: content-box;
                box-sizing: content-box;
        height: 0;
        margin-top: 20px;
        margin-bottom: 20px;
        border: 0;
        border-top: 1px solid #eeeeee;
      }
      button,
      input,
      optgroup,
      select,
      textarea {
        color: inherit;
        font: inherit;
        margin: 0;
        font-family: inherit;
        font-size: inherit;
        line-height: inherit;
      }


      button {
        overflow: visible;
      }
      button,
      select {
        text-transform: none;
      }
      button {
        -webkit-appearance: button;
        cursor: pointer;
      }

      button[disabled] {
        opacity: 0.6;
      }

      button::-moz-focus-inner,
      input::-moz-focus-inner {
        border: 0;
        padding: 0;
      }
      input {
        line-height: normal;
      }
      input[type="number"]::-webkit-inner-spin-button,
      input[type="number"]::-webkit-outer-spin-button {
        height: auto;
      }

      * {
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
      }
      *:before,
      *:after {
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
      }


      html {
        font-size: 10px;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
      }
      body {
        font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
        font-size: 14px;
        line-height: 1.42857143;
        color: #333333;
        overflow: hidden;
        -moz-transition: background-color 0.15s linear;
        -webkit-transition: background-color 0.15s linear;
        -o-transition: background-color 0.15s linear;
        transition: background-color 0.15s cubic-bezier(0.785, 0.135, 0.150, 0.860);
      }

      body.fadein {
        background: rgba(0, 0, 0, 0.65);
      }

      #container {
        background: white;
        padding: 12px 18px 40px 18px;
      }

      @media only screen and (min-width:500px) {
        #container {
          border-radius: 5px;
          padding: 30px 40px;
        }
      }

      @media only screen and (min-width:992px) {
        #container {
          margin-top: 140px;
        }
      }

      .fade {
        opacity: 0;
        -webkit-transition: opacity 0.15s linear;
        -o-transition: opacity 0.15s linear;
        transition: opacity 0.15s linear;
      }
      .fade.in {
        opacity: 1;
      }
      .modal {
        overflow-x: hidden;
        overflow-y: auto;
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 1050;
        -webkit-overflow-scrolling: touch;
        outline: 0;
      }

      .modal.fade .modal-dialog {
        -webkit-transform: translate(0, -25%);
        -ms-transform: translate(0, -25%);
        -o-transform: translate(0, -25%);
        transform: translate(0, -25%);
        -webkit-transition: -webkit-transform 0.3s ease-out;
        -o-transition: -o-transform 0.3s ease-out;
        transition: transform 0.3s ease-out;
      }
      .modal.in .modal-dialog {
        -webkit-transform: translate(0, 0);
        -ms-transform: translate(0, 0);
        -o-transform: translate(0, 0);
        transform: translate(0, 0);
      }
      .modal-open .modal {
        overflow-x: hidden;
        overflow-y: auto;
      }
      .modal-dialog {
        position: relative;
        width: auto;
        margin: 10px;
      }
      .modal-content {
        position: relative;
        background-color: #fff;
        border: 1px solid #999;
        border: 1px solid rgba(0, 0, 0, 0.2);
        border-radius: 6px;
        -webkit-background-clip: padding-box;
                background-clip: padding-box;
        outline: 0;
      }
      .modal-backdrop {
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 1040;
        background-color: #000;
      }
      .modal-backdrop.fade {
        opacity: 0;
        filter: alpha(opacity=0);
      }
      .modal-backdrop.in {
        opacity: 0.5;
        filter: alpha(opacity=50);
      }
      .modal-title {
        margin: 0;
        line-height: 1.42857143;
      }
      .modal-body {
        position: relative;
        padding: 15px;
      }
      @media (min-width: 768px) {
        .modal-dialog {
          width: 600px;
          margin: 30px auto;
        }
        .modal-sm {
          width: 300px;
        }
      }
      @media (min-width: 992px) {
        .modal-lg {
          width: 900px;
        }
      }
      .clearfix:before,
      .clearfix:after {
        content: " ";
        display: table;
      }
      .clearfix:after {
        clear: both;
      }


      /* only the stuff we need added here */

      h3 {
       font-size: 24px;
      }
      h4 {
        font-size: 18px;
        margin-top: 10px;
        margin-bottom: 10px;
      }
      h3, h4 {
        font-family: inherit;
        font-weight: 500;
        line-height: 1.1;
        color: inherit;
      }

      .form-control {
        display: block;
        width: 100%;
        height: 34px;
        padding: 6px 12px;
        font-size: 14px;
        line-height: 1.42857143;
        color: #555;
        background-color: #fff;
        background-image: none;
        border: 1px solid #ccc;
        border-radius: 5px;
        -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
        -moz-box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
        box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
        -webkit-transition: border-color ease-in-out .15s,-webkit-box-shadow ease-in-out .15s;
        -o-transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
        transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
      }

      .input-lg {
        font-size: 15px;
        height: 46px;
        padding: 10px 16px;
        line-height: 1.3333333;
      }

      @media screen and (-webkit-min-device-pixel-ratio:0) {
        select:focus,
        textarea:focus,
        input:focus {
          font-size: 16px;
          background: #eee;
        }
      }

      .submit-wrap {
        margin-top: 20px;
      }

      .form-group {
        margin-bottom: 15px;
      }

      .qty-label {
       width: 65%; 
       float: left; 
       text-align: right;
       padding-right: 20px
      }
      .qty-input-wrap {
        width: 35%; 
        float: left;
      }
      .qty-wrap {
        overflow: hidden;
      }
      .qty-wrap input {
        text-align: right;
      }

      .btn {
        display: inline-block;
        padding: 8px 12px;
        margin-bottom: 0;
        font-size: 14px;
        font-weight: bold;
        line-height: 1.42857143;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        -ms-touch-action: manipulation;
        touch-action: manipulation;
        cursor: pointer;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        background-image: none;
        border: 1px solid transparent;
        border-radius: 3px;
      }

      .btn-success {
        width: 100%;
        color: #fff;
        background-color: #5cb85c;
        border-color: #4cae4c;
      }

      .btn-lg {
        line-height: 24px;
        font-size: 15px;
        padding: 14px;
        line-height: 1.3333333;
      }

      .close {
        -webkit-appearance: none;
        padding: 0;
        cursor: pointer;
        background: 0 0;
        border: 0;
        float: right;
        font-size: 21px;
        font-weight: 700;
        line-height: 1;
        color: #000;
        text-shadow: 0 1px 0 #fff;
      }


      .alert {
        padding: 6px 11px;
        font-size: 13px;
        margin: 15px 0;
        border: 1px solid transparent;
        border-radius: 4px;
      }
      .alert-success {
        color: #3c763d;
        background-color: #dff0d8;
        border-color: #d6e9c6;
      }

      .alert-success a { 
        color: #244825;
      }

      .alert-danger {
        color: #a94442;
        background-color: #f2dede;
        border-color: #ebccd1;
      }

      .pull-right {
        float: right;
      }
      .text-right {
        text-align: right;
      }

      .modal-body { 
        padding: 22px 40px;
        font-size: 13px;
        line-height: 180%;
      }
      .modal-body h3:first-child {
        margin-top: 0;
      }
      .modal-title {
        margin: 0;
        font-size: 22px;
      }
      .modal-content .close {
        font-size: 30px;
      }
      .modal-backdrop.in {
        filter: alpha(opacity=65);
        opacity: .65;
      }
      .small-print {
        opacity: 0.835;
        font-size: 13px;
        line-height: 150%;
      }
      .small-print a {
        color: inherit;
        text-decoration: underline;
      }
      .product-name {
        margin-bottom: 20px;
      }

      .accepts_marketing {
        opacity: 0.835;
        font-size: 13px;
      }
      .accepts_marketing input {
        margin-right: 10px;
      }

      @media only screen and (max-width:786px) {
        .modal-body { 
          padding: 20px 30px;
        }
      }

      @media only screen and (max-width:500px) {
        .modal-dialog {
          margin: 0;
        }
        .modal-content { 
          border-radius: 0
        }
      }

      @media only screen and (min-width:500px) {
        #BISModal {
          max-width: 460px;
          margin: auto;
        }
      }

      .ie8 #BISModal {
        width: 100%;
        max-width: 460px;
        margin: auto;
        border: 1px solid #999;
      }

      select.default_variant {
        display: none;
      }

      .ie8 .modal-dialog {
        width: 460px !important;
        margin: 10px auto;
      }

      .completed_message {
        display: none;
      }
      .complete .completed_message {
        display: block;
      }

      #BISModal.in {
        position: relative;
        z-index: 1050;
        height: 100%;
        overflow: hidden;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
      }
        #container { background: #FFFFFF; } body { color: #333333; } body.fadein { background: rgba( 0,0,0, 0.65); } .btn { color: #FFFFFF; background-color: #5CB85C; border-color: #5CB85C; } .close { color: #CCCCCC; } .alert-danger { border-color: #F2DEDE; background-color: #F2DEDE; color: #A94442; } .alert-success { background-color: #DFF0D8; border-color: #DFF0D8; color: #3C763D; } .alert-success a { color: #3C763D; }  
    </style>  

</head> 
  <body class="action-close" data-new-gr-c-s-check-loaded="14.996.0" data-gr-ext-installed="">
    <div id="BISModal" class="">
        <div class="">
            <div class="  complete" id="container">
                <div class=""><button id="close_me" type="button" class="close action-close" data-dismiss="modal">×</button>
                    <h3 class="modal-title">EMAIL WHEN AVAILABLE</h3>
                    <p>Register your email address below to receive an email as soon as this becomes available again.</p>
                    <hr>
                  <h4 class="product-name">{{product.title}}</h4>
<!--                   <h6>[Default Title]</h6> -->
                    <form action="#" class="form-horizontal clearfix">
                        <div class="form-group">
                            <div class="col-sm-12"> 
                              <input type="email" id="email_address" placeholder="Email address" class="form-control input-lg" value=""/> 
                          </div>
                        </div>
                        <div class="control-group clearfix submit-wrap"> 
                          <button id="email_me" type="button" class="btn btn-success btn-lg col-xs-12"> Email me when available </button> 
                        </div>
                        <div id="message_holder"></div>
                        <div style="display:none;" class="hide completed_message alert alert-error" id="error_text"> Your notification has not been set, please check email and try again. <a href="#" class="action-close" id="close_me_a">Close</a> </div>
                        <div style="display:none;" class="hide completed_message alert alert-success" id="success_text"> Your notification has been registered. <a href="#" class="action-close" id="close_me">Close</a> </div>
                    </form>
                    <p class="small-print">We'll notify you when this product is in stock. We don't share your address with anybody else.</p>
                </div>
            </div>
        </div>
    </div>
    <script src="//code.jquery.com/jquery-3.1.0.min.js" type="text/javascript"></script>
    <script>
      (function(){
        window.PlobalBridge = {
            openURL:function(url,title,open_links_in){
                try {
                    if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.openURL){
                        window.webkit.messageHandlers.openURL.postMessage({'type':open_links_in,'url':url,'title':title});
                    }
                } catch(e) {
                    console.log("ios error openURL");
                    console.log(e);
                }
                try {
                    if(window && window.AndroidBridge && window.AndroidBridge.openURL){
                        window.AndroidBridge.openURL(open_links_in,url,title);
                    }
                } catch(e) {
                    console.log("android error openURL");
                    console.log(e);
                }
                if(!window.webkit && !window.AndroidBridge){
                    window.location.assign(url);
                }
            },
            openURLNew:function(data){
                try {
                    if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.openURLNew){
                        window.webkit.messageHandlers.openURLNew.postMessage(data);
                    }
                } catch(e) {
                    console.log("ios error openURLNew");
                    console.log(e);
                }
                try {
                    if(window && window.AndroidBridge && window.AndroidBridge.openURLNew){
                        window.AndroidBridge.openURLNew(JSON.stringify(data));
                    }
                } catch(e) {
                    console.log("android error openURLNew");
                    console.log(e);
                }
                if(!window.webkit && !window.AndroidBridge){
                    window.location.assign(data.url);
                }
                this.openURL(data.url,data.title,data.type);
            },
            openURLNewIOS:function(data){
                try {
                    if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.openURLNew){
                        window.webkit.messageHandlers.openURLNew.postMessage(data);
                    }
                } catch(e) {
                    console.log("ios error openURLNew");
                    console.log(e);
                }

                if(!window.webkit){
                    window.location.assign(data.url);
                }
                this.openURL(data.url,data.title,data.type);
            },
            redirectTo:function(app_feature_id){
                try {
                    if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.redirectTo){
                        window.webkit.messageHandlers.redirectTo.postMessage({'app_feature_id':app_feature_id});
                    }
                } catch(e) {
                    console.log("ios error redirectTo");
                    console.log(e);
                }
                try {
                    if(window && window.AndroidBridge && window.AndroidBridge.redirectTo){
                        window.AndroidBridge.redirectTo(JSON.stringify({'app_feature_id':app_feature_id}));
                    }
                } catch(e) {
                    console.log("android error redirectTo");
                    console.log(e);
                }
                if(!window.webkit && !window.AndroidBridge){
                    alert(app_feature_id);
                }
            },
            adjustWebViewHeight:function(container){
                if(!container){
                    var height = document.body.clientHeight;
                }else if(container == 0){
                    var height = 0;
                }
                else if(container == -25){
                    var height = -25;
                }else{
                    var height = document.getElementById(container).clientHeight;
                }
                height +=25;
                try {
                    if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.adjustWebViewHeight){
                        window.webkit.messageHandlers.adjustWebViewHeight.postMessage(height);
                    }
                } catch(e) {
                    console.log("ios error adjustWebViewHeight");
                    console.log(e);
                }
                if(!window.webkit && !window.AndroidBridge){
                    console.log('adjustWebViewHeight  '+height);
                }
            },
            addLineItemProperty:function(obj){
                try {
                    if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.addLineItemProperty){
                        window.webkit.messageHandlers.addLineItemProperty.postMessage(obj);
                    }
                } catch(e) {
                    console.log("ios error addLineItemProperty");
                    console.log(e);
                }
                try {
                    if(window && window.AndroidBridge && window.AndroidBridge.addLineItemProperty){
                        window.AndroidBridge.addLineItemProperty(JSON.stringify(obj));
                    }
                } catch(e) {
                    console.log("android error addLineItemProperty");
                    console.log(e);
                }
                // if(!window.webkit && !window.AndroidBridge){
                //     console.log('addLineItemProperty  '+JSON.stringify(obj));
                // }
                console.log('addLineItemProperty  '+JSON.stringify(obj));
            },
            removeLineItemProperty:function(key){
                try {
                    if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.removeLineItemProperty){
                        window.webkit.messageHandlers.removeLineItemProperty.postMessage(key);
                    }
                } catch(e) {
                    console.log("ios error addLineItemProperty");
                    console.log(e);
                }
                try {
                    if(window && window.AndroidBridge && window.AndroidBridge.removeLineItemProperty){
                        window.AndroidBridge.removeLineItemProperty(key);
                    }
                } catch(e) {
                    console.log("android error removeLineItemProperty");
                    console.log(e);
                }
                if(!window.webkit && !window.AndroidBridge){
                    console.log('removeLineItemProperty  '+key);
                }
            },
            setAddToCartBehaviour:function(obj){
                console.log("setAddToCartBehaviour");
                console.log(obj);
                try {
                    if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.setAddToCartBehaviour){
                        window.webkit.messageHandlers.setAddToCartBehaviour.postMessage(obj);
                    }
                } catch(e) {
                    console.log("ios error setAddToCartBehaviour");
                    console.log(e);
                }
                try {
                    if(window && window.AndroidBridge && window.AndroidBridge.setAddToCartBehaviour){
                        window.AndroidBridge.setAddToCartBehaviour(JSON.stringify(obj));
                    }
                } catch(e) {
                    console.log("android error setAddToCartBehaviour");
                    console.log(e);
                }
                if(!window.webkit && !window.AndroidBridge){
                    console.log('setAddToCartBehaviour  '+JSON.stringify(obj));
                }
            },
            setBuyNowBehaviour:function(obj){
                try {
                    if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.setBuyNowBehaviour){
                        window.webkit.messageHandlers.setBuyNowBehaviour.postMessage(obj);
                    }
                } catch(e) {
                    console.log("ios error setBuyNowBehaviour");
                    console.log(e);
                }
                try {
                    if(window && window.AndroidBridge && window.AndroidBridge.setBuyNowBehaviour){
                        window.AndroidBridge.setBuyNowBehaviour(JSON.stringify(obj));
                    }
                } catch(e) {
                    console.log("android error setBuyNowBehaviour");
                    console.log(e);
                }
                if(!window.webkit && !window.AndroidBridge){
                    console.log('setBuyNowBehaviour  '+JSON.stringify(obj));
                }
            },
            changeDisplayInfo:function(data){
                try {
                    if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.changeDisplayInfo){
                        window.webkit.messageHandlers.changeDisplayInfo.postMessage(data);
                    }
                } catch(e) {
                    console.log("ios error changeDisplayInfo");
                    console.log(e);
                }
                try {
                    if(window && window.AndroidBridge && window.AndroidBridge.changeDisplayInfo){
                        window.AndroidBridge.changeDisplayInfo(JSON.stringify(data));
                    }
                } catch(e) {
                    console.log("android error changeDisplayInfo");
                    console.log(e);
                }
                if(!window.webkit && !window.AndroidBridge){
                    console.log('changeDisplayInfo '+JSON.stringify(data));
                }
            },
            addCustomerProperty:function(key,value){
                console.log(key,value);
                try {
                    if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.addCustomerProperty){
                        window.webkit.messageHandlers.addCustomerProperty.postMessage({'key':key,'value':value});
                    }
                } catch(e) {
                    console.log("ios error addCustomerProperty");
                    console.log(e);
                }
                try {
                    if(window && window.AndroidBridge && window.AndroidBridge.addCustomerProperty){
                        window.AndroidBridge.addCustomerProperty(key,value);
                    }
                } catch(e) {
                    console.log("android error addCustomerProperty");
                    console.log(e);
                }
                if(!window.webkit && !window.AndroidBridge){
                    console.log('addCustomerProperty '+key+' '+ value);
                }
            },
            addToCart:function(variant){
                try {
                    if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.addToCart){
                        window.webkit.messageHandlers.addToCart.postMessage(variant);
                    }
                } catch(e) {
                    console.log("ios error addToCart");
                    console.log(e);
                }
                try {
                    if(window && window.AndroidBridge && window.AndroidBridge.addToCart){
                        window.AndroidBridge.addToCart(JSON.stringify(variant));
                    }
                } catch(e) {
                    console.log("android error addToCart");
                    console.log(e);
                }
                if(!window.webkit && !window.AndroidBridge){
                    console.log('addToCart '+JSON.stringify(variant));
                }
            },
            changeAddToCartBehaviour:function(rules){

            },
            dataReceiver:function(data){
                console.log('dataReceiver');
            },
            requestData:function(data){
                console.log('requestData');
            },
            shareData:function(data){
                try {
                    if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.shareData){
                        window.webkit.messageHandlers.shareData.postMessage({'data':data});
                    }
                } catch(e) {
                    console.log("ios error shareData");
                    console.log(e);
                }
                try {
                    if(window && window.AndroidBridge && window.AndroidBridge.shareData){
                        window.AndroidBridge.shareData(data);
                    }
                } catch(e) {
                    console.log("android error shareData");
                    console.log(e);
                }
                if(!window.webkit && !window.AndroidBridge){
                    console.log('shareData '+data);
                }
            },
            openFileChooser:function(){
                console.log('openFileChooser');
                try {
                    if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.openFileChooser){
                        window.webkit.messageHandlers.openFileChooser.postMessage("");
                    }
                } catch(e) {
                    console.log("ios error openFileChooser");
                    console.log(e);
                }
                try {
                    if(window && window.AndroidBridge && window.AndroidBridge.openFileChooser){
                        window.AndroidBridge.openFileChooser();
                    }
                } catch(e) {
                    console.log("android error openFileChooser");
                    console.log(e);
                }
                if(!window.webkit && !window.AndroidBridge){
                    console.log('openFileChooser');
                }
            },
            callBridgeFunction:function(functionName, paramsIos, paramAndroid){
                try {
                    if(window && window.AndroidBridge){
                        if(paramAndroid){
                            window.AndroidBridge[functionName](paramAndroid);
                        }
                        else{
                            window.AndroidBridge[functionName]();
                        }

                    }
                } catch(e) {
                    console.log("I got clicked android error");
                    console.log(e);
                }

                try{
                    if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers[functionName]){
                        if(paramsIos){
                            window.webkit.messageHandlers[functionName].postMessage(paramsIos);
                        }
                        else{
                            window.webkit.messageHandlers[functionName].postMessage();
                        }
                    }
                }
                catch(e){
                    console.log("I got clicked ios error");
                    console.log(e);
                }
            },
            openProductDetail:function(product_id, title) {
                var jsonToSend ={
                    product_id: product_id.toString(),
                    title: title
                };

                this.callBridgeFunction('openProductDetails', jsonToSend, JSON.stringify(jsonToSend));
            },
            openCollection: function (collection_id, title) {
                var jsonToSend ={
                    collection_id: collection_id.toString(),
                    title: title
                };

                this.callBridgeFunction('openCollection', jsonToSend, JSON.stringify(jsonToSend));
            },
            addToCart:function(variantID){
                event.stopPropagation();
                this.callBridgeFunction('addToCart', variantID.toString(), variantID.toString());
            },
            openCart:function(event) {
                event.stopPropagation();
                this.callBridgeFunction('openCart');
            },
            operate:{
                '+': function(a, b) { return a + b },
                '<': function(a, b) { return a < b },
                '=': function(a, b) { return a < b },
                'equal': function(a, b) { return a == b; },
                'not_equal': function(a, b) { return a != b; }
            },
            isJson:function(str) {
                try {
                    JSON.parse(str);
                } catch (e) {
                    return false;
                }
                return true;
            },
            validateData:function(obj){
                try {
                    if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.validateData){
                        window.webkit.messageHandlers.validateData.postMessage(obj);
                    }
                } catch(e) {
                    console.log("ios error validateData");
                    console.log(e);
                }
                try {
                    if(window && window.AndroidBridge && window.AndroidBridge.validateData){
                        window.AndroidBridge.validateData(JSON.stringify(obj));
                    }
                } catch(e) {
                    console.log("android error validateData");
                    console.log(e);
                }
                // if(!window.webkit && !window.AndroidBridge){
                //
                // }
                console.log('validateData '+JSON.stringify(obj));
            },
            triggerEvent:function(el, event) {
                if ("createEvent" in document) {
                    var evt = document.createEvent("HTMLEvents");
                    evt.initEvent(event, false, true);
                    el.dispatchEvent(evt);
                }
                else
                    el.fireEvent(event);
            }
        };
    })();
    </script>
  <script>
      var urlString = window.location.href;
      var paramString = urlString.split('?')[1];
      var queryString = new URLSearchParams(paramString);
      var q = {};
      for (let pair of queryString.entries()) {
         console.log("Key is: " + pair[0]);
         console.log("Value is: " + pair[1]);
        q[pair[0]] = pair[1];
      }
      
      $("#success_text").hide();
        $("#error_text").hide();
      $("#email_me").on("click",function(){
        $("#success_text").hide();
        $("#error_text").hide();
        var emailId = $("#email_address").val();
        var urlToSend  = "https://app.backinstock.org/stock_notification/create.json?kyte-baby-co.myshopify.com&notification[shop]=kyte-baby-co.myshopify.com&notification[channel]=email&notification[email]="+emailId+"&notification[product_no]="+q["product_id"]+"&notification[quantity_required]=1&notification[accepts_marketing]=false&notification[customer_utc_offset]=-19800&variant[variant_no]="+q["variant_id"];
        urlToSend  = "https://flztijqtda.execute-api.us-west-1.amazonaws.com/prod?kyte-baby-co.myshopify.com&notification[shop]=kyte-baby-co.myshopify.com&notification[channel]=email&notification[email]="+emailId+"&notification[product_no]="+q["product_id"]+"&notification[quantity_required]=1&notification[accepts_marketing]=false&notification[customer_utc_offset]=-19800&variant[variant_no]="+q["variant_id"];
        $.get(urlToSend, function(data, status){
          console.log(data);
          var messageTextCloseButton = '<a href="#" class="action-close" id="close_me_a" onclick="return close()">Close</a>';
          if(data.hasOwnProperty("errors")) {
            var d = "Your notification has not been set, please check email and try again.";
            if(data.errors.hasOwnProperty("base") && data.errors.base.hasOwnProperty(0)) {
              $("#error_text").html(d+" "+data.errors.base[0]+ " " + messageTextCloseButton);
            }
            $("#error_text").show();
          } else {
            $("#success_text").show();
            setInterval(function(){
                  window.PlobalBridge.redirectTo("previousmenu");
            }, 3000);
          }
          
        });
      });
      function close(){
        console.log("close");
        window.PlobalBridge.redirectTo("previousmenu");
      }
      $("#close_me").on("click",function(){
    window.PlobalBridge.redirectTo("previousmenu");
      });
      $("#close_me_a").on("click",function(){
    window.PlobalBridge.redirectTo("previousmenu");
      });
      $(document).keypress(
        function(event){
          if (event.which == '13') {
            event.preventDefault();
          }
      });
      $('body').on('click', 'a#close_me_a', function() {
      // do something
        window.PlobalBridge.redirectTo("previousmenu");
  });
    </script>
</body>
</html>