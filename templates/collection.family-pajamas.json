/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "collection-banner": {
      "type": "collection-banner-split",
      "settings": {
        "usp_group": "",
        "usps_list": [],
        "show_collection_image": false,
        "collection_sub_title": "{{ collection.metafields.header.subtitle.value }}",
        "show_collection_title": true,
        "show_collection_description": true,
        "use_metafield_description": true,
        "background_type": "boxed",
        "text_width": "medium",
        "text_position": "middle_left",
        "section_height": "clamp",
        "image_text_color": "rgba(0,0,0,0)",
        "text_background": "#fbf6f3",
        "image_overlay_color": "#fbf6f3",
        "image_overlay_opacity": 0,
        "quick_links": "",
        "quick_links_hide_title": false,
        "quick_links_menu_handle": "{{ collection.metafields.navigation.menu_handle.value }}",
        "quick_links_title": "{{ collection.metafields.navigation.menu_title.value }}"
      }
    },
    "custom_multi_column_wFRJ8E": {
      "type": "custom-multi-column",
      "blocks": {
        "item_caekXU": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/5_faf911f1-7d22-4a99-ac0e-677966979d0e.png",
            "image_width": 100,
            "title": "Vintage Santa",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/vintage-santa",
            "text_alignment": "",
            "vertical_alignment": "start",
            "overlay_opacity": 0,
            "overlay_color": "rgba(0,0,0,0)",
            "image_border": "rgba(0,0,0,0)"
          }
        },
        "item_8ELGdx": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/1_1b12c9db-22ea-48b4-9732-21fef8d1c587.png",
            "image_width": 100,
            "title": "Twinkle Tree",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/twinkle-tree",
            "text_alignment": "",
            "vertical_alignment": "start",
            "overlay_opacity": 0,
            "overlay_color": "rgba(0,0,0,0)",
            "image_border": "rgba(0,0,0,0)"
          }
        },
        "item_ihnz3q": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/3_5c692b14-23b0-4ac1-98f6-0e1c51801312.png",
            "image_width": 100,
            "title": "Candy Cane",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/candy-cane",
            "text_alignment": "",
            "vertical_alignment": "start",
            "overlay_opacity": 0,
            "overlay_color": "rgba(0,0,0,0)",
            "image_border": "rgba(0,0,0,0)"
          }
        },
        "item_t6WQfM": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/4_bbef927c-64a7-46cc-a5f7-b2604f50bda2.png",
            "image_width": 100,
            "title": "Merry & Bright",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/merry-bright",
            "text_alignment": "",
            "vertical_alignment": "start",
            "overlay_opacity": 0,
            "overlay_color": "rgba(0,0,0,0)",
            "image_border": "rgba(0,0,0,0)"
          }
        },
        "item_qda3Fy": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/o_1_1_1.png",
            "image_width": 100,
            "title": "Silly Goose",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/silly-goose",
            "text_alignment": "",
            "vertical_alignment": "start",
            "overlay_opacity": 0,
            "overlay_color": "rgba(0,0,0,0)",
            "image_border": "rgba(0,0,0,0)"
          }
        },
        "item_Lra9Gn": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/4_bbc806db-0967-467c-b371-64e8b23398df.png",
            "image_width": 100,
            "title": "Gingham Fir",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/gingham-fir",
            "text_alignment": "",
            "vertical_alignment": "start",
            "overlay_opacity": 0,
            "overlay_color": "rgba(0,0,0,0)",
            "image_border": "rgba(0,0,0,0)"
          }
        },
        "item_UMCnh9": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/o_3.png",
            "image_width": 100,
            "title": "Teddy Bear",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/teddy-bear",
            "text_alignment": "",
            "vertical_alignment": "start",
            "overlay_opacity": 0,
            "overlay_color": "rgba(0,0,0,0)",
            "image_border": "rgba(0,0,0,0)"
          }
        },
        "item_ThPdHJ": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/o_4.png",
            "image_width": 100,
            "title": "Bow",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/bow",
            "text_alignment": "",
            "vertical_alignment": "start",
            "overlay_opacity": 0,
            "overlay_color": "rgba(0,0,0,0)",
            "image_border": "rgba(0,0,0,0)"
          }
        },
        "item_Fh3Ywi": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/2_b3da426a-6529-4872-9ebc-a43a389d0d3b.png",
            "image_width": 100,
            "title": "Santa Sleigh",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/santa-sleigh",
            "text_alignment": "",
            "vertical_alignment": "start",
            "overlay_opacity": 0,
            "overlay_color": "rgba(0,0,0,0)",
            "image_border": "rgba(0,0,0,0)"
          }
        },
        "item_7gQNxw": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/3_d82f6f93-bdb5-43d5-9001-78a043bfe42d.png",
            "image_width": 100,
            "title": "Gingham Bisque",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/gingham-bisque",
            "text_alignment": "",
            "vertical_alignment": "start",
            "overlay_opacity": 0,
            "overlay_color": "rgba(0,0,0,0)",
            "image_border": "rgba(0,0,0,0)"
          }
        },
        "item_qejYtx": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/7_81c3d76e-05db-4ced-95bc-0a793d9ef972.png",
            "image_width": 100,
            "title": "Frosty Friends",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/frosty-friends",
            "text_alignment": "",
            "vertical_alignment": "start",
            "overlay_opacity": 0,
            "overlay_color": "rgba(0,0,0,0)",
            "image_border": "rgba(0,0,0,0)"
          }
        },
        "item_DEVfc8": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/6_feafa99f-beb6-45a5-9b2f-3ae76085592c.png",
            "image_width": 100,
            "title": "Polar Lights",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/polar-lights",
            "text_alignment": "",
            "vertical_alignment": "start",
            "overlay_opacity": 0,
            "overlay_color": "rgba(0,0,0,0)",
            "image_border": "rgba(0,0,0,0)"
          }
        }
      },
      "block_order": [
        "item_caekXU",
        "item_8ELGdx",
        "item_ihnz3q",
        "item_t6WQfM",
        "item_qda3Fy",
        "item_Lra9Gn",
        "item_UMCnh9",
        "item_ThPdHJ",
        "item_Fh3Ywi",
        "item_7gQNxw",
        "item_qejYtx",
        "item_DEVfc8"
      ],
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "",
        "subheading": "",
        "title": "",
        "heading_alignment": "center",
        "content": "",
        "content_style": "",
        "link_style": "link",
        "button_style": "",
        "reveal_on_scroll": true,
        "stack_items": false,
        "column_alignment": "center",
        "mobile_item_size": "small",
        "desktop_item_size": "small",
        "spacing": "normal",
        "block_content_style": "",
        "heading_style": "h5",
        "block_text_alignment": "center",
        "heading_over_image": false,
        "content_over_image": false,
        "button_over_image": false,
        "overlay_bottom_offset": 0,
        "background": "#ffffff",
        "subheading_color": "rgba(0,0,0,0)",
        "heading_color": "rgba(0,0,0,0)",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)",
        "block_heading_color": "rgba(0,0,0,0)",
        "block_subheading_color": "rgba(0,0,0,0)",
        "block_text_color": "rgba(0,0,0,0)",
        "link_color": "rgba(0,0,0,0)",
        "overlay_color": "rgba(0,0,0,0)",
        "overlay_opacity": 0
      }
    },
    "1664759269266d123b": {
      "type": "multi-column",
      "blocks": {
        "1664759268595ddfca-0": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Rectangle_230.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Jogger Sets",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/womens-jogger-set",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "1664759268595ddfca-1": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Rectangle_231.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Lounge Pants",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/women-s-lounge-pant",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "1664759268595ddfca-2": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Rectangle_232.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Lounge Robes",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/adult-lounge-robe",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "1664759268595ddfca-3": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Rectangle_233.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Pajama Sets",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/sets",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "b9790cba-ad6a-4f2d-b096-6caa89f8409c": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Rectangle_234.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Tank Sets",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/womens-tank-set",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        }
      },
      "block_order": [
        "1664759268595ddfca-0",
        "1664759268595ddfca-1",
        "1664759268595ddfca-2",
        "1664759268595ddfca-3",
        "b9790cba-ad6a-4f2d-b096-6caa89f8409c"
      ],
      "disabled": true,
      "settings": {
        "reveal_on_scroll": true,
        "stack_items": false,
        "subheading": "",
        "title": "",
        "content": "",
        "column_alignment": "center",
        "mobile_item_size": "small",
        "desktop_item_size": "small",
        "spacing": "tight",
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)"
      }
    },
    "main": {
      "type": "main-collection",
      "blocks": {
        "e571a9f5-2677-4d14-9017-728678ca99f2": {
          "type": "media_banner",
          "settings": {
            "column_span": 1,
            "position": 7,
            "video_filename": "",
            "video_url": "",
            "image": "shopify://shop_images/Ski_FF-Lifestyle.png",
            "image_position": "",
            "link_url": "shopify://pages/frequent-flyer-rewards",
            "overlay_subheading": "",
            "overlay_title": "",
            "overlay_link_text": "",
            "overlay_text": "",
            "overlay_color": "rgba(0,0,0,.2)",
            "text_color": "#ffffff"
          }
        },
        "0ae5d61c-aa10-40fc-90fd-977e67cdf322": {
          "type": "media_banner",
          "settings": {
            "column_span": 3,
            "position": 12,
            "video_filename": "Holiday Web Video.MP4",
            "video_url": "",
            "image": "shopify://shop_images/Twinkle_Tree_Vintage_Santa_Merry_Bright_Wide_Shot_1.jpg",
            "image_position": "",
            "link_url": "shopify://pages/holiday-market",
            "overlay_subheading": "Merry & Matching",
            "overlay_title": "The Holiday Shoppe",
            "overlay_link_text": "Shop Now",
            "overlay_text": "",
            "overlay_color": "rgba(0,0,0,.2)",
            "text_color": "#ffffff"
          }
        }
      },
      "block_order": [
        "e571a9f5-2677-4d14-9017-728678ca99f2",
        "0ae5d61c-aa10-40fc-90fd-977e67cdf322"
      ],
      "settings": {
        "aspect_ratio": "",
        "tier_display": "badge",
        "show_additional_text_fields": false,
        "show_sort_by": true,
        "products_per_page": 50,
        "mobile_products_per_row": "2",
        "desktop_products_per_row": 3,
        "show_filters": true,
        "show_filter_group_name": false,
        "show_color_swatch": true,
        "open_first_filter_group": true,
        "filter_position": "always_visible",
        "promotion_position": "top",
        "promotion_height": "small"
      }
    },
    "custom_liquid_NyHHFN": {
      "type": "custom-liquid",
      "custom_css": [
        "h4 {text-align: center;}",
        "p {margin-left: auto; margin-right: auto; max-width: 980px; text-align: center;}"
      ],
      "settings": {
        "subheading": "",
        "title": "",
        "liquid": "<h4>{{ collection.title }}</h4>\n<div class=\"rte\">{{ collection.description }}</div>",
        "add_vertical_spacing": true,
        "background_type": "boxed",
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)"
      }
    },
    "collection_list_Jb7996": {
      "type": "collection-list",
      "blocks": {
        "collection_raMpTk": {
          "type": "collection",
          "settings": {
            "collection": "0-5-tog",
            "subheading": "",
            "title": "",
            "link_text": "",
            "link_url": "",
            "text_color": "rgba(0,0,0,0)",
            "overlay_color": "#000000",
            "overlay_opacity": 30
          }
        },
        "collection_hXiRWL": {
          "type": "collection",
          "settings": {
            "collection": "2-5-tog-1",
            "subheading": "",
            "title": "",
            "link_text": "",
            "link_url": "",
            "text_color": "rgba(0,0,0,0)",
            "overlay_color": "#000000",
            "overlay_opacity": 30
          }
        },
        "collection_7Nenb3": {
          "type": "collection",
          "settings": {
            "collection": "",
            "subheading": "",
            "title": "",
            "link_text": "",
            "link_url": "",
            "text_color": "rgba(0,0,0,0)",
            "overlay_color": "#000000",
            "overlay_opacity": 30
          }
        },
        "collection_3L4FFM": {
          "type": "collection",
          "settings": {
            "collection": "",
            "subheading": "",
            "title": "",
            "link_text": "",
            "link_url": "",
            "text_color": "rgba(0,0,0,0)",
            "overlay_color": "#000000",
            "overlay_opacity": 30
          }
        }
      },
      "block_order": [
        "collection_raMpTk",
        "collection_hXiRWL",
        "collection_7Nenb3",
        "collection_3L4FFM"
      ],
      "disabled": true,
      "name": "Collection list",
      "settings": {
        "reveal_on_scroll": false,
        "layout": "carousel",
        "subheading": "",
        "title": "Collection list",
        "background": "#fbf6f3",
        "text_color": "rgba(0,0,0,0)"
      }
    },
    "rich_text_he4hw6": {
      "type": "rich-text",
      "disabled": true,
      "name": "Rich text",
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "",
        "subheading": "{{ collection.title }}",
        "title": "",
        "content": "{{ collection.description }}",
        "button_text": "",
        "button_link": "",
        "button_style": "",
        "button_icon": "",
        "background_type": "full_width",
        "text_width": "large",
        "text_position": "center",
        "text_alignment": "center",
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)"
      }
    },
    "faq_frkJFM": {
      "type": "faq",
      "blocks": {
        "category_T6zjcL": {
          "type": "category",
          "settings": {
            "title": "FAQ"
          }
        },
        "question_KBLNnN": {
          "type": "question",
          "settings": {
            "title": "Do you ship overseas?",
            "answer": "<p>Yes, we ship all over the world. Shipping costs will apply, and will be added at checkout. We run discounts and promotions all year, so stay tuned for exclusive deals.</p>"
          }
        },
        "question_DwQt4r": {
          "type": "question",
          "settings": {
            "title": "How long will it take to get my order?",
            "answer": "<p>It depends on where you are. Orders processed here will take 5-7 business days to arrive. Overseas deliveries can take anywhere from 7-16 days. Delivery details will be provided in your confirmation email.</p>"
          }
        },
        "category_WagKLJ": {
          "type": "category",
          "disabled": true,
          "settings": {
            "title": "Other"
          }
        },
        "question_cDpLbC": {
          "type": "question",
          "settings": {
            "title": "Any question?",
            "answer": "<p>You can contact us through our contact page! We will be happy to assist you.</p>"
          }
        }
      },
      "block_order": [
        "category_T6zjcL",
        "question_KBLNnN",
        "question_DwQt4r",
        "category_WagKLJ",
        "question_cDpLbC"
      ],
      "disabled": true,
      "name": "FAQ",
      "settings": {
        "show_navigation": false,
        "title": "",
        "content": "",
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)"
      }
    }
  },
  "order": [
    "collection-banner",
    "custom_multi_column_wFRJ8E",
    "1664759269266d123b",
    "main",
    "custom_liquid_NyHHFN",
    "collection_list_Jb7996",
    "rich_text_he4hw6",
    "faq_frkJFM"
  ]
}
