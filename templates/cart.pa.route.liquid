{% layout none %}
<style>
   .kl-private-reset-css-Xuajs1,.needsclick {display:none!important;}
</style>
<html>
   <head>
<!-- Start of Shoplift scripts -->
{% render 'shoplift' %}
<!-- End of Shoplift scripts -->

      <title>Route STYLES BY SOMA 5</title>
      <meta charset="utf-8" />
      <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=0">
      <style>
         -webkit-touch-callout: none; /* iOS Safari */
         -webkit-user-select: none; /* Safari */
         -khtml-user-select: none; /* Konqueror HTML */
         -moz-user-select: none; /* Old versions of Firefox */
         -ms-user-select: none; /* Internet Explorer/Edge */
         user-select: none;
         *{
         outline: none;
         }
         body{
         /*background-color: #ccc;*/
         }
         .route{
         margin: 10px auto;
         width:95vw;
         text-align:center;
         font-size: 10px;
         }
         #route-checkbox{
         float:right;
         margin-top: 5%;
         }
         .route-info{
         border: 1px solid #5ec8db;
         display: flex;
         height: 55px;
         border-radius: 5px;
         }
         .route-info-text{
         max-width:75vw;
         float: left;
         display: inline-block;
         }
         .route-info-text p{
         width:82vw;
         }
         p{
         text-align:left;
         }
         p.disclaimer{
         clear:both;
         display:block;
         margin-top: -5px;
         }
         .switch {
         position: relative;
         display: inline-block;
         width: 60px;
         height: 24px;
         }
         .route-info-switch{
         min-width:15vw;
         position: fixed;
         right: 1;
         }
         .switch input { 
         float:right;
         opacity: 0;
         width: 0;
         height: 0;
         }
         .slider {
         position: absolute;
         cursor: pointer;
         top: 0;
         left: 0;
         right: 0;
         bottom: 0;
         background-color: #ccc;
         -webkit-transition: .4s;
         transition: .4s;
         }
         .slider:before {
         position: absolute;
         content: "";
         height: 26px;
         width: 26px;
         left: 4px;
         bottom: 4px;
         background-color: white;
         -webkit-transition: .4s;
         transition: .4s;
         }
         input:checked + .slider {
         background-color: #5ec8db;
         }
         input:focus + .slider {
         box-shadow: 0 0 1px #5ec8db;
         }
         input:checked + .slider:before {
         -webkit-transform: translateX(26px);
         -ms-transform: translateX(26px);
         transform: translateX(26px);
         }
         /* Rounded sliders */
         .slider.round {
         border-radius: 34px;
         }
         .slider.round:before {
         border-radius: 50%;
         }
         .switch{
         transform: scale(.5);
         }
         .js-focus-visible { overflow: hidden; }
      </style>
   </head>
   <body>
      <script>
         function emptyLogs(){
         	$("#logss").empty();
           PlobalBridge.adjustWebViewHeight('route');
         }
      </script>
      <div class="route" id="route" style="
         font-family: 'DIN Next', sans-serif";>
         <div class="">
            <div class="route-info">
               <div class="route-info-text">
                  <img src="https://cdn.routeapp.io/route-widget/images/RoutePlusGray.svg" width="50vw" style="float:left;">
                  <p>&nbsp;<b>Shipping Protection</b> from Damage, Loss &amp; Theft for <strong class="insAmount">$0</strong></p>
               </div>
               <label class="switch route-info-switch" for="route-checkbox">
               <input id="route-checkbox" name="enable_route_protection" type="checkbox" value="1" checked>
               <span class="slider round"></span>
               </label>
            </div>
         </div>
         <br/>
         <p class="disclaimer">*By deselecting package protection, SBS is not liable for lost, damaged, or stolen items.</p>
         <div id="logss">
         </div>
         <div style="display:none;"><button onCLick="emptyLogs()">empty log</button></div>
      </div>
      <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
      <script>
         (function(){
             window.PlobalBridge = {
             	addToCart:function(data){
                      	console.log("addToCart");
                     try {
                         if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.addToCart){
                             window.webkit.messageHandlers.addToCart.postMessage(data);
                         }
                     } catch(e) {
                         console.log("ios error addToCart");
                         console.log(e);
                     }
                     try {
                         if(window && window.AndroidBridge && window.AndroidBridge.addToCart){
                             window.AndroidBridge.addToCart(JSON.stringify(data));
                         }
                     } catch(e) {
                         console.log("android error addToCart");
                         console.log(e);
                     }
                	},
                	removeFromCart:function(data){
                      	console.log("removeFromCart");              
                     try {
                         if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.removeFromCart){
                             window.webkit.messageHandlers.removeFromCart.postMessage(data);
                         }
                     } catch(e) {
                         console.log("ios error removeFromCart");
                         console.log(e);
                     }
                     try {
                         if(window && window.AndroidBridge && window.AndroidBridge.removeFromCart){
                             window.AndroidBridge.removeFromCart(JSON.stringify(data));
                         }
                     } catch(e) {
                         console.log("android error removeFromCart");
                         console.log(e);
                     }
                	},
                	adjustWebViewHeight:function(container){
                     if(!container){
                         var height = document.body.clientHeight;
                     }else if(container == 0){
                         var height = 0;
                     }
                     else if(container == -25){
                         var height = -25;
                     }else{
                         var height = document.getElementById(container).clientHeight;
                     }
                     height +=25;
                     try {
                         if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.adjustWebViewHeight){
                             window.webkit.messageHandlers.adjustWebViewHeight.postMessage(height);
                         }
                     } catch(e) {
                         console.log("ios error adjustWebViewHeight");
                         console.log(e);
                     }
                     if(!window.webkit && !window.AndroidBridge){
                         console.log('adjustWebViewHeight  '+height);
                     }
                	},
                    callBridgeFunction:function(functionName, paramsIos, paramAndroid){
                        console.log(functionName);
                        console.log(paramsIos);
                        console.log(paramAndroid);
                        try {
                            if(window && window.AndroidBridge && window.AndroidBridge.hasOwnProperty(functionName)){
                                if(paramAndroid){
                                    window.AndroidBridge[functionName](paramAndroid);
                                }
                                else{
                                    window.AndroidBridge[functionName]();
                                }
                            }
                        } catch(e) {
                            console.log("I got clicked android error");
                            console.log(e);
                        }
                        try{
                            if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers[functionName]){
                                if(paramsIos){
                                    window.webkit.messageHandlers[functionName].postMessage(paramsIos);
                                }
                                else{
                                    window.webkit.messageHandlers[functionName].postMessage();
                                }
                            }
                        }
                        catch(e){
                            console.log("I got clicked ios error");
                            console.log(e);
                        }
                    }	
             }
         })();
      </script>
      <script>
         function plobalLog(datass){
           if(typeof  datass == "object") {datass=JSON.stringify(datass)}
           //$("#logss").append("<div>"+datass+"</div><br/><br/>");
         }
           function validatePlatform(param){
         return true;
         
               if(param.json_data.Platform === "ANDROID" && param.json_data['Version-No'] >= 8){
                   return true;
               }
               if(param.json_data.Platform === "IOS" && param.json_data['Version-No'] >= 1.5){
                   return true;
               }
               return false;
           }
         
           var quote = false;
           var variants = [];
           window.variants = [];  
           var routeMerchantId = "merch_Gv3oacgGW3APkEFYUVHw";
           var cart = false;
           var route_v=false;
           var existing = false;
           var variantPrices = [];
           var closestPrice = 0;
           PlobalBridge.adjustWebViewHeight('route');
           function setVariants() {
                $.ajax({
                  url: "https://kytebaby.com/products/routeins.js",
                  async: false,
                  success:function(res){
                    console.log("success");
                    console.log(res);
                    plobalLog("success setVariants");
                    //plobalLog(res);
                    res = JSON.parse(res);
                    console.log(res);
                    variants = res.variants; 
                    window.variants=res.variants; 
                  }
              });
            }

          async function getQuotation(){
             if(!cart || !cart.subtotal) {
               return;
             }
             var postData = {"merchant_id": routeMerchantId , "cart":{}}
             console.log('getQuote subtotal ',quote, cart.subtotal);
             plobalLog("getQuote subtotal");
             plobalLog(JSON.stringify({"quote":quote, "subtotal":cart.subtotal}));
             try{
               postData.cart.covered = {"currency": "USD", "amount": String(cart.subtotal)};
                       
               var settings = {
                 "url": "https://api.route.com/v2/quotes",
                 "method": "POST",
                 "timeout": 0,
                 "headers": {
                   "authority": "api.route.com",
                   "accept": "application/json, text/plain, */*",
                   "accept-language": "en-US,en;q=0.9",
                   "content-type": "application/json;charset=UTF-8"
                 },
                 "data": JSON.stringify(postData)
               };
               
               $.ajax(settings).done(function (res) {
                 console.log(res);
                 quote = res;
                 quote.subtotal=cart.subtotal;
                 console.log('res getQuote subtotal ',quote, cart.subtotal);
                 plobalLog("res getQuote subtotal");
             		plobalLog(JSON.stringify({"quote":quote, "subtotal":cart.subtotal}));
                 if(!variants) {
                     setVariants();
                 }
                 if(!variants) {
                     variants = window.variants;
                 }
                 variantPrices = [];
                 variants.forEach(function(v){
         	variantPrices.push((v.price/100));
                 });
                 console.log("variantPrices="+variantPrices);
                 if(variantPrices[0]>=quote.premium.amount){
                 	closestPrice = variantPrices[0];
                 }
                 else if(variantPrices[variantPrices.length-1]<=quote.premium.amount) {
                   closestPrice = variantPrices[variantPrices.length-1];
                 } 
                 else {
                   for(i=1;i<variantPrices.length;i++){
                     if(
                     	quote.premium.amount >= variantPrices[i-1] &&
                       quote.premium.amount <= variantPrices[i]
                     ) {
                       closestPrice = variantPrices[i];
                       break;
                     }
                   }
                 }
                 console.log("closestPrice="+closestPrice);
                 variants.forEach(function(v){
                   if((v.price/100) == closestPrice){
                     route_v = v;
                   }
                 });
                 if(!route_v){
                   variants.forEach(function(v){
                     if(quote.premium.amount == (v.price/100)){
                       route_v = v;
                     }
                   });
                 }
                 if(!route_v){
                   var a_route_v = [];
                   var a_route_v_index = [];
                   variants.forEach(function(v){
                     if(quote.premium.amount >= (v.price/100)){
                       route_v = v;
                       a_route_v[v.price] = v;
                       a_route_v_index.push(v.price);
                     }
                   });
                   if(a_route_v_index.length) {
                     a_route_v_index = a_route_v_index.sort();
                     route_v = a_route_v[a_route_v_index[a_route_v_index.length-1]];
                   }
                 }
                 $('.insAmount').html('$'+(route_v?(route_v.price/100):0));
                 PlobalBridge.adjustWebViewHeight('route');
                 updateRouteItem();
               }, function(res){
                 quote = res;
                 quote.subtotal=cart.subtotal;
                 if(!variants) {
                     setVariants();
                 }
                 if(!variants) {
                     variants = window.variants;
                 }
                 variantPrices = [];
                 variants.forEach(function(v){
         	variantPrices.push((v.price/100));
                 });
                 console.log("variantPrices="+variantPrices);
                 if(variantPrices[0]>=quote.premium.amount){
                 	closestPrice = variantPrices[0];
                 }
                 else if(variantPrices[variantPrices.length-1]<=quote.premium.amount) {
                   closestPrice = variantPrices[variantPrices.length-1];
                 } 
                 else {
                   for(i=1;i<variantPrices.length;i++){
                     if(
                     	quote.premium.amount >= variantPrices[i-1] &&
                       quote.premium.amount <= variantPrices[i]
                     ) {
                       closestPrice = variantPrices[i];
                       break;
                     }
                   }
                 }
                 console.log("closestPrice="+closestPrice);
                 variants.forEach(function(v){
                   if((v.price/100) == closestPrice){
                     route_v = v;
                   }
                 });
                 if(!route_v){
                   variants.forEach(function(v){
                     if(quote.premium.amount == (v.price/100)){
                       route_v = v;
                     }
                   });
                 }
                 if(!route_v){
                   var a_route_v = [];
                   var a_route_v_index = [];
                   variants.forEach(function(v){
                     if(quote.premium.amount >= (v.price/100)){
                       route_v = v;
                     	a_route_v[v.price] = v;
                       a_route_v_index.push(v.price);
                     }
                   });
                   if(a_route_v_index.length) {
                     a_route_v_index = a_route_v_index.sort();
                     route_v = a_route_v[a_route_v_index[a_route_v_index.length-1]];
                   }
                 }
                 
                 $('.insAmount').html('$'+(route_v?(route_v.price/100):0));
                 PlobalBridge.adjustWebViewHeight('route');
                 updateRouteItem();
               });
             }catch(e){
               console.log("e");
               console.log(e);
               plobalLog("e");
               plobalLog(e);
             }
               
           }
          function updateRouteItem(){  
             console.log('updateRouteItem existing route_v', existing, route_v);
             plobalLog("updateRouteItem existing route_v");
             plobalLog({"existing":existing, "route_v":route_v});
             if( ($('#route-checkbox').prop('checked') && existing && route_v && existing.selected_variant_id != route_v.id) || cart.subtotal==0){
               plobalLog("remove +"+existing.selected_variant_id);
               PlobalBridge.removeFromCart({variant_id:existing.selected_variant_id});
         //         PlobalBridge.addToCart({variant_id:route_v.id,quantity:1});
         //         existing = {producthandle:'routeins',selected_variant_id:route_v.id};
             }
             if($('#route-checkbox').prop('checked') && !existing && route_v){
               plobalLog("add +"+route_v.id);
               PlobalBridge.addToCart({variant_id:route_v.id,quantity:1});//,product_type:'hidden'
               existing = {producthandle:'routeins',selected_variant_id:route_v.id};
               try {
               cart.items.forEach(function (item, index) {
               	if(route_v.id == item.selected_variant_id) {
                       window.PlobalBridge.callBridgeFunction('setQuantity',{'quantity':1,'line_item_id':item.line_item_id},JSON.stringify({'quantity':1,'line_item_id':item.line_item_id}));
                   }
               });
               }catch(err){
               	console.log(err);
               }
             }
           }
        
           function initFunction(param){
             	cart = param.json_data.cart;
             	console.log('initFunction',cart);
             	plobalLog("initFunction");
               //plobalLog(JSON.stringify(cart));
               cart.subtotal=0;
             	existing = false;
             try{
             plobalLog("cart.items="+cart.items.length);
             }catch(err){}
               cart.items.forEach(function (item, index) {
                 plobalLog("item.productHandle="+item.productHandle);
                 if(item.productHandle == 'routeins'){
                   $('#route-checkbox').prop('checked','checked');
                   existing = item;
                 }else{
                   cart.subtotal += parseFloat(item.variantList[0].line_price).toFixed(2)*parseInt(item.quantity);
                 }
               });
             	getQuotation();
           }
         
         setVariants();
           $(document).ready(function(){
           	$('#route-checkbox').change(function(){
                 if(!$(this).prop('checked')){
                   PlobalBridge.removeFromCart({variant_id:existing.selected_variant_id});
                 }else{
                   updateRouteItem();
                 }
           	});
             getQuotation();
           });
      </script>
   </body>
</html>