{"sections": {"collection-banner": {"type": "collection-banner-split", "settings": {"show_collection_image": false, "collection_sub_title": "{{ collection.metafields.header.subtitle.value }}", "show_collection_title": true, "show_collection_description": true, "background_type": "boxed", "text_width": "medium", "text_position": "middle_left", "section_height": "clamp", "image_text_color": "#6c5046", "text_background": "#f2eae3", "image_overlay_color": "#000000", "image_overlay_opacity": 20, "quick_links": "", "quick_links_hide_title": false, "quick_links_menu_handle": "{{ collection.metafields.navigation.menu_handle.value }}", "quick_links_title": "{{ collection.metafields.navigation.menu_title.value }}"}}, "link-bar-navigation": {"type": "link-bar-navigation", "blocks": {"link_ia9hBc": {"type": "link", "settings": {"title": "Intimates", "url": "shopify://collections/intimates-kyte-living", "highlighted": false}}, "link_hegMiC": {"type": "link", "settings": {"title": "Biker Short Sets", "url": "shopify://collections/womens-biker-short-sets", "highlighted": false}}, "link_fPtHt4": {"type": "link", "settings": {"title": "Botanicals", "url": "shopify://collections/womens-botanicals", "highlighted": false}}}, "block_order": ["link_ia9hBc", "link_hegMiC", "link_fPtHt4"], "settings": {"title": "New & Featured", "background": "{{ collection.metafields.sub_brand.sub_brand.value.background_color.value }}", "heading_color": "{{ collection.metafields.sub_brand.sub_brand.value.subheading_color.value }}", "text_color": "{{ collection.metafields.sub_brand.sub_brand.value.text_color.value }}"}}, "main": {"type": "main-collection", "settings": {"show_additional_text_fields": true, "show_sort_by": true, "products_per_page": 50, "mobile_products_per_row": "2", "desktop_products_per_row": 3, "show_filters": true, "show_filter_group_name": false, "show_color_swatch": true, "open_first_filter_group": true, "filter_position": "always_visible", "promotion_position": "top", "promotion_height": "small"}}, "custom_liquid_NyHHFN": {"type": "custom-liquid", "custom_css": ["h4 {text-align: center;}", "p {margin-left: auto; margin-right: auto; max-width: 980px; text-align: center;}"], "settings": {"subheading": "", "title": "", "liquid": "<h4>{{ collection.title }}</h4>\n<div class=\"rte\">{{ collection.description }}</div>", "add_vertical_spacing": true, "background_type": "boxed", "background": "rgba(0,0,0,0)", "text_color": "rgba(0,0,0,0)"}}}, "order": ["collection-banner", "link-bar-navigation", "main", "custom_liquid_NyHHFN"]}