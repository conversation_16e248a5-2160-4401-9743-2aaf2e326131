/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-page",
      "disabled": true,
      "settings": {
        "show_title": true,
        "page_width": "large"
      }
    },
    "513e97d6-f7d9-4ca2-9ed9-96d85b73ef28": {
      "type": "rich-text",
      "disabled": true,
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "",
        "subheading": "",
        "title": "Shipping",
        "content": "",
        "button_text": "",
        "button_link": "",
        "button_style": "",
        "button_icon": "",
        "background_type": "full_width",
        "text_width": "medium",
        "text_position": "center",
        "text_alignment": "center",
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)"
      }
    },
    "79ce2ad0-a8cb-48bc-a1fa-296aa740a70d": {
      "type": "rich-text",
      "disabled": true,
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "",
        "subheading": "",
        "title": "",
        "content": "<p>For more detailed information on unique shipping circumstances please visit our <a href=\"https://kytebaby.com/pages/terms-and-conditions\">Terms and Conditions</a> page.</p><p><strong>When will my REGULAR PRICED items ship? </strong>REGULAR PRICED ORDERS depart from our warehouse within <strong>3-5 Business Days</strong> of the date the order was placed<strong> </strong>(this does not include transit time with carrier). Please choose “VIP” shipping option at check-out for faster fulfillment time.</p><p><strong>Estimated Expected Delivery</strong> for US Standard Shipping is 5-10 Business Days.</p><p><strong>Estimated Expected Delivery</strong> for US VIP Shipping is 2-5 Business Days</p><p>For orders placed during <strong>LARGE RELEASES</strong> it could take up to 2 weeks to be shipped from our warehouse and/or <strong>SALES EVENTS</strong> it could take up to <strong>4</strong> weeks to be shipped from our warehouse.</p><p><strong>Standard Shipping Rates:</strong></p><p>Kyte Baby is pleased to offer FREE shipping on US orders over $85 USD, Canadian orders over $135 USD, or international orders over $300 USD. For Silver, Gold and Platinum members of our Rewards Program, FREE shipping minimums are $60 USD for US Customers and $90 USD for Canadian Customers. <strong>These amounts are based on the subtotal after discounts/rewards and before taxes.</strong> To learn more about our rewards program visit <a href=\"https://kytebaby.com/pages/frequent-flyer-rewards\">here</a>. </p><p>Otherwise, our shipping rates for regular priced items are as follows.</p><p>$7.50 USD for US orders that are less than $85 USD</p><p>$15 USD for Canadian orders less than $135 USD</p><p>€10 Euro for Ireland orders less than €60 Euro</p><p>£8 GBP for UK orders less than £50 GBP</p><p>$20 AUD for Australian orders less than $125 AUD</p><p>$35 USD for all other international orders under $300 USD</p><p><strong>Priority Shipping - VIP Fulfillment Rates:</strong></p><p>Selecting VIP Fulfillment at checkout for orders placed before noon CST, will be fulfilled and receive a tracking number the same day. All orders placed after noon CST will be fulfilled and receive a tracking number by end of next business day. It is not overnight shipping and the fulfillment period does not include time in transit. This option is unavailable during high volume periods and you will not see it at checkout. Please note VIP Fulfillment charge is not eligible for refund after the order is processed and shipped.</p><p>$25 USD for US Orders</p><p>$35 USD for Canadian Orders</p><p>Platinum members of our Rewards Program, <strong>save $15 USD ($20.25 CAD) OFF</strong> Priority Shipping / VIP Fulfillment and Gold <strong>save $10 USD ($13.50 CAD) OFF</strong>.</p><p><strong>VIP Shipping options will not be available during our Semi-Annual Clearance Sale. FREE shipping during our Semi-Annual Clearance Sale may differ from our standard FREE shipping minimums. </strong></p>",
        "button_text": "",
        "button_link": "",
        "button_style": "",
        "button_icon": "",
        "background_type": "full_width",
        "text_width": "fill",
        "text_position": "left",
        "text_alignment": "left",
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)"
      }
    },
    "custom_image_with_text_overlay_P6R3GD": {
      "type": "custom-image-with-text-overlay",
      "name": "🪁 Image with text overlay",
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "<div class=\"kyte-text-animation-container\">\n  <svg class=\"kyte-text-animation kyte-text-animation--1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1356 1356\">\n    <path id=\"wavepath\" fill-rule=\"even\" clip-rule=\"evenodd\"\n      d=\"M250 0H1106C1244.07 0 1356 111.929 1356 250V1106C1356 1244.07 1244.07 1356 1106 1356H250C111.929 1356 0 1244.07 0 1106V250C0 111.929 111.929 0 250 0Z\" />\n    <text text-anchor=\"middle\">\n      <textPath class=\"my-text\" href=\"#wavepath\" startOffset=\"150%\">\n        Soft & seasonal. Oh cozy night. Merry & Matching. Wrapped in comfort.\n        <animate attributeName=\"startOffset\" from=\"150%\" to=\"-50%\" begin=\"0s\" dur=\"30s\" repeatCount=\"indefinite\">\n        </animate>\n      </textPath>\n    </text>\n  </svg>\n</div>",
        "reveal_on_scroll": true,
        "section_height": "large",
        "video_filename": "",
        "video_url": "",
        "image": "shopify://shop_images/Twinkle_Tree_Vintage_Santa_Merry_Bright_Zipper_Romper-020.jpg",
        "mobile_image_position": "",
        "subheading": "",
        "title": "The Holiday Shoppe",
        "heading_style": "h1",
        "content": "<h4>Celebrate the season with ultra-soft, gift-worthy bamboo styles for the whole family.</h4>",
        "content_style": "",
        "button_text": "",
        "button_link": "",
        "button_icon": "",
        "button_style": "",
        "link_style": "link",
        "background_type": "full_width",
        "text_width": "medium",
        "text_position": "middle_left",
        "image_border_radius": true,
        "subheading_color": "#ffffff",
        "heading_color": "#ffffff",
        "text_color": "#ffffff",
        "button_background": "#ffffff",
        "button_text_color": "#000000",
        "overlay_color": "#000000",
        "overlay_opacity": 15
      }
    },
    "slideshow_MaE7nE": {
      "type": "slideshow",
      "blocks": {
        "image_ct3jYh": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/Toddler_in_Holiday_Prints_2160_x_1080_1.jpg",
            "mobile_image": "shopify://shop_images/Toddler_in_Holiday_1000_x_1400_1.jpg",
            "text_position": "bottom_left",
            "subheading": "",
            "title": "The Holiday Shoppe",
            "button_1_text": "",
            "button_1_link": "",
            "button_2_text": "",
            "button_2_link": "",
            "text_color": "#ffffff",
            "button_background": "#ffffff",
            "button_text_color": "#000000",
            "overlay_color": "#000000",
            "overlay_opacity": 20
          }
        }
      },
      "block_order": [
        "image_ct3jYh"
      ],
      "disabled": true,
      "name": "Slideshow",
      "settings": {
        "section_height": "auto",
        "transition_type": "sweep",
        "show_initial_transition": false,
        "autoplay": true,
        "cycle_speed": 5,
        "background": "rgba(0,0,0,0)"
      }
    },
    "multi_column_GLrmjP": {
      "type": "multi-column",
      "blocks": {
        "item_HinEMg": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/VITO_Family_Matching-025.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "<h4>Vintage Toys</h4>",
            "link_text": "Shop the collection",
            "link_url": "shopify://collections/vintage-toys",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_4VcAMt": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Frosty_Friends_Family_Matching2-04.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "<h4>Frosty Friends</h4>",
            "link_text": "Shop the collection",
            "link_url": "shopify://collections/frosty-friends",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_tejtte": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Holiday_Bow_Zippered_Romper-00.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "<h4>Holiday Bow</h4>",
            "link_text": "Shop the collection",
            "link_url": "shopify://collections/holiday-bow",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_qUPgBY": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Family_Matching_STBG_STFI_BG_FI-018.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "<h4>Solids + Stripes</h4>",
            "link_text": "Shop the collection",
            "link_url": "shopify://collections/holiday-stripes-solids",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_idp6QF": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/SKiFamily-01_FamilyPhoto.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "<h4>Ski</h4>",
            "link_text": "shop the collection",
            "link_url": "shopify://collections/ski",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_yxi3dR": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Candy_Cane_Family_Matching-08.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "<h4>Candy Cane</h4>",
            "link_text": "Shop the collection",
            "link_url": "shopify://collections/candy-cane",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_ML4GMt": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Vintage_Santa_Family_Group_Shot-07.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "<h4>Vintage Santa</h4>",
            "link_text": "Shop the collection",
            "link_url": "shopify://collections/vintage-santa",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_UPXpkH": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Merry_Bright_Family_Matching-02.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "<h4>Merry & Bright</h4>",
            "link_text": "Shop the collection",
            "link_url": "shopify://collections/merry-bright",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_Kepya3": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Twinkle_tree_in_mommy_and_me-03-2_FamilyPhoto.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "<h4>Twinkle Tree</h4>",
            "link_text": "Shop the collection",
            "link_url": "shopify://collections/twinkle-tree",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_ctw67x": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Fir_Bisque_Gingham_LS_PJ_Set_Toddler_PJ_Sets_Mom_Toddlers-01.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "<h4>Gingham</h4>",
            "link_text": "Shop the collection",
            "link_url": "shopify://collections/gingham-bisque-gingham-fir",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_Kmb6eq": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Santa_Sleigh_Family-02_FamilyPhoto.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "<h4>Santa Sleigh</h4>",
            "link_text": "Shop the collection",
            "link_url": "shopify://collections/santa-sleigh",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_xT9XBj": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Polar_Lights_Family_Matching-02.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "<h4>Polar Lights</h4>",
            "link_text": "Shop the collection",
            "link_url": "shopify://collections/polar-lights",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        }
      },
      "block_order": [
        "item_HinEMg",
        "item_4VcAMt",
        "item_tejtte",
        "item_qUPgBY",
        "item_idp6QF",
        "item_yxi3dR",
        "item_ML4GMt",
        "item_UPXpkH",
        "item_Kepya3",
        "item_ctw67x",
        "item_Kmb6eq",
        "item_xT9XBj"
      ],
      "settings": {
        "reveal_on_scroll": true,
        "stack_items": false,
        "subheading": "discover the holiday collections",
        "title": "",
        "content": "<p>Deck the halls with new arrivals! Shop your favorites now.</p>",
        "column_alignment": "center",
        "mobile_item_size": "large",
        "desktop_item_size": "medium",
        "spacing": "normal",
        "background": "#fbf6f3",
        "text_color": "#425a51"
      }
    },
    "slideshow_A9CAnj": {
      "type": "slideshow",
      "blocks": {
        "image_jayRex": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/Ship-Deadline_Desktop_b9765e55-a11a-4ec2-a7e6-cbcc732a2f8c.jpg",
            "text_position": "middle_center",
            "subheading": "",
            "title": "",
            "button_1_text": "",
            "button_1_link": "",
            "button_2_text": "",
            "button_2_link": "",
            "text_color": "#ffffff",
            "button_background": "#ffffff",
            "button_text_color": "#000000",
            "overlay_color": "#000000",
            "overlay_opacity": 0
          }
        }
      },
      "block_order": [
        "image_jayRex"
      ],
      "disabled": true,
      "settings": {
        "section_height": "auto",
        "transition_type": "sweep",
        "show_initial_transition": false,
        "autoplay": true,
        "cycle_speed": 5,
        "background": "rgba(0,0,0,0)"
      }
    },
    "featured_collections_GBdP7r": {
      "type": "featured-collections",
      "blocks": {
        "collection_bLEX6H": {
          "type": "collection",
          "settings": {
            "collection": "holiday-collections",
            "label": "",
            "button_text": "Shop All Holiday",
            "button_url": ""
          }
        }
      },
      "block_order": [
        "collection_bLEX6H"
      ],
      "settings": {
        "aspect_ratio": "tall",
        "subheading": "",
        "title": "",
        "content": "",
        "products_count": 20,
        "products_per_row": 4,
        "stack_products": true,
        "show_cta": false,
        "background": "#ffffff",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "#000000",
        "button_text_color": "rgba(0,0,0,0)"
      }
    },
    "multi_column_mK9LJH": {
      "type": "multi-column",
      "blocks": {
        "item_BjwWAn": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Vintage_Santa_Zippered_Footie-03.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "For Baby",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/gifts-for-baby",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_eU3HWf": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Cardinal_Toddler_PJs_1x1-1.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "For Kids",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/gifts-for-toddler",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_dVBtHB": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Candy_Cane_in_Womens_LS_PJ_Set-021.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "For Mom",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/gifts-for-her",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_VXNNJg": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Merry_and_Bright_in_Mens_Jogger_set-06.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "For Dad",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/gifts-for-him",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        }
      },
      "block_order": [
        "item_BjwWAn",
        "item_eU3HWf",
        "item_dVBtHB",
        "item_VXNNJg"
      ],
      "settings": {
        "reveal_on_scroll": true,
        "stack_items": false,
        "subheading": "gifts for the whole family",
        "title": "",
        "content": "",
        "column_alignment": "center",
        "mobile_item_size": "medium",
        "desktop_item_size": "medium",
        "spacing": "normal",
        "background": "#fbf6f3",
        "text_color": "#425a51"
      }
    },
    "1734136031d636976e": {
      "type": "apps",
      "blocks": {
        "foursixty_shoppable_social_ugc_custom_minifeed_fKfkCX": {
          "type": "shopify://apps/foursixty-shoppable-social-ugc/blocks/custom_minifeed/e2616dfe-fbb6-4e7d-874d-d250b973556d",
          "settings": {
            "foreground_color": "#ffffff",
            "background_color": "#000000",
            "background_color_alpha": 90,
            "post_spacing": 1,
            "hover_margin": 0,
            "rows": 2,
            "columns": 6,
            "target_blank": false,
            "show_icon": true,
            "show_text": false,
            "show_date": false,
            "call_to_action": "SHOP IT",
            "shop_type": "button",
            "body_font_size": 14,
            "body_font": "sans_serif_n4",
            "accent_font_size": 14,
            "accent_font": "sans_serif_n4",
            "disable_filter": false,
            "override_feed_id": "",
            "arbitrary_attributes": "data-category-filter=\"christmas\"",
            "custom_css": "",
            "custom_js": ""
          }
        }
      },
      "block_order": [
        "foursixty_shoppable_social_ugc_custom_minifeed_fKfkCX"
      ],
      "disabled": true,
      "settings": {
        "text_width": "medium",
        "text_position": "center",
        "text_alignment": "center",
        "background_type": "full_width",
        "subheading": "",
        "subheading_style": "heading--small",
        "title": "",
        "heading_style": "",
        "intro_text": "",
        "intro_text_style": "",
        "content": "",
        "content_style": "",
        "buttons_text": "",
        "buttons_text_style": "heading--small",
        "button_text": "",
        "button_link": "",
        "button_size": "",
        "button_style": "",
        "button_icon": "",
        "button_extra_attributes": "",
        "inner_background": "rgba(0,0,0,0)",
        "background": "",
        "heading_color": "",
        "text_color": "",
        "button_background": "",
        "button_text_color": "",
        "mobile_no_horizontal_padding": false,
        "include_horizontal_margins": true,
        "include_vertical_margins": true,
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": ""
      }
    },
    "slideshow_JiYGGz": {
      "type": "slideshow",
      "blocks": {
        "image_aNdjrc": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/Holiday-Shop-Desktop_6.jpg",
            "mobile_image": "shopify://shop_images/Holiday-Shop-Lookbook-Mobile_6.jpg",
            "text_position": "middle_center",
            "subheading": "",
            "title": "",
            "button_1_text": "",
            "button_1_link": "https://cdn.shopify.com/s/files/1/0019/7106/0847/files/Holiday_Lookbook.pdf_1.pdf?v=1758739224",
            "button_2_text": "",
            "button_2_link": "",
            "text_color": "#ffffff",
            "button_background": "#ffffff",
            "button_text_color": "#000000",
            "overlay_color": "#000000",
            "overlay_opacity": 0
          }
        }
      },
      "block_order": [
        "image_aNdjrc"
      ],
      "settings": {
        "section_height": "auto",
        "transition_type": "sweep",
        "show_initial_transition": false,
        "autoplay": true,
        "cycle_speed": 5,
        "background": "rgba(0,0,0,0)"
      }
    },
    "custom_special_promotions_twn69W": {
      "type": "custom-special-promotions",
      "blocks": {
        "promotion_WUphgJ": {
          "type": "promotion",
          "settings": {
            "image": "shopify://shop_images/Holiday_Train_Family_Matching-05.jpg",
            "tab": "Promotions",
            "title": "Holiday Pajamas",
            "link": "shopify://collections/holiday-family-pajamas",
            "icon": "shopify://shop_images/kyte_icon-sleep.png",
            "icon_title": "Festive Family Matching"
          }
        },
        "promotion_rerqGm": {
          "type": "promotion",
          "settings": {
            "image": "shopify://shop_images/kyte-baby-toddler-long-sleeve-twirl-dress-long-sleeve-twirl-dress-in-penguin-34873829720175.jpg",
            "tab": "Promotions",
            "title": "Holiday Outfits",
            "link": "shopify://collections/holiday-family-outfits",
            "icon": "shopify://shop_images/kyte_icon-photo-review.png",
            "icon_title": "Get Holiday Card Ready"
          }
        },
        "promotion_xftGct": {
          "type": "promotion",
          "settings": {
            "image": "shopify://shop_images/kyte-baby-crib-sheet-sugar-plum-crib-sheet-crib-sheet-in-sugar-plum-34643386925167.jpg",
            "tab": "Promotions",
            "title": "Holiday Bedding",
            "link": "shopify://collections/holiday-bedding",
            "icon": "shopify://shop_images/kyte_icon-blanket.png",
            "icon_title": "Keep Cozy All Season"
          }
        }
      },
      "block_order": [
        "promotion_WUphgJ",
        "promotion_rerqGm",
        "promotion_xftGct"
      ],
      "disabled": true,
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "",
        "horizontal_header": false,
        "subheading": "",
        "title": "",
        "content": "",
        "products_count": 4,
        "products_per_row": 2,
        "stack_products": false,
        "show_cta": false,
        "background": "#ffffff",
        "subheading_color": "",
        "heading_color": "",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "#fbf6f3",
        "button_text_color": "rgba(0,0,0,0)"
      }
    },
    "multi_column_EW3ngw": {
      "type": "multi-column",
      "blocks": {
        "item_qidzxC": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/3_efeea60e-bff3-411f-9c3b-ca048a5bdb3c.png",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "",
            "link_url": "",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_k9GjR6": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/4_1a62b5f7-7a36-461e-96d9-6f1dc3f5919c.png",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "",
            "link_url": "",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_bBTDcF": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/1_c5ebc815-8e8b-4fc2-be23-a28cbef2b71a.png",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "",
            "link_url": "",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_bALNrT": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/2_bfa74755-fa32-4158-a92a-b1a872ad9843.png",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "",
            "link_url": "",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        }
      },
      "block_order": [
        "item_qidzxC",
        "item_k9GjR6",
        "item_bBTDcF",
        "item_bALNrT"
      ],
      "disabled": true,
      "settings": {
        "reveal_on_scroll": true,
        "stack_items": false,
        "subheading": "",
        "title": "",
        "content": "",
        "column_alignment": "center",
        "mobile_item_size": "large",
        "desktop_item_size": "large",
        "spacing": "normal",
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)"
      }
    },
    "379d43fd-0457-48c4-a8bd-c616bb8a3242": {
      "type": "multi-column",
      "blocks": {
        "template--15282059182191__379d43fd-0457-48c4-a8bd-c616bb8a3242-item-4": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/kyte-baby-gc-gift-cards-gift-card-digital-only-31728382083183.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "<h4>Shopping for someone else but not sure what to give them?</h4>",
            "link_text": "Shop Gift Cards",
            "link_url": "shopify://products/gift-card",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        }
      },
      "block_order": [
        "template--15282059182191__379d43fd-0457-48c4-a8bd-c616bb8a3242-item-4"
      ],
      "disabled": true,
      "settings": {
        "reveal_on_scroll": true,
        "stack_items": false,
        "subheading": "",
        "title": "",
        "content": "",
        "column_alignment": "center",
        "mobile_item_size": "large",
        "desktop_item_size": "large",
        "spacing": "normal",
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)"
      }
    },
    "newsletter_69V6wb": {
      "type": "newsletter",
      "disabled": true,
      "settings": {
        "reveal_on_scroll": true,
        "image": "shopify://shop_images/Holiday_Shop_Grand_Flatlay.jpg",
        "subheading": "",
        "content": "<h4>Our final holiday collection launches October 24th! Sign up for emails to be first to shop:</h4>",
        "text_position": "center",
        "text_alignment": "center",
        "text_background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)"
      }
    },
    "1727914609ed3906d2": {
      "type": "apps",
      "blocks": {
        "klaviyo_email_marketing_sms_form_embed_block_Ttx7RH": {
          "type": "shopify://apps/klaviyo-email-marketing-sms/blocks/form-embed-block/2632fe16-c075-4321-a88b-50b567f42507",
          "settings": {
            "formId": "X2wm5d"
          }
        }
      },
      "block_order": [
        "klaviyo_email_marketing_sms_form_embed_block_Ttx7RH"
      ],
      "disabled": true,
      "settings": {
        "text_width": "medium",
        "text_position": "center",
        "text_alignment": "center",
        "background_type": "full_width",
        "subheading": "",
        "subheading_style": "heading--small",
        "title": "",
        "heading_style": "",
        "intro_text": "",
        "intro_text_style": "",
        "content": "",
        "content_style": "",
        "buttons_text": "",
        "buttons_text_style": "heading--small",
        "button_text": "",
        "button_link": "",
        "button_size": "",
        "button_style": "",
        "button_icon": "",
        "button_extra_attributes": "",
        "inner_background": "rgba(0,0,0,0)",
        "background": "",
        "heading_color": "",
        "text_color": "",
        "button_background": "",
        "button_text_color": "",
        "mobile_no_horizontal_padding": false,
        "include_horizontal_margins": true,
        "include_vertical_margins": false,
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": ""
      }
    },
    "collection_list_CDkgmY": {
      "type": "collection-list",
      "blocks": {
        "collection_ADixXW": {
          "type": "collection",
          "settings": {
            "collection": "",
            "image": "shopify://shop_images/Merry_Bright_Take_me_home_set-02.jpg",
            "subheading": "",
            "title": "Baby's First Christmas",
            "link_text": "",
            "link_url": "shopify://collections/first-christmas-outfits",
            "text_color": "rgba(0,0,0,0)",
            "overlay_color": "#000000",
            "overlay_opacity": 20
          }
        },
        "collection_6J4RWQ": {
          "type": "collection",
          "settings": {
            "collection": "",
            "image": "shopify://shop_images/Polar_Lights_Zipper_Footie-02.jpg",
            "subheading": "",
            "title": "The Hanukkah Collection",
            "link_text": "",
            "link_url": "shopify://collections/polar-lights",
            "text_color": "rgba(0,0,0,0)",
            "overlay_color": "#000000",
            "overlay_opacity": 20
          }
        },
        "collection_b7mMCh": {
          "type": "collection",
          "settings": {
            "collection": "",
            "image": "shopify://shop_images/VITO_Family_Matching-025_d619458c-92c5-4674-8778-b4b8e03257b6.jpg",
            "subheading": "",
            "title": "Matching Pajamas",
            "link_text": "",
            "link_url": "shopify://collections/holiday-family-pajamas",
            "text_color": "rgba(0,0,0,0)",
            "overlay_color": "#000000",
            "overlay_opacity": 20
          }
        },
        "collection_jFiGti": {
          "type": "collection",
          "settings": {
            "collection": "",
            "image": "shopify://shop_images/Vintage_Santa_Family_Matching-01.jpg",
            "subheading": "",
            "title": "Festive Fur Family",
            "link_text": "",
            "link_url": "shopify://collections/fur-family",
            "text_color": "rgba(0,0,0,0)",
            "overlay_color": "#000000",
            "overlay_opacity": 20
          }
        }
      },
      "block_order": [
        "collection_ADixXW",
        "collection_6J4RWQ",
        "collection_b7mMCh",
        "collection_jFiGti"
      ],
      "name": "Collection list",
      "settings": {
        "reveal_on_scroll": false,
        "layout": "carousel",
        "subheading": "",
        "title": "",
        "background": "#fbf6f3",
        "text_color": "rgba(0,0,0,0)"
      }
    },
    "multi_column_dGYYAV": {
      "type": "multi-column",
      "blocks": {
        "item_34HLFh": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/KB_Nov-Post_2.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "November Launch Calendar Overview",
            "link_url": "shopify://blogs/news/november-2025-launch-calendar-overview",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_EMpAYR": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/SKiFamily-03_FamilyPhoto.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Build Holiday Traditions",
            "link_url": "shopify://blogs/news/build-holiday-traditions-with-matching-family-christmas-pajamas",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_gkfJJ8": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Latte_in_Chunky_Knit-011-2_23e72c57-3267-4ba0-ba92-5d757bd9d2df.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Dressing Your Children for Winter",
            "link_url": "shopify://blogs/news/dressing-your-children-for-winter",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_8ADfPk": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Burgundy_Toddler_Blanket-01.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Tips for Keeping the Whole Family Warm on Cold Nights",
            "link_url": "shopify://blogs/news/tips-for-keeping-the-whole-family-warm-on-cold-nights",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_EigwVY": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Bisque_Gingham_Sleep_Bag-02.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Choosing the Right TOG",
            "link_url": "https://kytebaby.com/blogs/news/choosing-the-right-tog",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        }
      },
      "block_order": [
        "item_34HLFh",
        "item_EMpAYR",
        "item_gkfJJ8",
        "item_8ADfPk",
        "item_EigwVY"
      ],
      "settings": {
        "reveal_on_scroll": true,
        "stack_items": false,
        "subheading": "",
        "title": "",
        "content": "",
        "column_alignment": "center",
        "mobile_item_size": "medium",
        "desktop_item_size": "medium",
        "spacing": "normal",
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)"
      }
    }
  },
  "order": [
    "main",
    "513e97d6-f7d9-4ca2-9ed9-96d85b73ef28",
    "79ce2ad0-a8cb-48bc-a1fa-296aa740a70d",
    "custom_image_with_text_overlay_P6R3GD",
    "slideshow_MaE7nE",
    "multi_column_GLrmjP",
    "slideshow_A9CAnj",
    "featured_collections_GBdP7r",
    "multi_column_mK9LJH",
    "1734136031d636976e",
    "slideshow_JiYGGz",
    "custom_special_promotions_twn69W",
    "multi_column_EW3ngw",
    "379d43fd-0457-48c4-a8bd-c616bb8a3242",
    "newsletter_69V6wb",
    "1727914609ed3906d2",
    "collection_list_CDkgmY",
    "multi_column_dGYYAV"
  ]
}
