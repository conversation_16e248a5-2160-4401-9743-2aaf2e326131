{"sections": {"main": {"type": "main-customers-account", "blocks": {"orders": {"type": "orders", "settings": {}}}, "block_order": ["orders"], "settings": {}}, "169522415374d2e7a6": {"type": "apps", "blocks": {"88fc1a0f-d9a1-4d79-b913-a751d59c1f00": {"type": "shopify://apps/loudcrowd/blocks/ambassador-hub-profile/0afc7a0d-9f70-43a2-81e1-c20306ea13bd", "settings": {"sizeMarginTop": 0, "sizeMarginBottom": 0, "fontSizeTitle": 0, "memberSinceCaption": "", "fontSizeSubtitle": 0, "allowTikTokUsernameChanges": true, "socialNetworkIconColor": "#ffffff", "socialNetworkIconBkgColor": "#000000", "sizeSocialIcon": 20, "showStorefrontLink": true, "storefrontLinkCaption": "", "showCopyStorefrontLink": true, "copyStorefrontLinkCaption": "", "copyStorefrontLinkDescription": "", "showAmbassadorHubLink": true, "ambassadorHubLinkCaption": "Ambassador <PERSON><PERSON>", "urlAmbassadorHub": "", "borderButtonColor": "#000000", "showPersonalCode": true, "fontSizePersonalCode": 0, "fontSizePersonalCodeHelp": 0, "customCss": ""}}}, "block_order": ["88fc1a0f-d9a1-4d79-b913-a751d59c1f00"], "settings": {"styling_class": "", "title": "", "subheading": "", "content": "", "text_width": "medium", "text_position": "center", "text_alignment": "center", "mobile_no_horizontal_padding": false, "include_horizontal_margins": true, "include_vertical_margins": true}}}, "order": ["main", "169522415374d2e7a6"]}