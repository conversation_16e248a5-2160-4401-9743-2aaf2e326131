{%layout none%}
<script>
  window.requestData = {};
  window.product = {{ product | json }};
  
  function onDataReceive(data) {
            console.log("onDataReceive");
            console.log("typeof data");
            console.log(typeof data);
            console.log("data");
            console.log(data);
            if (typeof data == 'string') {
                data = JSON.parse(data);
            }
            console.log(data);
            console.log(data);
            window.requestData = data.json_data;
            //getProductDetails();
            //onVariantChange(window.requestData.pdp.selected_variant_id);
        }
  function onVariantChange(variant_id) {
  console.log("onVariantChange variant_id");
            console.log(variant_id);
            console.log("productQuantity");
            console.log(window.productQuantity);
            console.log("requestData.pdp");
            console.log(window.requestData.pdp);
  }
</script>