/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-page",
      "disabled": true,
      "settings": {
        "show_title": true,
        "page_width": "medium"
      }
    },
    "16636203759a7b7fb7": {
      "type": "rich-text",
      "disabled": true,
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "",
        "subheading": "The Name",
        "title": "Like the whimsy of flying a kite on a blustery day, our name is an homage to childhood: The innocence, freedom and fun of returning to nature—and it inspires everything we do at Kyte Baby.",
        "content": "",
        "button_text": "",
        "button_link": "",
        "button_style": "",
        "button_icon": "",
        "background_type": "boxed",
        "text_width": "medium",
        "text_position": "center",
        "text_alignment": "center",
        "background": "#e5e5e4",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)"
      }
    },
    "1663620719d894485b": {
      "type": "custom-content-with-carousel",
      "blocks": {
        "a1f55b98-c65c-4952-90e0-fe8a3da28adc": {
          "type": "item",
          "settings": {
            "icon": "shopify://shop_images/kyte_icon-fabric-scraps.png",
            "icon_code": "",
            "title": "",
            "content": "<p>Our fabric scraps never go to waste—they’re either used to make our littlest products (hello, bibs!) or donated to a local mop factory for their production.</p>"
          }
        },
        "1e23b886-4a27-470c-93e7-718af70a4bf2": {
          "type": "item",
          "settings": {
            "icon": "shopify://shop_images/kyte_icon-blanket-production.png",
            "icon_code": "",
            "title": "",
            "content": "<p>Our toddler blankets always sell out because we value low waste more than high sales, only producing them when they fit into the fabric layout of a production run.</p>"
          }
        },
        "00e33c34-89fa-4618-aabc-0c3207090697": {
          "type": "item",
          "settings": {
            "icon": "shopify://shop_images/kyte_icon-quality-long-lasting.png",
            "icon_code": "",
            "title": "",
            "content": "<p>We produce only high-quality garments that stand the test of time: Because our bamboo fabric is super stretchy, babies fit them for much longer than in cotton clothing.</p><p></p><p><a href=\"/pages/bamboo\" title=\"Bamboo*\">Why Bamboo? Learn more here.</a></p><p></p><p></p><p></p><p>     </p><p>  </p>"
          }
        }
      },
      "block_order": [
        "a1f55b98-c65c-4952-90e0-fe8a3da28adc",
        "1e23b886-4a27-470c-93e7-718af70a4bf2",
        "00e33c34-89fa-4618-aabc-0c3207090697"
      ],
      "disabled": true,
      "settings": {
        "background_type": "full_width",
        "text_width": "larger",
        "text_position": "center",
        "text_alignment": "left",
        "layout": "horizontal",
        "mobile_reduce_padding": true,
        "use_padding": false,
        "box_shadow": false,
        "subheading": "Sustainability",
        "subheading_style": "heading--small",
        "title": "We care about people and the planet 🌎",
        "heading_style": "h2",
        "intro_text": "",
        "intro_text_style": "text--large",
        "content": "<p>We made Kyte Baby for you—but we happen to care about the planet, too. We’ve vertically integrated our business to entrust production, environmental and ethical standards are consistently met. And we work exclusively with bamboo because it’s a sustainable resource.</p>",
        "content_style": "",
        "button_text": "",
        "button_link": "",
        "button_size": "",
        "button_style": "",
        "button_icon": "",
        "button_extra_attributes": "",
        "buttons_text": "",
        "buttons_text_style": "heading--small",
        "background": "#fbf6f3",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)",
        "styling_class": "",
        "anchor": "",
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": ""
      }
    },
    "65d519a9-69e9-47ad-9a29-18c17061d9ad": {
      "type": "custom-image-with-text-overlay",
      "disabled": true,
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "",
        "reveal_on_scroll": true,
        "section_height": "auto",
        "video_filename": "",
        "video_url": "",
        "image": "shopify://shop_images/IMG_9203.jpg",
        "mobile_image_position": "",
        "subheading": "",
        "title": "",
        "heading_style": "",
        "content": "",
        "content_style": "",
        "button_text": "",
        "button_link": "",
        "button_icon": "",
        "button_style": "",
        "link_style": "link",
        "background_type": "full_width",
        "text_width": "medium",
        "text_position": "middle_center",
        "image_border_radius": true,
        "subheading_color": "#ffffff",
        "heading_color": "#ffffff",
        "text_color": "#ffffff",
        "button_background": "#ffffff",
        "button_text_color": "#000000",
        "overlay_color": "#000000",
        "overlay_opacity": 0
      }
    },
    "b1837710-b14d-481e-9ffb-5bc4642cc7c3": {
      "type": "slideshow",
      "blocks": {
        "template--15155234308207__b1837710-b14d-481e-9ffb-5bc4642cc7c3-image-1": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/KB_KyteCares-Hero.jpg",
            "text_position": "middle_center",
            "subheading": "",
            "title": "",
            "button_1_text": "",
            "button_1_link": "",
            "button_2_text": "",
            "button_2_link": "",
            "text_color": "#ffffff",
            "button_background": "#ffffff",
            "button_text_color": "#000000",
            "overlay_color": "#000000",
            "overlay_opacity": 0
          }
        }
      },
      "block_order": [
        "template--15155234308207__b1837710-b14d-481e-9ffb-5bc4642cc7c3-image-1"
      ],
      "settings": {
        "section_height": "auto",
        "transition_type": "sweep",
        "show_initial_transition": false,
        "autoplay": true,
        "cycle_speed": 5,
        "background": "rgba(0,0,0,0)"
      }
    },
    "image_with_text_HamHaP": {
      "type": "image-with-text",
      "blocks": {
        "item_Tk8mdi": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/fall_family_photo.jpg",
            "tab_label": "",
            "subheading": "The Kyte Baby Family",
            "title": "",
            "content": "<h2>We're parents, too</h2><p>Kyte Baby may have started as one mama on a mission, but we couldn’t do what we do today without our team—a diverse group that includes parents, caregivers, and sustainability advocates hailing from all around the world, from our headquarters in the US and manufacturing facility in China to Canada, and even the UK.</p>",
            "button_text": "",
            "button_link": ""
          }
        }
      },
      "block_order": [
        "item_Tk8mdi"
      ],
      "disabled": true,
      "settings": {
        "reveal_on_scroll": true,
        "background_type": "full_width",
        "background_overlap": "image",
        "image_position": "left",
        "text_alignment": "left",
        "cycle_speed": 8,
        "accent_background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)"
      }
    },
    "custom_content_with_carousel_d3CmYL": {
      "type": "custom-content-with-carousel",
      "blocks": {
        "item_eqJcAK": {
          "type": "item",
          "settings": {
            "icon": "shopify://shop_images/Kite-Icon.png",
            "icon_code": "",
            "title": "",
            "content": "<h5>After 6 months at Kyte Baby, employees receive 4 weeks of paid parental leave and up to 22 weeks unpaid, for a total of 6 months.</h5><h5><br/></h5>"
          }
        },
        "item_khGLi7": {
          "type": "item",
          "settings": {
            "icon": "shopify://shop_images/Kite-Icon.png",
            "icon_code": "",
            "title": "",
            "content": "<h5>After 12 months at Kyte Baby, employees receive 8 weeks of paid parental leave and up to 44 weeks unpaid, for a total of 1 year.</h5><h5></h5>"
          }
        }
      },
      "block_order": [
        "item_eqJcAK",
        "item_khGLi7"
      ],
      "settings": {
        "background_type": "boxed",
        "text_width": "larger",
        "text_position": "left",
        "text_alignment": "left",
        "layout": "horizontal",
        "mobile_reduce_padding": true,
        "use_padding": false,
        "box_shadow": false,
        "subheading": "Our new parental leave policy",
        "subheading_style": "heading--small",
        "title": "Changes we're making for the better",
        "heading_style": "h2",
        "intro_text": "",
        "intro_text_style": "text--large",
        "content": "<p>At Kyte Baby, we have dedicated our company to helping babies and families find comfort for nearly a decade. We are proud of our products, team, and the community we’ve built along the way. As we continue to grow, so do our policies. We are excited to share our updated parental leave policy.</p><p>We understand the importance of family and recognize parents’ vital role in nurturing and supporting their families. With this updated policy, we aim to foster a culture of support and inclusivity while ensuring that all employees have the opportunity to be present during important moments in their lives. 🪁</p>",
        "content_style": "",
        "button_text": "",
        "button_link": "",
        "button_size": "",
        "button_style": "",
        "button_icon": "",
        "button_extra_attributes": "",
        "buttons_text": "",
        "buttons_text_style": "heading--small",
        "background": "#fbf6f3",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)",
        "styling_class": "",
        "anchor": "",
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": ""
      }
    },
    "promotion_blocks_wtjan8": {
      "type": "promotion-blocks",
      "blocks": {
        "image_bExqWW": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/DSC_8933_1.jpg",
            "highlight": false,
            "subheading": "",
            "title": "",
            "link_text": "",
            "link_url": "",
            "link_style": "link",
            "text_position": "middle_center",
            "background": "#f7f8fd",
            "text_color": "#1e316a",
            "button_background": "#ffffff",
            "button_text_color": "#1e316a"
          }
        },
        "image_dF3VpK": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/Untitled_design_70.png",
            "highlight": false,
            "subheading": "",
            "title": "Donating production  fabric scraps to local organizations for upcycling",
            "link_text": "",
            "link_url": "",
            "link_style": "link",
            "text_position": "middle_center",
            "background": "#f7f8fd",
            "text_color": "#000000",
            "button_background": "#ffffff",
            "button_text_color": "#1e316a"
          }
        },
        "image_DHYgac": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/Untitled_design_70.png",
            "highlight": false,
            "subheading": "",
            "title": "Using recyclable, degradable, or biodegradable  packaging materials",
            "link_text": "",
            "link_url": "",
            "link_style": "link",
            "text_position": "middle_center",
            "background": "#f7f8fd",
            "text_color": "#000000",
            "button_background": "#ffffff",
            "button_text_color": "#1e316a"
          }
        },
        "image_CBciKL": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/Untitled_design_70.png",
            "highlight": false,
            "subheading": "",
            "title": "Investing in direct  working relationships  with our supply chain",
            "link_text": "",
            "link_url": "",
            "link_style": "link",
            "text_position": "middle_center",
            "background": "#f7f8fd",
            "text_color": "#000000",
            "button_background": "#ffffff",
            "button_text_color": "#1e316a"
          }
        },
        "image_aLede3": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/Untitled_design_70.png",
            "highlight": false,
            "subheading": "",
            "title": "Creating a workplace culture that prioritizes a healthy work-life balance",
            "link_text": "",
            "link_url": "",
            "link_style": "link",
            "text_position": "middle_center",
            "background": "#f7f8fd",
            "text_color": "#000000",
            "button_background": "#ffffff",
            "button_text_color": "#1e316a"
          }
        }
      },
      "block_order": [
        "image_bExqWW",
        "image_dF3VpK",
        "image_DHYgac",
        "image_CBciKL",
        "image_aLede3"
      ],
      "settings": {
        "aspect_ratio": "",
        "subheading": "",
        "title": "Our commitment to sustainability includes",
        "section_size": "medium",
        "stack_on_mobile": true,
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)"
      }
    },
    "image_with_text_VtD76f": {
      "type": "image-with-text",
      "blocks": {
        "item_GzwytB": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Thyme_Flat_lay.jpg",
            "tab_label": "Item",
            "subheading": "",
            "title": "",
            "content": "<h4>Our goal is simple: to create a community of like-minded parents and caregivers who share a love for high quality, purposefully designed clothing for their babies and children. By staying true to our values and prioritizing integrity every step of the way, we’re paving the way for a better future.</h4>",
            "button_text": "",
            "button_link": ""
          }
        }
      },
      "block_order": [
        "item_GzwytB"
      ],
      "settings": {
        "reveal_on_scroll": true,
        "background_type": "full_width",
        "background_overlap": "image",
        "image_position": "left",
        "text_alignment": "center",
        "cycle_speed": 8,
        "accent_background": "#e3ded2",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)"
      }
    },
    "multi_column_D9kyad": {
      "type": "multi-column",
      "blocks": {
        "item_zAWDNQ": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Circle-1_1.png",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "<p>Our clothes are designed to hold up well to the wear and tear of everyday play</p>",
            "link_text": "",
            "link_url": "",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_pw6YkA": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Circle-2_1.png",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "<p>Each month, over 1,000 pieces of clothing are kept out landfills and given a second life by being resold in our Official Buy, Sell, Trade group on Facebook</p>",
            "link_text": "",
            "link_url": "",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_Dp87yn": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Circle-3_1.png",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "<p>Bamboo is a renewable resource and one of the fastest-growing plants on the planet</p>",
            "link_text": "",
            "link_url": "",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        }
      },
      "block_order": [
        "item_zAWDNQ",
        "item_pw6YkA",
        "item_Dp87yn"
      ],
      "settings": {
        "reveal_on_scroll": true,
        "stack_items": false,
        "subheading": "",
        "title": "",
        "content": "",
        "column_alignment": "center",
        "mobile_item_size": "medium",
        "desktop_item_size": "medium",
        "spacing": "normal",
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)"
      }
    },
    "rich_text_EC4A6q": {
      "type": "rich-text",
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "",
        "subheading": "",
        "title": "",
        "content": "<h4>Our product development team works closely with our manufacturers and suppliers, frequently visiting each of our factories, along with their supply chains, to personally ensure good working conditions and fair labor practices. We offer fully remote and hybrid positions with flexible hours, and provide a generous parental leave policy to ensure that all our employees have the opportunity to be present during important moments in their lives.</h4>",
        "button_text": "",
        "button_link": "",
        "button_style": "",
        "button_icon": "",
        "background_type": "boxed",
        "text_width": "fill",
        "text_position": "center",
        "text_alignment": "center",
        "background": "#fbf6f3",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)"
      }
    },
    "rich_text_MVwpbp": {
      "type": "rich-text",
      "disabled": true,
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "",
        "subheading": "",
        "title": "",
        "content": "<p>We understand the importance of family and recognize parents’ vital role in nurturing and supporting their families. With this updated policy, we aim to foster a culture of support and inclusivity while ensuring that all employees have the opportunity to be present during important moments in their lives. 🪁</p>",
        "button_text": "",
        "button_link": "",
        "button_style": "",
        "button_icon": "",
        "background_type": "full_width",
        "text_width": "fill",
        "text_position": "center",
        "text_alignment": "left",
        "background": "#fbf6f3",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)"
      }
    },
    "16636209318b0a79ec": {
      "type": "rich-text",
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "",
        "subheading": "",
        "title": "",
        "content": "<h2>Other causes we care about</h2><h6>As a family-oriented brand, social responsibility is one of our core values. We believe that nothing is more important than giving back to our community, and that’s why we donate to a different organization every month. Here are all the causes we have supported throughout our brand’s journey.</h6>",
        "button_text": "",
        "button_link": "",
        "button_style": "",
        "button_icon": "",
        "background_type": "full_width",
        "text_width": "fill",
        "text_position": "center",
        "text_alignment": "center",
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)"
      }
    },
    "multi_column_TAJDBP": {
      "type": "multi-column",
      "blocks": {
        "item_mdC3dk": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/AVLP-Currant.png",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our November 2025 Charity: AnnLee's Violet Light Project",
            "link_url": "shopify://blogs/news/our-november-2025-charity-annlees-violet-light-project",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_tihJ6Q": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Mount-Sinai_Chicago-blog-graphic_1.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our September NICU: Mount Sinai West",
            "link_url": "https://kytebaby.com/blogs/news/our-september-nicu-mount-sinai-west",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_X49tdm": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Kentucky-Childrens-Hospital-blog-graphic.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our October 2025 NICU: Kentucky Children's Hospital",
            "link_url": "https://kytebaby.com/blogs/news/our-october-2025-nicu-kentucky-childrens-hospital",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        }
      },
      "block_order": [
        "item_mdC3dk",
        "item_tihJ6Q",
        "item_X49tdm"
      ],
      "name": "Multi-column",
      "settings": {
        "reveal_on_scroll": true,
        "stack_items": false,
        "subheading": "",
        "title": "",
        "content": "",
        "column_alignment": "center",
        "mobile_item_size": "medium",
        "desktop_item_size": "medium",
        "spacing": "normal",
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)"
      }
    },
    "multi_column_rBHQFQ": {
      "type": "multi-column",
      "blocks": {
        "item_jmaXaM": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/EveryMom_blog_graphic_e18b7018-f4e6-4f7f-ba72-c625b5df78c2.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our August 2025 Charity: EveryMom Chicago",
            "link_url": "https://kytebaby.com/blogs/news/our-august-2025-charity-everymom-chicago",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_JTpbyP": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Memorial-Health-blog-graphic.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our July 2025 NICU: Memorial Health",
            "link_url": "shopify://blogs/news/our-july-2025-nicu-memorial-health",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_GykjGr": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Wolfson-Childrens-blog-graphic.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our June 2025 NICU: Wolfson Children's Hospital",
            "link_url": "shopify://blogs/news/our-june-2025-nicu-wolfson-childrens-hospital",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_ynDQga": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/UAMS-Health_blog_graphic.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our May NICU: UAMS",
            "link_url": "shopify://blogs/news/our-may-nicu-uams",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_cfGeUW": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Sanford-Childrens.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our April 2025 NICU: Sanford Children's Hospital Fargo",
            "link_url": "shopify://blogs/news/our-april-2025-nicu-sanford-childrens-hospital-fargo",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_YEkh3i": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Women-and-Infants.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our March 2025 NICU: Women & Infants Hospital",
            "link_url": "shopify://blogs/news/our-march-2025-nicu-women-infants-hospital",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_arzMqp": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Hawaii-Pacific-Health_b060b0f7-f3cd-4a16-85ed-8edd3a8e9ec5.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our February 2025 NICU: Kapiolani Medical Center for Women & Children",
            "link_url": "shopify://blogs/news/our-february-2025-nicu-kapiolani-medical-center-for-women-children",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_BCY3MY": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/CHLA.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our January NICU: Children's Hospital Los Angeles",
            "link_url": "shopify://blogs/news/our-january-nicu-childrens-hospital-los-angeles",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_FPNTX7": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/InovaChildrens.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our December NICU: Inova L.J. Murphy Children's Hospital",
            "link_url": "https://kytebaby.com/blogs/news/our-december-nicu-inova-l-j-murphy-childrens-hospital",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_HWh88p": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Footie_Snaps_in_Midnight_Slate_and_Cloud.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "National Prematurity Awareness Month",
            "link_url": "https://kytebaby.com/blogs/news/national-prematurity-awareness-month",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_CkyXiM": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/DSC_3869.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Kyte Cares: Nominate A NICU",
            "link_url": "https://kytebaby.com/blogs/news/kyte-cares-nominate-a-nicu",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_p6cBCM": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/IMG_6694.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our June Charity: Pregnancy After Loss Support",
            "link_url": "shopify://blogs/news/our-june-charity-pregnancy-after-loss-support",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_mgEtRr": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/PSI.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our May Charity: Postpartum Support International",
            "link_url": "shopify://blogs/news/our-may-charity-postpartum-support-international",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_fTKrcy": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/IAW_Logo.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our April Charity: Imagine A Way",
            "link_url": "https://kytebaby.com/blogs/news/our-april-charity-imagine-a-way",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_w7Uh6q": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/MomsRising.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our March Charity: MomsRising",
            "link_url": "shopify://blogs/news/our-march-charity-momsrising",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_zpwmUY": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Childrens-Heart-Foundation.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our February Charity: The Children's Heart Foundation",
            "link_url": "https://kytebaby.com/blogs/news/our-february-charity-the-childrens-heart-foundation",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_mdRPJf": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Fetal-Health-Foundation.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our January Charity: Fetal Health Foundation",
            "link_url": "shopify://blogs/news/our-january-charity-fetal-health-foundation",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_iwYQDJ": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Cribs-for-Kids.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our December Charity: Cribs for Kids",
            "link_url": "https://kytebaby.com/blogs/news/our-december-charity-cribs-for-kids",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_A46G8a": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/preemie-17_1.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our November Charity: Brave Beginnings",
            "link_url": "shopify://blogs/news/our-november-charity-brave-beginnings",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_3q9XpH": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/DSC_8409.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our October Charity: Down Syndrome Diagnosis Network",
            "link_url": "shopify://blogs/news/our-october-charity",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_4NkUV3": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Alexs-Lemonade-Stand_Logo.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our September Charity: Alex's Lemonade Stand Foundation",
            "link_url": "shopify://blogs/news/our-september-charity-alexs-lemonade-stand-foundation",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_ReE7HY": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/DSC_4494_f23c0c87-e8d0-4d84-b420-1c50a43c2ff8.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Helping Maui: Pacific Birth Collective",
            "link_url": "shopify://blogs/news/our-august-charity-pacific-birth-collective",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        }
      },
      "block_order": [
        "item_jmaXaM",
        "item_JTpbyP",
        "item_GykjGr",
        "item_ynDQga",
        "item_cfGeUW",
        "item_YEkh3i",
        "item_arzMqp",
        "item_BCY3MY",
        "item_FPNTX7",
        "item_HWh88p",
        "item_CkyXiM",
        "item_p6cBCM",
        "item_mgEtRr",
        "item_fTKrcy",
        "item_w7Uh6q",
        "item_zpwmUY",
        "item_mdRPJf",
        "item_iwYQDJ",
        "item_A46G8a",
        "item_3q9XpH",
        "item_4NkUV3",
        "item_ReE7HY"
      ],
      "settings": {
        "reveal_on_scroll": true,
        "stack_items": false,
        "subheading": "",
        "title": "",
        "content": "",
        "column_alignment": "center",
        "mobile_item_size": "medium",
        "desktop_item_size": "medium",
        "spacing": "normal",
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)"
      }
    },
    "a30b77fb-ae41-4b41-b7cb-e7f44fd368a5": {
      "type": "multi-column",
      "blocks": {
        "item_Tr4PiX": {
          "type": "item",
          "disabled": true,
          "settings": {
            "image": "shopify://shop_images/Fetal-Health-Foundation.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our January Charity: Fetal Health Foundation",
            "link_url": "shopify://blogs/news/our-january-charity-fetal-health-foundation",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "template--15155234308207__a30b77fb-ae41-4b41-b7cb-e7f44fd368a5-item-4": {
          "type": "item",
          "disabled": true,
          "settings": {
            "image": "shopify://shop_images/Cribs-for-Kids.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our December Charity: Cribs For Kids",
            "link_url": "https://kytebaby.com/blogs/news/our-december-charity-cribs-for-kids",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "template--15155234308207__a30b77fb-ae41-4b41-b7cb-e7f44fd368a5-item-1": {
          "type": "item",
          "disabled": true,
          "settings": {
            "image": "shopify://shop_images/preemie-17.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our November Charity: Brave Beginnings",
            "link_url": "https://kytebaby.com/blogs/news/our-november-charity-brave-beginnings",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "template--15155234308207__a30b77fb-ae41-4b41-b7cb-e7f44fd368a5-item-2": {
          "type": "item",
          "disabled": true,
          "settings": {
            "image": "shopify://shop_images/DSC_8409.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our October Charity: Down Syndrome Diagnosis Network",
            "link_url": "https://kytebaby.com/blogs/news/our-october-charity",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "template--15155234308207__a30b77fb-ae41-4b41-b7cb-e7f44fd368a5-item-3": {
          "type": "item",
          "disabled": true,
          "settings": {
            "image": "shopify://shop_images/Alexs-Lemonade-Stand_Logo.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our September Charity: Alex's Lemonade Stand Foundation",
            "link_url": "shopify://blogs/news/our-september-charity-alexs-lemonade-stand-foundation",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_CTgJcn": {
          "type": "item",
          "disabled": true,
          "settings": {
            "image": "shopify://shop_images/DSC_4494_f23c0c87-e8d0-4d84-b420-1c50a43c2ff8.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Helping Maui: Pacific Birth Collective",
            "link_url": "https://kytebaby.com/blogs/news/our-august-charity-pacific-birth-collective",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_8jWkEt": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Light-for-Levi_46a3dc25-e56c-4652-b7f1-398aca7604c6.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our August Charity: Light For Levi Foundation",
            "link_url": "https://kytebaby.com/blogs/news/our-august-charity-light-for-levi-foundation",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_7iXgKd": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/CCA_Logo_ef0e8cf7-8436-4d9e-815c-d9d706573d3f.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our July Charity: Children's Craniofacial Association",
            "link_url": "https://kytebaby.com/blogs/news/our-july-charity-children-s-craniofacial-association",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_hNWTwY": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/The_YMCA_Logo_520x500_d1bb1629-dc27-4836-b6c8-c41bc8c63d7a.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our June Charity: YMCA",
            "link_url": "shopify://blogs/news/our-june-charity-ymca",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_NF6xgM": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/kyte-baby-zippered-footies-zippered-footie-in-eagle-ray-32348205056111.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our April Charity: Palmarito",
            "link_url": "shopify://blogs/news/our-april-charity-palmarito-sea-turtle-rescue",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_wU9Be3": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Anchor_Point_Logo_aeecd269-5806-4f7a-a6c2-00788cde3cb3.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our March Charity: Anchor Point",
            "link_url": "shopify://blogs/news/our-march-charity-anchor-point",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_YqQd76": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Untitled-1_520x500_e0c30c80-be77-4676-a983-5775fced4165.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our February Charity: The Loveland Foundation",
            "link_url": "shopify://blogs/news/our-february-charity-the-loveland-foundation",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_hg3ra3": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/DSC_8386_e6eb6af5-0257-4aa9-9337-1e5d753b95bf.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our January Charity: Cook Children's Health Foundation",
            "link_url": "shopify://blogs/news/our-january-charity-cook-childrens-health-foundation",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_6BGzWz": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Untitled-1_237f8472-0958-41d5-af4c-931e854b9702.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our July Charity: Operation Smile",
            "link_url": "https://kytebaby.com/blogs/news/our-july-charity-operation-smile",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_XmNcAL": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/benjamin2_c870f3f4-dc48-4cf2-a54b-91be04d10885.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "CHD Awareness: Our Stories",
            "link_url": "https://kytebaby.com/blogs/news/chd-awareness-our-stories",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_HmT3RU": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Image_from_iOS_2_e522823c-27d2-436a-9ca4-95e403b7a37f.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Bundled & Blessed Event Recap",
            "link_url": "https://kytebaby.com/blogs/news/bundled-blessed-event-recap",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_JPaYtQ": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/firstCandle_logo_0e8d5a87-49d6-4fdc-a41b-47acee648260.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our October Charity: First Candle",
            "link_url": "shopify://blogs/news/our-october-charity-first-candle",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_Hpknp6": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/1f956613-2606-4482-a8b8-68be73633510-1029-toysfortotsbearlogoCMYK_4ef2a28e-db7f-493e-af7c-8d36f0453847.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Does kyte baby donate anywhere?",
            "link_url": "https://kytebaby.com/blogs/news/does-kyte-baby-donate-anywhere",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_fD9ka9": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Ronald_McDonald_House_Charities_logo_svg_5d87de01-b679-4eb0-8521-7444c58d51fe.png",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our september charity: RMHC",
            "link_url": "https://kytebaby.com/blogs/news/our-september-charity-rmhc",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_hTyN7r": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Untitled-1_54403c86-a6de-47bf-901b-0cecbb49e643.webp",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "Our September Charity: St. Jude Children's Research Hospital",
            "link_url": "https://kytebaby.com/blogs/news/our-september-charity-st-jude-childrens-research-hospital",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_Ue3Gwy": {
          "type": "item",
          "disabled": true,
          "settings": {
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Your content",
            "content": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>",
            "link_text": "",
            "link_url": "",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_pKqnm3": {
          "type": "item",
          "disabled": true,
          "settings": {
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Your content",
            "content": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>",
            "link_text": "",
            "link_url": "",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_hdb9mV": {
          "type": "item",
          "disabled": true,
          "settings": {
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Your content",
            "content": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>",
            "link_text": "",
            "link_url": "",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_MeTnHP": {
          "type": "item",
          "disabled": true,
          "settings": {
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Your content",
            "content": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>",
            "link_text": "",
            "link_url": "",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_waDPex": {
          "type": "item",
          "disabled": true,
          "settings": {
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Your content",
            "content": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>",
            "link_text": "",
            "link_url": "",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        }
      },
      "block_order": [
        "item_Tr4PiX",
        "template--15155234308207__a30b77fb-ae41-4b41-b7cb-e7f44fd368a5-item-4",
        "template--15155234308207__a30b77fb-ae41-4b41-b7cb-e7f44fd368a5-item-1",
        "template--15155234308207__a30b77fb-ae41-4b41-b7cb-e7f44fd368a5-item-2",
        "template--15155234308207__a30b77fb-ae41-4b41-b7cb-e7f44fd368a5-item-3",
        "item_CTgJcn",
        "item_8jWkEt",
        "item_7iXgKd",
        "item_hNWTwY",
        "item_NF6xgM",
        "item_wU9Be3",
        "item_YqQd76",
        "item_hg3ra3",
        "item_6BGzWz",
        "item_XmNcAL",
        "item_HmT3RU",
        "item_JPaYtQ",
        "item_Hpknp6",
        "item_fD9ka9",
        "item_hTyN7r",
        "item_Ue3Gwy",
        "item_pKqnm3",
        "item_hdb9mV",
        "item_MeTnHP",
        "item_waDPex"
      ],
      "settings": {
        "reveal_on_scroll": true,
        "stack_items": false,
        "subheading": "",
        "title": "",
        "content": "",
        "column_alignment": "center",
        "mobile_item_size": "medium",
        "desktop_item_size": "medium",
        "spacing": "normal",
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)"
      }
    },
    "custom_liquid_qqH6Ya": {
      "type": "custom-liquid",
      "settings": {
        "subheading": "",
        "title": "",
        "liquid": "<div data-tf-live=\"01J91ZWNTDA77JB4FYAWWRF98X\"></div><script src=\"//embed.typeform.com/next/embed.js\"></script>",
        "add_vertical_spacing": true,
        "background_type": "full_width",
        "background": "#fbf6f3",
        "text_color": "rgba(0,0,0,0)"
      }
    },
    "174477108848c30c2f": {
      "type": "apps",
      "blocks": {
        "foursixty_shoppable_social_ugc_custom_slider_EcXc6c": {
          "type": "shopify://apps/foursixty-shoppable-social-ugc/blocks/custom_slider/e2616dfe-fbb6-4e7d-874d-d250b973556d",
          "settings": {
            "foreground_color": "#ffffff",
            "background_color": "#000000",
            "background_color_alpha": 90,
            "post_spacing": 1,
            "hover_margin": 0,
            "cell_size": "25%",
            "target_blank": false,
            "arrows_outside": false,
            "show_icon": true,
            "show_text": false,
            "show_date": false,
            "call_to_action": "SHOP IT",
            "shop_type": "button",
            "body_font_size": 14,
            "body_font": "sans_serif_n4",
            "accent_font_size": 14,
            "accent_font": "sans_serif_n4",
            "disable_filter": false,
            "override_feed_id": "",
            "arbitrary_attributes": "data-category-filter=\"Nominate NICU\"",
            "custom_css": "",
            "custom_js": ""
          }
        }
      },
      "block_order": [
        "foursixty_shoppable_social_ugc_custom_slider_EcXc6c"
      ],
      "settings": {
        "text_width": "medium",
        "text_position": "center",
        "text_alignment": "center",
        "background_type": "full_width",
        "subheading": "",
        "subheading_style": "heading--small",
        "title": "",
        "heading_style": "",
        "intro_text": "",
        "intro_text_style": "",
        "content": "",
        "content_style": "",
        "buttons_text": "",
        "buttons_text_style": "heading--small",
        "button_text": "",
        "button_link": "",
        "button_size": "",
        "button_style": "",
        "button_icon": "",
        "button_extra_attributes": "",
        "inner_background": "rgba(0,0,0,0)",
        "background": "",
        "heading_color": "",
        "text_color": "",
        "button_background": "",
        "button_text_color": "",
        "mobile_no_horizontal_padding": false,
        "include_horizontal_margins": true,
        "include_vertical_margins": true,
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": ""
      }
    }
  },
  "order": [
    "main",
    "16636203759a7b7fb7",
    "1663620719d894485b",
    "65d519a9-69e9-47ad-9a29-18c17061d9ad",
    "b1837710-b14d-481e-9ffb-5bc4642cc7c3",
    "image_with_text_HamHaP",
    "custom_content_with_carousel_d3CmYL",
    "promotion_blocks_wtjan8",
    "image_with_text_VtD76f",
    "multi_column_D9kyad",
    "rich_text_EC4A6q",
    "rich_text_MVwpbp",
    "16636209318b0a79ec",
    "multi_column_TAJDBP",
    "multi_column_rBHQFQ",
    "a30b77fb-ae41-4b41-b7cb-e7f44fd368a5",
    "custom_liquid_qqH6Ya",
    "174477108848c30c2f"
  ]
}
