{% layout none %}
<title>Product PDP Collections</title>
<script>
        (function() {
            window.PlobalBridge = {
                addLineItemProperty:function(obj){
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.addLineItemProperty){
                    window.webkit.messageHandlers.addLineItemProperty.postMessage(obj);
                }
            } catch(e) {
                console.log("ios error addLineItemProperty");
                console.log(e);
            }
            try {
                if(window && window.AndroidBridge && window.AndroidBridge.addLineItemProperty){
                    window.AndroidBridge.addLineItemProperty(JSON.stringify(obj));
                }
            } catch(e) {
                console.log("android error addLineItemProperty");
                console.log(e);
            }
            // if(!window.webkit && !window.AndroidBridge){
            //     console.log('addLineItemProperty  '+JSON.stringify(obj));
            // }
            console.log('addLineItemProperty  '+JSON.stringify(obj));
        },
                adjustWebViewHeight: function(container) {
                    if (!container) {
                        var height = document.body.clientHeight;
                    } else if (container == 0) {
                        var height = 0;
                    } else if (container == -25) {
                        var height = -25;
                    } else {
                        var height = document.getElementById(container).clientHeight;
                    }
                    height = 50;
                    try {
                        if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.adjustWebViewHeight) {
                            window.webkit.messageHandlers.adjustWebViewHeight.postMessage(height);
                        }
                    } catch (e) {
                        console.log("ios error adjustWebViewHeight");
                        console.log(e);
                    }
                    if (!window.webkit && !window.AndroidBridge) {
                        console.log('adjustWebViewHeight  ' + height);
                    }
                },
                openURLNew: function(data) {
                    try {
                        if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.openURLNew) {
                            window.webkit.messageHandlers.openURLNew.postMessage(data);
                        }
                    } catch (e) {
                        console.log("ios error openURLNew");
                        console.log(e);
                    }
                    try {
                        if (window && window.AndroidBridge && window.AndroidBridge.openURLNew) {
                            window.AndroidBridge.openURLNew(JSON.stringify(data));
                        }
                    } catch (e) {
                        console.log("android error openURLNew");
                        console.log(e);
                    }
                    if (!window.webkit && !window.AndroidBridge) {
                        window.location.assign(data.url);
                    }
                    this.openURL(data.url, data.title, data.type);
                },
                callBridgeFunction: function(functionName, paramsIos, paramAndroid) {
                    console.log(functionName);
                    try {
                        if (window && window.AndroidBridge) {
                            if (paramAndroid) {
                                window.AndroidBridge[functionName](paramAndroid);
                            } else {
                                window.AndroidBridge[functionName]();
                            }
                        }
                    } catch (e) {
                        console.log("I got clicked android error");
                        console.log(e);
                    }
                    try {
                        if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers[functionName]) {
                            if (paramsIos) {
                                window.webkit.messageHandlers[functionName].postMessage(paramsIos);
                            } else {
                                window.webkit.messageHandlers[functionName].postMessage();
                            }
                        }
                    } catch (e) {
                        console.log("I got clicked ios error");
                        console.log(e);
                    }
                }
            }
        })();
    </script>
<script>
  function getProductDetails() {
            return;
            try {
                $.get('https://' + window.requestData['x-plobal-shop-url'] + '/products/' + window.requestData.pdp.productHandle + '?view=pa.inventory', function(response) {
                    console.log("response");
                    console.log(response);
                    window.productDetail = response;
                    if (typeof window.productDetail == 'string') {
                        window.productDetail = JSON.parse(window.productDetail);
                    }
                    onVariantChange(window.requestData.pdp.selected_variant_id);
                });
            } catch (err) {
                window.productDetail = {};
            }
        }

        function onVariantChange(variant_id) {
            console.log("onVariantChange variant_id");
            console.log(variant_id);
            console.log("productQuantity");
            console.log(window.productQuantity);
            console.log("requestData.pdp");
            console.log(window.requestData.pdp);
            var pv = {};
            var v = {};
            window.requestData.pdp.selected_variant_id = variant_id;
            console.log("requestData.pdp");
            console.log(window.requestData.pdp);
            console.log("requestData.customer_details");
            window.requestData.customer_details = <CUSTOMER_DATA>;
            console.log((window.requestData.hasOwnProperty('customer_details') ? window.requestData.customer_details : ""));
            var productTags = window.requestData.pdp.hasOwnProperty('tags') ? window.requestData.pdp.tags : "";
            var tags = window.requestData.hasOwnProperty('customer_details') && window.requestData.customer_details.hasOwnProperty('tags') ? window.requestData.customer_details.tags : "";
            tags = tags.split(",");
            tags = tags.map(s => s.trim());
            console.log("tags");
            console.log(tags);
            if (requestData.hasOwnProperty('pdp') && requestData.pdp.hasOwnProperty('collection_ids')) {
                var collection_ids = requestData.pdp.collection_ids;
                window.PlobalBridge.addLineItemProperty({'collection_ids':collection_ids});
                var hide_from_display = JSON.stringify(["collection_ids"]);
            var d = { "hide_from_display":hide_from_display};
                window.PlobalBridge.callBridgeFunction('addLineItemProperty', d, JSON.stringify(d));
            }
            return;
        }
</script>
<script>
  window.requestData = <REQUEST_DATA>;
        window.productQuantity = {};
        window.productDetail = {};

        function onDataReceive(data) {
            console.log("onDataReceive");
            console.log("typeof data");
            console.log(typeof data);
            console.log("data");
            console.log(data);
            if (typeof data == 'string') {
                data = JSON.parse(data);
            }
            console.log(data);
            console.log(data);
            window.requestData = data.json_data;
            getProductDetails();
            onVariantChange(window.requestData.pdp.selected_variant_id);
        }
</script>