<script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>

<style>
  *,::before,::after {
    box-sizing: border-box;
    margin: 0px;
    padding: 0px;
  }

  .container-fluid {
    max-width: 100%;
    margin: 0px auto;
  }

  .row {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    box-sizing: border-box;
  }

  .col-12 {
    flex: 0 0 100%;
    -webkit-box-flex: 0 0 100%;
    -moz-box-flex: 0 0 100%;
    -webkit-flex: 0 0 100%;
    -ms-flex: 0 0 100%;
    max-width: 100%;
  }

  .col-6 {
    flex: 0 0 50%;
    -webkit-box-flex: 0 0 50%;
    -moz-box-flex: 0 0 50%;
    -webkit-flex: 0 0 50%;
    -ms-flex: 0 0 50%;
    max-width: 50%;
  }

  .col-4 {
    flex: 0 0 33.33%;
    -webkit-box-flex: 0 0 33.33%;
    -moz-box-flex: 0 0 33.33%;
    -webkit-flex: 0 0 33.33%;
    -ms-flex: 0 0 33.33%;
    max-width: 33.33%;
  }

  .col-3 {
    flex: 0 0 25%;
    -webkit-box-flex: 0 0 25%;
    -moz-box-flex: 0 0 25%;
    -webkit-flex: 0 0 25%;
    -ms-flex: 0 0 25%;
    max-width: 25%;
  }

  /******* Media Query Start *******/
  @media only screen and (max-width: 768px) {
    .container {
      padding: 0px 25px;
    }

    .col-6 {
      flex: 0 0 100%;
      -webkit-box-flex: 0 0 100%;
      -moz-box-flex: 0 0 100%;
      -webkit-flex: 0 0 100%;
      -ms-flex: 0 0 100%;
      max-width: 100%;
    }

    .col-4 {
      flex: 0 0 50%;
      -webkit-box-flex: 0 0 50%;
      -moz-box-flex: 0 0 50%;
      -webkit-flex: 0 0 50%;
      -ms-flex: 0 0 50%;
      max-width: 50%;
    }

    .col-3 {
      flex: 0 0 50%;
      -webkit-box-flex: 0 0 50%;
      -moz-box-flex: 0 0 50%;
      -webkit-flex: 0 0 50%;
      -ms-flex: 0 0 50%;
      max-width: 50%;
    }
  }

  @media only screen and (max-width: 480px) { 
    .col-4, 
    .col-3 {
      flex: 0 0 100%;
      -webkit-box-flex: 0 0 100%;
      -moz-box-flex: 0 0 100%;
      -webkit-flex: 0 0 100%;
      -ms-flex: 0 0 100%;
      max-width: 100%;
    }
  }
  /******* Base Media Query End *******/


  /*  Main Section CSS Start  */
  .main-section {
    margin-bottom: 8px;
  }

  .main-section .container-fluid:first-child {
    background: url('https://cdn.shopify.com/s/files/1/0019/7106/0847/files/main-background.png?v=1650695679');
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .main-section .col-6:first-child {
    flex: 0 0 55.25%;
    max-width: 55.25%;
    align-self: center;
  }

  .main-section .col-6:first-child .col-wrapper {
    padding: 50px 0px;
  }

  .main-section .col-6:first-child .image-wrapper {
    position: relative;
    padding-bottom: 0%;
    max-width: 260px;
    margin: 0px auto 22px;
  }

  .main-section .col-6:first-child .image-wrapper .image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    max-width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .main-section .descriptions {
    font-size: 20px;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: 0.1em;
    text-align: center;
    text-transform: uppercase;
    color: #696766;
  }

  .main-section .col-6:last-child {
    flex: 0 0 43.75%;
    max-width: 43.75%;
    margin-left: auto;
  }

  .main-section .col-6:last-child .image-wrapper {
    position: relative;
    padding-bottom: 79.367%;
    height: 100%;
  }

  .main-section .col-6:last-child .image-wrapper .image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    max-width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .main-section .container-fluid:last-child p {
    font-size: 16px;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: 0.1em;
    text-align: center;
    margin: 6px 0px;
    padding: 0px 18px;
    text-transform: uppercase;
  }

  @media only screen and (max-width: 1024px) {
    .main-section .col-6:first-child,
    .main-section .col-6:last-child {
      flex: 0 0 50%;
      max-width: 50%;
    }
  }

  @media only screen and (max-width: 768px) {
    .main-section {
      margin-bottom: 8px;
    }

    .main-section .container-fluid:first-child {
      background: url('https://cdn.shopify.com/s/files/1/0019/7106/0847/files/Rectangle_26.png?v=1650702947');
      background-position: center;
      background-repeat: no-repeat;
      background-size: cover;
    }

    .main-section .col-6:first-child,
    .main-section .col-6:last-child {
      flex: 0 0 100%;
      max-width: 100%;
    }

    .main-section .col-6:first-child .col-wrapper {
      padding: 65px 0px 45px;
    }

    .main-section .col-6:first-child .image-wrapper {
      max-width: 200px;
    }

    .main-section .descriptions {
      font-size: 16px;
      line-height: 22px;
      margin: 0;
    }
  }
  /*  Main Section CSS End  */

  /*  Reward Section CSS Start  */
  .reward-section {
    padding: 100px 0px 8px;
    background-color: #fff;
  }

  .reward-section .container {
    max-width: 1256px;
    padding: 0px 32px;
  }

  .reward-section .row {
    display: flex;
    margin: 0px -16px;
    justify-content: center;
    flex-wrap: wrap;
  }

  .reward-section .description {
    font-size: 16px;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: 0.02em;
    text-align: center;
    margin-bottom: 6px;
  }

  .reward-section .heading {
    font-size: 20px;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: 0.1em;
    text-align: center;
    margin-bottom: 40px;
  }

  .reward-section .col-4 {
    max-width: 50%;
    margin: 32px;
  }

  .reward-section .col-4 .col-wrapper {
    margin: 0px 16px;
    background: rgb(251, 246, 243);
    border-radius: 10px;
    padding: 34px 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .reward-section .post-title {
    font-size: 20px;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: 0.1em;
    text-align: center;
    margin-bottom: 18px;
  }

  .reward-section .post-text {
    font-size: 16px;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: 0.02em;
    text-align: center;
    margin-bottom: 18px;
    text-transform: uppercase;
  }

  .reward-section .image-wrapper {
    position: relative;
    padding-bottom: 200px;
    width: 200px;
    margin: 0px auto;
  }

  .reward-section .image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    max-width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 20px;
  }

  .signup-button_lcButtonContainer__1Ws4J {
    padding: 0px;
    margin-top: 40px;
  }

  .reward-section .signup-button_lcButtonContainer__1Ws4J {
    margin-top: 0px;
  }

  .signup-button_lcButton__3PS4y {
    width: auto;
    height: auto;
    border-radius: 0px;
    padding: 14px 22px;
  }

  .signup-button_lcButtonText__1Qqcl {
    margin: 0px;
    font-size: 16px;
    font-weight: 700;
    line-height: 20px;
    letter-spacing: 0.1em;
    text-align: center;
  }

  @media only screen and (max-width: 1024px) {
    .reward-section .col-4 .col-wrapper {
      padding: 25px 20px;
    }

    .reward-section .image-wrapper {
      padding-bottom: 100%;
      width: 100%;
    }
  }

  @media only screen and (max-width: 768px) {
    .reward-section {
      padding: 40px 0px;
    }

    .reward-section .container {
      padding: 0px 20px;
    }

    .reward-section .row {
      margin: 0px;
    }

    .reward-section .col-4 {
      margin-bottom: 30px;
    }

    .reward-section .heading {
      margin-bottom: 40px;
    }

    .reward-section .col-4 .col-wrapper {
      max-width: 375px;
      margin: 0px 15px 20px;
    }

    .reward-section .post-text {
      min-height: 40px;
    }
  }

  @media only screen and (max-width: 480px) {
    .reward-section .container {
      padding: 0px 12px;
    }

    .reward-section .col-4 {
      flex: 0 0 50%;
      max-width: 50%;
      margin-bottom: 20px;
    }

    .reward-section .description {
      font-size: 16px;
      line-height: 22px;
    }

    .reward-section .heading {
      font-size: 20px;
      line-height: 30px;
    }

    .reward-section .col-4 .col-wrapper {
      margin: 0px 12px 20px;
      padding: 8px;
    }

    .reward-section .post-title {
      font-size: 15px;
      line-height: 26px;
      margin-bottom: 2px;
    }

    .reward-section .post-text {
      font-size: 15px;
      line-height: 20px;
      min-height: 40px;
      margin: 0;
    }
  }

  @media only screen and (max-width: 375px) { 
    .reward-section .col-4 .col-wrapper {
      margin-bottom: 20px;
    }
  }
  /*  Reward Section CSS End  */

  /*  Tiktok Reward Section CSS Start  */
  .tiktok-reward-section {
    padding: 74px 0px 92px;
    margin: 65px 0px 40px;
    background: rgb(251, 246, 243);
    text-align: center;
  }

  .tiktok-reward-section .container {
    max-width: 1286px;
    padding: 0px 50px;
  }

  .tiktok-reward-section .container > div:nth-child(2) {
    margin: 0px -16px;
  }

  .main-title-wrapper {
    max-width: 1005px;
    margin: 0px auto;
  }

  .tiktok-reward-section .sub-heading {
    font-size: 16px;
    font-weight: 400;
    line-height: 26px;
    letter-spacing: 0.02em;
    text-align: center;
    margin-bottom: 6px;
  }

  .tiktok-reward-section .heading {
    font-size: 20px;
    font-weight: 500;
    line-height: 30px;
    letter-spacing: 0.1em;
    text-align: center;
    margin-bottom: 18px;
  }

  .tiktok-reward-section .description  {
    font-size: 16px;
    font-weight: 400;
    line-height: 26px;
    letter-spacing: 0.02em;
    text-align: center;
    margin-bottom: 62px;
  }

  .tiktok-reward-section .container > div:nth-child(2) .col-4 {
    margin-bottom: 62px;
  }

  .tiktok-reward-section .container > div:nth-child(2) .col-wrapper {
    padding: 44px 46px;
    margin: 0px 16px;
    background: #FFFFFF;
    box-shadow: 0px 6px 20px rgba(0, 0, 0, 0.06);
    border-radius: 10px;
    min-height: 190px;
    height: 100%;
  }

  .tiktok-reward-section .title {
    font-size: 20px;
    font-weight: 500;
    line-height: 25px;
    letter-spacing: 0.1em;
    text-align: center;
    margin-bottom: 18px;
  }

  .tiktok-reward-section .sub-title {
    font-size: 16px;
    font-weight: 400;
    line-height: 26px;
    letter-spacing: 0em;
    text-align: center;
    text-transform: uppercase;
    margin: 0;
  }
  
  .tiktok-reward-section .sub-title.sub-title-second {
    text-transform: none;
    margin-top: 15px;
  }

  .tiktok-reward-section .container > div:last-child {
    margin: 0px -35px;
  }

  .tiktok-reward-section .container > div:last-child .col-3 {
    position: relative;
  }

  .tiktok-reward-section .container > div:last-child .col-parent-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .tiktok-reward-section .container > div:last-child .col-wrapper {
    height: 400px;
    background: #D8D9DA;
    border-radius: 10px;
    margin: 0px 10px;
    position: relative;
    padding-bottom: 100%;
    width: 100%;
  }

  .tiktok-reward-section .container > div:last-child .col-wrapper video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 10px;
    object-fit: cover;
  }

  @media only screen and (max-width: 989px) {
    .tiktok-reward-section .container {
      padding: 0px 12px;
    }

    .tiktok-reward-section .container > div:nth-child(2) {
      margin: 0px;
    }

    .tiktok-reward-section .container > div:last-child {
      margin: 0px;
    }
    .tiktok-reward-section .col-3 {
      flex: 0 0 50%;
      max-width: 50%;
      margin-bottom: 20px;
    }

    .tiktok-reward-section .container > div:nth-child(2) .col-4 {
      margin-bottom: 40px;
    }

    .tiktok-reward-section .container > div:nth-child(2) .col-wrapper {
      padding: 25px 25px;
      margin: 0px 12px;
    }

    .tiktok-reward-section .container > div:last-child .col-wrapper {
      max-width: 295px;
      margin: 0px auto;
    }
  }

  @media only screen and (max-width: 768px) {
    .tiktok-reward-section {
      margin: 30px 0px;
      padding: 60px 0px;
    }

    .tiktok-reward-section .container {
      padding: 0px 15px;
    }

    .tiktok-reward-section .container > div:nth-child(2) .col-4 {
      margin-bottom: 30px;
    }

    .tiktok-reward-section .container > div:nth-child(2) .col-wrapper {
      padding: 20px;
      min-height: 132px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
  }

  @media only screen and (max-width: 650px) {
    .tiktok-reward-section .col-3 {
      flex: 0 0 50%;
      max-width: 50%;
      margin-bottom: 20px;
    }

    .tiktok-reward-section .container > div:last-child .col-wrapper {
      max-width: 180px;
      margin: 0px 10px;
      min-height: 250px;
      height: auto;
    }
  }
  /*  Tiktok Reward Section CSS Start  */

  /*  How it Works Section CSS Start  */
  .how-works-section {
    padding: 32px 0px 64px;
  }

  .how-works-section .container {
    max-width: 1256px;
    padding: 0px 32px;
  }

  .how-works-section .row {
    margin: 0px -16px;;
  }

  .how-works-section .heading {
    font-size: 20px;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: 0.1em;
    text-align: center;
    margin-bottom: 32px;
  }

  .how-works-section .col-4 .col-wrapper {
    margin: 0px 16px;
    background: rgb(251, 246, 243);
    border-radius: 10px;
    padding: 28px;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 350px;
  }

  .how-work-svg {
    width: 45px;
    height: 45px;
    display: flex;
    margin: 38px 0px 23px;
  }

  .how-works-section .post-title {
    font-size: 20px;
    font-weight: 500;
    line-height: 30px;
    letter-spacing: 0.1em;
    text-align: left;
    margin-bottom: 16px;
  }

  .how-works-section .post-text {
    font-size: 16px;
    font-weight: 400;
    line-height: 32px;
    letter-spacing: 0.02em;
    text-align: left;
  }

  @media only screen and (max-width: 1024px) {
    .how-works-section .col-4 .col-wrapper {
      padding: 20px;
    }

    .how-work-svg {
      margin: 17px 0px;
    }
  }

  @media only screen and (max-width: 768px) { 
    .how-works-section {
      padding: 16px 0px;
    }

    .how-works-section .container {
      padding: 0px 20px;
    }

    .how-works-section .row {
      margin: 0px;
    }

    .how-works-section .col-4 {
      flex: 0 0 100%;
      max-width: 100%;
    }

    .how-works-section .heading {
      margin-bottom: 24px;
    }

    .how-works-section .col-4 .col-wrapper {
      max-width: 375px;
      min-height: 250px;
      margin: 0px auto 30px;
      height: auto;
    }

    .how-works-section .col-4:last-child .col-wrapper {
      margin-bottom: 0px;
    }
  }

  @media only screen and (max-width: 480px) { 
    .how-works-section .post-title {
      font-size: 16px;
      line-height: 26px;
    }

    .how-works-section .post-text {
      font-size: 15px;
      line-height: 24px;
    }
  }
  /*  How it Works Section CSS End  */

  /*  Post Terms Section CSS Start  */
  .post-terms-section {
    padding: 82px 0px 102px;
    background: rgb(251, 246, 243);
    margin-bottom: 62px;
  }

  .post-terms-section .col-6:first-child {
    border-right: 2px solid #000;
  }

  .post-terms-section .col-wrapper {
    height: 100%;
    padding: 0px 60px;
  }

  .post-terms-section .heading {
    font-size: 20px;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: 0.1em;
    text-align: left;
    margin-bottom: 52px;
  }

  .post-terms-section ul {
    margin: 0px;
  }

  .post-terms-section .item {
    font-size: 16px;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: 0.02em;
    text-align: left;
    margin-bottom: 24px;
  }

  .post-terms-section ul .item:last-child {
    margin-bottom: 0px;
  }

  .post-terms-section .container-fluid {
    margin-top: 66px;
    padding: 0px 42px;
  }

  .post-terms-section .container-fluid .col-3 {
    padding-left: 7px;
    padding-right: 7px;
  }

  .post-terms-section .image-wrapper {
    position: relative;
    padding-bottom: 100%;
    width: 100%;
    height: 100%;
  }

  .post-terms-section .image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    max-width: 100%;
    height: 100%;
    object-fit: cover;
  }

  @media only screen and (max-width: 768px) {
    .post-terms-section {
      padding: 60px 0px;
    }

    .post-terms-section .col-6:first-child {
      border-right: none;
      border-bottom: 1px solid #000;
      margin-bottom: 60px;
    }

    .post-terms-section .col-6:first-child .col-wrapper {
      padding-bottom: 60px;
    }

    .post-terms-section .heading {
      margin-bottom: 40px;
    }

    .post-terms-section .item {
      margin-bottom: 18px;
    }

    .post-terms-section .container-fluid {
      margin-top: 60px;
      padding: 0px 30px;
    }

    .post-terms-section .container-fluid .col-3 {
      margin-bottom: 14px;
    }
  }

  @media only screen and (max-width: 480px) {
    .post-terms-section {
      margin-bottom: 40px;
      padding-bottom: 24px;
    }
    .post-terms-section .col-3 {
      flex: 0 0 50%;
      max-width: 50%;
    }

    .post-terms-section .col-wrapper {
      padding: 0px 20px;
    }

    .post-terms-section .item {
      font-size: 15px;
    }

    .post-terms-section .container-fluid {
      padding: 0px 12px;
    }

    .post-terms-section .container-fluid .col-3 {
      padding-left: 13px;
      padding-right: 13px;
      margin-bottom: 26px;
    }
  }
  /*  Post Terms Section CSS End  */

  /*  FAQs Section CSS Start  */
  .faq-section {
    padding: 62px 0px;
  }

  .faq-section .container {
    max-width: 1286px;
    padding: 0px 30px;
  }

  .faq-section .heading {
    font-size: 20px;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: 0.1em;
    text-align: center;
    text-transform: uppercase;
    margin-bottom: 70px;
    color: #000000;
  }

  .faq-section .col-12 {
    margin-bottom: 30px;
    background: #FFFFFF;
    box-shadow: 0px 6px 20px rgba(0, 0, 0, 0.08);
    border-radius: 10px;   
    user-select: none;
    -webkit-user-select: none;
    transition: all 0.4s ease;
    -webkit-tap-highlight-color: transparent;
  }

  .faq-section .col-12:last-child {
    margin-bottom: 0px;
  }

  .faq-section .question-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 38px 50px;
    cursor: pointer;
  }

  .faq-section .question {
    font-size: 16px;
    font-weight: 500;
    line-height: 26px;
    letter-spacing: 0.02em;
    text-align: left;
    text-transform: none;
    margin: 0px;
    color: #000000;
    width: 90%;
  }

  .detail-open-arrow {
    width: 25px;
    height: 25px;
    display: flex;
    transition: all 0.4s ease;
    margin-left: 10px;
  }

  .faq-section .col-wrapper .bottom-wrapper {
    transition: all 0.3s;
    overflow: hidden;
  }

  .faq-section .col-wrapper:not(.top-click) .bottom-wrapper {
    height: 0px !important;
  }

  .answer-wrapper {
    padding: 0px 50px 50px;    
  }

  .faq-section .answer-wrapper .answer {
    font-size: 16px;
    font-weight: 400;
    line-height: 26px;
    letter-spacing: 0.02em;
    text-align: left;
    padding: 9.5px 27px;
    border-left: 8px solid #EBD7D5;
    transform: translateX(-20px);
    transition: all 0.3s ease;
    opacity: 1;
    margin: 0px;
  }

  .faq-section .col-wrapper.top-click .answer-wrapper .answer {
    transform: translateX(0px);
    transition: all 0.4s ease-in-out;
    opacity: 1;
  }

  .faq-section .col-wrapper.top-click .detail-open-arrow {
    transform: rotate(90deg);
  }

  @media only screen and (max-width: 768px) {
    .faq-section {
      padding: 70px 0px;
    }

    .faq-section .heading {
      margin-bottom: 50px;
    }

    .faq-section .question-wrapper {
      padding: 24px;
    }

    .detail-open-arrow {
      width: 16px;
      height: 16px;
    }

    .answer-wrapper {
      padding: 0px 24px 24px;
    }

    .faq-section .answer {
      padding: 4px 20px;
      border-left: 4px solid #000;
    }
  }

  @media only screen and (max-width: 475px) {
    .faq-section {
      padding: 30px 0px;
    }

    .faq-section .container {
      padding: 0px 25px;
    }

    .faq-section .heading {
      font-size: 16px;
      margin-bottom: 36px;
    }

    .faq-section .col-12 {
      margin-bottom: 18px;
    }

    .faq-section .question-wrapper {
      padding: 20px 16px;
    }

    .answer-wrapper {
      padding: 0px 12px 20px;
    }

    .faq-section .answer-wrapper .answer {
      padding: 4px 24px 4px 12px;
      border-left: 4px solid #EBD7D5;
    }
  }
  /*  FAQs Section CSS End  */
  
  .signup-button_lcButton__3PS4y {
    display: block;
  }
  
  .reward-section .signup-button_lcButtonContainer__1Ws4J .signup-button_lcButton__3PS4y:nth-child(2) {
    display: flex;
  }
  
  .tiktok-reward-section .signup-button_lcButtonContainer__1Ws4J .signup-button_lcButton__3PS4y:last-child {
    display: flex;
  }
</style>


<section class="main-section">
  <div class="container-fluid">
    <div class="row">
      <div class="col-6">
        <div class="col-wrapper">
          <div class="image-wrapper">
            <img class="main-logo-image" src="https://cdn.shopify.com/s/files/1/0019/7106/0847/files/logo-black-primary.png?v=1662046585" alt="Kyte Baby in black text with a tilted Kyte logo"/>
          </div>
          <p class="descriptions"><b>Become an Ambassador</b></p>
          <p class="descriptions">Earn Rewards, Discounts and Exclusive Gifts<br>Just by posting with Kyte Baby on Instagram and TikTok.</p>
          <div>
            <div class="apply-button-wrapper">
              <div class="lc-signup-container" 
               data-lc-button-text="Apply Now" 
               data-lc-button-text-color="white" 
               data-lc-button-bg-color="black" 
               data-lc-description="This application is only for Instagram Rewards. To also become a TikTok Ambassador, please apply separately on the TikTok Ambassador page.">
              <script type="text/javascript"  src="https://pub.loudcrowd.com/launch.js?hid=cHJvZ3JhbV9ob3N0OjE3NA" async></script>
              </div>
            </div>
          </div>

        <div class="login-wrapper" style="margin-top: 15px;">
            <p class="descriptions">
              Already a member? <a href="https://creators.loudcrowd.com/s/kyte-baby/login?intent=view-dashboard" class="login-link" style="text-decoration: underline; color: black;">Login</a>
            </p>
        </div>
          
        </div>
      </div>
      <div class="col-6">
        <div class="image-wrapper">
          <img class="image" src="https://cdn.shopify.com/s/files/1/0019/7106/0847/files/main-right-image.png?v=1650697603" alt="Three babies laying don wearing Kyte sleep bags"/>
        </div>
      </div>
    </div>
  </div>
</section>

<section class="reward-section">
  <div class="container">
    <p class="description">Tag @kytebaby on Instagram or TikTok</p>
    <h2 class="heading">Earn Monthly Social Rewards</h2>
    <div class="row">
      <div class="col-4">
        <div class="col-wrapper">
          <h3 class="post-title">1-2 Posts per Month</h3>
          <p class="post-text">500 Points</p>
          <div class="image-wrapper">
            <img class="image" src="https://cdn.shopify.com/s/files/1/0019/7106/0847/files/0.5_tog_Oat_Sleep_Bag_7dea5365-c058-4470-8fe2-f2d93c1ef0e0.jpg?v=1692369891" alt="Baby awake laying down in their crib staring at the camera"/>
          </div>
        </div>
      </div>
      <div class="col-4">
        <div class="col-wrapper">
          <h3 class="post-title">3+ Posts per Month</h3>
          <p class="post-text">1,000 points</p>
          <div class="image-wrapper">
            <img class="image" src="https://cdn.shopify.com/s/files/1/0019/7106/0847/files/reward-2.png?v=1650704408" alt="A happy family wearing matching Kyte smiling"/>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<section class="how-works-section">
  <div class="container">
    <h2 class="heading">How It Works</h2>
    <div class="row">
      <div class="col-4">
        <div class="col-wrapper">
          <div class="image-wrapper">
            <svg class="sign-svg how-work-svg" viewBox="0 0 42 42" fill="none">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M34.1186 4.40507C35.5899 5.57126 36.5959 7.10493 36.9473 8.72681C37.34 10.5373 36.9061 12.3748 35.7281 13.8938L15.6361 39.8046C14.6205 41.1153 13.0934 41.8797 11.4428 41.9018L3.69103 41.9998H3.66922C2.82326 41.9998 2.08881 41.4118 1.89732 40.5788L0.1424 32.9496C-0.230885 31.3253 0.137552 29.6421 1.1556 28.3314L3.79284 24.9284C4.41094 24.1297 5.55503 23.9876 6.34523 24.6173C7.13543 25.242 7.2736 26.3984 6.65307 27.1971L4.01584 30.6001C3.68133 31.0313 3.56014 31.5825 3.68376 32.1166L5.1066 38.3052L11.3967 38.2268C11.9372 38.2195 12.439 37.9696 12.7735 37.5384L32.8654 11.6276C33.3793 10.9661 33.5562 10.2556 33.3963 9.5108C33.2242 8.72436 32.6861 7.93792 31.8765 7.29848L28.5072 4.62557C27.2031 3.59659 25.3634 3.06739 24.1078 4.69172L21.4536 8.11187L25.6664 11.5271C26.4493 12.1592 26.5753 13.318 25.9475 14.1069C25.5888 14.5626 25.0604 14.7978 24.5271 14.7978C24.1296 14.7978 23.7272 14.6655 23.3927 14.3936L19.2114 11.0053L12.3372 19.8717C11.7167 20.6704 10.575 20.8149 9.78236 20.1828C8.99459 19.5581 8.85642 18.4017 9.47452 17.603L21.2451 2.42305C23.5285 -0.526701 27.5279 -0.815797 30.7493 1.73461L34.1186 4.40507ZM24.7247 38.3251H40.1821C41.1856 38.3251 42 39.1482 42 40.1625C42 41.1768 41.1856 42 40.1821 42H24.7247C23.7187 42 22.9067 41.1768 22.9067 40.1625C22.9067 39.1482 23.7187 38.3251 24.7247 38.3251Z" fill="#BFD5D2"/>
            </svg>
          </div>
          <h3 class="post-title">Sign Up</h3>
          <p class="post-text">Sign up with your email and Instagram/TikTok handle(s) on this page</p>
        </div>
      </div>
      <div class="col-4">
        <div class="col-wrapper">
          <div class="image-wrapper">
            <svg class="camera-svg how-work-svg" viewBox="0 0 48 42" fill="none">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M37.1553 7.8294C36.964 7.8294 36.789 7.71752 36.6233 7.37022C35.9607 5.9647 35.1487 4.24685 34.661 3.28653C33.5923 1.18408 31.7583 0.0163161 29.4787 0H18.5073C16.244 0.0163161 14.4077 1.18408 13.339 3.28886C12.8653 4.22121 12.0837 5.87613 11.4327 7.25834L11.295 7.54737C11.2133 7.71985 11.0407 7.8294 10.847 7.8294C5.23301 7.8294 0.666672 12.3909 0.666672 17.9967V31.8327C0.666672 37.4385 5.23301 42 10.847 42H12.5853C13.5513 42 14.3353 41.2168 14.3353 40.2518C14.3353 39.2869 13.5513 38.5037 12.5853 38.5037H10.847C7.16267 38.5037 4.16667 35.5108 4.16667 31.8327V17.9967C4.16667 14.3186 7.16267 11.3257 10.847 11.3257C12.3847 11.3257 13.8033 10.4283 14.459 9.04146L14.5967 8.7501C15.2337 7.39819 15.999 5.78056 16.461 4.86919C16.9393 3.92752 17.581 3.5033 18.5237 3.49631H29.4647C30.4213 3.5033 31.0607 3.92752 31.539 4.86686C32.015 5.80854 32.8107 7.48909 33.5433 9.04146C34.199 10.4283 35.6177 11.3257 37.1553 11.3257C40.8373 11.3257 43.8333 14.3186 43.8333 17.9967V31.8327C43.8333 35.5108 40.8373 38.5037 37.1553 38.5037H20.6867C19.7207 38.5037 18.9367 39.2869 18.9367 40.2518C18.9367 41.2168 19.7207 42 20.6867 42H37.1553C42.767 42 47.3333 37.4385 47.3333 31.8327V17.9967C47.3333 12.3909 42.767 7.8294 37.1553 7.8294ZM24.0019 33.2595C26.4542 33.2595 28.7642 32.2945 30.5072 30.5417C32.2409 28.7982 33.1882 26.5046 33.1672 24.0968C33.1695 21.654 32.2129 19.3488 30.4745 17.6123C28.7385 15.8781 26.4402 14.9248 24.0019 14.9248H23.9972C23.3252 14.9248 22.6532 14.9971 22.0022 15.1462C21.0595 15.3537 20.4645 16.2907 20.6769 17.2324C20.8869 18.1717 21.8202 18.7731 22.7652 18.554C23.1689 18.4654 23.5819 18.4211 23.9995 18.4211H24.0019C25.5069 18.4211 26.9255 19.0132 27.9989 20.0854C29.0769 21.1622 29.6695 22.5841 29.6672 24.1085C29.6789 25.5909 29.0955 26.9987 28.0245 28.0779C26.9442 29.1641 25.5139 29.7632 24.0019 29.7632H23.9972C22.4852 29.7608 21.0642 29.1688 19.9932 28.0942C18.9199 27.0197 18.3319 25.5956 18.3342 24.0805C18.3342 23.5001 18.4252 22.9244 18.6025 22.372C18.8942 21.4536 18.3855 20.47 17.4639 20.1763C16.5375 19.8872 15.5575 20.3907 15.2659 21.3114C14.9812 22.2088 14.8342 23.1365 14.8342 24.0782C14.8295 26.5209 15.7815 28.8261 17.5129 30.5626C19.2442 32.2991 21.5449 33.2571 23.9925 33.2595H24.0019ZM37.9536 18.0883C37.7902 18.2514 37.6036 18.3913 37.3936 18.4845C37.1836 18.5778 36.9502 18.6034 36.7169 18.6034C36.2736 18.6034 35.8302 18.4379 35.4802 18.0883C35.1536 17.762 34.9669 17.3191 34.9669 16.8552C34.9669 16.6431 35.0136 16.41 35.1069 16.2026C35.2002 15.9928 35.3402 15.783 35.4802 15.6199C35.6436 15.4777 35.8536 15.3378 36.0636 15.2446C36.4836 15.0814 36.9736 15.0814 37.3936 15.2446C37.6036 15.3378 37.8136 15.4777 37.9536 15.6199C38.1169 15.783 38.2569 15.9928 38.3502 16.2026C38.4436 16.41 38.4902 16.6431 38.4902 16.8552C38.4902 17.3191 38.3036 17.762 37.9536 18.0883Z" fill="#BFD5D2"/>
            </svg>
          </div>
          <h3 class="post-title">Post On Instagram or TikTok</h3>
          <p class="post-text">Post on Instagram or TikTok. Share a feed post, Reel, or TikTok as you enjoy using your Kyte Baby products. Don't forget to tag <strong>@kytebaby</strong></p>
        </div>
      </div>
      <div class="col-4">
        <div class="col-wrapper">
          <div class="image-wrapper">
            <svg class="star-svg how-work-svg" viewBox="0 0 48 48" fill="none">
              <path d="M36.8284 47.1453C36.5414 47.1523 36.2568 47.08 36.0071 46.9353L24.0184 40.6213L13.4624 46.3053C11.7404 47.2386 9.58444 46.5993 8.65111 44.875C8.63944 44.854 8.62777 44.833 8.61611 44.8096C8.24977 44.0886 8.12144 43.2743 8.24744 42.4763L10.3008 30.357L1.71877 21.6676C0.316439 20.221 0.316439 17.9203 1.71877 16.4713C2.26477 15.8973 2.98811 15.5263 3.77211 15.419L15.5391 13.6316L20.7728 2.72796C21.5871 0.95463 23.6848 0.175296 25.4604 0.98963C26.2304 1.34196 26.8464 1.9603 27.1988 2.72796L32.4558 13.669L44.2624 15.4773C45.2168 15.6196 46.0731 16.147 46.6331 16.9333C47.6784 18.436 47.5198 20.466 46.2551 21.789L37.7128 30.3756L38.9028 36.9066C39.0684 37.8866 38.4221 38.8223 37.4444 39.0066C36.4831 39.1793 35.5638 38.54 35.3911 37.5763L34.1801 31.0453C33.9771 29.8483 34.3574 28.6256 35.2068 27.76L43.7701 19.1126L31.9634 17.281C30.7664 17.0873 29.7514 16.294 29.2731 15.181L24.0184 4.28663L18.6984 15.3536C18.2201 16.469 17.2051 17.26 16.0081 17.4536L4.24111 19.241L12.7414 27.8743C13.5884 28.726 13.9688 29.9323 13.7681 31.1153L11.7148 43.2346L22.2684 37.574C23.3138 36.986 24.5901 36.986 25.6354 37.574L37.6078 43.888C38.3381 44.287 38.7091 45.1293 38.5108 45.939C38.3288 46.7486 37.6148 47.325 36.7864 47.339L36.8284 47.1453Z" fill="#BFD5D2"/>
            </svg>
          </div>
          <h3 class="post-title">Get Rewarded</h3>
          <p class="post-text">Now comes the good part. Unlock the above rewards based on how much you share.</p>
        </div>
      </div>
    </div>
  </div>
</section>

<section class="post-terms-section">
  <div class="container">
    <div class="row">
      <div class="col-6">
        <div class="col-wrapper">
          <h2 class="heading">Post Rules</h2>
          <ul>
            <li class="item">You must tag <strong>@kytebaby</strong> in your post. Tags must be visible and clickable.</li>
            <li class="item">The account you are posting from must be public so we can view your content, and must have previous posts.</li>
            <li class="item">Your posts must clearly show Kyte Baby items, with no other competing logos or brands featured.</li>
            <li class="item">For your post to count toward rewards, it must be organic content (i.e. no screenshots/reposts allowed).</li>
            <li class="item">No more than 3 other tags in the post (excluding people).</li>
            <li class="item">Sleeping baby photos are some of our favorites, however we can only use photos that show a safe sleep environment.</li>
            <li class="item">If your baby is in a crib, the space must be bare and free of all toys, loveys, paci clips, hats, bows, bumpers, blankets or pillows.</li>
            <li class="item">Images of babies in infant nests (SnuggleMe, Dock-A-Tot, etc.) will not be approved.</li>
            <li class="item">If baby is in a car seat, baby swing, bouncer, or high chair, they must be properly buckled in.</li>
            <li class="item">Please note, these rules will still apply if it is not readily apparent that your child is over 1 year old.</li>
            <li class="item">If your child is asleep, they should be photographed in a safe sleep environment that aligns with current national safety recommendations for infant sleep.</li>
            <li class="item">The above standards are in place to ensure the safety of your baby, which is always our highest priority at Kyte Baby.</li>
          </ul>
        </div>
      </div>
      <div class="col-6">
        <div class="col-wrapper">
          <h2 class="heading">Terms & Conditions</h2>
          <ul>
            <li class="item">To be eligible for rewards, you must complete the sign-up on this page and follow the post rules above.</li>
            <li class="item">By tagging us in your content, you give permission for your images to be re-shared for marketing purposes.</li>
            <li class="item">Only one post per day (24 hours) will count towards rewards.</li>
            <li class="item">Each reward tier can only be earned once per month.</li>
            <li class="item">This rewards program may be subject to change at any time.</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
  <div class="container-fluid">
    <div class="row">
      <div class="col-3">
        <div class="image-wrapper">
          <img class="image" src="https://cdn.shopify.com/s/files/1/0019/7106/0847/files/emerald_sage_eucalyptus_sleep_bag-1.jpg?v=1693487879" alt="Three babies sleeping wearing Kyte sleep bags"/>
        </div>
      </div>
      <div class="col-3">
        <div class="image-wrapper">
          <img class="image" src="https://cdn.shopify.com/s/files/1/0019/7106/0847/files/Pregnant_Women_in_Joggers_-_2.jpg?v=1692369890" alt="Three pregnant women smiling wearing Kyte Joggers"/>
        </div>
      </div>
      <div class="col-3">
        <div class="image-wrapper">
          <img class="image" src="https://cdn.shopify.com/s/files/1/0019/7106/0847/files/Fog_matching_family_1_x_1.jpg?v=1692369891" alt="Mother and Father holding their child while standing in the woods wearing Kyte pajamas"/>
        </div>
      </div>
      <div class="col-3">
        <div class="image-wrapper">
          <img class="image" src="https://cdn.shopify.com/s/files/1/0019/7106/0847/files/1402cd_1.0_tog_cloud_sleep_bag_1694d4ca-6074-46f5-ba75-cb77aac4e0ce.jpg?v=1693487879" alt="Baby asleep in a crib wearing a Kyte sleep bag"/>
        </div>
      </div>
    </div>
  </div>
</section>

<section class="faq-section">
  <div class="container">
    <h2 class="heading">Frequently Asked Questions</h2>
    <div class="row">
      <div class="col-12">
        <div class="col-wrapper">
          <div class="top-wrapper">
            <div class="question-wrapper">
              <h5 class="question">How do I get rewarded and when?</h5>
              <svg class="detail-open-arrow" width="25" height="25" viewBox="0 0 16 26" fill="none">
                <path d="M2.75 2.5L13.25 13L2.75 23.5" stroke="#EBD7D5" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div class="bottom-wrapper">
            <div class="answer-wrapper">
              <p class="answer">You will receive your reward on the first business day of the following month for the highest tier that you achieve in a given month.</p>
            </div>
          </div>
        </div>
      </div>
      <div class="col-12">
        <div class="col-wrapper">
          <div class="top-wrapper">
            <div class="question-wrapper">
              <h5 class="question">What are my reward credits eligible towards?</h5>
              <svg class="detail-open-arrow" width="25" height="25" viewBox="0 0 16 26" fill="none">
                <path d="M2.75 2.5L13.25 13L2.75 23.5" stroke="#EBD7D5" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div class="bottom-wrapper">
            <div class="answer-wrapper">
              <p class="answer">All reward credits are single use and redeemable on orders placed at <a href="http://kytebaby.com/">kytebaby.com</a></p>
            </div>
          </div>
        </div>
      </div>
      <div class="col-12">
        <div class="col-wrapper">
          <div class="top-wrapper">
            <div class="question-wrapper">
              <h5 class="question">Can I announce this on my social media?</h5>
              <svg class="detail-open-arrow" width="25" height="25" viewBox="0 0 16 26" fill="none">
                <path d="M2.75 2.5L13.25 13L2.75 23.5" stroke="#EBD7D5" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div class="bottom-wrapper">
            <div class="answer-wrapper">
              <p class="answer">There’s no need to make an announcement post. If you’d like to let your audience know about the program, feel free to use the hashtag #kytebabyambassador and/or add it to your bio! </p>
            </div>
          </div>
        </div>
      </div>
      <div class="col-12">
        <div class="col-wrapper">
          <div class="top-wrapper">
            <div class="question-wrapper">
              <h5 class="question">What happens if I post more than 3 times per month?</h5>
              <svg class="detail-open-arrow" width="25" height="25" viewBox="0 0 16 26" fill="none">
                <path d="M2.75 2.5L13.25 13L2.75 23.5" stroke="#EBD7D5" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div class="bottom-wrapper">
            <div class="answer-wrapper">
              <p class="answer">Ambitious! We love it! For monthly rewards, you will only earn points for the highest of the two tiers that you reach, regardless of whether you post more than 3 times. The max number of points you can earn in a given month is 1,000 for 3 or more posts. As the Kyte Baby Ambassador Program grows, we’ll be looking to add new rewards, challenges, and other exciting surprises so stay tuned.</p>
            </div>
          </div>
        </div>
      </div>
      <div class="col-12">
        <div class="col-wrapper">
          <div class="top-wrapper">
            <div class="question-wrapper">
              <h5 class="question">Are Instagram stories included? What about IGTVs and Reels?</h5>
              <svg class="detail-open-arrow" width="25" height="25" viewBox="0 0 16 26" fill="none">
                <path d="M2.75 2.5L13.25 13L2.75 23.5" stroke="#EBD7D5" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div class="bottom-wrapper">
            <div class="answer-wrapper">
              <p class="answer">Only in-feed Instagram posts, Reels, and TikToks are currently eligible for rewards. While you’re more than welcome to share additional content, these posts will not currently count towards the program.</p>
            </div>
          </div>
        </div>
      </div>
      <div class="col-12">
        <div class="col-wrapper">
          <div class="top-wrapper">
            <div class="question-wrapper">
              <h5 class="question">What if I didn't receive my reward?</h5>
              <svg class="detail-open-arrow" width="25" height="25" viewBox="0 0 16 26" fill="none">
                <path d="M2.75 2.5L13.25 13L2.75 23.5" stroke="#EBD7D5" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div class="bottom-wrapper">
            <div class="answer-wrapper">
              <p class="answer">Please contact <NAME_EMAIL>.</p>
            </div>
          </div>
        </div>
      </div>
      <div class="col-12">
        <div class="col-wrapper">
          <div class="top-wrapper">
            <div class="question-wrapper">
              <h5 class="question">How do I ensure my post is valid?</h5>
              <svg class="detail-open-arrow" width="25" height="25" viewBox="0 0 16 26" fill="none">
                <path d="M2.75 2.5L13.25 13L2.75 23.5" stroke="#EBD7D5" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div class="bottom-wrapper">
            <div class="answer-wrapper">
              <p class="answer">Ensure you are posting from a public account, tagging the correct brand, and following the post guidelines.</p>
            </div>
          </div>
        </div>
      </div>
      <div class="col-12">
        <div class="col-wrapper">
          <div class="top-wrapper">
            <div class="question-wrapper">
              <h5 class="question">Can I blur or cover up my child’s face in my posts?</h5>
              <svg class="detail-open-arrow" width="25" height="25" viewBox="0 0 16 26" fill="none">
                <path d="M2.75 2.5L13.25 13L2.75 23.5" stroke="#EBD7D5" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div class="bottom-wrapper">
            <div class="answer-wrapper">
              <p class="answer">Yes, your posts will still qualify toward the program if you blur or cover your child’s face.</p>
            </div>
          </div>
        </div>
      </div>            
      <div class="col-12">
        <div class="col-wrapper">
          <div class="top-wrapper">
            <div class="question-wrapper">
              <h5 class="question">Are there order minimums required to use the rewards?</h5>
              <svg class="detail-open-arrow" width="25" height="25" viewBox="0 0 16 26" fill="none">
                <path d="M2.75 2.5L13.25 13L2.75 23.5" stroke="#EBD7D5" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div class="bottom-wrapper">
            <div class="answer-wrapper">
              <p class="answer">Currently, there are no order minimums, but we reserve the right to update the rewards program at any time. When you complete a rewards tier, you will receive an email confirming your reward on the first business day of the following month, and your rewards balance will be updated in your account.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<script>
  $(document).ready ( function () {
    var video = $('.tiktok-reward-section .video');
    $(video).mouseenter(function () {
      $(this).get(0).play();
    }).mouseleave(function () {
      $(this).get(0).pause();
    })

    $('.video').each(function (){
      this.onplay = function() {
        $('.video').not(this).each(function (){
          $(this).get(0).pause();
        });
      };
    })

    resizeFun ();

    $(window).resize( function () {
      resizeFun ();
    });

    $(document).on('click', '.top-wrapper', function () {
      if (!$(this).parent().hasClass('top-click')) {
        resizeFun ();
      }
      $(this).parent().toggleClass('top-click').parents().siblings().children().removeClass('top-click');
    });
  });
  
  function resizeFun () {
    $('.answer-wrapper').each(function( index ) {
      var contentHeight = $(this).innerHeight();
      $(this).parent('.bottom-wrapper').css('height', contentHeight)
    });
  }
</script>