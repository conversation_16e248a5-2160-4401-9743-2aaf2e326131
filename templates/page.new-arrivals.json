/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-page",
      "disabled": true,
      "settings": {
        "show_title": true,
        "page_width": "small"
      }
    },
    "d0b79857-dc88-4a29-9ac3-4ca14d217278": {
      "type": "rich-text",
      "disabled": true,
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "",
        "subheading": "",
        "title": "",
        "content": "<p>Due to the warehouse move orders may take up to 2 weeks to be fulfilled</p>",
        "button_text": "",
        "button_link": "",
        "button_style": "",
        "button_icon": "",
        "background_type": "full_width",
        "text_width": "medium",
        "text_position": "center",
        "text_alignment": "center",
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)"
      }
    },
    "aa9424f1-28e9-4ba0-8d5a-a312a608d54b": {
      "type": "image-with-text-overlay",
      "settings": {
        "reveal_on_scroll": true,
        "section_height": "medium",
        "image": "shopify://shop_images/DSC_9893.jpg",
        "subheading": "Shop the latest",
        "title": "New Arrivals",
        "content": "",
        "button_text": "",
        "button_link": "shopify://collections/new-arrivals",
        "link_style": "link",
        "background_type": "full_width",
        "text_width": "medium",
        "text_position": "middle_left",
        "subheading_color": "#ffffff",
        "text_color": "#ffffff",
        "button_background": "#ffffff",
        "button_text_color": "#000000",
        "overlay_color": "#000000",
        "overlay_opacity": 15
      }
    },
    "92064f81-3f36-4e2b-8a51-cae2de3df8ab": {
      "type": "multi-column",
      "blocks": {
        "9ed83fac-0002-4d73-b561-70f4e4686a6c": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/SkiinZipperRomper-03.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Ski",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/ski",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_YYfwyQ": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Puffin_in_Youth_Blanket-02.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Puffin",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/puffin",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "template--15124002242671__92064f81-3f36-4e2b-8a51-cae2de3df8ab-167815169828c3d2e8-2": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Holiday_4_Group_Photo-02.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Holiday",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/holiday-collections",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "template--15124002242671__92064f81-3f36-4e2b-8a51-cae2de3df8ab-167815169828c3d2e8-3": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Gilmore_Girls_in_Zipper_Footie-03.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Gilmore Girls x Kyte Baby",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/gilmore-girls-x-kyte-baby",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        }
      },
      "block_order": [
        "9ed83fac-0002-4d73-b561-70f4e4686a6c",
        "item_YYfwyQ",
        "template--15124002242671__92064f81-3f36-4e2b-8a51-cae2de3df8ab-167815169828c3d2e8-2",
        "template--15124002242671__92064f81-3f36-4e2b-8a51-cae2de3df8ab-167815169828c3d2e8-3"
      ],
      "settings": {
        "reveal_on_scroll": true,
        "stack_items": false,
        "subheading": "",
        "title": "",
        "content": "",
        "column_alignment": "center",
        "mobile_item_size": "medium",
        "desktop_item_size": "large",
        "spacing": "normal",
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)"
      }
    },
    "f04bd254-002a-4d9a-8567-9cab78ae3eb0": {
      "type": "custom-featured-collections",
      "blocks": {
        "template--15124002242671__f04bd254-002a-4d9a-8567-9cab78ae3eb0-16781559096edff449-0": {
          "type": "collection",
          "settings": {
            "collection": "ski",
            "label": "",
            "collection_heading": "",
            "collection_description": "",
            "text_width": "medium",
            "text_position": "center",
            "text_alignment": "center",
            "text_style": "text--large",
            "button_text": "",
            "button_icon": "",
            "button_style": "",
            "button_size": "",
            "button_url": "",
            "video_filename": "",
            "video_url": "",
            "image": "shopify://shop_images/SkiinSLeepBag-02.jpg",
            "image_position": "",
            "link_url": "",
            "overlay_subheading": "",
            "overlay_title": "Time to hit the slopes!",
            "overlay_link_text": "",
            "overlay_text": "",
            "overlay_color": "rgba(0,0,0,.2)",
            "text_color": "#ffffff"
          }
        }
      },
      "block_order": [
        "template--15124002242671__f04bd254-002a-4d9a-8567-9cab78ae3eb0-16781559096edff449-0"
      ],
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "",
        "aspect_ratio": "",
        "show_additional_text_fields": true,
        "product_in_collection": false,
        "hide_markets": "",
        "horizontal_header": false,
        "horizontal_layout": true,
        "bottom_border": true,
        "content_style": "text--large",
        "button_text": "Shop All",
        "button_icon": "nav-arrow-right",
        "button_style": "button--secondary",
        "button_size": "button--small",
        "button_url": "shopify://collections/ski",
        "subheading": "",
        "title": "Ski",
        "content": "",
        "products_count": 8,
        "products_per_row": 4,
        "stack_products": false,
        "show_cta": false,
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)"
      }
    },
    "4161f847-d076-4a63-ab52-5634ed62cfad": {
      "type": "custom-featured-collections",
      "blocks": {
        "template--15124002242671__4161f847-d076-4a63-ab52-5634ed62cfad-16781559096edff449-0": {
          "type": "collection",
          "settings": {
            "collection": "puffin",
            "label": "",
            "collection_heading": "",
            "collection_description": "",
            "text_width": "medium",
            "text_position": "center",
            "text_alignment": "center",
            "text_style": "text--large",
            "button_text": "",
            "button_icon": "",
            "button_style": "",
            "button_size": "",
            "button_url": "",
            "video_filename": "",
            "video_url": "",
            "image": "shopify://shop_images/Puffin_in_Zipper_Footie-03.jpg",
            "image_position": "",
            "link_url": "",
            "overlay_subheading": "",
            "overlay_title": "Fly out to sea with Puffin",
            "overlay_link_text": "",
            "overlay_text": "",
            "overlay_color": "rgba(0,0,0,.2)",
            "text_color": "#ffffff"
          }
        }
      },
      "block_order": [
        "template--15124002242671__4161f847-d076-4a63-ab52-5634ed62cfad-16781559096edff449-0"
      ],
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "",
        "aspect_ratio": "",
        "show_additional_text_fields": true,
        "product_in_collection": false,
        "hide_markets": "",
        "horizontal_header": false,
        "horizontal_layout": true,
        "bottom_border": true,
        "content_style": "text--large",
        "button_text": "Shop All",
        "button_icon": "nav-arrow-right",
        "button_style": "button--secondary",
        "button_size": "button--small",
        "button_url": "shopify://collections/puffin",
        "subheading": "",
        "title": "Puffin",
        "content": "",
        "products_count": 8,
        "products_per_row": 4,
        "stack_products": false,
        "show_cta": false,
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)"
      }
    },
    "67ddeb8f-f107-412b-8d44-4e8ff1fe911b": {
      "type": "custom-featured-collections",
      "blocks": {
        "template--15124002242671__67ddeb8f-f107-412b-8d44-4e8ff1fe911b-16781559096edff449-0": {
          "type": "collection",
          "settings": {
            "collection": "holiday-collections",
            "label": "",
            "collection_heading": "",
            "collection_description": "",
            "text_width": "medium",
            "text_position": "center",
            "text_alignment": "center",
            "text_style": "text--large",
            "button_text": "",
            "button_icon": "",
            "button_style": "",
            "button_size": "",
            "button_url": "",
            "video_filename": "",
            "video_url": "",
            "image": "shopify://shop_images/Holiday_1_Footies-06.jpg",
            "image_position": "",
            "link_url": "",
            "overlay_subheading": "",
            "overlay_title": "Soft, cozy, and oh-so-merry",
            "overlay_link_text": "",
            "overlay_text": "",
            "overlay_color": "rgba(0,0,0,.2)",
            "text_color": "#ffffff"
          }
        }
      },
      "block_order": [
        "template--15124002242671__67ddeb8f-f107-412b-8d44-4e8ff1fe911b-16781559096edff449-0"
      ],
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "",
        "aspect_ratio": "",
        "show_additional_text_fields": true,
        "product_in_collection": false,
        "hide_markets": "",
        "horizontal_header": false,
        "horizontal_layout": true,
        "bottom_border": true,
        "content_style": "text--large",
        "button_text": "Shop All",
        "button_icon": "nav-arrow-right",
        "button_style": "button--secondary",
        "button_size": "button--small",
        "button_url": "shopify://collections/holiday-collections",
        "subheading": "",
        "title": "Holiday Collections",
        "content": "",
        "products_count": 8,
        "products_per_row": 4,
        "stack_products": false,
        "show_cta": false,
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)"
      }
    },
    "4667788d-733d-41bb-90f4-7cf93c8888c5": {
      "type": "custom-featured-collections",
      "blocks": {
        "template--15124002242671__4667788d-733d-41bb-90f4-7cf93c8888c5-16781559096edff449-0": {
          "type": "collection",
          "settings": {
            "collection": "gilmore-girls-x-kyte-baby",
            "label": "",
            "collection_heading": "",
            "collection_description": "",
            "text_width": "medium",
            "text_position": "center",
            "text_alignment": "center",
            "text_style": "text--large",
            "button_text": "",
            "button_icon": "",
            "button_style": "",
            "button_size": "",
            "button_url": "",
            "video_filename": "",
            "video_url": "",
            "image": "shopify://shop_images/Gilmore_Girls_Group-09_4023171d-19c9-4fe7-8988-724fdfa80a4a.jpg",
            "image_position": "",
            "link_url": "",
            "overlay_subheading": "",
            "overlay_title": "Cozy, classic + totally binge-worthy",
            "overlay_link_text": "",
            "overlay_text": "",
            "overlay_color": "rgba(0,0,0,.2)",
            "text_color": "#ffffff"
          }
        }
      },
      "block_order": [
        "template--15124002242671__4667788d-733d-41bb-90f4-7cf93c8888c5-16781559096edff449-0"
      ],
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "",
        "aspect_ratio": "",
        "show_additional_text_fields": true,
        "product_in_collection": false,
        "hide_markets": "",
        "horizontal_header": false,
        "horizontal_layout": true,
        "bottom_border": true,
        "content_style": "text--large",
        "button_text": "Shop All",
        "button_icon": "nav-arrow-right",
        "button_style": "button--secondary",
        "button_size": "button--small",
        "button_url": "shopify://collections/gilmore-girls-x-kyte-baby",
        "subheading": "",
        "title": "Gilmore Girls x Kyte Baby",
        "content": "",
        "products_count": 8,
        "products_per_row": 4,
        "stack_products": false,
        "show_cta": false,
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)"
      }
    },
    "8a1f39b6-32c6-415f-8418-96b84ff3793b": {
      "type": "rich-text",
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "",
        "subheading": "",
        "title": "",
        "content": "",
        "button_text": "Shop All New Arrivals",
        "button_link": "shopify://collections/new-arrivals",
        "button_style": "button--primary",
        "button_icon": "",
        "background_type": "full_width",
        "text_width": "medium",
        "text_position": "center",
        "text_alignment": "center",
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)"
      }
    },
    "dd9c0dd6-0deb-4701-9e40-39b47b306b13": {
      "type": "multi-column",
      "blocks": {
        "template--15124002242671__dd9c0dd6-0deb-4701-9e40-39b47b306b13-16779910284c807b57-0": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/KB_Dec-Post_2.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "",
            "content": "",
            "link_text": "december calendar",
            "link_url": "shopify://blogs/news/december-2025-launch-calendar-overview",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "template--15124002242671__dd9c0dd6-0deb-4701-9e40-39b47b306b13-16779910284c807b57-1": {
          "type": "item",
          "disabled": true,
          "settings": {
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Column 1",
            "content": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>",
            "link_text": "",
            "link_url": "",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "template--15124002242671__dd9c0dd6-0deb-4701-9e40-39b47b306b13-16779910284c807b57-2": {
          "type": "item",
          "disabled": true,
          "settings": {
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Column 1",
            "content": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>",
            "link_text": "",
            "link_url": "",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "template--15124002242671__dd9c0dd6-0deb-4701-9e40-39b47b306b13-16779910284c807b57-3": {
          "type": "item",
          "disabled": true,
          "settings": {
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Column 1",
            "content": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>",
            "link_text": "",
            "link_url": "",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        }
      },
      "block_order": [
        "template--15124002242671__dd9c0dd6-0deb-4701-9e40-39b47b306b13-16779910284c807b57-0",
        "template--15124002242671__dd9c0dd6-0deb-4701-9e40-39b47b306b13-16779910284c807b57-1",
        "template--15124002242671__dd9c0dd6-0deb-4701-9e40-39b47b306b13-16779910284c807b57-2",
        "template--15124002242671__dd9c0dd6-0deb-4701-9e40-39b47b306b13-16779910284c807b57-3"
      ],
      "settings": {
        "reveal_on_scroll": true,
        "stack_items": false,
        "subheading": "",
        "title": "Upcoming Launches",
        "content": "",
        "column_alignment": "center",
        "mobile_item_size": "large",
        "desktop_item_size": "medium",
        "spacing": "normal",
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)"
      }
    }
  },
  "order": [
    "main",
    "d0b79857-dc88-4a29-9ac3-4ca14d217278",
    "aa9424f1-28e9-4ba0-8d5a-a312a608d54b",
    "92064f81-3f36-4e2b-8a51-cae2de3df8ab",
    "f04bd254-002a-4d9a-8567-9cab78ae3eb0",
    "4161f847-d076-4a63-ab52-5634ed62cfad",
    "67ddeb8f-f107-412b-8d44-4e8ff1fe911b",
    "4667788d-733d-41bb-90f4-7cf93c8888c5",
    "8a1f39b6-32c6-415f-8418-96b84ff3793b",
    "dd9c0dd6-0deb-4701-9e40-39b47b306b13"
  ]
}
