{% layout none %}
<html>
    <head>
<!-- Start of Shoplift scripts -->
{% render 'shoplift' %}
<!-- End of Shoplift scripts -->

        <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=0"/>
    </head>
    <body>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script id="gorgias-chat-widget-install-v2" src="https://config.gorgias.chat/gorgias-chat-bundle-loader.js?applicationId=12328"></script>

<script>
GorgiasChat.init().then(function() {
    GorgiasChat.open();
var url = new URL(window.location.href);
var c = url.searchParams.get("customer_email");
if(c!='REPLACE_CUSTOMER_EMAIL'){
    GorgiasChat.captureUserEmail('<EMAIL>');
}
// console.log(c);
  var ccc = setInterval(function(){
                // console.log($('#chat-window').length);
    try {
    
            if($('#chat-window').length) {
                                // console.log("32");
                var chatIframe = $("#chat-window");
                var closeButton = chatIframe.contents().find('.close-handle');
                if(closeButton) {
                                //   console.log("36");
                    closeButton.hide(); 
                    //clearInterval(ccc);
                }
              }
    
    } catch(err){}; 
    try {
      var functionName = "openProductDetails";
      if(window && window.AndroidBridge && window.AndroidBridge.hasOwnProperty(functionName)){
        //android
        //message-window-iframe-if2eeh
        if($('#chat-window').length) {
                                // console.log("32");
                var chatIframe = $("#chat-window");
                var uploadButton = chatIframe.contents().find('.message-window-iframe-if2eeh');
                if(uploadButton) {
                                //   console.log("36");
                    uploadButton.hide(); 
                    //clearInterval(ccc);
                }
              }
      }
    } catch(e) {
      console.log("I got clicked android error");
      console.log(e);
    }
    
  }, 1000);
})
</script>
    </body>
</html>