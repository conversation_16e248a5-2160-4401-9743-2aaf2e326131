{% layout none %}
<!DOCTYPE html>
<html>
    <head>
<!-- Start of Shoplift scripts -->
{% render 'shoplift' %}
<!-- End of Shoplift scripts -->

      <meta name="viewport" content="width=device-width, initial-scale=1.0">

        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" integrity="sha384-1BmE4kWBq78iYhFldvKuhfTAU6auU8tT94WrHftjDbrCEXSU1oBoqyl2QvZ6jIW3" crossorigin="anonymous">
        <title>Delivery Location</title>

    </head>
    <body>
        <div class="container-fluid" id="main-container">
            <div class="row">
                <div class="col-sm col-md-12">
                    <div class="row">
                        <div class="col-sm col-md-12">
                            <div id="errorMessage"></div>
                            <div id="logss" style="display: none !important;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js" integrity="sha384-QJHtvGhmr9XOIpI6YVutG+2QOK9T+ZnN4kzFN1RtK3zEFEIsxhlmWl5/YESvpZ13" crossorigin="anonymous"></script>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-ka7Sk0Gln4gmtz2MlQnikT1wXgYsOg+OMhuP+IlRH9sENBO0LRn5q+8nbTov4+1p" crossorigin="anonymous"></script>
<!--        <script type="text/javascript" src="https://d3myyafggcycom.cloudfront.net/assets/js/PlobalBridge.js"></script> -->
      <script>
        (function(){
    window.PlobalBridge = {
        openURL:function(url,title,open_links_in){
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.openURL){
                    window.webkit.messageHandlers.openURL.postMessage({'type':open_links_in,'url':url,'title':title});
                }
            } catch(e) {
                console.log("ios error openURL");
                console.log(e);
            }
            try {
                if(window && window.AndroidBridge && window.AndroidBridge.openURL){
                    window.AndroidBridge.openURL(open_links_in,url,title);
                }
            } catch(e) {
                console.log("android error openURL");
                console.log(e);
            }
            if(!window.webkit && !window.AndroidBridge){
                window.location.assign(url);
            }
        },
        openURLNew:function(data){
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.openURLNew){
                    window.webkit.messageHandlers.openURLNew.postMessage(data);
                }
            } catch(e) {
                console.log("ios error openURLNew");
                console.log(e);
            }
            try {
                if(window && window.AndroidBridge && window.AndroidBridge.openURLNew){
                    window.AndroidBridge.openURLNew(JSON.stringify(data));
                }
            } catch(e) {
                console.log("android error openURLNew");
                console.log(e);
            }
            if(!window.webkit && !window.AndroidBridge){
                window.location.assign(data.url);
            }
            this.openURL(data.url,data.title,data.type);
        },
        openURLNewIOS:function(data){
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.openURLNew){
                    window.webkit.messageHandlers.openURLNew.postMessage(data);
                }
            } catch(e) {
                console.log("ios error openURLNew");
                console.log(e);
            }
            
            if(!window.webkit){
                window.location.assign(data.url);
            }
            this.openURL(data.url,data.title,data.type);
        },
        redirectTo:function(app_feature_id){
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.redirectTo){
                    window.webkit.messageHandlers.redirectTo.postMessage({'app_feature_id':app_feature_id});
                }
            } catch(e) {
                console.log("ios error redirectTo");
                console.log(e);
            }
            try {
                if(window && window.AndroidBridge && window.AndroidBridge.redirectTo){
                    window.AndroidBridge.redirectTo(JSON.stringify({'app_feature_id':app_feature_id}));
                }
            } catch(e) {
                console.log("android error redirectTo");
                console.log(e);
            }
            if(!window.webkit && !window.AndroidBridge){
                alert(app_feature_id);
            }
        },
        adjustWebViewHeight:function(container, adjustHeight = 25){
            if(!container){
                var height = document.body.clientHeight;
            }else if(container == 0){
                var height = 0;
            }
            else if(container == -25){
                var height = -25;
            }else{
                var height = document.getElementById(container).clientHeight;
            }
            height += adjustHeight;
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.adjustWebViewHeight){
                    window.webkit.messageHandlers.adjustWebViewHeight.postMessage(height);
                }
            } catch(e) {
                console.log("ios error adjustWebViewHeight");
                console.log(e);
            }
            if(!window.webkit && !window.AndroidBridge){
                console.log('adjustWebViewHeight  '+height);
            }
        },
        addLineItemProperty:function(obj){
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.addLineItemProperty){
                    window.webkit.messageHandlers.addLineItemProperty.postMessage(obj);
                }
            } catch(e) {
                console.log("ios error addLineItemProperty");
                console.log(e);
            }
            try {
                if(window && window.AndroidBridge && window.AndroidBridge.addLineItemProperty){
                    window.AndroidBridge.addLineItemProperty(JSON.stringify(obj));
                }
            } catch(e) {
                console.log("android error addLineItemProperty");
                console.log(e);
            }
            // if(!window.webkit && !window.AndroidBridge){
            //     console.log('addLineItemProperty  '+JSON.stringify(obj));
            // }
            console.log('addLineItemProperty  '+JSON.stringify(obj));
        },
        removeLineItemProperty:function(key){
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.removeLineItemProperty){
                    window.webkit.messageHandlers.removeLineItemProperty.postMessage(key);
                }
            } catch(e) {
                console.log("ios error addLineItemProperty");
                console.log(e);
            }
            try {
                if(window && window.AndroidBridge && window.AndroidBridge.removeLineItemProperty){
                    window.AndroidBridge.removeLineItemProperty(key);
                }
            } catch(e) {
                console.log("android error removeLineItemProperty");
                console.log(e);
            }
            if(!window.webkit && !window.AndroidBridge){
                console.log('removeLineItemProperty  '+key);
            }
        },
        setAddToCartBehaviour:function(obj){
            console.log("setAddToCartBehaviour");
            console.log(obj);
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.setAddToCartBehaviour){
                    window.webkit.messageHandlers.setAddToCartBehaviour.postMessage(obj);
                }
            } catch(e) {
                console.log("ios error setAddToCartBehaviour");
                console.log(e);
            }
            try {
                if(window && window.AndroidBridge && window.AndroidBridge.setAddToCartBehaviour){
                    window.AndroidBridge.setAddToCartBehaviour(JSON.stringify(obj));
                }
            } catch(e) {
                console.log("android error setAddToCartBehaviour");
                console.log(e);
            }
            if(!window.webkit && !window.AndroidBridge){
                console.log('setAddToCartBehaviour  '+JSON.stringify(obj));
            }
        },
        setBuyNowBehaviour:function(obj){
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.setBuyNowBehaviour){
                    window.webkit.messageHandlers.setBuyNowBehaviour.postMessage(obj);
                }
            } catch(e) {
                console.log("ios error setBuyNowBehaviour");
                console.log(e);
            }
            try {
                if(window && window.AndroidBridge && window.AndroidBridge.setBuyNowBehaviour){
                    window.AndroidBridge.setBuyNowBehaviour(JSON.stringify(obj));
                }
            } catch(e) {
                console.log("android error setBuyNowBehaviour");
                console.log(e);
            }
            if(!window.webkit && !window.AndroidBridge){
                console.log('setBuyNowBehaviour  '+JSON.stringify(obj));
            }
        },
        changeDisplayInfo:function(data){
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.changeDisplayInfo){
                    window.webkit.messageHandlers.changeDisplayInfo.postMessage(data);
                }
            } catch(e) {
                console.log("ios error changeDisplayInfo");
                console.log(e);
            }
            try {
                if(window && window.AndroidBridge && window.AndroidBridge.changeDisplayInfo){
                    window.AndroidBridge.changeDisplayInfo(JSON.stringify(data));
                }
            } catch(e) {
                console.log("android error changeDisplayInfo");
                console.log(e);
            }
            if(!window.webkit && !window.AndroidBridge){
                console.log('changeDisplayInfo '+JSON.stringify(data));
            }
        },
        addCustomerProperty:function(key,value){
            console.log(key,value);
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.addCustomerProperty){
                    window.webkit.messageHandlers.addCustomerProperty.postMessage({'key':key,'value':value});
                }
            } catch(e) {
                console.log("ios error addCustomerProperty");
                console.log(e);
            }
            try {
                if(window && window.AndroidBridge && window.AndroidBridge.addCustomerProperty){
                    window.AndroidBridge.addCustomerProperty(key,value);
                }
            } catch(e) {
                console.log("android error addCustomerProperty");
                console.log(e);
            }
            if(!window.webkit && !window.AndroidBridge){
                console.log('addCustomerProperty '+key+' '+ value);
            }
        },
        addToCart:function(variant){
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.addToCart){
                    window.webkit.messageHandlers.addToCart.postMessage(variant);
                }
            } catch(e) {
                console.log("ios error addToCart");
                console.log(e);
            }
            try {
                if(window && window.AndroidBridge && window.AndroidBridge.addToCart){
                    window.AndroidBridge.addToCart(JSON.stringify(variant));
                }
            } catch(e) {
                console.log("android error addToCart");
                console.log(e);
            }
            if(!window.webkit && !window.AndroidBridge){
                console.log('addToCart '+JSON.stringify(variant));
            }
        },
        changeAddToCartBehaviour:function(rules){

        },
        dataReceiver:function(data){
            console.log('dataReceiver');
        },
        requestData:function(data){
            console.log('requestData');
        },
        shareData:function(data){
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.shareData){
                    window.webkit.messageHandlers.shareData.postMessage({'data':data});
                }
            } catch(e) {
                console.log("ios error shareData");
                console.log(e);
            }
            try {
                if(window && window.AndroidBridge && window.AndroidBridge.shareData){
                    window.AndroidBridge.shareData(data);
                }
            } catch(e) {
                console.log("android error shareData");
                console.log(e);
            }
            if(!window.webkit && !window.AndroidBridge){
                console.log('shareData '+data);
            }
        },
        openFileChooser:function(){
            console.log('openFileChooser');
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.openFileChooser){
                    window.webkit.messageHandlers.openFileChooser.postMessage("");
                }
            } catch(e) {
                console.log("ios error openFileChooser");
                console.log(e);
            }
            try {
                if(window && window.AndroidBridge && window.AndroidBridge.openFileChooser){
                    window.AndroidBridge.openFileChooser();
                }
            } catch(e) {
                console.log("android error openFileChooser");
                console.log(e);
            }
            if(!window.webkit && !window.AndroidBridge){
                console.log('openFileChooser');
            }
        },
        callBridgeFunction:function(functionName, paramsIos, paramAndroid){
          console.log("functionName", functionName);
                console.log("paramsIos",paramsIos);
                console.log("paramAndroid",paramAndroid);
            try {
                if(window && window.AndroidBridge){
                    if(paramAndroid){
                        window.AndroidBridge[functionName](paramAndroid);
                    }
                    else{
                        window.AndroidBridge[functionName]();
                    }

                }
            } catch(e) {
                console.log("I got clicked android error");
                console.log(e);
            }

            try{
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers[functionName]){
                    if(paramsIos){
                        window.webkit.messageHandlers[functionName].postMessage(paramsIos);
                    }
                    else{
                        window.webkit.messageHandlers[functionName].postMessage();
                    }
                }
            }
            catch(e){
                console.log("I got clicked ios error");
                console.log(e);
            }
        },
        openProductDetail:function(product_id, title) {
            var jsonToSend ={
                product_id: product_id.toString(),
                title: title
            };

            this.callBridgeFunction('openProductDetails', jsonToSend, JSON.stringify(jsonToSend));
        },
        openCollection: function (collection_id, title) {
            var jsonToSend ={
                collection_id: collection_id.toString(),
                title: title
            };

            this.callBridgeFunction('openCollection', jsonToSend, JSON.stringify(jsonToSend));
        },
        addToCartNew:function(variantID){
             this.callBridgeFunction('addToCart', variantID.toString(), variantID.toString());
        },
        openCart:function(event) {
            event.stopPropagation();
            this.callBridgeFunction('openCart');
        },
        operate:{
            '+': function(a, b) { return a + b },
            '<': function(a, b) { return a < b },
            '=': function(a, b) { return a < b },
            'equal': function(a, b) { return a == b; },
            'not_equal': function(a, b) { return a != b; }
        },
        isJson:function(str) {
            try {
                JSON.parse(str);
            } catch (e) {
                return false;
            }
            return true;
        },
        validateData:function(obj){
          console.log("validateData");
                console.log(obj);
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.validateData){
                    window.webkit.messageHandlers.validateData.postMessage(obj);
                }
            } catch(e) {
                console.log("ios error validateData");
                console.log(e);
            }
            try {
                if(window && window.AndroidBridge && window.AndroidBridge.validateData){
                    window.AndroidBridge.validateData(JSON.stringify(obj));
                }
            } catch(e) {
                console.log("android error validateData");
                console.log(e);
            }
            // if(!window.webkit && !window.AndroidBridge){
            //
            // }
            console.log('validateData '+JSON.stringify(obj));
        },
        triggerEvent:function(el, event) {
            if ("createEvent" in document) {
                var evt = document.createEvent("HTMLEvents");
                evt.initEvent(event, false, true);
                el.dispatchEvent(evt);
            }
            else
                el.fireEvent(event);
        }
    };
})();
      </script>
        <script type="text/javascript">
            window.pinCodeJsonData = {
              "devlivery_settings": {
                "setup_level": "store", // "store"/"product",
                "pin_code_type" : "exact_match",// "regex"/ "exact_match"
                "available_pin_codes": [
                  
                ]
              }
            };
            $(document).ready(function(){
                // var jsonData = {"json_data":{"customer_details":{"first_Name":"Akshay","shipping_address":{"phone":"9028216523","city":"Sydney","country":"Australia","firstName":"Abhay","province":"New South Wales","company":"Plobal","lastName":"Kumar","identifier":"Z2lkOi8vc2hvcGlmeS9NYWlsaW5nQWRkcmVzcy83NDQzNzIwNjM0NTI3P21vZGVsX25hbWU9Q3VzdG9tZXJBZGRyZXNzJmN1c3RvbWVyX2FjY2Vzc190b2tlbj1zZ1lGSnlEUFdsRDZnWHg0eGFhUy1lOTFZaG1GLWNaVkdLYjZ2RnlfRkpBOE9WV0NEcmRfdXk3NldCaFVjOFZQd3lMN0g0RWVtR0NQU1R5aS1hSEsySTZ5UkN5cFIyN1BnTmJySzJ5UE9fYkdmSWxYY3hLZjN0RHFqWDZTaERtYXhpYU1QTGE2a3ZBcXBkRzVrbDByNU1IMkhHV2JfV1BVV2JReG8yUk93QS1xNUJhcmg0eWdiMDNBdWlGVklNOFAwZVUtUXpMekhjMFhXMjdGM3pXeXlrNTF0LUpwYVhwMVZJZ0VXVXFYOUZqcXI1QWxrVzhuRUJKTDg0SmJXeWp6","zip":"2000","address1":"Xyz street","address2":""},"last_Name":"Support","id":"5823699058847","last_name":"Support","email":"<EMAIL>","tags":[],"first_name":"Akshay"},"cart":{"item_count":"1","items":[{"boldExtraPrice":"","sizeChartUrl":"","optionList":[{"isRequired":false,"isBoldApp":false,"name":"Potency","position":"0","isEnabled":false},{"isRequired":false,"isBoldApp":false,"name":"Size","position":"1","isEnabled":false}],"title":"Dr. Willmar Schwabe India Belladonna Dilution","isBoldOptionOutOfStock":false,"tags":"","isAllDetailsAvailable":false,"published_at":"2021-10-13 10:16:30 +0000","selected_option_index":0,"updated_at":"","isOutOfStock":false,"vendor":"","boldMinimumQtyAvailable":"","share_url":"http://www.distacart.com/products/dr-willmar-schwabe-india-belladonna-dilution","selected_variant_id":"39595901354143","variant_id":"39595901354143","isOptionsAvailable":true,"collection_ids":"","variantList":[{"imageInfoArray":[{"src":"https://cdn.shopify.com/s/files/1/1857/6931/products/<EMAIL>?v=1621418659","image_id":"28426819928223"}],"position":0,"taxable":false,"original_line_price":5.96999979019165,"sku":"DR.W-GENIE-14946-02","discounted_price":5.96999979019165,"title":"6 CH / 30 ml","price":5.96999979019165,"price_tag":"$5.97USD ","isOutOfStock":false,"line_price":5.96999979019165,"isVariantAvailOnWeb":true,"optionList":[{"isRequired":false,"isBoldApp":false,"name":"6 CH","position":"0","isEnabled":false},{"isRequired":false,"isBoldApp":false,"name":"30 ml","position":"1","isEnabled":false}],"original_price":5.96999979019165,"variants_id":39595901354143}],"quantity":1,"product_id":6641149640863,"properties":{"hide_from_display":"[\"geolocation\"]","geolocation":"{\"exclude\": [\"Turkey\", \"Qatar\", \"United Arab Emirates\", \"Saudi Arabia\", \"Malaysia\", \"Germany\", \"Spain\"]}"},"product_description":"","line_item_id":"1eac970ec7347bd2111f65ab0fd014a7","imageInfoArray":[{"src":"https://cdn.shopify.com/s/files/1/1857/6931/products/<EMAIL>?v=1621418659","image_id":"28426819928223"}],"created_at":"","productHandle":"dr-willmar-schwabe-india-belladonna-dilution","product_code":"6641149640863","product_descriptionWebUrl":"https://browntaped.myshopify.com/products/dr-willmar-schwabe-india-belladonna-dilution?view=pa.ma.pd","isProductAvailOnWeb":true}]},"Code-Version":"1000","x-plobal-shop-url":"browntaped.myshopify.com","Platform":"IOS","screen":"Checkout","event":"cart_update","additional_details":{"latitude":"37.5831518","plan_display_name":"Shopify Plus","id":"18576931","pre_launch_enabled":false,"timezone":"(GMT-08:00) America/Los_Angeles","country_name":"United States","province":"California","google_apps_domain":"","requires_extra_payments_agreement":false,"weight_unit":"kg","password_enabled":false,"primary_location_id":"29563841","longitude":"-122.043636","currency":"USD","primary_locale":"en","eligible_for_card_reader_giveaway":true,"has_gift_cards":true,"myshopify_domain":"browntaped.myshopify.com","source":"","eligible_for_payments":true,"address1":"33701 Whimbrel Road","money_with_currency_format":"<span class=\"money\">${{amount}} USD</span>","county_taxes":false,"address2":"","scope":"shop_update","setup_required":false,"updated_at":"2022-03-04T15:56:10-08:00","money_with_currency_in_emails_format":"${{amount}} USD","name":"Distacart","country":"US","taxes_included":false,"email":"<EMAIL>","has_discounts":true,"plan_name":"shopify_plus","created_at":"2017-03-18T09:48:17-07:00","country_code":"US","tax_shipping":false,"iana_timezone":"America/Los_Angeles","has_storefront":true,"finances":true,"domain":"www.distacart.com","app_key":"005b56b5-0afe-11ec-9616-0618b81e595b","money_format":"<span class=\"money\">${{amount}} USD</span>","checkout_api_supported":true,"shop_owner":"Kiran K","enabled_presentment_currencies":["AED","AUD","CAD","CHF","DKK","ETB","EUR","GBP","HKD","IDR","ILS","INR","JPY","KRW","MMK","MUR","MXN","MYR","NOK","NZD","PHP","QAR","SAR","SEK","SGD","TTD","TWD","USD","VND","ZAR"],"zip":"94555","auto_configure_tax_inclusivity":"","phone":"8552534782","cookie_consent_level":"implicit","google_apps_login_enabled":"","city":"Fremont","env":"live","customer_email":"<EMAIL>","province_code":"CA","money_in_emails_format":"${{amount}}","visitor_tracking_consent_preference":"allow_all","multi_location_enabled":true},"checkout":{"shipping_ready":true,"raw_id":"Z2lkOi8vc2hvcGlmeS9DaGVja291dC8wM2JmZTEzYWJkY2MyZjRmZjMwYTBkYjM1MTcxZjQ2Mj9rZXk9Y2EyMTQ1MDQ0MmQwMWMyNzc4ZjRkZWVjMWFjNmQ5MmM=","totalPrice":5.97,"currency":"USD","shippingLine":{"title":"5-7 Business day Express Shipping &lt; A$13.99 (Fedex / DHL )","price":"10.97","priceV2":{"amount":"10.97","currencyCode":"USD"},"handle":"shopify-5-7%20Business%20day%20Express%20Shipping%20%3C%20A$13.99%20(Fedex%20/%20DHL%20)-10.97"},"ready":true,"customAttributes":{},"webCheckoutURL":"https://browntaped.myshopify.com/18576931/checkouts/03bfe13abdcc2f4ff30a0db35171f462?key=ca21450442d01c2778f4deec1ac6d92c","subtotalPrice":5.97,"totalTax":0,"lineItems":[{"imageInfo":{"imageId":"Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0SW1hZ2UvMjg0MjY4MTk5MjgyMjM=","src":"https://cdn.shopify.com/s/files/1/1857/6931/products/<EMAIL>?v=1621418659"},"productId":"Z2lkOi8vc2hvcGlmeS9DaGVja291dExpbmVJdGVtLzM5NTk1OTAxMzU0MTQzMD9jaGVja291dD0wM2JmZTEzYWJkY2MyZjRmZjMwYTBkYjM1MTcxZjQ2Mg==","title":"Dr. Willmar Schwabe India Belladonna Dilution","variantId":"Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC8zOTU5NTkwMTM1NDE0Mw==","quantity":1,"price":"5.97","customAttributes":[{"key":"geolocation","value":"{\"exclude\": [\"Turkey\", \"Qatar\", \"United Arab Emirates\", \"Saudi Arabia\", \"Malaysia\", \"Germany\", \"Spain\"]}"}],"variantTitle":"6 CH / 30 ml"}],"discountAllocationShipping":[],"id":"03bfe13abdcc2f4ff30a0db35171f462","isTaxesIncluded":false,"taxPrice":0,"shippingDiscountAllocations":[],"paymentDue":5.97,"shippingAddress":{"province":"Fujairah","company":"","city":"Fapco / f2 O&amp;M","isDefault":false,"address2":"Qidfa","zip":"00000","country":"United Arab Emirates","phone":"0566113766","address1":"Tps","lastName":"Murkute","identifier":"Z2lkOi8vc2hvcGlmeS9NYWlsaW5nQWRkcmVzcy83NDgzNDg5MTI0NTExP21vZGVsX25hbWU9Q3VzdG9tZXJBZGRyZXNzJmN1c3RvbWVyX2FjY2Vzc190b2tlbj1rUkQ2M1J2T1NfUkRsT0ktNmNCTU13NkN5LXdFRll3UFFKN3ZTaFRpNFc2M19ubHZyRzdLdWVPNk15STlKOEZWcy04ellzeS1HTDZiOWhrY2JIbVcwSGZkRXB0eHZnY1Z6cnhvd1pYUmR6TExNeFRQajZ5US1JOGd0c3UwWWFVOFFHbGZNdUp4Qkx6TDA0ZUNzRFVvRk02YmRnU2xMUTlaWVVoZV9kbm1YRkJIeFVEaW9xb2t3cHhockdibGZ2Mmk1MV9adjZzNml1dy1UY245NHp6YVYxN2RTQjI0eGVMbmlzdmRZV2M5Q1ZLTkFZdFpOOVFodXlqMnZmQlBrZVU0","id":"Z2lkOi8vc2hvcGlmeS9NYWlsaW5nQWRkcmVzcy83NDgzNDg5MTI0NTExP21vZGVsX25hbWU9Q3VzdG9tZXJBZGRyZXNzJmN1c3RvbWVyX2FjY2Vzc190b2tlbj1rUkQ2M1J2T1NfUkRsT0ktNmNCTU13NkN5LXdFRll3UFFKN3ZTaFRpNFc2M19ubHZyRzdLdWVPNk15STlKOEZWcy04ellzeS1HTDZiOWhrY2JIbVcwSGZkRXB0eHZnY1Z6cnhvd1pYUmR6TExNeFRQajZ5US1JOGd0c3UwWWFVOFFHbGZNdUp4Qkx6TDA0ZUNzRFVvRk02YmRnU2xMUTlaWVVoZV9kbm1YRkJIeFVEaW9xb2t3cHhockdibGZ2Mmk1MV9adjZzNml1dy1UY245NHp6YVYxN2RTQjI0eGVMbmlzdmRZV2M5Q1ZLTkFZdFpOOVFodXlqMnZmQlBrZVU0","firstName":"Akshay"},"appliedGiftCards":[],"taxesIncluded":false,"requiresShipping":true,"shippingRates":{"ready":true,"shippingRates":[{"handle":"shopify-5-7%20Business%20day%20Express%20Shipping%20%3C%20A$13.99%20(Fedex%20/%20DHL%20)-10.97","title":"5-7 Business day Express Shipping &lt; A$13.99 (Fedex / DHL )","price":"10.97"}]},"webUrl":"https://browntaped.myshopify.com/18576931/checkouts/03bfe13abdcc2f4ff30a0db35171f462?key=ca21450442d01c2778f4deec1ac6d92c"},"currency":{"code":"USD","rate":"1.000000","decimal":"2","format":"${{amount}}USD"},"Version-No":"YOUR_APP_VERSION"}};



                // var jsonData = {"json_data":{"x-plobal-shop-url":"browntaped.myshopify.com","Code-Version":"103","Platform":"ANDROID","Version-No":"17","customer_details":{"id":"5823699058847","first_name":"Akshay","last_name":"Support","email":"<EMAIL>","tags":[],"shipping_address":{"first_name":"Abhay","last_name":"Kumar","company":"Plobal","phone":"9028216523","address1":"Xyz street","address2":"","province":"New South Wales","city":"Sydney","zip":"2000","country":"Australia"}},"currency":{"code":"USD","format":"${{amount}}USD","rate":1,"decimal":"2"},"additional_details":{"id":"18576931","name":"Distacart","email":"<EMAIL>","domain":"www.distacart.com","province":"California","country":"US","address1":"33701 Whimbrel Road","zip":"94555","city":"Fremont","source":"","phone":"8552534782","latitude":"37.5831518","longitude":"-122.043636","primary_locale":"en","address2":"","created_at":"2017-03-18T09:48:17-07:00","updated_at":"2022-03-04T15:56:10-08:00","country_code":"US","country_name":"United States","currency":"USD","customer_email":"<EMAIL>","timezone":"(GMT-08:00) America/Los_Angeles","iana_timezone":"America/Los_Angeles","shop_owner":"Kiran K","money_format":"","weight_unit":"kg","province_code":"CA","taxes_included":false,"auto_configure_tax_inclusivity":"","tax_shipping":false,"county_taxes":false,"plan_display_name":"Shopify Plus","plan_name":"shopify_plus","has_discounts":true,"has_gift_cards":true,"myshopify_domain":"browntaped.myshopify.com","google_apps_domain":"","google_apps_login_enabled":"","money_in_emails_format":"${{amount}}","money_with_currency_in_emails_format":"${{amount}} USD","eligible_for_payments":true,"requires_extra_payments_agreement":false,"password_enabled":false,"has_storefront":true,"eligible_for_card_reader_giveaway":true,"finances":true,"primary_location_id":"29563841","cookie_consent_level":"implicit","visitor_tracking_consent_preference":"allow_all","checkout_api_supported":true,"multi_location_enabled":true,"setup_required":false,"pre_launch_enabled":false,"enabled_presentment_currencies":["AED","AUD","CAD","CHF","DKK","ETB","EUR","GBP","HKD","IDR","ILS","INR","JPY","KRW","MMK","MUR","MXN","MYR","NOK","NZD","PHP","QAR","SAR","SEK","SGD","TTD","TWD","USD","VND","ZAR"],"app_key":"005b56b5-0afe-11ec-9616-0618b81e595b","env":"live","scope":"shop_update"},"screen":"Checkout","checkout":"{\"appliedGiftCards\":[],\"currency\":\"USD\",\"customAttributes\":{},\"discountAllocationShipping\":[],\"id\":\"Z2lkOi8vc2hvcGlmeS9DaGVja291dC8wZmQ2MDAxNjhmNjBkMDcxZThmOTllMDIwNzU0NjM0Yj9rZXk9ZGI1NTUxNTJlMGQyNDQyZGFmOGZkZjMxZGE5NTI5OWM\\u003d\",\"isTaxesIncluded\":false,\"lineItems\":[{\"variantId\":\"Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC8zOTU5NTkwMTM1NDE0Mw\\u003d\\u003d\",\"title\":\"Dr. Willmar Schwabe India Belladonna Dilution\",\"productId\":\"Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0LzY2NDExNDk2NDA4NjM\\u003d\",\"variantTitle\":\"6 CH / 30 ml\",\"quantity\":1,\"price\":5.97,\"imageInfo\":{\"height\":0,\"imageId\":\"Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0SW1hZ2UvMjg0MjY4MTk5MjgyMjM\\u003d\",\"originalSrc\":\"https://cdn.shopify.com/s/files/1/1857/6931/products/HHMUS1sVzX.jpg?v\\u003d1621418659\",\"position\":0,\"src\":\"https://cdn.shopify.com/s/files/1/1857/6931/products/HHMUS1sVzX.jpg?v\\u003d1621418659\",\"width\":0},\"customAttributes\":{},\"discountLineItem\":[]},{\"variantId\":\"Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC8zOTU5NTkwMTM1NDE0Mw\\u003d\\u003d\",\"title\":\"Dr. Willmar Schwabe India Belladonna Dilution\",\"productId\":\"Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0LzY2NDExNDk2NDA4NjM\\u003d\",\"variantTitle\":\"6 CH / 30 ml\",\"quantity\":1,\"price\":5.97,\"imageInfo\":{\"height\":0,\"imageId\":\"Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0SW1hZ2UvMjg0MjY4MTk5MjgyMjM\\u003d\",\"originalSrc\":\"https://cdn.shopify.com/s/files/1/1857/6931/products/HHMUS1sVzX.jpg?v\\u003d1621418659\",\"position\":0,\"src\":\"https://cdn.shopify.com/s/files/1/1857/6931/products/HHMUS1sVzX.jpg?v\\u003d1621418659\",\"width\":0},\"customAttributes\":{\"geolocation\":\"{\\\"exclude\\\": [\\\"Turkey\\\", \\\"Qatar\\\", \\\"United Arab Emirates\\\", \\\"Saudi Arabia\\\", \\\"Malaysia\\\", \\\"Germany\\\", \\\"Spain\\\"]}\"},\"discountLineItem\":[]}],\"paymentDue\":\"11.94\",\"ready\":true,\"requiresShipping\":true,\"shippingAddress\":{\"id\":\"Z2lkOi8vc2hvcGlmeS9NYWlsaW5nQWRkcmVzcy8xMDYzMDkxMDc3MTM1OT9tb2RlbF9uYW1lPUFkZHJlc3M\\u003d\",\"address1\":\"Xyz street\",\"address2\":\"\",\"phone\":\"9028216523\",\"company\":\"Plobal\",\"firstName\":\"Abhay\",\"lastName\":\"Kumar\",\"zip\":\"2000\",\"country\":\"Australia\",\"city\":\"Sydney\",\"province\":\"New South Wales\"},\"shippingRates\":{\"ready\":true,\"shippingRates\":[{\"handle\":\"shopify-5-7%20Business%20day%20Express%20Shipping%20%3E%20A$14%20(Fedex%20/%20DHL%20)-6.48\",\"price\":6.48,\"title\":\"5-7 Business day Express Shipping \\u003e A$14 (Fedex / DHL )\"}]},\"subtotalPrice\":11.94,\"taxPrice\":0.0,\"totalPrice\":11.94,\"webUrl\":\"https://browntaped.myshopify.com/18576931/checkouts/0fd600168f60d071e8f99e020754634b?key\\u003ddb555152e0d2442daf8fdf31da95299c\"}"}};
                window.checkValidPinCode = function (data) {
                   window.plobalLog("checkValidPinCode");
                   window.plobalLog(data);
                   window.isValidPinCode = false;
                   if (window.pinCodeJsonData.devlivery_settings.pin_code_type == "regex") {
                        $.each(window.pinCodeJsonData.devlivery_settings.available_pin_codes, function(index, value) {
                          if (data.match(value)) {
                            window.isValidPinCode = true;
                          }
                        });
                   } else if (window.pinCodeJsonData.devlivery_settings.pin_code_type == "exact_match") {
                        $.each(window.pinCodeJsonData.devlivery_settings.available_pin_codes, function(index, value) {
                          if (data == value) {
                            window.isValidPinCode = true;
                          }
                        });
                   }
                   return window.isValidPinCode;
                  
                }
                window.shippingCountry = "";
                window.webviewId = "preorder";
                //window.PlobalBridge.validateData({"valid":false, "message": "Some products in your cart are not available to ship to your country", "id":"preorder"});
                var details =  {"id": window.webviewId, "action": "javascript:window.onDataReceive(all_data)"};
                window.PlobalBridge.callBridgeFunction("getFeatureData",details, JSON.stringify(details));
                window.plobalLog = function (textData, datass) {
                    if (datass == null) {
                      datass = '';
                    }
                    if(typeof  datass == "object") {datass=JSON.stringify(datass)}
                    if(typeof  textData == "object") {textData=JSON.stringify(textData)}
                    $("#logss").append("<div>"+datass+"</div>");
                    $("#logss").append("<div>"+textData+"</div>");
                  window.PlobalBridge.adjustWebViewHeight("main-container");
                  console.log("textData");console.log(textData);
                  console.log("datass");console.log(datass);
                }
                window.onDataReceive = function (data) {
                    window.requestData = data;
                  window.plobalLog("data input", data);
                  console.log("window.requestData");
                  console.log(window.requestData);
                  
                  if(
                    !window.requestData.hasOwnProperty("json_data") ||
                    !window.requestData.json_data.hasOwnProperty("checkout") ||
                    window.requestData.json_data.checkout == "null" ||
                    !window.requestData.json_data.checkout
                   ) {
                    //window.PlobalBridge.validateData({"valid":false, "message": "Please Add address.", "id":"preorder"});
                    //return;
                  }
                  var checkoutData = {};
                  if(
                    window.requestData.json_data.hasOwnProperty("Platform") &&
                    window.requestData.json_data.Platform == "IOS"
                  ) {
                    checkoutData = window.requestData.json_data.checkout;
                  } else {
                    try {
                    checkoutData = JSON.parse(window.requestData.json_data.checkout);
                    }catch(err){
                    checkoutData = window.requestData.json_data.checkout;
                    }
                  }
                  console.log("checkoutData");
                  console.log(checkoutData);
                  var shippingCountry = "";
                  if(
                    checkoutData &&
                    checkoutData.hasOwnProperty("shippingAddress") &&
                    checkoutData.shippingAddress.hasOwnProperty("country")
                  ) {
                    shippingCountry = checkoutData.shippingAddress.country.toLowerCase();
                  } else if(
                    window.requestData.json_data.hasOwnProperty("customer_details") &&
                    window.requestData.json_data.customer_details.hasOwnProperty("shipping_address") &&
                    window.requestData.json_data.customer_details.shipping_address.hasOwnProperty("country")
                  ) {
                    shippingCountry = window.requestData.json_data.customer_details.shipping_address.country.toLowerCase();
                  }
                    window.shippingCountry = shippingCountry;
                  console.log("shippingCountry");
                  console.log(shippingCountry);
                  var lineItems = [];
                  if(
                    checkoutData &&
                    checkoutData.hasOwnProperty("lineItems")
                  ) {
                    lineItems = checkoutData.lineItems;
                  } else {
                    lineItems = window.requestData.json_data.cart.items;
                  }
                  console.log("lineItems");
                  console.log(lineItems);
                    var validCheckout = true;
                  console.log("validCheckout");
                  console.log(validCheckout);
                  var validCountries = {};
                  var validCountriesProduct = {};
                  var invalidCountriesProduct = {};
                  var invalidCountries = {};
                  var productsNotAvailable = [];
                    lineItems.forEach(function(lineItem){
                       var collection_ids = "";
                      console.log(lineItem);
                      if(lineItem.hasOwnProperty("customAttributes")) {
                        if(lineItem.customAttributes.hasOwnProperty("collection_ids")) {
                          collection_ids = lineItem.customAttributes.collection_ids;
                          console.log("collection_ids");
                          console.log(collection_ids);
                        } 
                        else if(lineItem.customAttributes.hasOwnProperty("_collection_ids")) {
                          collection_ids = lineItem.customAttributes._collection_ids;
                          console.log("collection_ids");
                          console.log(collection_ids);
                        } 
                        else {
                            if (lineItem.customAttributes.length > 0) {
                              lineItem.customAttributes.forEach(function(cAttribute){
                                 if (cAttribute.hasOwnProperty("key") && cAttribute.key == "collection_ids") {
                                    collection_ids = cAttribute.value;
                                 }
                                if (cAttribute.hasOwnProperty("key") && cAttribute.key == "_collection_ids") {
                                    collection_ids = cAttribute.value;
                                 }
                              });
                            }
                        }
                      }
                      if(lineItem.hasOwnProperty("properties")) {
                        if(lineItem.properties.hasOwnProperty("collection_ids")) {
                          collection_ids = lineItem.properties.collection_ids;
                          console.log("collection_ids");
                          console.log(collection_ids);
                          //add code here
                          
                        }
                        if(lineItem.properties.hasOwnProperty("_collection_ids")) {
                          collection_ids = lineItem.properties._collection_ids;
                          console.log("collection_ids");
                          console.log(collection_ids);
                          //add code here
                          
                        }
                      }
                      if(collection_ids) {
                        console.log("collection_ids");
                          console.log(collection_ids);
                        if(window.shippingCountry == "canada") {
                          console.log("checking for canada");
                          var collection_ids_arr = collection_ids.split(",");
                          //https://kyte-baby-co.myshopify.com/admin/collections/281178833007.json
                          //https://kytebaby.com/collections/sleep-bag-walker-1
                          if(
                            collection_ids_arr.includes(281178833007) ||
                            collection_ids_arr.includes("281178833007")
                            
//                             collection_ids_arr.includes(280730304623) ||
//                             collection_ids_arr.includes("280730304623") ||
//                             collection_ids_arr.includes(74842767471) ||
//                             collection_ids_arr.includes("74842767471")
                          ) {
                            validCheckout = false;
                            if(!productsNotAvailable.includes(lineItem.title)) {
                              productsNotAvailable.push(lineItem.title);
                            }
                          }
                        }
                      }
                      
                    });
                  if(!validCheckout) {

                    window.PlobalBridge.validateData({"valid":false, "message": productsNotAvailable.join(", ")+" products in your cart "+ (productsNotAvailable.length > 1 ? "are" : "is") +" not available to ship to your country", "id":"preorder"});
                    return;
                  }
                  window.PlobalBridge.validateData({"valid":true, "message": "Deliverable in current location", "id":"preorder"});
                  return;
                }
     
                const resize_ob = new ResizeObserver(function(entries) {
                    // since we are observing only a single element, so we access the first element in entries array
                    let rect = entries[0].contentRect;

                    // current width & height
                    let width = rect.width;
                    let height = rect.height;

                    console.log('Current Width : ' + width);
                    console.log('Current Height : ' + height);
                    window.PlobalBridge.adjustWebViewHeight("main-container");
                });

                // start observing for resize
                resize_ob.observe(document.querySelector("#main-container"));
                // window.onDataReceive(jsonData);
            });
        </script>
    </body>
</html>