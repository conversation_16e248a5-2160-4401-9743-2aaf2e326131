/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "collection-banner": {
      "type": "collection-banner-split",
      "settings": {
        "usp_group": "",
        "usps_list": [],
        "show_collection_image": false,
        "collection_sub_title": "{{ collection.metafields.header.subtitle.value }}",
        "show_collection_title": true,
        "show_collection_description": true,
        "use_metafield_description": true,
        "background_type": "full_width",
        "text_width": "medium",
        "text_position": "middle_left",
        "section_height": "clamp",
        "image_text_color": "#ffffff",
        "text_background": "#889baf",
        "image_overlay_color": "#000000",
        "image_overlay_opacity": 20,
        "quick_links": "",
        "quick_links_hide_title": false,
        "quick_links_menu_handle": "{{ collection.metafields.navigation.menu_handle.value }}",
        "quick_links_title": "{{ collection.metafields.navigation.menu_title.value }}"
      }
    },
    "slideshow_z3p99y": {
      "type": "slideshow",
      "blocks": {
        "image_rWRTGE": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/4th-Ship-Deadline_Desktop.jpg",
            "text_position": "middle_center",
            "subheading": "",
            "title": "",
            "button_1_text": "Sign Up",
            "button_1_link": "#klaviyo_XpMspK",
            "button_2_text": "",
            "button_2_link": "",
            "text_color": "#ffffff",
            "button_background": "#ffffff",
            "button_text_color": "#000000",
            "overlay_color": "#000000",
            "overlay_opacity": 0
          }
        }
      },
      "block_order": [
        "image_rWRTGE"
      ],
      "disabled": true,
      "name": "Slideshow",
      "settings": {
        "section_height": "auto",
        "transition_type": "sweep",
        "show_initial_transition": false,
        "autoplay": true,
        "cycle_speed": 5,
        "background": "rgba(0,0,0,0)"
      }
    },
    "77f9f575-bf3b-468c-a858-b2376d2cd68b": {
      "type": "rich-text",
      "disabled": true,
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "",
        "subheading": "",
        "title": "",
        "content": "<p>Due to the warehouse move orders may take up to 2 weeks to be fulfilled</p>",
        "button_text": "",
        "button_link": "",
        "button_style": "",
        "button_icon": "",
        "background_type": "full_width",
        "text_width": "medium",
        "text_position": "center",
        "text_alignment": "center",
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)"
      }
    },
    "rich_text_7cTEtz": {
      "type": "rich-text",
      "disabled": true,
      "settings": {
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": "",
        "subheading": "",
        "title": "",
        "content": "",
        "button_text": "",
        "button_link": "",
        "button_style": "",
        "button_icon": "",
        "background_type": "full_width",
        "text_width": "large",
        "text_position": "center",
        "text_alignment": "center",
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)",
        "button_background": "rgba(0,0,0,0)",
        "button_text_color": "rgba(0,0,0,0)"
      }
    },
    "multi_column_jC9zmm": {
      "type": "multi-column",
      "blocks": {
        "item_kRHiNz": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Liberty_Rip_Tide_Tank_Set_SS_PJ_Set_in_Liberty_Rip_tide-07.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Liberty Rip Tide",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/liberty-riptide",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_PWXfan": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/SS_Twirl_Dress_BodySuit_in_Celebration-02.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Celebration",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/celebration",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_h3GnHb": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Slate_Biker_Short_Set_1x1-4.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Slate",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/slate",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_nC7jqJ": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Cardinal_LS_toddler_pajamas-01.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Cardinal",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/cardinal",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_zBKx7f": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Tahoe_Drawstring_Short-01.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Tahoe",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/tahoe",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_MhhaQ7": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Polo_Bodysuit_Dress_in_Fog-01.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Fog",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/fog",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_GVxm8C": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/kyte-baby-short-sleeve-bodysuits-bodysuit-in-navy-34514920341615.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Navy",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/navy",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_GLGkx4": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Cloud_New_Bow-01.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Cloud",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/cloud",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "item_x6KEcN": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/SnowCrewTee-1x1-01.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Snow",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/snow",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        }
      },
      "block_order": [
        "item_kRHiNz",
        "item_PWXfan",
        "item_h3GnHb",
        "item_nC7jqJ",
        "item_zBKx7f",
        "item_MhhaQ7",
        "item_GVxm8C",
        "item_GLGkx4",
        "item_x6KEcN"
      ],
      "settings": {
        "reveal_on_scroll": true,
        "stack_items": false,
        "subheading": "",
        "title": "",
        "content": "",
        "column_alignment": "center",
        "mobile_item_size": "medium",
        "desktop_item_size": "medium",
        "spacing": "normal",
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)"
      }
    },
    "1664759269266d123b": {
      "type": "multi-column",
      "blocks": {
        "1664759268595ddfca-0": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Rectangle_230.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Jogger Sets",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/womens-jogger-set",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "1664759268595ddfca-1": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Rectangle_231.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Lounge Pants",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/women-s-lounge-pant",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "1664759268595ddfca-2": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Rectangle_232.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Lounge Robes",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/adult-lounge-robe",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "1664759268595ddfca-3": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Rectangle_233.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Pajama Sets",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/sets",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        },
        "b9790cba-ad6a-4f2d-b096-6caa89f8409c": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/Rectangle_234.jpg",
            "image_width": 100,
            "image_border": "rgba(0,0,0,0)",
            "title": "Tank Sets",
            "content": "",
            "link_text": "",
            "link_url": "shopify://collections/womens-tank-set",
            "link_style": "link",
            "text_alignment": "center",
            "vertical_alignment": "start"
          }
        }
      },
      "block_order": [
        "1664759268595ddfca-0",
        "1664759268595ddfca-1",
        "1664759268595ddfca-2",
        "1664759268595ddfca-3",
        "b9790cba-ad6a-4f2d-b096-6caa89f8409c"
      ],
      "disabled": true,
      "settings": {
        "reveal_on_scroll": true,
        "stack_items": false,
        "subheading": "",
        "title": "",
        "content": "",
        "column_alignment": "center",
        "mobile_item_size": "small",
        "desktop_item_size": "small",
        "spacing": "tight",
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)"
      }
    },
    "main": {
      "type": "main-collection",
      "blocks": {
        "e571a9f5-2677-4d14-9017-728678ca99f2": {
          "type": "media_banner",
          "settings": {
            "column_span": 3,
            "position": 7,
            "video_filename": "Hotwheels 1.1.1.mp4",
            "video_url": "",
            "image": "shopify://shop_images/Heading_14_08331a23-513a-4b86-92c0-7675ab79b4f7.png",
            "image_position": "",
            "link_url": "shopify://collections/hot-wheels™-x-kyte-baby",
            "overlay_subheading": "New Arrival",
            "overlay_title": "Hot Wheels™ x Kyte Baby",
            "overlay_link_text": "Shop Now",
            "overlay_text": "",
            "overlay_color": "rgba(0,0,0,.2)",
            "text_color": "#ffffff"
          }
        },
        "0ae5d61c-aa10-40fc-90fd-977e67cdf322": {
          "type": "media_banner",
          "settings": {
            "column_span": 1,
            "position": 18,
            "video_filename": "",
            "video_url": "",
            "image": "shopify://shop_images/NPF_II_FF-Lifestyle_1.png",
            "image_position": "",
            "link_url": "shopify://pages/frequent-flyer-rewards",
            "overlay_subheading": "",
            "overlay_title": "",
            "overlay_link_text": "",
            "overlay_text": "",
            "overlay_color": "rgba(0,0,0,.2)",
            "text_color": "#ffffff"
          }
        }
      },
      "block_order": [
        "e571a9f5-2677-4d14-9017-728678ca99f2",
        "0ae5d61c-aa10-40fc-90fd-977e67cdf322"
      ],
      "settings": {
        "aspect_ratio": "",
        "tier_display": "badge",
        "show_additional_text_fields": true,
        "show_sort_by": true,
        "products_per_page": 50,
        "mobile_products_per_row": "2",
        "desktop_products_per_row": 3,
        "show_filters": true,
        "show_filter_group_name": false,
        "show_color_swatch": true,
        "open_first_filter_group": true,
        "filter_position": "always_visible",
        "promotion_position": "top",
        "promotion_height": "small"
      }
    },
    "17516439436cd4b74c": {
      "type": "apps",
      "blocks": {
        "foursixty_shoppable_social_ugc_custom_slider_d7K3jM": {
          "type": "shopify://apps/foursixty-shoppable-social-ugc/blocks/custom_slider/e2616dfe-fbb6-4e7d-874d-d250b973556d",
          "settings": {
            "foreground_color": "#ffffff",
            "background_color": "#000000",
            "background_color_alpha": 90,
            "post_spacing": 1,
            "hover_margin": 0,
            "cell_size": "25%",
            "target_blank": false,
            "arrows_outside": false,
            "show_icon": true,
            "show_text": false,
            "show_date": false,
            "call_to_action": "SHOP IT",
            "shop_type": "button",
            "body_font_size": 14,
            "body_font": "sans_serif_n4",
            "accent_font_size": 14,
            "accent_font": "sans_serif_n4",
            "disable_filter": false,
            "override_feed_id": "",
            "arbitrary_attributes": "data-category-filter=\"homepage 4th of july\"",
            "custom_css": "",
            "custom_js": ""
          }
        }
      },
      "block_order": [
        "foursixty_shoppable_social_ugc_custom_slider_d7K3jM"
      ],
      "settings": {
        "text_width": "medium",
        "text_position": "center",
        "text_alignment": "center",
        "background_type": "full_width",
        "subheading": "",
        "subheading_style": "heading--small",
        "title": "Baby, you're a firework!",
        "heading_style": "",
        "intro_text": "",
        "intro_text_style": "",
        "content": "",
        "content_style": "",
        "buttons_text": "",
        "buttons_text_style": "heading--small",
        "button_text": "",
        "button_link": "",
        "button_size": "",
        "button_style": "",
        "button_icon": "",
        "button_extra_attributes": "",
        "inner_background": "rgba(0,0,0,0)",
        "background": "",
        "heading_color": "",
        "text_color": "",
        "button_background": "",
        "button_text_color": "",
        "mobile_no_horizontal_padding": false,
        "include_horizontal_margins": true,
        "include_vertical_margins": false,
        "styling_class": "",
        "anchor": "",
        "use_padding": false,
        "box_shadow": false,
        "section_extra_padding": false,
        "override_blend": false,
        "extra_code": ""
      }
    },
    "custom_liquid_NyHHFN": {
      "type": "custom-liquid",
      "custom_css": [
        "h4 {text-align: center;}",
        "p {margin-left: auto; margin-right: auto; max-width: 980px; text-align: center;}"
      ],
      "settings": {
        "subheading": "",
        "title": "",
        "liquid": "<h4>{{ collection.title }}</h4>\n<div class=\"rte\">{{ collection.description }}</div>",
        "add_vertical_spacing": true,
        "background_type": "boxed",
        "background": "rgba(0,0,0,0)",
        "text_color": "rgba(0,0,0,0)"
      }
    }
  },
  "order": [
    "collection-banner",
    "slideshow_z3p99y",
    "77f9f575-bf3b-468c-a858-b2376d2cd68b",
    "rich_text_7cTEtz",
    "multi_column_jC9zmm",
    "1664759269266d123b",
    "main",
    "17516439436cd4b74c",
    "custom_liquid_NyHHFN"
  ]
}
