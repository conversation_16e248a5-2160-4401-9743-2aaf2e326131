{% layout none %}
<!-- 
changes - if user location is not received from native shipping meter is set to 85 and for tiers set same as for US

code changes - 
window.shipping_amount = 300;
        try {
          if(window.myCallAppData && window.myCallAppData.hasOwnProperty("country_code") && (window.myCallAppData.country_code)){
            window.shipping_amount = 300;
          } else{
          window.shipping_amount = 85;
            try {
              if(
                window.requestData.customer_details.tags.includes("swell_vip_bronze") ||
                window.requestData.customer_details.tags.includes("tier: Bronze")
              ) {
                window.shipping_amount = 85;
              }
                if(
                window.requestData.customer_details.tags.includes("swell_vip_silver") ||
                window.requestData.customer_details.tags.includes("tier: Silver")
              ) {
                window.shipping_amount = 60;
              }
                if(
                window.requestData.customer_details.tags.includes("swell_vip_gold") ||
                window.requestData.customer_details.tags.includes("tier: Gold")
              ) {
                window.shipping_amount = 60;
              }
            } catch(err){}
          }
        } catch(err){}
       // $("#mymessageCountry").show().html("other");
      } -->
<html lang="en"><head>
<!-- Start of Shoplift scripts -->
{% render 'shoplift' %}
<!-- End of Shoplift scripts -->

    <meta charset="UTF-8">
    <title>Kyte-1-22</title>
    <meta data-n-head="ssr" name="viewport" content="width=device-width, initial-scale=1">
    <style>
        .theContainer[data-v-1a69e700] {
            padding-right: 15px;
            padding-left: 15px;
            margin-right: auto;
            margin-left: auto
        }

        @media(min-width: 768px) {
            .theContainer[data-v-1a69e700] {
                width:768px
            }
        }

        @media(min-width: 1024px) {
            .theContainer[data-v-1a69e700] {
                width:970px
            }
        }

        @media(min-width: 1280px) {
            .theContainer[data-v-1a69e700] {
                width:1170px
            }
        }

        @media(min-width: 480px) and (max-width:768px) {
            .theContainer[data-v-1a69e700] {
                max-width:748px
            }
        }

        .lds-ring[data-v-13c956db] {
            display: inline-block;
            position: relative;
            width: 16px;
            height: 16px
        }

        .lds-ring div[data-v-13c956db] {
            box-sizing: border-box;
            display: block;
            position: absolute;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            -webkit-animation: lds-ring-data-v-13c956db 1.2s cubic-bezier(.5,0,.5,1) infinite;
            animation: lds-ring-data-v-13c956db 1.2s cubic-bezier(.5,0,.5,1) infinite;
            border: 2px solid transparent;
            border-top-color: #fff
        }

        .lds-ring div[data-v-13c956db]:first-child {
            -webkit-animation-delay: -.45s;
            animation-delay: -.45s
        }

        .lds-ring div[data-v-13c956db]:nth-child(2) {
            -webkit-animation-delay: -.3s;
            animation-delay: -.3s
        }

        .lds-ring div[data-v-13c956db]:nth-child(3) {
            -webkit-animation-delay: -.15s;
            animation-delay: -.15s
        }

        @-webkit-keyframes lds-ring-data-v-13c956db {
            0% {
                transform: rotate(0deg)
            }

            to {
                transform: rotate(1turn)
            }
        }

        @keyframes lds-ring-data-v-13c956db {
            0% {
                transform: rotate(0deg)
            }

            to {
                transform: rotate(1turn)
            }
        }

        .tab-enter-active[data-v-39445c57],.tab-leave-active[data-v-39445c57] {
            transition: .2s
        }

        .tab-enter[data-v-39445c57],.tab-leave-to[data-v-39445c57] {
            transform: translateY(-100%)
        }

        .remove-button[data-v-1b403fa6] {
            position: absolute;
            top: -1.125rem;
            right: 0;
            padding: 0
        }

        .is-animating[data-v-39a9cc1c] {
            -webkit-animation: heart-like-data-v-39a9cc1c;
            animation: heart-like-data-v-39a9cc1c;
            -webkit-animation-duration: .3s;
            animation-duration: .3s
        }

        @-webkit-keyframes heart-like-data-v-39a9cc1c {
            0% {
                transform: scale(1)
            }

            50% {
                transform: scale(1.2)
            }

            to {
                transform: scale(1)
            }
        }

        @keyframes heart-like-data-v-39a9cc1c {
            0% {
                transform: scale(1)
            }

            50% {
                transform: scale(1.2)
            }

            to {
                transform: scale(1)
            }
        }

        .tooltip[data-v-39a9cc1c] {
            z-index: 1;
            background-color: #333;
            bottom: calc(100% + 5px)
        }

        .fade-enter-active[data-v-39a9cc1c],.fade-leave-active[data-v-39a9cc1c] {
            transition: opacity .3s
        }

        .fade-enter[data-v-39a9cc1c],.fade-leave-to[data-v-39a9cc1c] {
            opacity: 0
        }

        .product-hover[data-v-38debe7a] {
            background-color: rgba(0,0,0,.45);
            opacity: 0;
            transition: .2s ease-out
        }

        .discount-badge[data-v-38debe7a]:after {
            content: "";
            width: 0;
            position: absolute;
            height: 0;
            right: -10px;
            border-color: transparent transparent transparent #ff7f65 !important;
            border-style: solid;
            border-width: 10.5px 0 10.5px 10px
        }

        [data-v-38debe7a] .progressive-image-main {
            max-height: 10rem;
            -o-object-fit: contain;
            object-fit: contain;
            top: 50%;
            transform: translateY(-50%)
        }

        .tooltip[data-v-38debe7a] {
            top: 100%;
            background-color: #333;
            width: 9rem
        }

        .fade-enter-active[data-v-38debe7a],.fade-leave-active[data-v-38debe7a] {
            transition: opacity .3s
        }

        .fade-enter[data-v-38debe7a],.fade-leave-to[data-v-38debe7a] {
            opacity: 0
        }

        .product-hover[data-v-161e4cd8] {
            background-color: rgba(0,0,0,.45);
            opacity: 0;
            transition: .2s ease-out
        }

        .discount-badge[data-v-161e4cd8]:after {
            content: "";
            width: 0;
            position: absolute;
            height: 0;
            right: -10px;
            border-color: transparent transparent transparent #ff7f65 !important;
            border-style: solid;
            border-width: 10.5px 0 10.5px 10px
        }

        [data-v-161e4cd8] .progressive-image-main {
            max-height: 10rem;
            -o-object-fit: contain;
            object-fit: contain;
            top: 50%;
            transform: translateY(-50%)
        }

        .tooltip[data-v-161e4cd8] {
            top: 100%;
            background-color: #333;
            width: 9rem
        }

        .fade-enter-active[data-v-161e4cd8],.fade-leave-active[data-v-161e4cd8] {
            transition: opacity .3s
        }

        .fade-enter[data-v-161e4cd8],.fade-leave-to[data-v-161e4cd8] {
            opacity: 0
        }

        .loadingBox[data-v-378a12f3] {
            display: inline-block;
            background: linear-gradient(180deg,#c3c3c3,#fff);
            -webkit-animation-name: loading-data-v-378a12f3;
            animation-name: loading-data-v-378a12f3;
            -webkit-animation-duration: 1s;
            animation-duration: 1s;
            -webkit-animation-iteration-count: infinite;
            animation-iteration-count: infinite
        }

        @-webkit-keyframes loading-data-v-378a12f3 {
            0% {
                background: #f2f2f2
            }

            50% {
                background: #e9e9e9
            }

            to {
                background: #f2f2f2
            }
        }

        @keyframes loading-data-v-378a12f3 {
            0% {
                background: #f2f2f2
            }

            50% {
                background: #e9e9e9
            }

            to {
                background: #f2f2f2
            }
        }

        .emulated-flex-gap>* {
            margin: 10px 0 0 10px
        }

        .emulated-flex-gap {
            display: inline-flex;
            flex-wrap: wrap;
            margin: -10px 0 0 -10px;
            width: calc(100% + 10px)
        }

        .ais-grid,.grid-products-md,.grid-products-sm,.loading {
            grid-template-columns: repeat(auto-fill,minmax(10rem,1fr));
            grid-gap: 10px
        }

        .grid-products-lg {
            grid-template-columns: repeat(auto-fill,minmax(16rem,1fr));
            grid-gap: 10px
        }

        @media screen and (max-width: 1024px) {
            .grid-products-lg {
                grid-template-columns:repeat(auto-fill,minmax(14rem,1fr))
            }
        }

        @media screen and (max-width: 640px) {
            .grid-products-lg {
                grid-template-columns:repeat(auto-fill,minmax(10rem,1fr))
            }
        }

        @media(max-width: 359.98px) {
            .ais-grid,.grid-products-lg,.grid-products-md,.grid-products-sm,.loading {
                grid-template-columns:repeat(auto-fill,minmax(7.5rem,1fr))
            }

            .more-button-text {
                font-size: .8rem
            }

            .more-button-arrow {
                width: 1.125rem;
                height: 1.125rem
            }
        }

        .tab[data-v-549d03f6]:after {
            content: "";
            -webkit-clip-path: polygon(75% 0,100% 50%,75% 100%,74% 100%,99% 50%,74% 0);
            clip-path: polygon(75% 0,100% 50%,75% 100%,74% 100%,99% 50%,74% 0);
            background: #dbdbdb;
            height: 49px;
            margin-left: -20px;
            width: 50px;
            display: inline-block
        }

        .sticky-offset[data-v-549d03f6] {
            top: 0
        }

        @media(max-width: 639.98px) {
            .sticky-offset[data-v-549d03f6] {
                top:145px
            }
        }

        @supports(-ms-accelerator:true) or (-ms-ime-align:auto) {
            .tab[data-v-549d03f6]:after {
                display: none
            }

            .tab[data-v-549d03f6] {
                border-right: 1px solid #dbdbdb
            }

            .edge-ml-3-container.sm\:flex:not(:first-of-type) .edge-ml-3[data-v-549d03f6],.edge-ml-3-container.sm\:hidden:not(:first-of-type) .edge-ml-3[data-v-549d03f6] {
                margin-left: .75rem
            }
        }

        .focus\:underline:focus span[data-v-549d03f6] {
            color: #89bd24!important
        }

        .grid-recipes[data-v-416527be] {
            grid-template-columns: repeat(4,minmax(10rem,1fr));
            grid-gap: 10px
        }

        @media(max-width: 1023.98px) {
            .grid-recipes[data-v-416527be] {
                grid-template-columns:repeat(3,minmax(10rem,1fr))
            }
        }

        @media(max-width: 639.98px) {
            .grid-recipes[data-v-416527be] {
                grid-template-columns:repeat(auto-fill,minmax(10rem,1fr))
            }
        }

        .fade-enter-active[data-v-416527be],.fade-leave-active[data-v-416527be] {
            transition: all .1s ease-in-out
        }

        .fade-leave-active[data-v-416527be] {
            opacity: 1
        }

        .fade-enter[data-v-416527be],.fade-leave-to[data-v-416527be] {
            opacity: 0;
            margin-top: 0
        }

        @media(max-width: 399.98px) {
            .dropdown-section[data-v-416527be] {
                width:100%;
                margin-top: 0
            }

            .dropdown-section[data-v-416527be]:not(:last-of-type) {
                margin-bottom: 1rem
            }
        }

        .nuxt-progress {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            width: 0;
            opacity: 1;
            transition: width .1s,opacity .4s;
            background-color: #ff7f65 !important;
            z-index: 999999
        }

        .nuxt-progress.nuxt-progress-notransition {
            transition: none
        }

        .nuxt-progress-failed {
            background-color: red
        }

        /*!normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css*/
        html {
            line-height: 1.15
        }

        body {
            margin: 0
        }

        main {
            display: block
        }

        h1 {
            font-size: 2em;
            margin: .67em 0
        }

        hr {
            box-sizing: content-box;
            height: 0;
            overflow: visible
        }

        pre {
            font-family: monospace,monospace;
            font-size: 1em
        }

        a {
            background-color: transparent
        }

        abbr[title] {
            border-bottom: none;
            text-decoration: underline;
            -webkit-text-decoration: underline dotted;
            text-decoration: underline dotted
        }

        b,strong {
            font-weight: bolder
        }

        code,kbd,samp {
            font-family: monospace,monospace;
            font-size: 1em
        }

        small {
            font-size: 80%
        }

        sub,sup {
            font-size: 75%;
            line-height: 0;
            position: relative;
            vertical-align: baseline
        }

        sub {
            bottom: -.25em
        }

        sup {
            top: -.5em
        }

        img {
            border-style: none
        }

        button,input,optgroup,select,textarea {
            font-family: inherit;
            font-size: 100%;
            line-height: 1.15;
            margin: 0
        }

        button,input {
            overflow: visible
        }

        button,select {
            text-transform: none
        }

        [type=button],[type=reset],[type=submit],button {
            -webkit-appearance: button
        }

        [type=button]::-moz-focus-inner,[type=reset]::-moz-focus-inner,[type=submit]::-moz-focus-inner,button::-moz-focus-inner {
            border-style: none;
            padding: 0
        }

        [type=button]:-moz-focusring,[type=reset]:-moz-focusring,[type=submit]:-moz-focusring,button:-moz-focusring {
            outline: 1px dotted ButtonText
        }

        fieldset {
            padding: .35em .75em .625em
        }

        legend {
            box-sizing: border-box;
            color: inherit;
            display: table;
            max-width: 100%;
            padding: 0;
            white-space: normal
        }

        progress {
            vertical-align: baseline
        }

        textarea {
            overflow: auto
        }

        [type=checkbox] {
            box-sizing: border-box;
            padding: 0
        }

        [type=number]::-webkit-inner-spin-button,[type=number]::-webkit-outer-spin-button {
            height: auto
        }

        [type=search] {
            -webkit-appearance: textfield;
            outline-offset: -2px
        }

        [type=search]::-webkit-search-decoration {
            -webkit-appearance: none
        }

        details {
            display: block
        }

        summary {
            display: list-item
        }

        [hidden],template {
            display: none
        }

        blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre {
            margin: 0
        }

        button {
            background-color: transparent;
            background-image: none
        }

        button:focus {
            outline: 1px dotted;
            outline: 5px auto -webkit-focus-ring-color
        }

        fieldset,ol,ul {
            margin: 0;
            padding: 0
        }

        ol,ul {
            list-style: none
        }

        html {
            font-family: system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;
            line-height: 1.5
        }

        *,:after,:before {
            border: 0 solid #ececec
        }

        hr {
            border-top-width: 1px
        }

        img {
            border-style: solid
        }

        textarea {
            resize: vertical
        }

        input::-moz-placeholder,textarea::-moz-placeholder {
            color: #a0aec0
        }

        input:-ms-input-placeholder,textarea:-ms-input-placeholder {
            color: #a0aec0
        }

        input::placeholder,textarea::placeholder {
            color: #a0aec0
        }

        button {
            cursor: pointer
        }

        table {
            border-collapse: collapse
        }

        h1,h2,h3,h4,h5,h6 {
            font-size: inherit;
            font-weight: inherit
        }

        a {
            color: inherit;
            text-decoration: inherit
        }

        button,input,optgroup,select,textarea {
            padding: 0;
            line-height: inherit;
            color: inherit
        }

        code,kbd,pre,samp {
            font-family: Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace
        }

        audio,canvas,embed,iframe,img,object,svg,video {
            display: block;
            vertical-align: middle
        }

        img,video {
            max-width: 100%;
            height: auto
        }

        .appearance-none {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none
        }

        .bg-primary {
            --bg-opacity:1;background-color: #89bd24;
            background-color: rgba(137,189,36,var(--bg-opacity))
        }

        .bg-secondary {
            --bg-opacity:1;background-color: #ff7f65 !important;
            background-color: rgba(230,0,126,var(--bg-opacity))
        }

        .bg-white {
            --bg-opacity:1;background-color: #fff;
            background-color: rgba(255,255,255,var(--bg-opacity))
        }

        .bg-gray-100 {
            --bg-opacity:1;background-color: #fafafa;
            background-color: rgba(250,250,250,var(--bg-opacity))
        }

        .bg-gray-200 {
            --bg-opacity:1;background-color: #f5f5f5;
            background-color: rgba(245,245,245,var(--bg-opacity))
        }

        .bg-gray-300 {
            --bg-opacity:1;background-color: #ececec;
            background-color: rgba(236,236,236,var(--bg-opacity))
        }

        .bg-gray-400 {
            --bg-opacity:1;background-color: #dbdbdb;
            background-color: rgba(219,219,219,var(--bg-opacity))
        }

        .bg-gray-500 {
            --bg-opacity:1;background-color: #bababa;
            background-color: rgba(186,186,186,var(--bg-opacity))
        }

        .bg-gray-600 {
            --bg-opacity:1;background-color: #8b8b8b;
            background-color: rgba(139,139,139,var(--bg-opacity))
        }

        .bg-gray-700 {
            --bg-opacity:1;background-color: #666;
            background-color: rgba(102,102,102,var(--bg-opacity))
        }

        .bg-gray-800 {
            --bg-opacity:1;background-color: #444;
            background-color: rgba(68,68,68,var(--bg-opacity))
        }

        .bg-red-600 {
            --bg-opacity:1;background-color: #e53e3e;
            background-color: rgba(229,62,62,var(--bg-opacity))
        }

        .bg-green-100 {
            --bg-opacity:1;background-color: #f0fff4;
            background-color: rgba(240,255,244,var(--bg-opacity))
        }

        .bg-green-200 {
            --bg-opacity:1;background-color: #c6f6d5;
            background-color: rgba(198,246,213,var(--bg-opacity))
        }

        .bg-blue-100 {
            --bg-opacity:1;background-color: #ebf8ff;
            background-color: rgba(235,248,255,var(--bg-opacity))
        }

        .bg-blue-500 {
            --bg-opacity:1;background-color: #4299e1;
            background-color: rgba(66,153,225,var(--bg-opacity))
        }

        .bg-blue-600 {
            --bg-opacity:1;background-color: #3182ce;
            background-color: rgba(49,130,206,var(--bg-opacity))
        }

        .bg-pink-200 {
            --bg-opacity:1;background-color: #fed7e2;
            background-color: rgba(254,215,226,var(--bg-opacity))
        }

        .hover\:bg-primary:hover {
            --bg-opacity:1;background-color: #89bd24;
            background-color: rgba(137,189,36,var(--bg-opacity))
        }

        .hover\:bg-secondary:hover {
            --bg-opacity:1;background-color: #ff7f65 !important;
            background-color: rgba(230,0,126,var(--bg-opacity))
        }

        .hover\:bg-gray-200:hover {
            --bg-opacity:1;background-color: #f5f5f5;
            background-color: rgba(245,245,245,var(--bg-opacity))
        }

        .hover\:bg-gray-300:hover {
            --bg-opacity:1;background-color: #ececec;
            background-color: rgba(236,236,236,var(--bg-opacity))
        }

        .hover\:bg-gray-800:hover {
            --bg-opacity:1;background-color: #444;
            background-color: rgba(68,68,68,var(--bg-opacity))
        }

        .even\:bg-gray-100:nth-child(2n),.odd\:bg-gray-100:nth-child(odd) {
            --bg-opacity:1;background-color: #fafafa;
            background-color: rgba(250,250,250,var(--bg-opacity))
        }

        .bg-opacity-25 {
            --bg-opacity:0.25}

        .bg-opacity-50 {
            --bg-opacity:0.5}

        .hover\:bg-opacity-75:hover {
            --bg-opacity:0.75}

        .border-primary {
            --border-opacity:1;border-color: #89bd24;
            border-color: rgba(137,189,36,var(--border-opacity))
        }

        .border-secondary {
            --border-opacity:1;border-color: #ff7f65 !important;
            border-color: rgba(230,0,126,var(--border-opacity))
        }

        .border-black {
            --border-opacity:1;border-color: #000;
            border-color: rgba(0,0,0,var(--border-opacity))
        }

        .border-white {
            --border-opacity:1;border-color: #fff;
            border-color: rgba(255,255,255,var(--border-opacity))
        }

        .border-gray-200 {
            --border-opacity:1;border-color: #f5f5f5;
            border-color: rgba(245,245,245,var(--border-opacity))
        }

        .border-gray-300 {
            --border-opacity:1;border-color: #ececec;
            border-color: rgba(236,236,236,var(--border-opacity))
        }

        .border-gray-400 {
            --border-opacity:1;border-color: #dbdbdb;
            border-color: rgba(219,219,219,var(--border-opacity))
        }

        .border-gray-500 {
            --border-opacity:1;border-color: #bababa;
            border-color: rgba(186,186,186,var(--border-opacity))
        }

        .border-red-300 {
            --border-opacity:1;border-color: #feb2b2;
            border-color: rgba(254,178,178,var(--border-opacity))
        }

        .border-green-400 {
            --border-opacity:1;border-color: #68d391;
            border-color: rgba(104,211,145,var(--border-opacity))
        }

        .border-blue-500 {
            --border-opacity:1;border-color: #4299e1;
            border-color: rgba(66,153,225,var(--border-opacity))
        }

        .hover\:border-secondary:hover {
            --border-opacity:1;border-color: #ff7f65 !important;
            border-color: rgba(230,0,126,var(--border-opacity))
        }

        .hover\:border-gray-400:hover {
            --border-opacity:1;border-color: #dbdbdb;
            border-color: rgba(219,219,219,var(--border-opacity))
        }

        .focus\:border-primary:focus {
            --border-opacity:1;border-color: #89bd24;
            border-color: rgba(137,189,36,var(--border-opacity))
        }

        .rounded-sm {
            border-radius: .125rem
        }

        .rounded {
            border-radius: .25rem
        }

        .rounded-md {
            border-radius: .375rem
        }

        .rounded-lg {
            border-radius: .5rem
        }

        .rounded-full {
            border-radius: 9999px
        }

        .rounded-r-full {
            border-top-right-radius: 9999px;
            border-bottom-right-radius: 9999px
        }

        .rounded-l-full {
            border-top-left-radius: 9999px;
            border-bottom-left-radius: 9999px
        }

        .border-solid {
            border-style: solid
        }

        .border-0 {
            border-width: 0
        }

        .border-2 {
            border-width: 2px
        }

        .border-4 {
            border-width: 4px
        }

        .border {
            border-width: 1px
        }

        .border-t-0 {
            border-top-width: 0
        }

        .border-r-0 {
            border-right-width: 0
        }

        .border-l-0 {
            border-left-width: 0
        }

        .border-t-2 {
            border-top-width: 2px
        }

        .border-t {
            border-top-width: 1px
        }

        .border-r {
            border-right-width: 1px
        }

        .border-b {
            border-bottom-width: 1px
        }

        .border-l {
            border-left-width: 1px
        }

        .first\:border-l-0:first-child {
            border-left-width: 0
        }

        .box-border {
            box-sizing: border-box
        }

        .box-content {
            box-sizing: content-box
        }

        .cursor-pointer {
            cursor: pointer
        }

        .cursor-wait {
            cursor: wait
        }

        .cursor-not-allowed {
            cursor: not-allowed
        }

        .disabled\:cursor-default:disabled {
            cursor: default
        }

        .disabled\:cursor-not-allowed:disabled {
            cursor: not-allowed
        }

        .block {
            display: block
        }

        .inline-block {
            display: inline-block
        }

        .inline {
            display: inline
        }

        .flex {
            display: flex
        }

        .inline-flex {
            display: inline-flex
        }

        .table {
            display: table
        }

        .grid {
            display: grid
        }

        .hidden {
            display: none
        }

        .flex-row {
            flex-direction: row
        }

        .flex-col {
            flex-direction: column
        }

        .flex-wrap {
            flex-wrap: wrap
        }

        .items-start {
            align-items: flex-start
        }

        .items-end {
            align-items: flex-end
        }

        .items-center {
            align-items: center
        }

        .self-start {
            align-self: flex-start
        }

        .self-end {
            align-self: flex-end
        }

        .self-center {
            align-self: center
        }

        .justify-start {
            justify-content: flex-start
        }

        .justify-end {
            justify-content: flex-end
        }

        .justify-center {
            justify-content: center
        }

        .justify-between {
            justify-content: space-between
        }

        .justify-evenly {
            justify-content: space-evenly
        }

        .flex-1 {
            flex: 1 1 0%
        }

        .flex-grow {
            flex-grow: 1
        }

        .flex-shrink-0 {
            flex-shrink: 0
        }

        .flex-shrink {
            flex-shrink: 1
        }

        .float-none {
            float: none
        }

        .font-semibold {
            font-weight: 600
        }

        .font-bold {
            font-weight: 700
        }

        .font-black {
            font-weight: 900
        }

        .h-1 {
            height: .25rem
        }

        .h-3 {
            height: .75rem
        }

        .h-4 {
            height: 1rem
        }

        .h-5 {
            height: 1.25rem
        }

        .h-6 {
            height: 1.5rem
        }

        .h-8 {
            height: 2rem
        }

        .h-10 {
            height: 2.5rem
        }

        .h-12 {
            height: 3rem
        }

        .h-16 {
            height: 4rem
        }

        .h-20 {
            height: 5rem
        }

        .h-24 {
            height: 6rem
        }

        .h-32 {
            height: 8rem
        }

        .h-40 {
            height: 10rem
        }

        .h-48 {
            height: 12rem
        }

        .h-56 {
            height: 14rem
        }

        .h-64 {
            height: 16rem
        }

        .h-auto {
            height: auto
        }

        .h-px {
            height: 1px
        }

        .h-full {
            height: 100%
        }

        .h-screen {
            height: 100vh
        }

        .text-xs {
            font-size: .75rem
        }

        .text-sm {
            font-size: .875rem
        }

        .text-base {
            font-size: 1rem
        }

        .text-lg {
            font-size: 1.125rem
        }

        .text-xl {
            font-size: 1.25rem
        }

        .text-2xl {
            font-size: 1.5rem
        }

        .text-3xl {
            font-size: 1.875rem
        }

        .text-4xl {
            font-size: 2.25rem
        }

        .leading-5 {
            line-height: 1.25rem
        }

        .leading-6 {
            line-height: 1.5rem
        }

        .leading-7 {
            line-height: 1.75rem
        }

        .leading-8 {
            line-height: 2rem
        }

        .leading-none {
            line-height: 1
        }

        .leading-tight {
            line-height: 1.25
        }

        .leading-relaxed {
            line-height: 1.625
        }

        .list-none {
            list-style-type: none
        }

        .m-0 {
            margin: 0
        }

        .m-1 {
            margin: .25rem
        }

        .m-2 {
            margin: .5rem
        }

        .m-3 {
            margin: .75rem
        }

        .m-4 {
            margin: 1rem
        }

        .my-0 {
            margin-top: 0;
            margin-bottom: 0
        }

        .my-1 {
            margin-top: .25rem;
            margin-bottom: .25rem
        }

        .mx-1 {
            margin-left: .25rem;
            margin-right: .25rem
        }

        .my-2 {
            margin-top: .5rem;
            margin-bottom: .5rem
        }

        .mx-2 {
            margin-left: .5rem;
            margin-right: .5rem
        }

        .my-3 {
            margin-top: .75rem;
            margin-bottom: .75rem
        }

        .my-4 {
            margin-top: 1rem;
            margin-bottom: 1rem
        }

        .mx-4 {
            margin-left: 1rem;
            margin-right: 1rem
        }

        .my-5 {
            margin-top: 1.25rem;
            margin-bottom: 1.25rem
        }

        .my-6 {
            margin-top: 1.5rem;
            margin-bottom: 1.5rem
        }

        .my-8 {
            margin-top: 2rem;
            margin-bottom: 2rem
        }

        .mx-10 {
            margin-left: 2.5rem;
            margin-right: 2.5rem
        }

        .my-12 {
            margin-top: 3rem;
            margin-bottom: 3rem
        }

        .my-32 {
            margin-top: 8rem;
            margin-bottom: 8rem
        }

        .mx-auto {
            margin-left: auto;
            margin-right: auto
        }

        .-mx-2 {
            margin-left: -.5rem;
            margin-right: -.5rem
        }

        .-mx-4 {
            margin-left: -1rem;
            margin-right: -1rem
        }

        .mt-0 {
            margin-top: 0
        }

        .mr-0 {
            margin-right: 0
        }

        .mt-1 {
            margin-top: .25rem
        }

        .mr-1 {
            margin-right: .25rem
        }

        .mb-1 {
            margin-bottom: .25rem
        }

        .ml-1 {
            margin-left: .25rem
        }

        .mt-2 {
            margin-top: .5rem
        }

        .mr-2 {
            margin-right: .5rem
        }

        .mb-2 {
            margin-bottom: .5rem
        }

        .ml-2 {
            margin-left: .5rem
        }

        .mt-3 {
            margin-top: .75rem
        }

        .mr-3 {
            margin-right: .75rem
        }

        .mb-3 {
            margin-bottom: .75rem
        }

        .ml-3 {
            margin-left: .75rem
        }

        .mt-4 {
            margin-top: 1rem
        }

        .mr-4 {
            margin-right: 1rem
        }

        .mb-4 {
            margin-bottom: 1rem
        }

        .ml-4 {
            margin-left: 1rem
        }

        .mt-5 {
            margin-top: 1.25rem
        }

        .mr-5 {
            margin-right: 1.25rem
        }

        .mb-5 {
            margin-bottom: 1.25rem
        }

        .ml-5 {
            margin-left: 1.25rem
        }

        .mt-6 {
            margin-top: 1.5rem
        }

        .mr-6 {
            margin-right: 1.5rem
        }

        .mb-6 {
            margin-bottom: 1.5rem
        }

        .ml-6 {
            margin-left: 1.5rem
        }

        .mt-8 {
            margin-top: 2rem
        }

        .mb-8 {
            margin-bottom: 2rem
        }

        .mt-10 {
            margin-top: 2.5rem
        }

        .mb-10 {
            margin-bottom: 2.5rem
        }

        .mt-12 {
            margin-top: 3rem
        }

        .mt-16 {
            margin-top: 4rem
        }

        .mt-24 {
            margin-top: 6rem
        }

        .ml-auto {
            margin-left: auto
        }

        .mb-px {
            margin-bottom: 1px
        }

        .-mr-1 {
            margin-right: -.25rem
        }

        .-mr-2 {
            margin-right: -.5rem
        }

        .-mb-2 {
            margin-bottom: -.5rem
        }

        .-mb-5 {
            margin-bottom: -1.25rem
        }

        .-mt-6 {
            margin-top: -1.5rem
        }

        .max-w-xs {
            max-width: 20rem
        }

        .max-w-sm {
            max-width: 24rem
        }

        .max-w-md {
            max-width: 28rem
        }

        .max-w-full {
            max-width: 100%
        }

        .min-h-64 {
            min-height: 16rem
        }

        .min-w-1\/4 {
            min-width: 25%
        }

        .object-contain {
            -o-object-fit: contain;
            object-fit: contain
        }

        .object-cover {
            -o-object-fit: cover;
            object-fit: cover
        }

        .opacity-25 {
            opacity: .25
        }

        .opacity-50 {
            opacity: .5
        }

        .hover\:opacity-75:hover,.opacity-75 {
            opacity: .75
        }

        .disabled\:opacity-25:disabled {
            opacity: .25
        }

        .active\:opacity-50:active,.disabled\:opacity-50:disabled {
            opacity: .5
        }

        .focus\:outline-none:focus,.outline-none {
            outline: 0
        }

        .overflow-auto {
            overflow: auto
        }

        .overflow-hidden {
            overflow: hidden
        }

        .overflow-x-auto {
            overflow-x: auto
        }

        .overflow-y-auto {
            overflow-y: auto
        }

        .overflow-y-hidden {
            overflow-y: hidden
        }

        .overflow-x-scroll {
            overflow-x: scroll
        }

        .overflow-y-scroll {
            overflow-y: scroll
        }

        .p-0 {
            padding: 0
        }

        .p-1 {
            padding: .25rem
        }

        .p-2 {
            padding: .5rem
        }

        .p-3 {
            padding: .75rem
        }

        .p-4 {
            padding: 1rem
        }

        .p-5 {
            padding: 1.25rem
        }

        .p-6 {
            padding: 1.5rem
        }

        .p-px {
            padding: 1px
        }

        .py-0 {
            padding-top: 0;
            padding-bottom: 0
        }

        .px-0 {
            padding-left: 0;
            padding-right: 0
        }

        .py-1 {
            padding-top: .25rem;
            padding-bottom: .25rem
        }

        .px-1 {
            padding-left: .25rem;
            padding-right: .25rem
        }

        .py-2 {
            padding-top: .5rem;
            padding-bottom: .5rem
        }

        .px-2 {
            padding-left: .5rem;
            padding-right: .5rem
        }

        .py-3 {
            padding-top: .75rem;
            padding-bottom: .75rem
        }

        .px-3 {
            padding-left: .75rem;
            padding-right: .75rem
        }

        .py-4 {
            padding-top: 1rem;
            padding-bottom: 1rem
        }

        .px-4 {
            padding-left: 1rem;
            padding-right: 1rem
        }

        .py-5 {
            padding-top: 1.25rem;
            padding-bottom: 1.25rem
        }

        .px-5 {
            padding-left: 1.25rem;
            padding-right: 1.25rem
        }

        .py-6 {
            padding-top: 1.5rem;
            padding-bottom: 1.5rem
        }

        .px-6 {
            padding-left: 1.5rem;
            padding-right: 1.5rem
        }

        .py-8 {
            padding-top: 2rem;
            padding-bottom: 2rem
        }

        .px-8 {
            padding-left: 2rem;
            padding-right: 2rem
        }

        .py-64 {
            padding-top: 16rem;
            padding-bottom: 16rem
        }

        .px-px {
            padding-left: 1px;
            padding-right: 1px
        }

        .pt-1 {
            padding-top: .25rem
        }

        .pr-1 {
            padding-right: .25rem
        }

        .pb-1 {
            padding-bottom: .25rem
        }

        .pl-1 {
            padding-left: .25rem
        }

        .pr-2 {
            padding-right: .5rem
        }

        .pb-2 {
            padding-bottom: .5rem
        }

        .pl-2 {
            padding-left: .5rem
        }

        .pt-3 {
            padding-top: .75rem
        }

        .pb-3 {
            padding-bottom: .75rem
        }

        .pl-3 {
            padding-left: .75rem
        }

        .pt-4 {
            padding-top: 1rem
        }

        .pr-4 {
            padding-right: 1rem
        }

        .pb-4 {
            padding-bottom: 1rem
        }

        .pl-4 {
            padding-left: 1rem
        }

        .pt-5 {
            padding-top: 1.25rem
        }

        .pl-5 {
            padding-left: 1.25rem
        }

        .pb-6 {
            padding-bottom: 1.5rem
        }

        .pr-8 {
            padding-right: 2rem
        }

        .pb-8 {
            padding-bottom: 2rem
        }

        .pl-8 {
            padding-left: 2rem
        }

        .pt-16 {
            padding-top: 4rem
        }

        .pb-20 {
            padding-bottom: 5rem
        }

        .first\:pl-0:first-child {
            padding-left: 0
        }

        .pointer-events-none {
            pointer-events: none
        }

        .fixed {
            position: fixed
        }

        .absolute {
            position: absolute
        }

        .relative {
            position: relative
        }

        .sticky {
            position: -webkit-sticky;
            position: sticky
        }

        .top-0 {
            top: 0
        }

        .right-0 {
            right: 0
        }

        .bottom-0 {
            bottom: 0
        }

        .left-0 {
            left: 0
        }

        .shadow {
            box-shadow: 0 1px 3px 0 rgba(0,0,0,.04),0 1px 2px 0 rgba(0,0,0,.04)
        }

        .shadow-md {
            box-shadow: 0 4px 6px -1px rgba(0,0,0,.04),0 2px 4px -1px rgba(0,0,0,.03)
        }

        .shadow-lg {
            box-shadow: 0 10px 15px -3px rgba(0,0,0,.04),0 4px 6px -2px rgba(0,0,0,.02)
        }

        .focus\:shadow-outline:focus {
            box-shadow: 0 0 0 2px #89be24
        }

        .focus\:shadow-outline-inset:focus {
            box-shadow: inset 0 0 0 2px #89be24
        }

        .focus\:shadow-error:focus {
            box-shadow: 0 0 0 2px #ff7887
        }

        .text-left {
            text-align: left
        }

        .text-center {
            text-align: center
        }

        .text-right {
            text-align: right
        }

        .text-primary {
            --text-opacity:1;color: #89bd24;
            color: rgba(137,189,36,var(--text-opacity))
        }

        .text-secondary {
            --text-opacity:1;color: #ff7f65 !important;
            color: rgba(230,0,126,var(--text-opacity))
        }

        .text-black {
            --text-opacity:1;color: #000;
            color: rgba(0,0,0,var(--text-opacity))
        }

        .text-white {
            --text-opacity:1;color: #fff;
            color: rgba(255,255,255,var(--text-opacity))
        }

        .text-gray-400 {
            --text-opacity:1;color: #dbdbdb;
            color: rgba(219,219,219,var(--text-opacity))
        }

        .text-gray-500 {
            --text-opacity:1;color: #bababa;
            color: rgba(186,186,186,var(--text-opacity))
        }

        .text-gray-600 {
            --text-opacity:1;color: #8b8b8b;
            color: rgba(139,139,139,var(--text-opacity))
        }

        .text-gray-700 {
            --text-opacity:1;color: #666;
            color: rgba(102,102,102,var(--text-opacity))
        }

        .text-gray-800 {
            --text-opacity:1;color: #444;
            color: rgba(68,68,68,var(--text-opacity))
        }

        .text-gray-900 {
            --text-opacity:1;color: #232323;
            color: rgba(35,35,35,var(--text-opacity))
        }

        .text-red-500 {
            --text-opacity:1;color: #f56565;
            color: rgba(245,101,101,var(--text-opacity))
        }

        .text-red-600 {
            --text-opacity:1;color: #e53e3e;
            color: rgba(229,62,62,var(--text-opacity))
        }

        .text-yellow-600 {
            --text-opacity:1;color: #d69e2e;
            color: rgba(214,158,46,var(--text-opacity))
        }

        .text-green-500 {
            --text-opacity:1;color: #48bb78;
            color: rgba(72,187,120,var(--text-opacity))
        }

        .text-green-600 {
            --text-opacity:1;color: #38a169;
            color: rgba(56,161,105,var(--text-opacity))
        }

        .text-blue-500 {
            --text-opacity:1;color: #4299e1;
            color: rgba(66,153,225,var(--text-opacity))
        }

        .text-blue-700 {
            --text-opacity:1;color: #2b6cb0;
            color: rgba(43,108,176,var(--text-opacity))
        }

        .hover\:text-primary:hover {
            --text-opacity:1;color: #89bd24;
            color: rgba(137,189,36,var(--text-opacity))
        }

        .hover\:text-white:hover {
            --text-opacity:1;color: #fff;
            color: rgba(255,255,255,var(--text-opacity))
        }

        .hover\:text-gray-600:hover {
            --text-opacity:1;color: #8b8b8b;
            color: rgba(139,139,139,var(--text-opacity))
        }

        .focus\:text-primary:focus {
            --text-opacity:1;color: #89bd24;
            color: rgba(137,189,36,var(--text-opacity))
        }

        .italic {
            font-style: italic
        }

        .uppercase {
            text-transform: uppercase
        }

        .capitalize {
            text-transform: capitalize
        }

        .underline {
            text-decoration: underline
        }

        .line-through {
            text-decoration: line-through
        }

        .focus\:underline:focus {
            text-decoration: underline
        }

        .select-none {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none
        }

        .visible {
            visibility: visible
        }

        .invisible {
            visibility: hidden
        }

        .whitespace-no-wrap {
            white-space: nowrap
        }

        .whitespace-pre-line {
            white-space: pre-line
        }

        .break-words {
            overflow-wrap: break-word
        }

        .w-3 {
            width: .75rem
        }

        .w-4 {
            width: 1rem
        }

        .w-5 {
            width: 1.25rem
        }

        .w-8 {
            width: 2rem
        }

        .w-10 {
            width: 2.5rem
        }

        .w-20 {
            width: 5rem
        }

        .w-32 {
            width: 8rem
        }

        .w-40 {
            width: 10rem
        }

        .w-48 {
            width: 12rem
        }

        .w-56 {
            width: 14rem
        }

        .w-64 {
            width: 16rem
        }

        .w-72 {
            width: 19rem
        }

        .w-100 {
            width: 25rem
        }

        .w-auto {
            width: auto
        }

        .w-1\/2 {
            width: 50%
        }

        .w-1\/4 {
            width: 25%
        }

        .w-3\/4 {
            width: 75%
        }

        .w-8\/12 {
            width: 66.666667%
        }

        .w-9\/12 {
            width: 75%
        }

        .w-11\/12 {
            width: 91.666667%
        }

        .w-full {
            width: 100%
        }

        .w-screen {
            width: 100vw
        }

        .z-0 {
            z-index: 0
        }

        .z-10 {
            z-index: 10
        }

        .z-20 {
            z-index: 20
        }

        .z-30 {
            z-index: 30
        }

        .z-40 {
            z-index: 40
        }

        .z-50 {
            z-index: 50
        }

        .transform {
            --transform-translate-x:0;--transform-translate-y:0;--transform-rotate:0;--transform-skew-x:0;--transform-skew-y:0;--transform-scale-x:1;--transform-scale-y:1;transform: translateX(var(--transform-translate-x)) translateY(var(--transform-translate-y)) rotate(var(--transform-rotate)) skewX(var(--transform-skew-x)) skewY(var(--transform-skew-y)) scaleX(var(--transform-scale-x)) scaleY(var(--transform-scale-y))
        }

        .rotate-0 {
            --transform-rotate:0}

        .rotate-45 {
            --transform-rotate:45deg}

        .transition-all {
            transition-property: all
        }

        .transition {
            transition-property: background-color,border-color,color,fill,stroke,opacity,box-shadow,transform
        }

        .ease-in-out {
            transition-timing-function: cubic-bezier(.4,0,.2,1)
        }

        .duration-100 {
            transition-duration: .1s
        }

        .duration-200 {
            transition-duration: .2s
        }

        .duration-300 {
            transition-duration: .3s
        }

        .duration-500 {
            transition-duration: .5s
        }

        @-webkit-keyframes spin {
            to {
                transform: rotate(1turn)
            }
        }

        @keyframes spin {
            to {
                transform: rotate(1turn)
            }
        }

        @-webkit-keyframes ping {
            75%,to {
                transform: scale(2);
                opacity: 0
            }
        }

        @keyframes ping {
            75%,to {
                transform: scale(2);
                opacity: 0
            }
        }

        @-webkit-keyframes pulse {
            50% {
                opacity: .5
            }
        }

        @keyframes pulse {
            50% {
                opacity: .5
            }
        }

        @-webkit-keyframes bounce {
            0%,to {
                transform: translateY(-25%);
                -webkit-animation-timing-function: cubic-bezier(.8,0,1,1);
                animation-timing-function: cubic-bezier(.8,0,1,1)
            }

            50% {
                transform: none;
                -webkit-animation-timing-function: cubic-bezier(0,0,.2,1);
                animation-timing-function: cubic-bezier(0,0,.2,1)
            }
        }

        @keyframes bounce {
            0%,to {
                transform: translateY(-25%);
                -webkit-animation-timing-function: cubic-bezier(.8,0,1,1);
                animation-timing-function: cubic-bezier(.8,0,1,1)
            }

            50% {
                transform: none;
                -webkit-animation-timing-function: cubic-bezier(0,0,.2,1);
                animation-timing-function: cubic-bezier(0,0,.2,1)
            }
        }

        .animate-spin {
            -webkit-animation: spin 1s linear infinite;
            animation: spin 1s linear infinite
        }

        @media(min-width: 640px) {
            .sm\:bg-white {
                --bg-opacity:1;
                background-color: #fff;
                background-color: rgba(255,255,255,var(--bg-opacity))
            }

            .sm\:border-secondary {
                --border-opacity:1;border-color: #ff7f65 !important;
                border-color: rgba(230,0,126,var(--border-opacity))
            }

            .sm\:rounded-lg {
                border-radius: .5rem
            }

            .sm\:border-2 {
                border-width: 2px
            }

            .sm\:border-b-0 {
                border-bottom-width: 0
            }

            .sm\:block {
                display: block
            }

            .sm\:inline {
                display: inline
            }

            .sm\:flex {
                display: flex
            }

            .sm\:hidden {
                display: none
            }

            .sm\:flex-row {
                flex-direction: row
            }

            .sm\:items-start {
                align-items: flex-start
            }

            .sm\:justify-start {
                justify-content: flex-start
            }

            .sm\:h-32 {
                height: 8rem
            }

            .sm\:h-40 {
                height: 10rem
            }

            .sm\:h-48 {
                height: 12rem
            }

            .sm\:h-56 {
                height: 14rem
            }

            .sm\:h-auto {
                height: auto
            }

            .sm\:text-sm {
                font-size: .875rem
            }

            .sm\:text-base {
                font-size: 1rem
            }

            .sm\:text-lg {
                font-size: 1.125rem
            }

            .sm\:text-xl {
                font-size: 1.25rem
            }

            .sm\:text-3xl {
                font-size: 1.875rem
            }

            .sm\:leading-loose {
                line-height: 2
            }

            .sm\:m-0 {
                margin: 0
            }

            .sm\:my-0 {
                margin-top: 0;
                margin-bottom: 0
            }

            .sm\:mx-1 {
                margin-left: .25rem;
                margin-right: .25rem
            }

            .sm\:mt-0 {
                margin-top: 0
            }

            .sm\:mb-0 {
                margin-bottom: 0
            }

            .sm\:ml-2 {
                margin-left: .5rem
            }

            .sm\:ml-3 {
                margin-left: .75rem
            }

            .sm\:mr-4 {
                margin-right: 1rem
            }

            .sm\:ml-4 {
                margin-left: 1rem
            }

            .sm\:ml-6 {
                margin-left: 1.5rem
            }

            .sm\:mt-10 {
                margin-top: 2.5rem
            }

            .sm\:p-0 {
                padding: 0
            }

            .sm\:p-8 {
                padding: 2rem
            }

            .sm\:py-2 {
                padding-top: .5rem;
                padding-bottom: .5rem
            }

            .sm\:px-2 {
                padding-left: .5rem;
                padding-right: .5rem
            }

            .sm\:py-3 {
                padding-top: .75rem;
                padding-bottom: .75rem
            }

            .sm\:px-4 {
                padding-left: 1rem;
                padding-right: 1rem
            }

            .sm\:px-8 {
                padding-left: 2rem;
                padding-right: 2rem
            }

            .sm\:pl-4 {
                padding-left: 1rem
            }

            .sm\:pr-6 {
                padding-right: 1.5rem
            }

            .sm\:static {
                position: static
            }

            .sm\:relative {
                position: relative
            }

            .sm\:sticky {
                position: -webkit-sticky;
                position: sticky
            }

            .sm\:top-0 {
                top: 0
            }

            .sm\:text-left {
                text-align: left
            }

            .sm\:text-black {
                --text-opacity:1;color: #000;
                color: rgba(0,0,0,var(--text-opacity))
            }

            .sm\:w-40 {
                width: 10rem
            }

            .sm\:w-48 {
                width: 12rem
            }

            .sm\:w-56 {
                width: 14rem
            }

            .sm\:w-auto {
                width: auto
            }

            .sm\:w-1\/3 {
                width: 33.333333%
            }

            .sm\:w-2\/3 {
                width: 66.666667%
            }

            .sm\:w-1\/4 {
                width: 25%
            }
        }

        @media(min-width: 768px) {
            .md\:border-gray-300 {
                --border-opacity:1;
                border-color: #ececec;
                border-color: rgba(236,236,236,var(--border-opacity))
            }

            .md\:flex-row {
                flex-direction: row
            }

            .md\:justify-between {
                justify-content: space-between
            }

            .md\:h-40 {
                height: 10rem
            }

            .md\:h-48 {
                height: 12rem
            }

            .md\:h-56 {
                height: 14rem
            }

            .md\:text-3xl {
                font-size: 1.875rem
            }

            .md\:text-4xl {
                font-size: 2.25rem
            }

            .md\:w-40 {
                width: 10rem
            }

            .md\:w-48 {
                width: 12rem
            }

            .md\:w-56 {
                width: 14rem
            }

            .md\:w-auto {
                width: auto
            }

            .md\:w-1\/4 {
                width: 25%
            }
        }

        @media(min-width: 1024px) {
            .lg\:block {
                display:block
            }

            .lg\:flex {
                display: flex
            }

            .lg\:flex-row {
                flex-direction: row
            }

            .lg\:even\:flex-row-reverse:nth-child(2n),.lg\:first\:flex-row-reverse:first-child,.lg\:flex-row-reverse {
                flex-direction: row-reverse
            }

            .lg\:justify-between {
                justify-content: space-between
            }

            .lg\:h-40 {
                height: 10rem
            }

            .lg\:h-56 {
                height: 14rem
            }

            .lg\:h-64 {
                height: 16rem
            }

            .lg\:text-base {
                font-size: 1rem
            }

            .lg\:text-xl {
                font-size: 1.25rem
            }

            .lg\:text-4xl {
                font-size: 2.25rem
            }

            .lg\:m-0 {
                margin: 0
            }

            .lg\:mt-0 {
                margin-top: 0
            }

            .lg\:ml-4 {
                margin-left: 1rem
            }

            .lg\:first\:mt-0:first-child {
                margin-top: 0
            }

            .lg\:max-w-sm {
                max-width: 24rem
            }

            .lg\:max-w-lg {
                max-width: 32rem
            }

            .lg\:p-0 {
                padding: 0
            }

            .lg\:p-10 {
                padding: 2.5rem
            }

            .lg\:py-4 {
                padding-top: 1rem;
                padding-bottom: 1rem
            }

            .lg\:px-4 {
                padding-left: 1rem;
                padding-right: 1rem
            }

            .lg\:py-6 {
                padding-top: 1.5rem;
                padding-bottom: 1.5rem
            }

            .lg\:px-6 {
                padding-left: 1.5rem;
                padding-right: 1.5rem
            }

            .lg\:pr-10 {
                padding-right: 2.5rem
            }

            .lg\:text-right {
                text-align: right
            }

            .lg\:w-40 {
                width: 10rem
            }

            .lg\:w-56 {
                width: 14rem
            }

            .lg\:w-64 {
                width: 16rem
            }

            .lg\:w-auto {
                width: auto
            }

            .lg\:w-1\/2 {
                width: 50%
            }

            .lg\:w-1\/3 {
                width: 33.333333%
            }

            .lg\:w-3\/4 {
                width: 75%
            }

            .lg\:w-1\/5 {
                width: 20%
            }

            .lg\:w-5\/12 {
                width: 41.666667%
            }
        }

        @media(min-width: 1280px) {
            .xl\:border-0 {
                border-width:0
            }

            .xl\:block {
                display: block
            }

            .xl\:hidden {
                display: none
            }

            .xl\:flex-row {
                flex-direction: row
            }

            .xl\:justify-between {
                justify-content: space-between
            }

            .xl\:h-40 {
                height: 10rem
            }

            .xl\:h-56 {
                height: 14rem
            }

            .xl\:h-64 {
                height: 16rem
            }

            .xl\:h-full {
                height: 100%
            }

            .xl\:text-base {
                font-size: 1rem
            }

            .xl\:text-xl {
                font-size: 1.25rem
            }

            .xl\:py-0 {
                padding-top: 0;
                padding-bottom: 0
            }

            .xl\:w-40 {
                width: 10rem
            }

            .xl\:w-56 {
                width: 14rem
            }

            .xl\:w-64 {
                width: 16rem
            }

            .xl\:w-auto {
                width: auto
            }

            .xl\:w-1\/3 {
                width: 33.333333%
            }
        }

        @media print {
            .print\:block {
                display: block
            }

            .print\:hidden {
                display: none
            }
        }

        html {
            font-size: 16px;
            font-family: Cabin,HelveticaNeue,Helvetica Neue,Helvetica,Arial,sans-serif;
            word-spacing: 1px;
            -ms-text-size-adjust: 100%;
            -webkit-text-size-adjust: 100%;
            -moz-osx-font-smoothing: grayscale;
            -webkit-font-smoothing: antialiased;
            box-sizing: border-box
        }

        *,:after,:before {
            box-sizing: border-box;
            margin: 0
        }

        .button--green {
            display: inline-block;
            border-radius: 4px;
            border: 1px solid #3b8070;
            color: #3b8070;
            text-decoration: none;
            padding: 10px 30px
        }

        .button--green:hover {
            color: #fff;
            background-color: #3b8070
        }

        .button--grey {
            display: inline-block;
            border-radius: 4px;
            border: 1px solid #35495e;
            color: #35495e;
            text-decoration: none;
            padding: 10px 30px;
            margin-left: 15px
        }

        .button--grey:hover {
            color: #fff;
            background-color: #35495e
        }

        .toasted {
            border-radius: 9999px!important
        }

        .toasted-container.bottom-right {
            right: 30px!important;
            bottom: 30px!important;
            min-width: auto!important
        }

        @media only screen and (max-width: 600px) {
            .toasted-container.bottom-right {
                right:20px!important;
                bottom: 20px!important;
                min-width: auto!important
            }
        }

        .vs__dropdown-toggle {
            margin-top: 1px!important;
            padding: 4px 0!important;
            border-radius: 40px!important;
            border-color: #d8d8d8!important
        }

        .html-content h1 {
            font-size: 2em;
            margin-bottom: .67em
        }

        .html-content h1,.html-content h2 {
            display: block;
            margin-left: 0;
            margin-right: 0;
            font-weight: 700
        }

        .html-content h2 {
            font-size: 1.5em;
            margin-bottom: .83em
        }

        .html-content h3 {
            font-size: 1.17em;
            margin-bottom: 1em
        }

        .html-content h3,.html-content h4 {
            display: block;
            margin-left: 0;
            margin-right: 0;
            font-weight: 700
        }

        .html-content h4 {
            font-size: 1em;
            margin-bottom: 1.33em
        }

        .html-content h5 {
            font-size: .83em;
            margin-bottom: 1.67em
        }

        .html-content h5,.html-content h6 {
            display: block;
            margin-left: 0;
            margin-right: 0;
            font-weight: 700
        }

        .html-content h6 {
            font-size: .67em;
            margin-bottom: 2.33em
        }

        .html-content p {
            display: block;
            margin-bottom: 1em;
            margin-left: 0;
            margin-right: 0
        }

        .html-content p:last-of-type {
            margin-bottom: 0
        }

        .html-content table {
            border: 1px solid #999;
            border-spacing: 2px;
            vertical-align: inherit;
            overflow-x: auto
        }

        .html-content td,.html-content th {
            border: 1px solid #999;
            padding: .25rem
        }

        .html-content table p {
            margin-bottom: 0
        }

        .html-content a {
            color: #00008b;
            text-decoration: underline
        }

        .html-content ul {
            padding-left: 40px;
            list-style: initial
        }

        .html-content ol {
            padding-left: 40px;
            list-style: decimal
        }

        .html-content .yt-iframe iframe {
            width: 100%;
            max-width: 640px;
            height: 360px
        }

        .html-content .scrollable-table {
            overflow-x: auto
        }

        @media(min-width: 1024px) {
            .big-banner {
                max-height:300px
            }
        }

        @media(min-width: 1280px) {
            .big-banner {
                max-height:352px
            }
        }

        .vue-slider-rail {
            background-color: rgba(137,189,36,.3)!important
        }

        .vue-slider-dot-handle,.vue-slider-process {
            background-color: #89bd24!important
        }

        .vue-slider-dot-handle {
            height: 20px!important;
            width: 20px!important;
            bottom: 3px!important;
            right: 3px!important
        }

        .vue-slider-dot-handle:after {
            background-color: rgba(137,189,36,.38)!important
        }

        .vue-slider-dot-tooltip-inner {
            background-color: #89bd24!important
        }

        .ais-SearchBox-input {
            padding: 3px 6px;
            font-size: 15px;
            border: 1px solid #dadada;
            border-radius: 4px;
            margin-bottom: 1px;
            outline: none
        }

        .ais-SearchBox-input:focus {
            border-color: #89bd24
        }

        .ais-RefinementList-showMore {
            color: #89bd24
        }

        .v--modal-overlay {
            z-index: 9999999!important
        }

        .ais-InstantSearch {
            width: 100%
        }

        .ais-Pagination {
            margin: 1rem 0
        }

        .ais-Pagination-list {
            display: flex
        }

        .ais-Pagination-link {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: visible;
            transition-duration: .15s;
            border-radius: .5rem
        }

        .ais-Pagination-link:hover {
            z-index: 1000;
            color: #89bd24
        }

        .ais-Pagination-list {
            justify-content: center
        }

        .ais-Pagination-list>* {
            background: #eee;
            color: #333;
            overflow: visible
        }

        .ais-Pagination-item {
            margin: 0!important;
            width: 2rem;
            height: 2rem;
            display: flex;
            align-items: center;
            justify-content: center
        }

        .ais-Pagination-item:nth-of-type(3) {
            border-top-left-radius: 1rem;
            border-bottom-left-radius: 1rem;
            padding-left: .5rem;
            width: 2.5rem
        }

        .ais-Pagination-item:nth-last-of-type(3) {
            border-top-right-radius: 1rem;
            border-bottom-right-radius: 1rem;
            padding-right: .5rem;
            width: 2.5rem
        }

        .ais-Pagination-item:nth-of-type(3):nth-last-of-type(3) {
            width: 3rem
        }

        .ais-Pagination-item--disabled {
            opacity: .5
        }

        .ais-Pagination-item--disabled .ais-Pagination-link:hover {
            box-shadow: none
        }

        .ais-Pagination-item--selected .ais-Pagination-link,.ais-Pagination-item--selected .ais-Pagination-link:hover {
            background: #89bd24;
            color: #fff;
            box-shadow: 0 0 8px 2px rgba(137,189,36,.6),0 2px 4px -1px rgba(137,189,36,.12);
            z-index: 1000
        }

        .ais-Pagination-item--firstPage,.ais-Pagination-item--previousPage {
            margin-right: .5rem!important;
            border-radius: 100%
        }

        .ais-Pagination-item--lastPage,.ais-Pagination-item--nextPage {
            margin-left: .5rem!important;
            border-radius: 100%
        }

        .ais-Pagination-item--firstPage .ais-Pagination-link,.ais-Pagination-item--lastPage .ais-Pagination-link,.ais-Pagination-item--nextPage .ais-Pagination-link,.ais-Pagination-item--previousPage .ais-Pagination-link {
            border-radius: 100%!important;
            padding-bottom: .1rem
        }

        .ais-Pagination-item--firstPage .ais-Pagination-link:hover,.ais-Pagination-item--lastPage .ais-Pagination-link:hover,.ais-Pagination-item--nextPage .ais-Pagination-link:hover,.ais-Pagination-item--previousPage .ais-Pagination-link:hover {
            background: #89bd24;
            color: #fff
        }

        @media(max-width: 399.98px) {
            .img-fluid-mobile[data-v-75635a24] .progressive-image-main {
                max-width:100%;
                height: auto!important
            }

            .img-no-right-margin[data-v-75635a24] {
                margin-right: 0
            }
        }

        .img-h-32[data-v-75635a24] .progressive-image-main {
            position: relative;
            height: 8rem;
            width: auto
        }

        .img-h-12[data-v-75635a24] .progressive-image-main {
            position: relative;
            height: 3rem;
            width: auto
        }

        .img-h-12[data-v-75635a24] .progressive-image-wrapper,.img-h-32[data-v-75635a24] .progressive-image-wrapper {
            padding-bottom: 0!important
        }

        .hide-enter-active,.hide-leave-active {
            transition: .2s
        }

        .hide-enter,.hide-leave-to {
            transform: translateX(-100%)
        }

        .fade-enter-active,.fade-leave-active {
            transition: all .2s ease-in-out
        }

        .fade-leave-active {
            opacity: 1
        }

        .fade-enter,.fade-leave-to {
            opacity: 0
        }

        .hidden-scroll {
            scrollbar-width: none;
            -ms-overflow-style: none
        }

        .hidden-scroll::-webkit-scrollbar {
            width: 0;
            background: 0 0
        }

        @media(max-width: 399.98px) {
            .sm-overflow-x[data-v-2007d50e] {
                overflow-x:scroll
            }

            .menu-inner-shadow[data-v-2007d50e] {
                box-shadow: inset 10px 0 10px -10px rgba(0,0,0,.3),inset -10px 0 10px -10px rgba(0,0,0,.3);
                padding-left: .75rem;
                padding-right: .75rem
            }

            .theContainer[data-v-2007d50e] {
                padding-left: 0;
                padding-right: 0
            }

            .no-scrollbar[data-v-2007d50e] {
                scrollbar-width: none;
                -ms-overflow-style: none
            }

            .no-scrollbar[data-v-2007d50e]::-webkit-scrollbar {
                width: 0;
                height: 0;
                background: 0 0
            }
        }

        @media(max-width: 349.98px) {
            .mobile-pr a[data-v-2007d50e]:last-of-type {
                padding-right:1rem
            }
        }

        .footer-leaf {
            display: block;
            width: 62.31px!important;
            height: 81px;
            margin: 16px auto -44px;
            padding: 0;
            z-index: 2;
            position: relative
        }

        .top-header-logo[data-v-81325358] {
            max-width: 200px
        }

        .top-header[data-v-81325358] {
            top: 0
        }

        @media screen and (max-width: 640px) {
            .top-header[data-v-81325358] {
                top:-53px
            }
        }

        @media screen and (max-width: 600px) {
            .top-header-logo img[data-v-81325358] {
                max-width:160px
            }
        }

        @media screen and (max-width: 992px) {
            .top-header-logo img[data-v-81325358] {
                max-width:214px
            }
        }

        .min-w-5[data-v-81325358] {
            min-width: 1.25rem
        }

        .remove-button[data-v-52534faa] {
            top: -1rem
        }

        .product-image[data-v-52534faa] {
            width: 7rem;
            height: 7rem;
            -o-object-fit: contain;
            object-fit: contain
        }

        @media(max-width: 400px) {
            .product-image[data-v-52534faa] {
                max-width:4rem;
                max-height: 8rem
            }
        }

        .button-lds-ring[data-v-7066b025] {
            display: inline-block;
            position: relative;
            width: 16px;
            height: 16px
        }

        .button-lds-ring div[data-v-7066b025] {
            box-sizing: border-box;
            display: block;
            position: absolute;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            -webkit-animation: lds-ring-data-v-7066b025 1.2s cubic-bezier(.5,0,.5,1) infinite;
            animation: lds-ring-data-v-7066b025 1.2s cubic-bezier(.5,0,.5,1) infinite;
            border: 2px solid transparent;
            border-top-color: #aaa
        }

        .button-lds-ring div[data-v-7066b025]:first-child {
            -webkit-animation-delay: -.45s;
            animation-delay: -.45s
        }

        .button-lds-ring div[data-v-7066b025]:nth-child(2) {
            -webkit-animation-delay: -.3s;
            animation-delay: -.3s
        }

        .button-lds-ring div[data-v-7066b025]:nth-child(3) {
            -webkit-animation-delay: -.15s;
            animation-delay: -.15s
        }

        @-webkit-keyframes button-lds-ring-data-v-7066b025 {
            0% {
                transform: rotate(0deg)
            }

            to {
                transform: rotate(1turn)
            }
        }

        @keyframes button-lds-ring-data-v-7066b025 {
            0% {
                transform: rotate(0deg)
            }

            to {
                transform: rotate(1turn)
            }
        }

        .lds-ring[data-v-7066b025] {
            display: inline-block;
            position: relative;
            width: 100px;
            height: 100px
        }

        .lds-ring div[data-v-7066b025] {
            box-sizing: border-box;
            display: block;
            position: absolute;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            -webkit-animation: lds-ring-data-v-7066b025 1.2s cubic-bezier(.5,0,.5,1) infinite;
            animation: lds-ring-data-v-7066b025 1.2s cubic-bezier(.5,0,.5,1) infinite;
            border: 3px solid transparent;
            border-top-color: #aaa
        }

        .lds-ring div[data-v-7066b025]:first-child {
            -webkit-animation-delay: -.45s;
            animation-delay: -.45s
        }

        .lds-ring div[data-v-7066b025]:nth-child(2) {
            -webkit-animation-delay: -.3s;
            animation-delay: -.3s
        }

        .lds-ring div[data-v-7066b025]:nth-child(3) {
            -webkit-animation-delay: -.15s;
            animation-delay: -.15s
        }

        @-webkit-keyframes lds-ring-data-v-7066b025 {
            0% {
                transform: rotate(0deg)
            }

            to {
                transform: rotate(1turn)
            }
        }

        @keyframes lds-ring-data-v-7066b025 {
            0% {
                transform: rotate(0deg)
            }

            to {
                transform: rotate(1turn)
            }
        }

        @media(max-width: 461.98px) {
            .mobile-mb[data-v-7066b025] {
                margin-bottom:.5rem
            }
        }

        [data-v-7066b025] .vs__dropdown-toggle {
            border: 1px solid #dbdbdb;
            padding: 6px;
            border-radius: 22px;
            width: 100%;
            max-width: 16rem
        }

        [data-v-7066b025] .vs__dropdown-menu {
            width: 100%;
            max-width: 16rem
        }

        [data-v-7066b025] .vs__dropdown-option {
            white-space: normal
        }

        @media(max-width: 499.98px) {
            .continue-shopping-button[data-v-7066b025] {
                padding:0
            }
        }

        @media(max-width: 369.98px) {
            .minimum-spend[data-v-7066b025] {
                width:120%;
                text-align: right;
                right: 2%
            }
        }

        .green-shadow[data-v-52e09b0e] {
            box-shadow: 0 2px 4px 2px rgba(104,211,145,.2),0 2px 4px -1px rgba(104,211,145,.12)
        }

        .max-w-xxs[data-v-52e09b0e] {
            max-width: 16rem
        }

        [data-v-2d3ddbbf] .vs__dropdown-toggle {
            border: 1px solid #dbdbdb;
            padding: 6px;
            border-radius: 22px;
            width: 16rem
        }

        [data-v-2d3ddbbf] .vs__dropdown-menu {
            width: 16rem
        }

        .remove-button[data-v-99c43536] {
            top: -1rem
        }

        .recipe-image[data-v-99c43536] {
            max-width: 9rem;
            height: 7rem;
            -o-object-fit: cover;
            object-fit: cover
        }

        @media(max-width: 349.98px) {
            .recipe-image[data-v-99c43536] {
                max-width:7rem
            }
        }

        .recipe-image[data-v-2032048e] {
            max-width: 9rem;
            height: 7rem;
            -o-object-fit: cover;
            object-fit: cover
        }

        @media(max-width: 349.98px) {
            .recipe-image[data-v-2032048e] {
                max-width:7rem
            }
        }

        .max-w-xxs[data-v-f34b7fbc] {
            max-width: 16rem
        }

        .lds-ring[data-v-eed520fa] {
            display: inline-block;
            position: relative;
            width: 60px;
            height: 60px
        }

        .lds-ring div[data-v-eed520fa] {
            box-sizing: border-box;
            display: block;
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            -webkit-animation: lds-ring-data-v-eed520fa 1.2s cubic-bezier(.5,0,.5,1) infinite;
            animation: lds-ring-data-v-eed520fa 1.2s cubic-bezier(.5,0,.5,1) infinite;
            border: 6px solid transparent;
            border-top-color: #aaa
        }

        .lds-ring div[data-v-eed520fa]:first-child {
            -webkit-animation-delay: -.45s;
            animation-delay: -.45s
        }

        .lds-ring div[data-v-eed520fa]:nth-child(2) {
            -webkit-animation-delay: -.3s;
            animation-delay: -.3s
        }

        .lds-ring div[data-v-eed520fa]:nth-child(3) {
            -webkit-animation-delay: -.15s;
            animation-delay: -.15s
        }

        @-webkit-keyframes lds-ring-data-v-eed520fa {
            0% {
                transform: rotate(0deg)
            }

            to {
                transform: rotate(1turn)
            }
        }

        @keyframes lds-ring-data-v-eed520fa {
            0% {
                transform: rotate(0deg)
            }

            to {
                transform: rotate(1turn)
            }
        }

        .button-lds-ring[data-v-4f908d90] {
            display: inline-block;
            position: relative;
            width: 16px;
            height: 16px
        }

        .button-lds-ring div[data-v-4f908d90] {
            box-sizing: border-box;
            display: block;
            position: absolute;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            -webkit-animation: lds-ring-data-v-4f908d90 1.2s cubic-bezier(.5,0,.5,1) infinite;
            animation: lds-ring-data-v-4f908d90 1.2s cubic-bezier(.5,0,.5,1) infinite;
            border: 2px solid transparent;
            border-top-color: #aaa
        }

        .button-lds-ring div[data-v-4f908d90]:first-child {
            -webkit-animation-delay: -.45s;
            animation-delay: -.45s
        }

        .button-lds-ring div[data-v-4f908d90]:nth-child(2) {
            -webkit-animation-delay: -.3s;
            animation-delay: -.3s
        }

        .button-lds-ring div[data-v-4f908d90]:nth-child(3) {
            -webkit-animation-delay: -.15s;
            animation-delay: -.15s
        }

        @-webkit-keyframes button-lds-ring-data-v-4f908d90 {
            0% {
                transform: rotate(0deg)
            }

            to {
                transform: rotate(1turn)
            }
        }

        @keyframes button-lds-ring-data-v-4f908d90 {
            0% {
                transform: rotate(0deg)
            }

            to {
                transform: rotate(1turn)
            }
        }

        .lds-ring[data-v-4f908d90] {
            display: inline-block;
            position: relative;
            width: 100px;
            height: 100px
        }

        .lds-ring div[data-v-4f908d90] {
            box-sizing: border-box;
            display: block;
            position: absolute;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            -webkit-animation: lds-ring-data-v-4f908d90 1.2s cubic-bezier(.5,0,.5,1) infinite;
            animation: lds-ring-data-v-4f908d90 1.2s cubic-bezier(.5,0,.5,1) infinite;
            border: 3px solid transparent;
            border-top-color: #aaa
        }

        .lds-ring div[data-v-4f908d90]:first-child {
            -webkit-animation-delay: -.45s;
            animation-delay: -.45s
        }

        .lds-ring div[data-v-4f908d90]:nth-child(2) {
            -webkit-animation-delay: -.3s;
            animation-delay: -.3s
        }

        .lds-ring div[data-v-4f908d90]:nth-child(3) {
            -webkit-animation-delay: -.15s;
            animation-delay: -.15s
        }

        @-webkit-keyframes lds-ring-data-v-4f908d90 {
            0% {
                transform: rotate(0deg)
            }

            to {
                transform: rotate(1turn)
            }
        }

        @keyframes lds-ring-data-v-4f908d90 {
            0% {
                transform: rotate(0deg)
            }

            to {
                transform: rotate(1turn)
            }
        }

        .sidebar[data-v-b6437ebc] {
            width: 30rem
        }

        @media(max-width: 639.98px) {
            .sidebar[data-v-b6437ebc] {
                width:100%
            }
        }

        .hide-enter-active[data-v-b6437ebc],.hide-leave-active[data-v-b6437ebc] {
            transition: all .2s ease-in-out
        }

        .hide-leave-active[data-v-b6437ebc] {
            transform: translateX(0)
        }

        .hide-enter[data-v-b6437ebc],.hide-leave-to[data-v-b6437ebc] {
            transform: translateX(30rem)
        }

        .fade-enter-active[data-v-b6437ebc],.fade-leave-active[data-v-b6437ebc] {
            transition: all .2s ease-in-out
        }

        .fade-leave-active[data-v-b6437ebc] {
            opacity: 1
        }

        .fade-enter[data-v-b6437ebc],.fade-leave-to[data-v-b6437ebc] {
            opacity: 0
        }

        @media(min-width: 640px) {
            .no-scrollbar[data-v-b6437ebc] {
                -ms-overflow-style:none;
                width: calc(100% + 20px);
                padding-right: 20px;
                -webkit-overflow-scrolling: touch
            }
        }

        .hide-enter-active[data-v-7f8fcb5a],.hide-leave-active[data-v-7f8fcb5a] {
            transition: .2s
        }

        .hide-enter[data-v-7f8fcb5a],.hide-leave-to[data-v-7f8fcb5a] {
            transform: translateX(-100%)
        }

        .fade-enter-active[data-v-7f8fcb5a],.fade-leave-active[data-v-7f8fcb5a] {
            transition: all .2s ease-in-out
        }

        .fade-leave-active[data-v-7f8fcb5a] {
            opacity: 1
        }

        .fade-enter[data-v-7f8fcb5a],.fade-leave-to[data-v-7f8fcb5a] {
            opacity: 0
        }

        .hidden-scroll[data-v-7f8fcb5a] {
            scrollbar-width: none;
            -ms-overflow-style: none
        }

        .hidden-scroll[data-v-7f8fcb5a]::-webkit-scrollbar {
            width: 0;
            background: 0 0
        }

        button svg[data-v-7f8fcb5a] {
            max-width: 100%;
            max-height: 100%
        }

        button.goBack[data-v-7f8fcb5a] {
            position: absolute;
            left: -4rem;
            top: 7px;
            text-transform: capitalize;
            transition: all .2s;
            border-radius: 3px;
            opacity: 0;
            font-size: 13px
        }

        button.goBack.visible[data-v-7f8fcb5a] {
            opacity: 1;
            left: .95rem
        }

        .list-item[data-v-7f8fcb5a] {
            display: block;
            margin-right: 10px;
            z-index: 2
        }

        .list-enter-active[data-v-7f8fcb5a],.list-leave-active[data-v-7f8fcb5a] {
            transition: transform .35s
        }

        .is-leaving .list-enter[data-v-7f8fcb5a] {
            opacity: 0;
            transform: translateX(-200px)
        }

        .is-entering .list-enter[data-v-7f8fcb5a] {
            opacity: 0;
            transform: translateX(200px)
        }

        .list-leave-to[data-v-7f8fcb5a] {
            transform: translateX(-40px)
        }

        .list-leave-active[data-v-7f8fcb5a] {
            position: absolute;
            left: 0;
            opacity: 0;
            z-index: -1
        }

        .top-header-logo[data-v-07fc5dda] {
            max-width: 200px
        }

        .top-header[data-v-07fc5dda] {
            top: 0
        }

        @media screen and (max-width: 640px) {
            .top-header[data-v-07fc5dda] {
                top:-53px
            }
        }

        @media screen and (max-width: 600px) {
            .top-header-logo img[data-v-07fc5dda] {
                max-width:160px
            }
        }

        @media screen and (max-width: 992px) {
            .top-header-logo img[data-v-07fc5dda] {
                max-width:214px
            }
        }

        @media print {
            #launcher[data-v-dab2b2dc],.layout-root>[data-v-dab2b2dc]:not(.recipe-content) {
                display: none
            }
        }

        .sm-overflow-x[data-v-dab2b2dc] {
            overflow-x: scroll
        }

        .no-scrollbar[data-v-dab2b2dc] {
            scrollbar-width: none;
            -ms-overflow-style: none
        }

        .no-scrollbar[data-v-dab2b2dc]::-webkit-scrollbar {
            width: 0;
            height: 0;
            background: 0 0
        }

    </style>

    <style>
       @font-face {
      font-family: 'Oswald-Regular';
      src: url('https://d3myyafggcycom.cloudfront.net/LIVE/assets//Oswald-Regular.ttf');
    }
        #main-div{
            /*display: none;*/
        }
      #main-div{text-align: center !important;}
      #main-div{font-family: "Oswald-Regular" !important;
    font-weight: 200;
    line-height: normal;
    text-transform: uppercase;
    letter-spacing: 0.0px;}
      
      .container p {
    color: #000;
    font-size: .8em;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.container  {
    color: #000;
    font-size: .75em;
    margin-bottom: 0.5em;
    text-align: center;
}
      
    </style>
</head>
<body>

  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
  <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>
  <div class="container" id="main-div">
    <div  style="display:none;" id="mymessageCountry"></div>
    <div id="mymessage">SPEND $<span id="remaining"></span> MORE AND GET FREE SHIPPING!</div>
    <div id="mymessage_full" style="display:none;">YOU ARE ELIGIBLE FOR FREE SHIPPING!</div>
  <div class="progress">
    <div id="progressbar" class="progress-bar" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100" style="width:10%;background-color:black;">
      <span class="sr-only" id="custom_message">70% Complete</span>
    </div>
  </div>
</div>

    <script>
        (function(){
            window.PlobalBridge = {
                openURL:function(url,title,open_links_in){
                    try {
                        if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.openURL){
                            window.webkit.messageHandlers.openURL.postMessage({'type':open_links_in,'url':url,'title':title});
                        }
                    } catch(e) {
                        console.log("ios error openURL");
                        console.log(e);
                    }
                    try {
                        if(window && window.AndroidBridge && window.AndroidBridge.openURL){
                            window.AndroidBridge.openURL(open_links_in,url,title);
                        }
                    } catch(e) {
                        console.log("android error openURL");
                        console.log(e);
                    }
                    if(!window.webkit && !window.AndroidBridge){
                        window.location.assign(url);
                    }
                },
                openURLNew:function(data){
                    try {
                        if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.openURLNew){
                            window.webkit.messageHandlers.openURLNew.postMessage(data);
                        }
                    } catch(e) {
                        console.log("ios error openURLNew");
                        console.log(e);
                    }
                    try {
                        if(window && window.AndroidBridge && window.AndroidBridge.openURLNew){
                            window.AndroidBridge.openURLNew(JSON.stringify(data));
                        }
                    } catch(e) {
                        console.log("android error openURLNew");
                        console.log(e);
                    }
                    if(!window.webkit && !window.AndroidBridge){
                        window.location.assign(data.url);
                    }
                    this.openURL(data.url,data.title,data.type);
                },
                openURLNewIOS:function(data){
                    try {
                        if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.openURLNew){
                            window.webkit.messageHandlers.openURLNew.postMessage(data);
                        }
                    } catch(e) {
                        console.log("ios error openURLNew");
                        console.log(e);
                    }

                    if(!window.webkit){
                        window.location.assign(data.url);
                    }
                    this.openURL(data.url,data.title,data.type);
                },
                redirectTo:function(app_feature_id){
                    try {
                        if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.redirectTo){
                            window.webkit.messageHandlers.redirectTo.postMessage({'app_feature_id':app_feature_id});
                        }
                    } catch(e) {
                        console.log("ios error redirectTo");
                        console.log(e);
                    }
                    try {
                        if(window && window.AndroidBridge && window.AndroidBridge.redirectTo){
                            window.AndroidBridge.redirectTo(JSON.stringify({'app_feature_id':app_feature_id}));
                        }
                    } catch(e) {
                        console.log("android error redirectTo");
                        console.log(e);
                    }
                    if(!window.webkit && !window.AndroidBridge){
                        alert(app_feature_id);
                    }
                },
                adjustWebViewHeight:function(container){
                    if(!container){
                        var height = document.body.clientHeight;
                    }else if(container == 0){
                        var height = 0;
                    }
                    else if(container == -25){
                        var height = -25;
                    }else{
                        var height = document.getElementById(container).clientHeight;
                    }
                    height +=25;
                    try {
                        if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.adjustWebViewHeight){
                            window.webkit.messageHandlers.adjustWebViewHeight.postMessage(height);
                        }
                    } catch(e) {
                        console.log("ios error adjustWebViewHeight");
                        console.log(e);
                    }
                    if(!window.webkit && !window.AndroidBridge){
                        console.log('adjustWebViewHeight  '+height);
                    }
                },
                addLineItemProperty:function(obj){
                    try {
                        if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.addLineItemProperty){
                            window.webkit.messageHandlers.addLineItemProperty.postMessage(obj);
                        }
                    } catch(e) {
                        console.log("ios error addLineItemProperty");
                        console.log(e);
                    }
                    try {
                        if(window && window.AndroidBridge && window.AndroidBridge.addLineItemProperty){
                            window.AndroidBridge.addLineItemProperty(JSON.stringify(obj));
                        }
                    } catch(e) {
                        console.log("android error addLineItemProperty");
                        console.log(e);
                    }
                    // if(!window.webkit && !window.AndroidBridge){
                    //     console.log('addLineItemProperty  '+JSON.stringify(obj));
                    // }
                    console.log('addLineItemProperty  '+JSON.stringify(obj));
                },
                removeLineItemProperty:function(key){
                    try {
                        if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.removeLineItemProperty){
                            window.webkit.messageHandlers.removeLineItemProperty.postMessage(key);
                        }
                    } catch(e) {
                        console.log("ios error addLineItemProperty");
                        console.log(e);
                    }
                    try {
                        if(window && window.AndroidBridge && window.AndroidBridge.removeLineItemProperty){
                            window.AndroidBridge.removeLineItemProperty(key);
                        }
                    } catch(e) {
                        console.log("android error removeLineItemProperty");
                        console.log(e);
                    }
                    if(!window.webkit && !window.AndroidBridge){
                        console.log('removeLineItemProperty  '+key);
                    }
                },
                setAddToCartBehaviour:function(obj){
                    console.log("setAddToCartBehaviour");
                    console.log(obj);
                    try {
                        if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.setAddToCartBehaviour){
                            window.webkit.messageHandlers.setAddToCartBehaviour.postMessage(obj);
                        }
                    } catch(e) {
                        console.log("ios error setAddToCartBehaviour");
                        console.log(e);
                    }
                    try {
                        if(window && window.AndroidBridge && window.AndroidBridge.setAddToCartBehaviour){
                            window.AndroidBridge.setAddToCartBehaviour(JSON.stringify(obj));
                        }
                    } catch(e) {
                        console.log("android error setAddToCartBehaviour");
                        console.log(e);
                    }
                    if(!window.webkit && !window.AndroidBridge){
                        console.log('setAddToCartBehaviour  '+JSON.stringify(obj));
                    }
                },
                setBuyNowBehaviour:function(obj){
                    try {
                        if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.setBuyNowBehaviour){
                            window.webkit.messageHandlers.setBuyNowBehaviour.postMessage(obj);
                        }
                    } catch(e) {
                        console.log("ios error setBuyNowBehaviour");
                        console.log(e);
                    }
                    try {
                        if(window && window.AndroidBridge && window.AndroidBridge.setBuyNowBehaviour){
                            window.AndroidBridge.setBuyNowBehaviour(JSON.stringify(obj));
                        }
                    } catch(e) {
                        console.log("android error setBuyNowBehaviour");
                        console.log(e);
                    }
                    if(!window.webkit && !window.AndroidBridge){
                        console.log('setBuyNowBehaviour  '+JSON.stringify(obj));
                    }
                },
                changeDisplayInfo:function(data){
                    try {
                        if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.changeDisplayInfo){
                            window.webkit.messageHandlers.changeDisplayInfo.postMessage(data);
                        }
                    } catch(e) {
                        console.log("ios error changeDisplayInfo");
                        console.log(e);
                    }
                    try {
                        if(window && window.AndroidBridge && window.AndroidBridge.changeDisplayInfo){
                            window.AndroidBridge.changeDisplayInfo(JSON.stringify(data));
                        }
                    } catch(e) {
                        console.log("android error changeDisplayInfo");
                        console.log(e);
                    }
                    if(!window.webkit && !window.AndroidBridge){
                        console.log('changeDisplayInfo '+JSON.stringify(data));
                    }
                },
                addCustomerProperty:function(key,value){
                    console.log(key,value);
                    try {
                        if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.addCustomerProperty){
                            window.webkit.messageHandlers.addCustomerProperty.postMessage({'key':key,'value':value});
                        }
                    } catch(e) {
                        console.log("ios error addCustomerProperty");
                        console.log(e);
                    }
                    try {
                        if(window && window.AndroidBridge && window.AndroidBridge.addCustomerProperty){
                            window.AndroidBridge.addCustomerProperty(key,value);
                        }
                    } catch(e) {
                        console.log("android error addCustomerProperty");
                        console.log(e);
                    }
                    if(!window.webkit && !window.AndroidBridge){
                        console.log('addCustomerProperty '+key+' '+ value);
                    }
                },
                addToCart:function(variant){
                    try {
                        if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.addToCart){
                            window.webkit.messageHandlers.addToCart.postMessage(variant);
                        }
                    } catch(e) {
                        console.log("ios error addToCart");
                        console.log(e);
                    }
                    try {
                        if(window && window.AndroidBridge && window.AndroidBridge.addToCart){
                            window.AndroidBridge.addToCart(JSON.stringify(variant));
                        }
                    } catch(e) {
                        console.log("android error addToCart");
                        console.log(e);
                    }
                    if(!window.webkit && !window.AndroidBridge){
                        console.log('addToCart '+JSON.stringify(variant));
                    }
                },
                changeAddToCartBehaviour:function(rules){

                },
                dataReceiver:function(data){
                    console.log('dataReceiver');
                },
                requestData:function(data){
                    console.log('requestData');
                },
                shareData:function(data){
                    try {
                        if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.shareData){
                            window.webkit.messageHandlers.shareData.postMessage({'data':data});
                        }
                    } catch(e) {
                        console.log("ios error shareData");
                        console.log(e);
                    }
                    try {
                        if(window && window.AndroidBridge && window.AndroidBridge.shareData){
                            window.AndroidBridge.shareData(data);
                        }
                    } catch(e) {
                        console.log("android error shareData");
                        console.log(e);
                    }
                    if(!window.webkit && !window.AndroidBridge){
                        console.log('shareData '+data);
                    }
                },
                openFileChooser:function(){
                    console.log('openFileChooser');
                    try {
                        if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.openFileChooser){
                            window.webkit.messageHandlers.openFileChooser.postMessage("");
                        }
                    } catch(e) {
                        console.log("ios error openFileChooser");
                        console.log(e);
                    }
                    try {
                        if(window && window.AndroidBridge && window.AndroidBridge.openFileChooser){
                            window.AndroidBridge.openFileChooser();
                        }
                    } catch(e) {
                        console.log("android error openFileChooser");
                        console.log(e);
                    }
                    if(!window.webkit && !window.AndroidBridge){
                        console.log('openFileChooser');
                    }
                },
                callBridgeFunction:function(functionName, paramsIos, paramAndroid){
                    try {
                        if(window && window.AndroidBridge){
                            if(paramAndroid){
                                window.AndroidBridge[functionName](paramAndroid);
                            }
                            else{
                                window.AndroidBridge[functionName]();
                            }

                        }
                    } catch(e) {
                        console.log("I got clicked android error");
                        console.log(e);
                    }

                    try{
                        if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers[functionName]){
                            if(paramsIos){
                                window.webkit.messageHandlers[functionName].postMessage(paramsIos);
                            }
                            else{
                                window.webkit.messageHandlers[functionName].postMessage();
                            }
                        }
                    }
                    catch(e){
                        console.log("I got clicked ios error");
                        console.log(e);
                    }
                },
                openProductDetail:function(product_id, title) {
                    var jsonToSend ={
                        product_id: product_id.toString(),
                        title: title
                    };

                    this.callBridgeFunction('openProductDetails', jsonToSend, JSON.stringify(jsonToSend));
                },
                openCollection: function (collection_id, title) {
                    var jsonToSend ={
                        collection_id: collection_id.toString(),
                        title: title
                    };

                    this.callBridgeFunction('openCollection', jsonToSend, JSON.stringify(jsonToSend));
                },
                addToCart:function(variantID){
                    event.stopPropagation();
                    this.callBridgeFunction('addToCart', variantID.toString(), variantID.toString());
                },
                openCart:function(event) {
                    event.stopPropagation();
                    this.callBridgeFunction('openCart');
                },
                operate:{
                    '+': function(a, b) { return a + b },
                    '<': function(a, b) { return a < b },
                    '=': function(a, b) { return a < b },
                    'equal': function(a, b) { return a == b; },
                    'not_equal': function(a, b) { return a != b; }
                },
                isJson:function(str) {
                    try {
                        JSON.parse(str);
                    } catch (e) {
                        return false;
                    }
                    return true;
                },
                validateData:function(obj){
                    try {
                        if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.validateData){
                            window.webkit.messageHandlers.validateData.postMessage(obj);
                        }
                    } catch(e) {
                        console.log("ios error validateData");
                        console.log(e);
                    }
                    try {
                        if(window && window.AndroidBridge && window.AndroidBridge.validateData){
                            window.AndroidBridge.validateData(JSON.stringify(obj));
                        }
                    } catch(e) {
                        console.log("android error validateData");
                        console.log(e);
                    }
                    // if(!window.webkit && !window.AndroidBridge){
                    //
                    // }
                    console.log('validateData '+JSON.stringify(obj));
                },
                triggerEvent:function(el, event) {
                    if ("createEvent" in document) {
                        var evt = document.createEvent("HTMLEvents");
                        evt.initEvent(event, false, true);
                        el.dispatchEvent(evt);
                    }
                    else
                        el.fireEvent(event);
                }
            };
        })();
    </script>

    <script>
      window.shipping_amount = 1000;
        var errorData = {"valid":false,"message":"Minimum spend not reached.","id":"bold"};
        var successData = {"valid":true,"message":"","id":"bold"};
        var data = errorData;

        window.requestData = {};

        function getDifferences(oldObj, newObj) {
            var diff = {};

            for (var k in oldObj) {
                if (!(k in newObj))
                    diff[k] = undefined;  // property gone so explicitly set it undefined
                else if (oldObj[k] !== newObj[k])
                    diff[k] = newObj[k];  // property in both but has changed
            }

            for (k in newObj) {
                if (!(k in oldObj))
                    diff[k] = newObj[k]; // property is new
            }

            return diff;
        }

        function onDataReceive(data){
            if(typeof data == 'string'){
                data = JSON.parse(data);
            }
            console.log(data);
            window.requestData = data.json_data;
      validateCartValue(window.requestData.cart);
//             if((data.json_data.Platform.toLowerCase() == 'android' && data.json_data['Code-Version'] >= 57)
//                 || (data.json_data.Platform.toLowerCase() == 'ios' && data.json_data['Code-Version'] >= 61)){
//                 validateCartValue(window.requestData.cart);
//             }
        }
        window.onload = function(){
            console.log('cart script load');
        };


        function onCartUpdate(data){
            if(typeof data == 'string'){
                data = JSON.parse(data);
            }
            var cart = data.json_data.cart;
            window.requestData = data.json_data;
            var diff = getDifferences(requestData.cart,cart);
            console.log('onCartUpdate ',cart);
          validateCartValue(cart);
//             if(diff.hasOwnProperty('items')){
//                 if((data.json_data.Platform.toLowerCase() == 'android' && data.json_data['Code-Version'] >= 57)
//                     || (data.json_data.Platform.toLowerCase() == 'ios' && data.json_data['Code-Version'] >= 61)) {
//                     validateCartValue(cart);
//                 }
//             }
        }

        function validateCartValue(cart) {
            console.log(cart);

            var total = 0;
      $("#mymessage").hide();
            $("#mymessage_full").hide();
            cart.items.forEach(function(li,i){
                var lPrice = 0;
                li.variantList.every(function (lItem) {
                    if(li.selected_variant_id == lItem.variants_Id || li.selected_variant_id == lItem.variants_id){
                        lPrice = lItem.price;
                        return false;
                    }
                    else{
                        return true;
                    }
                });

                total += li.quantity*lPrice;

            });

          
          if(total){
                        $("#main-div").show();
            setCartAmount(total);
          } else {
                      $("#progressbar").css("width",(100*total/window.shipping_amount)+"%");
            $("#main-div").hide();
          }
        }
          window.total = 0;
        function setCartAmount(total) {
          window.total = total;
          setShippingAmount();
            $("#progressbar").css("width",(100*total/window.shipping_amount)+"%");
          $("#custom_message").html((100*total/window.shipping_amount)+"% Complete");
          $("#remaining").html( ( (window.shipping_amount-total)>=0 ? (window.shipping_amount-total).toFixed(2) : 0) );
          if((window.shipping_amount-total)<=0) {
            $("#mymessage_full").show();
            $("#mymessage").hide();
          } else {
            $("#mymessage_full").hide();
            $("#mymessage").show();
          }
          PlobalBridge.adjustWebViewHeight("main-div");
          return;
            
        }
        
    </script>
  <script>
     window.shipping_amount = 85;
    try {
      if(
        window.requestData.customer_details.tags.includes("swell_vip_bronze") ||
        window.requestData.customer_details.tags.includes("tier: Bronze")
      ) {
        window.shipping_amount = 85;
      }
      if(
        window.requestData.customer_details.tags.includes("swell_vip_silver") ||
        window.requestData.customer_details.tags.includes("tier: Silver")
      ) {
        window.shipping_amount = 60;
      }
      if(
        window.requestData.customer_details.tags.includes("swell_vip_gold") ||
        window.requestData.customer_details.tags.includes("tier: Gold")
      ) {
        window.shipping_amount = 15;
      }
    } catch(err){}
    window.myCallAppData = [];
    function setShippingAmount() {
        window.shipping_amount = 85;
      try {
        if(! window.myCallAppData) { window.myCallAppData = {};}
        //window.myCallAppData.country_code = "CA";
      } catch(err){}
      if(window.myCallAppData && window.myCallAppData.hasOwnProperty("country_code") && ( window.myCallAppData.country_code == "CA"  || window.myCallAppData.country_code == "INNNNNNNN"  ) ) {
          //$("#mymessageCountry").show().html(window.myCallAppData.country_code);
      window.shipping_amount = 135;
        //bronze = 13500
        // silver = 9000
        // gold 1500
        try {
          if(
            window.requestData.customer_details.tags.includes("swell_vip_bronze") ||
            window.requestData.customer_details.tags.includes("tier: Bronze")
          ) {
            window.shipping_amount = 135;
          }
            if(
            window.requestData.customer_details.tags.includes("swell_vip_silver") ||
            window.requestData.customer_details.tags.includes("tier: Silver") 
          ) {
            window.shipping_amount = 90;
          }
            if(
            window.requestData.customer_details.tags.includes("swell_vip_gold") ||
            window.requestData.customer_details.tags.includes("tier: Gold")
          ) {
            window.shipping_amount = 90;
          }
        } catch(err){}
      } else if(window.myCallAppData && window.myCallAppData.hasOwnProperty("country_code") && (window.myCallAppData.country_code == "US")) {
        //$("#mymessageCountry").show().html(window.myCallAppData.country_code);
    window.shipping_amount = 85;
        //bronze = 8500
        // silver = 6000
        // gold 1500
        try {
          if(
            window.requestData.customer_details.tags.includes("swell_vip_bronze") ||
            window.requestData.customer_details.tags.includes("tier: Bronze")
          ) {
            window.shipping_amount = 85;
          }
            if(
            window.requestData.customer_details.tags.includes("swell_vip_silver") ||
            window.requestData.customer_details.tags.includes("tier: Silver")
          ) {
            window.shipping_amount = 60;
          }
            if(
            window.requestData.customer_details.tags.includes("swell_vip_gold") ||
            window.requestData.customer_details.tags.includes("tier: Gold")
          ) {
            window.shipping_amount = 60;
          }
        } catch(err){}
      } else {
        window.shipping_amount = 300;
        try {
          if(window.myCallAppData && window.myCallAppData.hasOwnProperty("country_code") && (window.myCallAppData.country_code)){
            window.shipping_amount = 300;
          } else{
            window.shipping_amount = 85;
            try {
              if(
                window.requestData.customer_details.tags.includes("swell_vip_bronze") ||
                window.requestData.customer_details.tags.includes("tier: Bronze")
              ) {
                window.shipping_amount = 85;
              }
                if(
                window.requestData.customer_details.tags.includes("swell_vip_silver") ||
                window.requestData.customer_details.tags.includes("tier: Silver")
              ) {
                window.shipping_amount = 60;
              }
                if(
                window.requestData.customer_details.tags.includes("swell_vip_gold") ||
                window.requestData.customer_details.tags.includes("tier: Gold")
              ) {
                window.shipping_amount = 60;
              }
            } catch(err){}
          }
        } catch(err){}
       // $("#mymessageCountry").show().html("other");
      }
    }
    function myCall(data){
//      window.myCallAppData.push(data);
            window.myCallAppData = data;
      console.log("myCall function called");
      console.log(data);
      setShippingAmount();
      var total = window.total;
      $("#progressbar").css("width",(100*total/window.shipping_amount)+"%");
          $("#custom_message").html((100*total/window.shipping_amount)+"% Complete");
          $("#remaining").html( ( (window.shipping_amount-total)>=0 ? (window.shipping_amount-total).toFixed(2) : 0) );
          if((window.shipping_amount-total)<=0) {
            $("#mymessage_full").show();
            $("#mymessage").hide();
          } else {
            $("#mymessage_full").hide();
            $("#mymessage").show();
          }
          PlobalBridge.adjustWebViewHeight("main-div");
          return;
    }
	
    try {
      if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers){
       var details = {"id": "quantity_limit", "action": "javascript:myCall(jsonData)"};
  window.PlobalBridge.callBridgeFunction("checkLocationPermission",details, JSON.stringify(details));
      }
    } catch(e) {
      console.log("ios error validateData");
      console.log(e);
    }
    try {
      if(window && window.AndroidBridge && window.AndroidBridge){
       var details = {"id": "quantity_limit", "action": "javascript:myCall(all_data)"};
  window.PlobalBridge.callBridgeFunction("checkLocationPermission",details, JSON.stringify(details));
      }
    } catch(e) {
      console.log("android error validateData");
      console.log(e);
    }
  </script>
  <script>
    window.myCallFeatureAppData = [];
    function myCallFeature(data){
      window.myCallFeatureAppData = data;
//      window.myCallFeatureAppData.push(data);
      console.log(data);
    }
    var details =  {"id": "quantity_limit", "action": "javascript:myCallFeature(all_data)"};
        window.PlobalBridge.callBridgeFunction("getFeatureData",details, JSON.stringify(details));
  </script>

</body></html>