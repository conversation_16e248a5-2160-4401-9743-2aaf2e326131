/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "kyte": {
    "product": {
      "addons": {
        "addons": "Options",
        "edit_details": "Edit Details"
      }
    }
  },
  "general": {
    "meta": {
      "page": "Page {{ page }}"
    },
    "label": {
      "color": "Color,Colour",
      "white": "White",
      "size": "Size, Tog Rating"
    },
    "breadcrumb": {
      "title": "Breadcrumb",
      "home": "Home"
    },
    "pagination": {
      "previous_page": "Previous",
      "next_page": "Next",
      "go_to_page": "Navigate to page {{page}}"
    },
    "accessibility": {
      "skip_to_content": "Skip to content",
      "close": "Close",
      "previous": "Previous",
      "next": "Next",
      "play_video": "Play video",
      "go_to_slide": "Go to slide {{num}}",
      "delete": "Delete",
      "show_product": "Show product {{title}}",
      "star_reviews_info": "{{ rating_value }} out of {{ rating_max }} stars",
      "accessibility_statement": "Accessibility Statement"
    },
    "social": {
      "facebook_share": "Share on Facebook",
      "pinterest_pin": "Pin on Pinterest",
      "twitter_tweet": "X on X",
      "email_share": "Share by e-mail",
      "email_label": "E-mail",
      "follow_label": "Follow us on {{ social_media }}"
    },
    "newsletter": {
      "email": "Your Email Address",
      "subscribe": "Subscribe",
      "success": "You have been subscribed to our newsletter"
    },
    "faq": {
      "categories": "Categories"
    },
    "form": {
      "max_characters": "{{ max_chars }} characters max"
    },
    "onboarding": {
      "blog_post_category": "Category",
      "blog_post_title": "Article",
      "product_vendor": "Vendor",
      "product_title": "Product",
      "product_description": "Write text about your product.",
      "collection_title": "Collection"
    }
  },
  "header": {
    "general": {
      "sub_brand_slider": "More from Kyte",
      "navigation": "Navigation",
      "newsletter": "Newsletter",
      "login": "Login",
      "account": "Account",
      "cart": "Cart",
      "language": "Language",
      "country": "Country\/region"
    }
  },
  "footer": {
    "general": {
      "language": "Language",
      "country": "I'm shopping from:",
      "we_accept": "We accept"
    },
    "newsletter": {
      "email": "Your e-mail",
      "submit": "Register",
      "success": "You have been registered to our newsletter."
    },
    "cookie_bar": {
      "accept": "Accept",
      "decline": "Decline"
    }
  },
  "cart": {
    "general": {
      "title": "Cart",
      "empty": "Your cart is empty",
      "start_shopping": "Start shopping",
      "free_shipping": "You are eligible for free shipping!",
      "free_shipping_remaining_html": "Spend <strong>{{ remaining_amount }}<\/strong> more and get free shipping",
      "free_shipping_to_html": "to <strong>{{ country_name }}<\/strong>",
      "free": "Free!",
      "no_more_stock": "No more stock",
      "shipping_tax_note": "Shipping & taxes calculated at checkout",
      "product": "Product",
      "quantity": "Quantity",
      "decrease_quantity": "Decrease quantity",
      "increase_quantity": "Increase quantity",
      "change_quantity": "Change quantity",
      "remove": "Remove",
      "order_note": "Gift note",
      "add_order_note": "Add gift note",
      "edit_order_note": "Edit gift note",
      "order_note_placeholder": "Your Gift Message Here",
      "order_note_save": "Save",
      "total": "Total",
      "checkout": "Checkout",
      "go_to_cart": "Go to cart",
      "we_accept": "We accept",
      "item_added": "Item added to your cart!",
      "item_added_short": "Added to your cart!",
      "view_cart": "View cart",
      "gift_wrap": {
        "checkbox_title": "Add Gift Wrap",
        "checkbox_title_disabled": "Add Gift Wrap (Not Available.)",
        "disabled_tooltip": "One or more items in the cart can't be gift wrapped.",
        "disabled_line_item_message": "This item can't be gift wrapped."
      },
      "item_count": {
        "zero": "{{ count }} items",
        "one": "{{ count }} item",
        "other": "{{ count }} items"
      }
    },
    "shipping_estimator": {
      "estimate_shipping": "Estimate shipping",
      "country": "Country",
      "province": "Province",
      "zip_code": "Zip code",
      "submit": "Estimate",
      "no_results": "Sorry, we do not ship to your address.",
      "one_result": "There is one shipping rate for your address:",
      "multiple_results": "There are several shipping rates for your address:",
      "error": "One or more error occurred while retrieving shipping rates:"
    }
  },
  "collection": {
    "general": {
      "filters": "Filter by",
      "show_filters": "Show filters",
      "all_products": "All products",
      "view_results": "View results",
      "apply_filters": "Apply filters",
      "clear_filters": "Clear all",
      "price_range_to": "to",
      "price_filter_from": "From price",
      "price_filter_to": "To price",
      "price_filter": "{{min_price}} - {{max_price}}",
      "no_results_title": "No results",
      "no_results_label": "Sorry, your search did not yield any results.",
      "no_results_button": "Reset filters",
      "empty_title": "Empty collection",
      "empty_label": "This collection does not contain any products.",
      "empty_button": "Shop now",
      "sort_by": "Sort by",
      "products_count": {
        "zero": "{{ count }} products",
        "one": "{{ count }} product",
        "other": "{{ count }} products"
      }
    },
    "product": {
      "sold_out": "Sold out",
      "discount_html": "Save {{ savings }}",
      "from_price_html": "From {{ price_min }}",
      "add_to_cart": "Add To Cart",
      "add_to_cart_short": "Add To Cart",
      "select_a_size": "Select a Size",
      "quick_view": "Quick view",
      "quick_add": "Quick add",
      "available_colors_count": {
        "one": "{{ count }} color available",
        "other": "{{ count }} colors available"
      }
    }
  },
  "product": {
    "general": {
      "description": "Details",
      "reviews": "Reviews ({{ rating_count }})",
      "sku": "SKU:",
      "regular_price": "Regular price",
      "sale_price": "Sale price",
      "view_in_space": "View in your space",
      "zoom": "Zoom",
      "share": "Share",
      "size_chart": "Size Chart",
      "tog_rating": "TOG Ratings",
      "need_help": "Need help?",
      "view_details": "View details",
      "include_taxes": "Tax included.",
      "product_limit": "Only {{ limit }} per customer allowed",
      "shipping_policy_html": "<a href=\"{{ link }}\" class=\"link link--accented\">Shipping calculated<\/a> at checkout",
      "special_messaging": {
        "pre_order_html": "<p><strong>This item is a pre-order<\/strong> and has an estimated ship date of <strong>{{ date }}<\/strong><\/p>"
      },
      "reviews_count": {
        "zero": "No reviews",
        "one": "{{ count }} review",
        "other": "{{ count }} reviews"
      },
      "bundle": {
        "widget_header_title_bundle": "Bundle Savings!",
        "widget_header_title_component": "Get this in a bundle and save!",
        "widget_header_text_bundle_html": "Save when you buy this set in the same size and color.",
        "widget_header_text_component_html": "Save when you buy this set in the same size and color.",
        "buy_separate": "Buy Separate",
        "buy_separately_html": "Buy Separately for {{ price }}",
        "buy_together_html": "Buy these together for only {{ price }}",
        "this_product": "This Product",
        "bundle_parent_loop_returns": "Because this item is a bundle, it is not available for returns.",
        "bundle_component_loop_returns": "Because this item is part of a bundle, it is not available for returns."
      }
    },
    "form": {
      "choose_options": "Choose options",
      "quantity": "Quantity",
      "variant": "Variant",
      "unavailable": "Unavailable",
      "pre_order": "Pre-order",
      "add_to_cart": "Add to cart",
      "sold_out": "Sold out",
      "in_stock": "In stock",
      "oversell_stock": "Re-stocking soon",
      "incoming_stock": "Re-stocking on {{ next_incoming_date }}",
      "low_stock_with_quantity_count": {
        "one": "Only {{count}} unit left",
        "other": "Only {{count}} units left"
      },
      "contact_us": "Contact us"
    },
    "ingredients": {
      "title": "Ingredients",
      "with": "With",
      "and": "and",
      "ingredient_benefits": "Ingredient Benefits",
      "active_ingredients": "Active Ingredients",
      "key_ingredients": "Key Ingredients",
      "all_ingredients": "All Ingredients"
    }
  },
  "store_availability": {
    "general": {
      "view_store_info": "View store information",
      "check_other_stores": "Check availability at other stores",
      "pick_up_available": "Pickup available",
      "pick_up_currently_unavailable": "Pickup currently unavailable",
      "pick_up_available_at": "Pickup available at {{ location_name }}",
      "pick_up_unavailable_at": "Pickup currently unavailable at {{ location_name }}"
    }
  },
  "list_collections": {
    "general": {
      "title": "Collections",
      "empty": "This store does not contain any collection.",
      "back_to_home": "Back to home page",
      "products_count": {
        "zero": "No products",
        "one": "{{count}} product",
        "other": "{{count}} products"
      }
    }
  },
  "blog": {
    "general": {
      "empty": "This blog has no articles",
      "back_to_home": "Back to home",
      "view": "View",
      "all_posts": "All posts"
    }
  },
  "article": {
    "general": {
      "written_by": "By {{ author }}",
      "share": "Share",
      "next": "Next",
      "reading": "Reading",
      "tags": "Tags",
      "continue_reading": "Continue reading",
      "reading_time": {
        "zero": "{{ count }} minutes",
        "one": "{{ count }} minute",
        "other": "{{ count }} minutes"
      }
    },
    "comments": {
      "leave_comment": "Leave a comment",
      "moderated": "All comments are moderated before being published.",
      "name": "Name",
      "email": "E-mail",
      "message": "Message",
      "submit": "Submit",
      "comment_sent": "Your comment has been sent. It will be visible once the shop owner has accepted it!",
      "comment_published": "Your comment has been published.",
      "comments_count": {
        "zero": "{{ count }} comments",
        "one": "{{ count }} comment",
        "other": "{{ count }} comments"
      }
    }
  },
  "customer": {
    "general": {
      "title": "My account"
    },
    "login": {
      "title": "Login",
      "instructions": "Please enter your e-mail and password:",
      "email": "E-mail",
      "password": "Password",
      "submit": "Login",
      "forgot_password": "Forgot password?",
      "new_customer": "New customer?",
      "create_account": "Create an account"
    },
    "recover_password": {
      "title": "Recover password",
      "instructions": "Please enter your e-mail:",
      "email": "E-mail",
      "submit": "Recover",
      "remember_password": "Remember your password?",
      "back_to_login": "Back to login",
      "success": "An e-mail has been sent to your address with instructions to recover your password."
    },
    "activate_account": {
      "title": "Activate account",
      "instructions": "Enter a password to create your account:",
      "password": "Password",
      "password_confirmation": "Password confirmation",
      "submit": "Activate",
      "cancel": "Cancel"
    },
    "reset_password": {
      "title": "Reset password",
      "instructions": "Enter a new password for your account:",
      "password": "Password",
      "password_confirmation": "Password confirmation",
      "submit": "Reset"
    },
    "register": {
      "title": "Register",
      "instructions": "Please fill in the fields below:",
      "first_name": "First name",
      "last_name": "Last name",
      "email": "E-mail",
      "password": "Password",
      "submit": "Create account",
      "already_have_account": "Already have an account?",
      "login": "Login"
    },
    "orders": {
      "title": "Orders",
      "no_orders": "You have not placed any orders yet.",
      "start_shopping": "Start shopping",
      "back_to_orders": "Back to orders",
      "number": "Order number",
      "order_name": "Order {{name}}",
      "date": "Date",
      "payment_status": "Payment status",
      "fulfillment_status": "Fulfillment status",
      "view_details": "View order details",
      "cancelled_at": "Cancelled at: {{date}}. Reason: {{reason}}",
      "fulfillment_with_url_and_number_html": "All products have been sent on {{date}}. Track the shipment with number {{tracking_number}} or by <a href=\"{{tracking_url}}\" target=\"_blank\">clicking here<\/a>.",
      "fulfillment_with_url_html": "All products have been sent on {{date}}. Track the shipment by <a href=\"{{tracking_url}}\" target=\"_blank\">clicking here<\/a>.",
      "fulfillment_with_number_html": "All products have been sent on {{date}}. Track the shipment with number {{tracking_number}}.",
      "fulfillment_html": "All products have been sent on {{date}}.",
      "line_fulfillment_with_url_and_number_html": "This product has been sent on {{date}}. Track the shipment with number {{tracking_number}} or by <a href=\"{{tracking_url}}\" target=\"_blank\">clicking here<\/a>.",
      "line_fulfillment_with_url_html": "This product has been sent on {{date}}. Track the shipment by <a href=\"{{tracking_url}}\" target=\"_blank\">clicking here<\/a>.",
      "line_fulfillment_with_number_html": "This product has been sent on {{date}}. Track the shipment with number {{tracking_number}}.",
      "line_fulfillment_html": "This product has been sent on {{date}}.",
      "total": "Total",
      "subtotal": "Subtotal",
      "discount": "Discount",
      "shipping": "Shipping",
      "total_duties": "Duties",
      "refunded_amount": "Refunded amount",
      "tax_included": "Tax included",
      "tax_excluded": "Tax excluded",
      "quantity": "Quantity: {{quantity}}",
      "shipping_address": "Shipping address",
      "billing_address": "Billing address"
    },
    "addresses": {
      "title": "Addresses",
      "no_address": "You have not saved any addresses yet.",
      "add_new": "Add a new address",
      "edit": "Edit",
      "delete": "Delete",
      "delete_confirm": "Are you sure you wish to delete this address?",
      "save": "Save",
      "default_address": "Default address",
      "address_count": "Address {{count}}",
      "fill_address": "Please fill in the fields below:",
      "first_name": "First name",
      "last_name": "Last name",
      "company": "Company",
      "phone": "Phone number",
      "address1": "Address 1",
      "address2": "Address 2",
      "city": "City",
      "zip": "Zip code",
      "country": "Country",
      "province": "Province",
      "set_default": "Set as default"
    },
    "logout": {
      "title": "Logout"
    }
  },
  "search": {
    "general": {
      "title": "Search",
      "terms": "Results for \"{{terms}}\"",
      "search_placeholder": "What are you looking for?",
      "no_results": "No results could be found.",
      "new_search": "New search",
      "view_all_results": "View all results",
      "products": "Products",
      "queries": "Suggestions",
      "articles": "Journal",
      "pages": "Pages",
      "collections": "Collections"
    }
  },
  "contact": {
    "form": {
      "name": "Name",
      "email": "E-mail",
      "message": "Message",
      "submit": "Submit",
      "successfully_sent": "Your e-mail has been sent."
    }
  },
  "gift_card": {
    "issued": {
      "subtext": "Here's your gift card!",
      "illustration": "Gift card illustration",
      "disabled": "Your gift card is disabled.",
      "expired": "Your gift card expired on {{expiry}}.",
      "expires_on": "Expires on {{expiry}}",
      "redeem": "Use this code at checkout to redeem your gift card:",
      "remaining_amount_html": "Remaining amount: {{balance}} (out of {{initial_value}})",
      "print": "Print",
      "scan_qr_code": "Scan this QR code:",
      "add_to_apple_wallet": "Add to Apple Wallet",
      "go_to_store": "Go to the store"
    },
    "recipient": {
      "checkbox": "I want to send this as a gift",
      "email_label": "Recipient email",
      "name_label": "Recipient name (optional)",
      "send_on_label": "Send on (optional)",
      "message_label": "Message (optional)"
    }
  },
  "password": {
    "general": {
      "store_owner": "Store owner?",
      "login": "Login here",
      "powered_by": "This store will be powered by",
      "enter_password": "Enter using password"
    },
    "password": {
      "label": "Please enter password to access the store",
      "input": "Password",
      "submit": "Enter"
    },
    "newsletter": {
      "success": "You have been registered to our newsletter",
      "email": "Your e-mail",
      "submit": "Notify me"
    }
  },
  "404": {
    "general": {
      "title": "Page not found",
      "description": "Sorry, this page does not exist.",
      "back_to_home": "Back to home"
    }
  },
  "rewards": {
    "customer": {
      "learn_more": "Learn More",
      "my_rewards": "My Rewards"
    }
  },
  "returns": {
    "no_returns_title": "Not available for returns.",
    "no_returns_text": "This item is final sale, and can't be returned.",
    "no_exchanges_title": "Not available for exchanges.",
    "no_exchanges_text": "Some items, like gift cards or bundle items, aren't available for exchanges. Please choose something else for your exchange!",
    "no_returns_or_exchanges_title": "Not available for returns or exchanges.",
    "no_returns_or_exchanges_text": "Some items, like gift cards or bundle items, aren't available for exchanges."
  }
}
