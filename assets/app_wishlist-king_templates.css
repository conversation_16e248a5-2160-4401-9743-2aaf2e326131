:root {
  --wk-icon--stroke-width: 1px;
  --wk-link--color: currentColor;
  --wk-link__icon--size: 20px;
  --wk-button--color: currentColor;
  --wk-button__icon--color: currentColor;
  --wk-button__icon--size: 16px;
  --wk-product-form__submit--background: white;
  --wk-product-form__submit--color: currentColor;
  --wk-share-button--color: white;
  --wk-share-button--background: var(---color--primary--rgb);
  --wk-share-button--size: 26px;
  --wk-page__image--aspect-ratio: 1 / 1;
  --wk-page__grid--max-columns: 4
}

.wk-icon {
  position: relative;
  display: inline-block
}

.wk-title {
  text-align: center;
  margin-bottom: 8px
}

.wk-icon__svg,
.wk-icon__svg:not(:root) {
  display: block;
  width: 100%;
  height: 100%;
  overflow: visible
}

.wk-icon__svg {
  fill: currentColor;
  stroke: currentColor;
  stroke-width: var(--wk-icon--stroke-width);
  display: block
}

.wk-page {
  display: flex;
  flex-direction: column
}

.wk-page:not(.wk-page--shared) .wk-icon__svg {
  stroke-width: calc(.5 * var(--wk-icon--stroke-width))
}

.wk-page-title {
  margin: 25px auto;
  text-align: center
}

.wk-header__list-item {
  display: inline-block;
  list-style-type: none
}

.wk-link {
  position: relative;
  display: inline-flex;
  align-items: center;
  color: var(--wk-link--color);
  text-decoration: none
}

.wk-link--empty .wk-icon__svg {
  fill: transparent
}

.wk-link__icon {
  width: var(--wk-link__icon--size);
  height: var(--wk-link__icon--size);
  margin: 0 5px 0 0;
  color: var(--wk-link--color)
}

.wk-link__count {
  position: relative;
  margin: 0
}

.wk-link__count::before {
  content: ""
}

.wk-link__count::after {
  content: ""
}

.wk-button {
  position: relative;
  display: flex;
  align-items: center;
  line-height: 1;
  appearance: none;
  border: none;
  background: none;
  padding: 1em 0;
  cursor: pointer
}

.wk-button:hover {
  color: var(--wk-button--color)
}

.wk-button--add .wk-icon__svg {
  fill: transparent
}

.wk-button__icon {
  width: var(--wk-button__icon--size);
  height: var(--wk-button__icon--size);
  color: var(--wk-button__icon--color);
  margin: 0 5px 0 0
}

.wk-button__label {
  position: relative;
  color: var(--wk-button--color)
}

.wk-button__count {
  color: var(--wk-button--color)
}

.wk-button.wk-button--floating {
  position: absolute;
  z-index: 1;
  top: 5px;
  right: 5px;
  width: auto;
  height: auto;
  min-width: 0;
  min-height: 0;
  padding-left: 5px;
  padding-right: 5px;
  padding-top: 5px;
  padding-bottom: 5px;
  margin-left: 0;
  margin-right: 0;
  margin-top: 0;
  margin-bottom: 0;
  border: none;
  line-height: 1;
  background: none
}

.wk-button--floating .wk-button__icon {
  margin: 0;
  top: 0;
  transform: none
}

.wk-button--floating .wk-button__label {
  display: none
}

.wk-page:not(.wk-page--shared) .wk-button.wk-button--floating {
  top: 5px;
  right: 5px;
  left: auto;
  bottom: auto;
  width: auto;
  height: auto;
  min-width: 0;
  min-height: 0;
  padding-left: 5px;
  padding-right: 5px;
  padding-top: 5px;
  padding-bottom: 5px;
  margin-left: 0;
  margin-right: 0;
  margin-top: 0;
  margin-bottom: 0;
  border: none;
  line-height: 1;
  background: none;
  box-shadow: none
}

.wk-page:not(.wk-page--shared) .wk-button--floating {
  --wk-button__icon--color: black
}

.wk-note {
  text-align: center
}

.wk-note__login a {
  text-decoration: underline
}

.wk-grid {
  display: grid;
  grid-template-columns: repeat(var(--wk-page__grid--max-columns), 1fr);
  grid-gap: 3em 2em;
  padding: 3em 1em 4em
}

@media (max-width: 850px) {
  .wk-grid {
    grid-template-columns: repeat(min(var(--wk-page__grid--max-columns), 3), 1fr)
  }
}

@media (max-width: 600px) {
  .wk-grid {
    grid-template-columns: repeat(min(var(--wk-page__grid--max-columns), 2), 1fr)
  }
}

@media (max-width: 320px) {
  .wk-grid {
    grid-template-columns: repeat(1, 1fr)
  }
}

.wk-grid__item {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%
}

.wk-product-image {
  display: block;
  padding-top: calc(100% / (var(--wk-page__image--aspect-ratio)));
  background-position: center center;
  background-size: contain;
  background-repeat: no-repeat;
  text-decoration: none;
  outline: none
}

.wk-product-image:empty {
  display: block
}

.wk-product-image:visited,
.wk-product-image:active,
.wk-product-image:focus {
  text-decoration: none
}

.wk-product-info {
  margin-top: 1em
}

.wk-product-title {
  font-weight: 700;
  text-decoration: none;
  color: currentColor
}

.wk-product-title:visited,
.wk-product-title:active,
.wk-product-title:focus {
  color: currentColor
}

.wk-product-price--current {
  display: inline-block
}

.wk-product-price--compare {
  display: none;
  text-decoration: line-through
}

.wk-product--sale .wk-product-price--compare {
  display: inline-block
}

.wk-product-form {
  margin-top: auto
}

.wk-product-form__options {
  margin: 1em 0 1em 0
}

.wk-product-form__option {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-bottom: .5em;
  width: 100%
}

.wk-product-form__option__select {
  width: 100%
}

.wk-product-form__option:last-of-type {
  margin-bottom: 0
}

.wk-product-form__option__label {
  display: none
}

.wk-product-form__submit {
  display: block;
  width: 100%;
  padding: .5em 1em;
  appearance: none;
  border: none;
  background: var(--wk-product-form__submit--background);
  color: var(--wk-product-form__submit--color);
  text-align: center;
  border: 1px solid var(--wk-product-form__submit--color)
}

.wk-sharing {
  margin-bottom: 50px
}

.wk-sharing__list {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  list-style: none;
  padding: 0 1em
}

.wk-sharing__list-item {
  display: inline-flex;
  margin: 0 4px
}

.wk-share-button {
  display: inline-flex;
  align-items: center;
  justify-items: center;
  width: var(--wk-share-button--size);
  height: var(--wk-share-button--size);
  padding: 6px;
  background: var(--wk-share-button--background);
  color: var(--wk-share-button--color);
  text-decoration: none;
  border: none
}

.wk-share-button:hover {
  background: var(--wk-share-button--background);
  color: var(--wk-share-button--color)
}

.wk-sharing__link {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 1em;
  font-size: 11px
}

.wk-sharing__link--hidden {
  display: none
}

.wk-sharing__link__copy-button {
  background: var(--wk-share-button--background);
  color: var(--wk-share-button--color);
  font-size: 11px;
  line-height: 1;
  padding: 4px 6px;
  margin-left: 1em;
  appearance: none;
  border: none
}