var li=Object.create;var On=Object.defineProperty;var si=Object.getOwnPropertyDescriptor;var ui=Object.getOwnPropertyNames;var fi=Object.getPrototypeOf,ci=Object.prototype.hasOwnProperty;var di=(C,f)=>()=>(f||C((f={exports:{}}).exports,f),f.exports);var pi=(C,f,J,Qe)=>{if(f&&typeof f=="object"||typeof f=="function")for(let ce of ui(f))!ci.call(C,ce)&&ce!==J&&On(C,ce,{get:()=>f[ce],enumerable:!(Qe=si(f,ce))||Qe.enumerable});return C};var hi=(C,f,J)=>(J=C!=null?li(fi(C)):{},pi(f||!C||!C.__esModule?On(J,"default",{value:C,enumerable:!0}):J,C));var kn=di(($t,Rn)=>{(function(C,f){typeof define=="function"&&define.amd?define(f):typeof $t=="object"?Rn.exports=f():C.PhotoSwipe=f()})($t,function(){"use strict";var C=function(f,J,Qe,ce){var l={features:null,bind:function(e,t,n,i){var o=(i?"remove":"add")+"EventListener";t=t.split(" ");for(var a=0;a<t.length;a++)t[a]&&e[o](t[a],n,!1)},isArray:function(e){return e instanceof Array},createEl:function(e,t){var n=document.createElement(t||"div");return e&&(n.className=e),n},getScrollY:function(){var e=window.pageYOffset;return e!==void 0?e:document.documentElement.scrollTop},unbind:function(e,t,n){l.bind(e,t,n,!0)},removeClass:function(e,t){var n=new RegExp("(\\s|^)"+t+"(\\s|$)");e.className=e.className.replace(n," ").replace(/^\s\s*/,"").replace(/\s\s*$/,"")},addClass:function(e,t){l.hasClass(e,t)||(e.className+=(e.className?" ":"")+t)},hasClass:function(e,t){return e.className&&new RegExp("(^|\\s)"+t+"(\\s|$)").test(e.className)},getChildByClass:function(e,t){for(var n=e.firstChild;n;){if(l.hasClass(n,t))return n;n=n.nextSibling}},arraySearch:function(e,t,n){for(var i=e.length;i--;)if(e[i][n]===t)return i;return-1},extend:function(e,t,n){for(var i in t)if(t.hasOwnProperty(i)){if(n&&e.hasOwnProperty(i))continue;e[i]=t[i]}},easing:{sine:{out:function(e){return Math.sin(e*(Math.PI/2))},inOut:function(e){return-(Math.cos(Math.PI*e)-1)/2}},cubic:{out:function(e){return--e*e*e+1}}},detectFeatures:function(){if(l.features)return l.features;var e=l.createEl(),t=e.style,n="",i={};if(i.oldIE=document.all&&!document.addEventListener,i.touch="ontouchstart"in window,window.requestAnimationFrame&&(i.raf=window.requestAnimationFrame,i.caf=window.cancelAnimationFrame),i.pointerEvent=!!window.PointerEvent||navigator.msPointerEnabled,!i.pointerEvent){var o=navigator.userAgent;if(/iP(hone|od)/.test(navigator.platform)){var a=navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/);a&&a.length>0&&(a=parseInt(a[1],10),a>=1&&a<8&&(i.isOldIOSPhone=!0))}var c=o.match(/Android\s([0-9\.]*)/),m=c?c[1]:0;m=parseFloat(m),m>=1&&(m<4.4&&(i.isOldAndroid=!0),i.androidVersion=m),i.isMobileOpera=/opera mini|opera mobi/i.test(o)}for(var d=["transform","perspective","animationName"],v=["","webkit","Moz","ms","O"],_,G,$=0;$<4;$++){n=v[$];for(var ke=0;ke<3;ke++)_=d[ke],G=n+(n?_.charAt(0).toUpperCase()+_.slice(1):_),!i[_]&&G in t&&(i[_]=G);n&&!i.raf&&(n=n.toLowerCase(),i.raf=window[n+"RequestAnimationFrame"],i.raf&&(i.caf=window[n+"CancelAnimationFrame"]||window[n+"CancelRequestAnimationFrame"]))}if(!i.raf){var Je=0;i.raf=function(X){var qt=new Date().getTime(),jt=Math.max(0,16-(qt-Je)),ai=window.setTimeout(function(){X(qt+jt)},jt);return Je=qt+jt,ai},i.caf=function(X){clearTimeout(X)}}return i.svg=!!document.createElementNS&&!!document.createElementNS("http://www.w3.org/2000/svg","svg").createSVGRect,l.features=i,i}};l.detectFeatures(),l.features.oldIE&&(l.bind=function(e,t,n,i){t=t.split(" ");for(var o=(i?"detach":"attach")+"Event",a,c=function(){n.handleEvent.call(n)},m=0;m<t.length;m++)if(a=t[m],a)if(typeof n=="object"&&n.handleEvent){if(!i)n["oldIE"+a]=c;else if(!n["oldIE"+a])return!1;e[o]("on"+a,n["oldIE"+a])}else e[o]("on"+a,n)});var r=this,Jt=25,Q=3,s={allowPanToNext:!0,spacing:.12,bgOpacity:1,mouseUsed:!1,loop:!0,pinchToClose:!0,closeOnScroll:!0,closeOnVerticalDrag:!0,verticalDragRange:.75,hideAnimationDuration:333,showAnimationDuration:333,showHideOpacity:!1,focus:!0,escKey:!0,arrowKeys:!0,mainScrollEndFriction:.35,panEndFriction:.35,isClickableElement:function(e){return e.tagName==="A"},getDoubleTapZoom:function(e,t){return e||t.initialZoomLevel<.7?1:1.33},maxSpreadZoom:1.33,modal:!0,scaleMode:"fit"};l.extend(s,ce);var ee=function(){return{x:0,y:0}},xe,Qt,vt,h,en,V,te=ee(),Ce=ee(),u=ee(),Ze,et,D,I={},g,de,_t,wt,bt,tt,ne=0,Se={},R=ee(),x,tn,H=0,nt,it,Fe,Ne,Te,ie,rt=!0,z,xt=[],ot,Ct,nn,rn,St,pe,w,Ue={},he=!1,Tt,He=function(e,t){l.extend(r,t.publicMethods),xt.push(e)},at=function(e){var t=Z();return e>t-1?e-t:e<0?t+e:e},Le={},b=function(e,t){return Le[e]||(Le[e]=[]),Le[e].push(t)},y=function(e){var t=Le[e];if(t){var n=Array.prototype.slice.call(arguments);n.shift();for(var i=0;i<t.length;i++)t[i].apply(r,n)}},L=function(){return new Date().getTime()},K=function(e){pt=e,r.bg.style.opacity=e*s.bgOpacity},on=function(e,t,n,i,o){(!he||o&&o!==r.currItem)&&(i=i/(o?o.fitRatio:r.currItem.fitRatio)),e[Te]=_t+t+"px, "+n+"px"+wt+" scale("+i+")"},S=function(e){W&&(e&&(g>r.currItem.fitRatio?he||(be(r.currItem,!1,!0),he=!0):he&&(be(r.currItem),he=!1)),on(W,u.x,u.y,g))},Be=function(e){e.container&&on(e.container.style,e.initialPosition.x,e.initialPosition.y,e.initialZoomLevel,e)},De=function(e,t){t[Te]=_t+e+"px, 0px"+wt},lt=function(e,t){if(!s.loop&&t){var n=h+(R.x*ne-e)/R.x,i=Math.round(e-j.x);(n<0&&i>0||n>=Z()-1&&i<0)&&(e=j.x+i*s.mainScrollEndFriction)}j.x=e,De(e,en)},Dt=function(e,t){var n=Ke[e]-Se[e];return Ce[e]+te[e]+n-n*(t/de)},M=function(e,t){e.x=t.x,e.y=t.y,t.id&&(e.id=t.id)},an=function(e){e.x=Math.round(e.x),e.y=Math.round(e.y)},It=null,Mt=function(){It&&(l.unbind(document,"mousemove",Mt),l.addClass(f,"pswp--has_mouse"),s.mouseUsed=!0,y("mouseUsed")),It=setTimeout(function(){It=null},100)},Fn=function(){l.bind(document,"keydown",r),w.transform&&l.bind(r.scrollWrap,"click",r),s.mouseUsed||l.bind(document,"mousemove",Mt),l.bind(window,"resize scroll orientationchange",r),y("bindEvents")},Nn=function(){l.unbind(window,"resize scroll orientationchange",r),l.unbind(window,"scroll",D.scroll),l.unbind(document,"keydown",r),l.unbind(document,"mousemove",Mt),w.transform&&l.unbind(r.scrollWrap,"click",r),B&&l.unbind(window,Ze,r),clearTimeout(Tt),y("unbindEvents")},Pt=function(e,t){var n=je(r.currItem,I,e);return t&&(p=n),n},ln=function(e){return e||(e=r.currItem),e.initialZoomLevel},sn=function(e){return e||(e=r.currItem),e.w>0?s.maxSpreadZoom:1},un=function(e,t,n,i){return i===r.currItem.initialZoomLevel?(n[e]=r.currItem.initialPosition[e],!0):(n[e]=Dt(e,i),n[e]>t.min[e]?(n[e]=t.min[e],!0):n[e]<t.max[e]?(n[e]=t.max[e],!0):!1)},Un=function(){if(Te){var e=w.perspective&&!z;_t="translate"+(e?"3d(":"("),wt=w.perspective?", 0px)":")";return}Te="left",l.addClass(f,"pswp--ie"),De=function(t,n){n.left=t+"px"},Be=function(t){var n=t.fitRatio>1?1:t.fitRatio,i=t.container.style,o=n*t.w,a=n*t.h;i.width=o+"px",i.height=a+"px",i.left=t.initialPosition.x+"px",i.top=t.initialPosition.y+"px"},S=function(){if(W){var t=W,n=r.currItem,i=n.fitRatio>1?1:n.fitRatio,o=i*n.w,a=i*n.h;t.width=o+"px",t.height=a+"px",t.left=u.x+"px",t.top=u.y+"px"}}},Hn=function(e){var t="";s.escKey&&e.keyCode===27?t="close":s.arrowKeys&&(e.keyCode===37?t="prev":e.keyCode===39&&(t="next")),t&&!e.ctrlKey&&!e.altKey&&!e.shiftKey&&!e.metaKey&&(e.preventDefault?e.preventDefault():e.returnValue=!1,r[t]())},Ln=function(e){e&&(Ae||ve||O||Ve)&&(e.preventDefault(),e.stopPropagation())},fn=function(){r.setScrollOffset(0,l.getScrollY())},k={},Ie=0,We=function(e){k[e]&&(k[e].raf&&Ct(k[e].raf),Ie--,delete k[e])},At=function(e){k[e]&&We(e),k[e]||(Ie++,k[e]={})},Ye=function(){for(var e in k)k.hasOwnProperty(e)&&We(e)},Ge=function(e,t,n,i,o,a,c){var m=L(),d;At(e);var v=function(){if(k[e]){if(d=L()-m,d>=i){We(e),a(n),c&&c();return}a((n-t)*o(d/i)+t),k[e].raf=ot(v)}};v()},Bn={shout:y,listen:b,viewportSize:I,options:s,isMainScrollAnimating:function(){return O},getZoomLevel:function(){return g},getCurrentIndex:function(){return h},isDragging:function(){return B},isZooming:function(){return q},setScrollOffset:function(e,t){Se.x=e,pe=Se.y=t,y("updateScrollOffset",Se)},applyZoomPan:function(e,t,n,i){u.x=t,u.y=n,g=e,S(i)},init:function(){if(!(xe||Qt)){var e;r.framework=l,r.template=f,r.bg=l.getChildByClass(f,"pswp__bg"),nn=f.className,xe=!0,w=l.detectFeatures(),ot=w.raf,Ct=w.caf,Te=w.transform,St=w.oldIE,r.scrollWrap=l.getChildByClass(f,"pswp__scroll-wrap"),r.container=l.getChildByClass(r.scrollWrap,"pswp__container"),en=r.container.style,r.itemHolders=x=[{el:r.container.children[0],wrap:0,index:-1},{el:r.container.children[1],wrap:0,index:-1},{el:r.container.children[2],wrap:0,index:-1}],x[0].el.style.display=x[2].el.style.display="none",Un(),D={resize:r.updateSize,orientationchange:function(){clearTimeout(Tt),Tt=setTimeout(function(){I.x!==r.scrollWrap.clientWidth&&r.updateSize()},500)},scroll:fn,keydown:Hn,click:Ln};var t=w.isOldIOSPhone||w.isOldAndroid||w.isMobileOpera;for((!w.animationName||!w.transform||t)&&(s.showAnimationDuration=s.hideAnimationDuration=0),e=0;e<xt.length;e++)r["init"+xt[e]]();if(J){var n=r.ui=new J(r,l);n.init()}y("firstUpdate"),h=h||s.index||0,(isNaN(h)||h<0||h>=Z())&&(h=0),r.currItem=ue(h),(w.isOldIOSPhone||w.isOldAndroid)&&(rt=!1),f.setAttribute("aria-hidden","false"),s.modal&&(rt?f.style.position="fixed":(f.style.position="absolute",f.style.top=l.getScrollY()+"px")),pe===void 0&&(y("initialLayout"),pe=rn=l.getScrollY());var i="pswp--open ";for(s.mainClass&&(i+=s.mainClass+" "),s.showHideOpacity&&(i+="pswp--animate_opacity "),i+=z?"pswp--touch":"pswp--notouch",i+=w.animationName?" pswp--css_animation":"",i+=w.svg?" pswp--svg":"",l.addClass(f,i),r.updateSize(),V=-1,H=null,e=0;e<Q;e++)De((e+V)*R.x,x[e].el.style);St||l.bind(r.scrollWrap,et,r),b("initialZoomInEnd",function(){r.setContent(x[0],h-1),r.setContent(x[2],h+1),x[0].el.style.display=x[2].el.style.display="block",s.focus&&f.focus(),Fn()}),r.setContent(x[1],h),r.updateCurrItem(),y("afterInit"),rt||(bt=setInterval(function(){!Ie&&!B&&!q&&g===r.currItem.initialZoomLevel&&r.updateSize()},1e3)),l.addClass(f,"pswp--visible")}},close:function(){xe&&(xe=!1,Qt=!0,y("close"),Nn(),Cn(r.currItem,null,!0,r.destroy))},destroy:function(){y("destroy"),_e&&clearTimeout(_e),f.setAttribute("aria-hidden","true"),f.className=nn,bt&&clearInterval(bt),l.unbind(r.scrollWrap,et,r),l.unbind(window,"scroll",r),Ft(),Ye(),Le=null},panTo:function(e,t,n){n||(e>p.min.x?e=p.min.x:e<p.max.x&&(e=p.max.x),t>p.min.y?t=p.min.y:t<p.max.y&&(t=p.max.y)),u.x=e,u.y=t,S()},handleEvent:function(e){e=e||window.event,D[e.type]&&D[e.type](e)},goTo:function(e){e=at(e);var t=e-h;H=t,h=e,r.currItem=ue(h),ne-=t,lt(R.x*ne),Ye(),O=!1,r.updateCurrItem()},next:function(){r.goTo(h+1)},prev:function(){r.goTo(h-1)},updateCurrZoomItem:function(e){if(e&&y("beforeChange",0),x[1].el.children.length){var t=x[1].el.children[0];l.hasClass(t,"pswp__zoom-wrap")?W=t.style:W=null}else W=null;p=r.currItem.bounds,de=g=r.currItem.initialZoomLevel,u.x=p.center.x,u.y=p.center.y,e&&y("afterChange")},invalidateCurrItems:function(){tt=!0;for(var e=0;e<Q;e++)x[e].item&&(x[e].item.needsUpdate=!0)},updateCurrItem:function(e){if(H!==0){var t=Math.abs(H),n;if(!(e&&t<2)){r.currItem=ue(h),he=!1,y("beforeChange",H),t>=Q&&(V+=H+(H>0?-Q:Q),t=Q);for(var i=0;i<t;i++)H>0?(n=x.shift(),x[Q-1]=n,V++,De((V+2)*R.x,n.el.style),r.setContent(n,h-t+i+1+1)):(n=x.pop(),x.unshift(n),V--,De(V*R.x,n.el.style),r.setContent(n,h+t-i-1-1));if(W&&Math.abs(H)===1){var o=ue(tn);o.initialZoomLevel!==g&&(je(o,I),be(o),Be(o))}H=0,r.updateCurrZoomItem(),tn=h,y("afterChange")}}},updateSize:function(e){if(!rt&&s.modal){var t=l.getScrollY();if(pe!==t&&(f.style.top=t+"px",pe=t),!e&&Ue.x===window.innerWidth&&Ue.y===window.innerHeight)return;Ue.x=window.innerWidth,Ue.y=window.innerHeight,f.style.height=Ue.y+"px"}if(I.x=r.scrollWrap.clientWidth,I.y=r.scrollWrap.clientHeight,fn(),R.x=I.x+Math.round(I.x*s.spacing),R.y=I.y,lt(R.x*ne),y("beforeResize"),V!==void 0){for(var n,i,o,a=0;a<Q;a++)n=x[a],De((a+V)*R.x,n.el.style),o=h+a-1,s.loop&&Z()>2&&(o=at(o)),i=ue(o),i&&(tt||i.needsUpdate||!i.bounds)?(r.cleanSlide(i),r.setContent(n,o),a===1&&(r.currItem=i,r.updateCurrZoomItem(!0)),i.needsUpdate=!1):n.index===-1&&o>=0&&r.setContent(n,o),i&&i.container&&(je(i,I),be(i),Be(i));tt=!1}de=g=r.currItem.initialZoomLevel,p=r.currItem.bounds,p&&(u.x=p.center.x,u.y=p.center.y,S(!0)),y("resize")},zoomTo:function(e,t,n,i,o){t&&(de=g,Ke.x=Math.abs(t.x)-u.x,Ke.y=Math.abs(t.y)-u.y,M(Ce,u));var a=Pt(e,!1),c={};un("x",a,c,e),un("y",a,c,e);var m=g,d={x:u.x,y:u.y};an(c);var v=function(_){_===1?(g=e,u.x=c.x,u.y=c.y):(g=(e-m)*_+m,u.x=(c.x-d.x)*_+d.x,u.y=(c.y-d.y)*_+d.y),o&&o(_),S(_===1)};n?Ge("customZoomTo",0,1,n,i||l.easing.sine.inOut,v):v(1)}},cn=30,Et=10,dn,st,P={},me={},A={},E={},Me={},re=[],ye={},Xe,ge=[],Pe={},Ot,Ve,ze,ut=0,ft=ee(),Rt=0,B,kt,ve,Ae,ct,oe,N,q,pn,hn,p,j=ee(),W,O,Ke=ee(),Ee=ee(),ae,Zt,dt,pt,ht,Wn=function(e,t){return e.x===t.x&&e.y===t.y},Yn=function(e,t){return Math.abs(e.x-t.x)<Jt&&Math.abs(e.y-t.y)<Jt},mn=function(e,t){return Pe.x=Math.abs(e.x-t.x),Pe.y=Math.abs(e.y-t.y),Math.sqrt(Pe.x*Pe.x+Pe.y*Pe.y)},Ft=function(){ct&&(Ct(ct),ct=null)},yn=function(){B&&(ct=ot(yn),qn())},Gn=function(){return!(s.scaleMode==="fit"&&g===r.currItem.initialZoomLevel)},gn=function(e,t){return!e||e===document||e.getAttribute("class")&&e.getAttribute("class").indexOf("pswp__scroll-wrap")>-1?!1:t(e)?e:gn(e.parentNode,t)},Nt={},vn=function(e,t){return Nt.prevent=!gn(e.target,s.isClickableElement),y("preventDragEvent",e,t,Nt),Nt.prevent},_n=function(e,t){return t.x=e.pageX,t.y=e.pageY,t.id=e.identifier,t},wn=function(e,t,n){n.x=(e.x+t.x)*.5,n.y=(e.y+t.y)*.5},Xn=function(e,t,n){if(e-st>50){var i=ge.length>2?ge.shift():{};i.x=t,i.y=n,ge.push(i),st=e}},bn=function(){var e=u.y-r.currItem.initialPosition.y;return 1-Math.abs(e/(I.y/2))},qe={},Vn={},le=[],mt,Ut=function(e){for(;le.length>0;)le.pop();return ie?(mt=0,re.forEach(function(t){mt===0?le[0]=t:mt===1&&(le[1]=t),mt++})):e.type.indexOf("touch")>-1?e.touches&&e.touches.length>0&&(le[0]=_n(e.touches[0],qe),e.touches.length>1&&(le[1]=_n(e.touches[1],Vn))):(qe.x=e.pageX,qe.y=e.pageY,qe.id="",le[0]=qe),le},xn=function(e,t){var n,i=0,o=u[e]+t[e],a,c=t[e]>0,m=j.x+t.x,d=j.x-ye.x,v,_;if(o>p.min[e]||o<p.max[e]?n=s.panEndFriction:n=1,o=u[e]+t[e]*n,(s.allowPanToNext||g===r.currItem.initialZoomLevel)&&(W?ae==="h"&&e==="x"&&!ve&&(c?(o>p.min[e]&&(n=s.panEndFriction,i=p.min[e]-o,a=p.min[e]-Ce[e]),(a<=0||d<0)&&Z()>1?(_=m,d<0&&m>ye.x&&(_=ye.x)):p.min.x!==p.max.x&&(v=o)):(o<p.max[e]&&(n=s.panEndFriction,i=o-p.max[e],a=Ce[e]-p.max[e]),(a<=0||d>0)&&Z()>1?(_=m,d>0&&m<ye.x&&(_=ye.x)):p.min.x!==p.max.x&&(v=o))):_=m,e==="x"))return _!==void 0&&(lt(_,!0),_===ye.x?oe=!1:oe=!0),p.min.x!==p.max.x&&(v!==void 0?u.x=v:oe||(u.x+=t.x*n)),_!==void 0;O||oe||g>r.currItem.fitRatio&&(u[e]+=t[e]*n)},zn=function(e){if(!(e.type==="mousedown"&&e.button>0)){if(Oe){e.preventDefault();return}if(!(ze&&e.type==="mousedown")){if(vn(e,!0)&&e.preventDefault(),y("pointerDown"),ie){var t=l.arraySearch(re,e.pointerId,"id");t<0&&(t=re.length),re[t]={x:e.pageX,y:e.pageY,id:e.pointerId}}var n=Ut(e),i=n.length;N=null,Ye(),(!B||i===1)&&(B=Zt=!0,l.bind(window,Ze,r),Ot=ht=dt=Ve=oe=Ae=kt=ve=!1,ae=null,y("firstTouchStart",n),M(Ce,u),te.x=te.y=0,M(E,n[0]),M(Me,E),ye.x=R.x*ne,ge=[{x:E.x,y:E.y}],st=dn=L(),Pt(g,!0),Ft(),yn()),!q&&i>1&&!O&&!oe&&(de=g,ve=!1,q=kt=!0,te.y=te.x=0,M(Ce,u),M(P,n[0]),M(me,n[1]),wn(P,me,Ee),Ke.x=Math.abs(Ee.x)-u.x,Ke.y=Math.abs(Ee.y)-u.y,pn=hn=mn(P,me))}}},Kn=function(e){if(e.preventDefault(),ie){var t=l.arraySearch(re,e.pointerId,"id");if(t>-1){var n=re[t];n.x=e.pageX,n.y=e.pageY}}if(B){var i=Ut(e);if(!ae&&!Ae&&!q)if(j.x!==R.x*ne)ae="h";else{var o=Math.abs(i[0].x-E.x)-Math.abs(i[0].y-E.y);Math.abs(o)>=Et&&(ae=o>0?"h":"v",N=i)}else N=i}},qn=function(){if(N){var e=N.length;if(e!==0)if(M(P,N[0]),A.x=P.x-E.x,A.y=P.y-E.y,q&&e>1){if(E.x=P.x,E.y=P.y,!A.x&&!A.y&&Wn(N[1],me))return;M(me,N[1]),ve||(ve=!0,y("zoomGestureStarted"));var t=mn(P,me),n=ei(t);n>r.currItem.initialZoomLevel+r.currItem.initialZoomLevel/15&&(ht=!0);var i=1,o=ln(),a=sn();if(n<o)if(s.pinchToClose&&!ht&&de<=r.currItem.initialZoomLevel){var c=o-n,m=1-c/(o/1.2);K(m),y("onPinchClose",m),dt=!0}else i=(o-n)/o,i>1&&(i=1),n=o-i*(o/3);else n>a&&(i=(n-a)/(o*6),i>1&&(i=1),n=a+i*o);i<0&&(i=0),pn=t,wn(P,me,ft),te.x+=ft.x-Ee.x,te.y+=ft.y-Ee.y,M(Ee,ft),u.x=Dt("x",n),u.y=Dt("y",n),Ot=n>g,g=n,S()}else{if(!ae||(Zt&&(Zt=!1,Math.abs(A.x)>=Et&&(A.x-=N[0].x-Me.x),Math.abs(A.y)>=Et&&(A.y-=N[0].y-Me.y)),E.x=P.x,E.y=P.y,A.x===0&&A.y===0))return;if(ae==="v"&&s.closeOnVerticalDrag&&!Gn()){te.y+=A.y,u.y+=A.y;var d=bn();Ve=!0,y("onVerticalDrag",d),K(d),S();return}Xn(L(),P.x,P.y),Ae=!0,p=r.currItem.bounds;var v=xn("x",A);v||(xn("y",A),an(u),S())}}},jn=function(e){if(w.isOldAndroid){if(ze&&e.type==="mouseup")return;e.type.indexOf("touch")>-1&&(clearTimeout(ze),ze=setTimeout(function(){ze=0},600))}y("pointerUp"),vn(e,!1)&&e.preventDefault();var t;if(ie){var n=l.arraySearch(re,e.pointerId,"id");if(n>-1)if(t=re.splice(n,1)[0],navigator.msPointerEnabled){var i={4:"mouse",2:"touch",3:"pen"};t.type=i[e.pointerType],t.type||(t.type=e.pointerType||"mouse")}else t.type=e.pointerType||"mouse"}var o=Ut(e),a,c=o.length;if(e.type==="mouseup"&&(c=0),c===2)return N=null,!0;c===1&&M(Me,o[0]),c===0&&!ae&&!O&&(t||(e.type==="mouseup"?t={x:e.pageX,y:e.pageY,type:"mouse"}:e.changedTouches&&e.changedTouches[0]&&(t={x:e.changedTouches[0].pageX,y:e.changedTouches[0].pageY,type:"touch"})),y("touchRelease",e,t));var m=-1;if(c===0&&(B=!1,l.unbind(window,Ze,r),Ft(),q?m=0:Rt!==-1&&(m=L()-Rt)),Rt=c===1?L():-1,m!==-1&&m<150?a="zoom":a="swipe",q&&c<2&&(q=!1,c===1&&(a="zoomPointerUp"),y("zoomGestureEnded")),N=null,!(!Ae&&!ve&&!O&&!Ve)){if(Ye(),Xe||(Xe=$n()),Xe.calculateSwipeSpeed("x"),Ve){var d=bn();if(d<s.verticalDragRange)r.close();else{var v=u.y,_=pt;Ge("verticalDrag",0,1,300,l.easing.cubic.out,function($){u.y=(r.currItem.initialPosition.y-v)*$+v,K((1-_)*$+_),S()}),y("onVerticalDrag",1)}return}if((oe||O)&&c===0){var G=Qn(a,Xe);if(G)return;a="zoomPointerUp"}if(!O){if(a!=="swipe"){ti();return}!oe&&g>r.currItem.fitRatio&&Jn(Xe)}}},$n=function(){var e,t,n={lastFlickOffset:{},lastFlickDist:{},lastFlickSpeed:{},slowDownRatio:{},slowDownRatioReverse:{},speedDecelerationRatio:{},speedDecelerationRatioAbs:{},distanceOffset:{},backAnimDestination:{},backAnimStarted:{},calculateSwipeSpeed:function(i){ge.length>1?(e=L()-st+50,t=ge[ge.length-2][i]):(e=L()-dn,t=Me[i]),n.lastFlickOffset[i]=E[i]-t,n.lastFlickDist[i]=Math.abs(n.lastFlickOffset[i]),n.lastFlickDist[i]>20?n.lastFlickSpeed[i]=n.lastFlickOffset[i]/e:n.lastFlickSpeed[i]=0,Math.abs(n.lastFlickSpeed[i])<.1&&(n.lastFlickSpeed[i]=0),n.slowDownRatio[i]=.95,n.slowDownRatioReverse[i]=1-n.slowDownRatio[i],n.speedDecelerationRatio[i]=1},calculateOverBoundsAnimOffset:function(i,o){n.backAnimStarted[i]||(u[i]>p.min[i]?n.backAnimDestination[i]=p.min[i]:u[i]<p.max[i]&&(n.backAnimDestination[i]=p.max[i]),n.backAnimDestination[i]!==void 0&&(n.slowDownRatio[i]=.7,n.slowDownRatioReverse[i]=1-n.slowDownRatio[i],n.speedDecelerationRatioAbs[i]<.05&&(n.lastFlickSpeed[i]=0,n.backAnimStarted[i]=!0,Ge("bounceZoomPan"+i,u[i],n.backAnimDestination[i],o||300,l.easing.sine.out,function(a){u[i]=a,S()}))))},calculateAnimOffset:function(i){n.backAnimStarted[i]||(n.speedDecelerationRatio[i]=n.speedDecelerationRatio[i]*(n.slowDownRatio[i]+n.slowDownRatioReverse[i]-n.slowDownRatioReverse[i]*n.timeDiff/10),n.speedDecelerationRatioAbs[i]=Math.abs(n.lastFlickSpeed[i]*n.speedDecelerationRatio[i]),n.distanceOffset[i]=n.lastFlickSpeed[i]*n.speedDecelerationRatio[i]*n.timeDiff,u[i]+=n.distanceOffset[i])},panAnimLoop:function(){if(k.zoomPan&&(k.zoomPan.raf=ot(n.panAnimLoop),n.now=L(),n.timeDiff=n.now-n.lastNow,n.lastNow=n.now,n.calculateAnimOffset("x"),n.calculateAnimOffset("y"),S(),n.calculateOverBoundsAnimOffset("x"),n.calculateOverBoundsAnimOffset("y"),n.speedDecelerationRatioAbs.x<.05&&n.speedDecelerationRatioAbs.y<.05)){u.x=Math.round(u.x),u.y=Math.round(u.y),S(),We("zoomPan");return}}};return n},Jn=function(e){if(e.calculateSwipeSpeed("y"),p=r.currItem.bounds,e.backAnimDestination={},e.backAnimStarted={},Math.abs(e.lastFlickSpeed.x)<=.05&&Math.abs(e.lastFlickSpeed.y)<=.05)return e.speedDecelerationRatioAbs.x=e.speedDecelerationRatioAbs.y=0,e.calculateOverBoundsAnimOffset("x"),e.calculateOverBoundsAnimOffset("y"),!0;At("zoomPan"),e.lastNow=L(),e.panAnimLoop()},Qn=function(e,t){var n;O||(ut=h);var i;if(e==="swipe"){var o=E.x-Me.x,a=t.lastFlickDist.x<10;o>cn&&(a||t.lastFlickOffset.x>20)?i=-1:o<-cn&&(a||t.lastFlickOffset.x<-20)&&(i=1)}var c;i&&(h+=i,h<0?(h=s.loop?Z()-1:0,c=!0):h>=Z()&&(h=s.loop?0:Z()-1,c=!0),(!c||s.loop)&&(H+=i,ne-=i,n=!0));var m=R.x*ne,d=Math.abs(m-j.x),v;return!n&&m>j.x!=t.lastFlickSpeed.x>0?v=333:(v=Math.abs(t.lastFlickSpeed.x)>0?d/Math.abs(t.lastFlickSpeed.x):333,v=Math.min(v,400),v=Math.max(v,250)),ut===h&&(n=!1),O=!0,y("mainScrollAnimStart"),Ge("mainScroll",j.x,m,v,l.easing.cubic.out,lt,function(){Ye(),O=!1,ut=-1,(n||ut!==h)&&r.updateCurrItem(),y("mainScrollAnimComplete")}),n&&r.updateCurrItem(!0),n},ei=function(e){return 1/hn*e*de},ti=function(){var e=g,t=ln(),n=sn();g<t?e=t:g>n&&(e=n);var i=1,o,a=pt;return dt&&!Ot&&!ht&&g<t?(r.close(),!0):(dt&&(o=function(c){K((i-a)*c+a)}),r.zoomTo(e,0,200,l.easing.cubic.out,o),!0)};He("Gestures",{publicMethods:{initGestures:function(){var e=function(t,n,i,o,a){nt=t+n,it=t+i,Fe=t+o,a?Ne=t+a:Ne=""};ie=w.pointerEvent,ie&&w.touch&&(w.touch=!1),ie?navigator.msPointerEnabled?e("MSPointer","Down","Move","Up","Cancel"):e("pointer","down","move","up","cancel"):w.touch?(e("touch","start","move","end","cancel"),z=!0):e("mouse","down","move","up"),Ze=it+" "+Fe+" "+Ne,et=nt,ie&&!z&&(z=navigator.maxTouchPoints>1||navigator.msMaxTouchPoints>1),r.likelyTouchDevice=z,D[nt]=zn,D[it]=Kn,D[Fe]=jn,Ne&&(D[Ne]=D[Fe]),w.touch&&(et+=" mousedown",Ze+=" mousemove mouseup",D.mousedown=D[nt],D.mousemove=D[it],D.mouseup=D[Fe]),z||(s.allowPanToNext=!1)}}});var _e,Cn=function(e,t,n,i){_e&&clearTimeout(_e),Oe=!0,Ht=!0;var o;e.initialLayout?(o=e.initialLayout,e.initialLayout=null):o=s.getThumbBoundsFn&&s.getThumbBoundsFn(h);var a=n?s.hideAnimationDuration:s.showAnimationDuration,c=function(){We("initialZoom"),n?(r.template.removeAttribute("style"),r.bg.removeAttribute("style")):(K(1),t&&(t.style.display="block"),l.addClass(f,"pswp--animated-in"),y("initialZoom"+(n?"OutEnd":"InEnd"))),i&&i(),Oe=!1};if(!a||!o||o.x===void 0){y("initialZoom"+(n?"Out":"In")),g=e.initialZoomLevel,M(u,e.initialPosition),S(),f.style.opacity=n?0:1,K(1),a?setTimeout(function(){c()},a):c();return}var m=function(){var d=vt,v=!r.currItem.src||r.currItem.loadError||s.showHideOpacity;e.miniImg&&(e.miniImg.style.webkitBackfaceVisibility="hidden"),n||(g=o.w/e.w,u.x=o.x,u.y=o.y-rn,r[v?"template":"bg"].style.opacity=.001,S()),At("initialZoom"),n&&!d&&l.removeClass(f,"pswp--animated-in"),v&&(n?l[(d?"remove":"add")+"Class"](f,"pswp--animate_opacity"):setTimeout(function(){l.addClass(f,"pswp--animate_opacity")},30)),_e=setTimeout(function(){if(y("initialZoom"+(n?"Out":"In")),!n)g=e.initialZoomLevel,M(u,e.initialPosition),S(),K(1),v?f.style.opacity=1:K(1),_e=setTimeout(c,a+20);else{var _=o.w/e.w,G={x:u.x,y:u.y},$=g,ke=pt,Je=function(X){X===1?(g=_,u.x=o.x,u.y=o.y-pe):(g=(_-$)*X+$,u.x=(o.x-G.x)*X+G.x,u.y=(o.y-pe-G.y)*X+G.y),S(),v?f.style.opacity=1-X:K(ke-X*ke)};d?Ge("initialZoom",0,1,a,l.easing.cubic.out,Je,c):(Je(1),_e=setTimeout(c,a+20))}},n?25:90)};m()},se,U={},we=[],Ht,Oe,ni={index:0,errorMsg:'<div class="pswp__error-msg"><a href="%url%" target="_blank">The image</a> could not be loaded.</div>',forceProgressiveLoading:!1,preload:[1,1],getNumItemsFn:function(){return se.length}},ue,Z,ii,Sn=function(){return{center:{x:0,y:0},max:{x:0,y:0},min:{x:0,y:0}}},ri=function(e,t,n){var i=e.bounds;i.center.x=Math.round((U.x-t)/2),i.center.y=Math.round((U.y-n)/2)+e.vGap.top,i.max.x=t>U.x?Math.round(U.x-t):i.center.x,i.max.y=n>U.y?Math.round(U.y-n)+e.vGap.top:i.center.y,i.min.x=t>U.x?0:i.center.x,i.min.y=n>U.y?e.vGap.top:i.center.y},je=function(e,t,n){if(e.src&&!e.loadError){var i=!n;if(i&&(e.vGap||(e.vGap={top:0,bottom:0}),y("parseVerticalMargin",e)),U.x=t.x,U.y=t.y-e.vGap.top-e.vGap.bottom,i){var o=U.x/e.w,a=U.y/e.h;e.fitRatio=o<a?o:a;var c=s.scaleMode;c==="orig"?n=1:c==="fit"&&(n=e.fitRatio),n>1&&(n=1),e.initialZoomLevel=n,e.bounds||(e.bounds=Sn())}return n?(ri(e,e.w*n,e.h*n),i&&n===e.initialZoomLevel&&(e.initialPosition=e.bounds.center),e.bounds):void 0}else return e.w=e.h=0,e.initialZoomLevel=e.fitRatio=1,e.bounds=Sn(),e.initialPosition=e.bounds.center,e.bounds},yt=function(e,t,n,i,o,a){t.loadError||i&&(t.imageAppended=!0,be(t,i,t===r.currItem&&he),n.appendChild(i),a&&setTimeout(function(){t&&t.loaded&&t.placeholder&&(t.placeholder.style.display="none",t.placeholder=null)},500))},Tn=function(e){e.loading=!0,e.loaded=!1;var t=e.img=l.createEl("pswp__img","img"),n=function(){e.loading=!1,e.loaded=!0,e.loadComplete?e.loadComplete(e):e.img=null,t.onload=t.onerror=null,t=null};return t.onload=n,t.onerror=function(){e.loadError=!0,n()},t.src=e.src,t},Dn=function(e,t){if(e.src&&e.loadError&&e.container)return t&&(e.container.innerHTML=""),e.container.innerHTML=s.errorMsg.replace("%url%",e.src),!0},be=function(e,t,n){if(e.src){t||(t=e.container.lastChild);var i=n?e.w:Math.round(e.w*e.fitRatio),o=n?e.h:Math.round(e.h*e.fitRatio);e.placeholder&&!e.loaded&&(e.placeholder.style.width=i+"px",e.placeholder.style.height=o+"px"),t.style.width=i+"px",t.style.height=o+"px"}},In=function(){if(we.length){for(var e,t=0;t<we.length;t++)e=we[t],e.holder.index===e.index&&yt(e.index,e.item,e.baseDiv,e.img,!1,e.clearPlaceholder);we=[]}};He("Controller",{publicMethods:{lazyLoadItem:function(e){e=at(e);var t=ue(e);!t||(t.loaded||t.loading)&&!tt||(y("gettingData",e,t),t.src&&Tn(t))},initController:function(){l.extend(s,ni,!0),r.items=se=Qe,ue=r.getItemAt,Z=s.getNumItemsFn,ii=s.loop,Z()<3&&(s.loop=!1),b("beforeChange",function(e){var t=s.preload,n=e===null?!0:e>=0,i=Math.min(t[0],Z()),o=Math.min(t[1],Z()),a;for(a=1;a<=(n?o:i);a++)r.lazyLoadItem(h+a);for(a=1;a<=(n?i:o);a++)r.lazyLoadItem(h-a)}),b("initialLayout",function(){r.currItem.initialLayout=s.getThumbBoundsFn&&s.getThumbBoundsFn(h)}),b("mainScrollAnimComplete",In),b("initialZoomInEnd",In),b("destroy",function(){for(var e,t=0;t<se.length;t++)e=se[t],e.container&&(e.container=null),e.placeholder&&(e.placeholder=null),e.img&&(e.img=null),e.preloader&&(e.preloader=null),e.loadError&&(e.loaded=e.loadError=!1);we=null})},getItemAt:function(e){return e>=0&&se[e]!==void 0?se[e]:!1},allowProgressiveImg:function(){return s.forceProgressiveLoading||!z||s.mouseUsed||screen.width>1200},setContent:function(e,t){s.loop&&(t=at(t));var n=r.getItemAt(e.index);n&&(n.container=null);var i=r.getItemAt(t),o;if(!i){e.el.innerHTML="";return}y("gettingData",t,i),e.index=t,e.item=i;var a=i.container=l.createEl("pswp__zoom-wrap");if(!i.src&&i.html&&(i.html.tagName?a.appendChild(i.html):a.innerHTML=i.html),Dn(i),je(i,I),i.src&&!i.loadError&&!i.loaded){if(i.loadComplete=function(d){if(xe){if(e&&e.index===t){if(Dn(d,!0)){d.loadComplete=d.img=null,je(d,I),Be(d),e.index===h&&r.updateCurrZoomItem();return}d.imageAppended?!Oe&&d.placeholder&&(d.placeholder.style.display="none",d.placeholder=null):w.transform&&(O||Oe)?we.push({item:d,baseDiv:a,img:d.img,index:t,holder:e,clearPlaceholder:!0}):yt(t,d,a,d.img,O||Oe,!0)}d.loadComplete=null,d.img=null,y("imageLoadComplete",t,d)}},l.features.transform){var c="pswp__img pswp__img--placeholder";c+=i.msrc?"":" pswp__img--placeholder--blank";var m=l.createEl(c,i.msrc?"img":"");i.msrc&&(m.src=i.msrc),be(i,m),a.appendChild(m),i.placeholder=m}i.loading||Tn(i),r.allowProgressiveImg()&&(!Ht&&w.transform?we.push({item:i,baseDiv:a,img:i.img,index:t,holder:e}):yt(t,i,a,i.img,!0,!0))}else i.src&&!i.loadError&&(o=l.createEl("pswp__img","img"),o.style.opacity=1,o.src=i.src,be(i,o),yt(t,i,a,o,!0));!Ht&&t===h?(W=a.style,Cn(i,o||i.img)):Be(i),e.el.innerHTML="",e.el.appendChild(a)},cleanSlide:function(e){e.img&&(e.img.onload=e.img.onerror=null),e.loaded=e.loading=e.img=e.imageAppended=!1}}});var fe,Lt={},Bt=function(e,t,n){var i=document.createEvent("CustomEvent"),o={origEvent:e,target:e.target,releasePoint:t,pointerType:n||"touch"};i.initCustomEvent("pswpTap",!0,!0,o),e.target.dispatchEvent(i)};He("Tap",{publicMethods:{initTap:function(){b("firstTouchStart",r.onTapStart),b("touchRelease",r.onTapRelease),b("destroy",function(){Lt={},fe=null})},onTapStart:function(e){e.length>1&&(clearTimeout(fe),fe=null)},onTapRelease:function(e,t){if(t&&!Ae&&!kt&&!Ie){var n=t;if(fe&&(clearTimeout(fe),fe=null,Yn(n,Lt))){y("doubleTap",n);return}if(t.type==="mouse"){Bt(e,t,"mouse");return}var i=e.target.tagName.toUpperCase();if(i==="BUTTON"||l.hasClass(e.target,"pswp__single-tap")){Bt(e,t);return}M(Lt,n),fe=setTimeout(function(){Bt(e,t),fe=null},300)}}}});var F;He("DesktopZoom",{publicMethods:{initDesktopZoom:function(){St||(z?b("mouseUsed",function(){r.setupDesktopZoom()}):r.setupDesktopZoom(!0))},setupDesktopZoom:function(e){F={};var t="wheel mousewheel DOMMouseScroll";b("bindEvents",function(){l.bind(f,t,r.handleMouseWheel)}),b("unbindEvents",function(){F&&l.unbind(f,t,r.handleMouseWheel)}),r.mouseZoomedIn=!1;var n,i=function(){r.mouseZoomedIn&&(l.removeClass(f,"pswp--zoomed-in"),r.mouseZoomedIn=!1),g<1?l.addClass(f,"pswp--zoom-allowed"):l.removeClass(f,"pswp--zoom-allowed"),o()},o=function(){n&&(l.removeClass(f,"pswp--dragging"),n=!1)};b("resize",i),b("afterChange",i),b("pointerDown",function(){r.mouseZoomedIn&&(n=!0,l.addClass(f,"pswp--dragging"))}),b("pointerUp",o),e||i()},handleMouseWheel:function(e){if(g<=r.currItem.fitRatio)return s.modal&&(!s.closeOnScroll||Ie||B?e.preventDefault():Te&&Math.abs(e.deltaY)>2&&(vt=!0,r.close())),!0;if(e.stopPropagation(),F.x=0,"deltaX"in e)e.deltaMode===1?(F.x=e.deltaX*18,F.y=e.deltaY*18):(F.x=e.deltaX,F.y=e.deltaY);else if("wheelDelta"in e)e.wheelDeltaX&&(F.x=-.16*e.wheelDeltaX),e.wheelDeltaY?F.y=-.16*e.wheelDeltaY:F.y=-.16*e.wheelDelta;else if("detail"in e)F.y=e.detail;else return;Pt(g,!0);var t=u.x-F.x,n=u.y-F.y;(s.modal||t<=p.min.x&&t>=p.max.x&&n<=p.min.y&&n>=p.max.y)&&e.preventDefault(),r.panTo(t,n)},toggleDesktopZoom:function(e){e=e||{x:I.x/2+Se.x,y:I.y/2+Se.y};var t=s.getDoubleTapZoom(!0,r.currItem),n=g===t;r.mouseZoomedIn=!n,r.zoomTo(n?r.currItem.initialZoomLevel:t,e,333),l[(n?"remove":"add")+"Class"](f,"pswp--zoomed-in")}}});var oi={history:!0,galleryUID:1},Wt,Mn,$e,gt,Yt,Pn,T,Re,Gt,Xt,Y,Vt,zt=function(){return Y.hash.substring(1)},An=function(){Wt&&clearTimeout(Wt),$e&&clearTimeout($e)},En=function(){var e=zt(),t={};if(e.length<5)return t;var n,i=e.split("&");for(n=0;n<i.length;n++)if(i[n]){var o=i[n].split("=");o.length<2||(t[o[0]]=o[1])}if(s.galleryPIDs){var a=t.pid;for(t.pid=0,n=0;n<se.length;n++)if(se[n].pid===a){t.pid=n;break}}else t.pid=parseInt(t.pid,10)-1;return t.pid<0&&(t.pid=0),t},Kt=function(){if($e&&clearTimeout($e),Ie||B){$e=setTimeout(Kt,500);return}gt?clearTimeout(Mn):gt=!0;var e=h+1,t=ue(h);t.hasOwnProperty("pid")&&(e=t.pid);var n=T+"&gid="+s.galleryUID+"&pid="+e;Re||Y.hash.indexOf(n)===-1&&(Xt=!0);var i=Y.href.split("#")[0]+"#"+n;Vt?"#"+n!==window.location.hash&&history[Re?"replaceState":"pushState"]("",document.title,i):Re?Y.replace(i):Y.hash=n,Re=!0,Mn=setTimeout(function(){gt=!1},60)};He("History",{publicMethods:{initHistory:function(){if(l.extend(s,oi,!0),!!s.history){Y=window.location,Xt=!1,Gt=!1,Re=!1,T=zt(),Vt="pushState"in history,T.indexOf("gid=")>-1&&(T=T.split("&gid=")[0],T=T.split("?gid=")[0]),b("afterChange",r.updateURL),b("unbindEvents",function(){l.unbind(window,"hashchange",r.onHashChange)});var e=function(){Pn=!0,Gt||(Xt?history.back():T?Y.hash=T:Vt?history.pushState("",document.title,Y.pathname+Y.search):Y.hash=""),An()};b("unbindEvents",function(){vt&&e()}),b("destroy",function(){Pn||e()}),b("firstUpdate",function(){h=En().pid});var t=T.indexOf("pid=");t>-1&&(T=T.substring(0,t),T.slice(-1)==="&"&&(T=T.slice(0,-1))),setTimeout(function(){xe&&l.bind(window,"hashchange",r.onHashChange)},40)}},onHashChange:function(){if(zt()===T){Gt=!0,r.close();return}gt||(Yt=!0,r.goTo(En().pid),Yt=!1)},updateURL:function(){An(),!Yt&&(Re?Wt=setTimeout(Kt,800):Kt())}}}),l.extend(r,Bn)};return C})});var Zn=hi(kn());window.ThemePhotoSwipe=Zn.default;
/*! Bundled license information:

photoswipe/dist/photoswipe.js:
  (*! PhotoSwipe - v4.1.3 - 2019-01-08
  * http://photoswipe.com
  * Copyright (c) 2019 Dmitry Semenov; *)
*/
