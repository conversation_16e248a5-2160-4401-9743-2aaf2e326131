window.theme=window.theme||{},theme.utils={compact:function(t){for(var e=-1,n=null==t?0:t.length,i=0,o=[];++e<n;){var l=t[e];l&&(o[i++]=l)}return o}},Shopify.Theme={selectors:{productForm:".product-form"},sections:{sectionName:{_init:function(){var t=this.container;console.log(t)},onLoad:function(){this._init()}}},functions:{fadeOut:function(t,e){var n,i;t&&(e?(n=1,i=setInterval(function(){(n-=50/e)<=0&&(clearInterval(i),n=0,t.style.display="none",t.style.visibility="hidden"),t.style.opacity=n,t.style.filter="alpha(opacity="+100*n+")"},50)):(t.style.opacity=0,t.style.filter="alpha(opacity=0)",t.style.display="none",t.style.visibility="hidden"))}},components:{tooltips:function(){tippy("[data-tippy-content]",{theme:"dark",placement:"bottom"})}}},window.addEventListener("beforeunload",t=>{});
//# sourceMappingURL=custom.js.map
