/*! minireset.css v0.0.6 | MIT License | github.com/jgthms/minireset.css */
/*! PhotoSwipe main CSS by <PERSON> | photoswipe.com | MIT license */
@media screen and (max-width: 740px) {
  .hidden-phone {
    display: none !important;
  }
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .hidden-tablet {
    display: none !important;
  }
}

@media screen and (min-width: 741px) {
  .hidden-tablet-and-up {
    display: none !important;
  }
}

@media screen and (max-width: 999px) {
  .hidden-pocket {
    display: none !important;
  }
}

@media screen and (min-width: 1000px) and (max-width: 1199px) {
  .hidden-lap {
    display: none !important;
  }
}

@media screen and (min-width: 1000px) {
  .hidden-lap-and-up {
    display: none !important;
  }
}

@media screen and (min-width: 1200px) {
  .hidden-desk {
    display: none !important;
  }
}

@media screen and (min-width: 1400px) {
  .hidden-wide {
    display: none !important;
  }
}

@media screen and (pointer: fine) {
  .hidden-no-touch {
    display: none !important;
  }
}

@media screen and not (pointer: fine) {
  .hidden-touch {
    display: none !important;
  }
}

@media print {
  .hidden-print {
    display: none !important;
  }
}

*, :before, :after {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  box-sizing: border-box !important;
}

html {
  -ms-overflow-style: -ms-autohiding-scrollbar;
  -moz-text-size-adjust: 100%;
  text-size-adjust: 100%;
  font-family: sans-serif;
}

body {
  margin: 0;
}

[hidden] {
  display: none;
}

blockquote:first-child, ul:first-child, ol:first-child, p:first-child, h1:first-child, h2:first-child, h3:first-child, h4:first-child, h5:first-child, h6:first-child {
  margin-block-start: 0 !important;
}

blockquote:last-child, ul:last-child, ol:last-child, p:last-child, h1:last-child, h2:last-child, h3:last-child, h4:last-child, h5:last-child, h6:last-child {
  margin-block-end: 0 !important;
}

a {
  color: inherit;
  text-decoration: none;
}

button, input, select, textarea {
  color: inherit;
  font: inherit;
  text-align: inherit;
  margin: 0;
}

button, [type="submit"] {
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
  touch-action: manipulation;
  background: none;
  border: none;
  border-radius: 0;
  padding: 0;
  overflow: visible;
}

button[disabled], html input[disabled] {
  cursor: default;
}

img, video {
  vertical-align: top;
  border-style: none;
  max-width: 100%;
  height: auto;
}

img:-moz-loading {
  visibility: hidden;
}

iframe {
  border: 0;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td, th {
  padding: 0;
}

.pswp {
  touch-action: none;
  z-index: 1500;
  -webkit-backface-visibility: hidden;
  outline: none;
  width: 100%;
  height: 100%;
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
}

.pswp img {
  max-width: none;
}

.pswp--animate_opacity {
  opacity: .001;
  will-change: opacity;
  transition: opacity .333s cubic-bezier(.4, 0, .22, 1);
}

.pswp--open {
  display: block;
}

.pswp--zoom-allowed .pswp__img {
  cursor: var(--zoom-cursor-svg-url) 26 26, zoom-in;
}

.pswp--zoomed-in .pswp__img {
  cursor: grab;
}

.pswp--dragging .pswp__img {
  cursor: grabbing;
}

.pswp__bg {
  background: rgb(var(--background));
  opacity: 0;
  -webkit-backface-visibility: hidden;
  will-change: opacity;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  transform: translateZ(0);
}

.pswp__scroll-wrap {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
}

.pswp__container, .pswp__zoom-wrap {
  touch-action: none;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.pswp__container, .pswp__img {
  -webkit-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}

.pswp__zoom-wrap {
  transform-origin: 0 0;
  width: 100%;
  transition: transform .333s cubic-bezier(.4, 0, .22, 1);
  position: absolute;
}

.pswp__bg {
  will-change: opacity;
  transition: opacity .333s cubic-bezier(.4, 0, .22, 1);
}

.pswp--animated-in .pswp__bg, .pswp--animated-in .pswp__zoom-wrap {
  transition: none;
}

.pswp__container, .pswp__zoom-wrap {
  -webkit-backface-visibility: hidden;
}

.pswp__item {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: hidden;
}

.pswp__img {
  width: auto;
  height: auto;
  position: absolute;
  top: 0;
  left: 0;
}

.pswp__top-bar {
  position: absolute;
  top: var(--container-gutter);
}

.pswp__top-bar:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: var(--container-gutter);
}

.pswp__top-bar:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: var(--container-gutter);
}

.pswp__prev-next-buttons {
  margin-inline: var(--container-gutter);
  pointer-events: none;
  justify-content: space-between;
  display: flex;
  position: absolute;
  top: calc(50% - 28px);
  left: 0;
  right: 0;
}

.pswp__dots-nav-wrapper {
  padding-inline: var(--container-gutter);
  background: rgb(var(--background));
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-block-start: 20px;
  padding-block-end: 20px;
  transition: opacity .25s ease-in-out .2s, transform .25s ease-in-out .2s;
  display: flex;
  position: absolute;
  bottom: 0;
}

.pswp__dots-nav-wrapper .dots-nav {
  padding-inline-start: 20px;
  padding-inline-end: 20px;
}

.pswp__ui--hidden .pswp__dots-nav-wrapper {
  opacity: 0;
  transform: translateY(10px);
}

.pswp__button svg {
  transition: transform .25s ease-in-out;
}

@supports (padding: max(0px)) {
  .pswp__dots-nav-wrapper {
    padding-block-end: max(20px, env(safe-area-inset-bottom, 0px)  + 20px);
  }
}

@media screen and (pointer: fine) {
  .pswp__button:hover svg {
    transform: rotateZ(90deg);
  }
}

.flickity-enabled {
  position: relative;
  overflow: visible !important;
}

.flickity-enabled:focus {
  outline-offset: 2px;
}

.flickity-viewport {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.flickity-slider {
  width: 100%;
  height: 100%;
  position: absolute;
}

.flickity-enabled.is-draggable {
  -webkit-tap-highlight-color: transparent;
  -webkit-user-select: none;
  user-select: none;
}

.flickity-enabled.is-draggable:not(.is-hovering-right):not(.is-hovering-left) .flickity-viewport {
  cursor: grab;
}

.flickity-enabled.is-draggable:not(.is-hovering-right):not(.is-hovering-left) .flickity-viewport.is-pointer-down {
  cursor: grabbing;
}

.flickity-enabled.is-hovering-right .flickity-viewport {
  cursor: var(--arrow-right-svg-url) 17 14, e-resize;
}

.flickity-enabled.is-hovering-left .flickity-viewport {
  cursor: var(--arrow-left-svg-url) 17 14, w-resize;
}

.flickity-rtl .flickity-page-dots {
  direction: rtl;
}

.flickity-enabled.is-fade .flickity-slider > * {
  pointer-events: none;
  z-index: 0;
  transition: visibility .2s linear;
}

.flickity-enabled.is-fade .flickity-slider > .is-selected {
  pointer-events: auto;
  z-index: 1;
}

.flickity-enabled.is-fade .flickity-slider > :not(.is-selected) {
  visibility: hidden;
}

html {
  font-family: var(--text-font-family);
  font-weight: var(--text-font-weight);
  font-style: var(--text-font-style);
  font-size: calc(var(--base-font-size)  - 1px);
  color: rgb(var(--text-color));
  background: rgb(var(--background));
  line-height: 1.71429;
}

:lang(ar) * {
  letter-spacing: normal !important;
}

p strong, p b {
  font-weight: var(--text-font-bold-weight);
}

.heading, .blockquote, .rte h1, .rte h2, .rte h3, .rte h4, .rte h5, .rte h6, .rte blockquote {
  font-family: var(--heading-font-family);
  font-weight: var(--heading-font-weight);
  font-style: var(--heading-font-style);
  color: rgb(var(--heading-color));
  text-transform: var(--heading-text-transform);
  display: block;
}

.text--small {
  font-size: calc(var(--base-font-size)  - 2px);
  line-height: 1.69231;
}

.text--xsmall {
  font-size: calc(var(--base-font-size)  - 3px);
  line-height: 1.5;
}

.text--xxsmall {
  font-size: calc(var(--base-font-size)  - 5px);
  line-height: 1.5;
}

.text--large {
  font-size: calc(var(--base-font-size)  + 1px);
}

.text--subdued {
  color: rgba(var(--text-color), .7);
}

.text--left {
  text-align: start;
}

.text--center {
  text-align: center;
}

.text--right {
  text-align: end;
}

.text--strong {
  font-weight: var(--text-font-bold-weight);
}

.text--underlined {
  text-underline-offset: 3px;
  text-decoration: underline;
}

p a:not(.button), .rte a:not(.button), .link {
  text-underline-offset: 2px;
  text-decoration: underline;
  -webkit-text-decoration-color: rgba(var(--text-color), .35);
  text-decoration-color: rgba(var(--text-color), .35);
  transition: text-decoration-color .2s ease-in-out, color .2s ease-in-out;
}

.heading--small.link {
  line-height: 1.8;
}

@media screen and (pointer: fine) {
  p a:not(.button):hover, .rte a:not(.button):hover, .link:hover {
    color: rgb(var(--text-color));
    -webkit-text-decoration-color: rgb(var(--text-color));
    text-decoration-color: rgb(var(--text-color));
  }
}

.link--animated {
  width: max-content;
  display: block;
  position: relative;
}

.link--animated:after {
  content: "";
  transform-origin: var(--transform-origin-end);
  background: currentColor;
  width: 100%;
  height: 1px;
  transition: transform .3s;
  position: absolute;
  bottom: 2px;
  left: 0;
  transform: scaleX(0);
}

.text--underlined.link--animated {
  text-decoration: none;
}

.text--underlined.link--animated:after {
  transform: scaleX(1);
}

@media screen and (pointer: fine) {
  .link--animated[aria-expanded="true"]:after, .link--animated:hover:after {
    transform-origin: var(--transform-origin-start);
    transform: scaleX(1);
  }

  @keyframes textUnderlinedAnimatedKeyframes {
    0% {
      transform-origin: var(--transform-origin-end);
      transform: scaleX(1);
    }

    50% {
      transform-origin: var(--transform-origin-end);
      transform: scaleX(0);
    }

    51% {
      transform-origin: var(--transform-origin-start);
    }

    100% {
      transform: scaleX(1);
    }
  }

  .text--underlined.link--animated:hover:after {
    animation: .6s textUnderlinedAnimatedKeyframes;
  }
}

.link--faded {
  transition: opacity .25s ease-in-out;
}

.link--faded:hover {
  opacity: .7;
}

.visually-hidden {
  clip: rect(0 0 0 0);
  border: 0;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  position: absolute !important;
}

@media screen and (min-width: 741px) {
  html {
    font-size: var(--base-font-size);
    line-height: 1.73333;
  }

  .text--small {
    font-size: calc(var(--base-font-size)  - 1px);
    line-height: 1.71429;
  }

  .text--xsmall {
    font-size: calc(var(--base-font-size)  - 2px);
    line-height: 1.53846;
  }

  .text--xxsmall {
    font-size: calc(var(--base-font-size)  - 4px);
    line-height: 1.53846;
  }

  .text--large {
    font-size: calc(var(--base-font-size)  + 5px);
  }
}

.heading:first-child {
  margin-block-start: 0;
}

.heading:last-child {
  margin-block-end: 0;
}

.heading--small, .heading--xsmall, .heading--xxsmall {
  font-family: var(--text-font-family);
  font-style: var(--text-font-style);
  text-transform: uppercase;
  font-weight: var(--text-font-bold-weight);
  letter-spacing: 1px;
  line-height: 1.46636;
}

.heading--xxsmall {
  font-size: var(--heading-xxsmall-font-size);
}

.heading--xsmall {
  font-size: var(--heading-xsmall-font-size);
}

.heading--small {
  font-size: var(--heading-small-font-size);
  line-height: 1.5;
}

.heading--large, .rte .heading--large {
  font-size: var(--heading-large-font-size);
  letter-spacing: -.9px;
  line-height: 1.11111;
}

.h1, .rte h1 {
  font-size: var(--heading-h1-font-size);
  letter-spacing: -.9px;
  line-height: 1.11111;
}

.h2, .rte h2 {
  font-size: var(--heading-h2-font-size);
  letter-spacing: -.6px;
  line-height: 1.13333;
}

.h3, .rte h3 {
  font-size: var(--heading-h3-font-size);
  letter-spacing: -.4px;
  line-height: 1.15385;
}

.h4, .rte h4 {
  font-size: var(--heading-h4-font-size);
  letter-spacing: -.3px;
  line-height: 1.16667;
}

.h5, .rte h5 {
  font-size: var(--heading-h5-font-size);
  letter-spacing: -.3px;
  line-height: 1.2;
}

.h6, .rte h6 {
  font-size: var(--heading-h6-font-size);
  line-height: 1.25;
}

.blockquote, .rte blockquote {
  font-size: var(--heading-h4-font-size);
  letter-spacing: -.3px;
  padding-block-start: 24px;
  padding-block-end: 0;
  padding-inline-start: 24px;
  padding-inline-end: 24px;
  line-height: 1.16667;
  position: relative;
}

.blockquote:before, .rte blockquote:before {
  content: "";
  background: rgb(var(--text-color));
  opacity: .15;
  width: 71px;
  height: 56px;
  position: absolute;
  top: -10px;
  -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDEgODAiPiAgICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTAgNDEuNzdWMGg0MS42MDR2NDEuNzdMMjAuMDI2IDgwSC45ODdMMjEuNzIgNDEuNzdIMHptNTkuMzk2IDBWMEgxMDF2NDEuNzdMNzkuNDIyIDgwSDYwLjM4M2wyMC43MzItMzguMjNINTkuMzk2eiIgZmlsbD0iY3VycmVudENvbG9yIiAvPjwvc3ZnPg==");
  mask-image: url("data:image/svg+xml;base64,PHN2ZyBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDEgODAiPiAgICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTAgNDEuNzdWMGg0MS42MDR2NDEuNzdMMjAuMDI2IDgwSC45ODdMMjEuNzIgNDEuNzdIMHptNTkuMzk2IDBWMEgxMDF2NDEuNzdMNzkuNDIyIDgwSDYwLjM4M2wyMC43MzItMzguMjNINTkuMzk2eiIgZmlsbD0iY3VycmVudENvbG9yIiAvPjwvc3ZnPg==");
  -webkit-mask-size: 71px 56px;
  mask-size: 71px 56px;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
}

.blockquote:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  left: calc(50% - 35.5px);
}

.rte blockquote:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  left: calc(50% - 35.5px);
}

.blockquote:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  right: calc(50% - 35.5px);
}

.rte blockquote:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  right: calc(50% - 35.5px);
}

.unordered-list, .text-container ul, .rte ul {
  margin-inline-start: 1.25em;
  margin-inline-end: 0;
  padding: 0;
  list-style-type: square;
}

.ordered-list, .text-container ol, .rte ol {
  margin-inline-start: 1em;
  margin-inline-end: 0;
  padding: 0;
}

.unordered-list li, .text-container ul li, .rte ul li {
  padding-block-start: 2px;
  padding-block-end: 2px;
  padding-inline-start: 0;
  padding-inline-end: 5px;
}

.ordered-list li, .text-container ol li, .rte ol li {
  padding-block-start: 3px;
  padding-block-end: 3px;
  padding-inline-start: 0;
  padding-inline-end: 9px;
}

.unordered-list li::marker, .text-container ul li::marker, .rte ul li::marker {
  color: inherit;
  font-size: 16px;
}

.ordered-list li::marker, .text-container ol li::marker, .rte ol li::marker {
  color: inherit;
  font-size: 11px;
}

.table-wrapper {
  overflow-x: auto;
}

.table, .rte table {
  --table-spacing: 16px;
  width: 100%;
}

.table--loose {
  --table-spacing: 24px;
}

.table th:not([class*="text--"]), .rte table th:not([class*="text--"]) {
  text-align: start;
}

.table th, .rte table th {
  border-bottom: 1px solid rgb(var(--border-color));
  padding-block-end: 15px;
  padding-inline: var(--table-spacing);
}

.table td.half-spaced, .rte table td.half-spaced {
  padding: calc(var(--table-spacing) / 2);
}

.table td, .rte table td {
  padding: var(--table-spacing);
  padding-block-end: 0;
}

.table tr[onclick] {
  cursor: pointer;
}

.table th:first-child, .rte table th:first-child, .table td:first-child, .rte table td:first-child {
  padding-inline-start: 0;
}

.table th:last-child, .rte table th:last-child, .table td:last-child, .rte table td:last-child {
  padding-inline-end: 0;
}

.table tfoot tr:first-child td, .rte table tfoot tr:first-child td {
  border-top: 1px solid rgb(var(--border-color));
}

.table tfoot tr:not(:first-child) td, .rte table tfoot tr:not(:first-child) td {
  padding-block-start: 8px;
}

.table--bordered td {
  border-top: 1px solid rgb(var(--border-color));
  padding-block-end: var(--table-spacing);
}

.table--footered tbody tr:last-child td {
  padding-block-end: var(--table-spacing);
}

@media screen and (max-width: 740px) {
  .table tfoot td, .rte table tfoot td {
    padding-block-start: 16px;
  }
}

@media screen and (min-width: 741px) {
  .ordered-list li::marker, .text-container ol li::marker, .rte ol li::marker {
    font-size: 12px;
  }

  .heading--xsmall {
    line-height: 1.46667;
  }

  .heading--small {
    line-height: 1.23077;
  }

  .heading--large {
    line-height: 1;
  }

  .h1, .rte h1 {
    letter-spacing: -1px;
    line-height: 1.04167;
  }

  .h2, .rte h2 {
    letter-spacing: -1px;
    line-height: 1.10526;
  }

  .h3, .rte h3 {
    letter-spacing: -.8px;
    line-height: 1.0625;
  }

  .h4, .rte h4 {
    letter-spacing: -.6px;
    line-height: 1.16667;
  }

  .h5, .rte h5 {
    letter-spacing: -.3px;
    line-height: 1.2;
  }

  .h6, .rte h6 {
    line-height: 1.22222;
  }

  .blockquote, .rte blockquote {
    letter-spacing: -.6px;
    min-height: 63px;
    padding-inline-start: 49px;
    padding-inline-end: 0;
    line-height: 1.16667;
  }

  .blockquote--center {
    padding-inline-start: 0;
  }

  .blockquote:not(.blockquote--center):before, .rte blockquote:not(.blockquote--center):before {
    width: 80px;
    height: 63px;
    top: 0;
    -webkit-mask-size: 80px 63px;
    mask-size: 80px 63px;
  }

  .blockquote:not(.blockquote--center):not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    left: 0;
  }

  .rte blockquote:not(.blockquote--center):not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    left: 0;
  }

  .blockquote:not(.blockquote--center):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    right: 0;
  }

  .rte blockquote:not(.blockquote--center):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    right: 0;
  }

  .unordered-list li, .text-container ul li, .rte ul li {
    padding-block-start: 4px;
    padding-block-end: 4px;
    padding-inline-start: 0;
    padding-inline-end: 5px;
  }

  .ordered-list li, .text-container ol li, .rte ol li {
    padding-block-start: 4px;
    padding-block-end: 4px;
    padding-inline-start: 0;
    padding-inline-end: 9px;
  }
}

@media screen and (min-width: 1000px) {
  .table {
    --table-spacing: 24px;
  }

  .table--loose {
    --table-spacing: 32px;
  }
}

@media screen and (min-width: 1200px) {
  .heading--large, .rte .heading--large {
    line-height: 1;
  }

  .h1, .rte h1 {
    letter-spacing: -1px;
    line-height: 1.07143;
  }

  .h2, .rte h2 {
    letter-spacing: -1px;
    line-height: 1.08333;
  }

  .h3, .rte h3 {
    letter-spacing: -.8px;
    line-height: 1.11111;
  }

  .h4, .rte h4 {
    letter-spacing: -.7px;
    line-height: 1.13333;
  }

  .h5, .rte h5 {
    letter-spacing: -.4px;
    line-height: 1.16667;
  }

  .h6, .rte h6 {
    line-height: 1.22222;
  }

  .blockquote, .rte blockquote {
    letter-spacing: -.7px;
    min-height: 80px;
    padding-inline-start: 69px;
    line-height: 1.13333;
  }

  .blockquote--center, .rte .blockquote--center {
    padding-inline-start: 0;
  }

  .blockquote:not(.blockquote--center):before, .rte blockquote:not(.blockquote--center):before {
    width: 101px;
    height: 81px;
    -webkit-mask-size: 101px 81px;
    mask-size: 101px 81px;
  }
}

.text-container p:not(.heading) + p, .rte p:not(.heading) + p, .text-container p + form, .rte p + form {
  margin-block-start: 24px;
}

.text-container .heading--large, .rte .heading--large {
  margin-block-start: 48px;
  margin-block-end: 40px;
  margin-inline-start: 0;
  margin-inline-end: 0;
}

.text-container .h1, .rte h1 {
  margin-block-start: 48px;
  margin-block-end: 24px;
  margin-inline-start: 0;
  margin-inline-end: 0;
}

.text-container .h2, .rte h2, .text-container .h3, .rte h3, .text-container .h4, .rte h4, .text-container .h5, .rte h5, .text-container .h6, .rte h6 {
  margin-block-start: 40px;
  margin-block-end: 16px;
  margin-inline-start: 0;
  margin-inline-end: 0;
}

.text-container .heading--small, .rte .heading--small {
  margin: 16px 0;
}

.text-container .heading--xsmall, .rte .heading--xsmall {
  margin: 12px 0;
}

.blockquote, .rte blockquote {
  margin-block-start: 48px;
  margin-block-end: 64px;
  margin-inline-start: 0;
  margin-inline-end: 0;
}

.text-container img:not([style*="float"]), .rte img:not([style*="float"]) {
  margin: 34px 0;
  display: block;
}

.text-container ul, .rte ul, .text-container ol, .rte ol {
  margin-block-start: 1em;
  margin-block-end: 1em;
}

@media screen and (min-width: 1000px) {
  .text-container p + form, .rte p + form {
    margin-block-start: 32px;
  }

  .text-container .h1, .rte h1 {
    margin-block-start: 48px;
    margin-block-end: 32px;
    margin-inline-start: 0;
    margin-inline-end: 0;
  }

  .text-container .h2, .rte h2, .text-container .h3, .rte h3, .text-container .h4, .rte h4 {
    margin-block-start: 48px;
    margin-block-end: 24px;
    margin-inline-start: 0;
    margin-inline-end: 0;
  }

  .text-container .h5, .rte h5, .text-container .h6, .rte h6 {
    margin-block-start: 40px;
    margin-block-end: 16px;
    margin-inline-start: 0;
    margin-inline-end: 0;
  }

  .blockquote, .rte blockquote {
    margin-block-start: 80px;
    margin-block-end: 96px;
    margin-inline-start: 0;
    margin-inline-end: 0;
  }
}

.rte > :first-child, .text-container > :first-child {
  margin-block-start: 0;
}

.rte > :last-child, .text-container > :last-child {
  margin-block-end: 0;
}

.heading--small + .heading--large {
  margin-block-start: 32px;
}

.heading--small + p, .heading--xsmall + p, .heading--small + p, .heading--small + .h1, .heading--small + h1, .heading--small + .h2, .heading--small + h2, .heading--small + .h3, .heading--small + h3, .heading--small + .h4, .heading--small + h4, .heading--small + .h5, .heading--small + h5, .heading--small + .h6, .heading--small + h6, .heading--xsmall + p, .heading--xsmall + .h1, .heading--xsmall + h1, .heading--xsmall + .h2, .heading--xsmall + h2, .heading--xsmall + .h3, .heading--xsmall + h3 {
  margin-block-start: 16px;
}

.heading--xsmall + .h4, .heading--xsmall + h4, .heading--xsmall + .h5, .heading--xsmall + h5, .heading--xsmall + .h6, .heading--xsmall + h6 {
  margin-block-start: 12px;
}

.heading + .button-wrapper, .heading + .button-group, p + .button-wrapper, p + .button-group, .button-wrapper + p, .button-group + p {
  margin-block-start: 32px;
}

@media screen and (min-width: 741px) {
  .heading--small + p, .heading--small + .h1, .heading--small + h1, .heading--small + .h2, .heading--small + h2, .heading--small + .h3, .heading--small + h3, .heading--small + .h4, .heading--small + h4 {
    margin-block-start: 24px;
  }

  .heading--small + .h5, .heading--small + h5, .heading--small + .h6, .heading--small + h6 {
    margin-block-start: 16px;
  }

  .heading--xsmall + .h1, .heading--xsmall + h1, .heading--xsmall + .h2, .heading--xsmall + h2 {
    margin-block-start: 24px;
  }
}

.shopify-section {
  color: rgb(var(--text-color));
  scroll-margin-top: calc(var(--header-height, 0px) * var(--enable-sticky-header)  + var(--announcement-bar-height, 0px) * var(--enable-sticky-announcement-bar, 0));
}

.js .no-focus-outline :focus {
  outline: none;
}

.lock-all {
  touch-action: none;
  overflow: hidden;
}

@media screen and (max-width: 740px) {
  .lock-mobile {
    touch-action: none;
    overflow: hidden;
  }
}

.container, .shopify-policy__container.shopify-policy__container {
  max-width: var(--container-max-width);
  padding-inline: var(--container-gutter);
  --container-outer-margin: 0px;
  width: 100%;
  margin-inline-start: auto;
  margin-inline-end: auto;
}

.container--small {
  max-width: 930px;
}

@media screen and (max-width: 999px) {
  .container--flush {
    padding-inline-start: 0;
    padding-inline-end: 0;
  }
}

@media screen and (min-width: 1400px) {
  .container--medium {
    padding-inline: calc(var(--container-gutter)  + var(--grid-column-width)  + var(--grid-gap));
  }
}

.js .no-js {
  display: none !important;
}

.js .js\:hidden, .no-js .no-js\:hidden {
  display: none;
}

[reveal] {
  opacity: 0;
}

[reveal-visibility] {
  visibility: hidden;
}

.no-js [reveal] {
  opacity: 1;
}

.no-js [reveal-visibility] {
  visibility: visible;
}

.skip-to-content:focus {
  clip: auto;
  color: rgb(var(--text-color));
  background-color: rgb(var(--background));
  opacity: 1;
  z-index: 10000;
  width: auto;
  height: auto;
  margin: 0;
  padding: 10px;
  transition: none;
}

.vertical-breather {
  padding-block: var(--vertical-breather);
}

.vertical-breather--tight {
  padding-block: var(--vertical-breather-tight);
}

.vertical-breather--margin {
  margin-block: var(--vertical-breather);
  padding-block-start: 0 !important;
  padding-block-end: 0 !important;
}

.vertical-breather--margin.vertical-breather--tight {
  margin-block: var(--vertical-breather-tight);
}

@media screen and (min-width: 741px) {
  .vertical-breather--extra-tight {
    padding-block-start: 48px;
    padding-block-end: 48px;
  }

  .vertical-breather--margin.vertical-breather--extra-tight {
    margin-block-start: 48px;
    margin-block-end: 48px;
  }
}

.anchor {
  scroll-margin-top: calc(var(--header-height, 0px) * var(--enable-sticky-header)  + var(--announcement-bar-height, 0px) * var(--enable-sticky-announcement-bar, 0)  + var(--anchor-offset, 20px));
}

.anchor.vertical-breather:not(.vertical-breather--margin) {
  --anchor-offset: 0px;
}

.anchor.vertical-breather--margin {
  --anchor-offset: var(--vertical-breather);
}

.anchor.vertical-breather--tight.vertical-breather--margin {
  --anchor-offset: var(--vertical-breather-tight);
}

.icon {
  vertical-align: middle;
  pointer-events: none;
  background: none;
  display: block;
  overflow: visible;
}

.icon--inline {
  display: inline-block;
}

.icon-text {
  align-items: center;
  display: flex;
}

.icon-text svg, .icon-text img {
  margin-inline-end: 12px;
}

@supports (scale: 1) {
  [dir="rtl"] .icon--direction-aware {
    scale: -1 1;
  }
}

@supports not (scale: 1) {
  [dir="rtl"] .icon--direction-aware {
    transform: scale(-1, 1);
  }
}

.list--unstyled {
  margin: 0;
  padding: 0;
  list-style: none;
}

.loading-bar {
  opacity: 0;
  background: rgb(var(--loading-bar-background));
  transform-origin: 0;
  z-index: 50;
  pointer-events: none;
  width: 100%;
  height: 3px;
  transition: transform .25s ease-in-out;
  position: fixed;
  top: 0;
  left: 0;
  transform: scaleX(0);
}

.loading-bar.is-visible {
  opacity: 1;
}

.section {
  margin: var(--vertical-breather) 0;
  display: block;
}

.section--tight {
  margin-block: var(--vertical-breather-tight);
}

.section:empty {
  display: none;
}

.section--flush {
  margin-block-start: 0;
  margin-block-end: 0;
}

.section__color-wrapper {
  background: rgb(var(--section-background, var(--background)));
  display: flow-root;
}

.section__color-wrapper--boxed {
  border-radius: var(--block-border-radius);
}

.section__header {
  text-align: center;
  max-width: 1000px;
  margin-block-end: min(32px, var(--vertical-breather));
  margin-inline-start: auto;
  margin-inline-end: auto;
}

.section__header:only-child {
  margin-block-end: 0;
}

.section__header--tight {
  max-width: 800px;
}

.section__header--left {
  text-align: start;
  margin-left: 0;
}

.section__header--right {
  text-align: end;
  margin-right: 0;
}

.section__footer {
  text-align: center;
  margin-block-start: 32px;
}

@media screen and (min-width: 741px) {
  .section__header {
    margin-block-end: min(40px, var(--vertical-breather));
  }

  .section__footer {
    margin-block-start: min(40px, var(--vertical-breather));
  }
}

@media screen and (min-width: 1000px) {
  .section__header {
    margin-block-end: min(48px, var(--vertical-breather));
  }

  .section__footer {
    margin-block-start: min(48px, var(--vertical-breather));
  }
}

.page-header {
  text-align: center;
  position: relative;
}

.page-header--secondary {
  background: rgb(var(--secondary-background));
}

.page-header--clear:after, .page-header:before {
  content: "";
  clear: left;
  display: table;
}

.page-header__text-wrapper {
  max-width: 850px;
  margin: 38px auto;
}

.page-header--small .page-header__text-wrapper {
  margin-block-start: 24px;
  margin-block-end: 24px;
}

.page-header--alone .page-header__text-wrapper {
  margin-block-end: 72px;
}

.page-content, .shopify-policy__body {
  max-width: 1200px;
  margin-block-end: min(var(--vertical-breather), 80px);
  margin-inline-start: auto;
  margin-inline-end: auto;
}

.page-content--medium, .shopify-policy__body {
  max-width: 670px;
}

.page-content--small {
  max-width: 460px;
}

.page-content--fluid {
  max-width: none;
}

@media screen and (min-width: 741px) {
  .page-header__text-wrapper {
    margin-block-start: 68px;
    margin-block-end: 68px;
  }

  .page-header--small .page-header__text-wrapper {
    margin-block-start: 48px;
    margin-block-end: 40px;
  }

  .page-header--alone .page-header__text-wrapper {
    margin-block-end: 120px;
  }

  .breadcrumb--floating + .page-header__text-wrapper {
    margin-block-start: 80px;
  }
}

.breadcrumb {
  z-index: 1;
}

.breadcrumb--floating {
  position: absolute;
  top: 0;
}

.breadcrumb--floating:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 0;
}

.breadcrumb--floating:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 0;
}

.breadcrumb__list {
  margin: 0;
  padding: 26px 0;
  list-style: none;
  display: inline-flex;
}

.breadcrumb__item + .breadcrumb__item:before {
  content: "/";
  opacity: .7;
  float: left;
  margin: 0 4px;
}

.breadcrumb__link {
  transition: opacity .2s ease-in-out;
}

.breadcrumb__link:not([aria-current="page"]):not(:hover) {
  opacity: .7;
}

[dir="rtl"] .breadcrumb__item + .breadcrumb__item:before {
  float: right;
}

.pagination {
  justify-content: center;
  margin-block-start: 40px;
  display: flex;
}

.pagination__nav {
  border-collapse: separate;
  table-layout: fixed;
  display: table;
}

.pagination__nav-item {
  box-shadow: 1px 0 0 0 rgb(var(--border-color)), 0 1px 0 0 rgb(var(--border-color)), 1px 1px 0 0 rgb(var(--border-color)), 1px 0 0 0 rgb(var(--border-color)) inset, 0 1px 0 0 rgb(var(--border-color)) inset;
  vertical-align: middle;
  text-align: center;
  width: 47px;
  height: 47px;
  display: table-cell;
  position: relative;
}

.pagination__nav-item:first-child:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  border-top-left-radius: var(--button-border-radius);
  border-bottom-left-radius: var(--button-border-radius);
}

.pagination__nav-item:first-child:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  border-top-left-radius: var(--button-border-radius);
  border-bottom-left-radius: var(--button-border-radius);
}

.pagination__nav-item:first-child:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  border-top-right-radius: var(--button-border-radius);
  border-bottom-right-radius: var(--button-border-radius);
}

.pagination__nav-item:first-child:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  border-top-right-radius: var(--button-border-radius);
  border-bottom-right-radius: var(--button-border-radius);
}

.pagination__nav-item:last-child:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  border-top-right-radius: var(--button-border-radius);
  border-bottom-right-radius: var(--button-border-radius);
}

.pagination__nav-item:last-child:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  border-top-right-radius: var(--button-border-radius);
  border-bottom-right-radius: var(--button-border-radius);
}

.pagination__nav-item:last-child:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  border-top-left-radius: var(--button-border-radius);
  border-bottom-left-radius: var(--button-border-radius);
}

.pagination__nav-item:last-child:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  border-top-left-radius: var(--button-border-radius);
  border-bottom-left-radius: var(--button-border-radius);
}

.pagination__nav-item svg {
  margin: 0 auto;
}

.pagination__nav-item[aria-current]:before {
  content: "";
  pointer-events: none;
  z-index: 1;
  max-width: calc(100% - 3px);
  max-height: calc(100% - 3px);
  position: absolute;
  top: 2px;
  bottom: 0;
  left: 2px;
  right: 0;
  box-shadow: 0 0 0 2px;
}

@media screen and (min-width: 741px) {
  .pagination {
    margin-block-start: 48px;
  }

  .pagination__nav-item {
    width: 56px;
    height: 56px;
  }
}

.linklist__item:not(:first-child) {
  padding-block-start: 12px;
}

.linklist__item a {
  word-break: break-word;
  display: inline-block;
}

@media screen and (min-width: 1000px) {
  .linklist__item:not(:first-child) {
    padding-block-start: 6px;
  }
}

.animated-plus {
  width: 10px;
  height: 10px;
  position: relative;
}

.animated-plus:before, .animated-plus:after {
  content: "";
  background-color: currentColor;
  transition: transform .4s ease-in-out, opacity .4s ease-in-out;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%)rotate(-90deg);
}

.animated-plus:before {
  opacity: 1;
  width: 10px;
  height: 2px;
}

.animated-plus:after {
  width: 2px;
  height: 10px;
}

[aria-expanded="true"] > .animated-plus:before {
  opacity: 0;
}

[aria-expanded="true"] > .animated-plus:before, [aria-expanded="true"] > .animated-plus:after {
  transform: translate(-50%, -50%)rotate(90deg);
}

.aspect-ratio {
  margin-inline-start: auto;
  margin-inline-end: auto;
  display: block;
  position: relative;
}

.aspect-ratio img, .aspect-ratio video, .aspect-ratio svg {
  top: 0;
  object-fit: cover;
  object-position: center;
  width: 100%;
  max-width: 100%;
  height: 100%;
  max-height: 100%;
  position: absolute;
  left: 0;
}

.aspect-ratio--square img, .aspect-ratio--short img, .aspect-ratio--tall img {
  object-fit: contain;
  top: 50%;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 50%;
  transform: translate(-50%, -50%) !important;
}

.aspect-ratio--square {
  padding-block-end: 100% !important;
}

.aspect-ratio--short {
  padding-block-end: 75% !important;
}

.aspect-ratio--tall {
  padding-block-end: 150% !important;
}

@supports (aspect-ratio: 1 / 1) {
  .aspect-ratio {
    aspect-ratio: var(--aspect-ratio);
    padding-bottom: 0 !important;
  }

  .aspect-ratio--natural img, .aspect-ratio--natural video, .aspect-ratio--natural svg {
    width: auto;
    position: relative;
  }

  .aspect-ratio--square {
    aspect-ratio: 1;
  }

  .aspect-ratio--short {
    aspect-ratio: 4 / 3;
  }

  .aspect-ratio--tall {
    aspect-ratio: 2 / 3;
  }
}

.placeholder-image {
  background-color: rgb(var(--secondary-background));
  padding-block-end: 75%;
  position: relative;
}

.placeholder-background {
  background-color: rgb(var(--secondary-background));
}

.placeholder-image svg {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
}

.placeholder-image svg:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 0;
}

.placeholder-image svg:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 0;
}

@media screen and (min-width: 1000px) {
  .placeholder-image {
    padding-block-end: 45%;
  }
}

.progress-bar {
  background: rgba(var(--text-color), .15);
  height: 2px;
  display: block;
  position: relative;
}

.progress-bar:before {
  content: "";
  width: calc(100% / var(--divider));
  transform: translateX(calc(var(--transform-logical-flip) * var(--transform, 0%) * (var(--divider)  - 1)));
  transform-origin: var(--transform-origin-start);
  background: rgb(var(--text-color));
  height: 100%;
  position: absolute;
  top: 0;
}

.progress-bar:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  left: 0;
}

.progress-bar:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  right: 0;
}

[draggable].is-scrollable {
  cursor: none;
}

.custom-drag-cursor {
  pointer-events: none;
  visibility: visible;
  width: 60px;
  height: 60px;
  transition: visibility .15s linear;
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}

.custom-drag-cursor svg {
  opacity: 1;
  transition: transform .15s ease-in-out, opacity .15s ease-in-out;
  transform: scale(1);
}

.custom-drag-cursor[hidden] svg {
  opacity: 0;
  transform: scale(.5);
}

@media screen and (max-width: 999px), screen and not (pointer: fine) {
  .custom-drag-cursor {
    display: none;
  }
}

.tap-area {
  position: relative;
}

.tap-area:before {
  content: "";
  position: absolute;
  top: -6px;
  bottom: -6px;
  left: -6px;
  right: -6px;
}

.tap-area--large:before {
  top: -10px;
  bottom: -10px;
  left: -10px;
  right: -10px;
}

@media screen and (max-width: 999px) {
  .scroller {
    scroll-snap-type: x mandatory;
    margin-inline: calc(-1 * var(--container-gutter));
    scrollbar-width: none;
    overflow: auto hidden;
  }

  .scroller::-webkit-scrollbar {
    display: none;
  }

  .scroller__inner {
    padding-inline: var(--container-gutter);
    min-width: min-content;
    margin-inline-start: auto;
    margin-inline-end: auto;
  }
}

.hide-scrollbar {
  scrollbar-width: none;
  overflow: auto hidden;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.js .animated-element {
  visibility: hidden;
}

.square-separator {
  vertical-align: middle;
  background: currentColor;
  flex-shrink: 0;
  width: 4px;
  height: 4px;
  margin: 0 8px;
  display: inline-block;
  position: relative;
}

.square-separator--block {
  margin-inline-start: 12px;
  margin-inline-end: 12px;
  top: 1px;
}

.square-separator--subdued {
  opacity: .5;
}

@keyframes prevNextButtonKeyframe {
  0% {
    transform: translateX(0%);
  }

  50% {
    transform: translateX(calc(50% + 10px));
  }

  51% {
    transform: translateX(calc(-50% - 10px));
  }

  100% {
    transform: translateX(0%);
  }
}

.prev-next-buttons {
  pointer-events: none;
  display: inline-grid;
}

.prev-next-buttons--row {
  grid-auto-flow: column;
}

.prev-next-button {
  background: rgb(var(--prev-next-button-background, var(--root-background)));
  color: rgb(var(--prev-next-button-color, var(--root-text-color)));
  border: 1px solid rgba(var(--prev-next-button-color, var(--root-text-color)), .15);
  border-radius: var(--button-border-radius);
  pointer-events: auto;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  transition: color .2s ease-in-out;
  display: flex;
  overflow: hidden;
}

.prev-next-button[disabled] {
  color: rgba(var(--prev-next-button-color), .3);
}

.prev-next-button svg {
  width: 100%;
}

.prev-next-buttons:not(.prev-next-buttons--row) .prev-next-button:first-child:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)), .prev-next-buttons:not(.prev-next-buttons--row) .prev-next-button:first-child:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.prev-next-buttons:not(.prev-next-buttons--row) .prev-next-button:last-child:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)), .prev-next-buttons:not(.prev-next-buttons--row) .prev-next-button:last-child:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.prev-next-buttons--row .prev-next-button:first-child:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.prev-next-buttons--row .prev-next-button:first-child:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.prev-next-buttons--row .prev-next-button:last-child:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.prev-next-buttons--row .prev-next-button:last-child:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

@media screen and (min-width: 741px) {
  .prev-next-button:not(.prev-next-button--small) {
    width: 56px;
    height: 56px;
  }

  .prev-next-buttons:not(.prev-next-buttons--row) .prev-next-button:last-child {
    border-top: none;
  }

  .prev-next-buttons--row .prev-next-button:last-child {
    border-left: none;
  }
}

@media screen and (pointer: fine) {
  .prev-next-button--prev:hover svg {
    animation: .3s ease-in-out reverse forwards prevNextButtonKeyframe;
  }

  .prev-next-button--next:hover svg {
    animation: .3s ease-in-out forwards prevNextButtonKeyframe;
  }
}

.dots-nav {
  flex-wrap: wrap;
  justify-content: flex-start;
  margin: -6px;
  display: flex;
}

.dots-nav--centered {
  justify-content: center;
}

.dots-nav__item {
  background: rgb(var(--text-color));
  border-radius: min(var(--button-border-radius), 6px);
  opacity: .3;
  width: 6px;
  height: 6px;
  margin: 6px;
  transition: opacity .2s ease-in-out;
  position: relative;
}

.dots-nav__item[aria-current="true"] {
  opacity: 1;
}

.price-list {
  flex-wrap: wrap;
  align-items: baseline;
  display: inline-flex;
}

.price-list--centered {
  justify-content: center;
}

.price-list--stack {
  display: inline-grid;
}

.price-list:not(.price-list--stack) > .price:not(:last-child) {
  margin-inline-end: 10px;
}

.price-list > .price--block {
  flex-basis: 100%;
  margin-inline-start: 0 !important;
}

.price-list + .link {
  margin-inline-start: 16px;
}

.price--highlight {
  color: rgb(var(--product-on-sale-accent));
}

.price--compare {
  opacity: .7;
  text-decoration: line-through;
}

.price--large {
  font-size: calc(var(--base-font-size)  + 3px);
}

.unit-price-measurement {
  display: inline-flex;
}

@media screen and (min-width: 1000px) {
  .price--large:not(.price--compare) {
    font-size: calc(var(--base-font-size)  + 7px);
  }
}

.label {
  vertical-align: top;
  font-family: var(--text-font-family);
  font-style: var(--text-font-style);
  font-weight: var(--text-font-bold-weight);
  text-transform: uppercase;
  letter-spacing: .5px;
  border-radius: min(var(--block-border-radius), 2px);
  padding: 0 5px;
  font-size: 10px;
  display: inline-block;
}

.label--highlight {
  background: rgb(var(--product-on-sale-accent));
  color: #fff;
}

.label--subdued {
  background: rgb(var(--product-sold-out-accent));
  color: #fff;
}

.label--custom {
  background: rgb(var(--product-custom-label-background));
  color: rgb(var(--product-custom-label-text-color));
}

.label--custom2 {
  background: rgb(var(--product-custom-label-2-background));
  color: rgb(var(--product-custom-label-2-text-color));
}

.label-list {
  pointer-events: none;
  flex-direction: column;
  align-items: flex-start;
  display: flex;
}

.label-list:not(.label-list--horizontal) .label:not(:last-child) {
  margin-block-end: 4px;
}

.label-list--horizontal {
  flex-direction: row;
}

.label-list--horizontal .label:not(:last-child) {
  margin-inline-end: 4px;
}

@media screen and (min-width: 741px) {
  .label {
    font-size: 12px;
  }
}

.tag-list {
  flex-wrap: wrap;
  align-items: center;
  margin: -6px;
  display: flex;
}

.tag {
  background: rgba(var(--text-color), .05);
  align-items: center;
  margin: 6px;
  padding-block-start: 7px;
  padding-block-end: 8px;
  padding-inline-start: 14px;
  padding-inline-end: 13px;
  display: flex;
}

.tag__icon {
  cursor: pointer;
  margin-block-start: 1px;
  margin-block-end: 0;
  margin-inline-start: 0;
  margin-inline-end: 9px;
  position: relative;
}

.tag-link {
  padding-inline-start: 6px;
}

.social-media {
  flex-wrap: wrap;
  display: flex;
}

.social-media__item {
  box-shadow: 1px 0 0 0 rgb(var(--border-color)), 0 1px 0 0 rgb(var(--border-color)), 1px 1px 0 0 rgb(var(--border-color)), 1px 0 0 0 rgb(var(--border-color)) inset, 0 1px 0 0 rgb(var(--border-color)) inset;
  position: relative;
  transform: translateZ(0);
}

.no-focus-outline .social-media__item {
  overflow: hidden;
}

.social-media__item:before {
  content: "";
  clip-path: polygon(0 25%, 100% 0, 100% 100%, 0% 100%);
  transform-origin: bottom;
  pointer-events: none;
  z-index: 1;
  background: currentColor;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  transition: transform .3s cubic-bezier(.215, .61, .355, 1), clip-path .3s cubic-bezier(.215, .61, .355, 1);
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  transform: scaleY(0);
}

.social-media:not(.social-media--no-radius) .social-media__item:first-child:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  border-top-left-radius: var(--button-border-radius);
  border-bottom-left-radius: var(--button-border-radius);
}

.social-media:not(.social-media--no-radius) .social-media__item:first-child:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  border-top-right-radius: var(--button-border-radius);
  border-bottom-right-radius: var(--button-border-radius);
}

.social-media:not(.social-media--no-radius) .social-media__item:last-child:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  border-top-right-radius: var(--button-border-radius);
  border-bottom-right-radius: var(--button-border-radius);
}

.social-media:not(.social-media--no-radius) .social-media__item:last-child:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  border-top-left-radius: var(--button-border-radius);
  border-bottom-left-radius: var(--button-border-radius);
}

.social-media__link {
  color: currentColor;
  z-index: 1;
  justify-content: center;
  align-items: center;
  width: 45px;
  height: 45px;
  transition: color .3s cubic-bezier(.215, .61, .355, 1);
  display: flex;
  position: relative;
}

@media screen and (min-width: 741px) {
  .social-media__link {
    width: 55px;
    height: 55px;
  }
}

@media screen and (pointer: fine) {
  .social-media__item:hover .social-media__link {
    color: rgb(var(--background));
  }

  .social-media__item:hover:before {
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
    transform: scaleY(1);
  }
}

.banner {
  text-align: start;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 12px 16px;
  display: flex;
}

.banner--centered {
  justify-content: center;
}

.banner--margin {
  margin-block-start: 24px;
}

.banner__ribbon {
  margin-inline-end: 10px;
}

.banner__content {
  margin: 0;
}

.banner--success {
  --text-color: rgb(var(--success-color));
  background: rgb(var(--success-background));
  color: rgb(var(--success-color));
}

.banner--error {
  --text-color: rgb(var(--error-color));
  background: rgb(var(--error-background));
  color: rgb(var(--error-color));
}

.banner__content ul {
  padding-inline-start: 10px;
  list-style-position: inside;
}

@media screen and (min-width: 741px) {
  .banner {
    padding: 13px 18px;
  }
}

.tabs-nav {
  margin-block-end: 32px;
  display: block;
  position: relative;
}

.tabs-nav:not(:first-child) {
  margin-block-start: 24px;
}

.tabs-nav__scroller {
  display: block;
}

.tabs-nav__scroller-inner {
  line-height: 1;
  position: relative;
}

.tabs-nav__item-list {
  vertical-align: top;
  box-shadow: 0 -1px rgb(var(--border-color)) inset;
  grid-auto-columns: max-content;
  grid-auto-flow: column;
  justify-content: flex-start;
  gap: 32px;
  display: inline-grid;
}

.tabs-nav__item {
  opacity: .7;
  padding-block-end: 18px;
  transition: opacity .25s ease-in-out;
}

.tabs-nav__item[aria-expanded="true"] {
  opacity: 1;
}

.tabs-nav__position {
  transform: scaleX(var(--scale, 0)) translateX(var(--translate, 0));
  transform-origin: 0;
  background: currentColor;
  width: 100%;
  height: 2px;
  position: absolute;
  bottom: 0;
}

.tabs-nav__position:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 0;
}

.tabs-nav__position:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 0;
}

.tabs-nav__position.is-initialized {
  transition: transform .4s ease-in-out;
}

@supports (scale: 0) {
  .tabs-nav__position {
    scale: var(--scale, 0) 1;
    translate: calc(var(--translate, 0) * var(--scale, 0));
    transform: none;
  }

  .tabs-nav__position.is-initialized {
    transition: scale .2s ease-in-out, translate .4s ease-in-out;
  }
}

.tabs-nav--center .tabs-nav__scroller-inner {
  max-width: max-content;
  margin-inline-start: auto;
  margin-inline-end: auto;
}

.tabs-nav__arrows {
  z-index: 1;
  display: none;
  position: absolute;
  top: -5px;
}

.tabs-nav__arrows:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 0;
}

.tabs-nav__arrows:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 0;
}

.tabs-nav__scroller.is-scrollable + .tabs-nav__arrows {
  display: flex;
}

.tabs-nav__arrow-item {
  background: rgb(var(--background));
  border: 1px solid rgb(var(--border-color));
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  display: flex;
}

.tabs-nav__arrow-item + .tabs-nav__arrow-item {
  border-inline-start: none;
}

.tabs-nav[arrows] .tabs-nav__scroller {
  overflow: hidden;
}

.tabs-nav[arrows] .tabs-nav__scroller.is-scrollable:before {
  content: "";
  z-index: 1;
  pointer-events: none;
  background: linear-gradient(to var(--transform-origin-start), rgb(var(--section-background, var(--background))), rgba(var(--section-background, var(--background)), 0));
  width: 48px;
  height: 100%;
  position: absolute;
  top: -2px;
}

.tabs-nav[arrows] .tabs-nav__scroller.is-scrollable:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  right: 48px;
}

.tabs-nav[arrows] .tabs-nav__scroller.is-scrollable:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  left: 48px;
}

.tabs-nav[arrows] .tabs-nav__item-list {
  width: 100%;
  min-width: max-content;
  margin-inline-end: 0;
}

.tabs-nav[arrows] .tabs-nav__item-list:after {
  content: "";
  width: 35px;
  display: block;
}

.tabs-nav--no-border.tabs-nav--narrow {
  margin-block-end: 24px;
}

.tabs-nav--no-border .tabs-nav__item-list {
  box-shadow: none;
}

.tabs-nav--no-border.tabs-nav--narrow .tabs-nav__item {
  padding-block-end: 5px;
}

@media screen and (max-width: 999px) {
  .tabs-nav--edge2edge {
    margin-inline: calc(-1 * var(--container-gutter));
  }

  .tabs-nav--edge2edge .tabs-nav__scroller-inner {
    padding-inline: var(--container-gutter);
    min-width: max-content;
  }
}

@media screen and (min-width: 741px) {
  .tabs-nav:not(:first-child) {
    margin-block-start: 32px;
  }

  .tabs-nav--no-border.tabs-nav--narrow {
    margin-block-end: 32px;
  }

  .tabs-nav__item-list {
    gap: 54px;
  }

  .tabs-nav--loose .tabs-nav__item-list {
    gap: 72px;
  }

  .tabs-nav--narrow .tabs-nav__item-list {
    gap: 40px;
  }
}

.empty-state {
  text-align: center;
  margin: 100px 0;
  position: relative;
}

.empty-state--bottom-only {
  margin-block-start: 24px;
}

.empty-state__background-text {
  text-align: center;
  opacity: .05;
  width: 100%;
  margin-block-start: -20px;
  font-size: 120px;
  font-weight: bold;
  line-height: 0;
  position: absolute;
}

.empty-state__background-text:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 0;
}

.empty-state__background-text:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 0;
}

@media screen and (min-width: 741px) {
  .empty-state {
    margin-block-start: 150px;
    margin-block-end: 150px;
  }

  .empty-state--bottom-only {
    margin-block-start: 50px;
  }

  .empty-state__background-text {
    margin-block-start: -35px;
    font-size: 200px;
    position: absolute;
  }
}

@media screen and (min-width: 1200px) {
  .empty-state {
    margin-block-start: 225px;
    margin-block-end: 225px;
  }

  .empty-state--bottom-only {
    margin-block-start: 50px;
  }
}

.bubble-count {
  color: rgb(var(--background));
  background: rgb(var(--heading-color));
  font-weight: var(--text-font-bold-weight);
  font-family: var(--text-font-family);
  font-style: var(--text-font-style);
  letter-spacing: 0;
  border-radius: 21px;
  justify-content: center;
  align-items: center;
  min-width: 21px;
  height: 21px;
  font-size: 9px;
  line-height: 1;
  transition: background .2s ease-in-out, color .2s ease-in-out;
  display: inline-flex;
  position: relative;
}

.bubble-count--top {
  vertical-align: top;
}

.quantity-selector {
  --quantity-selector-height: 46px;
  border: 1px solid rgb(var(--border-color));
  border-radius: var(--button-border-radius);
  vertical-align: middle;
  justify-content: flex-start;
  align-items: center;
  display: inline-flex;
}

.quantity-selector__button {
  height: var(--quantity-selector-height);
  width: var(--quantity-selector-height);
  justify-content: center;
  align-items: center;
  display: flex;
}

.quantity-selector__input {
  height: var(--quantity-selector-height);
  line-height: var(--quantity-selector-height);
  text-align: center;
  -webkit-appearance: none;
  appearance: none;
  background: none;
  border: none;
  padding: 0 10px;
}

.quantity-selector--small {
  --quantity-selector-height: 28px;
}

.quantity-selector--small .quantity-selector__input {
  padding: 0 2px;
}

@keyframes spinnerRotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes spinnerDash {
  0% {
    stroke-dasharray: 1 200;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 89 200;
    stroke-dashoffset: -35px;
  }

  100% {
    stroke-dasharray: 89 200;
    stroke-dashoffset: -124px;
  }
}

@keyframes spinnerColor {
  0% {
    stroke: currentColor;
  }

  40% {
    stroke: currentColor;
  }

  66% {
    stroke: currentColor;
  }

  80%, 90% {
    stroke: currentColor;
  }
}

.spinner svg {
  transform-origin: center;
  margin: auto;
  animation: 2s linear infinite spinnerRotate;
}

.spinner circle {
  animation: 1.5s ease-in-out infinite spinnerDash, 6s ease-in-out infinite spinnerColor;
}

[data-tooltip] {
  position: relative;
}

[data-tooltip]:before {
  content: attr(data-tooltip);
  white-space: nowrap;
  background: rgb(var(--heading-color));
  color: rgb(var(--background));
  font-size: calc(var(--base-font-size)  - 2px);
  pointer-events: none;
  visibility: hidden;
  opacity: 0;
  z-index: 1;
  padding: 5px 10px;
  transition: visibility .2s ease-in-out, opacity .2s ease-in-out;
  position: absolute;
  bottom: calc(100% + 6px);
  left: 50%;
  transform: translateX(-50%);
}

[data-tooltip]:after {
  content: "";
  border-style: solid;
  border-width: 6px;
  border-color: transparent transparent rgb(var(--heading-color)) rgb(var(--heading-color));
  visibility: hidden;
  z-index: 1;
  opacity: 0;
  width: 0;
  height: 0;
  transition: visibility .2s ease-in-out, opacity .2s ease-in-out;
  position: absolute;
  bottom: calc(100% + 1px);
  left: calc(50% - 7px);
  transform: rotate(-45deg);
  box-shadow: -1px 1px 1px #0000001a;
}

[data-tooltip]:hover:before, [data-tooltip]:hover:after {
  opacity: 1;
  visibility: visible;
}

[data-tooltip-position="bottom-left"]:before {
  top: calc(100% + 4px);
  bottom: auto;
  left: auto;
  right: -6px;
  transform: none;
}

[data-tooltip-position="bottom-left"]:after {
  top: calc(100% - 1px);
  left: calc(50% - 6px);
  transform: rotate(135deg);
}

.cart-notification {
  --heading-color: 255, 255, 255;
  --text-color: 255, 255, 255;
  --cart-notification-background: rgb(var(--success-color));
  transform: translateY(var(--cart-notification-offset, 0px));
  color: rgb(var(--text-color));
  visibility: visible;
  width: 100%;
  transition: visibility .25s ease-in-out, transform .25s ease-in-out;
  display: block;
  position: absolute;
  top: 100%;
  overflow: hidden;
}

.cart-notification--error {
  --cart-notification-background: rgb(var(--error-color));
}

.cart-notification--drawer {
  --cart-notification-offset: 0;
  top: var(--header-height-without-bottom-nav);
  z-index: 1;
}

.cart-notification--fixed {
  position: fixed;
  top: 0;
}

.cart-notification[hidden] {
  visibility: hidden;
}

.cart-notification__overflow {
  background: var(--cart-notification-background);
  transition: opacity .25s ease-in-out, transform .25s ease-in-out;
  transform: translateY(0);
}

.cart-notification[hidden] .cart-notification__overflow {
  opacity: 0;
  transform: translateY(-100%);
}

.cart-notification__wrapper {
  justify-content: center;
  align-items: flex-start;
  padding-block-start: 14px;
  padding-block-end: 14px;
  display: flex;
  position: relative;
}

.cart-notification .icon--cart-notification {
  position: relative;
  top: 2px;
}

.cart-notification__text-wrapper {
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
  margin-inline-start: 12px;
  display: flex;
}

.cart-notification__heading {
  margin-inline-end: 12px;
  font-size: 14px;
}

.cart-notification__close {
  margin-block-start: -1px;
  position: absolute;
}

.cart-notification__close:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 0;
}

.cart-notification__close:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 0;
}

.cart-notification--drawer .cart-notification__text-wrapper {
  flex-grow: 1;
  justify-content: space-between;
}

@media screen and (max-width: 740px) {
  .cart-notification__text-wrapper {
    flex-grow: 1;
    justify-content: space-between;
  }
}

@media screen and (max-width: 999px) {
  .cart-notification {
    transform: none !important;
  }
}

@media screen and (min-width: 741px) {
  .cart-notification__wrapper {
    padding-block-start: 19px;
    padding-block-end: 19px;
  }

  .cart-notification .icon--cart-notification {
    top: 4px;
  }

  .cart-notification__heading {
    margin-inline-end: 16px;
    font-size: 16px;
  }

  .cart-notification__close svg {
    width: 15px;
    height: 15px;
  }
}

.payment-methods-list {
  grid-template-columns: repeat(auto-fit, 38px);
  gap: 8px;
  display: grid;
}

.payment-methods-list--center {
  justify-content: center;
}

@media screen and (min-width: 741px) {
  .payment-methods-list--auto {
    grid-auto-flow: column;
  }
}

.link-bar {
  box-shadow: 0 1px rgb(var(--border-color)), 0 -1px rgb(var(--border-color));
  text-align: center;
  display: block;
  position: relative;
}

.link-bar__wrapper {
  align-items: center;
  max-width: 100%;
  display: inline-flex;
  position: relative;
}

.link-bar__scroller {
  scroll-snap-type: x proximity;
}

.link-bar__title {
  z-index: 1;
  background: rgb(var(--background));
  flex-shrink: 0;
  position: sticky;
  left: 0;
}

.link-bar__title:after {
  content: "";
  background-image: linear-gradient(to var(--transform-origin-end), rgb(var(--background)) 35%, rgba(var(--background), 0));
  width: 28px;
  height: 100%;
  position: absolute;
  top: 0;
}

.link-bar__title + .link-bar__scroller {
  padding-inline-start: 28px;
}

.link-bar__linklist {
  grid-auto-flow: column;
  align-items: center;
  gap: 28px;
  min-width: max-content;
  display: grid;
}

.link-bar__link-item, .link-bar__title {
  padding-block-start: 14px;
  padding-block-end: 14px;
}

.link-bar__link-item--selected {
  scroll-snap-align: center;
}

@media screen and (max-width: 999px) {
  .link-bar__wrapper:after {
    content: "";
    width: var(--container-gutter);
    background-image: linear-gradient(to var(--transform-origin-start), rgb(var(--background)), rgba(var(--background), 0));
    height: 100%;
    position: absolute;
    top: 0;
  }

  .link-bar__wrapper:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
    left: 100%;
  }

  .link-bar__wrapper:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
    right: 100%;
  }

  .link-bar__scroller {
    margin-inline-end: calc(-1 * var(--container-gutter));
  }

  .link-bar__linklist {
    padding-inline-end: var(--container-gutter);
  }
}

@media screen and (min-width: 741px) {
  .link-bar__title + .link-bar__scroller {
    padding-inline-start: 48px;
  }

  .link-bar__linklist {
    gap: 40px;
  }

  .link-bar__link-item, .link-bar__title {
    padding-block-start: 20px;
    padding-block-end: 20px;
  }
}

@media screen and (min-width: 1200px) {
  .link-bar__link-item, .link-bar__title {
    padding-block-start: 27px;
    padding-block-end: 27px;
  }
}

.mobile-share-buttons__item {
  align-items: center;
  margin-inline-start: 24px;
  margin-inline-end: 24px;
  padding-block-start: 20px;
  padding-block-end: 20px;
  display: flex;
}

@supports (padding: max(0px)) {
  .mobile-share-buttons {
    padding-block-end: max(20px, env(safe-area-inset-bottom)  + 20px);
  }
}

.mobile-share-buttons__item:not(:last-child) {
  border-block-end: 1px solid rgb(var(--border-color));
}

.mobile-share-buttons__item svg {
  margin-inline-end: 16px;
}

.mobile-toolbar {
  visibility: visible;
  z-index: 2;
  align-items: center;
  transition: margin-top .2s ease-in-out, visibility .2s ease-in-out;
  display: flex;
  position: sticky;
  top: calc(var(--enable-sticky-header) * var(--header-height, 0px)  + var(--announcement-bar-height, 0px) * var(--enable-sticky-announcement-bar, 0));
}

.mobile-toolbar.is-collapsed {
  visibility: hidden;
  margin-top: -48px;
}

.mobile-toolbar--fixed {
  position: relative;
  top: 0;
}

.mobile-toolbar__item {
  border-top: 1px solid rgb(var(--border-color));
  border-bottom: 1px solid rgb(var(--border-color));
  background: rgb(var(--background));
  flex: 1 0 0;
  justify-content: center;
  align-items: center;
  padding: 11px;
  display: flex;
}

.mobile-toolbar__item + .mobile-toolbar__item {
  border-inline-start: 1px solid rgb(var(--border-color));
}

.mobile-toolbar__item .icon--chevron {
  margin-inline-start: 10px;
}

@media screen and (max-width: 740px) {
  .combo-box {
    --heading-color: var(--root-heading-color);
    --text-color: var(--root-text-color);
    --background: var(--root-background);
    z-index: 10;
    color: rgb(var(--text-color));
    background: rgb(var(--background));
    visibility: hidden;
    border-radius: 10px 10px 0 0;
    flex-direction: column;
    width: 100vw;
    max-height: 75vh;
    transition: transform .7s cubic-bezier(.75, 0, .175, 1), visibility .7s cubic-bezier(.75, 0, .175, 1);
    display: flex;
    position: fixed;
    bottom: 0;
    transform: translateY(100%);
  }

  .combo-box:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 0;
  }

  .combo-box:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 0;
  }

  .combo-box[open] {
    visibility: visible;
    transform: translateY(0);
  }

  .combo-box__overlay {
    content: "";
    opacity: 0;
    visibility: hidden;
    background: #000;
    width: 100%;
    height: 100vh;
    transition: opacity .5s ease-in-out, visibility .5s ease-in-out;
    position: absolute;
    bottom: calc(100% - 10px);
  }

  .combo-box__overlay:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 0;
  }

  .combo-box__overlay:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 0;
  }

  .combo-box__header {
    text-align: center;
    box-shadow: 0 1px rgb(var(--border-color));
    border-radius: 10px 10px 0 0;
    justify-content: center;
    align-items: center;
    min-height: 64px;
    padding: 18px 32px;
    display: flex;
    position: relative;
  }

  .combo-box__header, .combo-box__content {
    background: inherit;
  }

  .combo-box__title {
    margin-block-end: 0;
  }

  .combo-box__close-button {
    z-index: 1;
    position: absolute;
    top: 24px;
  }

  .combo-box__close-button:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 24px;
  }

  .combo-box__close-button:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 24px;
  }

  .combo-box[open] > .combo-box__overlay {
    visibility: visible;
    opacity: .3;
  }

  .combo-box__option-list {
    padding-block-end: max(16px, env(safe-area-inset-bottom, 0px)  + 16px);
    padding-inline-start: 24px;
    padding-inline-end: 24px;
    overflow: hidden auto;
  }

  .combo-box__option-item {
    border-bottom: 1px solid rgb(var(--border-color));
    width: 100%;
    padding-block-start: 20px;
    padding-block-end: 20px;
    position: relative;
  }

  .combo-box__option-item:not([hidden]) {
    display: block;
  }

  .combo-box__option-item:last-child {
    border-bottom: none;
  }

  .combo-box__option-item[aria-selected="true"]:after {
    content: "";
    background-color: currentColor;
    width: 12px;
    height: 9px;
    margin-inline-start: 12px;
    display: inline-block;
    -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEgMy40TDQuNzQ5MzEgN0wxMSAxIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIi8+PC9zdmc+");
    mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEgMy40TDQuNzQ5MzEgN0wxMSAxIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIi8+PC9zdmc+");
    -webkit-mask-size: 12px 9px;
    mask-size: 12px 9px;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
  }

  .combo-box__option-item.is-disabled {
    color: rgba(var(--text-color), .5);
  }
}

@media screen and (min-width: 741px) {
  .combo-box {
    overscroll-behavior: contain;
    background: rgb(var(--background));
    z-index: 2;
    border: 1px solid rgb(var(--border-color));
    border-radius: 0 0 var(--button-border-radius) var(--button-border-radius);
    border-top: none;
    width: 100%;
    max-height: 245px;
    padding: 8px 0;
    transition: opacity .2s ease-in-out, visibility .2s ease-in-out;
    display: block;
    position: absolute;
    top: 100%;
    overflow: auto;
  }

  .combo-box:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 0;
  }

  .combo-box:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 0;
  }

  .combo-box--top {
    border-top: 1px solid rgb(var(--border-color));
    border-radius: var(--button-border-radius) var(--button-border-radius) 0 0;
    border-bottom: none;
    top: auto;
    bottom: 100%;
  }

  .combo-box:not([open]) {
    visibility: hidden;
    opacity: 0;
  }

  .combo-box__header {
    display: none;
  }

  .combo-box__option-list {
    min-width: max-content;
  }

  .combo-box__option-item:not([hidden]) {
    text-align: left;
    align-items: center;
    width: 100%;
    padding: 8px 18px;
    transition: background .2s ease-in-out;
    display: flex;
  }

  .combo-box__option-item.is-disabled {
    color: rgba(var(--text-color), .5);
  }

  .combo-box__option-item:hover, .combo-box__option-item:focus {
    background: rgb(var(--secondary-background));
  }

  .combo-box__color-swatch {
    border-radius: var(--color-swatch-border-radius);
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    width: 16px;
    height: 16px;
    margin-inline-end: 10px;
  }

  .combo-box + .select {
    transition: border-radius .2s ease-in-out;
  }

  .combo-box[open] + .select:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)), .combo-box[open] + .select:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
  }

  .combo-box--top[open] + .select:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)), .combo-box--top[open] + .select:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }
}

.price-range {
  padding-block-start: 5px;
  display: block;
}

.price-range__input-group {
  align-items: center;
  display: flex;
}

.price-range__input {
  flex: 1 0 0;
  min-width: 0;
}

.price-range__delimiter {
  margin-inline-start: 20px;
  margin-inline-end: 20px;
}

.price-range__range-group {
  margin-block-end: 15px;
  position: relative;
}

.no-js .price-range__range-group {
  display: none !important;
}

@media screen and not (pointer: fine) {
  .price-range {
    padding-block-start: 7px;
  }

  .price-range__range-group {
    margin-block-end: 18px;
  }
}

.scroll-spy {
  box-shadow: 1px 0 rgba(var(--text-color), .25) inset;
  display: block;
  position: sticky;
  top: calc(24px + var(--header-height, 0px) * var(--enable-sticky-header)  + var(--announcement-bar-height, 0px) * var(--enable-sticky-announcement-bar, 0));
}

.scroll-spy__list {
  padding-left: 6px;
}

.scroll-spy__item {
  opacity: .7;
  transition: opacity .25s ease-in-out;
}

.scroll-spy__item.is-visible {
  opacity: 1;
}

.scroll-spy__anchor {
  padding: 10px 24px;
  display: block;
}

.scroll-spy svg {
  z-index: -1;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
}

.scroll-spy svg:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 0;
}

.scroll-spy svg:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 0;
}

.scroll-spy path {
  fill: #0000;
  stroke: currentColor;
  stroke-width: 2px;
  stroke-dasharray: 0 0 0 1000;
  stroke-linecap: square;
  transition: all .3s;
}

split-lines {
  display: block;
}

@media screen and (pointer: fine) {
  .features--image-zoom .image-zoom img {
    transition: transform .5s;
    transform: translateZ(0);
  }

  .features--image-zoom .image-zoom:hover img {
    transform: scale(1.03);
  }
}

.rating {
  vertical-align: bottom;
  align-items: center;
  display: inline-flex;
}

.rating__stars {
  grid-auto-flow: column;
  column-gap: 2px;
  display: grid;
}

.rating__star {
  color: rgb(var(--product-star-rating));
}

.rating__star--empty {
  color: rgba(var(--product-star-rating), .4);
}

.rating__caption {
  margin-inline-start: 8px;
}

.openable__overlay {
  content: "";
  bottom: 100%;
  opacity: 0;
  visibility: hidden;
  background: #000;
  width: 100%;
  height: 100vh;
  transition: opacity .6s ease-in-out, visibility .6s ease-in-out;
  position: absolute;
  left: 0;
}

[open] > .openable__overlay {
  visibility: visible;
  opacity: .2;
}

product-rerender {
  display: contents;
}

::-webkit-date-and-time-value {
  text-align: start;
}

.form__banner:not(:last-child), .form__info {
  margin-block-end: 24px;
}

.form__info {
  margin-block-start: 0;
}

.form__submit {
  margin-block-start: var(--form-submit-margin);
}

.form__submit--closer {
  margin-block-start: 16px;
}

.form__secondary-action {
  text-align: center;
  width: 100%;
  margin-block-start: 18px;
  display: block;
}

.form__secondary-action .link {
  padding-left: 8px;
}

@media screen and (min-width: 1000px) {
  .form__banner:not(:last-child), .form__info {
    margin-block-end: 32px;
  }
}

.input {
  position: relative;
}

.input + .input, .input + .input-row, .input-row + .input-row, .input-row + .input {
  margin-block-start: var(--form-input-gap);
}

.input__field, #shopify-product-reviews .spr-form-input-text, #shopify-product-reviews .spr-form-input-email, #shopify-product-reviews .spr-form-input-textarea {
  -webkit-appearance: none;
  appearance: none;
  height: var(--form-input-field-height);
  line-height: var(--form-input-field-height);
  border-radius: var(--button-border-radius);
  border: 1px solid rgba(var(--text-color), .15);
  box-shadow: none;
  color: var(--form-input-color);
  text-align: start;
  background: none;
  width: 100%;
  padding: 0 18px;
  transition: border .2s ease-in-out, box-shadow .2s ease-in-out;
}

.input__field:focus, select:focus, #shopify-product-reviews .spr-form-input-text:focus, #shopify-product-reviews .spr-form-input-email:focus, #shopify-product-reviews .spr-form-input-textarea:focus {
  border-color: rgb(var(--text-color));
  box-shadow: 0 0 0 1px rgb(var(--text-color)) inset;
  outline: none;
}

.input__field::placeholder {
  color: rgba(var(--text-color), .7);
}

.input__field--transparent {
  background: none;
}

.input__field--textarea, #shopify-product-reviews .spr-form-input-textarea {
  resize: vertical;
  vertical-align: top;
  line-height: inherit;
  height: auto;
  padding-block-start: 12px;
  padding-block-end: 12px;
}

.input__label {
  pointer-events: none;
  transform-origin: var(--transform-origin-start) top;
  background: rgb(var(--section-block-background, var(--section-background, var(--background))));
  color: rgba(var(--text-color), .7);
  white-space: nowrap;
  padding: 0 5px;
  line-height: 1;
  transition: transform .2s ease-in-out;
  position: absolute;
  top: calc((var(--form-input-field-height) / 2)  - .5em);
  transform: translateY(0);
}

.input__label:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 12px;
}

.input__label:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 12px;
}

:focus-within ~ .input__label, .is-filled ~ .input__label {
  transform: scale(.733)translateY(calc(-24px - .5em))translateX(3.665px);
}

.input__block-label {
  margin-block-end: 8px;
  display: inline-block;
}

.input__field-link {
  position: absolute;
  top: 1.1em;
}

.input__field-link:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 18px;
}

.input__field-link:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 18px;
}

.input__submit-icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.input__submit-icon:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 20px;
}

.input__submit-icon:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 20px;
}

.input-row .button {
  width: 100%;
}

@media screen and (min-width: 741px) {
  .input-row {
    gap: var(--form-input-gap);
    grid-template-columns: repeat(auto-fit, minmax(10px, 1fr));
    display: grid;
  }

  .input-row .input {
    margin-block-start: 0;
  }

  .input + .input--checkbox, .input-row + .input--checkbox {
    margin-block-start: 30px;
  }

  .input__field-link:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 18px;
  }

  .input__field-link:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 18px;
  }
}

.input-prefix {
  border: 1px solid rgb(var(--border-color));
  justify-content: space-between;
  align-items: center;
  padding: 8px 10px;
  display: flex;
}

.input-prefix__field {
  -webkit-appearance: none;
  appearance: none;
  -moz-appearance: textfield;
  text-align: end;
  background: none;
  border: none;
  width: 100%;
  min-width: 0;
  padding: 0;
}

.input-prefix__field::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.input-prefix__field::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.checkbox-container {
  align-items: baseline;
  display: flex;
}

.checkbox-container + .checkbox-container {
  margin-block-start: 10px;
}

.checkbox {
  -webkit-appearance: none;
  border: 1px solid rgb(var(--border-color-darker));
  background-color: rgb(var(--background));
  cursor: pointer;
  border-radius: 0;
  flex: none;
  width: 14px;
  height: 14px;
  transition: background-color .2s ease-in-out, border .2s ease-in-out;
  position: relative;
  top: 2px;
}

.checkbox:checked {
  border-color: rgb(var(--heading-color));
  background-color: rgb(var(--heading-color));
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEwIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEgM0w0IDZMOS4wMDE0NiAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjEuNSIvPjwvc3ZnPg==");
  background-position: center;
  background-repeat: no-repeat;
}

.checkbox:disabled + label {
  opacity: .7;
  cursor: default;
}

.checkbox + label {
  cursor: pointer;
  padding-inline-start: 12px;
}

@media screen and (min-width: 1000px) {
  .checkbox-container + .checkbox-container {
    margin-block-start: 2px;
  }
}

.select-wrapper {
  position: relative;
}

.select {
  -webkit-appearance: none;
  appearance: none;
  height: var(--form-input-field-height);
  border: 1px solid rgb(var(--border-color));
  border-radius: var(--button-border-radius);
  cursor: pointer;
  background: none;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 18px;
  display: flex;
}

.select--collapse-start:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.select--collapse-start:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.select--collapse-end:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.select--collapse-end:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.select svg {
  margin-inline-start: 20px;
  transition: transform .25s ease-in-out;
}

.select ~ svg {
  position: absolute;
  top: calc(50% - 4px);
}

.select ~ svg:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 18px;
}

.select ~ svg:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 18px;
}

.select__selected-value {
  white-space: nowrap;
  text-overflow: ellipsis;
  align-items: center;
  max-width: 100%;
  display: flex;
  position: relative;
  top: -1px;
  overflow: hidden;
}

.select__color-swatch {
  border-radius: var(--color-swatch-border-radius);
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  width: 16px;
  height: 16px;
  margin-inline-end: 10px;
  position: relative;
}

.select__color-swatch--white {
  box-shadow: 0 0 0 1px rgba(var(--text-color), .3) inset;
}

.select--small {
  height: auto;
  padding: 6px 12px;
}

.select--small svg {
  margin-inline-start: 10px;
}

.select[aria-expanded="true"] svg {
  transform: rotateZ(180deg);
}

@media screen and (min-width: 741px) {
  .select__selected-value {
    pointer-events: none;
  }
}

.range {
  -webkit-appearance: none;
  appearance: none;
  background: none;
  width: 100%;
}

.range::-webkit-slider-thumb {
  -webkit-appearance: none;
}

.range::-webkit-slider-thumb {
  background: rgb(var(--background));
  cursor: pointer;
  z-index: 1;
  box-shadow: 0 0 0 5px rgb(var(--text-color)) inset;
  border: none;
  border-radius: 100%;
  width: 14px;
  height: 14px;
  margin-top: -5px;
  position: relative;
}

.range::-webkit-slider-runnable-track {
  cursor: pointer;
  background: rgb(var(--border-color));
  border: none;
  border-radius: 4px;
  width: 100%;
  height: 6px;
}

.range::-moz-range-thumb {
  background: rgb(var(--background));
  cursor: pointer;
  box-shadow: 0 0 0 5px rgb(var(--text-color)) inset;
  border: none;
  border-radius: 100%;
  width: 14px;
  height: 14px;
}

.range::-moz-range-progress {
  cursor: pointer;
  border: none;
  border-radius: 4px;
  width: 100%;
  height: 6px;
}

.range::-moz-range-track {
  cursor: pointer;
  border: none;
  border-radius: 4px;
  width: 100%;
  height: 6px;
}

.range::-moz-range-progress {
  background-color: rgba(var(--text-color), .7);
}

.range::-moz-range-track {
  background-color: rgb(var(--border-color));
}

@media screen and not (pointer: fine) {
  .range::-webkit-slider-thumb {
    box-shadow: 0 0 0 7px rgb(var(--text-color)) inset;
    width: 20px;
    height: 20px;
    margin-top: -7px;
  }

  .range::-moz-range-thumb {
    box-shadow: 0 0 0 7px rgb(var(--text-color)) inset;
    width: 20px;
    height: 20px;
  }
}

.range-group {
  background: linear-gradient(to var(--transform-origin-end), rgb(var(--border-color)) var(--range-min), rgba(var(--text-color), .7) var(--range-min), rgba(var(--text-color), .7) var(--range-max), rgb(var(--border-color)) var(--range-max));
  border-radius: 4px;
  height: 6px;
}

.range-group .range {
  pointer-events: none;
  vertical-align: top;
  height: 6px;
}

.range-group .range::-webkit-slider-runnable-track {
  background: none;
}

.range-group .range::-webkit-slider-thumb {
  pointer-events: auto;
}

.range-group .range::-moz-range-progress {
  background: none;
}

.range-group .range::-moz-range-track {
  background: none;
}

.range-group .range::-moz-range-thumb {
  pointer-events: auto;
}

.range-group .range:last-child {
  position: absolute;
  top: 0;
}

.range-group .range:last-child:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 0;
}

.range-group .range:last-child:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 0;
}

.switch-checkbox {
  background: rgb(var(--border-color));
  -webkit-appearance: none;
  appearance: none;
  cursor: pointer;
  border-radius: 20px;
  width: 40px;
  height: 20px;
  transition: background .1s ease-in;
  position: relative;
}

.switch-checkbox:before {
  content: "";
  background: rgb(var(--background));
  border-radius: 100%;
  width: 14px;
  height: 14px;
  transition: transform .1s ease-in;
  display: block;
  position: absolute;
  top: 3px;
  box-shadow: 0 1px 1px #0003;
}

.switch-checkbox:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  left: 3px;
}

.switch-checkbox:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  right: 3px;
}

.switch-checkbox:checked {
  background: rgb(var(--text-color));
}

.switch-checkbox:checked:before {
  transform: translateX(calc(100% + 6px));
}

.button, .shopify-challenge__button, #shopify-product-reviews .spr-summary-actions-newreview, #shopify-product-reviews .spr-button {
  -webkit-appearance: none;
  appearance: none;
  line-height: var(--button-height);
  text-align: center;
  border-radius: var(--button-border-radius);
  background: rgb(var(--button-background));
  color: rgb(var(--button-text-color));
  padding: 0 30px;
  text-decoration: none;
  display: inline-block;
  position: relative;
}

.button:not(.button--text), .shopify-challenge__button, #shopify-product-reviews .spr-summary-actions-newreview, #shopify-product-reviews .spr-button {
  font-size: calc(var(--base-font-size)  - 3px);
  font-family: var(--text-font-family);
  font-weight: var(--text-font-bold-weight);
  text-transform: uppercase;
  letter-spacing: 1.5px;
}

.button--small, #shopify-product-reviews .spr-summary-actions-newreview {
  line-height: var(--button-small-height);
  padding: 0 20px;
}

.button--primary, .shopify-challenge__button, #shopify-product-reviews .spr-summary-actions-newreview, #shopify-product-reviews .spr-button {
  --button-background: var(--primary-button-background);
  --button-text-color: var(--primary-button-text-color);
}

.button--secondary {
  --button-background: var(--secondary-button-background);
  --button-text-color: var(--secondary-button-text-color);
}

.button--ternary {
  --button-background: var(--secondary-background);
  --button-text-color: var(--root-text-color);
}

.button--outline {
  /* --button-background: var(--background); */
  /* --button-text-color: var(--root-text-color); */
  /* border: 1px solid rgb(var(--border-color)); */
}

.button--full {
  width: 100%;
}

.shopify-payment-button {
  min-height: var(--button-height);
}

/*
.shopify-payment-button__button--branded {
  border-radius: var(--button-border-radius) !important;
  min-height: var(--button-height) !important;
  overflow: hidden !important;
}
*/

.shopify-payment-button__button--unbranded {
  --button-background: var(--primary-button-background);
  --button-text-color: var(--primary-button-text-color);
  -webkit-appearance: none !important;
  appearance: none !important;
  line-height: var(--button-height) !important;
  text-align: center !important;
  border-radius: var(--button-border-radius) !important;
  font-size: calc(var(--base-font-size)  - 3px) !important;
  font-family: var(--text-font-family) !important;
  font-weight: var(--text-font-bold-weight) !important;
  text-transform: uppercase !important;
  letter-spacing: 1.5px !important;
  background-color: rgb(var(--button-background)) !important;
  color: rgb(var(--button-text-color)) !important;
  padding: 0 30px !important;
  text-decoration: none !important;
  display: inline-block !important;
  position: relative !important;
}

shopify-accelerated-checkout, shopify-accelerated-checkout-cart {
  --shopify-accelerated-checkout-button-block-size: var(--button-height);
  --shopify-accelerated-checkout-button-border-radius: var(--button-border-radius);
  --shopify-accelerated-checkout-button-box-shadow: none;
}

@media screen and (min-width: 741px) {
  .button:not(.button--text), .shopify-challenge__button, #shopify-product-reviews .spr-summary-actions-newreview, #shopify-product-reviews .spr-button {
    font-size: calc(var(--base-font-size)  - 2px);
    letter-spacing: 2px;
    padding-inline-start: 35px;
    padding-inline-end: 35px;
  }

  .button--small:not(.button--text), #shopify-product-reviews .spr-summary-actions-newreview {
    font-size: calc(var(--base-font-size)  - 3px);
    padding-inline-start: 28px;
    padding-inline-end: 28px;
  }

  .shopify-payment-button__button--unbranded {
    font-size: calc(var(--base-font-size)  - 2px) !important;
    letter-spacing: 2px !important;
    padding-inline-start: 35px !important;
    padding-inline-end: 35px !important;
  }
}

@media screen and (pointer: fine) {
  .button, .shopify-challenge__button, #shopify-product-reviews .spr-summary-actions-newreview, #shopify-product-reviews .spr-button, .shopify-payment-button__button {
    background-image: linear-gradient(178deg, rgb(var(--button-background)), rgb(var(--button-background)) 10%, #00000012 10%, #00000012 100%), linear-gradient(rgb(var(--button-background)), rgb(var(--button-background)));
    background-position: 100% -100%, 100% 100%;
    background-repeat: no-repeat;
    background-size: 100% 200%, 100% 100%;
    transition: background-position .3s cubic-bezier(.215, .61, .355, 1);
    transform: translateZ(0);
  }

  .button:hover, .shopify-challenge__button:hover, #shopify-product-reviews .spr-summary-actions-newreview:hover, #shopify-product-reviews .spr-button:hover, .shopify-payment-button__button:hover {
    background-position: 100% 25%, 100% 100%;
  }

  @supports (color: color-contrast(wheat vs black, white)) and (color: rgb(from wheat r g b / 0.07)) {
    .button, .shopify-challenge__button, #shopify-product-reviews .spr-summary-actions-newreview, #shopify-product-reviews .spr-button, .shopify-payment-button__button {
      --button-overlay-color: rgb(from color-contrast(rgb(var(--button-background)) vs white, black) r g b / 7%);
      background-image: linear-gradient(178deg, rgb(var(--button-background)), rgb(var(--button-background)) 10%, var(--button-overlay-color) 10%, var(--button-overlay-color) 100%), linear-gradient(rgb(var(--button-background)), rgb(var(--button-background)));
    }
  }
}

.loader-button__text {
  justify-content: center;
  align-items: center;
  display: flex;
}

.loader-button__loader {
  top: 50%;
  opacity: 0;
  position: absolute;
  left: 50%;
}

.button-group {
  font-size: 0;
}

@media screen and (max-width: 740px) {
  .button-group .button {
    margin: 12px;
    padding: 0 18px;
  }

  .button-group__wrapper {
    margin: -12px;
  }
}

@media screen and (min-width: 741px) {
  .button-group__wrapper {
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    display: inline-grid;
  }
}

.collapsible {
  visibility: hidden;
  height: 0;
  display: block;
  overflow: hidden;
}

.no-js .collapsible, .collapsible[open] {
  visibility: visible;
  height: auto;
  overflow: visible;
}

.collapsible-toggle {
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 18px 0;
  display: flex;
}

.collapsible-toggle__selected-value {
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 45%;
  margin-inline-start: auto;
  margin-inline-end: 12px;
  font-weight: normal;
  overflow: hidden;
}

.collapsible-toggle svg {
  transition: transform .2s ease-in-out;
}

.collapsible-toggle[aria-expanded="true"] svg {
  transform: rotateZ(180deg);
}

@media screen and (min-width: 741px) {
  .collapsible-toggle {
    padding: 21px 0;
  }
}

.content-box {
  z-index: 1;
  position: relative;
}

.content-box--text-center {
  text-align: center;
}

.content-box--text-right {
  text-align: end;
}

@media screen and (max-width: 740px) {
  .content-box--small, .content-box--medium, .container--flush .content-box--large {
    margin-inline-start: 24px;
    margin-inline-end: 24px;
  }

  .content-box--left {
    margin-inline-end: auto;
  }

  .content-box--right {
    margin-inline-start: auto;
  }
}

@media screen and (min-width: 741px) {
  .content-box {
    width: calc(var(--grid-column-width) * 16 + var(--grid-gap) * 15);
    margin-inline-start: auto;
    margin-inline-end: auto;
  }

  .content-box--medium {
    width: calc(var(--grid-column-width) * 14 + var(--grid-gap) * 13);
  }

  .content-box--small {
    width: calc(var(--grid-column-width) * 10 + var(--grid-gap) * 9);
  }

  .content-box--fill {
    width: 100% !important;
  }

  .content-box--left {
    margin-inline-start: calc(var(--grid-column-width)  + var(--grid-gap));
  }

  .content-box--right {
    margin-inline-end: calc(var(--grid-column-width)  + var(--grid-gap));
  }
}

@media screen and (min-width: 1000px) {
  .content-box {
    width: calc(var(--grid-column-width) * 14 + var(--grid-gap) * 13);
  }

  .content-box--medium {
    width: calc(var(--grid-column-width) * 10 + var(--grid-gap) * 9);
  }

  .content-box--small {
    width: calc(var(--grid-column-width) * 8 + var(--grid-gap) * 7);
  }
}

@media screen and (min-width: 1400px) {
  .content-box--small {
    width: calc(var(--grid-column-width) * 7 + var(--grid-gap) * 6);
  }
}

.drawer {
  --heading-color: var(--root-heading-color);
  --text-color: var(--root-text-color);
  --background: var(--root-background);
  --primary-button-background: var(--root-primary-button-background);
  --primary-button-text-color: var(--root-primary-button-text-color);
  --section-background: initial;
  background: rgb(var(--background));
  color: rgb(var(--text-color));
  z-index: 10;
  transform: translateX(calc(var(--transform-logical-flip) * 100%));
  visibility: hidden;
  text-align: start;
  flex-direction: column;
  width: 89vw;
  max-width: 400px;
  height: 100%;
  max-height: 100vh;
  font-size: 1rem;
  transition: transform .6s cubic-bezier(.75, 0, .175, 1), visibility .6s cubic-bezier(.75, 0, .175, 1);
  display: flex;
  position: fixed;
  top: 0;
}

.drawer:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 0;
}

.drawer:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 0;
}

.drawer--from-left {
  transform: translateX(calc(var(--transform-logical-flip) * -100%));
}

.drawer--from-left:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 0;
  right: auto;
}

.drawer--from-left:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: auto;
  right: 0;
}

.drawer[open] {
  visibility: visible;
  transform: translateX(0);
}

.drawer--from-left .drawer__overlay:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 100%;
  right: auto;
}

.drawer--from-left .drawer__overlay:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: auto;
  right: 100%;
}

.drawer--large {
  max-width: 500px;
}

.drawer__overlay {
  content: "";
  opacity: 0;
  visibility: hidden;
  background: #000;
  width: 100vw;
  height: 100vh;
  transition: visibility .6s ease-in-out, opacity .6s ease-in-out;
  position: fixed;
  top: 0;
}

.drawer__overlay:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 100%;
}

.drawer__overlay:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 100%;
}

.drawer[open] > .drawer__overlay {
  visibility: visible;
  opacity: .3;
}

.drawer__header {
  height: var(--header-height-without-bottom-nav);
  border-bottom: 1px solid rgb(var(--root-border-color));
  background: rgb(var(--root-background));
  z-index: 1;
  flex-shrink: 0;
  align-items: center;
  max-height: 80px;
  padding-block-start: 20px;
  padding-block-end: 20px;
  display: flex;
  position: relative;
}

.drawer__header--shadowed {
  border-bottom: none;
  height: auto;
  padding-block-end: 6px;
}

.drawer__header--shadowed:after {
  content: "";
  background: linear-gradient(var(--root-background), rgba(var(--root-background), 0));
  z-index: 1;
  pointer-events: none;
  width: 100%;
  height: 24px;
  position: absolute;
  top: 100%;
}

.drawer__header--shadowed:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
  left: 0;
}

.drawer__header--shadowed:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
  right: 0;
}

.drawer__title {
  justify-content: flex-start;
  align-items: center;
  max-width: 100%;
  margin: 0;
  display: flex;
}

.drawer__title--stack {
  flex-direction: column;
  flex-grow: 1;
  align-items: flex-start;
}

.drawer__title .icon {
  margin-inline-end: 12px;
}

.drawer__header-action {
  margin-inline-start: 16px;
}

.drawer__close-button {
  position: absolute;
  top: var(--container-gutter);
}

.drawer__close-button:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: var(--container-gutter);
}

.drawer__close-button:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: var(--container-gutter);
}

.drawer__header .drawer__close-button {
  top: calc(50% - 7px);
}

.drawer__close-button--block {
  position: relative;
  top: auto !important;
  bottom: auto !important;
  left: auto !important;
  right: auto !important;
}

.drawer__header, .drawer__content, .drawer__footer {
  padding-inline: var(--container-gutter);
}

.drawer__content {
  flex-grow: 1;
  padding-block-end: 24px;
  overflow: hidden auto;
}

.drawer__content--padded-start {
  padding-block-start: 24px;
}

.drawer__content--center {
  text-align: center;
  flex-grow: 0;
  margin-block-start: auto;
  margin-block-end: auto;
  padding-block-end: 0;
}

.drawer__footer {
  z-index: 1;
  margin-block-start: auto;
  padding-block-start: 20px;
  padding-block-end: 20px;
  transform: translateZ(0);
}

@supports (padding: max(0px)) {
  .drawer__footer {
    padding-block-end: max(20px, env(safe-area-inset-bottom, 0px)  + 20px);
  }
}

.drawer__footer--bordered {
  box-shadow: 0 1px rgb(var(--root-border-color)) inset;
}

.drawer__footer--no-top-padding {
  padding-block-start: 0 !important;
}

.drawer__footer:before {
  content: "";
  background: linear-gradient(rgba(var(--root-background), 0), rgb(var(--root-background)));
  z-index: 1;
  pointer-events: none;
  width: 100%;
  height: 24px;
  position: absolute;
  bottom: 100%;
}

.drawer__footer:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  left: 0;
}

.drawer__footer:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  right: 0;
}

@media screen and (min-width: 741px) {
  .drawer__header {
    max-height: 90px;
    padding-block-start: 24px;
    padding-block-end: 24px;
  }

  .drawer__header-action {
    margin-inline-start: 24px;
  }

  .drawer__content--padded-start {
    padding-block-start: 30px;
  }

  .drawer__footer:not(.drawer__footer--tight) {
    padding-block: var(--container-gutter);
  }
}

.popover-button {
  width: max-content;
}

.popover-button svg {
  margin-inline-start: 10px;
  transition: transform .2s ease-in-out;
  position: relative;
  top: -1px;
}

.popover-button[aria-expanded="true"] svg {
  transform: rotateZ(180deg);
}

.popover {
  --heading-color: var(--root-heading-color);
  --text-color: var(--root-text-color);
  --background: var(--root-background);
  --primary-button-background: var(--root-primary-button-background);
  --primary-button-text-color: var(--root-primary-button-text-color);
  --section-background: var(--root-background);
  z-index: 10;
  color: rgb(var(--text-color));
  background: rgb(var(--background));
  visibility: hidden;
  text-align: start;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  font-size: 1rem;
  transition: visibility .25s ease-in-out;
  display: block;
  position: fixed;
}

.popover[open] {
  visibility: visible;
}

.popover__overlay {
  content: "";
  bottom: calc(100% - 10px);
  opacity: 0;
  visibility: hidden;
  background: #000;
  width: 100%;
  height: 100vh;
  transition: opacity .6s ease-in-out, visibility .6s ease-in-out;
  position: absolute;
  left: 0;
}

.popover[open] > .popover__overlay {
  visibility: visible;
  opacity: .3;
}

.popover__header {
  border-bottom: 1px solid rgb(var(--root-border-color));
  border-radius: 10px 10px 0 0;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  min-height: 64px;
  padding: 10px 24px;
  display: flex;
  position: relative;
}

.popover__header--no-border {
  border-bottom: none;
}

.popover__title {
  justify-content: center;
  align-items: center;
  margin: 0;
  display: flex;
}

.popover__title svg {
  margin-inline-end: 12px;
}

.popover__close-button {
  z-index: 1;
  position: absolute;
  top: 24px;
}

.popover__close-button:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 24px;
}

.popover__close-button:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 24px;
}

.popover__content {
  padding: 24px;
  overflow: auto;
}

.popover__content--no-padding {
  padding: 0 !important;
}

@supports (padding: max(0px)) {
  .popover__content {
    padding-block-end: max(24px, env(safe-area-inset-bottom, 0px)  + 24px);
  }
}

@media screen and (max-width: 999px) {
  .popover {
    bottom: 0;
    touch-action: manipulation;
    border-radius: 10px 10px 0 0;
    flex-direction: column;
    width: 100vw;
    max-height: 75vh;
    transition: transform .6s cubic-bezier(.75, 0, .175, 1), visibility .6s cubic-bezier(.75, 0, .175, 1);
    display: flex;
    left: 0;
    transform: translateY(100%);
  }

  .popover[open] {
    transform: translateY(0);
  }

  .popover__header, .popover__content {
    background: inherit;
  }

  .drawer:not(.drawer--from-left) .popover:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: -11vw;
  }

  .drawer:not(.drawer--from-left) .popover:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: -11vw;
  }
}

@media screen and (min-width: 1000px) {
  .popover-container {
    position: relative;
  }

  .popover {
    border: 1px solid rgb(var(--root-border-color));
    z-index: 2;
    opacity: 0;
    border-radius: min(var(--block-border-radius), 4px);
    transition: opacity .25s ease-in-out, visibility .25s ease-in-out;
    position: absolute;
    top: calc(100% + 18px);
  }

  .popover[open] {
    opacity: 1;
  }

  .popover:after, .popover:before {
    content: "";
    border-style: solid;
    border-color: transparent transparent rgb(var(--root-background)) transparent;
    border-width: 8px;
    width: 0;
    height: 0;
    position: absolute;
    bottom: 100%;
  }

  .popover:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
    right: 24px;
  }

  .popover:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    right: 24px;
  }

  .popover:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
    left: 24px;
  }

  .popover:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    left: 24px;
  }

  .popover:before {
    border-color: transparent transparent rgb(var(--root-border-color)) transparent;
    border-width: 9px;
  }

  .popover:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    right: 23px;
  }

  .popover:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    left: 23px;
  }

  .popover__overlay, .popover__header {
    display: none;
  }

  .popover__content {
    padding-inline-start: 32px;
    padding-inline-end: 32px;
  }

  .popover__content--restrict {
    max-height: 400px;
  }

  .popover--small {
    font-size: calc(var(--base-font-size)  - 3px);
    line-height: 1.5;
  }

  .popover--top {
    top: auto;
    bottom: calc(100% + 18px);
  }

  .popover--top:before, .popover--top:after {
    border-color: rgb(var(--root-background)) transparent transparent transparent;
    top: 100%;
    bottom: auto;
  }

  .popover--top:before {
    border-color: rgb(var(--root-border-color)) transparent transparent transparent;
  }

  .popover--left:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 0;
    right: auto;
  }

  .popover--left:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: auto;
    right: 0;
  }

  .popover--left:before, .popover--left:after {
    display: none;
  }

  .popover-button + .popover:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: -28px;
  }

  .popover-button + .popover:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: -28px;
  }

  .select + .popover:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: -15px;
  }

  .select + .popover:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: -15px;
  }
}

.popover__choice-list {
  white-space: nowrap;
}

.popover__choice-item {
  text-align: center;
  width: 100%;
  display: block;
}

.popover__choice-item:not(:first-child) {
  margin-block-start: 7px;
}

.popover__choice-label {
  cursor: pointer;
  position: relative;
}

input:checked + .popover__choice-label:after, .popover__choice-label[aria-current]:after {
  content: "";
  background-color: currentColor;
  width: 12px;
  height: 9px;
  position: absolute;
  top: calc(50% - 4.5px);
  -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEgMy40TDQuNzQ5MzEgN0wxMSAxIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIi8+PC9zdmc+");
  mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEgMy40TDQuNzQ5MzEgN0wxMSAxIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIi8+PC9zdmc+");
  -webkit-mask-size: 12px 9px;
  mask-size: 12px 9px;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
}

input:checked + .popover__choice-label:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
  right: -26px;
}

.popover__choice-label[aria-current]:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
  right: -26px;
}

input:checked + .popover__choice-label:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
  left: -26px;
}

.popover__choice-label[aria-current]:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
  left: -26px;
}

@media screen and (max-width: 999px) {
  .popover__choice-item:not(:first-child) {
    margin-block-start: 16px;
  }
}

@media screen and (min-width: 1000px) {
  input:checked + .popover__choice-label:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
    right: -22px;
  }

  .popover__choice-label[aria-current]:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
    right: -22px;
  }

  input:checked + .popover__choice-label:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
    left: -22px;
  }

  .popover__choice-label[aria-current]:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
    left: -22px;
  }

  .popover__choice-label {
    margin-inline-end: 22px;
  }

  .popover__choice-item {
    text-align: left;
  }

  .popover--small .popover__content {
    padding: 14px 20px;
  }
}

.modal {
  --heading-color: var(--root-heading-color);
  --text-color: var(--root-text-color);
  --background: var(--root-background);
  z-index: 10;
  visibility: hidden;
  font-size: 1rem;
  transition: visibility .25s ease-in-out;
  position: fixed;
}

.modal:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 0;
}

.modal:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 0;
}

.modal[open] {
  visibility: visible;
}

.modal__overlay {
  content: "";
  opacity: 0;
  visibility: hidden;
  background: #000;
  width: 100vw;
  height: 100vh;
  transition: opacity .5s ease-in-out, visibility .5s ease-in-out;
  position: absolute;
  bottom: calc(100% - 10px);
}

.modal__overlay:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 0;
}

.modal__overlay:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 0;
}

.modal[open] > .modal__overlay {
  visibility: visible;
  opacity: .3;
}

.modal__close-button {
  z-index: 1;
  position: absolute;
  top: 24px;
}

.modal__close-button:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 24px;
}

.modal__close-button:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 24px;
}

.modal__content {
  background: rgb(var(--background));
  color: rgb(var(--text-color));
  border-radius: var(--block-border-radius);
  display: flow-root;
  position: relative;
}

@media screen and (max-width: 740px) {
  .modal {
    touch-action: manipulation;
    width: 100vw;
    transition: transform .7s cubic-bezier(.75, 0, .175, 1), visibility .7s cubic-bezier(.75, 0, .175, 1);
    bottom: 0;
    transform: translateY(100%);
  }

  .modal[open] {
    transform: translateY(0);
  }

  .modal__content {
    border-radius: 10px 10px 0 0;
    max-height: 81vh;
    overflow: hidden;
  }
}

@media screen and (min-width: 741px) {
  .modal {
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    display: flex;
    top: 0;
  }

  .modal__overlay {
    position: fixed;
    top: 0;
    bottom: auto;
  }

  .modal__content {
    opacity: 0;
    will-change: transform;
    max-height: calc(100vh - 160px);
    margin: 80px;
    transition: transform .3s cubic-bezier(.75, 0, .175, 1), opacity .3s cubic-bezier(.75, 0, .175, 1);
    overflow: auto;
    transform: scale(.8);
  }

  .modal[open] .modal__content {
    opacity: 1;
    transform: scale(1);
  }
}

.color-swatch-list {
  grid-template-columns: repeat(auto-fit, 40px);
  justify-content: flex-start;
  gap: 7px;
  display: grid;
}

.color-swatch__item {
  cursor: pointer;
  border: 3px solid rgb(var(--section-background, var(--background)));
  border-radius: var(--color-swatch-border-radius);
  -webkit-tap-highlight-color: transparent;
  background-position: center;
  background-size: cover;
  width: 40px;
  height: 40px;
  display: block;
  position: relative;
}

.color-swatch__item:before, .color-swatch__item:after {
  content: "";
  border: 2px solid rgb(var(--section-background, var(--background)));
  pointer-events: none;
  border-radius: inherit;
  position: absolute;
}

.color-swatch__item:before {
  width: calc(100% + 2px);
  height: calc(100% + 2px);
  top: -1px;
  left: -1px;
}

.color-swatch__item:after {
  border-color: rgb(var(--text-color));
  opacity: 0;
  width: calc(100% + 6px);
  height: calc(100% + 6px);
  transition: opacity .2s, transform .2s;
  top: -3px;
  left: -3px;
  transform: scale(.8);
}

.color-swatch--white .color-swatch__item:before {
  box-shadow: 0 0 0 1px rgba(var(--text-color), .3) inset;
}

.color-swatch__radio:checked + .color-swatch__item:after, .color-swatch__item.is-selected:after {
  opacity: 1;
  transform: scale(1);
}

.color-swatch-list--mini {
  grid-template-columns: repeat(auto-fit, 14px);
}

.color-swatch-list--mini .color-swatch__item {
  border-width: 2px;
  width: 14px;
  height: 14px;
}

.color-swatch-list--mini .color-swatch__item:before, .color-swatch-list--mini .color-swatch__item:after {
  border-width: 1px;
}

.color-swatch.is-disabled .color-swatch__item:before {
  background: linear-gradient(to bottom right, transparent calc(50% - 1px), rgb(var(--background)) calc(50% - 1px), rgb(var(--background)) calc(50% + 1px), transparent calc(50% + 1px)) no-repeat;
}

@media screen and not (pointer: fine) {
  .color-swatch[data-tooltip]:before, .color-swatch[data-tooltip]:after {
    display: none;
  }
}

.block-swatch-list {
  flex-wrap: wrap;
  justify-content: flex-start;
  margin: -4px;
  display: flex;
}

.block-swatch__item {
  border: 1px solid rgb(var(--border-color));
  border-radius: var(--button-border-radius);
  background: rgb(var(--background));
  color: rgb(var(--text-color));
  text-align: center;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  min-width: 56px;
  margin: 4px;
  padding: 11px 18px 13px;
  transition: background .2s;
  display: block;
  position: relative;
}

.block-swatch__item:after {
  content: "";
  box-shadow: 0 0 0 1px rgb(var(--text-color)) inset, 0 0 0 1px rgb(var(--text-color));
  border-radius: var(--button-border-radius);
  opacity: 0;
  width: 100%;
  height: 100%;
  transition: opacity .2s, transform .2s;
  position: absolute;
  top: 0;
  transform: scale(.9);
}

.block-swatch__item:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
  left: 0;
}

.block-swatch__item:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
  right: 0;
}

.block-swatch-list--small .block-swatch__item {
  min-width: 44px;
  margin: 4px;
  padding: 4px 12px;
}

.block-swatch__radio:checked + .block-swatch__item, .block-swatch__item.is-selected {
  background: rgb(var(--secondary-background));
}

.block-swatch__radio:checked + .block-swatch__item:after, .block-swatch__item.is-selected:after {
  opacity: 1;
  transform: scale(1);
}

.block-swatch.is-disabled .block-swatch__item {
  color: rgba(var(--text-color), .5);
  background: linear-gradient(to bottom right, transparent calc(50% - 1px), rgb(var(--border-color)) 50%, transparent calc(50% + 1px)) no-repeat;
}

.variant-swatch-list {
  flex-wrap: wrap;
  justify-content: flex-start;
  margin: -6px;
  display: flex;
}

.variant-swatch__item {
  border: 1px solid rgb(var(--border-color));
  text-align: center;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  margin: 6px;
  display: block;
  position: relative;
}

.variant-swatch__image {
  width: 50px;
}

.variant-swatch__item, .variant-swatch__image {
  border-radius: min(var(--block-border-radius), 4px);
}

.variant-swatch__item:after {
  content: "";
  box-shadow: 0 0 0 1px rgb(var(--text-color)) inset, 0 0 0 1px rgb(var(--text-color));
  border-radius: min(var(--block-border-radius), 3px);
  opacity: 0;
  width: 100%;
  height: 100%;
  transition: opacity .2s, transform .2s;
  position: absolute;
  top: 0;
  transform: scale(.9);
}

.variant-swatch__item:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
  left: 0;
}

.variant-swatch__item:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
  right: 0;
}

.variant-swatch__radio:checked + .variant-swatch__item:after, .variant-swatch__item.is-selected:after {
  opacity: 1;
  transform: scale(1);
}

.variant-swatch.is-disabled .variant-swatch__image {
  opacity: .4;
}

.variant-swatch.is-disabled .variant-swatch__item:before {
  content: "";
  background: linear-gradient(to bottom right, transparent calc(50% - 1px), rgb(var(--border-color)) 50%, transparent calc(50% + 1px)) no-repeat;
  z-index: 1;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
}

.variant-swatch.is-disabled .variant-swatch__item:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  left: 0;
}

.variant-swatch.is-disabled .variant-swatch__item:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  right: 0;
}

@media screen and (min-width: 741px) {
  .variant-swatch-list {
    margin: -6px;
  }

  .variant-swatch__item {
    margin: 6px;
  }

  .variant-swatch__image {
    width: 72px;
  }
}

.visual-filter-list {
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 8px;
  display: grid;
}

.visual-filter__item {
  text-align: center;
  cursor: pointer;
  border: 1px solid rgb(var(--border-color));
  border-radius: var(--button-border-radius);
  background: rgb(var(--background));
  flex-direction: column;
  align-items: center;
  row-gap: 8px;
  padding: 8px;
  font-size: 14px;
  line-height: 1.4;
  transition: border-color .15s, background .15s;
  display: flex;
}

.visual-filter__item img {
  width: 32px;
}

:checked + .visual-filter__item {
  background: rgb(var(--secondary-background));
  border-color: currentColor;
}

:is(.color-swatch__radio:focus-visible + label, .block-swatch__radio:focus-visible + label, .variant-swatch__radio:focus-visible + label) {
  outline: auto 5px -webkit-focus-ring-color;
}

.shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__controls-area {
  background: rgb(var(--background));
  border-color: rgba(var(--text-color), .25);
}

.shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button {
  color: rgb(var(--text-color));
}

.shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control:hover {
  color: rgba(var(--text-color), .55);
}

.shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control:active, .shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control.focus-visible:focus {
  color: rgba(var(--text-color), .55);
  background: rgba(var(--text-color), .25);
}

.shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control:not(:last-child):after {
  border-color: rgba(var(--text-color), .25);
}

.shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster {
  background: rgb(var(--background));
  border-color: rgba(var(--text-color), .25);
}

.shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster:hover, .shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster:focus {
  color: rgba(var(--text-color), .55);
}

.model-wrapper {
  padding-block-end: 100%;
  display: block;
  position: relative;
}

.model-wrapper .shopify-model-viewer-ui, .model-wrapper model-viewer {
  opacity: 1;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
}

.model-wrapper .shopify-model-viewer-ui:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 0;
}

.model-wrapper model-viewer:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 0;
}

.model-wrapper .shopify-model-viewer-ui:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 0;
}

.model-wrapper model-viewer:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 0;
}

.video-wrapper {
  display: block;
  position: relative;
}

.video-wrapper:after {
  content: "";
  pointer-events: none;
  padding-block-end: 56.25%;
  display: block;
}

.video-wrapper iframe {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
}

.video-wrapper iframe:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 0;
}

.video-wrapper iframe:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 0;
}

.video-wrapper--cover {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.video-wrapper--cover:after {
  display: none;
}

.video-wrapper--cover iframe {
  width: var(--video-width, 100%);
  height: var(--video-height, 100%);
  position: absolute;
  transform: translate(-50%, -50%);
  top: 50% !important;
  left: 50% !important;
  right: auto !important;
}

@media (min-aspect-ratio: 16 / 9) {
  .video-wrapper--cover iframe {
    --video-height: 56.25vw;
  }
}

@media (max-aspect-ratio: 16 / 9) {
  .video-wrapper--cover iframe {
    --video-width: 177.78vh;
    height: calc(var(--video-height)  + 200px);
  }
}

.video-wrapper--inert iframe {
  pointer-events: none;
}

.video-wrapper--native {
  aspect-ratio: var(--aspect-ratio);
}

.video-wrapper--native video {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.video-wrapper--native:after {
  display: none;
}

@supports not (aspect-ratio: 1) {
  .video-wrapper--native video {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
  }

  .video-wrapper--native:after {
    padding-bottom: calc(100% / var(--aspect-ratio));
    display: block;
  }
}

.video-wrapper__poster {
  cursor: pointer;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
  position: absolute;
  top: 0;
  left: 0;
}

.video-wrapper[autoplay] .video-wrapper__poster {
  cursor: default;
}

.video-wrapper__poster, .video-wrapper iframe {
  opacity: 1;
  visibility: visible;
  transition: opacity .25s ease-in-out, visibility .25s ease-in-out;
}

.video-wrapper__poster-content {
  position: absolute;
}

.video-wrapper__poster-image {
  object-fit: cover;
  object-position: center;
  width: 100%;
  height: 100%;
}

@keyframes playButtonRipple {
  0% {
    box-shadow: 0 0 0 0 rgb(var(--play-button-background)), 0 0 0 0 rgb(var(--play-button-background));
  }

  100% {
    box-shadow: 0 0 0 9px rgba(var(--play-button-background), 0), 0 0 0 18px rgba(var(--play-button-background), 0);
  }
}

.video-wrapper__play-button {
  border-radius: 100%;
}

.video-wrapper__play-button--ripple {
  animation: 1.4s ease-in-out infinite playButtonRipple;
}

.video-wrapper__play-button:not(:only-child) {
  margin-block-end: 32px;
}

@media screen and (min-width: 1000px) {
  @keyframes playButtonRipple {
    0% {
      box-shadow: 0 0 0 0 rgb(var(--play-button-background)), 0 0 0 0 rgb(var(--play-button-background));
    }

    100% {
      box-shadow: 0 0 0 17px rgba(var(--play-button-background), 0), 0 0 0 32px rgba(var(--play-button-background), 0);
    }
  }

  .video-wrapper__play-button:not(:only-child) {
    margin-block-end: 40px;
  }

  .video-wrapper__play-button--large svg {
    width: 104px;
    height: 104px;
  }
}

.product-facet {
  margin-block-start: 24px;
  margin-block-end: 48px;
  display: block;
}

.product-facet__filters-header {
  border-bottom: 1px solid rgb(var(--border-color));
  padding-block-end: 24px;
}

.product-facet__filters:not(.drawer) {
  padding-block-end: 24px;
  display: block;
}

.product-facet__active-list {
  margin-block-start: -6px;
  margin-block-end: 18px;
}

.product-facet__filter-item + .product-facet__filter-item {
  border-top: 1px solid rgb(var(--border-color));
}

.product-facet__filter-item .collapsible__content {
  padding-inline-start: 8px;
}

.product-facet__filter-item:not(:last-child) .collapsible__content {
  margin-block-end: 25px;
}

.product-facet__submit {
  margin-block-start: 40px;
}

.product-facet__active-count {
  margin-inline-start: 8px;
}

.product-facet__sort-by-title {
  padding-inline-end: 7px;
}

.product-facet__product-list {
  margin-block-start: calc(var(--container-gutter) / 2);
}

.product-facet__meta-bar {
  justify-content: center;
  align-items: flex-start;
  margin-block-end: 16px;
  display: flex;
}

.product-facet__meta-bar-item {
  align-items: center;
  display: flex;
}

.product-facet__meta-bar-item .icon--filters, .mobile-toolbar__item .icon--filters {
  margin-inline-end: 13px;
}

@media screen and (max-width: 740px) {
  .product-facet__active-list {
    margin-inline-start: -18px;
    margin-inline-end: -18px;
  }

  .mobile-toolbar__item--filters.has-filters .mobile-toolbar__item-label:after {
    content: "";
    background: currentColor;
    border-radius: 6px;
    width: 6px;
    height: 6px;
    position: absolute;
  }
}

@media screen and (max-width: 999px) {
  .product-facet__filters:not(.drawer) {
    display: none;
  }
}

@media screen and (min-width: 741px) {
  .product-facet {
    margin-block-start: 40px;
    margin-block-end: 80px;
  }

  .product-facet__active-list {
    margin-block-start: 18px;
    margin-block-end: 0;
  }

  .drawer .product-facet__active-list {
    margin-block-start: 26px;
  }

  .product-facet__meta-bar {
    margin-block-end: 24px;
  }

  .product-facet__meta-bar-item--filter {
    margin-inline-end: 44px;
  }
}

@media screen and (min-width: 1000px) {
  .product-facet {
    justify-content: flex-start;
    display: flex;
  }

  .product-facet__aside {
    flex: none;
    width: 230px;
    margin-inline-end: 40px;
  }

  .product-facet__aside-inner {
    top: calc(var(--header-height, 0px) * var(--enable-sticky-header)  + var(--announcement-bar-height, 0px) * var(--enable-sticky-announcement-bar, 0)  + 24px);
    display: block;
    position: sticky;
  }

  .product-facet__main {
    flex: 1 0 0;
  }

  .product-facet__filters:not(.drawer) .drawer__content {
    padding: 0;
    overflow: visible;
  }

  .product-facet__meta-bar {
    justify-content: flex-start;
  }

  .product-facet__meta-bar-item:last-child {
    margin-inline-start: auto;
  }
}

@media screen and (min-width: 1200px) {
  .product-facet__aside {
    width: 265px;
  }
}

.account__block-list {
  row-gap: 24px;
  display: grid;
}

.account__block-item:empty {
  display: none;
}

.account__back-button {
  z-index: 1;
  justify-content: center;
  align-items: center;
  margin-block-start: 30px;
  display: inline-flex;
}

.account__back-button svg {
  margin-inline-end: 14px;
}

@media screen and (min-width: 741px) {
  .account__block-list {
    row-gap: 32px;
  }
}

@media screen and (min-width: 1000px) {
  .account__back-button {
    margin-block-start: 48px;
    position: absolute;
  }

  .account__back-button:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: var(--container-outer-width);
  }

  .account__back-button:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: var(--container-outer-width);
  }
}

.account__order-table-item:hover .link--animated:after {
  transform-origin: var(--transform-origin-start);
  transform: scale(1);
}

.account__order-list-item + .account__order-list-item {
  border-top: 1px solid rgb(var(--border-color));
  margin-block-start: 24px;
  padding-block-start: 24px;
}

.account__order-item-info {
  grid-template-columns: 1fr 1fr;
  gap: 24px 64px;
  margin-block-start: 16px;
  margin-block-end: 16px;
  display: grid;
}

.account__order-item-block .heading {
  margin-block-end: 8px;
}

.account__order-date {
  margin-block-start: -6px;
  display: block;
}

@media screen and (max-width: 740px) {
  .account--order .page-header {
    text-align: start;
  }
}

@media screen and (min-width: 741px) {
  .account__order-date {
    margin-block-start: -18px;
  }

  .account__order-addresses .account__addresses-list {
    margin-block-start: 24px;
  }
}

.account__addresses-list {
  border: 1px solid rgb(var(--border-color));
  display: grid;
}

.account__address {
  flex-direction: column;
  min-height: 200px;
  padding: 24px;
  display: flex;
}

.account__address--auto {
  min-height: 0 !important;
}

.account__address:not(:first-child) {
  border-block-start: 1px solid rgb(var(--border-color));
}

.account__address--empty {
  justify-content: center;
  align-items: center;
}

.account__address--empty svg {
  margin-block-end: 16px;
}

.account__address-details {
  margin-block-start: 10px;
}

.account__address--empty {
  background: rgb(var(--secondary-background));
}

.account__address-actions {
  grid-auto-flow: column;
  justify-content: flex-start;
  gap: 20px;
  margin-block-start: auto;
  padding-block-start: 10px;
  display: grid;
}

@media screen and (min-width: 741px) {
  .account__addresses-list {
    border: none;
    grid-template-columns: repeat(auto-fit, 50%);
    justify-content: center;
  }

  .account__address {
    border-block: 1px solid rgb(var(--border-color));
    border-inline-end: 1px solid rgb(var(--border-color));
  }
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .account__address:nth-child(2n) ~ .account__address {
    border-block-start: none;
  }

  .account__address:nth-child(odd) {
    border-inline-start: 1px solid rgb(var(--border-color));
  }
}

@media screen and (min-width: 1000px) {
  .account__addresses-list:not(.account__addresses-list--wide) {
    grid-template-columns: repeat(auto-fit, 33.3333%);
  }

  .account__address:nth-child(3n) ~ .account__address {
    border-block-start: none;
  }

  .account__address:nth-child(3n+1) {
    border-inline-start: 1px solid rgb(var(--border-color));
  }
}

.shopify-section--header ~ .shopify-section--announcement-bar {
  top: calc(var(--enable-sticky-announcement-bar, 0) * var(--enable-sticky-header, 0) * var(--header-height, 0px)) !important;
}

.announcement-bar {
  background: rgb(var(--section-background));
  justify-content: center;
  align-items: center;
  padding-inline-start: 20px;
  padding-inline-end: 20px;
  display: flex;
}

.announcement-bar--multiple {
  justify-content: space-between;
}

.announcement-bar__list {
  padding-inline-start: 10px;
  padding-inline-end: 10px;
}

.announcement-bar__item {
  display: block;
}

.announcement-bar__item[hidden] {
  visibility: hidden;
  height: 0;
}

.announcement-bar__message {
  text-align: center;
  padding-block-start: 15px;
  padding-block-end: 15px;
}

.announcement-bar__message .link {
  margin-inline-start: 4px;
}

.announcement-bar__close-button {
  position: absolute;
  top: var(--container-gutter);
}

.announcement-bar__close-button:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: var(--container-gutter);
}

.announcement-bar__close-button:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: var(--container-gutter);
}

.announcement-bar__content {
  z-index: 5;
  display: block;
}

.announcement-bar__content[hidden] {
  visibility: hidden;
}

.announcement-bar__content-inner {
  background: rgb(var(--section-background));
}

.announcement-bar__content-overlay {
  content: "";
  opacity: .3;
  z-index: -1;
  background: #000;
  width: 100%;
  height: 100vh;
  transition: opacity .5s ease-in-out;
  position: absolute;
  bottom: calc(100% - 10px);
}

.announcement-bar__content-overlay:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 0;
}

.announcement-bar__content-overlay:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 0;
}

.announcement-bar__content[hidden] .announcement-bar__content-overlay {
  opacity: 0;
}

@media screen and (max-width: 740px) {
  .announcement-bar__content {
    width: 100%;
    transition: visibility .6s linear, opacity .6s cubic-bezier(.75, 0, .175, 1), transform .6s cubic-bezier(.75, 0, .175, 1);
    position: fixed;
    bottom: 0;
  }

  .announcement-bar__content:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 0;
  }

  .announcement-bar__content:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 0;
  }

  .announcement-bar__content[hidden] {
    transform: translateY(100%);
  }

  .announcement-bar__content-inner {
    border-radius: 10px 10px 0 0;
    flex-direction: column;
    max-height: 81vh;
    display: flex;
    overflow: hidden;
  }

  .announcement-bar__content-image {
    flex: none;
  }

  .announcement-bar__content-text-wrapper {
    text-align: center;
    padding: 32px 48px;
    overflow: auto;
  }

  .announcement-bar__content.has-image .announcement-bar__close-button {
    color: #fff;
  }

  @supports (padding: max(0px)) {
    .announcement-bar__content-text-wrapper {
      padding-block-end: max(32px, env(safe-area-inset-bottom, 0px)  + 32px);
    }
  }
}

@media screen and (min-width: 741px) {
  .announcement-bar {
    padding-inline: var(--container-gutter);
    justify-content: center;
    position: relative;
  }

  .announcement-bar__list {
    max-width: var(--container-max-width);
    padding-inline-start: 40px;
    padding-inline-end: 40px;
  }

  .announcement-bar__content {
    box-shadow: 0 -1px rgba(var(--text-color), .2);
    width: 100%;
    transition: visibility .5s linear, box-shadow .5s ease-in-out;
    position: absolute;
    top: 100%;
  }

  .announcement-bar__content:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 0;
  }

  .announcement-bar__content:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 0;
  }

  .announcement-bar__content[hidden] {
    box-shadow: none;
  }

  .announcement-bar__content-overflow {
    height: calc(100vh - var(--announcement-bar-height));
    overflow: hidden;
  }

  .announcement-bar__content-inner {
    background: rgb(var(--section-background));
    opacity: 1;
    grid-auto-columns: 1fr;
    align-items: center;
    max-height: 80vh;
    transition: opacity .5s cubic-bezier(.75, 0, .175, 1), transform .5s cubic-bezier(.75, 0, .175, 1);
    display: grid;
    overflow: hidden;
    transform: translateY(0);
  }

  .announcement-bar__content[hidden] .announcement-bar__content-inner {
    opacity: 0;
    transform: translateY(-100%);
  }

  .announcement-bar__content-overlay {
    top: 0;
    bottom: auto;
  }

  .announcement-bar__content-image, .announcement-bar__content-text-wrapper {
    max-height: inherit;
    grid-row: 1;
  }

  .announcement-bar__content-image {
    object-fit: cover;
    object-position: center;
    height: 100%;
    max-height: max-content;
  }

  .announcement-bar__content-text-wrapper {
    padding: 80px var(--container-gutter);
    overscroll-behavior: contain;
    overflow: auto;
  }

  .announcement-bar__content-text {
    text-align: center;
    max-width: 420px;
    margin-inline-start: auto;
    margin-inline-end: auto;
  }

  .announcement-bar__message {
    min-width: 495px;
    max-width: 1000px;
  }
}

.article__header {
  background: rgb(var(--section-header-background));
  display: block;
  position: relative;
}

.article__header-content {
  padding: 40px var(--container-gutter);
  color: rgb(var(--text-color));
  margin-inline-start: auto;
  margin-inline-end: auto;
}

.article__image-wrapper {
  overflow: hidden;
}

@media screen and (min-width: 1000px) {
  .article__header {
    flex-direction: row-reverse;
    align-items: center;
    display: flex;
  }

  .article__header-content {
    padding-block-start: 72px;
    padding-block-end: 72px;
  }

  .article__header .breadcrumb:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: var(--container-outer-margin);
  }

  .article__header .breadcrumb:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: var(--container-outer-margin);
  }

  .article__image-wrapper {
    flex: none;
    align-self: stretch;
  }

  .article__image-wrapper--tall {
    width: 37.5%;
  }

  .article__image-wrapper--square {
    width: 50%;
  }

  .article__image {
    object-fit: cover;
    object-position: center;
    width: 100%;
    height: 100%;
  }

  .article__image-wrapper--tall + .article__header-content {
    padding-inline-end: 100px;
  }

  .article__header-content:only-child {
    text-align: center;
    max-width: 668px;
    padding-inline-start: 0;
    padding-inline-end: 0;
  }
}

@media screen and (min-width: 1400px) {
  .article__header-content {
    padding-inline-start: calc(var(--container-outer-margin)  + var(--grid-column-width)  + var(--grid-gap));
    padding-inline-end: 70px;
  }

  .article__image-wrapper--tall + .article__header-content {
    padding-inline-end: 160px;
  }
}

.article__nav {
  border-bottom: 1px solid rgb(var(--border-color));
  background: rgb(var(--background));
  z-index: 1;
  opacity: 0;
  visibility: hidden;
  width: 100%;
  transition: transform .3s, opacity .3s, visibility .3s;
  display: block;
  position: fixed;
  top: calc(var(--enable-sticky-header) * var(--header-height, 0px)  + var(--announcement-bar-height, 0px) * var(--enable-sticky-announcement-bar, 0));
  transform: translateY(-100%);
}

.article__nav.is-visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.article__nav:after {
  content: "";
  transform-origin: var(--transform-origin-start);
  transform: scaleX(var(--transform));
  background: currentColor;
  width: 100%;
  height: 2px;
  position: absolute;
  bottom: 0;
  box-shadow: 0 1px;
}

.article__nav:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
  left: 0;
}

.article__nav:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
  right: 0;
}

.article__nav-wrapper {
  align-items: center;
  padding: 18px 0;
  display: flex;
  position: relative;
}

.article__nav-item {
  align-items: center;
  display: flex;
}

.article__nav-item-title {
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
  max-width: 200px;
  overflow: hidden;
}

.article__nav-item-label {
  margin-inline-end: 10px;
}

.article__nav-item--next .article__nav-arrow {
  margin-inline-start: 20px;
}

.article__nav-item--prev .article__nav-arrow {
  margin-inline-end: 20px;
}

.article__reading-time {
  flex-shrink: 0;
}

@media screen and (max-width: 740px) {
  .article__nav-item {
    will-change: transform;
    justify-content: center;
    width: 100%;
    transition: transform .2s ease-in-out, opacity .2s ease-in-out, visibility .2s ease-in-out;
  }

  .article__nav-item--next {
    opacity: 0;
    visibility: hidden;
    align-items: baseline;
    transition-delay: 0s;
    position: absolute;
    transform: translateY(-6px);
  }

  .article__nav-item--next:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 0;
  }

  .article__nav-item--next:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 0;
  }

  .article__nav-item--current {
    transition-delay: .15s;
  }

  .article__nav-arrow {
    align-self: center;
    margin-inline-start: auto;
  }

  .article__nav--show-next .article__nav-item--current {
    opacity: 0;
    transition-delay: 0s;
    transform: translateY(6px);
  }

  .article__nav--show-next .article__nav-item--next {
    opacity: 1;
    visibility: visible;
    transition-delay: .15s;
    transform: translateX(0);
  }
}

@media screen and (min-width: 741px) {
  .article__nav-wrapper {
    justify-content: center;
  }

  .article__nav-item--prev, .article__nav-item--next {
    position: absolute;
  }

  .article__nav-item--prev:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 0;
  }

  .article__nav-item--prev:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 0;
  }

  .article__nav-item--next:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 0;
  }

  .article__nav-item--next:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 0;
  }

  .article__nav-item-title {
    max-width: 225px;
  }

  .article__nav-item--prev .article__nav-item-title, .article__nav-item--next .article__nav-item-title {
    opacity: 0;
    will-change: transform;
    transition: transform .2s ease-in-out, opacity .2s ease-in-out;
    transform: translateY(-6px);
  }

  .article__nav-item:hover .article__nav-item-title, .article__nav-item:focus .article__nav-item-title {
    opacity: 1;
    transform: translateY(0);
  }
}

@media screen and (min-width: 1000px) {
  .article__nav-wrapper {
    padding: 27px 0;
  }
}

@media screen and (min-width: 1200px) {
  .article__nav-item-title {
    max-width: 300px;
  }
}

.article {
  margin: 40px 0;
}

.article__inner {
  flex-direction: column-reverse;
  justify-content: center;
  display: flex;
  position: relative;
}

.article__info {
  border-top: 1px solid rgb(var(--border-color));
  gap: 14px;
  width: 100%;
  margin-block-start: 40px;
  padding-block-start: 24px;
  display: grid;
}

.article__meta-item + .article__meta-item:before {
  content: "";
  vertical-align: 2px;
  background: rgb(var(--text-color));
  width: 4px;
  height: 4px;
  margin-inline-start: 12px;
  margin-inline-end: 12px;
  display: inline-block;
}

.article__tags {
  align-items: center;
  display: flex;
}

.article__tags-item {
  margin-inline-end: 12px;
  display: inline-block;
}

.article__tags-label {
  margin-inline-end: 15px;
}

.article__share {
  align-items: center;
  width: max-content;
  display: flex;
}

.article__info {
  max-width: max-content;
}

.article__share-button-list {
  grid-auto-flow: column;
  justify-content: flex-start;
  align-items: center;
  gap: 26px;
  margin-inline-start: 15px;
  padding: 0;
  list-style: none;
  display: grid;
}

.article__comments-count {
  vertical-align: top;
  display: inline-flex;
}

.article__comments-count svg {
  margin-inline-end: 8px;
  position: relative;
  top: 1px;
}

@media screen and (min-width: 1000px) {
  .article {
    margin-block-start: 80px;
    margin-block-end: 80px;
  }

  .article__inner {
    align-items: center;
  }

  .article__content, .article__info {
    flex-grow: 1;
    width: 100%;
    max-width: 668px;
  }
}

@media screen and (min-width: 1200px) {
  .article__inner {
    flex-direction: column;
    min-height: 200px;
  }

  .article__info {
    gap: 28px;
    width: 170px;
    margin-block-start: 0;
    padding-block-start: 30px;
    position: absolute;
    top: 0;
  }

  .article__info:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 0;
  }

  .article__info:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 0;
  }

  .article__meta {
    justify-content: flex-start;
    gap: 8px;
    display: grid;
  }

  .article__meta-item:before {
    display: none !important;
  }

  .article__tags-label {
    margin-block-end: 10px;
  }

  .article__share, .article__tags {
    display: block;
  }

  .article__share-label {
    margin-block-end: 16px;
    display: block;
  }

  .article__share-button-list {
    margin-inline-start: 6px;
  }
}

@media screen and (min-width: 1400px) {
  .article__inner {
    margin-inline: calc(var(--grid-column-width)  + var(--grid-gap));
  }

  .article__info {
    width: 185px;
  }
}

.article__prev-next {
  background: rgb(var(--secondary-background));
}

@media screen and (min-width: 741px) {
  .article__prev-next .article-list {
    grid-template-columns: none;
    grid-auto-columns: 310px;
    justify-content: center;
  }

  .article__prev-next .article-item {
    width: auto !important;
  }
}

.article__comment-list-heading {
  margin-block-end: 30px;
}

.article-comment {
  background: rgb(var(--secondary-background));
  border-radius: var(--block-border-radius-reduced);
  padding: 24px;
}

.article-comment + .article-comment {
  margin-block-start: 16px;
}

.article-comment__meta {
  align-items: center;
  display: flex;
}

.article-comment__gravatar {
  border-radius: 100%;
  width: 40px;
  margin-inline-end: 16px;
}

.article-comment__author {
  margin-block-end: 0;
}

.article-comment__date {
  margin-block-start: 2px;
  margin-block-end: 4px;
  display: block;
}

.article-comment__content {
  margin-block-start: 15px;
}

.article__comment-list + .article__comment-form {
  margin-block-start: 48px;
}

.article__comment-form-title {
  margin-block-start: 0;
}

@media screen and (min-width: 741px) {
  .article-comment {
    padding: 32px;
  }

  .article-comment__gravatar {
    align-self: flex-start;
    width: 48px;
    margin-inline-end: 21px;
  }

  .article__comment-list-heading {
    margin-block-end: 34px;
  }

  .article__comment-list + .article__comment-form {
    margin-block-start: 64px;
  }
}

@media screen and (min-width: 1000px) {
  .article__comment-box {
    max-width: 748px;
    margin-inline-start: auto;
    margin-inline-end: auto;
  }
}

.article-list {
  --article-list-row-gap: 40px;
  --article-list-column-gap: var(--container-gutter);
  gap: var(--article-list-row-gap) var(--article-list-column-gap);
  display: grid;
}

.article-list--scrollable {
  grid-auto-flow: column;
}

.article-item {
  width: 100%;
}

.article-item__image-container {
  border-radius: var(--block-border-radius-reduced);
  z-index: 0;
  margin-block-end: 20px;
  display: block;
  position: relative;
  overflow: hidden;
}

.article-item__arrow {
  opacity: 0;
  visibility: hidden;
  border: none;
  transition: opacity .15s ease-in-out, visibility .15s ease-in-out, transform .15s ease-in-out;
  position: absolute;
  bottom: 20px;
  transform: scale(.5);
}

.article-item__arrow:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 20px;
}

.article-item__arrow:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 20px;
}

.article-item--horizontal {
  align-items: center;
  display: flex;
}

.article-item--horizontal .article-item__image-container {
  border-radius: calc(var(--block-border-radius-reduced) / 2);
  z-index: 0;
}

.article-item--horizontal .article-item__arrow {
  bottom: 12px;
}

.article-item--horizontal .article-item__arrow:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 12px;
}

.article-item--horizontal .article-item__arrow:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 12px;
}

.article-item--featured .article-item__arrow {
  bottom: 32px;
}

.article-item--featured .article-item__arrow:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 32px;
}

.article-item--featured .article-item__arrow:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 32px;
}

.article-item__category {
  color: rgba(var(--text-color), .7);
  width: max-content;
}

.article-item__excerpt {
  margin-block-start: -4px;
}

.article-list--section .article-item:only-child {
  max-width: 668px;
}

@media screen and (max-width: 999px) {
  .article-list--scrollable .article-item:not(:only-child) {
    scroll-snap-align: center;
    scroll-snap-stop: always;
    width: 81vw;
  }
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .article-list--has-four {
    grid-template-columns: repeat(2, 1fr);
  }

  .article-list--scrollable .article-item:not(:only-child) {
    width: 52vw;
  }
}

@media screen and (min-width: 741px) {
  .article-list--stacked {
    grid-template-columns: repeat(auto-fit, calc(100% / 3 - var(--article-list-column-gap) * (2 / 3)));
    justify-content: safe center;
  }
}

@media screen and (min-width: 1000px) {
  .article-list {
    --article-list-row-gap: 48px;
    --article-list-column-gap: 48px;
  }

  .article-list + .pagination {
    margin-block-start: 64px;
  }

  .article-list--section {
    grid-template-columns: repeat(auto-fit, calc(100% / 3 - var(--article-list-column-gap) * (2 / 3)));
    justify-content: safe center;
  }

  .article-item__image-container {
    margin-block-end: 24px;
  }

  .article-item__excerpt {
    margin-block-start: -8px;
  }

  .article-list--collage {
    grid-template-columns: 1.37731fr 1fr;
    column-gap: 48px;
  }

  .article-item--featured .article-item__image-container {
    margin-block-end: 32px;
  }

  .article-list__secondary-list {
    grid-auto-rows: max-content;
    row-gap: 48px;
    display: grid;
  }

  .article-list__secondary-list .article-item__image-container {
    flex: none;
    width: 42%;
    margin-block-end: 0;
    margin-inline-end: 32px;
  }
}

@media screen and (min-width: 1200px) {
  .article-list {
    margin-inline: calc(var(--grid-column-width)  + var(--grid-gap));
  }

  .article-list--collage {
    column-gap: 70px;
  }
}

@media screen and (pointer: fine) {
  .article-item:hover .article-item__arrow {
    visibility: visible;
    opacity: 1;
    transform: scale(1);
  }
}

.checkout-button {
  justify-content: center;
  align-items: center;
  display: flex;
  position: relative;
}

.checkout-button__lock {
  position: absolute;
}

.checkout-button__lock:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 18px;
}

.checkout-button__lock:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 18px;
}

.checkout-button .square-separator {
  margin-inline-start: 11px;
  margin-inline-end: 12px;
}

@media screen and (min-width: 741px) {
  .checkout-button__lock:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 20px;
  }

  .checkout-button__lock:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 20px;
  }
}

.discount-badge {
  font-family: var(--text-font-family);
  font-style: var(--text-font-style);
  font-weight: var(--text-font-bold-weight);
  text-transform: uppercase;
  letter-spacing: .5px;
  background: rgb(var(--heading-color));
  color: rgb(var(--background));
  align-items: center;
  padding: 3px 6px;
  font-size: 11px;
  display: flex;
}

.discount-badge svg {
  margin-inline-end: 8px;
  position: relative;
}

.shipping-bar {
  text-align: center;
  width: 100%;
  margin-block-start: 16px;
  margin-block-end: 4px;
  margin-inline-start: auto;
  margin-inline-end: auto;
  display: block;
}

.shipping-bar__text {
  display: block;
}

.shipping-bar__progress {
  border: 2px solid;
  border-radius: 4px;
  height: 7px;
  margin-block-start: 6px;
  display: block;
  position: relative;
}

.shipping-bar__progress:after {
  content: "";
  transform: scaleX(var(--progress));
  transform-origin: var(--transform-origin-start);
  background: currentColor;
  transition: transform .2s;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.shipping-bar--large {
  max-width: 392px;
}

@media screen and (min-width: 741px) {
  .shipping-bar {
    margin-block-end: 8px;
  }

  .shipping-bar__progress {
    height: 8px;
    margin-block-start: 8px;
  }

  .shipping-bar--large .shipping-bar__progress {
    margin-block-start: 16px;
  }
}

.line-item {
  display: flow-root;
}

.line-item__content-wrapper {
  margin-block-start: 20px;
  display: flex;
  position: relative;
}

.line-item--centered .line-item__content-wrapper {
  align-items: center;
}

.line-item__image-wrapper {
  flex: none;
  align-self: flex-start;
  width: 80px;
  margin-inline-end: 24px;
  display: block;
  position: relative;
}

.line-item__image {
  border-radius: min(var(--block-border-radius), 4px);
}

.line-item__loader {
  background: rgb(var(--root-background));
  color: rgb(var(--root-text-color));
  border-radius: 32px;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  transition: opacity .2s ease-in-out, transform .2s ease-in-out, visibility .2s ease-in-out;
  display: flex;
  position: absolute;
  top: calc(50% - 16px);
}

.line-item__loader:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: calc(50% - 16px);
}

.line-item__loader:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: calc(50% - 16px);
}

.line-item__loader[hidden] {
  opacity: 0;
  visibility: hidden;
  transform: scale(.4);
}

.line-item__quantity {
  margin-block-start: 8px;
  display: block;
}

.line-item__discount-list + .line-item__quantity {
  margin-block-start: 12px;
}

.line-item__remove-button {
  margin-inline-start: 12px;
}

.line-item__discount-list {
  flex-direction: column;
  align-items: flex-start;
  margin-block-start: 8px;
  display: flex;
}

.line-item__discount-badge:not(:last-child) {
  margin-block-end: 4px;
}

@media screen and (min-width: 741px) {
  .line-item__content-wrapper {
    margin-block-start: 24px;
  }

  .line-item__image-wrapper {
    width: 92px;
  }

  .line-item__quantity {
    vertical-align: top;
    margin-block-start: 14px;
  }

  .line-item__price-list-container {
    text-align: right;
    vertical-align: top;
    flex-shrink: 0;
    margin-inline-start: auto;
    padding-inline-start: 30px;
    line-height: 1.5;
  }

  .line-item__quantity--block .line-item__remove-button {
    margin-block-start: 10px;
    margin-inline-start: 0;
    display: block;
  }

  .line-item__discount-list {
    margin-block-start: 14px;
  }
}

@media screen and (min-width: 741px) {
  .line-item__fulfillment {
    margin-inline-start: 116px;
  }
}

.line-item-table {
  margin-block-end: 40px;
}

.line-item-table .line-item {
  display: table-row;
}

.line-item-table .line-item__content-wrapper {
  margin-block-start: 0;
}

@media screen and (max-width: 740px) {
  .line-item-table {
    table-layout: fixed;
  }

  .line-item-table__list .line-item:first-child .line-item__product {
    padding-block-start: 0;
  }

  .line-item-table__list .line-item__product {
    width: 100%;
    padding-inline-end: 0;
  }

  .line-item-table__footer {
    display: table-row;
  }

  .line-item-table__footer td:nth-child(2) {
    width: 100%;
    padding-inline-start: 0;
  }
}

@media screen and (min-width: 741px) {
  .line-item-table__list .line-item__quantity {
    margin-block-start: 0;
  }
}

.mini-cart__discount-list {
  gap: 10px;
  margin-block-end: 8px;
  display: grid;
}

.mini-cart__discount {
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.mini-cart__discount-badge {
  margin-inline-end: 14px;
}

.mini-cart__actions {
  flex-wrap: wrap;
  justify-content: space-between;
  margin-block-end: 14px;
  display: flex;
}

.mini-cart__order-note {
  background: rgb(var(--background));
  padding: var(--container-gutter);
  z-index: 1;
  visibility: hidden;
  opacity: 0;
  width: 100%;
  transition: visibility .25s ease-in-out, opacity .25s ease-in-out, transform .25s ease-in-out;
  display: block;
  position: absolute;
  bottom: 0;
  transform: translateY(100%);
}

.mini-cart__order-note:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 0;
}

.mini-cart__order-note:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 0;
}

.mini-cart__order-note[open] {
  visibility: visible;
  opacity: 1;
  transform: translateY(0);
}

.mini-cart__order-note-title {
  margin-block-end: 24px;
}

@media screen and (max-width: 740px) {
  .mini-cart__actions {
    flex-direction: column;
    align-items: flex-start;
  }

  .mini-cart__actions > :first-child:not(:only-child) {
    margin-block-end: 7px;
  }

  .mini-cart__order-note {
    width: 100vw;
  }

  .mini-cart__order-note:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: -11vw;
  }

  .mini-cart__order-note:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: -11vw;
  }

  @supports (padding: max(0px)) {
    .mini-cart__order-note {
      padding-block-end: max(24px, env(safe-area-inset-bottom, 0px)  + 24px);
    }
  }
}

@media screen and (min-width: 741px) {
  .mini-cart__drawer-footer {
    padding-block-end: var(--container-gutter);
  }

  .mini-cart__discount {
    justify-content: flex-end;
  }

  .mini-cart__actions {
    margin-block-end: 26px;
  }
}

.mini-cart__recommendations:not([hidden]) {
  display: block;
}

.mini-cart__recommendations-inner {
  margin-block-start: 24px;
  margin-inline: calc(-1 * var(--container-gutter));
  padding: 16px var(--container-gutter);
  background: rgb(var(--secondary-background));
}

.mini-cart__recommendations-heading {
  margin-block-start: 0 !important;
}

.mini-cart__recommendations .product-item-meta__title {
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 100%;
  overflow: hidden;
}

@media screen and (max-width: 999px) {
  .mini-cart__recommendations-list {
    grid-gap: var(--grid-gap);
    grid-auto-columns: minmax(64vw, 1fr);
    grid-auto-flow: column;
    display: grid;
  }

  .mini-cart__recommendations .product-item {
    scroll-snap-align: start;
    scroll-snap-stop: always;
    scroll-margin: var(--container-gutter);
    flex-direction: row;
    align-items: center;
    display: flex;
  }

  .mini-cart__recommendations .product-item__image-wrapper {
    flex: none;
    width: 65px;
    margin-block-start: 0;
    margin-block-end: 0;
    margin-inline-start: 0;
    margin-inline-end: 24px;
  }

  .mini-cart__recommendations .product-item__info {
    text-align: start;
    min-width: 0;
  }

  .mini-cart__recommendations .price-list {
    justify-content: start;
  }
}

/*
@media screen and (min-width: 1000px) {
  .mini-cart__recommendations {
    text-align: center;
    width: 240px;
    height: 100%;
    position: absolute;
    top: 0;
    overflow: hidden;
  }

  .mini-cart__recommendations:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 100%;
  }

  .mini-cart__recommendations:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 100%;
  }

  .mini-cart__recommendations-inner {
    scrollbar-width: thin;
    box-shadow: -10px 0 24px 4px rgb(var(--text-color), .05) inset;
    height: 100%;
    margin: 0;
    padding-block-start: 35px;
    padding-block-end: 35px;
    transition: transform .25s ease-in;
    overflow: hidden auto;
  }

  .mini-cart:not([open]) .mini-cart__recommendations-inner {
    transform: translateX(100%);
  }

  .mini-cart__recommendations .product-item {
    margin-block-start: 40px;
  }

  .mini-cart__recommendations .product-item__image-wrapper {
    flex: none;
    width: 92px;
    margin-block-start: 0;
    margin-block-end: 24px;
    margin-inline-start: auto;
    margin-inline-end: auto;
  }

  .mini-cart__recommendations .spinner {
    height: 100%;
    display: flex;
  }
}
*/

.cart__recap {
  padding: var(--container-gutter);
  background: rgb(var(--secondary-background));
  border-radius: var(--block-border-radius-reduced);
}

.cart__recap-block {
  gap: 10px;
  margin-block-end: 10px;
  display: grid;
}

.cart__recap-block > * {
  margin-block-start: 0;
  margin-block-end: 0;
}

.cart__recap-note {
  margin-block-end: 24px;
}

.cart__total-container, .cart__discount {
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
  display: flex;
}

.cart__discount-list {
  gap: 10px;
  display: grid;
}

.cart__discount-badge {
  align-self: stretch;
}

.cart__order-note {
  padding-block-start: 14px;
}

.cart__checkout-button:not(:only-child) {
  margin-block-start: 24px;
}

.cart__payment-methods {
  text-align: center;
  margin-block-start: 24px;
}

.cart__payment-methods-label {
  margin-block-end: 16px;
  display: block;
}

@media screen and (max-width: 999px) {
  .cart__aside {
    margin-block-start: 24px;
  }
}

@media screen and (min-width: 1000px) {
  .cart {
    grid-template-columns: 1fr 300px;
    gap: 40px;
    display: grid;
  }

  .cart__aside-inner {
    top: calc(var(--header-height, 0px) * var(--enable-sticky-header)  + var(--announcement-bar-height, 0px) * var(--enable-sticky-announcement-bar, 0)  + 24px);
    display: block;
    position: sticky;
  }
}

@media screen and (min-width: 1200px) {
  .cart {
    grid-template-columns: 1fr 390px;
    gap: 70px;
  }
}

.shipping-estimator {
  border: 1px solid rgb(var(--border-color));
  border-radius: var(--block-border-radius-reduced);
}

.shipping-estimator__toggle-button, .shipping-estimator__form {
  padding: 24px;
}

.shipping-estimator__form {
  display: block;
  padding-block-start: 0 !important;
}

.shipping-estimator__results {
  margin-block-start: 24px;
}

@media screen and (min-width: 741px) {
  .shipping-estimator__form .input-row .input:first-child, .shipping-estimator__form .input-row .input:nth-child(2) {
    grid-column: span 2;
  }

  .shipping-estimator__toggle-button, .shipping-estimator__form {
    padding: 32px;
  }
}

.product-facet__main .promotion-block-list {
  --promotion-block-gutter: 24px;
}

.product-facet__main .promotion-block-list--top {
  margin-block-end: 20px;
}

.product-facet__main .promotion-block-list--bottom {
  margin-block-start: 36px;
}

@media screen and (max-width: 740px) {
  .product-facet__main .promotion-block-list {
    --promotion-block-gutter: 12px;
    margin-inline: calc(-1 * (var(--container-gutter)  - var(--promotion-block-gutter) / 2));
  }
}

@media screen and (min-width: 741px) {
  .product-facet__main .promotion-block-list--bottom {
    margin-block-start: 60px;
  }
}

.section__header + .contact__form {
  margin-block-start: 24px;
}

.contact__text-list {
  border: 1px solid rgba(var(--text-color), .15);
  padding: 32px;
}

.contact__text-item + .contact__text-item {
  margin-block-start: 32px;
}

@media screen and (max-width: 999px) {
  .contact__form, .contact__aside {
    max-width: 460px;
    margin-inline-start: auto;
    margin-inline-end: auto;
  }

  .contact__aside {
    margin-block-start: 40px;
  }
}

@media screen and (min-width: 741px) {
  .contact__text-list {
    grid-template-columns: repeat(auto-fit, minmax(10px, 1fr));
    gap: 32px;
    display: grid;
  }

  .contact__text-item {
    margin-block-start: 0 !important;
  }
}

@media screen and (min-width: 1000px) {
  .contact {
    flex-direction: row-reverse;
    justify-content: center;
    display: flex;
  }

  .contact__main {
    flex: 1 0 auto;
    max-width: 460px;
  }

  .contact__main:only-child {
    flex-grow: 1;
    max-width: none;
  }

  .contact__main:not(:only-child) .section__header {
    text-align: start;
    margin-block-end: 32px;
  }

  .contact__form {
    max-width: 460px;
    margin-inline-start: auto;
    margin-inline-end: auto;
  }

  .contact__aside {
    max-width: 530px;
    margin-inline-end: 40px;
  }
}

@media screen and (min-width: 1200px) {
  .contact__aside {
    margin-inline-end: 90px;
  }
}

.cookie-bar {
  width: calc(100% - var(--container-gutter) * 2);
  border: 1px solid rgb(var(--root-border-color));
  background: rgb(var(--root-background));
  z-index: 2;
  max-width: 400px;
  padding: 20px;
  transition: visibility .2s ease-in-out, opacity .2s ease-in-out;
  display: block;
  position: fixed;
  bottom: var(--container-gutter);
}

.cookie-bar:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: var(--container-gutter);
}

.cookie-bar:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: var(--container-gutter);
}

.cookie-bar[hidden] {
  visibility: hidden;
  opacity: 0;
}

.cookie-bar__actions {
  margin-top: 18px;
}

.cookie-bar__actions .button:last-child {
  margin-inline-start: 4px;
}

@media screen and (max-width: 740px) {
  @supports (padding: max(0px)) {
    .cookie-bar {
      bottom: max(24px, env(safe-area-inset-bottom, 0px)  + 24px);
    }
  }
}

@media screen and (min-width: 741px) {
  .cookie-bar {
    padding: 30px;
  }
}

.faq {
  position: relative;
}

.faq__category {
  --anchor-offset: 20px;
  background: rgb(var(--secondary-background));
  margin-block-end: 0;
  padding: 20px 24px;
}

.faq__item + .faq__item {
  border-top: 1px solid rgba(var(--text-color), .15);
}

.faq__item .collapsible__content {
  padding-block-end: 26px;
  padding-inline-end: 40px;
}

@media screen and (max-width: 740px) {
  .faq {
    margin-inline: calc(-1 * var(--container-gutter));
  }

  .faq__item {
    padding-inline: var(--container-gutter);
  }

  .faq__item ~ .faq__category {
    margin-block-start: 30px;
  }
}

@media screen and (min-width: 741px) {
  .faq__item .collapsible-toggle, .faq__item .collapsible__content {
    padding-inline-start: 24px;
    padding-inline-end: 24px;
  }
}

@media screen and (min-width: 1000px) {
  .faq__wrapper {
    width: calc(var(--grid-column-width) * 10 + var(--grid-gap) * 9);
    max-width: 668px;
    margin-inline-start: auto;
    margin-inline-end: auto;
  }

  .faq-navigation {
    max-width: calc(var(--grid-column-width) * 4 + var(--grid-gap) * 3);
    height: 100%;
    display: block;
    position: absolute;
    top: 0;
  }

  .faq-navigation:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 0;
  }

  .faq-navigation:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 0;
  }
}

@media screen and (min-width: 1400px) {
  .faq-navigation {
    max-width: calc(var(--grid-column-width) * 3 + var(--grid-gap) * 2);
  }

  .faq-navigation:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: calc(var(--grid-column-width)  + var(--grid-gap));
  }

  .faq-navigation:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: calc(var(--grid-column-width)  + var(--grid-gap));
  }
}

.footer {
  background: rgb(var(--background));
  color: rgb(var(--text-color));
  padding: 48px 0;
}

.footer--bordered {
  border-top: 1px solid rgb(var(--border-color));
}

.footer__item-list {
  grid-template-columns: 1fr 1fr;
  justify-content: space-between;
  gap: 40px;
  display: grid;
}

.footer__item {
  word-break: break-word;
  max-width: 325px;
}

.footer__item-title {
  margin-block-end: 12px;
}

.footer__image {
  display: block;
}

.footer__item--social-media .footer__item-content {
  margin-block-start: 20px;
}

.footer__aside {
  margin-block-start: 42px;
}

.footer__cross-border {
  display: flex;
}

.footer__cross-border .popover-container + .popover-container {
  margin-inline-start: -1px;
}

.footer__newsletter-form {
  margin-block-start: 16px;
}

.footer__copyright, .footer__payment-methods {
  color: rgba(var(--footer-text-color), .7);
  margin-block-start: 32px;
  display: block;
}

.footer__copyright {
  align-items: center;
  display: flex;
}

.footer__follow-and-payment {
  gap: 16px;
  margin-inline-start: auto;
  display: grid;
}

.footer__payment-methods-label {
  margin-block-end: 8px;
  display: inline-block;
}

@media screen and (max-width: 740px) {
  .footer__item--image, .footer__item--newsletter, .footer__item--newsletter + .footer__item--social-media:last-child {
    grid-column: span 2;
  }
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .footer__item-list {
    grid-template-columns: 1fr 1fr 1fr;
  }

  .footer__item--image.is-first {
    grid-column: span 3;
  }

  .footer__item--newsletter {
    grid-column: span 2;
  }
}

@media screen and (min-width: 741px) {
  .footer__payment-methods {
    align-items: center;
    display: flex;
  }

  .footer__payment-methods-label {
    margin-block-end: 0;
    margin-inline-end: 14px;
  }
}

@media screen and (min-width: 1000px) {
  .footer {
    padding-block-start: 72px;
    padding-block-end: 50px;
  }

  .footer__item-list {
    grid-template-columns: none;
    grid-auto-flow: column;
  }

  .footer__item-title {
    margin-block-end: 20px;
  }

  .footer__aside {
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: center;
    row-gap: 24px;
    margin-block-start: 50px;
    display: flex;
  }

  .footer__cross-border {
    margin-inline-end: 32px;
  }

  .footer__payment-methods, .footer__copyright {
    margin-block-start: 0;
  }

  .footer__payment-methods {
    margin-inline-start: auto;
  }
}

@media screen and (min-width: 1200px) {
  .footer__cross-border {
    margin-inline-end: 50px;
  }
}

.gallery {
  --gallery-image-height: 370px;
  display: block;
  position: relative;
}

.gallery__list-wrapper:not(.is-scrollable) ~ .custom-drag-cursor {
  visibility: hidden;
}

.gallery__list-wrapper:not(.is-scrollable) ~ .gallery__prev-next-buttons, .gallery__list-wrapper:not(.is-scrollable) ~ .gallery__progress-bar-wrapper {
  display: none;
}

.gallery__list-wrapper {
  display: block;
}

.gallery__list {
  flex-wrap: nowrap;
  display: flex;
}

.gallery__list-wrapper.is-scrollable .gallery__list:after {
  content: "";
  flex: 0 0 var(--container-outer-width);
}

.gallery__item {
  flex-shrink: 0;
  width: max-content;
}

.gallery__item:not(:first-child) {
  margin-inline-start: var(--container-gutter);
}

.gallery__figure {
  margin: 0;
  display: table;
}

.gallery__image {
  height: var(--gallery-image-height);
  border-radius: var(--block-border-radius-reduced);
  -webkit-user-select: none;
  user-select: none;
  width: auto;
  display: block;
  overflow: hidden;
}

.gallery__caption {
  caption-side: bottom;
  margin-block-start: 16px;
  display: table-caption;
}

.gallery__progress-bar {
  margin-block-start: 32px;
  display: block;
}

.gallery__prev-next-buttons {
  z-index: 1;
  position: absolute;
  top: calc(var(--gallery-image-height) / 2 - 56px);
}

.gallery__prev-next-buttons:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: calc(var(--container-outer-width)  - 28px);
}

.gallery__prev-next-buttons:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: calc(var(--container-outer-width)  - 28px);
}

@media screen and not (pointer: fine) {
  .gallery__prev-next-buttons {
    display: none !important;
  }
}

@media screen and (min-width: 1000px) {
  .gallery {
    --gallery-image-height: 40vw;
  }
}

@media screen and (min-width: 1200px) {
  .gallery {
    --gallery-image-height: 35vw;
  }
}

@media screen and (min-width: 1400px) {
  .gallery {
    --gallery-image-height: 30vw;
  }
}

.gift-card {
  color-adjust: exact;
  background: rgb(var(--background));
  min-height: var(--window-height, 100vh);
  text-align: center;
}

.gift-card__wrapper {
  max-width: 530px;
  margin-inline-start: auto;
  margin-inline-end: auto;
}

.gift-card__logo {
  margin-block-end: 32px;
}

.gift-card__logo-image {
  margin-inline-start: auto;
  margin-inline-end: auto;
  display: block;
}

.gift-card__image-wrapper {
  max-width: 280px;
  margin-block-end: -65px;
  margin-inline-start: auto;
  margin-inline-end: auto;
}

.gift-card__image {
  border-radius: 18px;
}

.gift-card__card {
  background: rgb(var(--section-card-background));
  color: rgb(var(--text-color));
  padding: 32px;
}

.gift-card__card + .gift-card__card {
  margin-block-start: 16px;
}

.gift-card__main {
  padding-block-start: 97px;
}

.gift-card__amount {
  color: rgb(var(--product-on-sale-accent));
}

.gift-card__code-container {
  gap: 8px;
  display: grid;
}

.gift-card__code {
  -webkit-appearance: none;
  appearance: none;
  height: var(--button-height);
  line-height: var(--button-height);
  border: 1px solid rgb(var(--border-color));
  background: none;
  border-radius: 0;
  padding-inline-start: 12px;
  padding-inline-end: 12px;
}

.gift-card__expires-on {
  margin-block-start: 16px;
}

.gift-card__aside {
  gap: 24px;
  display: grid;
}

.gift-card__qr {
  display: block;
}

.gift-card__qr img, .gift-card__wallet {
  width: 132px;
  margin-inline-start: auto;
  margin-inline-end: auto;
}

.gift-card__button-wrapper {
  margin-block-start: 24px;
}

@media screen and (min-width: 741px) {
  .gift-card__logo {
    margin-block-end: 56px;
  }

  .gift-card__card {
    padding: 40px;
  }

  .gift-card__image-wrapper {
    margin-block-end: -95px;
  }

  .gift-card__main {
    padding-block-start: 135px;
  }

  .gift-card__image-wrapper {
    max-width: 360px;
  }

  .gift-card__code-container {
    grid-template-columns: 1fr auto;
    gap: 16px;
  }

  .gift-card__aside {
    grid-auto-flow: column;
    align-items: center;
    gap: 32px;
  }

  .gift-card__aside .heading {
    text-align: start;
  }

  .gift-card__qr img {
    width: 68px;
  }

  .gift-card__wallet {
    width: 145px;
  }

  .gift-card__button-wrapper {
    margin-block-start: 40px;
  }
}

.header {
  background: rgb(var(--header-background));
  color: rgb(var(--header-text-color));
  transition: background .2s ease-in-out, color .2s ease-in-out, box-shadow .2s ease-in-out;
  display: block;
}

.header--bordered {
  box-shadow: 0 1px #0000;
}

.header--bordered:not(.header--transparent) {
  box-shadow: 0 1px rgb(var(--border-color));
}

.header__logo {
  margin: 0;
  display: block;
  position: relative;
}

.header__logo-link, .header__logo-image {
  width: max-content;
  display: block;
}

.header__logo-text {
  color: currentColor;
  max-width: min(350px, 60vw);
}

.header__logo-image {
  transition: opacity .2s ease-in-out;
}

.header__logo-image--transparent {
  opacity: 0;
  object-fit: contain;
  object-position: center;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
}

.header__logo-image--transparent:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 0;
}

.header__logo-image--transparent:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 0;
}

.js .header--transparent .header__logo-image:not(:last-child) {
  opacity: 0;
}

.js .header--transparent .header__logo-image--transparent {
  opacity: 1;
}

.header__wrapper {
  align-items: center;
  padding: 20px 0;
  display: flex;
}

.header__inline-navigation {
  align-items: center;
  display: flex;
}

.header__inline-navigation, .header__secondary-links {
  flex: 1 1 0;
}

.header__icon-wrapper {
  display: block;
}

@media screen and (max-width: 740px) {
  .header__logo-text {
    text-align: center;
  }
}

@media screen and (min-width: 741px) {
  .header__wrapper {
    padding: calc(27px - var(--reduce-header-padding) * 6px) 0;
  }
}

@media screen and (min-width: 1000px) and (max-width: 1199px) {
  .header__search-bar {
    margin-inline-start: 24px;
  }
}

.header__secondary-links {
  justify-content: flex-end;
  align-items: center;
  display: flex;
}

.header__secondary-links .header__linklist {
  flex-wrap: nowrap;
}

.header__icon-list {
  grid-auto-flow: column;
  justify-content: flex-start;
  align-items: center;
  gap: 20px;
  display: grid;
}

.header__cart-count {
  margin-inline-start: 8px;
  top: -1px;
}

.header__cart-count--floating {
  margin-inline-start: 0;
  position: absolute;
  top: -8px;
}

.header__cart-count--floating:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: -14px;
}

.header__cart-count--floating:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: -14px;
}

.header__cart-count {
  background: rgb(var(--header-text-color));
  color: rgb(var(--header-background));
}

.js .header--transparent .header__cart-count {
  color: rgb(var(--header-transparent-bubble-text-color));
}

@media screen and (min-width: 741px) {
  .header__icon-list {
    gap: 24px;
  }
}

.header__cross-border {
  grid-auto-flow: column;
  gap: 18px;
  display: grid;
}

.header__secondary-links .header__cross-border {
  margin-inline-end: 24px;
}

@media screen and (min-width: 1200px) {
  .header__secondary-links .header__cross-border {
    margin-inline-end: 30px;
  }
}

.header__bottom-navigation {
  padding-block: calc(17px - var(--reduce-header-padding) * 8px) calc(19px - var(--reduce-header-padding) * 8px);
  border-top: 1px solid rgb(var(--header-border-color));
  transition: border-top .2s ease-in-out;
}

.header__linklist {
  flex-wrap: wrap;
  row-gap: 12px;
  display: flex;
}

.header__linklist-item {
  flex-shrink: 0;
}

.header__linklist-item:not(:last-child) {
  margin-inline-end: 32px;
}

.header__linklist-item.has-dropdown:hover:before {
  content: attr(data-item-title);
  opacity: 0;
  height: 100%;
  margin-inline-start: -32px;
  padding-inline-start: 32px;
  padding-inline-end: 32px;
  position: absolute;
  top: 0;
}

.header__bottom-navigation .header__linklist-item:hover:before {
  height: calc(100% - var(--header-height-without-bottom-nav));
  top: auto;
  bottom: 0;
}

.header__linklist-link {
  display: block;
}

.header__bottom-navigation .header__linklist {
  justify-content: center;
}

.nav-dropdown {
  background: rgb(var(--background));
  color: rgb(var(--text-color));
  border: 1px solid rgba(var(--text-color), .15);
  white-space: nowrap;
  visibility: hidden;
  opacity: 0;
  margin-inline-start: -32px;
  padding-block-start: 20px;
  padding-block-end: 20px;
  transition: opacity .25s ease-in-out, visibility .25s ease-in-out;
  display: block;
  position: absolute;
  top: 100%;
}

.nav-dropdown:not([hidden]), .focus-outline :focus-within > .nav-dropdown, .no-js :focus-within > .nav-dropdown, .no-js :hover > .nav-dropdown {
  visibility: visible;
  opacity: 1;
}

.nav-dropdown--restrict {
  max-height: calc(100vh - var(--header-height)  - 20px);
  overflow: auto;
}

.nav-dropdown .nav-dropdown {
  margin-inline-start: 0;
  top: -20px;
}

.nav-dropdown .nav-dropdown:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 100%;
}

.nav-dropdown .nav-dropdown:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 100%;
}

.nav-dropdown__item {
  position: relative;
}

.nav-dropdown__link {
  justify-content: space-between;
  align-items: center;
  padding: 3px 30px;
  display: flex;
}

.nav-dropdown__link > svg {
  margin-inline-start: 16px;
  transition: transform .25s ease-in-out;
  position: relative;
  top: 2px;
}

.nav-dropdown__link[aria-expanded="true"] > svg {
  transform: translateX(calc(var(--transform-logical-flip) * 8px));
}

.mega-menu {
  --mega-menu-column-gap: 48px;
  --mega-menu-image-gap: 24px;
  visibility: hidden;
  opacity: 0;
  background: rgb(var(--background));
  color: rgb(var(--text-color));
  width: 100%;
  transition: opacity .25s ease-in-out, visibility .25s ease-in-out;
  display: block;
  position: absolute;
  top: 100%;
  left: 0;
}

.mega-menu.is-closing, .mega-menu[hidden] {
  z-index: -1;
}

.mega-menu:after {
  content: "";
  height: calc(100vh - 100% - var(--header-height, 0px));
  opacity: 0;
  pointer-events: none;
  background: #000;
  width: 100%;
  transition: opacity .25s ease-in-out;
  position: absolute;
  top: 100%;
  left: 0;
}

.mega-menu:not([hidden]), .focus-outline :focus-within > .mega-menu, .no-js :focus-within > .mega-menu, .no-js :hover > .mega-menu {
  visibility: visible;
  opacity: 1;
}

.mega-menu:not([hidden]):after, .focus-outline :focus-within > .mega-menu:after, .no-js :focus-within > .mega-menu:after, .no-js :hover > .mega-menu:after {
  opacity: .3;
}

.mega-menu.is-closing:after {
  opacity: 0;
  transition-delay: .15s;
}

.header--bordered .mega-menu {
  margin-top: 1px;
}

.mega-menu__inner {
  justify-content: safe center;
  column-gap: var(--mega-menu-column-gap);
  max-height: calc(100vh - var(--header-height, 0px)  - var(--announcement-bar-height, 0px)  - 50px);
  z-index: 1;
  grid-auto-flow: column;
  padding-block-start: 48px;
  padding-block-end: 48px;
  display: grid;
  position: relative;
  overflow: auto;
}

.mega-menu__columns-wrapper {
  margin: calc(-1 * var(--mega-menu-column-gap) / 2);
  flex-wrap: wrap;
  display: flex;
}

.mega-menu__column {
  margin: calc(var(--mega-menu-column-gap) / 2);
}

.mega-menu__images-wrapper {
  align-items: flex-start;
  gap: var(--mega-menu-image-gap);
  grid-auto-flow: column;
  display: grid;
}

.mega-menu__images-wrapper--tight {
  gap: 20px;
}

.mega-menu__image-push {
  text-align: center;
  width: 180px;
}

.mega-menu__image-push:only-child {
  width: 200px;
}

.mega-menu__image-wrapper {
  border-radius: var(--block-border-radius-reduced);
  z-index: 0;
  margin-block-end: 18px;
  overflow: hidden;
}

.mega-menu__image {
  border-radius: inherit;
}

.mega-menu__heading {
  margin-block-end: 7px;
}

.mega-menu__title {
  margin-block-end: 16px;
}

@media screen and (min-width: 1200px) {
  .mega-menu {
    --mega-menu-column-gap: 64px;
  }
}

@media screen and (min-width: 1400px) {
  .mega-menu {
    --mega-menu-column-gap: 80px;
    --mega-menu-image-gap: 40px;
  }

  .mega-menu__image-push {
    width: 240px;
  }

  .mega-menu__image-push:only-child {
    width: 280px;
  }
}

.mobile-nav__item {
  display: flow-root;
}

/* .mobile-nav__item:not(:last-child) { */
.mobile-nav__item {
  border-bottom: 1px solid rgba(var(--text-color), .15);
}

.mobile-nav__link {
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin: 0;
  padding: 21px 0;
  display: flex;
}

.mobile-nav__image-heading {
  margin-block-end: 7px;
}

.mobile-nav__image-text {
  display: block;
}

.mobile-nav .collapsible {
  margin-inline: calc(-1 * var(--container-gutter));
}

.mobile-nav .mobile-nav {
  margin-inline: var(--container-gutter);
  padding-inline-start: 8px;
  margin-block-end: 24px !important;
}

.mobile-nav .mobile-nav .mobile-nav__item {
  border: none;
  margin-block-end: 15px;
}

.mobile-nav .mobile-nav .mobile-nav__link {
  padding: 0;
}

.mobile-nav .mobile-nav .mobile-nav {
  border-inline-start: 1px solid rgba(var(--text-color), .15);
  padding-inline-start: 20px;
  margin-block-start: 16px !important;
  margin-block-end: 10px !important;
}

.mobile-nav .mobile-nav .mobile-nav .mobile-nav__item {
  margin-block-end: 14px;
}

.mobile-nav .mobile-nav .mobile-nav .mobile-nav__item:last-child {
  margin-block-end: 0;
}

.mobile-nav + .mobile-nav__images-wrapper {
  padding-inline-start: 8px;
}

.mobile-nav__images-scroller {
  padding-inline: var(--container-gutter);
  grid-auto-flow: column;
  gap: 12px;
  width: -moz-fit-content;
  width: fit-content;
  margin-block-end: 32px;
  display: grid;
}

.mobile-nav__image-push {
  text-align: center;
  min-width: 120px;
  max-width: 134px;
}

.mobile-nav__image {
  border-radius: min(var(--block-border-radius), 4px);
  margin-block-end: 14px;
  display: block;
}

.mobile-nav__footer {
  flex-wrap: wrap;
  justify-content: space-between;
  display: flex;
}

.image-with-text {
  --image-height: min(var(--image-max-height), (100vw - var(--container-gutter) * 4) * (1 / var(--image-aspect-ratio)));
  text-align: center;
  display: block;
  position: relative;
}

.image-with-text:before {
  content: "";
  background: rgb(var(--section-accent-background));
  z-index: -1;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
}

.image-with-text:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  left: 0;
}

.image-with-text:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  right: 0;
}

.image-with-text--overlap-image:before {
  height: var(--image-height);
}

.image-with-text--overlap-text:before {
  --image-height-difference: calc(100% - var(--image-height));
  --image-height-difference-abs: max(var(--image-height-difference), -1 * var(--image-height-difference));
  height: var(--image-height-difference-abs);
  top: auto;
  bottom: 0;
}

.image-with-text__image-wrapper {
  border-radius: var(--block-border-radius-reduced);
  z-index: 0;
  margin-block-end: 32px;
  position: relative;
  overflow: hidden;
}

.image-with-text__image {
  width: 100%;
  display: block;
  position: relative;
}

.image-with-text__image:not([hidden]) {
  z-index: 1;
}

.image-with-text__image:not(:first-child) {
  object-fit: cover;
  object-position: center;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
}

.image-with-text__image:not(:first-child):not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 0;
}

.image-with-text__image:not(:first-child):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 0;
}

.image-with-text__image[hidden] {
  visibility: hidden;
  z-index: -1;
  transition: visibility .6s linear;
}

.image-with-text__wrapper {
  overflow: hidden;
}

.image-with-text__content-list {
  flex-wrap: nowrap;
  display: flex;
}

.image-with-text__content {
  flex-shrink: 0;
  order: 0;
  width: 100%;
  display: block;
}

.image-with-text__content[hidden] {
  visibility: hidden;
  order: 1;
}

.image-with-text__navigation {
  grid-template-columns: 1fr 1fr;
  align-items: flex-start;
  column-gap: 24px;
  margin-block-start: 40px;
  display: inline-grid;
}

@keyframes navigationItemAnimation {
  from {
    transform: scaleX(0);
  }

  to {
    transform: scaleX(1);
  }
}

.image-with-text__navigation-item {
  position: relative;
}

.image-with-text__navigation-item:before, .image-with-text__navigation-item:after {
  content: "";
  background: rgba(var(--text-color), .15);
  width: 100%;
  height: 2px;
  margin-block-end: 16px;
  display: block;
}

.image-with-text__navigation-item:after {
  background: rgb(var(--text-color));
  transform-origin: var(--transform-origin-start);
  position: absolute;
  top: 0;
  transform: scaleX(0);
}

.image-with-text__navigation-item:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
  left: 0;
}

.image-with-text__navigation-item:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
  right: 0;
}

.image-with-text__navigation-item[aria-current="true"]:after {
  animation: navigationItemAnimation var(--section-autoplay-duration) linear;
  animation-play-state: var(--section-animation-play-state, running);
}

@media screen and (max-width: 999px) {
  .image-with-text {
    padding: var(--vertical-breather) var(--container-gutter);
  }

  .image-with-text--boxed {
    padding: var(--container-gutter);
  }
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .image-with-text {
    padding: var(--vertical-breather) calc((var(--grid-column-width)  + var(--grid-gap)) * 2);
  }

  .image-with-text--boxed {
    padding: var(--vertical-breather) calc((var(--grid-column-width)  + var(--grid-gap)) * 2 + var(--container-gutter));
  }

  .image-with-text__image-wrapper {
    margin-block-end: 48px;
  }
}

@media screen and (min-width: 1000px) {
  .image-with-text:before {
    width: calc(var(--grid-column-width) * 13 + (var(--grid-gap) * 12)  + var(--container-outer-margin));
    height: 100%;
    top: 0;
  }

  .image-with-text:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    left: auto;
    right: 0;
  }

  .image-with-text:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    left: 0;
    right: auto;
  }

  .image-with-text--reverse:not(.image-with-text--overlap-image):not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    left: 0;
    right: auto;
  }

  .image-with-text--overlap-image:not(.image-with-text--reverse):not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    left: 0;
    right: auto;
  }

  .image-with-text--reverse:not(.image-with-text--overlap-image):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    left: auto;
    right: 0;
  }

  .image-with-text--overlap-image:not(.image-with-text--reverse):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    left: auto;
    right: 0;
  }

  .image-with-text--overlap-image:before {
    width: calc(var(--grid-column-width) * 7 + (var(--grid-gap) * 6)  + var(--container-outer-margin));
  }

  .image-with-text--overlap-both:before {
    width: 100% !important;
  }

  .image-with-text__wrapper {
    padding: var(--vertical-breather) 0;
    align-items: center;
    display: flex;
  }

  .image-with-text--reverse .image-with-text__wrapper {
    flex-direction: row-reverse;
  }

  .image-with-text__image-wrapper {
    width: calc(var(--grid-column-width) * 8 + (var(--grid-gap) * 7));
    margin-block-end: 0;
    margin-inline-start: calc(var(--grid-column-width)  + var(--grid-gap));
  }

  .image-with-text--reverse .image-with-text__image-wrapper {
    margin-inline: 0 calc(var(--grid-column-width)  + var(--grid-gap));
  }

  .image-with-text__content-wrapper {
    width: calc(var(--grid-column-width) * 9 + (var(--grid-gap) * 8));
    margin-inline-start: auto;
    margin-inline-end: auto;
  }
}

@media screen and (min-width: 1200px) {
  .image-with-text__navigation {
    column-gap: 40px;
    margin-block-start: 48px;
  }
}

@media screen and (min-width: 1400px) {
  .image-with-text__content-wrapper {
    width: calc(var(--grid-column-width) * 7 + (var(--grid-gap) * 6));
    margin-inline-end: calc((var(--grid-column-width)  + var(--grid-gap)) * 2);
  }
}

.image-with-text-block {
  display: block;
}

.image-with-text-block__image-wrapper {
  background: rgb(var(--secondary-background));
  overflow: hidden;
}

.image-with-text-block__content {
  background-color: rgb(var(--section-block-background));
  border-radius: var(--block-border-radius);
  padding: 48px 24px;
}

.image-with-text-block__text-container {
  margin-block-start: 24px;
}

@media screen and (max-width: 999px) {
  .image-with-text-block__content {
    width: auto;
  }

  .image-with-text-block--overlap-left .image-with-text-block__content, .image-with-text-block--overlap-right .image-with-text-block__content {
    margin-block: calc(-1 * var(--container-gutter)) 0;
    margin-inline: var(--container-gutter);
    padding: 40px;
  }

  .image-with-text-block:not(.image-with-text-block--overlap-left):not(.image-with-text-block--overlap-right) .image-with-text-block__content {
    border-radius: 0;
  }

  .image-with-text-block--cover {
    align-items: center;
    display: flex;
    position: relative;
  }

  .image-with-text-block--cover .image-with-text-block__image-wrapper {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
  }

  .image-with-text-block--cover .image-with-text-block__image-wrapper:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 0;
  }

  .image-with-text-block--cover .image-with-text-block__image-wrapper:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 0;
  }

  .image-with-text-block--cover .image-with-text-block__image {
    object-fit: cover;
    object-position: center;
    height: 100%;
  }

  .image-with-text-block--cover .image-with-text-block__content {
    padding: var(--container-gutter);
    margin: var(--vertical-breather) var(--container-gutter);
  }
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .image-with-text-block--cover .image-with-text-block__content {
    max-width: 400px;
    margin-inline-start: auto;
    margin-inline-end: auto;
  }
}

@media screen and (min-width: 741px) {
  .image-with-text-block__content {
    padding-inline-start: 48px;
    padding-inline-end: 48px;
  }
}

@media screen and (min-width: 1000px) {
  .image-with-text-block {
    align-items: center;
    min-height: 500px;
    display: flex;
    position: relative;
  }

  .image-with-text-block__image-wrapper {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
  }

  .image-with-text-block__image-wrapper:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 0;
  }

  .image-with-text-block__image-wrapper:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 0;
  }

  .image-with-text-block__image {
    object-fit: cover;
    object-position: center;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
  }

  .image-with-text-block__image:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 0;
  }

  .image-with-text-block__image:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 0;
  }

  .image-with-text-block__content {
    margin-block: var(--vertical-breather);
  }

  .image-with-text-block--overlap-right .image-with-text-block__image-wrapper, .image-with-text-block--overlap-left .image-with-text-block__image-wrapper {
    width: calc(var(--grid-column-width) * 14 + (var(--grid-gap) * 13)  + var(--container-outer-margin));
  }

  .image-with-text-block--overlap-left .image-with-text-block__image-wrapper:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: auto;
    right: 0;
  }

  .image-with-text-block--overlap-left .image-with-text-block__image-wrapper:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 0;
    right: auto;
  }
}

@media screen and (min-width: 1200px) {
  .image-with-text-block {
    min-height: 700px;
  }

  .image-with-text-block--small {
    min-height: 400px;
  }

  .image-with-text-block__content:not(.image-with-text-block__content--tight) {
    padding: 64px;
  }
}

.image-overlay {
  --image-height: auto;
  min-height: var(--image-height);
  color: rgb(var(--text-color));
  background: rgb(var(--section-overlay-color));
  display: flex;
  position: relative;
}

.image-overlay--small {
  --image-height: 375px;
}

.image-overlay--medium {
  --image-height: 500px;
}

.image-overlay--large {
  --image-height: 600px;
}

.image-overlay:before {
  content: "";
  width: 0;
  padding-block-end: calc(100% / var(--image-aspect-ratio));
  display: block;
}

.image-overlay__image-wrapper:after {
  content: "";
  background: rgba(var(--section-overlay-color), var(--section-overlay-opacity));
  pointer-events: none;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
}

.image-overlay__image-wrapper:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
  left: 0;
}

.image-overlay__image-wrapper:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
  right: 0;
}

.image-overlay__image-wrapper, .image-overlay__image {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  overflow: hidden;
}

:is(.image-overlay__image-wrapper:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)), .image-overlay__image:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  left: 0;
}

:is(.image-overlay__image-wrapper:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)), .image-overlay__image:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  right: 0;
}

.image-overlay__image {
  object-fit: cover;
  object-position: center;
}

.image-overlay__image--placeholder {
  background: rgb(var(--background));
}

.image-overlay__content-wrapper {
  min-height: var(--image-height);
  align-items: var(--section-items-alignment);
  padding: var(--vertical-breather) 0;
  justify-content: center;
  width: 100%;
  height: 100%;
  display: flex;
  position: relative;
}

.image-overlay__text-container {
  margin-block-start: 24px;
}

.image-overlay__text-container .button-wrapper:only-child {
  margin-block-start: 32px;
}

@media screen and (min-width: 741px) {
  .image-overlay--small {
    --image-height: 400px;
  }

  .image-overlay--medium {
    --image-height: 550px;
  }

  .image-overlay--large {
    --image-height: 700px;
  }
}

.list-collections {
  display: block;
  position: relative;
}

.list-collections__item-list {
  align-items: center;
  gap: 24px;
  display: grid;
}

.list-collections__item {
  --heading-color: 255, 255, 255;
  --text-color: 255, 255, 255;
  text-align: center;
  background: rgb(var(--secondary-background));
  border-radius: var(--block-border-radius-reduced);
  z-index: 0;
  display: block;
  position: relative;
  overflow: hidden;
}

.list-collections__item.image-zoom {
  will-change: transform;
}

.list-collections__item:hover .link {
  -webkit-text-decoration-color: rgb(var(--text-color));
  text-decoration-color: rgb(var(--text-color));
}

.list-collections__item-image-wrapper {
  height: 100%;
  position: relative;
}

.list-collections__item.has-overlay .list-collections__item-image-wrapper:before {
  content: "";
  background: rgba(var(--section-block-overlay, 0, 0, 0), var(--section-block-overlay-opacity, .2));
  z-index: 1;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.list-collections__item-image {
  object-fit: cover;
  object-position: center;
  width: 100%;
  height: 100%;
  min-height: 120px;
}

.list-collections__item-info {
  z-index: 1;
  width: 100%;
  padding-inline-start: 24px;
  padding-inline-end: 24px;
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}

@media screen and (min-width: 741px) {
  .list-collections__item-list {
    grid-template-columns: repeat(auto-fit, calc(100% / 3 - var(--container-gutter) * 2 / 3));
    grid-gap: var(--container-gutter);
    justify-content: safe center;
  }
}

@media screen and (min-width: 1000px) {
  .list-collections__item-info {
    padding-inline: var(--container-gutter);
  }
}

@media screen and (min-width: 741px) {
  .list-collections--collage {
    --list-collections-collage-rows-height: 220px;
  }

  .list-collections--collage .list-collections__item-list {
    grid-template-columns: repeat(var(--section-collage-column), 1fr);
    grid-auto-rows: var(--list-collections-collage-rows-height);
    grid-auto-flow: dense;
  }

  .list-collections--collage .list-collections__item:only-child {
    width: 590px;
    margin-inline-start: auto;
    margin-inline-end: auto;
  }

  .list-collections--collage .list-collections__item {
    height: 100%;
  }

  .list-collections--collage .list-collections__item--highlight {
    grid-row: auto / span 2;
  }

  .list-collections--collage .list-collections__item--shift {
    grid-column: 2;
  }
}

@media screen and (min-width: 1000px) {
  .list-collections--collage {
    --list-collections-collage-rows-height: 250px;
  }

  .list-collections--collage .list-collections__item-list {
    margin-inline: calc(var(--grid-column-width)  + var(--grid-gap));
  }
}

@media screen and (min-width: 1200px) {
  .list-collections--collage {
    --list-collections-collage-rows-height: 300px;
  }
}

@media screen and (min-width: 1400px) {
  .list-collections--collage {
    --list-collections-collage-rows-height: 350px;
  }
}

.list-collections__scroller {
  scroll-snap-type: x mandatory;
  display: block;
  overflow: auto;
}

.list-collections--carousel .list-collections__item-list {
  padding: 0 var(--container-gutter);
  grid-template-columns: none;
  grid-auto-columns: 80vw;
  grid-auto-flow: column;
  width: min-content;
  min-width: 100%;
}

.list-collections--carousel .list-collections__item {
  scroll-snap-align: center;
  scroll-snap-stop: always;
}

@media screen and (min-width: 741px) {
  .list-collections--carousel .list-collections__item-list {
    grid-auto-columns: 60vw;
  }
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .list-collections--grid .container {
    scroll-snap-type: x mandatory;
    scrollbar-width: none;
    padding-inline-start: 0;
    padding-inline-end: 0;
    display: block;
    overflow: auto;
  }

  .list-collections--grid .container::-webkit-scrollbar {
    display: none;
  }

  .list-collections--grid .list-collections__item-list {
    padding: 0 var(--container-gutter);
    grid-template-columns: none;
    grid-auto-columns: 60vw;
    grid-auto-flow: column;
    width: min-content;
    min-width: 100%;
  }

  .list-collections--grid .list-collections__item {
    scroll-snap-align: center;
    scroll-snap-stop: always;
  }
}

@media screen and (min-width: 1000px) {
  .list-collections__scroller {
    scroll-snap-type: none;
  }

  .list-collections--carousel .list-collections__item-list {
    padding-inline: var(--container-outer-margin);
    grid-auto-columns: 23vw;
  }

  .list-collections__scroller.is-scrollable .list-collections__item-list {
    padding-inline-end: calc(var(--container-outer-margin)  + 28px);
  }

  .list-collections__prev-next {
    z-index: 1;
    display: none;
    position: absolute;
    top: calc(50% - 56px);
  }

  .list-collections__prev-next:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: var(--container-outer-width);
  }

  .list-collections__prev-next:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: var(--container-outer-width);
  }

  .list-collections__scroller.is-scrollable + .list-collections__prev-next {
    display: block;
  }

  .list-collections__arrow:last-child {
    border-top: none;
  }
}

.logo-list {
  display: block;
  position: relative;
}

.logo-list__list {
  --logos-per-row: 2;
  --logos-gap: 8px;
  grid-template-columns: repeat(min(var(--logos-per-row), var(--section-logo-count)), minmax(140px, 200px));
  gap: var(--logos-gap);
  justify-content: center;
  display: grid;
}

.logo-list__item {
  background: rgb(var(--section-logo-background));
  border-radius: var(--block-border-radius-reduced);
  justify-content: center;
  align-items: center;
  padding: 16px;
  display: flex;
}

.logo-list__image--placeholder {
  height: 100px;
}

@media screen and (max-width: 999px) {
  .logo-list--carousel .logo-list__list {
    grid-template-columns: none;
    grid-auto-columns: 140px;
    grid-auto-flow: column;
  }

  .logo-list--grid {
    padding-inline: var(--container-gutter);
  }
}

@media screen and (min-width: 741px) {
  .logo-list__list {
    --logos-per-row: 3;
    --logos-gap: 16px;
  }

  .logo-list--carousel .logo-list__list {
    grid-auto-columns: 185px;
  }
}

@media screen and (min-width: 1000px) {
  .logo-list__list {
    --logos-per-row: 6;
    --logos-gap: 24px;
  }

  .logo-list__prev-next {
    pointer-events: none;
    justify-content: space-between;
    width: 100%;
    display: flex;
    position: absolute;
    top: calc(50% - 28px);
  }

  .logo-list--carousel .logo-list__prev-next + .logo-list__list {
    grid-template-columns: none;
    grid-auto-flow: column;
    grid-auto-columns: calc(100% / var(--logos-per-row)  - (var(--logos-gap) / var(--logos-per-row) * (var(--logos-per-row)  - 1)));
    justify-content: flex-start;
    margin-inline-start: 88px;
    margin-inline-end: 88px;
    overflow: hidden;
  }

  .logo-list__arrow {
    opacity: 0;
    visibility: hidden;
    transition: opacity .15s ease-in-out, visibility .15s ease-in-out, transform .15s ease-in-out;
    transform: scale(.5);
  }

  .logo-list__arrow:not([disabled]) {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
  }
}

@media screen and (min-width: 1200px) {
  .logo-list--grid {
    margin-inline: calc(var(--grid-column-width)  + var(--grid-gap));
  }
}

.multi-column {
  --multi-column-row-gap: 32px;
  --multi-column-column-gap: 24px;
  --section-items-per-row: 2;
  --item-width: calc(var(--container-inner-width) / var(--section-items-per-row)  - (var(--multi-column-column-gap) / var(--section-items-per-row) * (var(--section-items-per-row)  - 1)));
  display: block;
  position: relative;
}

.multi-column--spacing-tight {
  --multi-column-column-gap: 12px;
}

.multi-column--spacing-loose {
  --multi-column-column-gap: 32px;
}

.multi-column--pocket-medium, .multi-column--pocket-large {
  --section-items-per-row: 1;
}

.multi-column__inner {
  grid-template-columns: repeat(auto-fit, var(--item-width));
  gap: var(--multi-column-row-gap) var(--multi-column-column-gap);
  justify-content: safe center;
  padding-block-end: 2px;
  display: grid;
}

.multi-column__inner--left {
  justify-content: safe start;
}

.multi-column__inner--right {
  justify-content: safe end;
}

.multi-column__inner--scroller {
  grid-auto-flow: column;
  grid-template-columns: none !important;
}

.multi-column__item--align-center {
  align-self: center;
}

.multi-column__item--align-end {
  align-self: end;
}

.multi-column__image-wrapper {
  border-radius: min(8px, var(--block-border-radius));
  z-index: 0;
  margin-block-end: 20px;
  margin-inline-start: auto;
  margin-inline-end: auto;
  display: block;
  overflow: hidden;
}

.multi-column__image-wrapper:only-child {
  margin-block-end: 0;
}

.multi-column__image {
  width: 100%;
}

@media screen and (max-width: 999px) {
  .multi-column__inner--scroller {
    padding-left: var(--container-gutter);
    padding-right: var(--container-gutter);
    min-width: min-content;
  }
}

@media screen and (max-width: 740px) {
  .multi-column__inner--scroller {
    grid-auto-columns: 25vw;
  }

  .multi-column--pocket-medium .multi-column__inner--scroller {
    grid-auto-columns: 35vw;
  }

  .multi-column--pocket-large .multi-column__inner--scroller {
    grid-auto-columns: 56vw;
  }
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .multi-column--pocket-small {
    --section-items-per-row: 5;
  }

  .multi-column--pocket-medium {
    --section-items-per-row: 4;
  }

  .multi-column--pocket-large {
    --section-items-per-row: 3;
  }

  .multi-column__inner--scroller {
    grid-auto-columns: 20vw;
  }

  .multi-column--pocket-medium .multi-column__inner--scroller {
    grid-auto-columns: 26vw;
  }

  .multi-column--pocket-large .multi-column__inner--scroller {
    grid-auto-columns: 36vw;
  }
}

@media screen and (min-width: 741px) {
  .multi-column--spacing-normal {
    --multi-column-row-gap: 40px;
  }

  .multi-column--spacing-loose {
    --multi-column-row-gap: 48px;
    --multi-column-column-gap: 32px;
  }
}

@media screen and (min-width: 1000px) {
  .multi-column--spacing-normal {
    --multi-column-column-gap: 40px;
  }

  .multi-column--spacing-tight {
    --multi-column-column-gap: 24px;
  }

  .multi-column--spacing-loose {
    --multi-column-column-gap: 60px;
  }

  .multi-column--pico {
    --section-items-per-row: 6;
  }

  .multi-column--small {
    --section-items-per-row: 5;
  }

  .multi-column--medium {
    --section-items-per-row: 4;
  }

  .multi-column--large {
    --section-items-per-row: 3;
  }

  .multi-column__inner--scroller {
    grid-auto-columns: var(--item-width);
    overflow: hidden;
  }

  .multi-column__inner:not(.is-scrollable) + .multi-column__prev-next {
    display: none;
  }

  .multi-column__image-wrapper {
    margin-block-end: 24px;
  }

  .multi-column__prev-next {
    pointer-events: none;
    top: calc(var(--item-width) / var(--smallest-image-aspect-ratio) / 2 - 28px);
    justify-content: space-between;
    width: calc(100% - 56px);
    display: flex;
    position: absolute;
    left: 28px;
  }

  .multi-column__prev-next--no-image {
    top: calc(50% - 28px);
  }

  .multi-column__arrow {
    z-index: 1;
    opacity: 0;
    visibility: hidden;
    transition: opacity .15s ease-in-out, visibility .15s ease-in-out, transform .15s ease-in-out;
    transform: scale(.5);
  }

  .multi-column:hover .multi-column__arrow:not([disabled]) {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
  }
}

@media screen and (min-width: 1200px) {
  .multi-column--pico {
    --section-items-per-row: 8;
  }

  .multi-column--small {
    --section-items-per-row: 7;
  }

  .multi-column--medium {
    --section-items-per-row: 5;
  }
}

@media screen and (pointer: fine) {
  .multi-column__item:hover .multi-column__link {
    -webkit-text-decoration-color: rgb(var(--text-color));
    text-decoration-color: rgb(var(--text-color));
  }
}

@media screen and not (pointer: fine) {
  .multi-column__arrow:not([disabled]) {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
  }
}

.newsletter__form {
  margin-top: 32px;
}

.newsletter__form .input-row {
  grid-template-columns: none;
}

@media screen and (min-width: 1200px) {
  .newsletter__form .input-row {
    grid-template-columns: 1fr auto;
  }
}

[aria-controls="newsletter-popup"] {
  display: none;
}

.newsletter-modal {
  flex-direction: column;
  align-items: center;
  display: flex;
}

.newsletter-modal__image {
  object-fit: cover;
  object-position: center;
}

.newsletter-modal__content {
  width: 100%;
  padding-block-start: 32px;
  padding-block-end: 40px;
  padding-inline-start: 24px;
  padding-inline-end: 24px;
}

.newsletter-modal__content--extra {
  padding-block-start: 40px;
}

@media screen and (max-width: 740px) {
  .newsletter-modal {
    max-height: inherit;
  }

  .newsletter-modal__image {
    max-height: 200px;
  }

  .newsletter-modal__content {
    overflow: auto;
  }
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .newsletter-modal__image {
    max-height: 350px;
  }
}

@media screen and (min-width: 741px) {
  .newsletter-modal__content {
    padding: 60px;
  }
}

@media screen and (min-width: 1000px) {
  .newsletter-modal {
    flex-direction: row;
  }

  .newsletter-modal--reverse {
    flex-direction: row-reverse;
  }

  .newsletter-modal__image, .newsletter-modal__content {
    flex: 1 0 0;
    min-width: 500px;
    max-width: 500px;
  }

  .newsletter-modal__content {
    padding: 80px;
  }
}

.password {
  background: rgb(var(--section-background));
  color: rgb(var(--text-color));
}

.password__logo {
  margin-block-end: 0;
}

.password__logo-image {
  display: block;
}

.password__main {
  text-align: center;
  z-index: 1;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  min-height: 100vh;
  padding-inline-start: 48px;
  padding-inline-end: 48px;
  display: flex;
  position: relative;
}

@supports (--css: variables) {
  .password__main {
    min-height: var(--window-height, 100vh);
  }
}

.password__content {
  width: 100%;
  max-width: 390px;
  padding-block-start: 24px;
  padding-block-end: 24px;
}

.password__storefront-login {
  margin-block-start: 20px;
  display: block;
}

.password__storefront-login svg {
  vertical-align: sub;
  margin-inline-end: 12px;
}

.password__storefront-form {
  max-width: 340px;
  margin-inline-start: auto;
  margin-inline-end: auto;
}

.password__newsletter {
  margin-block-start: 24px;
}

.password__shopify-logo svg {
  margin-inline-start: 12px;
}

.password__copyright {
  flex-direction: column;
  align-items: center;
  display: flex;
}

.password__powered-by {
  align-items: center;
  display: flex;
}

@media screen and (max-width: 999px) {
  .password__image {
    object-fit: cover;
    object-position: center;
    opacity: .1;
    filter: grayscale();
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }

  .password__newsletter-form .input__label {
    background: none;
  }

  .password__newsletter-form :focus-within ~ .input__label, .password__newsletter-form .is-filled ~ .input__label {
    background: rgb(var(--section-background));
  }

  .password__admin-link {
    padding-block-start: 8px;
  }

  .password__storefront-form {
    background: inherit;
    border-radius: 10px 10px 0 0;
    max-width: none;
    margin-inline-start: 0;
    margin-inline-end: 0;
    padding: 62px 48px 48px;
    position: relative;
  }
}

@media screen and (min-width: 741px) {
  .password__copyright {
    flex-direction: row;
  }

  .password__newsletter {
    margin-block-start: 32px;
  }

  .password__storefront-login {
    margin-block-start: 28px;
  }

  .password__shopify-logo svg {
    width: 98px;
    height: 28px;
  }

  .password__admin-link {
    padding-inline-start: 18px;
  }
}

@media screen and (min-width: 1000px) {
  .password {
    grid-auto-columns: 50%;
    grid-auto-flow: column;
    justify-content: center;
    display: grid;
  }

  .password__image {
    object-fit: cover;
    object-position: center;
    height: 100%;
    max-height: 100vh;
  }
}

.predictive-search__form {
  align-items: center;
  display: flex;
}

.predictive-search__input {
  box-shadow: none;
  -webkit-appearance: none;
  appearance: none;
  background: none;
  border: none;
  min-width: 300px;
  margin-inline-start: 8px;
  padding: 0;
}

.predictive-search__input::placeholder {
  color: rgba(var(--text-color), .7);
  transition: color .2s ease-in-out;
}

.header__search-bar .predictive-search__input::placeholder {
  color: rgba(var(--header-text-color), .8);
}

.predictive-search .tabs-nav, .predictive-search__menu-list {
  padding-block-start: 24px;
}

.predictive-search__menu + .predictive-search__menu {
  padding-block-start: 40px;
}

.predictive-search__menu-title {
  margin-block-end: 16px;
}

.predictive-search__product-item:first-child .line-item__content-wrapper {
  margin-block-start: 0;
}

.predictive-search__product-item {
  position: relative;
}

.predictive-search__product-item svg {
  opacity: 0;
  transform: translateX(calc(var(--transform-logical-flip) * min(var(--container-gutter), 30px)));
  transition: opacity .2s ease-in-out, transform .2s ease-in-out;
  position: absolute;
  top: calc(50% - 7px);
  right: 0;
}

@media screen and (pointer: fine) {
  .predictive-search__product-item:hover svg {
    opacity: 1;
    transform: translateX(0);
  }
}

.predictive-search__linklist {
  max-width: 450px;
  margin-inline-start: auto;
  margin-inline-end: auto;
}

.predictive-search__linklist--narrow {
  max-width: 390px;
}

.predictive-search__linklist--bordered {
  border-block: 1px solid rgb(var(--border-color));
}

.predictive-search__linklist-item {
  border-top: 1px solid rgb(var(--border-color));
}

.predictive-search__linklist-item:first-child {
  border-top: none;
}

.predictive-search__linklist-link {
  justify-content: space-between;
  align-items: center;
  padding-block-start: 15px;
  padding-block-end: 15px;
  display: flex;
}

.predictive-search__linklist-link mark {
  background: none;
  font-weight: bold;
}

.predictive-search__linklist-link svg {
  opacity: 0;
  transform: translateX(calc(var(--transform-logical-flip) * min(var(--container-gutter), 30px)));
  transition: opacity .2s ease-in-out, transform .2s ease-in-out;
}

@media screen and (pointer: fine) {
  .predictive-search__linklist-link:hover svg {
    opacity: 1;
    transform: translateX(0);
  }
}

.predictive-search__article-item + .predictive-search__article-item {
  margin-block-start: 24px;
}

.predictive-search__article-image-wrapper {
  flex: none;
  width: 100px;
  margin-inline-end: 18px;
  display: block;
  position: relative;
}

.predictive-search__article-category {
  margin-block-end: 8px;
}

@supports (-webkit-touch-callout: none) {
  @media screen and (max-width: 740px) {
    .predictive-search .drawer__content--center {
      margin-top: 150px;
    }
  }
}

@media screen and (min-width: 741px) {
  .predictive-search__input {
    margin-inline-start: 12px;
  }

  .predictive-search .tabs-nav, .predictive-search__menu-list {
    padding-block-start: 32px;
  }

  .predictive-search__article-image-wrapper {
    width: 140px;
  }
}

.press-list {
  text-align: center;
  display: block;
}

.press-list__wrapper {
  flex-wrap: nowrap;
  max-width: 800px;
  margin-inline-start: auto;
  margin-inline-end: auto;
  padding-block-start: 10px;
  display: flex;
  overflow: hidden;
}

.press-list__item {
  flex-shrink: 0;
  order: 0;
  width: 100%;
  display: block;
}

.press-list__item[hidden] {
  visibility: hidden;
  order: 1;
}

.press-list__logo-list {
  grid-auto-flow: column;
  align-items: center;
  gap: 48px;
  margin-block-start: 40px;
  display: inline-grid;
}

.press-list__logo-item {
  opacity: .3;
  transition: opacity .2s ease-in-out;
}

.press-list__logo-item[aria-current="true"] {
  opacity: 1;
}

.press-list__logo-image {
  vertical-align: middle;
}

@media screen and (max-width: 999px) {
  .press-list__logo-list-wrapper {
    margin-inline: calc(-1 * var(--container-gutter));
    overflow: auto hidden;
  }

  .press-list__logo-list {
    margin-inline: var(--container-gutter);
  }
}

@media screen and (min-width: 741px) {
  .press-list__logo-list {
    gap: 80px;
    margin-block-start: 48px;
  }
}

@media screen and (max-width: 740px) {
  .product:not(.product--featured) {
    margin-block: var(--container-gutter) 36px;
  }
}

@media screen and (min-width: 1000px) {
  .product {
    justify-content: space-between;
    align-items: flex-start;
    display: flex;
  }

  .product:not(.product--featured) {
    margin-block-end: 80px;
  }

  .product__media {
    --product-media-width: calc(var(--grid-column-width) * 10 + var(--grid-gap) * 10);
    width: var(--product-media-width);
  }

  .product__info {
    --product-info-width: calc(var(--grid-column-width) * 9 + var(--grid-gap) * 8);
    width: var(--product-info-width);
    flex: none;
  }

  .product__info:only-child {
    margin-inline-start: auto;
    margin-inline-end: auto;
  }
}

@media screen and (min-width: 1200px) {
  .product {
    margin-inline: calc(var(--grid-column-width)  + var(--grid-gap));
  }

  .product__media {
    --product-media-width: calc(var(--grid-column-width) * 9 + var(--grid-gap) * 9);
  }

  .product__info {
    --product-info-width: calc(var(--grid-column-width) * 8 + var(--grid-gap) * 7);
  }
}

@media screen and (min-width: 1400px) {
  .product__media {
    --product-media-width: calc(var(--grid-column-width) * 10 + var(--grid-gap) * 10);
  }

  .product__info {
    --product-info-width: calc(var(--grid-column-width) * 7 + var(--grid-gap) * 6);
  }

  .product--thumbnails-bottom .product__media {
    --product-media-width: calc(var(--grid-column-width) * 9 + var(--grid-gap) * 8);
  }

  .product--thumbnails-bottom .product__info {
    --product-info-width: calc(var(--grid-column-width) * 8 + var(--grid-gap) * 7);
  }
}

.product__media {
  display: block;
}

.product__media-list-wrapper {
  margin-inline-start: auto;
  margin-inline-end: auto;
  position: relative;
}

.product__media-list, .product__media-item {
  text-align: center;
  min-width: 100%;
  display: block;
}

.product__media-item {
  width: 100%;
}

.product__media-list:not(.flickity-enabled) .product__media-item:not(.is-selected), .product__media .is-filtered {
  display: none;
}

.product__media-image-wrapper {
  background: rgb(var(--secondary-background));
  border-radius: var(--block-border-radius-reduced);
  z-index: 0;
  overflow: hidden;
}

.product__media-nav {
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;
  margin-block-start: 18px;
  display: flex;
  position: relative;
}

.product__thumbnail-scroll-shadow {
  max-width: 100%;
}

.product__thumbnail-list {
  position: relative;
}

.product__thumbnail-list-inner {
  grid-auto-flow: column;
  align-items: start;
  display: grid;
}

.product__thumbnail-item {
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  padding: 2px;
  display: inline-block;
  position: relative;
}

.product__thumbnail {
  width: 76px;
  min-width: 76px;
  padding: 2px;
  position: relative;
}

.product__thumbnail, .product__thumbnail > img {
  border-radius: min(var(--block-border-radius), 4px);
}

.product__thumbnail .placeholder-background {
  display: block;
}

.product__thumbnail:after {
  content: "";
  box-shadow: 0 0 0 2px rgb(var(--text-color));
  border-radius: inherit;
  opacity: 0;
  width: 100%;
  height: 100%;
  transition: opacity .2s, transform .2s;
  position: absolute;
  top: 0;
  transform: scale(.9);
}

.product__thumbnail:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
  left: 0;
}

.product__thumbnail:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
  right: 0;
}

.product__thumbnail-item[aria-current] .product__thumbnail:after {
  opacity: 1;
  transform: scale(1);
}

.product__thumbnail-badge {
  position: absolute;
  top: 4px;
}

.product__thumbnail-badge:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 4px;
}

.product__thumbnail-badge:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 4px;
}

.product__view-in-space {
  justify-content: center;
  align-items: center;
  margin-block-start: 8px;
  margin-block-end: 8px;
  display: flex;
}

.product__view-in-space svg {
  margin-inline-end: 14px;
}

.product__view-in-space[data-shopify-xr-hidden] {
  visibility: hidden;
}

.product__zoom-button {
  border: 1px solid rgb(var(--border-color));
  border-radius: var(--button-border-radius);
  background: rgb(var(--root-background));
  color: rgb(var(--root-text-color));
  z-index: 1;
  justify-content: center;
  align-items: center;
  width: 36px;
  height: 36px;
  transition: opacity .25s ease-in-out, visibility .25s ease-in-out, transform .25s ease-in-out, color .25s ease-in-out;
  display: flex;
  position: absolute;
  bottom: 16px;
}

.product__zoom-button:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 16px;
}

.product__zoom-button:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 16px;
}

.product__zoom-button[hidden] {
  opacity: 0;
  visibility: hidden;
  transform: scale(.4);
}

@media screen and (max-width: 999px) {
  .product__media-list {
    margin-inline: calc(-1 * var(--container-gutter));
  }

  .product__media-item {
    padding-inline: var(--container-gutter);
  }

  .product__media-nav .dots-nav {
    padding-inline-start: 20px;
    padding-inline-end: 20px;
  }

  .product__thumbnail-list {
    margin-inline: calc(-1 * var(--container-gutter));
  }

  .product__thumbnail-list-inner {
    padding-inline: var(--container-gutter);
    width: max-content;
  }
}

@media screen and (min-width: 1000px) {
  .product__view-in-space {
    display: none;
  }

  .product__media-nav {
    margin-block-start: 16px;
  }

  .product__thumbnail-scroll-shadow {
    --scroll-shadow-size: 65px;
    --scroll-shadow-right: linear-gradient(to left, rgb(var(--background)), rgba(var(--background), 0));
    --scroll-shadow-left: linear-gradient(to right, rgb(var(--background)), rgba(var(--background), 0));
    --scroll-shadow-bottom: linear-gradient(to top, rgb(var(--background)), rgba(var(--background), 0));
    --scroll-shadow-top: linear-gradient(to bottom, rgb(var(--background)), rgba(var(--background), 0));
  }

  .product__thumbnail-list {
    overflow: hidden;
  }

  .product__thumbnail-list-inner {
    gap: 8px;
  }

  .product__thumbnail {
    width: 64px;
    min-width: 64px;
  }

  .product--thumbnails-left .product__media {
    flex-direction: row-reverse;
    align-items: flex-start;
    display: flex;
  }

  .product--thumbnails-left .product__media-nav {
    align-items: flex-start;
    margin-block-start: 0;
  }

  .product--thumbnails-left .product__media-list-wrapper {
    flex-grow: 1;
  }

  .product--thumbnails-left .product__media-prev-next {
    transform: rotate(90deg) scale(var(--scale-factor));
  }

  .product--thumbnails-left .product__thumbnail-list {
    max-height: calc((var(--product-media-width)  - 136px) / var(--largest-image-aspect-ratio));
    margin-inline-end: 36px;
  }

  .product--thumbnails-left .product__thumbnail-list-inner {
    grid-auto-flow: row;
  }

  .product--thumbnails-left .product__thumbnail {
    width: 60px;
    min-width: 60px;
  }
}

@media screen and (min-width: 1400px) {
  .product__media {
    padding-inline-start: 36px;
  }
}

@media screen and (pointer: fine) {
  .product__zoom-button:hover {
    color: rgba(var(--text-color), .7);
  }
}

.product-meta {
  border-bottom: 1px solid rgb(var(--border-color));
  margin: 24px 0;
  padding-block-end: 16px;
  display: block;
}

.product-meta__price-list-container {
  align-items: center;
  margin-block-start: -8px;
  display: flex;
}

.product-meta__label-list:not(:empty) {
  margin-inline-start: 16px;
}

.product-meta__reference {
  justify-content: space-between;
  align-items: center;
  margin-block-start: 10px;
  display: flex;
}

.product-meta__sku {
  letter-spacing: .45px;
  position: relative;
  top: 1px;
}

.product-meta__taxes-included {
  margin-block-start: 0;
}

.product-meta__aside {
  justify-content: space-between;
  margin-block-start: 18px;
  display: flex;
}

.product-meta__share {
  align-items: center;
  display: flex;
}

.product-meta__share-label {
  margin-inline-end: 20px;
}

.product-meta__share-button-list {
  grid-auto-flow: column;
  justify-content: flex-start;
  align-items: center;
  gap: 18px;
  display: inline-grid;
}

.product-meta__reviews-badge .rating__caption {
  position: relative;
}

.product-meta__reviews-badge .rating__caption:after {
  content: "";
  transform-origin: var(--transform-origin-end);
  background: currentColor;
  width: 100%;
  height: 1px;
  transition: transform .3s;
  position: absolute;
  bottom: 2px;
  left: 0;
  transform: scaleX(0);
}

@media screen and (min-width: 1000px) {
  .product-meta {
    margin-block-start: 0;
  }

  .product-meta__title {
    margin-block-end: 24px;
  }
}

@media screen and (pointer: fine) {
  .product-meta__reviews-badge:hover .spr-badge-caption:after, .product-meta__reviews-badge:hover .rating__caption:after {
    transform-origin: var(--transform-origin-start);
    transform: scaleX(1);
  }
}

.product-form {
  row-gap: 16px;
  display: grid;
}

.product-form__variants {
  display: block;
}

.product-form__variants[hide-sold-out-variants] .combo-box__option-item.is-disabled:not([aria-selected="true"]) {
  display: none;
}

.product-form__variants[hide-sold-out-variants] :where(.block-swatch.is-disabled, .color-swatch.is-disabled, .variant-swatch.is-disabled):not(:has(:checked)) {
  display: none;
}

.product-form__option-info, .product-form__quantity-label {
  margin-block-end: 8px;
  display: flex;
}

.product-form__option-value {
  margin-inline-start: 8px;
}

.product-form__option-link {
  margin-inline-start: auto;
}

.no-js .product-form__option-selector {
  display: none;
}

.product-form__option-selector + .product-form__option-selector {
  margin-block-start: 16px;
}

.product-form__payment-container {
  gap: 10px;
  margin-block-start: 8px;
  display: grid;
}

.product-form__description {
  margin-block-start: 8px;
  margin-block-end: 8px;
}

.product-form__image--center {
  text-align: center;
}

.product-form__image--right {
  text-align: end;
}

.product-form__image img {
  width: 100%;
}

shopify-payment-terms {
  display: block;
}

.product-form__view-details {
  text-align: center;
  margin-block-start: 24px;
}

.product-form__store-availability-container {
  display: block;
}

.product-form__store-availability-container:empty {
  display: none;
}

@media screen and (min-width: 741px) {
  .product-form__payment-container {
    margin-block-start: 16px;
  }
}

.inventory {
  color: rgb(var(--product-in-stock-text-color));
}

.inventory--low {
  color: rgb(var(--product-low-stock-text-color));
}

.product-content {
  margin-block-start: 36px;
  margin-block-end: 36px;
  display: flex;
}

@media screen and (max-width: 999px) {
  .product-content {
    flex-direction: column-reverse;
  }

  .product-content__tabs + .product-content__featured-products {
    margin-block-end: 38px;
  }
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .product-content {
    margin-block-start: 48px;
    margin-block-end: 48px;
  }
}

@media screen and (min-width: 1000px) {
  .product-content {
    justify-content: space-between;
    margin-block-start: 80px;
    margin-block-end: 80px;
  }
}

@media screen and (min-width: 1200px) {
  .product-content {
    margin-inline: calc(var(--grid-column-width)  + var(--grid-gap));
  }
}

.product-tabs__trust-list:not(:first-child) {
  margin-block-start: 32px;
}

.product-tabs__tab-item-wrapper:not([hidden]) {
  display: block;
}

@media screen and (max-width: 740px) {
  .product-content__tabs {
    margin: 0 calc(-1 * var(--container-gutter));
  }

  .product-tabs__tab-item-wrapper {
    --anchor-offset: 0px;
    padding: 0 var(--container-gutter);
  }

  .product-tabs__trust-title:not(:last-child) {
    margin-block-end: 24px;
  }
}

@media screen and (max-width: 999px) {
  .product-tabs__tab-item-wrapper {
    border-top: 1px solid rgb(var(--border-color));
    display: block;
  }

  .product-tabs__tab-item-wrapper:last-child {
    border-bottom: 1px solid rgb(var(--border-color));
  }

  .product-tabs__tab-item-content {
    margin-block-start: -2px;
    margin-block-end: 25px;
  }
}

@media screen and (min-width: 741px) {
  .product-tabs__trust-list:not(:first-child) {
    margin-block-start: 15px;
    padding-inline-start: 0;
  }

  .product-tabs__trust-list:first-child {
    text-align: center;
  }

  .product-tabs__trust-title {
    margin-block-start: 25px;
    display: inline-flex;
  }

  .product-tabs__trust-title:not(:last-child) {
    margin-inline-end: 35px;
  }
}

@media screen and (min-width: 1000px) {
  .product-content__tabs {
    width: calc(var(--grid-column-width) * 10 + var(--grid-gap) * 10);
    flex: none;
  }

  .product-content__tabs:only-child {
    width: calc(var(--grid-column-width) * 12 + var(--grid-gap) * 12);
    margin-inline-start: auto;
    margin-inline-end: auto;
  }

  .product-tabs__tab-item-wrapper .collapsible {
    visibility: visible;
    height: auto;
    overflow: auto;
  }
}

.product-content__featured-products-title {
  margin-block-end: 0;
}

.product-content__featured-products-list {
  grid-gap: var(--grid-gap);
  grid-template-columns: 1fr 1fr;
  margin-block-start: 20px;
  display: grid;
}

@media screen and (max-width: 740px) {
  .product-content__featured-products .product-item {
    scroll-snap-align: center;
    scroll-snap-stop: always;
    scroll-snap-margin: var(--container-gutter);
    scroll-margin: var(--container-gutter);
    flex-direction: row;
    align-items: center;
  }

  .product-content__featured-products-list {
    grid-template-columns: none;
    grid-auto-columns: minmax(64vw, 1fr);
    grid-auto-flow: column;
  }

  .product-content__featured-products .product-item__image-wrapper {
    flex: none;
    width: 104px;
    margin-block-start: 0;
    margin-block-end: 0;
    margin-inline-start: 0;
    margin-inline-end: 24px;
  }

  .product-content__featured-products .product-item__info {
    text-align: start;
  }

  .product-content__featured-products .price-list {
    justify-content: flex-start;
  }

  .product-content__featured-products .product-item__link {
    margin-inline-start: 0;
  }
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .product-content__featured-products-list {
    grid-template-columns: 214px 214px;
  }
}

@media screen and (min-width: 1000px) {
  .product-content__featured-products {
    width: calc(var(--grid-column-width) * 9 + var(--grid-gap) * 8);
    flex: none;
  }

  .product-content__featured-products-list {
    margin-block-start: 32px;
  }
}

@media screen and (min-width: 1200px) {
  .product-content__featured-products {
    width: calc(var(--grid-column-width) * 7 + var(--grid-gap) * 6);
  }
}

.quick-buy-product {
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  display: flex;
}

.quick-buy-product__image {
  flex: none;
  align-self: flex-start;
  width: 65px;
  margin-inline-end: 20px;
}

@media screen and (max-width: 740px) {
  .quick-buy-product {
    padding-block-start: 14px;
    padding-block-end: 14px;
    padding-inline-end: 32px;
  }

  .popover--quick-buy .product-form {
    padding-inline-start: 24px;
    padding-inline-end: 24px;
  }

  .popover--quick-buy .product-form > :first-child:not(.product-form__buy-buttons) {
    padding-block-start: 16px;
  }

  .popover--quick-buy .product-form__buy-buttons {
    margin-inline: calc(-1 * var(--container-gutter));
    padding: 16px;
    padding-block-end: max(16px, env(safe-area-inset-bottom, 0px)  + 16px);
  }

  .popover--quick-buy .product-form__buy-buttons:not(:only-child) {
    border-top: 1px solid rgb(var(--border-color));
    background: rgb(var(--root-background));
    margin-block-start: 8px;
    position: sticky;
    bottom: 0;
  }

  .popover--quick-buy .product-form__payment-container {
    margin-block-start: 0;
  }
}

@media screen and (min-width: 741px) {
  .quick-buy-product {
    margin-block-start: 32px;
    margin-block-end: 24px;
    margin-inline-start: 0;
    margin-inline-end: 0;
  }

  .quick-buy-product__image {
    width: 114px;
    margin-inline-end: 32px;
  }
}

.product-sticky-form {
  z-index: 2;
  width: 100%;
  padding-block-end: max(24px, env(safe-area-inset-bottom, 0px)  + 24px);
  transition: opacity .25s ease-in-out, transform .25s ease-in-out, visibility .25s ease-in-out;
  display: block;
  position: fixed;
  bottom: 0;
}

.product-sticky-form:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 0;
}

.product-sticky-form:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 0;
}

.product-sticky-form[hidden] {
  opacity: 0;
  visibility: hidden;
  transform: translateY(100%);
}

@media screen and (max-width: 999px) {
  .product-sticky-form .product-form__add-button {
    width: 100%;
  }
}

@media screen and (min-width: 1000px) {
  .product-sticky-form {
    background: rgb(var(--background));
    border-bottom: 1px solid rgb(var(--border-color));
    inset-block: calc(var(--header-height, 0px) * var(--enable-sticky-header)  + var(--announcement-bar-height, 0px) * var(--enable-sticky-announcement-bar, 0)) auto;
    box-shadow: 0 6px 5px -5px rgba(var(--border-color), .4), 0 1px rgb(var(--border-color)) inset;
    padding-block-start: 16px;
    padding-block-end: 16px;
    transition: opacity .25s ease-in-out, transform .25s ease-in-out, visibility .25s ease-in-out;
  }

  .product-sticky-form[hidden] {
    transform: translateY(-100%);
  }

  .product-sticky-form:hover {
    z-index: 4;
  }

  .product-sticky-form__form, .product-sticky-form__variants {
    margin-inline-start: auto;
    display: flex;
  }

  .product-sticky-form__content-wrapper, .product-sticky-form__inner {
    align-items: center;
    display: flex;
  }

  .product-sticky-form__image-wrapper {
    flex: none;
    width: 55px;
    margin-inline-end: 18px;
  }

  .product-sticky-form__variants .select-wrapper + .select-wrapper {
    margin-inline-start: -1px;
  }

  .product-sticky-form__variants .select-wrapper:not(:first-child):not(:last-child) .select {
    border-radius: 0;
  }

  .product-sticky-form__variants .select-wrapper:first-child:not(:only-child) .select:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .product-sticky-form__variants .select-wrapper:first-child:not(:only-child) .select:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .product-sticky-form__variants .select-wrapper:last-child:not(:only-child) .select:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .product-sticky-form__variants .select-wrapper:last-child:not(:only-child) .select:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .product-sticky-form__variants .select {
    min-width: 150px;
    max-width: 300px;
  }

  @supports (height: min(1px, 2px)) {
    .product-sticky-form__variants .select {
      min-width: min(max(var(--largest-option-width), 150px), 300px);
    }
  }

  .product-sticky-form .button, .product-sticky-form .select {
    height: 48px;
    line-height: 48px;
  }

  .product-sticky-form__payment-container {
    margin-inline-start: 10px;
  }
}

.gift-card-recipient__fields {
  margin-block-start: 10px;
}

.product-item-meta {
  display: block;
}

.product-item-meta__vendor {
  margin-block-end: 6px;
  display: block;
}

.product-item-meta__title {
  margin-block-end: 4px;
  line-height: 1.6;
  display: block;
}

.product-item-meta__reviews-badge {
  margin-block-start: 2px;
  display: block;
}

.product-item-meta__color-count, .product-item-meta__reviews-badge + .product-item-meta__color-count {
  margin-block-start: 4px;
}

.product-item-meta__reviews-badge .spr-icon {
  width: 12px;
  height: 12px;
}

.product-item-meta__swatch-list {
  overflow-wrap: anywhere;
  justify-content: center;
  gap: 6px;
  margin-block-start: 10px;
  margin-block-end: 2px;
}

.product-item-meta__property-list {
  grid-auto-flow: row;
  margin-block-start: 6px;
  margin-block-end: 4px;
  display: grid;
}

@media screen and (min-width: 741px) {
  .product-item-meta__property-list {
    margin-block-start: 6px;
    margin-block-end: 6px;
  }

  .product-item-meta__color-count {
    margin-block-start: 8px;
  }

  .product-item-meta__title {
    line-height: 1.5;
  }
}

.product-item {
  flex-direction: column;
  display: flex;
  position: relative;
}

.product-item__image-wrapper {
  margin-block-end: 16px;
  display: block;
  position: relative;
  overflow: hidden;
}

.product-item__image-wrapper--placeholder {
  fill: currentColor;
}

.product-item__label-list {
  z-index: 1;
  position: absolute;
  top: 10px;
}

.product-item__label-list:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 10px;
}

.product-item__label-list:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 10px;
}

.product-item__image-wrapper--multiple .product-item__primary-image, .product-item__image-wrapper--multiple .product-item__secondary-image {
  transition: opacity .3s ease-in-out, visibility .3s ease-in-out;
}

.product-item__aspect-ratio {
  isolation: isolate;
}

.product-item__primary-image, .product-item__secondary-image {
  border-radius: var(--block-border-radius-reduced);
}

.product-item__secondary-image {
  visibility: hidden;
  opacity: 0;
  object-fit: contain;
  object-position: center;
  display: none;
  position: absolute;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
}

.product-item__info {
  text-align: center;
  flex-direction: column;
  flex-grow: 1;
  justify-content: flex-start;
  display: flex;
}

.product-item__info--with-button {
  justify-content: space-between;
}

.product-item__cta {
  margin-block-start: 16px;
}

.product-item__quick-form {
  visibility: hidden;
  opacity: 0;
  width: 100%;
  padding: 10px;
  transition: visibility .2s ease-in-out, opacity .2s ease-in-out;
  position: absolute;
  bottom: 0;
}

.product-item__quick-form:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 0;
}

.product-item__quick-form:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 0;
}

.product-item__quick-buy-button {
  background: rgb(var(--root-background));
  color: rgb(var(--root-text-color));
  border: 1px solid rgb(var(--root-border-color));
  padding: 5px;
  position: absolute;
  bottom: 12px;
}

.product-item__quick-buy-button:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 12px;
}

.product-item__quick-buy-button:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 12px;
}

.product-item__link {
  margin-block-start: 6px;
  margin-block-end: 0;
  margin-inline-start: auto;
  margin-inline-end: auto;
  display: inline-block;
}

@media screen and (pointer: fine) {
  .product-item__secondary-image {
    display: block;
  }

  .product-item__image-wrapper--multiple:hover .product-item__primary-image {
    visibility: hidden;
    opacity: 0;
  }

  .product-item__image-wrapper--multiple:hover .product-item__secondary-image, .product-item__image-wrapper:hover .product-item__quick-form {
    visibility: visible;
    opacity: 1;
  }
}

@media screen and (pointer: fine) and (prefers-reduced-motion: no-preference) {
  .product-item__quick-form {
    transition: visibility .2s ease-in-out, opacity .2s ease-in-out, transform .2s ease-in-out;
    transform: translateY(16px);
  }

  .product-item__image-wrapper:hover .product-item__quick-form {
    visibility: visible;
    opacity: 1;
    transform: translateY(0);
  }
}

@media screen and not (pointer: fine) {
  .product-item__quick-form {
    visibility: visible;
    opacity: 1;
  }
}

@media screen and (min-width: 1000px) {
  .product-item__image-wrapper {
    margin-block-end: 24px;
  }
}

.product-list {
  --product-list-column-gap: var(--grid-gap);
}

.product-list:not([hidden]) {
  display: block;
  position: relative;
}

.product-list__inner {
  grid-template-columns: repeat(auto-fit, calc(100% / var(--section-products-per-row)  - var(--product-list-column-gap) * (var(--section-products-per-row)  - 1) / var(--section-products-per-row)));
  gap: var(--product-list-block-spacing) var(--product-list-column-gap);
  display: grid;
  overflow: hidden;
}

.product-list__inner--scroller {
  overflow-x: auto;
}

.product-list--center .product-list__inner {
  justify-content: safe center;
}

@media screen and (max-width: 740px) {
  .product-list {
    --product-list-column-gap: 12px;
  }

  .product-list__inner:not(.product-list__inner--scroller) {
    margin-inline: calc(-1 * var(--container-gutter) / 2);
  }
}

@media screen and (max-width: 999px) {
  .product-list__inner--scroller {
    padding-inline: var(--container-gutter);
    grid-template-columns: none;
    grid-auto-columns: 52vw;
    grid-auto-flow: column;
    min-width: min-content;
  }
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .product-list__inner--scroller {
    grid-auto-columns: 35vw;
  }
}

@media screen and (min-width: 1000px) {
  .product-list {
    --item-width: calc((var(--container-max-width-minus-gutters)  - 56px) / var(--section-products-per-row)  - (var(--grid-gap) / var(--section-products-per-row) * (var(--section-products-per-row)  - 1)));
  }

  @supports (width: max(1px, 2px)) {
    .product-list {
      --item-width: calc((min(100vw - var(--container-gutter) * 2, var(--container-max-width-minus-gutters))  - 56px) / var(--section-products-per-row)  - (var(--grid-gap) / var(--section-products-per-row) * (var(--section-products-per-row)  - 1)));
    }
  }

  .product-list__prev-next {
    pointer-events: none;
    top: calc(var(--item-width) / var(--smallest-image-aspect-ratio) / 2 - 28px);
    justify-content: space-between;
    width: 100%;
    display: flex;
    position: absolute;
  }

  .product-list__arrow {
    z-index: 1;
    opacity: 0;
    visibility: hidden;
    transition: opacity .15s ease-in-out, visibility .15s ease-in-out, transform .15s ease-in-out;
    transform: scale(.5);
  }

  .product-list:hover .product-list__arrow:not([disabled]) {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
  }

  .product-list__inner--scroller {
    grid-template-columns: none;
    grid-auto-flow: column;
    grid-auto-columns: calc(100% / var(--section-products-per-row, 4)  - (var(--grid-gap) / var(--section-products-per-row, 4) * (var(--section-products-per-row, 4)  - 1)));
    margin-inline-start: 28px;
    margin-inline-end: 28px;
    overflow: hidden;
  }

  .product-list__inner--desktop-no-scroller {
    margin-inline-start: 0;
    margin-inline-end: 0;
  }
}

@media screen and not (pointer: fine) {
  .product-list__arrow:not([disabled]) {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
  }
}

.promotion-block-list {
  --promotion-block-gutter: var(--container-gutter);
  margin: calc(-1 * var(--promotion-block-gutter) / 2);
  flex-wrap: wrap;
  justify-content: center;
  display: flex;
}

.promotion-block {
  --promotion-block-padding: 32px;
  --promotion-block-min-height: 180px;
  margin: calc(var(--promotion-block-gutter) / 2);
  padding: var(--promotion-block-padding);
  background: rgb(var(--section-block-background));
  color: rgb(var(--text-color));
  min-height: var(--promotion-block-min-height);
  align-items: var(--section-blocks-alignment, flex-end);
  border-radius: var(--block-border-radius-reduced);
  width: 100%;
  min-width: 0;
  display: flex;
  position: relative;
  overflow: hidden;
}

.promotion-block--medium {
  --promotion-block-min-height: 210px;
}

.promotion-block--large {
  --promotion-block-min-height: 250px;
}

.promotion-block__content-wrapper {
  width: 100%;
  position: relative;
}

.promotion-block:hover .link {
  -webkit-text-decoration-color: rgb(var(--text-color));
  text-decoration-color: rgb(var(--text-color));
}

.promotion-block__image {
  object-fit: cover;
  object-position: center;
  border-radius: var(--block-border-radius-reduced);
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
}

.promotion-block__image:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 0;
}

.promotion-block__image:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 0;
}

.promotion-block--quote:before {
  content: "";
  background: rgb(var(--text-color));
  opacity: .3;
  pointer-events: none;
  width: 50px;
  height: 40px;
  position: absolute;
  top: var(--promotion-block-padding);
  -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDEgODAiPiAgICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTAgNDEuNzdWMGg0MS42MDR2NDEuNzdMMjAuMDI2IDgwSC45ODdMMjEuNzIgNDEuNzdIMHptNTkuMzk2IDBWMEgxMDF2NDEuNzdMNzkuNDIyIDgwSDYwLjM4M2wyMC43MzItMzguMjNINTkuMzk2eiIgZmlsbD0iY3VycmVudENvbG9yIiAvPjwvc3ZnPg==");
  mask-image: url("data:image/svg+xml;base64,PHN2ZyBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDEgODAiPiAgICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTAgNDEuNzdWMGg0MS42MDR2NDEuNzdMMjAuMDI2IDgwSC45ODdMMjEuNzIgNDEuNzdIMHptNTkuMzk2IDBWMEgxMDF2NDEuNzdMNzkuNDIyIDgwSDYwLjM4M2wyMC43MzItMzguMjNINTkuMzk2eiIgZmlsbD0iY3VycmVudENvbG9yIiAvPjwvc3ZnPg==");
  -webkit-mask-size: 50px 40px;
  mask-size: 50px 40px;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
}

.promotion-block--quote:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  left: var(--promotion-block-padding);
}

.promotion-block--quote:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  right: var(--promotion-block-padding);
}

.promotion-block--quote .promotion-block__content-wrapper {
  margin-block-start: 50px;
}

.promotion-block--video {
  padding: 0 !important;
}

.promotion-block .video-wrapper {
  --video-height: calc(var(--promotion-block-min-height)  + 60px);
  min-height: var(--promotion-block-min-height);
  height: 100%;
}

.promotion-block .video-wrapper--native {
  width: 100%;
}

.promotion-block--products {
  text-align: center;
  justify-content: center;
  align-items: center;
  padding: 24px !important;
}

.promotion-block__product-list-wrapper {
  display: block;
}

.promotion-block__product-list {
  scroll-snap-type: x mandatory;
  flex-wrap: nowrap;
  align-items: center;
  margin-inline-start: -24px;
  margin-inline-end: -24px;
  display: flex;
  overflow: hidden;
}

.promotion-block__product-list-item {
  scroll-snap-align: center;
  scroll-snap-stop: always;
  min-width: 100%;
  display: block;
}

.promotion-block__product-list-item .placeholder-background {
  background: none;
}

.promotion-block__product-list-prev-next {
  pointer-events: none;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  display: flex;
  position: absolute;
  top: calc(50% - 20px);
}

.promotion-block__product-list .product-item__image-wrapper {
  width: 100%;
  max-width: 150px;
  margin-inline-start: auto;
  margin-inline-end: auto;
}

@media screen and not (pointer: fine) {
  .promotion-block__product-list {
    overflow: auto;
  }
}

@media screen and (max-width: 740px) {
  .promotion-block-list--scrollable {
    flex-wrap: nowrap;
  }

  .promotion-block-list--scrollable .promotion-block {
    scroll-snap-align: center;
    scroll-snap-stop: always;
    scroll-snap-margin: var(--promotion-block-gutter);
    scroll-margin: var(--promotion-block-gutter);
    width: 81vw;
  }
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .promotion-block {
    flex: 1 0 calc(50% - var(--container-gutter));
  }

  .promotion-block .newsletter__form .input-row {
    grid-template-columns: 1fr auto;
  }
}

@media screen and (min-width: 1000px) {
  .promotion-block {
    flex: 1 0 calc(33.3333% - var(--promotion-block-gutter));
  }

  .promotion-block:not(.promotion-block--expand):only-child {
    max-width: 50%;
  }

  .promotion-block--highlight {
    flex-basis: calc(66.6666% - var(--promotion-block-gutter));
  }

  .promotion-block--quote:before {
    width: 60px;
    height: 48px;
    -webkit-mask-size: 60px 48px;
    mask-size: 60px 48px;
  }

  .promotion-block:not(.promotion-block--highlight) .newsletter__form .input-row {
    grid-template-columns: none;
  }
}

@media screen and (min-width: 1200px) {
  .promotion-block {
    --promotion-block-min-height: 250px;
    --promotion-block-padding: 48px;
  }

  .promotion-block--compact {
    --promotion-block-padding: 40px;
  }

  .promotion-block--medium {
    --promotion-block-min-height: 320px;
  }

  .promotion-block--large {
    --promotion-block-min-height: 370px;
  }
}

@media screen and (min-width: 1400px) {
  .promotion-block .newsletter__form .input-row {
    grid-template-columns: 1fr auto;
  }
}

.main-search__form {
  --form-input-field-height: 60px;
  max-width: 390px;
  margin-inline-start: auto;
  margin-inline-end: auto;
  position: relative;
}

.main-search__input {
  padding-inline-end: 55px;
}

.main-search__submit {
  position: absolute;
  top: calc(50% - 10px);
}

.main-search__submit:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 20px;
}

.main-search__submit:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 20px;
}

.main-search__empty-text {
  margin-block-start: 40px;
}

.main-search__results {
  display: block;
}

.main-search__form + .tabs-nav {
  margin-block-start: 38px;
}

@media screen and (min-width: 741px) {
  .main-search__form {
    --form-input-field-height: 80px;
  }

  .main-search__submit:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 32px;
  }

  .main-search__submit:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 32px;
  }

  .main-search__input {
    padding-inline-start: 30px;
    padding-inline-end: 80px;
  }

  .main-search__form + .tabs-nav {
    margin-block-start: 68px;
  }
}

.shop-the-look, .shop-the-look__item {
  display: block;
  position: relative;
  overflow: hidden;
}

.shop-the-look__item {
  background: rgb(var(--secondary-background));
}

.shop-the-look__item[hidden] {
  visibility: hidden;
  z-index: -1;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.shop-the-look__item[hidden] .popover, .shop-the-look__item[hidden] .drawer {
  display: none;
}

.shop-the-look__image {
  width: 100%;
}

.shop-the-look__product-wrapper {
  position: absolute;
}

.shop-the-look__dot {
  background: var(--section-dot-inner-background);
  box-shadow: 0 0 0 8px rgb(var(--section-dot-background)) inset, 0 1px 5px #00000026;
  border-radius: 100%;
  width: 24px;
  height: 24px;
  display: block;
  position: relative;
}

@keyframes shopTheLookDotKeyframe {
  0% {
    opacity: 1;
    transform: scale(.4);
  }

  100% {
    opacity: 0;
    transform: scale(1.3);
  }
}

.shop-the-look__dot:after {
  content: "";
  border: 2px solid rgba(var(--section-dot-background), .6);
  border-radius: 100%;
  width: 32px;
  height: 32px;
  animation: 2s ease-in-out infinite shopTheLookDotKeyframe;
  position: absolute;
  top: -4px;
  left: -4px;
}

.shop-the-look__product-wrapper {
  margin-block-start: -12px;
  margin-inline-start: -12px;
}

.shop-the-look__product {
  left: calc(100% + 28px);
  background: rgb(var(--background));
  visibility: hidden;
  opacity: 0;
  will-change: transform;
  transform-origin: top var(--transform-origin-start);
  z-index: 1;
  border-radius: min(var(--block-border-radius), 4px);
  align-items: center;
  width: max-content;
  max-width: 46vw;
  padding-block-start: 15px;
  padding-block-end: 15px;
  padding-inline-start: 32px;
  padding-inline-end: 15px;
  transition: visibility .4s cubic-bezier(.75, 0, .175, 1), opacity .4s cubic-bezier(.75, 0, .175, 1), transform .4s cubic-bezier(.75, 0, .175, 1);
  display: flex;
  position: absolute;
  top: 50%;
  transform: scale(.8)translateY(-50%);
  box-shadow: 0 1px 5px #0000001a;
}

.shop-the-look__product[open] {
  visibility: visible;
  opacity: 1;
  transform: scale(1)translateY(-50%);
}

.shop-the-look__product:before {
  content: "";
  border-style: solid;
  border-color: transparent rgb(var(--background)) transparent transparent;
  border-width: 8px;
  width: 0;
  height: 0;
  position: absolute;
  right: 100%;
}

.shop-the-look__product--reverse {
  transform-origin: top var(--transform-origin-end);
  left: auto;
  right: calc(100% + 28px);
}

.shop-the-look__product--reverse:before {
  border-color: transparent transparent transparent rgb(var(--background));
  left: 100%;
  right: auto;
}

.shop-the-look__product-bottom-wrapper {
  grid-auto-flow: column;
  justify-content: flex-start;
  gap: 12px;
  display: grid;
}

.shop-the-look__product-image {
  flex: none;
  width: 72px;
  margin-inline-end: 24px;
}

.shop-the-look__product-vendor {
  max-width: max-content;
  margin-block-end: 1px;
  display: block;
}

.shop-the-look__product-title {
  margin-block-end: 2px;
  display: block;
}

.shop-the-look__nav {
  background: rgb(var(--background));
  color: rgb(var(--text-color));
  border: 1px solid rgb(var(--border-color));
  border-radius: min(var(--button-border-radius), 10px);
  z-index: 1;
  display: block;
  position: absolute;
  bottom: 24px;
  overflow: hidden;
  transform: translateZ(0);
}

.shop-the-look__prev-next-buttons {
  flex-direction: row;
  justify-content: center;
  align-items: center;
  display: flex;
}

.shop-the-look__arrow {
  border: none;
  border-radius: 0;
}

.shop-the-look__arrow:first-child {
  border-inline-end: 1px solid rgb(var(--border-color));
}

@media screen and (min-width: 741px) {
  .shop-the-look__nav {
    bottom: 40px;
  }
}

@media screen and (min-width: 1000px) {
  .shop-the-look__label {
    text-align: center;
    border-block-end: 1px solid rgb(var(--border-color));
    padding-inline-start: 20px;
    padding-inline-end: 20px;
    line-height: 56px;
  }

  .shop-the-look__arrow:last-child {
    border-inline-start: 1px solid rgb(var(--border-color));
  }

  .shop-the-look__counter {
    text-align: center;
    flex-grow: 1;
    padding-inline-start: 20px;
    padding-inline-end: 20px;
    line-height: 1.4;
    overflow: hidden;
  }

  .shop-the-look__counter-page {
    position: relative;
  }

  .shop-the-look__counter-page-base {
    opacity: 0;
  }

  .shop-the-look__counter-page-transition {
    height: 100%;
    line-height: normal;
    display: inline-block;
    position: absolute;
    top: 0;
  }

  .shop-the-look__counter-page-transition:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 0;
  }

  .shop-the-look__counter-page-transition:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 0;
  }

  .shop-the-look__counter-page-transition[hidden] {
    visibility: hidden;
    transform: translateY(100%);
  }
}

.shopify-challenge__container {
  margin-block: var(--vertical-breather) !important;
}

.shopify-challenge__container .shopify-challenge__button {
  margin-block-start: 30px;
}

.slideshow {
  --slideshow-min-height: 0;
  display: block;
  position: relative;
}

.slideshow--small {
  --slideshow-min-height: 120vw;
}

.slideshow--medium {
  --slideshow-min-height: 133vw;
}

.slideshow--large {
  --slideshow-min-height: 160vw;
}

.slideshow--fit {
  --slideshow-min-height: calc(var(--window-height)  - var(--header-height, 0px) * (-1 * (var(--enable-transparent-header)  - 1))  - var(--announcement-bar-height, 0px));
}

@supports (min-height: 100svh) {
  .slideshow--fit {
    --slideshow-min-height: calc(100svh - var(--header-height, 0px) * (-1 * (var(--enable-transparent-header)  - 1))  - var(--announcement-bar-height, 0px));
  }
}

.slideshow, .slideshow__slide-list, .slideshow__slide, .slideshow__slide-inner {
  min-height: var(--slideshow-min-height);
}

.slideshow__slide {
  z-index: 1;
  display: block;
  position: relative;
}

.slideshow__slide:not(:only-child) {
  cursor: grab;
  -webkit-user-select: none;
  user-select: none;
}

.slideshow__slide[hidden] {
  visibility: hidden;
  z-index: 0;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.slideshow__slide:not(:only-child) .slideshow__text-wrapper--bottom {
  padding-block-end: calc(var(--vertical-breather)  + 30px);
}

.slideshow__slide-inner {
  align-items: var(--section-blocks-alignment);
  width: 100%;
  height: 100%;
  display: flex;
  position: relative;
}

.slideshow__slide-inner:before {
  content: "";
  width: 0;
  padding-block-end: calc(100 / var(--mobile-image-aspect-ratio) * 1%);
  display: block;
}

.slideshow__image-wrapper {
  overflow: hidden;
}

.slideshow__image-wrapper, .slideshow__image {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
}

:is(.slideshow__image-wrapper:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)), .slideshow__image:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  left: 0;
}

:is(.slideshow__image-wrapper:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)), .slideshow__image:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  right: 0;
}

.slideshow__image {
  object-fit: cover;
  object-position: center;
}

.slideshow__image--placeholder {
  background: rgb(var(--secondary-background));
  width: auto;
}

.slideshow__image-wrapper:before {
  content: "";
  background: rgba(var(--section-blocks-overlay-color), var(--section-blocks-overlay-opacity));
  pointer-events: none;
  z-index: 1;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
}

.slideshow__image-wrapper:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  left: 0;
}

.slideshow__image-wrapper:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  right: 0;
}

@supports (width: max(1px, 2px)) {
  .slideshow__text-wrapper--top {
    padding-block-start: max(var(--vertical-breather), calc((var(--header-height)  + 25px) * var(--enable-transparent-header)));
  }
}

@media screen and (min-width: 1000px) {
  @supports (width: max(1px, 2px)) {
    .slideshow__text-wrapper--top {
      padding-block-start: max(var(--vertical-breather), calc((var(--header-height)  + 40px) * var(--enable-transparent-header)));
    }
  }
}

@keyframes slideshowProgressBarAnimation {
  from {
    transform: scaleX(0);
  }

  to {
    transform: scaleX(1);
  }
}

.slideshow__nav {
  z-index: 1;
  pointer-events: none;
  justify-content: center;
  display: flex;
  position: absolute;
  bottom: 15px;
  left: 0;
  right: 0;
}

.slideshow__progress-bar {
  pointer-events: auto;
  flex-basis: 48px;
  margin: 0 8px;
  padding: 18px 0;
  position: relative;
}

.slideshow__progress-bar:before, .slideshow__progress-bar:after {
  content: "";
  background: rgba(var(--progress-bar-color), .5);
  width: 100%;
  height: 2px;
  transition: background .2s ease-in-out;
  position: absolute;
  top: calc(50% - 1px);
}

.slideshow__progress-bar:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  left: 0;
}

.slideshow__progress-bar:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
  left: 0;
}

.slideshow__progress-bar:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  right: 0;
}

.slideshow__progress-bar:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
  right: 0;
}

.slideshow__progress-bar:after {
  transform-origin: var(--transform-origin-start);
  background: rgb(var(--progress-bar-color));
  transform: scaleX(0);
}

.slideshow__progress-bar[aria-current="true"]:after {
  animation: slideshowProgressBarAnimation var(--section-autoplay-duration) linear;
  animation-play-state: var(--section-animation-play-state, paused);
  animation-fill-mode: forwards;
}

@media screen and (min-width: 741px) {
  .slideshow--small {
    --slideshow-min-height: 70vw;
  }

  .slideshow--medium {
    --slideshow-min-height: 90vw;
  }

  .slideshow--large {
    --slideshow-min-height: 105vw;
  }
}

@media screen and (min-width: 1000px) {
  .slideshow--small {
    --slideshow-min-height: 42vw;
  }

  .slideshow--medium {
    --slideshow-min-height: 48vw;
  }

  .slideshow--large {
    --slideshow-min-height: 54vw;
  }

  .slideshow__slide--split .slideshow__image-wrapper {
    width: 50%;
  }

  .slideshow__slide--split .slideshow__image-wrapper--secondary {
    width: calc(50% + 1px);
  }

  .slideshow__slide--split .slideshow__image-wrapper--secondary:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: calc(50% - 1px);
  }

  .slideshow__slide--split .slideshow__image-wrapper--secondary:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: calc(50% - 1px);
  }

  .slideshow__slide-inner:before {
    padding-block-end: calc(100 / var(--image-aspect-ratio) * 1%);
  }

  .slideshow__nav {
    bottom: 23px;
  }

  .slideshow__progress-bar {
    flex-basis: 64px;
  }
}

@media screen and (min-width: 1200px) {
  .slideshow--small {
    --slideshow-min-height: 38vw;
  }

  .slideshow--medium {
    --slideshow-min-height: 40vw;
  }

  .slideshow--large {
    --slideshow-min-height: 48vw;
  }
}

.store-availability-container:not(:first-child) {
  margin-block-start: 8px;
}

.store-availability-information, .store-availability-list__stock {
  align-items: center;
  display: flex;
}

.store-availability-information {
  align-items: baseline;
}

.store-availability-information .icon--store-availability-out-of-stock {
  position: relative;
  top: 1px;
}

.store-availability-information-container {
  margin-inline-start: 8px;
}

.store-availability-information__title, .store-availability-information__link {
  display: block;
}

.store-availability-information__title {
  margin-block-end: -2px;
}

.store-availability-information__link {
  margin-block-start: 10px;
}

.store-availabilities-modal__product-title {
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 85%;
  margin: 0;
  overflow: hidden;
}

.store-availabilities-modal__variant-title {
  margin-block-start: 6px;
}

.store-availabilities-list {
  margin-block-start: 10px;
}

.store-availability-list__item {
  padding-block-start: 24px;
}

.store-availability-list__item + .store-availability-list__item {
  border-top: 1px solid rgb(var(--border-color));
  margin-block-start: 22px;
}

.store-availability-list__location {
  margin-block-end: 2px;
}

.store-availability-list__stock svg {
  margin-inline-end: 8px;
}

.store-availability-list__contact {
  margin-block-start: 8px;
}

.store-availability-list__contact p {
  margin-block-end: 0;
}

.testimonial-list {
  max-width: 580px;
  margin-inline-start: auto;
  margin-inline-end: auto;
  display: block;
}

.testimonial__author, .testimonial-list__nav {
  padding: 0 24px;
}

.testimonial-list__wrapper {
  flex-wrap: nowrap;
  display: flex;
  overflow: hidden;
}

.testimonial {
  will-change: transform;
  flex-shrink: 0;
  order: 0;
  width: 100%;
  display: block;
}

.testimonial[hidden] {
  visibility: hidden;
  order: 1;
}

.testimonial:not(:only-child) {
  cursor: grab;
  -webkit-user-select: none;
  user-select: none;
}

.testimonial__content {
  margin: 0;
}

.testimonial__author {
  margin-block-start: 32px;
}

.testimonial-list__nav {
  margin-block-start: 40px;
}

@media screen and (min-width: 741px) {
  .testimonial__author, .testimonial-list__nav {
    margin-block-start: 32px;
    padding-block-start: 0;
    padding-block-end: 0;
    padding-inline-start: 0;
    padding-inline-end: 48px;
  }
}

@media screen and (min-width: 1000px) {
  .testimonial-list {
    max-width: 690px;
  }
}

@media screen and (min-width: 1200px) {
  .testimonial-list {
    max-width: 875px;
  }

  .testimonial__author, .testimonial-list__nav {
    margin-block-start: 40px;
    padding-block-start: 0;
    padding-block-end: 0;
    padding-inline-start: 0;
    padding-inline-end: 70px;
  }
}

.text-with-icons {
  display: block;
}

.text-with-icons__list {
  scroll-snap-type: x mandatory;
  margin-inline: calc(-1 * var(--container-gutter));
  display: flex;
}

.text-with-icons__item {
  text-align: center;
  scroll-snap-align: center;
  scroll-snap-stop: always;
  flex: none;
  width: 100%;
  padding: 0 48px;
  display: block;
}

.text-with-icons__icon-wrapper {
  margin-block-end: 16px;
}

.text-with-icons__custom-icon {
  max-width: 24px;
  display: block;
}

.text-with-icons__icon-wrapper > * {
  margin: 0 auto;
}

.text-with-icons__dots {
  margin-block-start: 26px;
}

@media screen and (min-width: 1000px) {
  .text-with-icons__list {
    grid-auto-columns: minmax(200px, 400px);
    grid-auto-flow: column;
    justify-content: center;
    gap: 48px;
    margin-inline-start: 0;
    margin-inline-end: 0;
    display: grid;
  }

  .text-with-icons__item {
    padding: 0;
  }

  .text-with-icons__content-wrapper .heading + p {
    margin-block-start: 16px;
  }
}

.timeline__inner {
  position: relative;
}

.timeline__list-wrapper {
  display: block;
}

.timeline__list {
  grid-auto-flow: column;
  justify-content: safe center;
  align-items: center;
  min-width: min-content;
  display: grid;
  position: relative;
}

.timeline__item {
  color: rgb(var(--text-color));
}

.timeline__content {
  background: rgb(var(--section-box-background));
  padding: 40px;
}

.timeline__image.placeholder-background {
  fill: rgb(var(--section-background));
  background: rgb(var(--text-color));
}

.timeline__nav-wrapper {
  margin-block-start: 40px;
}

.timeline__nav {
  grid-template-columns: repeat(var(--section-items-count), minmax(0, 1fr));
  align-items: start;
  padding-block-start: 18px;
  display: grid;
  position: relative;
}

.timeline__nav-item {
  opacity: .7;
  padding-inline-end: 48px;
  transition: opacity .2s ease-in-out;
}

.timeline__nav-item:last-child {
  padding-inline-end: 0;
}

.timeline__nav-item[aria-current="true"] {
  opacity: 1;
}

.timeline__progress-bar {
  width: 100%;
  position: absolute;
  top: 0;
}

.timeline__progress-bar:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 0;
}

.timeline__progress-bar:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 0;
}

.timeline__progress-bar:before {
  transition: transform .3s ease-in-out;
}

.timeline__prev-next-buttons {
  z-index: 1;
  position: absolute;
  top: calc(50% - 56px);
}

.timeline__prev-next-buttons:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: calc(var(--container-outer-width)  - 28px);
}

.timeline__prev-next-buttons:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: calc(var(--container-outer-width)  - 28px);
}

@media screen and (max-width: 999px) {
  .timeline {
    --timeline-image-max-width: 70vw;
    --timeline-content-max-width: 79vw;
  }

  .timeline__list-wrapper {
    scroll-snap-type: x mandatory;
  }

  .timeline__list-wrapper .container {
    padding-inline-start: 0;
    padding-inline-end: 0;
  }

  .timeline__list {
    padding-inline: calc((100vw - var(--timeline-image-max-width)) / 2);
  }

  .timeline__item {
    grid-template-columns: var(--timeline-image-max-width) var(--timeline-content-max-width);
    align-items: center;
    display: grid;
  }

  .timeline__item:not(:last-child) {
    padding-inline-end: calc((100vw - var(--timeline-image-max-width)));
  }

  .timeline__image-wrapper {
    position: sticky;
  }

  .timeline__image-wrapper:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: calc((100vw - var(--timeline-image-max-width)) / 2);
  }

  .timeline__image-wrapper:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: calc((100vw - var(--timeline-image-max-width)) / 2);
  }

  .timeline__image {
    border-radius: var(--block-border-radius);
    width: 100%;
  }

  .timeline__content-wrapper {
    max-width: var(--timeline-content-max-width);
    box-shadow: -10px 0 30px 10px rgba(var(--section-background), .2);
    border-radius: var(--block-border-radius);
    z-index: 1;
  }

  .timeline__content {
    border-radius: var(--block-border-radius);
    margin-inline-end: calc(-1 * (var(--timeline-content-max-width)  - var(--timeline-image-max-width)));
    position: relative;
  }

  .timeline__content:before {
    content: "";
    width: var(--timeline-image-max-width);
    pointer-events: none;
    height: 100%;
    position: absolute;
    top: 0;
  }

  .timeline__content:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    right: 100%;
  }

  .timeline__content:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    left: 100%;
  }

  .timeline__content, .timeline__content:before {
    scroll-snap-align: center;
    scroll-snap-stop: always;
  }

  .timeline__nav-wrapper {
    margin-inline: calc(-1 * var(--container-gutter));
  }

  .timeline__nav-scroller {
    width: max-content;
    min-width: 100%;
  }

  .timeline__nav {
    margin-inline-start: 24px;
    margin-inline-end: 24px;
  }

  .timeline__nav-item {
    max-width: 190px;
  }
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .timeline {
    --timeline-image-max-width: 50vw;
    --timeline-content-max-width: 54vw;
  }
}

@media screen and (min-width: 1000px) {
  .timeline__list {
    align-items: stretch;
  }

  .timeline__list-wrapper {
    overflow: hidden;
  }

  .timeline__item {
    width: calc(var(--grid-column-width) * 13 + var(--grid-gap) * 12);
    background: rgb(var(--section-box-background));
    border-radius: var(--block-border-radius);
    z-index: 0;
    flex: none;
    transition: opacity .25s ease-in-out;
    display: flex;
    overflow: hidden;
  }

  .timeline__item[hidden] {
    opacity: .2;
    will-change: opacity;
  }

  .timeline__item:not(:last-child) {
    margin-inline-end: calc(var(--grid-column-width)  + var(--grid-gap));
  }

  .timeline__item:last-child:not(:only-child) {
    margin-right: var(--container-outer-width);
  }

  .timeline__content-wrapper {
    align-self: center;
  }

  .timeline__content-wrapper--top {
    align-self: flex-start;
  }

  .timeline__content-wrapper--bottom {
    align-self: flex-end;
  }

  .timeline__image-wrapper, .timeline__content-wrapper {
    flex: none;
    width: 50%;
  }

  .timeline__image-wrapper {
    min-height: 100%;
  }

  .timeline__image {
    object-fit: cover;
    object-position: center;
    width: 100%;
    height: 100%;
  }

  .timeline__nav-item {
    padding-inline-end: 20px;
  }
}

@media screen and (min-width: 1400px) {
  .timeline__content {
    padding: 64px;
  }
}

.video-section--boxed {
  margin-inline-start: auto;
  margin-inline-end: auto;
}

.video-section--boxed.video-section--small {
  max-width: 800px;
}

.video-section--boxed.video-section--medium {
  max-width: 1000px;
}

.video-section--boxed.video-section--large {
  max-width: 1200px;
}

.video-section .video-wrapper--native {
  width: 100%;
}

.video-section--full .video-wrapper {
  --video-width: 100vw;
  height: var(--video-height);
}

.video-section--full.video-section--small {
  --video-height: 250px;
}

.video-section--full.video-section--medium {
  --video-height: 350px;
}

.video-section--full.video-section--large {
  --video-height: 450px;
}

@media screen and (min-width: 741px) and (max-width: 999px) {
  .video-section--full.video-section--small {
    --video-height: 300px;
  }

  .video-section--full.video-section--medium {
    --video-height: 375px;
  }

  .video-section--full.video-section--large {
    --video-height: 425px;
  }
}

@media screen and (min-width: 1000px) and (max-width: 1199px) {
  .video-section--full.video-section--small {
    --video-height: 400px;
  }

  .video-section--full.video-section--medium {
    --video-height: 500px;
  }

  .video-section--full.video-section--large {
    --video-height: 570px;
  }
}

@media screen and (min-width: 1200px) {
  .video-section--full.video-section--small {
    --video-height: 600px;
  }

  .video-section--full.video-section--medium {
    --video-height: 700px;
  }

  .video-section--full.video-section--large {
    --video-height: 800px;
  }
}

@media screen and (min-width: 1000px) {
  .product-list__inner--scroller {
    scroll-snap-type: x mandatory;
    overflow: auto hidden;
  }

  .product-list__inner--scroller .product-item {
    scroll-snap-align: end;
  }
}
