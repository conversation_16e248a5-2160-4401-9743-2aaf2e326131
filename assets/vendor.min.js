(function(){"use strict";var i=function(e,t){var r=function(h){for(var u=0,f=h.length;u<f;u++)o(h[u])},o=function(h){var u=h.target,f=h.attributeName,y=h.oldValue;u.attributeChangedCallback(f,y,u.getAttribute(f))};return function(a,h){var u=a.constructor.observedAttributes;return u&&e(h).then(function(){new t(r).observe(a,{attributes:!0,attributeOldValue:!0,attributeFilter:u});for(var f=0,y=u.length;f<y;f++)a.hasAttribute(u[f])&&o({target:a,attributeName:u[f],oldValue:null})}),a}};function l(e,t){if(e){if(typeof e=="string")return m(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return m(e,t)}}function m(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,o=new Array(t);r<t;r++)o[r]=e[r];return o}function M(e,t){var r=typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=l(e))||t&&e&&typeof e.length=="number"){r&&(e=r);var o=0,a=function(){};return{s:a,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(y){throw y},f:a}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var h=!0,u=!1,f;return{s:function(){r=r.call(e)},n:function(){var y=r.next();return h=y.done,y},e:function(y){u=!0,f=y},f:function(){try{!h&&r.return!=null&&r.return()}finally{if(u)throw f}}}}var d=!0,P=!1,C="querySelectorAll",$=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:document,o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:MutationObserver,a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:["*"],h=function y(q,U,A,s,v,w){var L=M(q),B;try{for(L.s();!(B=L.n()).done;){var O=B.value;(w||C in O)&&(v?A.has(O)||(A.add(O),s.delete(O),t(O,v)):s.has(O)||(s.add(O),A.delete(O),t(O,v)),w||y(O[C](U),U,A,s,v,d))}}catch(mt){L.e(mt)}finally{L.f()}},u=new o(function(y){if(a.length){var q=a.join(","),U=new Set,A=new Set,s=M(y),v;try{for(s.s();!(v=s.n()).done;){var w=v.value,L=w.addedNodes,B=w.removedNodes;h(B,q,U,A,P,P),h(L,q,U,A,d,P)}}catch(O){s.e(O)}finally{s.f()}}}),f=u.observe;return(u.observe=function(y){return f.call(u,y,{subtree:d,childList:d})})(r),u},b="querySelectorAll",S=self,x=S.document,H=S.Element,j=S.MutationObserver,_=S.Set,ft=S.WeakMap,z=function(t){return b in t},W=[].filter,R=function(e){var t=new ft,r=function(s){for(var v=0,w=s.length;v<w;v++)t.delete(s[v])},o=function(){for(var s=q.takeRecords(),v=0,w=s.length;v<w;v++)u(W.call(s[v].removedNodes,z),!1),u(W.call(s[v].addedNodes,z),!0)},a=function(s){return s.matches||s.webkitMatchesSelector||s.msMatchesSelector},h=function(s,v){var w;if(v)for(var L,B=a(s),O=0,mt=f.length;O<mt;O++)B.call(s,L=f[O])&&(t.has(s)||t.set(s,new _),w=t.get(s),w.has(L)||(w.add(L),e.handle(s,v,L)));else t.has(s)&&(w=t.get(s),t.delete(s),w.forEach(function(ne){e.handle(s,v,ne)}))},u=function(s){for(var v=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,w=0,L=s.length;w<L;w++)h(s[w],v)},f=e.query,y=e.root||x,q=$(h,y,j,f),U=H.prototype.attachShadow;return U&&(H.prototype.attachShadow=function(A){var s=U.call(this,A);return q.observe(s),s}),f.length&&u(y[b](f)),{drop:r,flush:o,observer:q,parse:u}},p=self,g=p.document,I=p.Map,V=p.MutationObserver,Q=p.Object,Y=p.Set,Z=p.WeakMap,X=p.Element,n=p.HTMLElement,c=p.Node,N=p.Error,T=p.TypeError,E=p.Reflect,D=Q.defineProperty,lt=Q.keys,Vt=Q.getOwnPropertyNames,F=Q.setPrototypeOf,J=!self.customElements,Tt=function(t){for(var r=lt(t),o=[],a=new Y,h=r.length,u=0;u<h;u++){o[u]=t[r[u]];try{delete t[r[u]]}catch{a.add(u)}}return function(){for(var f=0;f<h;f++)a.has(f)||(t[r[f]]=o[f])}};if(J){var ht=function(){var t=this.constructor;if(!dt.has(t))throw new T("Illegal constructor");var r=dt.get(t);if(et)return At(et,r);var o=Et.call(g,r);return At(F(o,t.prototype),r)},Et=g.createElement,dt=new I,tt=new I,St=new I,G=new I,Ot=[],Qt=function(t,r,o){var a=St.get(o);if(r&&!a.isPrototypeOf(t)){var h=Tt(t);et=F(t,a);try{new a.constructor}finally{et=null,h()}}var u="".concat(r?"":"dis","connectedCallback");u in a&&t[u]()},_t=R({query:Ot,handle:Qt}),Yt=_t.parse,et=null,vt=function(t){if(!tt.has(t)){var r,o=new Promise(function(a){r=a});tt.set(t,{$:o,_:r})}return tt.get(t).$},At=i(vt,V);self.customElements={define:function(t,r){if(G.has(t))throw new N('the name "'.concat(t,'" has already been used with this registry'));dt.set(r,t),St.set(t,r.prototype),G.set(t,r),Ot.push(t),vt(t).then(function(){Yt(g.querySelectorAll(t))}),tt.get(t)._(r)},get:function(t){return G.get(t)},whenDefined:vt},D(ht.prototype=n.prototype,"constructor",{value:ht}),self.HTMLElement=ht,g.createElement=function(e,t){var r=t&&t.is,o=r?G.get(r):G.get(e);return o?new o:Et.call(g,e)},"isConnected"in c.prototype||D(c.prototype,"isConnected",{configurable:!0,get:function(){return!(this.ownerDocument.compareDocumentPosition(this)&this.DOCUMENT_POSITION_DISCONNECTED)}})}else if(J=!self.customElements.get("extends-br"),J)try{var It=function e(){return self.Reflect.construct(HTMLBRElement,[],e)};It.prototype=HTMLLIElement.prototype;var Lt="extends-br";self.customElements.define("extends-br",It,{extends:"br"}),J=g.createElement("br",{is:Lt}).outerHTML.indexOf(Lt)<0;var Mt=self.customElements,Bt=Mt.get,Xt=Mt.whenDefined;self.customElements.whenDefined=function(e){var t=this;return Xt.call(this,e).then(function(r){return r||Bt.call(t,e)})}}catch{}if(J){var Pt=function(t){var r=pt.get(t);Ct(r.querySelectorAll(this),t.isConnected)},k=self.customElements,Nt=g.createElement,Ft=k.define,Jt=k.get,Gt=k.upgrade,Kt=E||{construct:function(t){return t.call(this)}},zt=Kt.construct,pt=new Z,gt=new Y,rt=new I,nt=new I,Dt=new I,ot=new I,Ht=[],at=[],kt=function(t){return ot.get(t)||Jt.call(k,t)},Zt=function(t,r,o){var a=Dt.get(o);if(r&&!a.isPrototypeOf(t)){var h=Tt(t);it=F(t,a);try{new a.constructor}finally{it=null,h()}}var u="".concat(r?"":"dis","connectedCallback");u in a&&t[u]()},te=R({query:at,handle:Zt}),Ct=te.parse,ee=R({query:Ht,handle:function(t,r){pt.has(t)&&(r?gt.add(t):gt.delete(t),at.length&&Pt.call(at,t))}}),re=ee.parse,$t=X.prototype.attachShadow;$t&&(X.prototype.attachShadow=function(e){var t=$t.call(this,e);return pt.set(this,t),t});var yt=function(t){if(!nt.has(t)){var r,o=new Promise(function(a){r=a});nt.set(t,{$:o,_:r})}return nt.get(t).$},wt=i(yt,V),it=null;Vt(self).filter(function(e){return/^HTML.*Element$/.test(e)}).forEach(function(e){var t=self[e];function r(){var o=this.constructor;if(!rt.has(o))throw new T("Illegal constructor");var a=rt.get(o),h=a.is,u=a.tag;if(h){if(it)return wt(it,h);var f=Nt.call(g,u);return f.setAttribute("is",h),wt(F(f,o.prototype),h)}else return zt.call(this,t,[],o)}D(r.prototype=t.prototype,"constructor",{value:r}),D(self,e,{value:r})}),g.createElement=function(e,t){var r=t&&t.is;if(r){var o=ot.get(r);if(o&&rt.get(o).tag===e)return new o}var a=Nt.call(g,e);return r&&a.setAttribute("is",r),a},k.get=kt,k.whenDefined=yt,k.upgrade=function(e){var t=e.getAttribute("is");if(t){var r=ot.get(t);if(r){wt(F(e,r.prototype),t);return}}Gt.call(k,e)},k.define=function(e,t,r){if(kt(e))throw new N("'".concat(e,"' has already been defined as a custom element"));var o,a=r&&r.extends;rt.set(t,a?{is:e,tag:a}:{is:"",tag:e}),a?(o="".concat(a,'[is="').concat(e,'"]'),Dt.set(o,t.prototype),ot.set(e,t),at.push(o)):(Ft.apply(k,arguments),Ht.push(o=e)),yt(e).then(function(){a?(Ct(g.querySelectorAll(o)),gt.forEach(Pt,[o])):re(g.querySelectorAll(o))}),nt.get(e)._(t)}}})();(function(i,l){if(typeof i.createEvent!="function")return!1;var m=function(n){var c=n.toLowerCase(),N="MS"+n;return navigator.msPointerEnabled?N:window.PointerEvent?c:!1},M=function(n){return"on"+n in window?n:!1},d={useJquery:!l.IGNORE_JQUERY&&typeof jQuery<"u",swipeThreshold:l.SWIPE_THRESHOLD||100,tapThreshold:l.TAP_THRESHOLD||150,dbltapThreshold:l.DBL_TAP_THRESHOLD||200,longtapThreshold:l.LONG_TAP_THRESHOLD||1e3,tapPrecision:l.TAP_PRECISION/2||60/2,justTouchEvents:l.JUST_ON_TOUCH_DEVICES},P=!1,C={touchstart:M("touchstart")||m("PointerDown"),touchend:M("touchend")||m("PointerUp"),touchmove:M("touchmove")||m("PointerMove")},$=function(n){return!n.pointerId||typeof R>"u"||n.pointerId===R},b=function(n,c,N){for(var T=c.split(" "),E=T.length;E--;)n.addEventListener(T[E],N,!1)},S=function(n){var c=!!(n.targetTouches&&n.targetTouches.length);switch(!0){case!!n.target.touches:return n.target.touches[0];case(c&&typeof n.targetTouches[0].pageX<"u"):return n.targetTouches[0];case(c&&!!n.targetTouches[0].touches):return n.targetTouches[0].touches[0];default:return n}},x=function(n){return(n.targetTouches||n.target.touches||[]).length>1},H=function(){return new Date().getTime()},j=function(n,c,N,T){var E=i.createEvent("Event");if(E.originalEvent=N,T=T||{},T.x=p,T.y=g,d.useJquery&&(E=jQuery.Event(c,{originalEvent:N}),jQuery(n).trigger(E,T)),E.initEvent){for(var D in T)E[D]=T[D];E.initEvent(c,!0,!0),n.dispatchEvent(E)}for(;n;)n["on"+c]&&n["on"+c](E),n=n.parentNode},_=function(n){if(!(!$(n)||x(n))&&(R=n.pointerId,n.type!=="mousedown"&&(P=!0),!(n.type==="mousedown"&&P))){var c=S(n);I=p=c.pageX,V=g=c.pageY,X=setTimeout(function(){j(n.target,"longtap",n),Y=n.target},d.longtapThreshold),Q=H(),W++}},ft=function(n){if(!(!$(n)||x(n))){if(R=void 0,n.type==="mouseup"&&P){P=!1;return}var c=[],N=H(),T=V-g,E=I-p;if(clearTimeout(Z),clearTimeout(X),E<=-d.swipeThreshold&&c.push("swiperight"),E>=d.swipeThreshold&&c.push("swipeleft"),T<=-d.swipeThreshold&&c.push("swipedown"),T>=d.swipeThreshold&&c.push("swipeup"),c.length){for(var D=0;D<c.length;D++){var lt=c[D];j(n.target,lt,n,{distance:{x:Math.abs(E),y:Math.abs(T)}})}W=0}else I>=p-d.tapPrecision&&I<=p+d.tapPrecision&&V>=g-d.tapPrecision&&V<=g+d.tapPrecision&&Q+d.tapThreshold-N>=0&&(j(n.target,W>=2&&Y===n.target?"dbltap":"tap",n),Y=n.target),Z=setTimeout(function(){W=0},d.dbltapThreshold)}},z=function(n){if($(n)&&!(n.type==="mousemove"&&P)){var c=S(n);p=c.pageX,g=c.pageY}},W=0,R,p,g,I,V,Q,Y,Z,X;b(i,C.touchstart+(d.justTouchEvents?"":" mousedown"),_),b(i,C.touchend+(d.justTouchEvents?"":" mouseup"),ft),b(i,C.touchmove+(d.justTouchEvents?"":" mousemove"),z),l.tocca=function(n){for(var c in n)d[c]=n[c];return d}})(document,window);var ut=null,jt,Rt,qt,Ut=65,bt,K,xt=new Set,Wt=1111;oe();function oe(){if(!document.createElement("link").relList.supports("prefetch"))return;let l="instantVaryAccept"in document.body.dataset||"Shopify"in window,m=navigator.userAgent.indexOf("Chrome/");if(m>-1&&(ut=parseInt(navigator.userAgent.substring(m+7))),l&&ut&&ut<110)return;let M="instantMousedownShortcut"in document.body.dataset;jt="instantAllowQueryString"in document.body.dataset,Rt="instantAllowExternalLinks"in document.body.dataset,qt="instantWhitelist"in document.body.dataset;let d={capture:!0,passive:!0},P=!1,C=!1,$=!1;if("instantIntensity"in document.body.dataset){let b=document.body.dataset.instantIntensity;if(b.startsWith("mousedown"))P=!0,b=="mousedown-only"&&(C=!0);else if(b.startsWith("viewport")){let S=navigator.connection&&navigator.connection.saveData,x=navigator.connection&&navigator.connection.effectiveType&&navigator.connection.effectiveType.includes("2g");!S&&!x&&(b=="viewport"?document.documentElement.clientWidth*document.documentElement.clientHeight<45e4&&($=!0):b=="viewport-all"&&($=!0))}else{let S=parseInt(b);isNaN(S)||(Ut=S)}}if(C||document.addEventListener("touchstart",ae,d),P?M||document.addEventListener("mousedown",ue,d):document.addEventListener("mouseover",ie,d),M&&document.addEventListener("mousedown",ce,d),$){let b=window.requestIdleCallback;b||(b=S=>{S()}),b(function(){let x=new IntersectionObserver(H=>{H.forEach(j=>{if(j.isIntersecting){let _=j.target;x.unobserve(_),ct(_.href)}})});document.querySelectorAll("a").forEach(H=>{st(H)&&x.observe(H)})},{timeout:1500})}}function ae(i){bt=performance.now();let l=i.target.closest("a");st(l)&&ct(l.href,"high")}function ie(i){if(performance.now()-bt<Wt||!("closest"in i.target))return;let l=i.target.closest("a");st(l)&&(l.addEventListener("mouseout",se,{passive:!0}),K=setTimeout(()=>{ct(l.href,"high"),K=void 0},Ut))}function ue(i){let l=i.target.closest("a");st(l)&&ct(l.href,"high")}function se(i){i.relatedTarget&&i.target.closest("a")==i.relatedTarget.closest("a")||K&&(clearTimeout(K),K=void 0)}function ce(i){if(performance.now()-bt<Wt)return;let l=i.target.closest("a");if(i.which>1||i.metaKey||i.ctrlKey||!l)return;l.addEventListener("click",function(M){M.detail!=1337&&M.preventDefault()},{capture:!0,passive:!1,once:!0});let m=new MouseEvent("click",{view:window,bubbles:!0,cancelable:!1,detail:1337});l.dispatchEvent(m)}function st(i){if(!(!i||!i.href)&&!(qt&&!("instant"in i.dataset))&&!(i.origin!=location.origin&&(!(Rt||"instant"in i.dataset)||!ut))&&["http:","https:"].includes(i.protocol)&&!(i.protocol=="http:"&&location.protocol=="https:")&&!(!jt&&i.search&&!("instant"in i.dataset))&&!(i.hash&&i.pathname+i.search==location.pathname+location.search)&&!("noInstant"in i.dataset))return!0}function ct(i,l="auto"){if(xt.has(i))return;let m=document.createElement("link");m.rel="prefetch",m.href=i,m.fetchPriority=l,m.as="document",document.head.appendChild(m),xt.add(i)}
/*! Bundled license information:

@ungap/custom-elements/index.js:
  (*! (c) Andrea Giammarchi @webreflection ISC *)
  (*! (c) Andrea Giammarchi - ISC *)

instant.page/instantpage.js:
  (*! instant.page v5.2.0 - (C) 2019-2023 Alexandre Dieulot - https://instant.page/license *)
*/
