{"version": 3, "sources": ["style-guide.scss", "style-guide.css"], "names": [], "mappings": "AAGA,oBAEE,8CAAA,CAEA,kCCoDF,CD3CE,kCAEE,eAAA,CADA,YC8CJ,CDxCA,uBAOE,8CAAA,CACA,iCAAA,CAHA,iBAAA,CADA,WAAA,CAFA,uBAAA,CAAA,eAAA,CACA,KC8CF,CDlCA,2BAEE,YAAA,CACA,cAAA,CACA,sBAAA,CAEA,QAAA,CADA,SCsCF,CDnCE,6BACE,YCqCJ,CDpCI,mCACE,YCsCN,CD7BA,4BAGE,gDAAA,CAKA,8CAAA,CAFA,kBAAA,CADA,YCiCF,CDxBA,qBACE,YAAA,CACA,qEC4BF,CDzBA,mBAKE,kBAAA,CAHA,YAAA,CAEA,qBAAA,CADA,cAAA,CAIA,YAAA,CAEA,iBCyBF,CDvBE,kDAKE,+CAAA,CACA,2CAAA,CAJA,kBAAA,CACA,cAAA,CAKA,qCCsBJ,CDpBI,gHAEE,gDCqBN,CDpBM,sKACE,gCCsBR,CDjBM,oFACE,iCCmBR,CDbE,8CAUE,uBAAA,CAFA,kBAAA,CAFA,wCAAA,CAGA,yBAAA,CALA,WAAA,CAFA,UAAA,CASA,qCAAA,CARA,UCoBJ,CDRE,6CAIE,yCAAA,CACA,2CAAA,CAHA,kBCWJ,CDNI,wEACE,eCQN,CDHE,6CACE,eCKJ,CDGI,8CACE,2DAAA,CACA,wEAAA,CACA,+CAAA,CACA,qCAAA,CACA,kDAAA,CACA,oDCDN,CDLI,8CACE,2DAAA,CACA,wEAAA,CACA,+CAAA,CACA,qCAAA,CACA,kDAAA,CACA,oDCON,CDbI,8CACE,2DAAA,CACA,wEAAA,CACA,+CAAA,CACA,qCAAA,CACA,kDAAA,CACA,oDCeN,CDrBI,8CACE,2DAAA,CACA,wEAAA,CACA,+CAAA,CACA,qCAAA,CACA,kDAAA,CACA,oDCuBN,CD7BI,8CACE,2DAAA,CACA,wEAAA,CACA,+CAAA,CACA,qCAAA,CACA,kDAAA,CACA,oDC+BN,CDrCI,8CACE,2DAAA,CACA,wEAAA,CACA,+CAAA,CACA,qCAAA,CACA,kDAAA,CACA,oDCuCN,CD7CI,8CACE,2DAAA,CACA,wEAAA,CACA,+CAAA,CACA,qCAAA,CACA,kDAAA,CACA,oDC+CN,CDrDI,8CACE,2DAAA,CACA,wEAAA,CACA,+CAAA,CACA,qCAAA,CACA,kDAAA,CACA,oDCuDN,CD9CI,8CACE,2DAAA,CACA,wEAAA,CACA,yCAAA,CACA,qCAAA,CACA,kDAAA,CACA,oDCgDN,CDtDI,8CACE,2DAAA,CACA,wEAAA,CACA,yCAAA,CACA,qCAAA,CACA,kDAAA,CACA,oDCwDN,CD9DI,gDACE,6DAAA,CACA,0EAAA,CACA,yCAAA,CACA,uCAAA,CACA,oDAAA,CACA,sDCgEN,CDtEI,+CACE,4DAAA,CACA,yEAAA,CACA,yCAAA,CACA,sCAAA,CACA,mDAAA,CACA,qDCwEN,CD9EI,8CACE,2DAAA,CACA,wEAAA,CACA,yCAAA,CACA,qCAAA,CACA,kDAAA,CACA,oDCgFN,CDtFI,8CACE,2DAAA,CACA,wEAAA,CACA,yCAAA,CACA,qCAAA,CACA,kDAAA,CACA,oDCwFN,CD9FI,6CACE,0DAAA,CACA,uEAAA,CACA,yCAAA,CACA,oCAAA,CACA,iDAAA,CACA,mDCgGN,CDtGI,2CACE,wDAAA,CACA,qEAAA,CACA,yCAAA,CACA,kCAAA,CACA,+CAAA,CACA,iDCwGN,CD9GI,+CACE,4DAAA,CACA,yEAAA,CACA,yCAAA,CACA,sCAAA,CACA,mDAAA,CACA,qDCgHN,CD1GE,2CACE,+CAAA,CACA,iCC4GJ,CDzGE,kDACE,+CAAA,CACA,uCC2GJ,CDxGE,iDACE,+CAAA,CACA,wCC0GJ,CDvGE,4CACE,+CAAA,CACA,iCAAA,CACA,+CCyGJ,CDtGE,gDACE,+CAAA,CACA,sCCwGJ,CDrGE,gDACE,+CAAA,CACA,sCCuGJ,CDpGE,gDACE,+CAAA,CACA,sCCsGJ,CDjGE,sDACE,+CAAA,CACA,6CCmGJ,CDhGE,2DACE,+CAAA,CACA,kDCkGJ,CD/FE,2DACE,+CAAA,CACA,kDCiGJ,CD9FE,2DACE,+CAAA,CACA,kDCgGJ,CD7FE,4DACE,+CAAA,CACA,2DC+FJ,CD5FE,4DACE,+CAAA,CACA,2DC8FJ,CDzFI,uDACE,+CAAA,CACA,oHAAA,CACA,0BC2FN,CDxFI,yDACE,+CAAA,CACA,wHAAA,CACA,0BC0FN,CDvFI,wDACE,+CAAA,CACA,sHAAA,CACA,0BCyFN,CDtFI,2DACE,+CAAA,CACA,4HAAA,CACA,0BCwFN,CDjFA,kBAKE,8CAAA,CAHA,YAAA,CACA,6DCqFF,CDjFE,0CACE,kFCmFJ,CDhFE,sBAGE,8CAAA,CAEA,yBAAA,CADA,YAAA,CAFA,iBCoFJ,CD/EI,0BAEE,WAAA,CACA,eAAA,CACA,cAAA,CAHA,UCoFN,CD9EI,4BACE,oBAAA,CACA,SCgFN,CD9EQ,kCACE,6BAAA,CACA,sCCgFV", "file": "style-guide.css", "sourcesContent": ["@import '../custom/utilities/_variables.scss';\n@import '../custom/utilities/_mixins.scss';\n\n.styleguide-section {\n\n  background: var(---background-color--content-2);\n\n  padding: var(--vertical-breather) 0;\n\n  .container {\n\n  }\n\n  .page-header {\n  }\n\n  .page-content {\n    margin-top: 0;\n    margin-bottom: 0;\n  }\n\n}\n\n.styleguide-navigation {\n\n  position: sticky;\n  top: 0;\n  padding: 1em;\n  margin-bottom: 2em;\n\n  background: var(---background-color--content-1);\n  box-shadow: 0 0 5px rgba(0,0,0,0.1);\n\n}\n\n\n/* ----- Color Swatches ----- */\n\n.styleguide-color-swatches {\n\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: center;\n  padding: 0;\n  margin: 0;\n\n  a {\n    margin: 0.25em;\n    &:after {\n      content: none;\n    }\n  }\n\n}\n\n\n/* ----- Docs Sections ----- */\n\n.Rte--preview,\n.rte--preview {\n\n  --background: var(---background-color--content-1);\n\n  padding: 60px;\n  margin-bottom: 50px;\n\n  background: var(---background-color--content-1);\n\n}\n\n/* ----- Color Swatches ----- */\n\n.docs-color-swatches {\n  display: grid;\n  grid-template-columns: 12.5% 12.5% 12.5% 12.5% 12.5% 12.5% 12.5% 12.5%;\n}\n\n.docs-color-swatch {\n\n  display: flex;\n  flex-wrap: wrap;\n  flex-direction: column;\n  align-items: center;\n\n  margin: 0 1em;\n\n  text-align: center;\n\n  .docs-color-swatch__background {\n\n    border-radius: 100%;\n    cursor: pointer;\n\n    background-color: var(--swatch-background-color);\n    border: 1px solid var(--swatch-border-color);\n\n    transition: 0.25s background, 0.25s color;\n\n    &:hover,\n    &:focus {\n      background: var(--swatch-background-color--hover);\n      .docs-color-swatch__swatch {\n        color: var(--swatch-color--hover);\n      }\n    }\n\n    &:active {\n      .docs-color-swatch__swatch {\n        color: var(--swatch-color--active);\n      }\n    }\n\n  }\n\n  .docs-color-swatch__swatch {\n\n    margin: 1em;\n    width: 50px;\n    height: 50px;\n\n    box-shadow: 0 0 5px rgba(0,0,0,.15) inset;\n\n    border-radius: 100%;\n    color: var(--swatch-color);\n    background: currentColor;\n    transition: 0.25s background, 0.25s color;\n\n  }\n\n  .docs-color-swatch__inner {\n\n    border-radius: 100%;\n\n    background: var(--swatch-background-color);\n    border: 1px solid var(--swatch-border-color);\n\n    .docs-color-swatch__swatch {\n      box-shadow: none;\n    }\n\n  }\n\n  .docs-color-swatch__title {\n    margin-top: 0.5em;\n  }\n\n  // Brand Colors\n\n  $brand-colors: \"brand-1\", \"brand-2\", \"brand-3\", \"brand-4\", \"brand-5\", \"brand-6\", \"brand-7\", \"brand-8\";\n\n  @each $color-name in $brand-colors  {\n    &.docs-color-swatch--#{$color-name} {\n      --swatch-background-color: var(---background-color--#{$color-name});\n      --swatch-background-color--hover: var(---background-color--#{$color-name}-hover);\n      --swatch-border-color: var(---color-line--light);\n      --swatch-color: var(---color--#{$color-name});\n      --swatch-color--hover: var(---color--#{$color-name}-hover);\n      --swatch-color--active: var(---color--#{$color-name}-active);\n    }\n  }\n\n  // System Colors\n\n  $system-colors: \"default\", \"primary\", \"secondary\", \"tertiary\", \"success\", \"warning\", \"danger\", \"info\", \"disabled\";\n\n  @each $color-name in $system-colors  {\n    &.docs-color-swatch--#{$color-name} {\n      --swatch-background-color: var(---background-color--#{$color-name});\n      --swatch-background-color--hover: var(---background-color--#{$color-name}-hover);\n      --swatch-border-color: var(--color-swatch);\n      --swatch-color: var(---color--#{$color-name});\n      --swatch-color--hover: var(---color--#{$color-name}-hover);\n      --swatch-color--active: var(---color--#{$color-name}-active);\n    }\n  }\n\n  // Content Colors\n\n  &.docs-color-swatch--body {\n    --swatch-border-color: var(---color-line--light);\n    --swatch-color: var(---color-text);\n  }\n\n  &.docs-color-swatch--body-strong {\n    --swatch-border-color: var(---color-line--light);\n    --swatch-color: var(---color-text--dark);\n  }\n\n  &.docs-color-swatch--body-light {\n    --swatch-border-color: var(---color-line--light);\n    --swatch-color: var(---color-text--light);\n  }\n\n  &.docs-color-swatch--links {\n    --swatch-border-color: var(---color-line--light);\n    --swatch-color: var(---color-link);\n    --swatch-color--hover: var(---color-link--hover);\n  }\n\n  &.docs-color-swatch--heading-1 {\n    --swatch-border-color: var(---color-line--light);\n    --swatch-color: var(---color-heading-1);\n  }\n\n  &.docs-color-swatch--heading-2 {\n    --swatch-border-color: var(---color-line--light);\n    --swatch-color: var(---color-heading-2);\n  }\n\n  &.docs-color-swatch--heading-3 {\n    --swatch-border-color: var(---color-line--light);\n    --swatch-color: var(---color-heading-3);\n  }\n\n  // Layout Colors\n\n  &.docs-color-swatch--background-body {\n    --swatch-border-color: var(---color-line--light);\n    --swatch-color: var(---background-color--body);\n  }\n\n  &.docs-color-swatch--background-content-1 {\n    --swatch-border-color: var(---color-line--light);\n    --swatch-color: var(---background-color--content-1);\n  }\n\n  &.docs-color-swatch--background-content-2 {\n    --swatch-border-color: var(---color-line--light);\n    --swatch-color: var(---background-color--content-2);\n  }\n\n  &.docs-color-swatch--background-content-3 {\n    --swatch-border-color: var(---color-line--light);\n    --swatch-color: var(---background-color--content-3);\n  }\n\n  &.docs-color-swatch--background-reversed-1 {\n    --swatch-border-color: var(---color-line--light);\n    --swatch-color: var(---background-color--content-reversed-1);\n  }\n\n  &.docs-color-swatch--background-reversed-2 {\n    --swatch-border-color: var(---color-line--light);\n    --swatch-color: var(---background-color--content-reversed-2);\n  }\n  \n  // Gradients\n  \n    &.docs-color-swatch--gradient-primary {\n      --swatch-border-color: var(---color-line--light);\n      --swatch-background-color: linear-gradient(115deg, var(---color--primary--light) 20%, var(---color--primary--dark) 80%);;\n      --swatch-color: transparent;\n    }\n  \n    &.docs-color-swatch--gradient-secondary {\n      --swatch-border-color: var(---color-line--light);\n      --swatch-background-color: linear-gradient(115deg, var(---color--secondary--light) 20%, var(---color--secondary--dark) 80%);;\n      --swatch-color: transparent;\n    }\n  \n    &.docs-color-swatch--gradient-tertiary {\n      --swatch-border-color: var(---color-line--light);\n      --swatch-background-color: linear-gradient(115deg, var(---color--tertiary--light) 20%, var(---color--tertiary--dark) 80%);;\n      --swatch-color: transparent;\n    }\n\n    &.docs-color-swatch--gradient-quarternary {\n      --swatch-border-color: var(---color-line--light);\n      --swatch-background-color: linear-gradient(115deg, var(---color--quarternary--light) 20%, var(---color--quarternary--dark) 80%);;\n      --swatch-color: transparent;\n    }\n\n}\n\n/* ----- Logos ----- */\n\n.styleguide-logos {\n\n  display: grid;\n  grid-template-columns: 10% 10% 10% 10% 10% 10% 10% 10% 10% 10%;\n\n  background: var(---background-color--content-2);\n\n  &.styleguide-icons--small {\n    grid-template-columns: 5% 5% 5% 5% 5% 5% 5% 5% 5% 5% 5% 5% 5% 5% 5% 5% 5% !important;\n  }\n\n  > div {\n\n    position: relative;\n    background: var(---background-color--content-1);;\n    padding: 20px;\n    outline: 1px solid #e6e6e6;\n\n    svg {\n      width: 100%;\n      height: 100%;\n      max-height: 20px;\n      max-width: 20px;\n    }\n\n    &:hover {\n      transform: scale(1.5);\n      z-index: 1;\n      svg {\n        * {\n          stroke: var(---color--primary);\n          fill: var(---background-color--primary);\n        }\n      }\n    }\n  }\n\n}\n", "@charset \"UTF-8\";\n/*  ------------------------------\n    Grid Variables\n    ------------------------------ */\n/**\n * Define your breakpoints. The first value is the prefix that shall be used for\n * your classes (e.g. `.small--one-half`), the second value is the media query\n * that the breakpoint fires at.\n */\n/**\n * Define which namespaced breakpoints you would like to generate for each of\n * widths, push and pull. This is handy if you only need pull on, say, desk, or\n * you only need a new width breakpoint at mobile sizes. It allows you to only\n * compile as much CSS as you need. All are turned on by default, but you can\n * add and remove breakpoints at will.\n *\n * Push and pull shall only be used if `$push` and/or `$pull` and `$responsive`\n * have been set to ‘true’.\n */\n/*\n\n  1. Utilities\n  2. Responsive\n  3. UI Elements\n    3.1. Buttons\n\n*/\n/*  ==============================\n    1. Utilities\n    ============================== */\n/*  ==============================\n    2. Responsive\n    ============================== */\n/*================ Responsive Show/Hide Helper ================*/\n/*================ Responsive Text Alignment Helper ================*/\n/*  ==============================\n    3. UI Elements\n    ============================== */\n/*  ------------------------------\n    3.1. Buttons\n    ------------------------------ */\n/* ------------------------------\n   Headings\n   ------------------------------ */\n/* ------------------------------\n   Labels\n   ------------------------------ */\n/* ------------------------------\n   Inputs\n   ------------------------------ */\n/* ------------------------------\n   RTE\n   ------------------------------ */\n/*  ------------------------------\n    3.3. Shopify\n    ------------------------------ */\n.styleguide-section {\n  background: var(---background-color--content-2);\n  padding: var(--vertical-breather) 0;\n}\n.styleguide-section .page-content {\n  margin-top: 0;\n  margin-bottom: 0;\n}\n\n.styleguide-navigation {\n  position: sticky;\n  top: 0;\n  padding: 1em;\n  margin-bottom: 2em;\n  background: var(---background-color--content-1);\n  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);\n}\n\n/* ----- Color Swatches ----- */\n.styleguide-color-swatches {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: center;\n  padding: 0;\n  margin: 0;\n}\n.styleguide-color-swatches a {\n  margin: 0.25em;\n}\n.styleguide-color-swatches a:after {\n  content: none;\n}\n\n/* ----- Docs Sections ----- */\n.Rte--preview,\n.rte--preview {\n  --background: var(---background-color--content-1);\n  padding: 60px;\n  margin-bottom: 50px;\n  background: var(---background-color--content-1);\n}\n\n/* ----- Color Swatches ----- */\n.docs-color-swatches {\n  display: grid;\n  grid-template-columns: 12.5% 12.5% 12.5% 12.5% 12.5% 12.5% 12.5% 12.5%;\n}\n\n.docs-color-swatch {\n  display: flex;\n  flex-wrap: wrap;\n  flex-direction: column;\n  align-items: center;\n  margin: 0 1em;\n  text-align: center;\n}\n.docs-color-swatch .docs-color-swatch__background {\n  border-radius: 100%;\n  cursor: pointer;\n  background-color: var(--swatch-background-color);\n  border: 1px solid var(--swatch-border-color);\n  transition: 0.25s background, 0.25s color;\n}\n.docs-color-swatch .docs-color-swatch__background:hover, .docs-color-swatch .docs-color-swatch__background:focus {\n  background: var(--swatch-background-color--hover);\n}\n.docs-color-swatch .docs-color-swatch__background:hover .docs-color-swatch__swatch, .docs-color-swatch .docs-color-swatch__background:focus .docs-color-swatch__swatch {\n  color: var(--swatch-color--hover);\n}\n.docs-color-swatch .docs-color-swatch__background:active .docs-color-swatch__swatch {\n  color: var(--swatch-color--active);\n}\n.docs-color-swatch .docs-color-swatch__swatch {\n  margin: 1em;\n  width: 50px;\n  height: 50px;\n  box-shadow: 0 0 5px rgba(0, 0, 0, 0.15) inset;\n  border-radius: 100%;\n  color: var(--swatch-color);\n  background: currentColor;\n  transition: 0.25s background, 0.25s color;\n}\n.docs-color-swatch .docs-color-swatch__inner {\n  border-radius: 100%;\n  background: var(--swatch-background-color);\n  border: 1px solid var(--swatch-border-color);\n}\n.docs-color-swatch .docs-color-swatch__inner .docs-color-swatch__swatch {\n  box-shadow: none;\n}\n.docs-color-swatch .docs-color-swatch__title {\n  margin-top: 0.5em;\n}\n.docs-color-swatch.docs-color-swatch--brand-1 {\n  --swatch-background-color: var(---background-color--brand-1);\n  --swatch-background-color--hover: var(---background-color--brand-1-hover);\n  --swatch-border-color: var(---color-line--light);\n  --swatch-color: var(---color--brand-1);\n  --swatch-color--hover: var(---color--brand-1-hover);\n  --swatch-color--active: var(---color--brand-1-active);\n}\n.docs-color-swatch.docs-color-swatch--brand-2 {\n  --swatch-background-color: var(---background-color--brand-2);\n  --swatch-background-color--hover: var(---background-color--brand-2-hover);\n  --swatch-border-color: var(---color-line--light);\n  --swatch-color: var(---color--brand-2);\n  --swatch-color--hover: var(---color--brand-2-hover);\n  --swatch-color--active: var(---color--brand-2-active);\n}\n.docs-color-swatch.docs-color-swatch--brand-3 {\n  --swatch-background-color: var(---background-color--brand-3);\n  --swatch-background-color--hover: var(---background-color--brand-3-hover);\n  --swatch-border-color: var(---color-line--light);\n  --swatch-color: var(---color--brand-3);\n  --swatch-color--hover: var(---color--brand-3-hover);\n  --swatch-color--active: var(---color--brand-3-active);\n}\n.docs-color-swatch.docs-color-swatch--brand-4 {\n  --swatch-background-color: var(---background-color--brand-4);\n  --swatch-background-color--hover: var(---background-color--brand-4-hover);\n  --swatch-border-color: var(---color-line--light);\n  --swatch-color: var(---color--brand-4);\n  --swatch-color--hover: var(---color--brand-4-hover);\n  --swatch-color--active: var(---color--brand-4-active);\n}\n.docs-color-swatch.docs-color-swatch--brand-5 {\n  --swatch-background-color: var(---background-color--brand-5);\n  --swatch-background-color--hover: var(---background-color--brand-5-hover);\n  --swatch-border-color: var(---color-line--light);\n  --swatch-color: var(---color--brand-5);\n  --swatch-color--hover: var(---color--brand-5-hover);\n  --swatch-color--active: var(---color--brand-5-active);\n}\n.docs-color-swatch.docs-color-swatch--brand-6 {\n  --swatch-background-color: var(---background-color--brand-6);\n  --swatch-background-color--hover: var(---background-color--brand-6-hover);\n  --swatch-border-color: var(---color-line--light);\n  --swatch-color: var(---color--brand-6);\n  --swatch-color--hover: var(---color--brand-6-hover);\n  --swatch-color--active: var(---color--brand-6-active);\n}\n.docs-color-swatch.docs-color-swatch--brand-7 {\n  --swatch-background-color: var(---background-color--brand-7);\n  --swatch-background-color--hover: var(---background-color--brand-7-hover);\n  --swatch-border-color: var(---color-line--light);\n  --swatch-color: var(---color--brand-7);\n  --swatch-color--hover: var(---color--brand-7-hover);\n  --swatch-color--active: var(---color--brand-7-active);\n}\n.docs-color-swatch.docs-color-swatch--brand-8 {\n  --swatch-background-color: var(---background-color--brand-8);\n  --swatch-background-color--hover: var(---background-color--brand-8-hover);\n  --swatch-border-color: var(---color-line--light);\n  --swatch-color: var(---color--brand-8);\n  --swatch-color--hover: var(---color--brand-8-hover);\n  --swatch-color--active: var(---color--brand-8-active);\n}\n.docs-color-swatch.docs-color-swatch--default {\n  --swatch-background-color: var(---background-color--default);\n  --swatch-background-color--hover: var(---background-color--default-hover);\n  --swatch-border-color: var(--color-swatch);\n  --swatch-color: var(---color--default);\n  --swatch-color--hover: var(---color--default-hover);\n  --swatch-color--active: var(---color--default-active);\n}\n.docs-color-swatch.docs-color-swatch--primary {\n  --swatch-background-color: var(---background-color--primary);\n  --swatch-background-color--hover: var(---background-color--primary-hover);\n  --swatch-border-color: var(--color-swatch);\n  --swatch-color: var(---color--primary);\n  --swatch-color--hover: var(---color--primary-hover);\n  --swatch-color--active: var(---color--primary-active);\n}\n.docs-color-swatch.docs-color-swatch--secondary {\n  --swatch-background-color: var(---background-color--secondary);\n  --swatch-background-color--hover: var(---background-color--secondary-hover);\n  --swatch-border-color: var(--color-swatch);\n  --swatch-color: var(---color--secondary);\n  --swatch-color--hover: var(---color--secondary-hover);\n  --swatch-color--active: var(---color--secondary-active);\n}\n.docs-color-swatch.docs-color-swatch--tertiary {\n  --swatch-background-color: var(---background-color--tertiary);\n  --swatch-background-color--hover: var(---background-color--tertiary-hover);\n  --swatch-border-color: var(--color-swatch);\n  --swatch-color: var(---color--tertiary);\n  --swatch-color--hover: var(---color--tertiary-hover);\n  --swatch-color--active: var(---color--tertiary-active);\n}\n.docs-color-swatch.docs-color-swatch--success {\n  --swatch-background-color: var(---background-color--success);\n  --swatch-background-color--hover: var(---background-color--success-hover);\n  --swatch-border-color: var(--color-swatch);\n  --swatch-color: var(---color--success);\n  --swatch-color--hover: var(---color--success-hover);\n  --swatch-color--active: var(---color--success-active);\n}\n.docs-color-swatch.docs-color-swatch--warning {\n  --swatch-background-color: var(---background-color--warning);\n  --swatch-background-color--hover: var(---background-color--warning-hover);\n  --swatch-border-color: var(--color-swatch);\n  --swatch-color: var(---color--warning);\n  --swatch-color--hover: var(---color--warning-hover);\n  --swatch-color--active: var(---color--warning-active);\n}\n.docs-color-swatch.docs-color-swatch--danger {\n  --swatch-background-color: var(---background-color--danger);\n  --swatch-background-color--hover: var(---background-color--danger-hover);\n  --swatch-border-color: var(--color-swatch);\n  --swatch-color: var(---color--danger);\n  --swatch-color--hover: var(---color--danger-hover);\n  --swatch-color--active: var(---color--danger-active);\n}\n.docs-color-swatch.docs-color-swatch--info {\n  --swatch-background-color: var(---background-color--info);\n  --swatch-background-color--hover: var(---background-color--info-hover);\n  --swatch-border-color: var(--color-swatch);\n  --swatch-color: var(---color--info);\n  --swatch-color--hover: var(---color--info-hover);\n  --swatch-color--active: var(---color--info-active);\n}\n.docs-color-swatch.docs-color-swatch--disabled {\n  --swatch-background-color: var(---background-color--disabled);\n  --swatch-background-color--hover: var(---background-color--disabled-hover);\n  --swatch-border-color: var(--color-swatch);\n  --swatch-color: var(---color--disabled);\n  --swatch-color--hover: var(---color--disabled-hover);\n  --swatch-color--active: var(---color--disabled-active);\n}\n.docs-color-swatch.docs-color-swatch--body {\n  --swatch-border-color: var(---color-line--light);\n  --swatch-color: var(---color-text);\n}\n.docs-color-swatch.docs-color-swatch--body-strong {\n  --swatch-border-color: var(---color-line--light);\n  --swatch-color: var(---color-text--dark);\n}\n.docs-color-swatch.docs-color-swatch--body-light {\n  --swatch-border-color: var(---color-line--light);\n  --swatch-color: var(---color-text--light);\n}\n.docs-color-swatch.docs-color-swatch--links {\n  --swatch-border-color: var(---color-line--light);\n  --swatch-color: var(---color-link);\n  --swatch-color--hover: var(---color-link--hover);\n}\n.docs-color-swatch.docs-color-swatch--heading-1 {\n  --swatch-border-color: var(---color-line--light);\n  --swatch-color: var(---color-heading-1);\n}\n.docs-color-swatch.docs-color-swatch--heading-2 {\n  --swatch-border-color: var(---color-line--light);\n  --swatch-color: var(---color-heading-2);\n}\n.docs-color-swatch.docs-color-swatch--heading-3 {\n  --swatch-border-color: var(---color-line--light);\n  --swatch-color: var(---color-heading-3);\n}\n.docs-color-swatch.docs-color-swatch--background-body {\n  --swatch-border-color: var(---color-line--light);\n  --swatch-color: var(---background-color--body);\n}\n.docs-color-swatch.docs-color-swatch--background-content-1 {\n  --swatch-border-color: var(---color-line--light);\n  --swatch-color: var(---background-color--content-1);\n}\n.docs-color-swatch.docs-color-swatch--background-content-2 {\n  --swatch-border-color: var(---color-line--light);\n  --swatch-color: var(---background-color--content-2);\n}\n.docs-color-swatch.docs-color-swatch--background-content-3 {\n  --swatch-border-color: var(---color-line--light);\n  --swatch-color: var(---background-color--content-3);\n}\n.docs-color-swatch.docs-color-swatch--background-reversed-1 {\n  --swatch-border-color: var(---color-line--light);\n  --swatch-color: var(---background-color--content-reversed-1);\n}\n.docs-color-swatch.docs-color-swatch--background-reversed-2 {\n  --swatch-border-color: var(---color-line--light);\n  --swatch-color: var(---background-color--content-reversed-2);\n}\n.docs-color-swatch.docs-color-swatch--gradient-primary {\n  --swatch-border-color: var(---color-line--light);\n  --swatch-background-color: linear-gradient(115deg, var(---color--primary--light) 20%, var(---color--primary--dark) 80%);\n  --swatch-color: transparent;\n}\n.docs-color-swatch.docs-color-swatch--gradient-secondary {\n  --swatch-border-color: var(---color-line--light);\n  --swatch-background-color: linear-gradient(115deg, var(---color--secondary--light) 20%, var(---color--secondary--dark) 80%);\n  --swatch-color: transparent;\n}\n.docs-color-swatch.docs-color-swatch--gradient-tertiary {\n  --swatch-border-color: var(---color-line--light);\n  --swatch-background-color: linear-gradient(115deg, var(---color--tertiary--light) 20%, var(---color--tertiary--dark) 80%);\n  --swatch-color: transparent;\n}\n.docs-color-swatch.docs-color-swatch--gradient-quarternary {\n  --swatch-border-color: var(---color-line--light);\n  --swatch-background-color: linear-gradient(115deg, var(---color--quarternary--light) 20%, var(---color--quarternary--dark) 80%);\n  --swatch-color: transparent;\n}\n\n/* ----- Logos ----- */\n.styleguide-logos {\n  display: grid;\n  grid-template-columns: 10% 10% 10% 10% 10% 10% 10% 10% 10% 10%;\n  background: var(---background-color--content-2);\n}\n.styleguide-logos.styleguide-icons--small {\n  grid-template-columns: 5% 5% 5% 5% 5% 5% 5% 5% 5% 5% 5% 5% 5% 5% 5% 5% 5% !important;\n}\n.styleguide-logos > div {\n  position: relative;\n  background: var(---background-color--content-1);\n  padding: 20px;\n  outline: 1px solid #e6e6e6;\n}\n.styleguide-logos > div svg {\n  width: 100%;\n  height: 100%;\n  max-height: 20px;\n  max-width: 20px;\n}\n.styleguide-logos > div:hover {\n  transform: scale(1.5);\n  z-index: 1;\n}\n.styleguide-logos > div:hover svg * {\n  stroke: var(---color--primary);\n  fill: var(---background-color--primary);\n}"]}