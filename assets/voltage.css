/* Instafeed */
@media only screen and (max-width: 740px) {
  .instafeed-section {
    margin-bottom: 0px;
  }
  .instafeed-section .container--no-horizontal-padding--mobile {
    padding-bottom: 0px;
  }
  #insta-feed {
    gap: 5px;
  }
  #insta-feed>a {
    min-height: 165px;
    min-width: 165px;
  }
}

/* Homepage Custom Section with Icons */
.section.custom-featured-promotions .special-promotion-item__icon-wrapper {
  gap: 10px;
  height: 50px;
}
.section.custom-featured-promotions .special-promotion-item__icon-image {
  height: 40px;
  width: 40px;
}
@media only screen and (min-width: 1001px) {
  .section.custom-featured-promotions .special-promotion-item__icon-image {
    height: 55px;
    width: 55px;
  }
}

.usp-icons-carousel .usp-icons-carousel__icon {
  max-height: 55px;
  max-width: 55px;
  min-width: 56px;
}

/* About Quote */
#shopify-section-template--15069310746735__16636203759a7b7fb7 .heading.h2 {
  font-size: 20px;
}
@media only screen and (min-width: 1001px) {
  #shopify-section-template--15069310746735__16636203759a7b7fb7 .heading.h2 {
    font-size: 28px;
  }
}

/* Bamboo Heading */
#shopify-section-template--15069310681199__16636211958affedc3 .heading.h2 {
  font-size: 20px;
}
@media only screen and (min-width: 1001px) {
  #shopify-section-template--15069310681199__16636211958affedc3 .heading.h2 {
    font-size: 28px;
  }
}

#shopify-section-template--15069310681199__16636211958affedc3 .text-container .h5 {
  text-transform: uppercase;
  letter-spacing: 1px;
}

#shopify-section-template--15069310681199__16636211958affedc3 .container {
  background: #FBF6F3;
  padding: 40px;
  border-radius: 12px;
}
#shopify-section-template--15069310681199__16636211958affedc3 .multi-column__image-wrapper {
  max-width: 50px !important;
}

@media only screen and (max-width: 1000px) {
#shopify-section-template--15069310681199__16636211958affedc3 {
  padding: 0px 24px;
}

#shopify-section-template--15069310681199__16636211958affedc3 section {
  margin-top: 0px;
}
  
#shopify-section-template--15069310681199__16636211958affedc3 .container {
  background: #FBF6F3;
  padding: 40px 0px;
  border-radius: 12px;
}

#shopify-section-template--15069310681199__16636211958affedc3 .multi-column__image-wrapper {
  max-width: 50px !important;
}

#shopify-section-template--15069310681199__16636211958affedc3 .multi-column__item {
max-width: 250px;
margin: 0 auto;
}
}


/* Cart z-index */
.gorgias-chat-key-1spa6uy {
  z-index: 9 !important;
}

/* Mega nav 15" laptop fix */
@media screen and (min-width: 1400px) {
  .mega-menu__image-push {
    width: 200px;
  }
}

@media screen and (min-width: 1600px) {
  .mega-menu__image-push {
    width: 240px;
  }
}

/* ATC Qty height fix for app injections */
.quantity-selector-atc__qty {
  max-height: 66.8px;
}

/* Back In Stock */
#BIS_trigger {
  /* background: #000;
  color: #fff; */
  font-size: 14px;
  /* text-align: center;
  display: block; */
}

/* Afterpay */
afterpay-placement {
  text-align: center;
  margin-bottom: -15px;
}

.captcha-disclaimer {
  display: block;
  margin-top: 5px;
}
.captcha-disclaimer p,
.captcha-disclaimer a {
  font-size: var(---font-size-body-xs--desktop);
  color: var(---color--kyte-dark-grey) !important;
}

/* PDP Upsells Tweak */
.product-item.product-item--upsell .product-item__cta-wrapper .product-item__link {
  text-align: center;
  margin: 0px;
}

.product-item.product-item--upsell .product-item__cta-wrapper .collapsible-toggle {
  padding: 10px 15px; 
}

.product-item.product-item--upsell .block-swatch:not([disabled]) .block-swatch__item {
  
}

/* Mobile Cart Tweak */
@media only screen and (max-width: 500px) {
  .mini-cart .shipping-bar .shipping-bar__text {
    font-size: var(---font-size-body-small--mobile);
  }
  .shopify-section--mini-cart .line-item .product-item-meta__title {
    font-size: var(---font-size-product-title--mobile);
  }
  .shopify-section--mini-cart .line-item .quantity-selector__button, .shopify-section--mini-cart .line-item .quantity-selector__input {
    --quantity-selector-height: 32px !important;
  }
  .shopify-section--mini-cart .line-item .line-item__content-wrapper {
    margin-top: 30px;
  }
  .shopify-section--mini-cart .line-item:first-of-type .line-item__content-wrapper {
    margin-top: 15px;
  }
}

/* Kyte Carousel Review Badge */
.kyte-carousel-reviews-badge {
  margin-right: 4px !important;
}

/* Loop hide GCs */
.loop-returns-activated [data-item-title="Gifts"],
.loop-returns-activated .gift-card .product-form,
.gift-card .loop-returns-activated .product-form {
  display: none;
}

/* Mobile Badge Size */
@media only screen and (max-width: 500px) {
  .custom-collection-banner--split h1 {
    /* font-size: calc(var(---font-size-h1--mobile) - 16px); */
  }
  .custom-collection-banner--split .major {
    /* font-size: 13px; */
  }
  .product-item__inner .label {
    font-size: calc(var(---font-size-body-small--mobile) - 2px);
  }
}

/* Mobile Shortcuts */
@media screen and (min-width: 741px) {
  .header-mobile-shortcuts {
    display: none !important;
  }
}