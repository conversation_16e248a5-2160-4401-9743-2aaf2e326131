/* Version: 1.6.1-kyte - December 15, 2022 13:34:07 */
!function(){"use strict";const t=[];function n(){}Promise.resolve();const e=[];function i(t,i=n){let r;const s=new Set;function o(n){if(o=n,((i=t)!=i?o==o:i!==o||i&&"object"==typeof i||"function"==typeof i)&&(t=n,r)){const n=!e.length;for(const n of s)n[1](),e.push(n,t);if(n){for(let t=0;t<e.length;t+=2)e[t][0](e[t+1]);e.length=0}}var i,o}return{set:o,update:function(n){o(n(t))},subscribe:function(e,a=n){const c=[e,a];return s.add(c),1===s.size&&(r=i(o)||n),e(t),()=>{s.delete(c),0===s.size&&(r(),r=null)}}}}const r={method:"GET",headers:{"Content-Type":"application/json"}};async function s(t,n=r){return(await fetch(t,Object.assign(Object.assign({},n),{method:"GET"}))).json()}async function o(t,n,e=r){const i=n instanceof FormData,s=Object.assign(Object.assign({},e),{method:"POST",headers:Object.assign({"Content-Type":i?"multipart/form-data":"application/json"},e.headers)}),o=i?n:JSON.stringify(n);return(await fetch(t,Object.assign(Object.assign({},s),{body:o}))).json()}function a(t,n,e=7){const i=new Date;i.setDate(i.getDate()+e);const r=JSON.stringify({value:n,expires:i.toJSON()});return localStorage.setItem(t,r),n}function c(t,n=!1){const e=localStorage.getItem(t);try{if(null===e)throw Error(`limitedStorage failed to find ${t}`);const n=JSON.parse(e),i=new Date;return i>=new Date(n.expires)?(localStorage.removeItem(t),null):n.value}catch(t){return null}}var u={set:a,get:c},d="1.6.1-kyte";async function l(t){const n=`/products/${t.handle}.json`,{product:e}=await s(n),i=e.tags.split(", ");return Object.assign(Object.assign(Object.assign({},e),t),{tags:i})}const f="voltage_addons_data";async function g(t,n){if(!m(n)){const{items:e}=n;return async function(t,n){let e=[];const i=n.length;if(void 0===i)throw Error("Malformed incoming data");let r=0;for(;r<i;r++){const i=n[r];if(void 0===i)continue;const s=t.find((t=>t.key===i.key));void 0===s?e.push(l(i)):e.push(Object.assign(Object.assign({},s),i))}return Promise.all(e)}(t,e)}return async function(t,n){const e=t.findIndex((t=>t.key===n.key));if(e>-1){const i=[...t];return i[e]=Object.assign(Object.assign({},t[e]),n),i}return[await l(n),...t]}(t,n)}const m=t=>void 0!==t.product_id;async function y(t){const n=m(t)?await async function(t,n){switch(Object.assign(Object.assign({},r),n).method){case"POST":return await o(t,n);default:return await s(t,n)}}("/cart.js",{method:"GET"}):t,e=await async function(t){const{items:n}=t,e=await Promise.all(n.map(l));return Object.assign(Object.assign({},t),{items:e})}(n),{items:i,attributes:a,note:c,cart_level_discount_applications:g}=e,y={items:i,attributes:a,note:null===c?"":c,discounts:g,version:d};return u.set(f,y)}async function p(t){const n=u.get(f);if(null===n||n.version!==d)try{return await y(t)}catch(t){return console.warn("Storage did not exist and could not be created.",t),n}else try{const e=m(t),i=await g(n.items,t),r=e&&!t.attributes?n.attributes:t.attributes,s=e&&!t.note?n.note:t.note,o=e&&!t.cart_level_discount_applications?n.discounts:t.cart_level_discount_applications;return u.set(f,{items:i,attributes:r,note:null===s?"":s,discounts:o,version:d})}catch(t){return console.warn("Storage could not be updated",t),n}}function h(t,n){if(null===t)return t;const e=t.dataset[n];if(void 0===e)return null;try{return JSON.parse(e)}catch(t){return e}}async function b(){const t=c("customer_country_code");if(!t&&function(){const t=h(document.getElementById("BonusCountdownData"),"settings");return"string"!=typeof t&&null!==t&&Object.keys(t.countries).length>0}())try{const t=await fetch("https://reallyfreegeoip.org/json/"),{country_code:n}=await t.json();return a("customer_country_code",n),n}catch(t){return"null"}return`${t}`}const j=document.querySelector("#FreeGiftsAddon");const w=[h(j,"cart"),h(j,"singleProduct")].filter((function(t){return"string"!=typeof t&&null!==t}));function v(t){return null!==t.properties&&"Free Gift"===t.properties.Promo}function O(t,n){switch(n.type){case"cart":return function(t,n){const e=t.length;let i=Object.assign(Object.assign({},n),{giftsInCart:0,requiredVariantsInCart:0,timesMinimumAdded:0,total:0}),r=0;for(;r<e;r++){const e=t[r],s=v(e);e.variant_id===n.giftId&&s?i.giftsInCart+=e.quantity:s||(i.total+=e.final_line_price),e.variant_id===n.requiredId&&(i.requiredVariantsInCart+=e.quantity)}return i.timesMinimumAdded=Math.floor(i.total/n.requiredAmount),i}(t,n);case"product":return function(t,n){const e=t.length;let i=Object.assign(Object.assign({},n),{giftsInCart:0,total:0,timesMinimumAdded:0}),r=0;for(;r<e;r++){const n=t[r],e=v(n);i.validIds.includes(n.variant_id)&&!e&&(i.total+=n.final_line_price),n.variant_id===i.giftId&&e&&(i.giftsInCart+=n.quantity)}return i.timesMinimumAdded=Math.floor(i.total/n.requiredAmount),i}(t,n);default:throw Error("Invalid Gift Type")}}function _(t){const n=!!t.requiredAmount&&t.requiredAmount>0,e=!!t.requiredId&&t.requiredId>0,i=t.requiredVariantsInCart||0,{timesMinimumAdded:r}=t;let s=0;n&&e?s=i>r?r:i:n?s=r:e?s=i:t.total>0&&(s=1);const{giftMax:o}=t;return o>0&&(n||e)&&s>o?o:s}async function I(t,n,e){if(0===e.giftsInCart)return await async function(t,n){const e=[{id:t,quantity:n,properties:{Promo:"Free Gift"}}],{items:i}=await o("/cart/add.js",{items:e});return i[0]}(e.giftId,n);const i=function(t,n){const e="function"==typeof n?n:t=>(Array.isArray(n)?n:[n]).includes(t.variant_id);let i=[],r=0;const s=t.length;for(;r<s;r++){const n=t[r];void 0!==n&&e(n)&&i.push({line:r+1,variant:n.variant_id,quantity:n.quantity,properties:Object.assign({},n.properties)})}return i}(t,(t=>{const n=v(t);return t.variant_id===e.giftId&&n}))[0];if(void 0===i)throw Error("Supposed pre-existing gift item not found");return await async function(t,n){return await o("/cart/change.js",{line:t,quantity:n})}(i.line,n)}async function q(t,n){let e=[],i=[];for(let n=w.length-1;n>=0;n--)try{const r=w[n];if(void 0===r)continue;const s=O(t,r),o=_(s),{giftsInCart:a}=s,c=a===o;i.push(r.giftId),c||e.push(I(t,o,s))}catch(t){}let r=function(t,n){return t.filter((t=>v(t)&&n.indexOf(t.variant_id)<0)).reduce(((t,n)=>Object.assign(Object.assign({},t),{[n.key]:0})),{})}(t,i);Object.keys(r).length>0&&e.push(async function(t){return await o("/cart/update.js",{updates:t})}(r)),Promise.all(e).then(n)}const S=i([]),E=i({}),A=i(""),C=i([]),M=i("");function P(t){S.set(t.items),E.set(Object.assign({},t.attributes)),A.set(t.note),C.set(t.discounts)}document.addEventListener("cart:updated",(function(t){const{cart:n}=t.detail;!function(t){p(t).then((t=>{null!==t&&P(t)}))}(n)})),S.subscribe((t=>{q(t,(async t=>{if(t.length>0){!async function(t){const n=new CustomEvent("cart:refresh",{bubbles:!0,detail:{cart:t}});document.documentElement.dispatchEvent(n)}(await s("/cart.js"))}}))}));const k=".grid-product:not(.grid-promotion-ad)";function x(t){const n=document.querySelector(k);null!==n&&t.forEach((t=>{t.style.height=`${n.clientHeight}px`,t.classList.remove("faded")}))}const T=[function(){const t=Array.from(document.querySelectorAll(".grid-promotion-ad")),[n]=t;if(void 0===n)return;const e=setInterval((()=>{null!==function(t){const n=document.querySelector(k);if(null!==n){const e=n.querySelector("img");if(null!==e){if(""==e.srcset)return null;let n=new Image;n.onload=()=>{x(t)},n.srcset=e.srcset}else window.addEventListener("load",(()=>x(t)));window.addEventListener("resize",(()=>x(t)))}}(t)&&clearInterval(e)}),250)}];!async function(){T.forEach((t=>t())),await async function(){const t=await s("/cart.js"),n=await b(),e=await p(t);null!==e&&(P(e),M.set(n))}(),t.forEach((t=>t.mount()))}()}();
