var vt=Object.create;var tt=Object.defineProperty;var mt=Object.getOwnPropertyDescriptor;var yt=Object.getOwnPropertyNames;var St=Object.getPrototypeOf,bt=Object.prototype.hasOwnProperty;var x=(h,o)=>()=>(o||h((o={exports:{}}).exports,o),o.exports);var Et=(h,o,c,l)=>{if(o&&typeof o=="object"||typeof o=="function")for(let s of yt(o))!bt.call(h,s)&&s!==c&&tt(h,s,{get:()=>o[s],enumerable:!(l=mt(o,s))||l.enumerable});return h};var xt=(h,o,c)=>(c=h!=null?vt(St(h)):{},Et(o||!h||!h.__esModule?tt(c,"default",{value:h,enumerable:!0}):c,h));var A=x((et,I)=>{(function(h,o){typeof define=="function"&&define.amd?define(o):typeof I=="object"&&I.exports?I.exports=o():h.EvEmitter=o()})(typeof window<"u"?window:et,function(){"use strict";function h(){}var o=h.prototype;return o.on=function(c,l){if(!(!c||!l)){var s=this._events=this._events||{},i=s[c]=s[c]||[];return i.indexOf(l)==-1&&i.push(l),this}},o.once=function(c,l){if(!(!c||!l)){this.on(c,l);var s=this._onceEvents=this._onceEvents||{},i=s[c]=s[c]||{};return i[l]=!0,this}},o.off=function(c,l){var s=this._events&&this._events[c];if(!(!s||!s.length)){var i=s.indexOf(l);return i!=-1&&s.splice(i,1),this}},o.emitEvent=function(c,l){var s=this._events&&this._events[c];if(!(!s||!s.length)){s=s.slice(0),l=l||[];for(var i=this._onceEvents&&this._onceEvents[c],a=0;a<s.length;a++){var t=s[a],e=i&&i[t];e&&(this.off(c,t),delete i[t]),t.apply(this,l)}return this}},o.allOff=function(){delete this._events,delete this._onceEvents},h})});var Y=x((Dt,L)=>{(function(h,o){typeof define=="function"&&define.amd?define(o):typeof L=="object"&&L.exports?L.exports=o():h.getSize=o()})(window,function(){"use strict";function o(u){var m=parseFloat(u),v=u.indexOf("%")==-1&&!isNaN(m);return v&&m}function c(){}var l=typeof console>"u"?c:function(u){console.error(u)},s=["paddingLeft","paddingRight","paddingTop","paddingBottom","marginLeft","marginRight","marginTop","marginBottom","borderLeftWidth","borderRightWidth","borderTopWidth","borderBottomWidth"],i=s.length;function a(){for(var u={width:0,height:0,innerWidth:0,innerHeight:0,outerWidth:0,outerHeight:0},m=0;m<i;m++){var v=s[m];u[v]=0}return u}function t(u){var m=getComputedStyle(u);return m||l("Style returned "+m+". Are you running this code in a hidden iframe on Firefox? See https://bit.ly/getsizebug1"),m}var e=!1,r;function d(){if(!e){e=!0;var u=document.createElement("div");u.style.width="200px",u.style.padding="1px 2px 3px 4px",u.style.borderStyle="solid",u.style.borderWidth="1px 2px 3px 4px",u.style.boxSizing="border-box";var m=document.body||document.documentElement;m.appendChild(u);var v=t(u);r=Math.round(o(v.width))==200,p.isBoxSizeOuter=r,m.removeChild(u)}}function p(u){if(d(),typeof u=="string"&&(u=document.querySelector(u)),!(!u||typeof u!="object"||!u.nodeType)){var m=t(u);if(m.display=="none")return a();var v={};v.width=u.offsetWidth,v.height=u.offsetHeight;for(var g=v.isBorderBox=m.boxSizing=="border-box",C=0;C<i;C++){var n=s[C],f=m[n],y=parseFloat(f);v[n]=isNaN(y)?0:y}var S=v.paddingLeft+v.paddingRight,b=v.paddingTop+v.paddingBottom,E=v.marginLeft+v.marginRight,D=v.marginTop+v.marginBottom,z=v.borderLeftWidth+v.borderRightWidth,J=v.borderTopWidth+v.borderBottomWidth,$=g&&r,Z=o(m.width);Z!==!1&&(v.width=Z+($?0:S+z));var K=o(m.height);return K!==!1&&(v.height=K+($?0:b+J)),v.innerWidth=v.width-(S+z),v.innerHeight=v.height-(b+J),v.outerWidth=v.width+E,v.outerHeight=v.height+D,v}}return p})});var it=x((Pt,k)=>{(function(h,o){"use strict";typeof define=="function"&&define.amd?define(o):typeof k=="object"&&k.exports?k.exports=o():h.matchesSelector=o()})(window,function(){"use strict";var o=function(){var c=window.Element.prototype;if(c.matches)return"matches";if(c.matchesSelector)return"matchesSelector";for(var l=["webkit","moz","ms","o"],s=0;s<l.length;s++){var i=l[s],a=i+"MatchesSelector";if(c[a])return a}}();return function(l,s){return l[o](s)}})});var P=x((zt,M)=>{(function(h,o){typeof define=="function"&&define.amd?define(["desandro-matches-selector/matches-selector"],function(c){return o(h,c)}):typeof M=="object"&&M.exports?M.exports=o(h,it()):h.fizzyUIUtils=o(h,h.matchesSelector)})(window,function(o,c){"use strict";var l={};l.extend=function(a,t){for(var e in t)a[e]=t[e];return a},l.modulo=function(a,t){return(a%t+t)%t};var s=Array.prototype.slice;l.makeArray=function(a){if(Array.isArray(a))return a;if(a==null)return[];var t=typeof a=="object"&&typeof a.length=="number";return t?s.call(a):[a]},l.removeFrom=function(a,t){var e=a.indexOf(t);e!=-1&&a.splice(e,1)},l.getParent=function(a,t){for(;a.parentNode&&a!=document.body;)if(a=a.parentNode,c(a,t))return a},l.getQueryElement=function(a){return typeof a=="string"?document.querySelector(a):a},l.handleEvent=function(a){var t="on"+a.type;this[t]&&this[t](a)},l.filterFindElements=function(a,t){a=l.makeArray(a);var e=[];return a.forEach(function(r){if(r instanceof HTMLElement){if(!t){e.push(r);return}c(r,t)&&e.push(r);for(var d=r.querySelectorAll(t),p=0;p<d.length;p++)e.push(d[p])}}),e},l.debounceMethod=function(a,t,e){e=e||100;var r=a.prototype[t],d=t+"Timeout";a.prototype[t]=function(){var p=this[d];clearTimeout(p);var u=arguments,m=this;this[d]=setTimeout(function(){r.apply(m,u),delete m[d]},e)}},l.docReady=function(a){var t=document.readyState;t=="complete"||t=="interactive"?setTimeout(a):document.addEventListener("DOMContentLoaded",a)},l.toDashed=function(a){return a.replace(/(.)([A-Z])/g,function(t,e,r){return e+"-"+r}).toLowerCase()};var i=o.console;return l.htmlInit=function(a,t){l.docReady(function(){var e=l.toDashed(t),r="data-"+e,d=document.querySelectorAll("["+r+"]"),p=document.querySelectorAll(".js-"+e),u=l.makeArray(d).concat(l.makeArray(p)),m=r+"-options",v=o.jQuery;u.forEach(function(g){var C=g.getAttribute(r)||g.getAttribute(m),n;try{n=C&&JSON.parse(C)}catch(y){i&&i.error("Error parsing "+r+" on "+g.className+": "+y);return}var f=new a(g,n);v&&v.data(g,t,f)})})},l})});var st=x((_t,W)=>{(function(h,o){typeof define=="function"&&define.amd?define(["get-size/get-size"],function(c){return o(h,c)}):typeof W=="object"&&W.exports?W.exports=o(h,Y()):(h.Flickity=h.Flickity||{},h.Flickity.Cell=o(h,h.getSize))})(window,function(o,c){"use strict";function l(i,a){this.element=i,this.parent=a,this.create()}var s=l.prototype;return s.create=function(){this.element.style.position="absolute",this.element.setAttribute("aria-hidden","true"),this.x=0,this.shift=0,this.element.style[this.parent.originSide]=0},s.destroy=function(){this.unselect(),this.element.style.position="";var i=this.parent.originSide;this.element.style[i]="",this.element.style.transform="",this.element.removeAttribute("aria-hidden")},s.getSize=function(){this.size=c(this.element)},s.setPosition=function(i){this.x=i,this.updateTarget(),this.renderPosition(i)},s.updateTarget=s.setDefaultTarget=function(){var i=this.parent.originSide=="left"?"marginLeft":"marginRight";this.target=this.x+this.size[i]+this.size.width*this.parent.cellAlign},s.renderPosition=function(i){var a=this.parent.originSide==="left"?1:-1,t=this.parent.options.percentPosition?i*a*(this.parent.size.innerWidth/this.size.width):i*a;this.element.style.transform="translateX("+this.parent.getPositionValue(t)+")"},s.select=function(){this.element.classList.add("is-selected"),this.element.removeAttribute("aria-hidden")},s.unselect=function(){this.element.classList.remove("is-selected"),this.element.setAttribute("aria-hidden","true")},s.wrapShift=function(i){this.shift=i,this.renderPosition(this.x+this.parent.slideableWidth*i)},s.remove=function(){this.element.parentNode.removeChild(this.element)},l})});var nt=x((It,T)=>{(function(h,o){typeof define=="function"&&define.amd?define(o):typeof T=="object"&&T.exports?T.exports=o():(h.Flickity=h.Flickity||{},h.Flickity.Slide=o())})(window,function(){"use strict";function o(l){this.parent=l,this.isOriginLeft=l.originSide=="left",this.cells=[],this.outerWidth=0,this.height=0}var c=o.prototype;return c.addCell=function(l){if(this.cells.push(l),this.outerWidth+=l.size.outerWidth,this.height=Math.max(l.size.outerHeight,this.height),this.cells.length==1){this.x=l.x;var s=this.isOriginLeft?"marginLeft":"marginRight";this.firstMargin=l.size[s]}},c.updateTarget=function(){var l=this.isOriginLeft?"marginRight":"marginLeft",s=this.getLastCell(),i=s?s.size[l]:0,a=this.outerWidth-(this.firstMargin+i);this.target=this.x+this.firstMargin+a*this.parent.cellAlign},c.getLastCell=function(){return this.cells[this.cells.length-1]},c.select=function(){this.cells.forEach(function(l){l.select()})},c.unselect=function(){this.cells.forEach(function(l){l.unselect()})},c.getCellElements=function(){return this.cells.map(function(l){return l.element})},o})});var rt=x((At,F)=>{(function(h,o){typeof define=="function"&&define.amd?define(["fizzy-ui-utils/utils"],function(c){return o(h,c)}):typeof F=="object"&&F.exports?F.exports=o(h,P()):(h.Flickity=h.Flickity||{},h.Flickity.animatePrototype=o(h,h.fizzyUIUtils))})(window,function(o,c){"use strict";var l={};return l.startAnimation=function(){this.isAnimating||(this.isAnimating=!0,this.restingFrames=0,this.animate())},l.animate=function(){this.applyDragForce(),this.applySelectedAttraction();var s=this.x;if(this.integratePhysics(),this.positionSlider(),this.settle(s),this.isAnimating){var i=this;requestAnimationFrame(function(){i.animate()})}},l.positionSlider=function(){var s=this.x;this.options.wrapAround&&this.cells.length>1&&(s=c.modulo(s,this.slideableWidth),s-=this.slideableWidth,this.shiftWrapCells(s)),this.setTranslateX(s,this.isAnimating),this.dispatchScrollEvent()},l.setTranslateX=function(s,i){s+=this.cursorPosition,s=this.options.rightToLeft?-s:s;var a=this.getPositionValue(s);this.slider.style.transform=i?"translate3d("+a+",0,0)":"translateX("+a+")"},l.dispatchScrollEvent=function(){var s=this.slides[0];if(s){var i=-this.x-s.target,a=i/this.slidesWidth;this.dispatchEvent("scroll",null,[a,i])}},l.positionSliderAtSelected=function(){this.cells.length&&(this.x=-this.selectedSlide.target,this.velocity=0,this.positionSlider())},l.getPositionValue=function(s){return this.options.percentPosition?Math.round(s/this.size.innerWidth*1e4)*.01+"%":Math.round(s)+"px"},l.settle=function(s){var i=!this.isPointerDown&&Math.round(this.x*100)==Math.round(s*100);i&&this.restingFrames++,this.restingFrames>2&&(this.isAnimating=!1,delete this.isFreeScrolling,this.positionSlider(),this.dispatchEvent("settle",null,[this.selectedIndex]))},l.shiftWrapCells=function(s){var i=this.cursorPosition+s;this._shiftCells(this.beforeShiftCells,i,-1);var a=this.size.innerWidth-(s+this.slideableWidth+this.cursorPosition);this._shiftCells(this.afterShiftCells,a,1)},l._shiftCells=function(s,i,a){for(var t=0;t<s.length;t++){var e=s[t],r=i>0?a:0;e.wrapShift(r),i-=e.size.outerWidth}},l._unshiftCells=function(s){if(!(!s||!s.length))for(var i=0;i<s.length;i++)s[i].wrapShift(0)},l.integratePhysics=function(){this.x+=this.velocity,this.velocity*=this.getFrictionFactor()},l.applyForce=function(s){this.velocity+=s},l.getFrictionFactor=function(){return 1-this.options[this.isFreeScrolling?"freeScrollFriction":"friction"]},l.getRestingPosition=function(){return this.x+this.velocity/(1-this.getFrictionFactor())},l.applyDragForce=function(){if(!(!this.isDraggable||!this.isPointerDown)){var s=this.dragX-this.x,i=s-this.velocity;this.applyForce(i)}},l.applySelectedAttraction=function(){var s=this.isDraggable&&this.isPointerDown;if(!(s||this.isFreeScrolling||!this.slides.length)){var i=this.selectedSlide.target*-1-this.x,a=i*this.options.selectedAttraction;this.applyForce(a)}},l})});var _=x((Lt,U)=>{(function(h,o){if(typeof define=="function"&&define.amd)define(["ev-emitter/ev-emitter","get-size/get-size","fizzy-ui-utils/utils","./cell","./slide","./animate"],function(l,s,i,a,t,e){return o(h,l,s,i,a,t,e)});else if(typeof U=="object"&&U.exports)U.exports=o(h,A(),Y(),P(),st(),nt(),rt());else{var c=h.Flickity;h.Flickity=o(h,h.EvEmitter,h.getSize,h.fizzyUIUtils,c.Cell,c.Slide,c.animatePrototype)}})(window,function(o,c,l,s,i,a,t){"use strict";var e=o.jQuery,r=o.getComputedStyle,d=o.console;function p(n,f){for(n=s.makeArray(n);n.length;)f.appendChild(n.shift())}var u=0,m={};function v(n,f){var y=s.getQueryElement(n);if(!y){d&&d.error("Bad element for Flickity: "+(y||n));return}if(this.element=y,this.element.flickityGUID){var S=m[this.element.flickityGUID];return S&&S.option(f),S}e&&(this.$element=e(this.element)),this.options=s.extend({},this.constructor.defaults),this.option(f),this._create()}v.defaults={accessibility:!0,cellAlign:"center",freeScrollFriction:.075,friction:.28,namespaceJQueryEvents:!0,percentPosition:!0,resize:!0,selectedAttraction:.025,setGallerySize:!0},v.createMethods=[];var g=v.prototype;s.extend(g,c.prototype),g._create=function(){var n=this.guid=++u;this.element.flickityGUID=n,m[n]=this,this.selectedIndex=0,this.restingFrames=0,this.x=0,this.velocity=0,this.originSide=this.options.rightToLeft?"right":"left",this.viewport=document.createElement("div"),this.viewport.className="flickity-viewport",this._createSlider(),(this.options.resize||this.options.watchCSS)&&o.addEventListener("resize",this);for(var f in this.options.on){var y=this.options.on[f];this.on(f,y)}v.createMethods.forEach(function(S){this[S]()},this),this.options.watchCSS?this.watchCSS():this.activate()},g.option=function(n){s.extend(this.options,n)},g.activate=function(){if(!this.isActive){this.isActive=!0,this.element.classList.add("flickity-enabled"),this.options.rightToLeft&&this.element.classList.add("flickity-rtl"),this.getSize();var n=this._filterFindCellElements(this.element.children);p(n,this.slider),this.viewport.appendChild(this.slider),this.element.appendChild(this.viewport),this.reloadCells(),this.options.accessibility&&(this.element.tabIndex=0,this.element.addEventListener("keydown",this)),this.emitEvent("activate"),this.selectInitialIndex(),this.isInitActivated=!0,this.dispatchEvent("ready")}},g._createSlider=function(){var n=document.createElement("div");n.className="flickity-slider",n.style[this.originSide]=0,this.slider=n},g._filterFindCellElements=function(n){return s.filterFindElements(n,this.options.cellSelector)},g.reloadCells=function(){this.cells=this._makeCells(this.slider.children),this.positionCells(),this._getWrapShiftCells(),this.setGallerySize()},g._makeCells=function(n){var f=this._filterFindCellElements(n),y=f.map(function(S){return new i(S,this)},this);return y},g.getLastCell=function(){return this.cells[this.cells.length-1]},g.getLastSlide=function(){return this.slides[this.slides.length-1]},g.positionCells=function(){this._sizeCells(this.cells),this._positionCells(0)},g._positionCells=function(n){n=n||0,this.maxCellHeight=n&&this.maxCellHeight||0;var f=0;if(n>0){var y=this.cells[n-1];f=y.x+y.size.outerWidth}for(var S=this.cells.length,b=n;b<S;b++){var E=this.cells[b];E.setPosition(f),f+=E.size.outerWidth,this.maxCellHeight=Math.max(E.size.outerHeight,this.maxCellHeight)}this.slideableWidth=f,this.updateSlides(),this._containSlides(),this.slidesWidth=S?this.getLastSlide().target-this.slides[0].target:0},g._sizeCells=function(n){n.forEach(function(f){f.getSize()})},g.updateSlides=function(){if(this.slides=[],!!this.cells.length){var n=new a(this);this.slides.push(n);var f=this.originSide=="left",y=f?"marginRight":"marginLeft",S=this._getCanCellFit();this.cells.forEach(function(b,E){if(!n.cells.length){n.addCell(b);return}var D=n.outerWidth-n.firstMargin+(b.size.outerWidth-b.size[y]);S.call(this,E,D)||(n.updateTarget(),n=new a(this),this.slides.push(n)),n.addCell(b)},this),n.updateTarget(),this.updateSelectedSlide()}},g._getCanCellFit=function(){var n=this.options.groupCells;if(n){if(typeof n=="number"){var f=parseInt(n,10);return function(b){return b%f!==0}}}else return function(){return!1};var y=typeof n=="string"&&n.match(/^(\d+)%$/),S=y?parseInt(y[1],10)/100:1;return function(b,E){return E<=(this.size.innerWidth+1)*S}},g._init=g.reposition=function(){this.positionCells(),this.positionSliderAtSelected()},g.getSize=function(){this.size=l(this.element),this.setCellAlign(),this.cursorPosition=this.size.innerWidth*this.cellAlign};var C={center:{left:.5,right:.5},left:{left:0,right:1},right:{right:0,left:1}};return g.setCellAlign=function(){var n=C[this.options.cellAlign];this.cellAlign=n?n[this.originSide]:this.options.cellAlign},g.setGallerySize=function(){if(this.options.setGallerySize){var n=this.options.adaptiveHeight&&this.selectedSlide?this.selectedSlide.height:this.maxCellHeight;this.viewport.style.height=n+"px"}},g._getWrapShiftCells=function(){if(this.options.wrapAround){this._unshiftCells(this.beforeShiftCells),this._unshiftCells(this.afterShiftCells);var n=this.cursorPosition,f=this.cells.length-1;this.beforeShiftCells=this._getGapCells(n,f,-1),n=this.size.innerWidth-this.cursorPosition,this.afterShiftCells=this._getGapCells(n,0,1)}},g._getGapCells=function(n,f,y){for(var S=[];n>0;){var b=this.cells[f];if(!b)break;S.push(b),f+=y,n-=b.size.outerWidth}return S},g._containSlides=function(){if(!(!this.options.contain||this.options.wrapAround||!this.cells.length)){var n=this.options.rightToLeft,f=n?"marginRight":"marginLeft",y=n?"marginLeft":"marginRight",S=this.slideableWidth-this.getLastCell().size[y],b=S<this.size.innerWidth,E=this.cursorPosition+this.cells[0].size[f],D=S-this.size.innerWidth*(1-this.cellAlign);this.slides.forEach(function(z){b?z.target=S*this.cellAlign:(z.target=Math.max(z.target,E),z.target=Math.min(z.target,D))},this)}},g.dispatchEvent=function(n,f,y){var S=f?[f].concat(y):y;if(this.emitEvent(n,S),e&&this.$element){n+=this.options.namespaceJQueryEvents?".flickity":"";var b=n;if(f){var E=new e.Event(f);E.type=n,b=E}this.$element.trigger(b,y)}},g.select=function(n,f,y){if(this.isActive&&(n=parseInt(n,10),this._wrapSelect(n),(this.options.wrapAround||f)&&(n=s.modulo(n,this.slides.length)),!!this.slides[n])){var S=this.selectedIndex;this.selectedIndex=n,this.updateSelectedSlide(),y?this.positionSliderAtSelected():this.startAnimation(),this.options.adaptiveHeight&&this.setGallerySize(),this.dispatchEvent("select",null,[n]),n!=S&&this.dispatchEvent("change",null,[n]),this.dispatchEvent("cellSelect")}},g._wrapSelect=function(n){var f=this.slides.length,y=this.options.wrapAround&&f>1;if(!y)return n;var S=s.modulo(n,f),b=Math.abs(S-this.selectedIndex),E=Math.abs(S+f-this.selectedIndex),D=Math.abs(S-f-this.selectedIndex);!this.isDragSelect&&E<b?n+=f:!this.isDragSelect&&D<b&&(n-=f),n<0?this.x-=this.slideableWidth:n>=f&&(this.x+=this.slideableWidth)},g.previous=function(n,f){this.select(this.selectedIndex-1,n,f)},g.next=function(n,f){this.select(this.selectedIndex+1,n,f)},g.updateSelectedSlide=function(){var n=this.slides[this.selectedIndex];n&&(this.unselectSelectedSlide(),this.selectedSlide=n,n.select(),this.selectedCells=n.cells,this.selectedElements=n.getCellElements(),this.selectedCell=n.cells[0],this.selectedElement=this.selectedElements[0])},g.unselectSelectedSlide=function(){this.selectedSlide&&this.selectedSlide.unselect()},g.selectInitialIndex=function(){var n=this.options.initialIndex;if(this.isInitActivated){this.select(this.selectedIndex,!1,!0);return}if(n&&typeof n=="string"){var f=this.queryCell(n);if(f){this.selectCell(n,!1,!0);return}}var y=0;n&&this.slides[n]&&(y=n),this.select(y,!1,!0)},g.selectCell=function(n,f,y){var S=this.queryCell(n);if(S){var b=this.getCellSlideIndex(S);this.select(b,f,y)}},g.getCellSlideIndex=function(n){for(var f=0;f<this.slides.length;f++){var y=this.slides[f],S=y.cells.indexOf(n);if(S!=-1)return f}},g.getCell=function(n){for(var f=0;f<this.cells.length;f++){var y=this.cells[f];if(y.element==n)return y}},g.getCells=function(n){n=s.makeArray(n);var f=[];return n.forEach(function(y){var S=this.getCell(y);S&&f.push(S)},this),f},g.getCellElements=function(){return this.cells.map(function(n){return n.element})},g.getParentCell=function(n){var f=this.getCell(n);return f||(n=s.getParent(n,".flickity-slider > *"),this.getCell(n))},g.getAdjacentCellElements=function(n,f){if(!n)return this.selectedSlide.getCellElements();f=f===void 0?this.selectedIndex:f;var y=this.slides.length;if(1+n*2>=y)return this.getCellElements();for(var S=[],b=f-n;b<=f+n;b++){var E=this.options.wrapAround?s.modulo(b,y):b,D=this.slides[E];D&&(S=S.concat(D.getCellElements()))}return S},g.queryCell=function(n){if(typeof n=="number")return this.cells[n];if(typeof n=="string"){if(n.match(/^[#.]?[\d/]/))return;n=this.element.querySelector(n)}return this.getCell(n)},g.uiChange=function(){this.emitEvent("uiChange")},g.childUIPointerDown=function(n){n.type!="touchstart"&&n.preventDefault(),this.focus()},g.onresize=function(){this.watchCSS(),this.resize()},s.debounceMethod(v,"onresize",150),g.resize=function(){if(!(!this.isActive||this.isAnimating||this.isDragging)){this.getSize(),this.options.wrapAround&&(this.x=s.modulo(this.x,this.slideableWidth)),this.positionCells(),this._getWrapShiftCells(),this.setGallerySize(),this.emitEvent("resize");var n=this.selectedElements&&this.selectedElements[0];this.selectCell(n,!1,!0)}},g.watchCSS=function(){var n=this.options.watchCSS;if(n){var f=r(this.element,":after").content;f.indexOf("flickity")!=-1?this.activate():this.deactivate()}},g.onkeydown=function(n){var f=document.activeElement&&document.activeElement!=this.element;if(!(!this.options.accessibility||f)){var y=v.keyboardHandlers[n.keyCode];y&&y.call(this)}},v.keyboardHandlers={37:function(){var n=this.options.rightToLeft?"next":"previous";this.uiChange(),this[n]()},39:function(){var n=this.options.rightToLeft?"previous":"next";this.uiChange(),this[n]()}},g.focus=function(){var n=o.pageYOffset;this.element.focus({preventScroll:!0}),o.pageYOffset!=n&&o.scrollTo(o.pageXOffset,n)},g.deactivate=function(){this.isActive&&(this.element.classList.remove("flickity-enabled"),this.element.classList.remove("flickity-rtl"),this.unselectSelectedSlide(),this.cells.forEach(function(n){n.destroy()}),this.element.removeChild(this.viewport),p(this.slider.children,this.element),this.options.accessibility&&(this.element.removeAttribute("tabIndex"),this.element.removeEventListener("keydown",this)),this.isActive=!1,this.emitEvent("deactivate"))},g.destroy=function(){this.deactivate(),o.removeEventListener("resize",this),this.allOff(),this.emitEvent("destroy"),e&&this.$element&&e.removeData(this.element,"flickity"),delete this.element.flickityGUID,delete m[this.guid]},s.extend(g,t),v.data=function(n){n=s.getQueryElement(n);var f=n&&n.flickityGUID;return f&&m[f]},s.htmlInit(v,"flickity"),e&&e.bridget&&e.bridget("flickity",v),v.setJQuery=function(n){e=n},v.Cell=i,v.Slide=a,v})});var q=x((kt,O)=>{(function(h,o){typeof define=="function"&&define.amd?define(["ev-emitter/ev-emitter"],function(c){return o(h,c)}):typeof O=="object"&&O.exports?O.exports=o(h,A()):h.Unipointer=o(h,h.EvEmitter)})(window,function(o,c){"use strict";function l(){}function s(){}var i=s.prototype=Object.create(c.prototype);i.bindStartEvent=function(t){this._bindStartEvent(t,!0)},i.unbindStartEvent=function(t){this._bindStartEvent(t,!1)},i._bindStartEvent=function(t,e){e=e===void 0?!0:e;var r=e?"addEventListener":"removeEventListener",d="mousedown";"ontouchstart"in o?d="touchstart":o.PointerEvent&&(d="pointerdown"),t[r](d,this)},i.handleEvent=function(t){var e="on"+t.type;this[e]&&this[e](t)},i.getTouch=function(t){for(var e=0;e<t.length;e++){var r=t[e];if(r.identifier==this.pointerIdentifier)return r}},i.onmousedown=function(t){var e=t.button;e&&e!==0&&e!==1||this._pointerDown(t,t)},i.ontouchstart=function(t){this._pointerDown(t,t.changedTouches[0])},i.onpointerdown=function(t){this._pointerDown(t,t)},i._pointerDown=function(t,e){t.button||this.isPointerDown||(this.isPointerDown=!0,this.pointerIdentifier=e.pointerId!==void 0?e.pointerId:e.identifier,this.pointerDown(t,e))},i.pointerDown=function(t,e){this._bindPostStartEvents(t),this.emitEvent("pointerDown",[t,e])};var a={mousedown:["mousemove","mouseup"],touchstart:["touchmove","touchend","touchcancel"],pointerdown:["pointermove","pointerup","pointercancel"]};return i._bindPostStartEvents=function(t){if(t){var e=a[t.type];e.forEach(function(r){o.addEventListener(r,this)},this),this._boundPointerEvents=e}},i._unbindPostStartEvents=function(){this._boundPointerEvents&&(this._boundPointerEvents.forEach(function(t){o.removeEventListener(t,this)},this),delete this._boundPointerEvents)},i.onmousemove=function(t){this._pointerMove(t,t)},i.onpointermove=function(t){t.pointerId==this.pointerIdentifier&&this._pointerMove(t,t)},i.ontouchmove=function(t){var e=this.getTouch(t.changedTouches);e&&this._pointerMove(t,e)},i._pointerMove=function(t,e){this.pointerMove(t,e)},i.pointerMove=function(t,e){this.emitEvent("pointerMove",[t,e])},i.onmouseup=function(t){this._pointerUp(t,t)},i.onpointerup=function(t){t.pointerId==this.pointerIdentifier&&this._pointerUp(t,t)},i.ontouchend=function(t){var e=this.getTouch(t.changedTouches);e&&this._pointerUp(t,e)},i._pointerUp=function(t,e){this._pointerDone(),this.pointerUp(t,e)},i.pointerUp=function(t,e){this.emitEvent("pointerUp",[t,e])},i._pointerDone=function(){this._pointerReset(),this._unbindPostStartEvents(),this.pointerDone()},i._pointerReset=function(){this.isPointerDown=!1,delete this.pointerIdentifier},i.pointerDone=l,i.onpointercancel=function(t){t.pointerId==this.pointerIdentifier&&this._pointerCancel(t,t)},i.ontouchcancel=function(t){var e=this.getTouch(t.changedTouches);e&&this._pointerCancel(t,e)},i._pointerCancel=function(t,e){this._pointerDone(),this.pointerCancel(t,e)},i.pointerCancel=function(t,e){this.emitEvent("pointerCancel",[t,e])},s.getPointerPoint=function(t){return{x:t.pageX,y:t.pageY}},s})});var ot=x((Mt,w)=>{(function(h,o){typeof define=="function"&&define.amd?define(["unipointer/unipointer"],function(c){return o(h,c)}):typeof w=="object"&&w.exports?w.exports=o(h,q()):h.Unidragger=o(h,h.Unipointer)})(window,function(o,c){"use strict";function l(){}var s=l.prototype=Object.create(c.prototype);s.bindHandles=function(){this._bindHandles(!0)},s.unbindHandles=function(){this._bindHandles(!1)},s._bindHandles=function(t){t=t===void 0?!0:t;for(var e=t?"addEventListener":"removeEventListener",r=t?this._touchActionValue:"",d=0;d<this.handles.length;d++){var p=this.handles[d];this._bindStartEvent(p,t),p[e]("click",this),o.PointerEvent&&(p.style.touchAction=r)}},s._touchActionValue="none",s.pointerDown=function(t,e){var r=this.okayPointerDown(t);r&&(this.pointerDownPointer={pageX:e.pageX,pageY:e.pageY},t.preventDefault(),this.pointerDownBlur(),this._bindPostStartEvents(t),this.emitEvent("pointerDown",[t,e]))};var i={TEXTAREA:!0,INPUT:!0,SELECT:!0,OPTION:!0},a={radio:!0,checkbox:!0,button:!0,submit:!0,image:!0,file:!0};return s.okayPointerDown=function(t){var e=i[t.target.nodeName],r=a[t.target.type],d=!e||r;return d||this._pointerReset(),d},s.pointerDownBlur=function(){var t=document.activeElement,e=t&&t.blur&&t!=document.body;e&&t.blur()},s.pointerMove=function(t,e){var r=this._dragPointerMove(t,e);this.emitEvent("pointerMove",[t,e,r]),this._dragMove(t,e,r)},s._dragPointerMove=function(t,e){var r={x:e.pageX-this.pointerDownPointer.pageX,y:e.pageY-this.pointerDownPointer.pageY};return!this.isDragging&&this.hasDragStarted(r)&&this._dragStart(t,e),r},s.hasDragStarted=function(t){return Math.abs(t.x)>3||Math.abs(t.y)>3},s.pointerUp=function(t,e){this.emitEvent("pointerUp",[t,e]),this._dragPointerUp(t,e)},s._dragPointerUp=function(t,e){this.isDragging?this._dragEnd(t,e):this._staticClick(t,e)},s._dragStart=function(t,e){this.isDragging=!0,this.isPreventingClicks=!0,this.dragStart(t,e)},s.dragStart=function(t,e){this.emitEvent("dragStart",[t,e])},s._dragMove=function(t,e,r){this.isDragging&&this.dragMove(t,e,r)},s.dragMove=function(t,e,r){t.preventDefault(),this.emitEvent("dragMove",[t,e,r])},s._dragEnd=function(t,e){this.isDragging=!1,setTimeout((function(){delete this.isPreventingClicks}).bind(this)),this.dragEnd(t,e)},s.dragEnd=function(t,e){this.emitEvent("dragEnd",[t,e])},s.onclick=function(t){this.isPreventingClicks&&t.preventDefault()},s._staticClick=function(t,e){this.isIgnoringMouseUp&&t.type=="mouseup"||(this.staticClick(t,e),t.type!="mouseup"&&(this.isIgnoringMouseUp=!0,setTimeout((function(){delete this.isIgnoringMouseUp}).bind(this),400)))},s.staticClick=function(t,e){this.emitEvent("staticClick",[t,e])},l.getPointerPoint=c.getPointerPoint,l})});var at=x((Wt,B)=>{(function(h,o){typeof define=="function"&&define.amd?define(["./flickity","unidragger/unidragger","fizzy-ui-utils/utils"],function(c,l,s){return o(h,c,l,s)}):typeof B=="object"&&B.exports?B.exports=o(h,_(),ot(),P()):h.Flickity=o(h,h.Flickity,h.Unidragger,h.fizzyUIUtils)})(window,function(o,c,l,s){"use strict";s.extend(c.defaults,{draggable:">1",dragThreshold:3}),c.createMethods.push("_createDrag");var i=c.prototype;s.extend(i,l.prototype),i._touchActionValue="pan-y",i._createDrag=function(){this.on("activate",this.onActivateDrag),this.on("uiChange",this._uiChangeDrag),this.on("deactivate",this.onDeactivateDrag),this.on("cellChange",this.updateDraggable)},i.onActivateDrag=function(){this.handles=[this.viewport],this.bindHandles(),this.updateDraggable()},i.onDeactivateDrag=function(){this.unbindHandles(),this.element.classList.remove("is-draggable")},i.updateDraggable=function(){this.options.draggable==">1"?this.isDraggable=this.slides.length>1:this.isDraggable=this.options.draggable,this.isDraggable?this.element.classList.add("is-draggable"):this.element.classList.remove("is-draggable")},i.bindDrag=function(){this.options.draggable=!0,this.updateDraggable()},i.unbindDrag=function(){this.options.draggable=!1,this.updateDraggable()},i._uiChangeDrag=function(){delete this.isFreeScrolling},i.pointerDown=function(e,r){if(!this.isDraggable){this._pointerDownDefault(e,r);return}var d=this.okayPointerDown(e);d&&(this._pointerDownPreventDefault(e),this.pointerDownFocus(e),document.activeElement!=this.element&&this.pointerDownBlur(),this.dragX=this.x,this.viewport.classList.add("is-pointer-down"),this.pointerDownScroll=t(),o.addEventListener("scroll",this),this._pointerDownDefault(e,r))},i._pointerDownDefault=function(e,r){this.pointerDownPointer={pageX:r.pageX,pageY:r.pageY},this._bindPostStartEvents(e),this.dispatchEvent("pointerDown",e,[r])};var a={INPUT:!0,TEXTAREA:!0,SELECT:!0};i.pointerDownFocus=function(e){var r=a[e.target.nodeName];r||this.focus()},i._pointerDownPreventDefault=function(e){var r=e.type=="touchstart",d=e.pointerType=="touch",p=a[e.target.nodeName];!r&&!d&&!p&&e.preventDefault()},i.hasDragStarted=function(e){return Math.abs(e.x)>this.options.dragThreshold},i.pointerUp=function(e,r){delete this.isTouchScrolling,this.viewport.classList.remove("is-pointer-down"),this.dispatchEvent("pointerUp",e,[r]),this._dragPointerUp(e,r)},i.pointerDone=function(){o.removeEventListener("scroll",this),delete this.pointerDownScroll},i.dragStart=function(e,r){this.isDraggable&&(this.dragStartPosition=this.x,this.startAnimation(),o.removeEventListener("scroll",this),this.dispatchEvent("dragStart",e,[r]))},i.pointerMove=function(e,r){var d=this._dragPointerMove(e,r);this.dispatchEvent("pointerMove",e,[r,d]),this._dragMove(e,r,d)},i.dragMove=function(e,r,d){if(this.isDraggable){e.preventDefault(),this.previousDragX=this.dragX;var p=this.options.rightToLeft?-1:1;this.options.wrapAround&&(d.x%=this.slideableWidth);var u=this.dragStartPosition+d.x*p;if(!this.options.wrapAround&&this.slides.length){var m=Math.max(-this.slides[0].target,this.dragStartPosition);u=u>m?(u+m)*.5:u;var v=Math.min(-this.getLastSlide().target,this.dragStartPosition);u=u<v?(u+v)*.5:u}this.dragX=u,this.dragMoveTime=new Date,this.dispatchEvent("dragMove",e,[r,d])}},i.dragEnd=function(e,r){if(this.isDraggable){this.options.freeScroll&&(this.isFreeScrolling=!0);var d=this.dragEndRestingSelect();if(this.options.freeScroll&&!this.options.wrapAround){var p=this.getRestingPosition();this.isFreeScrolling=-p>this.slides[0].target&&-p<this.getLastSlide().target}else!this.options.freeScroll&&d==this.selectedIndex&&(d+=this.dragEndBoostSelect());delete this.previousDragX,this.isDragSelect=this.options.wrapAround,this.select(d),delete this.isDragSelect,this.dispatchEvent("dragEnd",e,[r])}},i.dragEndRestingSelect=function(){var e=this.getRestingPosition(),r=Math.abs(this.getSlideDistance(-e,this.selectedIndex)),d=this._getClosestResting(e,r,1),p=this._getClosestResting(e,r,-1),u=d.distance<p.distance?d.index:p.index;return u},i._getClosestResting=function(e,r,d){for(var p=this.selectedIndex,u=1/0,m=this.options.contain&&!this.options.wrapAround?function(v,g){return v<=g}:function(v,g){return v<g};m(r,u)&&(p+=d,u=r,r=this.getSlideDistance(-e,p),r!==null);)r=Math.abs(r);return{distance:u,index:p-d}},i.getSlideDistance=function(e,r){var d=this.slides.length,p=this.options.wrapAround&&d>1,u=p?s.modulo(r,d):r,m=this.slides[u];if(!m)return null;var v=p?this.slideableWidth*Math.floor(r/d):0;return e-(m.target+v)},i.dragEndBoostSelect=function(){if(this.previousDragX===void 0||!this.dragMoveTime||new Date-this.dragMoveTime>100)return 0;var e=this.getSlideDistance(-this.dragX,this.selectedIndex),r=this.previousDragX-this.dragX;return e>0&&r>0?1:e<0&&r<0?-1:0},i.staticClick=function(e,r){var d=this.getParentCell(e.target),p=d&&d.element,u=d&&this.cells.indexOf(d);this.dispatchEvent("staticClick",e,[r,p,u])},i.onscroll=function(){var e=t(),r=this.pointerDownScroll.x-e.x,d=this.pointerDownScroll.y-e.y;(Math.abs(r)>3||Math.abs(d)>3)&&this._pointerDone()};function t(){return{x:o.pageXOffset,y:o.pageYOffset}}return c})});var ht=x((Tt,H)=>{(function(h,o){typeof define=="function"&&define.amd?define(["./flickity","unipointer/unipointer","fizzy-ui-utils/utils"],function(c,l,s){return o(h,c,l,s)}):typeof H=="object"&&H.exports?H.exports=o(h,_(),q(),P()):o(h,h.Flickity,h.Unipointer,h.fizzyUIUtils)})(window,function(o,c,l,s){"use strict";var i="http://www.w3.org/2000/svg";function a(r,d){this.direction=r,this.parent=d,this._create()}a.prototype=Object.create(l.prototype),a.prototype._create=function(){this.isEnabled=!0,this.isPrevious=this.direction==-1;var r=this.parent.options.rightToLeft?1:-1;this.isLeft=this.direction==r;var d=this.element=document.createElement("button");d.className="flickity-button flickity-prev-next-button",d.className+=this.isPrevious?" previous":" next",d.setAttribute("type","button"),this.disable(),d.setAttribute("aria-label",this.isPrevious?"Previous":"Next");var p=this.createSVG();d.appendChild(p),this.parent.on("select",this.update.bind(this)),this.on("pointerDown",this.parent.childUIPointerDown.bind(this.parent))},a.prototype.activate=function(){this.bindStartEvent(this.element),this.element.addEventListener("click",this),this.parent.element.appendChild(this.element)},a.prototype.deactivate=function(){this.parent.element.removeChild(this.element),this.unbindStartEvent(this.element),this.element.removeEventListener("click",this)},a.prototype.createSVG=function(){var r=document.createElementNS(i,"svg");r.setAttribute("class","flickity-button-icon"),r.setAttribute("viewBox","0 0 100 100");var d=document.createElementNS(i,"path"),p=t(this.parent.options.arrowShape);return d.setAttribute("d",p),d.setAttribute("class","arrow"),this.isLeft||d.setAttribute("transform","translate(100, 100) rotate(180) "),r.appendChild(d),r};function t(r){return typeof r=="string"?r:"M "+r.x0+",50 L "+r.x1+","+(r.y1+50)+" L "+r.x2+","+(r.y2+50)+" L "+r.x3+",50  L "+r.x2+","+(50-r.y2)+" L "+r.x1+","+(50-r.y1)+" Z"}a.prototype.handleEvent=s.handleEvent,a.prototype.onclick=function(){if(this.isEnabled){this.parent.uiChange();var r=this.isPrevious?"previous":"next";this.parent[r]()}},a.prototype.enable=function(){this.isEnabled||(this.element.disabled=!1,this.isEnabled=!0)},a.prototype.disable=function(){this.isEnabled&&(this.element.disabled=!0,this.isEnabled=!1)},a.prototype.update=function(){var r=this.parent.slides;if(this.parent.options.wrapAround&&r.length>1){this.enable();return}var d=r.length?r.length-1:0,p=this.isPrevious?0:d,u=this.parent.selectedIndex==p?"disable":"enable";this[u]()},a.prototype.destroy=function(){this.deactivate(),this.allOff()},s.extend(c.defaults,{prevNextButtons:!0,arrowShape:{x0:10,x1:60,y1:50,x2:70,y2:40,x3:30}}),c.createMethods.push("_createPrevNextButtons");var e=c.prototype;return e._createPrevNextButtons=function(){this.options.prevNextButtons&&(this.prevButton=new a(-1,this),this.nextButton=new a(1,this),this.on("activate",this.activatePrevNextButtons))},e.activatePrevNextButtons=function(){this.prevButton.activate(),this.nextButton.activate(),this.on("deactivate",this.deactivatePrevNextButtons)},e.deactivatePrevNextButtons=function(){this.prevButton.deactivate(),this.nextButton.deactivate(),this.off("deactivate",this.deactivatePrevNextButtons)},c.PrevNextButton=a,c})});var lt=x((Ft,N)=>{(function(h,o){typeof define=="function"&&define.amd?define(["./flickity","unipointer/unipointer","fizzy-ui-utils/utils"],function(c,l,s){return o(h,c,l,s)}):typeof N=="object"&&N.exports?N.exports=o(h,_(),q(),P()):o(h,h.Flickity,h.Unipointer,h.fizzyUIUtils)})(window,function(o,c,l,s){"use strict";function i(t){this.parent=t,this._create()}i.prototype=Object.create(l.prototype),i.prototype._create=function(){this.holder=document.createElement("ol"),this.holder.className="flickity-page-dots",this.dots=[],this.handleClick=this.onClick.bind(this),this.on("pointerDown",this.parent.childUIPointerDown.bind(this.parent))},i.prototype.activate=function(){this.setDots(),this.holder.addEventListener("click",this.handleClick),this.bindStartEvent(this.holder),this.parent.element.appendChild(this.holder)},i.prototype.deactivate=function(){this.holder.removeEventListener("click",this.handleClick),this.unbindStartEvent(this.holder),this.parent.element.removeChild(this.holder)},i.prototype.setDots=function(){var t=this.parent.slides.length-this.dots.length;t>0?this.addDots(t):t<0&&this.removeDots(-t)},i.prototype.addDots=function(t){for(var e=document.createDocumentFragment(),r=[],d=this.dots.length,p=d+t,u=d;u<p;u++){var m=document.createElement("li");m.className="dot",m.setAttribute("aria-label","Page dot "+(u+1)),e.appendChild(m),r.push(m)}this.holder.appendChild(e),this.dots=this.dots.concat(r)},i.prototype.removeDots=function(t){var e=this.dots.splice(this.dots.length-t,t);e.forEach(function(r){this.holder.removeChild(r)},this)},i.prototype.updateSelected=function(){this.selectedDot&&(this.selectedDot.className="dot",this.selectedDot.removeAttribute("aria-current")),this.dots.length&&(this.selectedDot=this.dots[this.parent.selectedIndex],this.selectedDot.className="dot is-selected",this.selectedDot.setAttribute("aria-current","step"))},i.prototype.onTap=i.prototype.onClick=function(t){var e=t.target;if(e.nodeName=="LI"){this.parent.uiChange();var r=this.dots.indexOf(e);this.parent.select(r)}},i.prototype.destroy=function(){this.deactivate(),this.allOff()},c.PageDots=i,s.extend(c.defaults,{pageDots:!0}),c.createMethods.push("_createPageDots");var a=c.prototype;return a._createPageDots=function(){this.options.pageDots&&(this.pageDots=new i(this),this.on("activate",this.activatePageDots),this.on("select",this.updateSelectedPageDots),this.on("cellChange",this.updatePageDots),this.on("resize",this.updatePageDots),this.on("deactivate",this.deactivatePageDots))},a.activatePageDots=function(){this.pageDots.activate()},a.updateSelectedPageDots=function(){this.pageDots.updateSelected()},a.updatePageDots=function(){this.pageDots.setDots()},a.deactivatePageDots=function(){this.pageDots.deactivate()},c.PageDots=i,c})});var ct=x((Ut,X)=>{(function(h,o){typeof define=="function"&&define.amd?define(["ev-emitter/ev-emitter","fizzy-ui-utils/utils","./flickity"],function(c,l,s){return o(c,l,s)}):typeof X=="object"&&X.exports?X.exports=o(A(),P(),_()):o(h.EvEmitter,h.fizzyUIUtils,h.Flickity)})(window,function(o,c,l){"use strict";function s(a){this.parent=a,this.state="stopped",this.onVisibilityChange=this.visibilityChange.bind(this),this.onVisibilityPlay=this.visibilityPlay.bind(this)}s.prototype=Object.create(o.prototype),s.prototype.play=function(){if(this.state!="playing"){var a=document.hidden;if(a){document.addEventListener("visibilitychange",this.onVisibilityPlay);return}this.state="playing",document.addEventListener("visibilitychange",this.onVisibilityChange),this.tick()}},s.prototype.tick=function(){if(this.state=="playing"){var a=this.parent.options.autoPlay;a=typeof a=="number"?a:3e3;var t=this;this.clear(),this.timeout=setTimeout(function(){t.parent.next(!0),t.tick()},a)}},s.prototype.stop=function(){this.state="stopped",this.clear(),document.removeEventListener("visibilitychange",this.onVisibilityChange)},s.prototype.clear=function(){clearTimeout(this.timeout)},s.prototype.pause=function(){this.state=="playing"&&(this.state="paused",this.clear())},s.prototype.unpause=function(){this.state=="paused"&&this.play()},s.prototype.visibilityChange=function(){var a=document.hidden;this[a?"pause":"unpause"]()},s.prototype.visibilityPlay=function(){this.play(),document.removeEventListener("visibilitychange",this.onVisibilityPlay)},c.extend(l.defaults,{pauseAutoPlayOnHover:!0}),l.createMethods.push("_createPlayer");var i=l.prototype;return i._createPlayer=function(){this.player=new s(this),this.on("activate",this.activatePlayer),this.on("uiChange",this.stopPlayer),this.on("pointerDown",this.stopPlayer),this.on("deactivate",this.deactivatePlayer)},i.activatePlayer=function(){this.options.autoPlay&&(this.player.play(),this.element.addEventListener("mouseenter",this))},i.playPlayer=function(){this.player.play()},i.stopPlayer=function(){this.player.stop()},i.pausePlayer=function(){this.player.pause()},i.unpausePlayer=function(){this.player.unpause()},i.deactivatePlayer=function(){this.player.stop(),this.element.removeEventListener("mouseenter",this)},i.onmouseenter=function(){this.options.pauseAutoPlayOnHover&&(this.player.pause(),this.element.addEventListener("mouseleave",this))},i.onmouseleave=function(){this.player.unpause(),this.element.removeEventListener("mouseleave",this)},l.Player=s,l})});var dt=x((Ot,R)=>{(function(h,o){typeof define=="function"&&define.amd?define(["./flickity","fizzy-ui-utils/utils"],function(c,l){return o(h,c,l)}):typeof R=="object"&&R.exports?R.exports=o(h,_(),P()):o(h,h.Flickity,h.fizzyUIUtils)})(window,function(o,c,l){"use strict";function s(a){var t=document.createDocumentFragment();return a.forEach(function(e){t.appendChild(e.element)}),t}var i=c.prototype;return i.insert=function(a,t){var e=this._makeCells(a);if(!(!e||!e.length)){var r=this.cells.length;t=t===void 0?r:t;var d=s(e),p=t==r;if(p)this.slider.appendChild(d);else{var u=this.cells[t].element;this.slider.insertBefore(d,u)}if(t===0)this.cells=e.concat(this.cells);else if(p)this.cells=this.cells.concat(e);else{var m=this.cells.splice(t,r-t);this.cells=this.cells.concat(e).concat(m)}this._sizeCells(e),this.cellChange(t,!0)}},i.append=function(a){this.insert(a,this.cells.length)},i.prepend=function(a){this.insert(a,0)},i.remove=function(a){var t=this.getCells(a);if(!(!t||!t.length)){var e=this.cells.length-1;t.forEach(function(r){r.remove();var d=this.cells.indexOf(r);e=Math.min(d,e),l.removeFrom(this.cells,r)},this),this.cellChange(e,!0)}},i.cellSizeChange=function(a){var t=this.getCell(a);if(t){t.getSize();var e=this.cells.indexOf(t);this.cellChange(e)}},i.cellChange=function(a,t){var e=this.selectedElement;this._positionCells(a),this._getWrapShiftCells(),this.setGallerySize();var r=this.getCell(e);r&&(this.selectedIndex=this.getCellSlideIndex(r)),this.selectedIndex=Math.min(this.slides.length-1,this.selectedIndex),this.emitEvent("cellChange",[a]),this.select(this.selectedIndex),t&&this.positionSliderAtSelected()},c})});var ut=x((qt,j)=>{(function(h,o){typeof define=="function"&&define.amd?define(["./flickity","fizzy-ui-utils/utils"],function(c,l){return o(h,c,l)}):typeof j=="object"&&j.exports?j.exports=o(h,_(),P()):o(h,h.Flickity,h.fizzyUIUtils)})(window,function(o,c,l){"use strict";c.createMethods.push("_createLazyload");var s=c.prototype;s._createLazyload=function(){this.on("select",this.lazyLoad)},s.lazyLoad=function(){var t=this.options.lazyLoad;if(t){var e=typeof t=="number"?t:0,r=this.getAdjacentCellElements(e),d=[];r.forEach(function(p){var u=i(p);d=d.concat(u)}),d.forEach(function(p){new a(p,this)},this)}};function i(t){if(t.nodeName=="IMG"){var e=t.getAttribute("data-flickity-lazyload"),r=t.getAttribute("data-flickity-lazyload-src"),d=t.getAttribute("data-flickity-lazyload-srcset");if(e||r||d)return[t]}var p="img[data-flickity-lazyload], img[data-flickity-lazyload-src], img[data-flickity-lazyload-srcset]",u=t.querySelectorAll(p);return l.makeArray(u)}function a(t,e){this.img=t,this.flickity=e,this.load()}return a.prototype.handleEvent=l.handleEvent,a.prototype.load=function(){this.img.addEventListener("load",this),this.img.addEventListener("error",this);var t=this.img.getAttribute("data-flickity-lazyload")||this.img.getAttribute("data-flickity-lazyload-src"),e=this.img.getAttribute("data-flickity-lazyload-srcset");this.img.src=t,e&&this.img.setAttribute("srcset",e),this.img.removeAttribute("data-flickity-lazyload"),this.img.removeAttribute("data-flickity-lazyload-src"),this.img.removeAttribute("data-flickity-lazyload-srcset")},a.prototype.onload=function(t){this.complete(t,"flickity-lazyloaded")},a.prototype.onerror=function(t){this.complete(t,"flickity-lazyerror")},a.prototype.complete=function(t,e){this.img.removeEventListener("load",this),this.img.removeEventListener("error",this);var r=this.flickity.getParentCell(this.img),d=r&&r.element;this.flickity.cellSizeChange(d),this.img.classList.add(e),this.flickity.dispatchEvent("lazyLoad",t,d)},c.LazyLoader=a,c})});var ft=x((wt,G)=>{(function(h,o){typeof define=="function"&&define.amd?define(["./flickity","./drag","./prev-next-button","./page-dots","./player","./add-remove-cell","./lazyload"],o):typeof G=="object"&&G.exports&&(G.exports=o(_(),at(),ht(),lt(),ct(),dt(),ut()))})(window,function(o){return o})});var gt=x((pt,V)=>{(function(h,o){typeof define=="function"&&define.amd?define(["flickity/js/index","fizzy-ui-utils/utils"],o):typeof V=="object"&&V.exports?V.exports=o(ft(),P()):o(h.Flickity,h.fizzyUIUtils)})(pt,function(o,c){var l=o.Slide,s=l.prototype.updateTarget;l.prototype.updateTarget=function(){if(s.apply(this,arguments),!!this.parent.options.fade){var p=this.target-this.x,u=this.cells[0].x;this.cells.forEach(function(m){var v=m.x-u-p;m.renderPosition(v)})}},l.prototype.setOpacity=function(p){this.cells.forEach(function(u){u.element.style.opacity=p})};var i=o.prototype;o.createMethods.push("_createFade"),i._createFade=function(){this.fadeIndex=this.selectedIndex,this.prevSelectedIndex=this.selectedIndex,this.on("select",this.onSelectFade),this.on("dragEnd",this.onDragEndFade),this.on("settle",this.onSettleFade),this.on("activate",this.onActivateFade),this.on("deactivate",this.onDeactivateFade)};var a=i.updateSlides;i.updateSlides=function(){a.apply(this,arguments),this.options.fade&&this.slides.forEach(function(p,u){var m=u==this.selectedIndex?1:0;p.setOpacity(m)},this)},i.onSelectFade=function(){this.fadeIndex=Math.min(this.prevSelectedIndex,this.slides.length-1),this.prevSelectedIndex=this.selectedIndex},i.onSettleFade=function(){if(delete this.didDragEnd,!!this.options.fade){this.selectedSlide.setOpacity(1);var p=this.slides[this.fadeIndex];p&&this.fadeIndex!=this.selectedIndex&&this.slides[this.fadeIndex].setOpacity(0)}},i.onDragEndFade=function(){this.didDragEnd=!0},i.onActivateFade=function(){this.options.fade&&this.element.classList.add("is-fade")},i.onDeactivateFade=function(){this.options.fade&&(this.element.classList.remove("is-fade"),this.slides.forEach(function(p){p.setOpacity("")}))};var t=i.positionSlider;i.positionSlider=function(){if(!this.options.fade){t.apply(this,arguments);return}this.fadeSlides(),this.dispatchScrollEvent()};var e=i.positionSliderAtSelected;i.positionSliderAtSelected=function(){this.options.fade&&this.setTranslateX(0),e.apply(this,arguments)},i.fadeSlides=function(){if(!(this.slides.length<2)){var p=this.getFadeIndexes(),u=this.slides[p.a],m=this.slides[p.b],v=this.wrapDifference(u.target,m.target),g=this.wrapDifference(u.target,-this.x);g=g/v,u.setOpacity(1-g),m.setOpacity(g);var C=p.a;this.isDragging&&(C=g>.5?p.a:p.b);var n=this.fadeHideIndex!=null&&this.fadeHideIndex!=C&&this.fadeHideIndex!=p.a&&this.fadeHideIndex!=p.b;n&&this.slides[this.fadeHideIndex].setOpacity(0),this.fadeHideIndex=C}},i.getFadeIndexes=function(){return!this.isDragging&&!this.didDragEnd?{a:this.fadeIndex,b:this.selectedIndex}:this.options.wrapAround?this.getFadeDragWrapIndexes():this.getFadeDragLimitIndexes()},i.getFadeDragWrapIndexes=function(){var p=this.slides.map(function(f,y){return this.getSlideDistance(-this.x,y)},this),u=p.map(function(f){return Math.abs(f)}),m=Math.min.apply(Math,u),v=u.indexOf(m),g=p[v],C=this.slides.length,n=g>=0?1:-1;return{a:v,b:c.modulo(v+n,C)}},i.getFadeDragLimitIndexes=function(){for(var p=0,u=0;u<this.slides.length-1;u++){var m=this.slides[u];if(-this.x<m.target)break;p=u}return{a:p,b:p+1}},i.wrapDifference=function(p,u){var m=u-p;if(!this.options.wrapAround)return m;var v=m+this.slideableWidth,g=m-this.slideableWidth;return Math.abs(v)<Math.abs(m)&&(m=v),Math.abs(g)<Math.abs(m)&&(m=g),m};var r=i._getWrapShiftCells;i._getWrapShiftCells=function(){this.options.fade||r.apply(this,arguments)};var d=i.shiftWrapCells;return i.shiftWrapCells=function(){this.options.fade||d.apply(this,arguments)},o})});var Q=xt(gt());(()=>{let h=!1,o;document.body.addEventListener("touchstart",function(c){let l=c.target.closest(".flickity-slider");if(l)if(Q.default.data(l.closest(".flickity-enabled")).isDraggable)h=!0;else{h=!1;return}else{h=!1;return}o={x:c.touches[0].pageX,y:c.touches[0].pageY}}),document.body.addEventListener("touchmove",function(c){if(!(h&&c.cancelable))return;let l={x:c.touches[0].pageX-o.x,y:c.touches[0].pageY-o.y};Math.abs(l.x)>8&&c.preventDefault()},{passive:!1})})();window.ThemeFlickity=Q.default;
/*! Bundled license information:

get-size/get-size.js:
  (*!
   * getSize v2.0.3
   * measure size of elements
   * MIT license
   *)

unipointer/unipointer.js:
  (*!
   * Unipointer v2.4.0
   * base class for doing one thing with pointer event
   * MIT license
   *)

unidragger/unidragger.js:
  (*!
   * Unidragger v2.4.0
   * Draggable base class
   * MIT license
   *)

flickity/js/index.js:
  (*!
   * Flickity v2.3.0
   * Touch, responsive, flickable carousels
   *
   * Licensed GPLv3 for open source use
   * or Flickity Commercial License for commercial use
   *
   * https://flickity.metafizzy.co
   * Copyright 2015-2021 Metafizzy
   *)
*/
