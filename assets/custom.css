@charset "UTF-8";
/* 1. Variables */
/*
$site-width: 1600px;
$container-width: 1200px;
$container-narrow-width: 800px;
$container-extra-narrow-width: 600px;

$container-gutter--desktop: 24px;
$container-gutter--mobile: 24px;

$section-spacer--desktop: 50px;
$section-spacer--mobile: 25px;
*/
/*  ------------------------------
    Grid Variables
    ------------------------------ */
/**
 * Define your breakpoints. The first value is the prefix that shall be used for
 * your classes (e.g. `.small--one-half`), the second value is the media query
 * that the breakpoint fires at.
 */
/**
 * Define which namespaced breakpoints you would like to generate for each of
 * widths, push and pull. This is handy if you only need pull on, say, desk, or
 * you only need a new width breakpoint at mobile sizes. It allows you to only
 * compile as much CSS as you need. All are turned on by default, but you can
 * add and remove breakpoints at will.
 *
 * Push and pull shall only be used if `$push` and/or `$pull` and `$responsive`
 * have been set to ‘true’.
 */
/* 2. Mixins */
/*

  1. Utilities
  2. Responsive
  3. UI Elements
    3.1. Buttons

*/
/*  ==============================
    1. Utilities
    ============================== */
/*  ==============================
    2. Responsive
    ============================== */
/*================ Responsive Show/Hide Helper ================*/
/*================ Responsive Text Alignment Helper ================*/
/*  ==============================
    3. UI Elements
    ============================== */
/*  ------------------------------
    3.1. Buttons
    ------------------------------ */
/* ------------------------------
   Headings
   ------------------------------ */
/* ------------------------------
   Labels
   ------------------------------ */
/* ------------------------------
   Inputs
   ------------------------------ */
/* ------------------------------
   RTE
   ------------------------------ */
/*  ------------------------------
    3.3. Shopify
    ------------------------------ */
/* 3. Fonts  */
/* 4. Basic Styles */
/*  -----------------------------------
    Text Size
    ----------------------------------- */
.text--small {
  font-size: var(---font-size-body--desktop);
}

.text--xsmall {
  font-size: var(---font-size-body-small--desktop);
}

.text--xxsmall {
  font-size: var(---font-size-body-xs--desktop);
}

.text--large {
  font-size: var(---font-size-body-large--desktop);
}

@minclude respond-to($small-up) {
  .text--small {
    font-size: var(---font-size-body--mobile);
  }
  .text--xsmall {
    font-size: var(---font-size-body-small--mobile);
  }
  .text--xxsmall {
    font-size: var(---font-size-body-xs--mobile);
  }
  .text--large {
    font-size: var(---font-size-body-large--mobile);
  }
}
/*  -----------------------------------
    Links
    ----------------------------------- */
.link--animated.link--animated--spaced:after {
  bottom: -0.25em;
}
.link--animated.link--animated--bold {
  font-weight: var(---font-weight-body--bold);
}
.link--animated.link--animated--bold:after {
  height: 2px;
}
.link--animated.link--animated--show-underline:after {
  transform: scaleX(1);
}
.link--animated.link--animated--show-underline:hover:after, .link--animated.link--animated--show-underline:focus:after {
  transform: scaleX(0);
}

/*  -----------------------------------
    Icon with Text
    ----------------------------------- */
/*  ==============================
    1. Root Styles
    ============================== */
* {
  box-sizing: border-box;
}

::-moz-selection {
  color: var(---color--primary);
  background: var(---background-color--primary);
}

::selection {
  color: var(---color--primary);
  background: var(---background-color--primary);
}

html,
body {
  font-family: var(---font-family-body);
  font-weight: var(---font-weight-body);
  font-style: var(---font-style-body);
  letter-spacing: var(---letter-spacing--body);
  font-size: var(---font-size-body--mobile);
  line-height: var(---line-height-body--mobile);
  scroll-behavior: smooth;
}
@media only screen and (min-width: 1001px) {
  html,
body {
    font-size: var(---font-size-body--desktop);
    line-height: var(---line-height-body--desktop);
  }
}

h1, .h1,
h2, .h2,
h3, .h3,
h4, .h4 {
  font-family: var(---font-family-heading);
  font-style: var(---font-style-heading);
  font-weight: var(---font-weight-heading);
  font-variation-settings: "wght" 400;
  line-height: var(---line-height-heading--mobile);
}
h1 span.heading--alternate, .h1 span.heading--alternate,
h2 span.heading--alternate, .h2 span.heading--alternate,
h3 span.heading--alternate, .h3 span.heading--alternate,
h4 span.heading--alternate, .h4 span.heading--alternate {
  color: var(---color-heading-1);
  text-transform: none;
  font-weight: var(---font-weight-heading);
}
h1 a, .h1 a,
h2 a, .h2 a,
h3 a, .h3 a,
h4 a, .h4 a {
  color: var(---color-link);
}
@media only screen and (min-width: 741px) {
  h1, .h1,
h2, .h2,
h3, .h3,
h4, .h4 {
    line-height: var(---line-height-heading--desktop);
  }
}

h5, .h5,
h6, .h6, product-facet .collapsible-toggle {
  font-family: var(---font-family-heading);
  font-style: var(---font-style-heading);
  font-weight: var(---font-weight-heading);
  font-variation-settings: "wght" 400;
  line-height: var(---line-height-heading--mobile);
}
h5 span.heading--alternate, .h5 span.heading--alternate,
h6 span.heading--alternate, .h6 span.heading--alternate, product-facet .collapsible-toggle span.heading--alternate {
  color: var(---color-heading-1);
  text-transform: none;
  font-weight: var(---font-weight-heading);
}
h5 a, .h5 a,
h6 a, .h6 a, product-facet .collapsible-toggle a {
  color: var(---color-link);
}
@media only screen and (min-width: 741px) {
  h5, .h5,
h6, .h6, product-facet .collapsible-toggle {
    line-height: var(---line-height-heading--desktop);
  }
}

h1, .h1 {
  font-size: var(--font-size-h1);
}

h2, .h2 {
  font-size: var(--font-size-h2);
}

h3, .h3 {
  font-size: var(--font-size-h3);
}

h4, .h4 {
  font-size: var(--font-size-h4);
}

h5, .h5 {
  font-size: var(--font-size-h5);
}

h6, .h6, product-facet .collapsible-toggle {
  font-size: var(--font-size-h6);
}

h1, .h1,
.rte h1, .rte .h1 {
  line-height: 1.2;
}

@media only screen and (max-width: 740px) {
  .h1--mobile {
    font-size: var(---font-size-h1--mobile) !important;
  }
}

@media only screen and (max-width: 740px) {
  .h2--mobile {
    font-size: var(---font-size-h2--mobile) !important;
  }
}

@media only screen and (max-width: 740px) {
  .h3--mobile {
    font-size: var(---font-size-h3--mobile) !important;
  }
}

@media only screen and (max-width: 740px) {
  .h4--mobile {
    font-size: var(---font-size-h4--mobile) !important;
  }
}

@media only screen and (max-width: 740px) {
  .h5--mobile {
    font-size: var(---font-size-h5--mobile) !important;
  }
}

@media only screen and (max-width: 740px) {
  .h6--mobile {
    font-size: var(---font-size-h6--mobile) !important;
  }
}

.heading.heading--small, .tolstoy-stories .tolstoy-stories-title {
  text-transform: uppercase;
  font-style: var(---font-style-subheading);
  font-variation-settings: "wght" 400;
  font-family: var(---font-family-subheading);
  color: var(--subheading-color, inherit);
  font-size: var(---font-size-subheading--mobile);
}
@media only screen and (min-width: 741px) {
  .heading.heading--small, .tolstoy-stories .tolstoy-stories-title {
    line-height: var(---line-height-subheading--desktop);
  }
}
.heading.heading--small + p, .tolstoy-stories .tolstoy-stories-title + p,
.heading.heading--small + .h1,
.tolstoy-stories .tolstoy-stories-title + .h1,
.heading.heading--small + h1,
.tolstoy-stories .tolstoy-stories-title + h1,
.heading.heading--small + .h2,
.tolstoy-stories .tolstoy-stories-title + .h2,
.heading.heading--small + h2,
.tolstoy-stories .tolstoy-stories-title + h2,
.heading.heading--small + .h3,
.tolstoy-stories .tolstoy-stories-title + .h3,
.heading.heading--small + h3,
.tolstoy-stories .tolstoy-stories-title + h3,
.heading.heading--small + .h4,
.tolstoy-stories .tolstoy-stories-title + .h4,
.heading.heading--small + h4,
.tolstoy-stories .tolstoy-stories-title + h4 {
  margin-top: 12px;
}
.heading.heading--small + hr, .tolstoy-stories .tolstoy-stories-title + hr {
  margin-top: 0;
}
@media only screen and (min-width: 741px) {
  .heading.heading--small, .tolstoy-stories .tolstoy-stories-title {
    font-size: var(---font-size-subheading--desktop);
  }
}

.subheading.heading--xsmall,
.heading.heading--xsmall,
.tolstoy-stories .heading--xsmall.tolstoy-stories-title {
  text-transform: uppercase;
  font-style: var(---font-style-subheading);
  font-variation-settings: "wght" 400;
  font-family: var(---font-family-subheading);
  color: var(--subheading-color, inherit);
  font-size: var(---font-size-subheading-small);
}
@media only screen and (min-width: 741px) {
  .subheading.heading--xsmall,
.heading.heading--xsmall,
.tolstoy-stories .heading--xsmall.tolstoy-stories-title {
    line-height: var(---line-height-subheading--desktop);
  }
}

.heading.heading--xsmall, .tolstoy-stories .heading--xsmall.tolstoy-stories-title,
.subheading {
  color: RGB(var(--subheading-color, var(--heading-color))) !important;
}

@media only screen and (max-width: 740px) {
  .h3--mobile {
    font-size: var(---font-size-h3--mobile);
  }
}

p:not(.heading),
.p:not(.heading) {
  margin-top: 0;
  margin-bottom: 1em;
  font-size: var(---font-size-body--mobile);
  font-family: var(---font-family-body);
  font-weight: var(---font-weight-body);
  font-variation-settings: "wght" 400;
}
@media only screen and (min-width: 741px) {
  p:not(.heading),
.p:not(.heading) {
    font-size: var(---font-size-body--desktop);
  }
}
p:not(.heading).text--xxsmall, p:not(.heading).tiny,
.p:not(.heading).text--xxsmall,
.p:not(.heading).tiny {
  font-size: var(---font-size-body-xs--mobile);
}
@media only screen and (min-width: 741px) {
  p:not(.heading).text--xxsmall, p:not(.heading).tiny,
.p:not(.heading).text--xxsmall,
.p:not(.heading).tiny {
    font-size: var(---font-size-body-xs--desktop);
  }
}
p:not(.heading).text--xsmall, p:not(.heading).minor,
.p:not(.heading).text--xsmall,
.p:not(.heading).minor {
  font-size: var(---font-size-body-small--mobile);
  font-variation-settings: "wght" 300;
}
@media only screen and (min-width: 741px) {
  p:not(.heading).text--xsmall, p:not(.heading).minor,
.p:not(.heading).text--xsmall,
.p:not(.heading).minor {
    font-size: var(---font-size-body-small--desktop);
  }
}
p:not(.heading).text--large, p:not(.heading).major,
.p:not(.heading).text--large,
.p:not(.heading).major {
  font-family: var(--heading-font-family--alternate);
  font-size: var(---font-size-body-large--mobile);
}
@media only screen and (min-width: 741px) {
  p:not(.heading).text--large, p:not(.heading).major,
.p:not(.heading).text--large,
.p:not(.heading).major {
    font-size: var(---font-size-body-large--desktop);
  }
}

@media only screen and (max-width: 740px) {
  .p--mobile {
    font-size: var(---font-size-body--mobile);
    font-family: var(---font-family-body);
    font-weight: var(---font-weight-body);
  }
}

.text--xxsmall,
.tiny {
  font-size: var(---font-size-body-xs--mobile);
}
@media only screen and (min-width: 741px) {
  .text--xxsmall,
.tiny {
    font-size: var(---font-size-body-xs--desktop);
  }
}
.text--xxsmall p,
.tiny p {
  font-size: var(---font-size-body-xs--mobile);
}
@media only screen and (min-width: 741px) {
  .text--xxsmall p,
.tiny p {
    font-size: var(---font-size-body-xs--desktop);
  }
}

@media only screen and (max-width: 740px) {
  .text--xxsmall--mobile,
.p-tiny--mobile {
    font-family: var(---font-family-body);
    font-weight: var(---font-weight-body);
    font-size: var(---font-size-body-xs--mobile);
  }
}

.text--xsmall,
.minor {
  font-variation-settings: "wght" 300;
  font-size: var(---font-size-body-small--mobile);
}
@media only screen and (min-width: 741px) {
  .text--xsmall,
.minor {
    font-size: var(---font-size-body-small--desktop);
  }
}
.text--xsmall p:not(.heading),
.minor p:not(.heading) {
  font-variation-settings: "wght" 300;
  font-size: var(---font-size-body-small--mobile);
}
@media only screen and (min-width: 741px) {
  .text--xsmall p:not(.heading),
.minor p:not(.heading) {
    font-size: var(---font-size-body-small--desktop);
  }
}

@media only screen and (max-width: 740px) {
  .text--small--mobile,
.p-minor--mobile {
    font-size: var(---font-size-body-small--mobile);
    font-family: var(---font-family-body);
    font-weight: var(---font-weight-body);
  }
}

.text--large,
.major {
  font-family: var(--heading-font-family--alternate);
  font-size: var(---font-size-body-large--mobile);
}
@media only screen and (min-width: 741px) {
  .text--large,
.major {
    font-size: var(---font-size-body-large--desktop);
  }
}
.text--large p:not(.heading),
.major p:not(.heading) {
  font-family: var(--heading-font-family--alternate);
  font-size: var(---font-size-body-large--mobile);
}
@media only screen and (min-width: 741px) {
  .text--large p:not(.heading),
.major p:not(.heading) {
    font-size: var(---font-size-body-large--desktop);
  }
}

.text--large--mobile,
.p-major--mobile {
  font-family: var(--heading-font-family--alternate);
}
@media only screen and (max-width: 740px) {
  .text--large--mobile,
.p-major--mobile {
    font-size: var(---font-size-body-large--mobile);
    font-family: var(---font-family-body);
    font-weight: var(---font-weight-body);
  }
}

.text--subdued {
  font-weight: 300;
  color: var(---color--kyte-dark-grey);
}

strong, .strong {
  --color: var(---color-text-dark);
  font-weight: var(---font-weight-body--bold);
  font-variation-settings: "wght" var(---font-weight-body--bold);
}

.link--animated {
  display: inline-block;
}

.link__icon {
  display: inline;
  line-height: 0;
  vertical-align: middle;
}
.link__icon svg {
  display: inline-block;
}
.link__icon .icon--custom-external {
  opacity: 0.25;
}

.sub-brand {
  ---color-line: var(--sub-brand--secondary-background-color);
  ---color-line--rgb: var(--sub-brand--secondary-background-color--rgb);
  --subheading-color: var(--sub-brand--subheading-color--rgb);
  --heading-color: var(--sub-brand--heading-color--rgb);
  ---color--primary: var(--sub-brand--button-color);
  ---color--primary--rgb: var(--sub-brand--button-color--rgb);
  /* ========== Basic Styles ========== */
  /* ========== Sections ========== */
  /* ----- Header ----- */
  /* ========== Pages ========== */
  /* ----- Product ----- */
  /* ========== Components ========== */
  /* ----- Shipping Bar ----- */
  /* ----- Quantity Selector ----- */
  /* ----- Buttons ----- */
  /* ----- Tabs ----- */
  /* ----- Swatches ----- */
  /* ----- Product Card ----- */
}
.sub-brand .text--subdued {
  color: RGBA(var(--sub-brand--text-color--rgb), 0.5);
  -webkit-text-decoration-color: RGBA(var(--sub-brand--text-color--rgb), 0.1);
          text-decoration-color: RGBA(var(--sub-brand--text-color--rgb), 0.1);
}
.sub-brand #main .label {
  color: RGB(var(--sub-brand--subheading-color--rgb));
  background-color: RGB(var(--sub-brand--heading-color--rgb));
}
.sub-brand #main .label.label--subdued {
  background-color: RGB(var(--sub-brand--subheading-color--rgb));
  color: RGB(var(--sub-brand--heading-color--rgb));
}
.sub-brand #main .label.label--light {
  background-color: RGB(var(--sub-brand--secondary-background-color--rgb));
  color: RGB(var(--sub-brand--heading-color--rgb));
}
.sub-brand #main .label.label--custom {
  background-color: RGB(var(--sub-brand-color-4--rgb));
  color: RGB(var(--sub-brand--heading-color--rgb));
}
.sub-brand #main .price {
  color: RGB(var(--sub-brand--subheading-color--rgb));
}
.sub-brand store-header.header {
  --header-text-color: var(--sub-brand--heading-color--rgb);
}
.sub-brand .header__icon-wrapper {
  color: RGB(var(--header-text-color));
}
.sub-brand .header__icon-wrapper:not([aria-controls=search-drawer]) svg * {
  fill: RGB(var(--header-text-color));
}
.sub-brand .header__icon-wrapper svg.icon--custom-search * {
  fill: RGB(var(--header-text-color));
}
.sub-brand .header__cart-count {
  background-color: RGB(var(--header-text-color));
}
.sub-brand .shopify-section--top-menu-bar a {
  color: RGB(var(--text-color));
}
.sub-brand .shipping-bar .shipping-bar__progress {
  background-color: RGB(var(--sub-brand--secondary-background-color--rgb));
}
.sub-brand .shipping-bar .shipping-bar__progress:after {
  background-color: RGB(var(--primary-button-background));
}
.sub-brand .shopify-section--mini-cart .line-item .quantity-selector .quantity-selector__button {
  ---color-text: var(--sub-brand--button-color--rgb);
  background-color: RGB(var(--sub-brand--button-text-color--rgb));
  color: RGB(var(--sub-brand--button-color--rgb));
  box-shadow: 0 0 0 1px RGBA(var(---color-text), 0.25);
  transition: 0.25s background, 0.25s color;
}
.sub-brand .shopify-section--mini-cart .line-item .quantity-selector .quantity-selector__button:hover, .sub-brand .shopify-section--mini-cart .line-item .quantity-selector .quantity-selector__button:active {
  background-color: RGB(var(--sub-brand-color-6--rgb));
  color: RGB(var(--sub-brand--button-text-color--rgb));
  box-shadow: 0 0 0 1px RGBA(var(--sub-brand--button-text-color--rgb), 0.25);
}
.sub-brand .button.button--primary, .sub-brand .button--primary.swym-add-to-wishlist,
.sub-brand .button--primary.giftreggie-pdp-registry-cta, .sub-brand .button--primary.shopify-payment-button__button--unbranded, .sub-brand .drawer .popover .button--primary.wk-button, .drawer .popover .sub-brand .button--primary.wk-button {
  ---color-text: var(--sub-brand--button-color--rgb);
  background-color: RGB(var(--sub-brand--button-color--rgb));
  color: RGB(var(--sub-brand--button-text-color--rgb));
  box-shadow: 0 0 0 1px RGBA(var(--sub-brand--button-text-color--rgb), 0.25);
}
.sub-brand .button.button--primary:hover, .sub-brand .button--primary.swym-add-to-wishlist:hover,
.sub-brand .button--primary.giftreggie-pdp-registry-cta:hover, .sub-brand .button--primary.shopify-payment-button__button--unbranded:hover, .sub-brand .drawer .popover .button--primary.wk-button:hover, .drawer .popover .sub-brand .button--primary.wk-button:hover, .sub-brand .button.button--primary:active, .sub-brand .button--primary.swym-add-to-wishlist:active,
.sub-brand .button--primary.giftreggie-pdp-registry-cta:active, .sub-brand .button--primary.shopify-payment-button__button--unbranded:active, .sub-brand .drawer .popover .button--primary.wk-button:active, .drawer .popover .sub-brand .button--primary.wk-button:active {
  background-color: RGB(var(--sub-brand--button-text-color--rgb));
  color: RGB(var(--sub-brand--button-color--rgb));
  box-shadow: 0 0 0 1px RGBA(var(--sub-brand--button-color--rgb), 0.25);
}
.sub-brand .button.button--secondary, .sub-brand .button--secondary.swym-add-to-wishlist,
.sub-brand .button--secondary.giftreggie-pdp-registry-cta, .sub-brand .button--secondary.shopify-payment-button__button--unbranded, .sub-brand .drawer .popover .button--secondary.wk-button, .drawer .popover .sub-brand .button--secondary.wk-button {
  ---color-text: var(--sub-brand--button-color--rgb);
  background-color: RGB(var(--sub-brand--button-text-color--rgb));
  color: RGB(var(--sub-brand--button-color--rgb));
  box-shadow: 0 0 0 1px RGBA(var(---color-text), 0.25);
}
.sub-brand .button.button--secondary:hover, .sub-brand .button--secondary.swym-add-to-wishlist:hover,
.sub-brand .button--secondary.giftreggie-pdp-registry-cta:hover, .sub-brand .button--secondary.shopify-payment-button__button--unbranded:hover, .sub-brand .drawer .popover .button--secondary.wk-button:hover, .drawer .popover .sub-brand .button--secondary.wk-button:hover, .sub-brand .button.button--secondary:active, .sub-brand .button--secondary.swym-add-to-wishlist:active,
.sub-brand .button--secondary.giftreggie-pdp-registry-cta:active, .sub-brand .button--secondary.shopify-payment-button__button--unbranded:active, .sub-brand .drawer .popover .button--secondary.wk-button:active, .drawer .popover .sub-brand .button--secondary.wk-button:active {
  background-color: RGB(var(--sub-brand--button-color--rgb));
  color: RGB(var(--sub-brand--button-text-color--rgb));
  box-shadow: 0 0 0 1px RGBA(var(--sub-brand--button-text-color--rgb), 0.25);
}
.sub-brand .button.button--tertiary, .sub-brand .button--tertiary.swym-add-to-wishlist,
.sub-brand .button--tertiary.giftreggie-pdp-registry-cta, .sub-brand .button--tertiary.shopify-payment-button__button--unbranded, .sub-brand .drawer .popover .button--tertiary.wk-button, .drawer .popover .sub-brand .button--tertiary.wk-button {
  ---color-text: var(--sub-brand--button-color--rgb);
  background-color: RGB(var(--sub-brand--button-text-color--rgb));
  color: RGB(var(--sub-brand--button-color--rgb));
  box-shadow: 0 0 0 1px RGBA(var(---color-text), 0.25);
}
.sub-brand .button.button--tertiary:hover, .sub-brand .button--tertiary.swym-add-to-wishlist:hover,
.sub-brand .button--tertiary.giftreggie-pdp-registry-cta:hover, .sub-brand .button--tertiary.shopify-payment-button__button--unbranded:hover, .sub-brand .drawer .popover .button--tertiary.wk-button:hover, .drawer .popover .sub-brand .button--tertiary.wk-button:hover, .sub-brand .button.button--tertiary:active, .sub-brand .button--tertiary.swym-add-to-wishlist:active,
.sub-brand .button--tertiary.giftreggie-pdp-registry-cta:active, .sub-brand .button--tertiary.shopify-payment-button__button--unbranded:active, .sub-brand .drawer .popover .button--tertiary.wk-button:active, .drawer .popover .sub-brand .button--tertiary.wk-button:active {
  background-color: RGB(var(--sub-brand--subheading-color--rgb));
  color: RGB(var(--sub-brand--button-text-color--rgb));
  box-shadow: 0 0 0 1px RGBA(var(--sub-brand--button-text-color--rgb), 0.25);
}
.sub-brand .tabs-nav .tabs-nav__position {
  color: RGB(var(--sub-brand--button-color--rgb));
}
.sub-brand .color-swatch .color-swatch__item:after {
  --text-color: var(--sub-brand--subheading-color--rgb);
}
.sub-brand .block-swatch {
  --text-color: var(--sub-brand--heading-color--rgb);
}
.sub-brand .block-swatch:not(.is-disabled) .block-swatch__item {
  ---color-primary: var(--sub-brand--button-color);
  --text-color: var(--sub-brand--heading-color--rgb);
  outline: 1px solid RGBA(var(--sub-brand--subheading-color--rgb), 0.5);
}
.sub-brand .block-swatch:not(.is-disabled) .block-swatch__item:focus + .block-swatch__item, .sub-brand .block-swatch:not(.is-disabled) .block-swatch__item:hover + .block-swatch__item {
  --text-color: var(--sub-brand--subheading-color--rgb);
}
.sub-brand .block-swatch input:checked + .block-swatch__item {
  --text-color: var(--sub-brand--subheading-color--rgb);
}
.sub-brand .block-swatch.is-disabled .block-swatch__item {
  cursor: default;
}
.sub-brand .product-item .product-item-meta__title {
  color: RGB(var(--sub-brand--heading-color--rgb));
}
.sub-brand .product-item .product-item-meta__price-list-container .price {
  color: RGB(var(--sub-brand--subheading-color--rgb));
}
.sub-brand .product-item.product-item--slider .product-item__cta-wrapper .block-swatch:not(.is-disabled) .block-swatch__item {
  color: RGB(var(--sub-brand--button-color--rgb));
  border-color: RGB(var(--sub-brand--button-color--rgb));
}
.sub-brand .product-item.product-item--slider .product-item__cta-wrapper .block-swatch:not(.is-disabled) .block-swatch__item:hover, .sub-brand .product-item.product-item--slider .product-item__cta-wrapper .block-swatch:not(.is-disabled) .block-swatch__item:active {
  color: RGB(var(---color-text--reversed--rgb));
  background-color: RGB(var(--sub-brand--button-color--rgb));
}
.sub-brand .product-item.product-item--slider .product-item__cta-wrapper > .button, .sub-brand .product-item.product-item--slider .product-item__cta-wrapper > .swym-add-to-wishlist,
.sub-brand .product-item.product-item--slider .product-item__cta-wrapper > .giftreggie-pdp-registry-cta, .sub-brand .product-item.product-item--slider .product-item__cta-wrapper > .shopify-payment-button__button--unbranded, .sub-brand .product-item.product-item--slider .drawer .popover .product-item__cta-wrapper > .wk-button, .drawer .popover .sub-brand .product-item.product-item--slider .product-item__cta-wrapper > .wk-button {
  color: RGB(var(--sub-brand--button-color--rgb));
}
.sub-brand .product-item.product-item--slider .product-item__cta-wrapper > .button:hover, .sub-brand .product-item.product-item--slider .product-item__cta-wrapper > .swym-add-to-wishlist:hover,
.sub-brand .product-item.product-item--slider .product-item__cta-wrapper > .giftreggie-pdp-registry-cta:hover, .sub-brand .product-item.product-item--slider .product-item__cta-wrapper > .shopify-payment-button__button--unbranded:hover, .sub-brand .product-item.product-item--slider .drawer .popover .product-item__cta-wrapper > .wk-button:hover, .drawer .popover .sub-brand .product-item.product-item--slider .product-item__cta-wrapper > .wk-button:hover, .sub-brand .product-item.product-item--slider .product-item__cta-wrapper > .button:active, .sub-brand .product-item.product-item--slider .product-item__cta-wrapper > .swym-add-to-wishlist:active,
.sub-brand .product-item.product-item--slider .product-item__cta-wrapper > .giftreggie-pdp-registry-cta:active, .sub-brand .product-item.product-item--slider .product-item__cta-wrapper > .shopify-payment-button__button--unbranded:active, .sub-brand .product-item.product-item--slider .drawer .popover .product-item__cta-wrapper > .wk-button:active, .drawer .popover .sub-brand .product-item.product-item--slider .product-item__cta-wrapper > .wk-button:active {
  background: transparent;
}
/*  ------------------------------
    1. Inputs
    ------------------------------ */
textarea[disabled],
select[disabled],
input[type=text][disabled],
input[type=number][disabled],
input[type=email][disabled],
input[type=tel][disabled],
input[type=password][disabled],
input[type=date][disabled] {
  cursor: not-allowed;
}

.input-row {
  position: relative;
  width: 100%;
}

.input-row--compact {
  --form-input-field-height: 40px;
  display: flex;
}
.input-row--compact > .input:not(:first-child) * {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.input-row--compact > .input:not(:last-child) * {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-row--compact > .input + .input {
  margin: 0;
}
.input-row--compact .input button {
  margin: 0;
}
.input-row--compact .button, .input-row--compact .swym-add-to-wishlist,
.input-row--compact .giftreggie-pdp-registry-cta, .input-row--compact .shopify-payment-button__button--unbranded, .input-row--compact .drawer .popover .wk-button, .drawer .popover .input-row--compact .wk-button {
  height: calc(var(--form-input-field-height) - 2px);
  margin: 1px 0 !important;
}
.input-row--compact input:focus-within ~ .input__label {
  transform: scale(0.733) translateY(calc(-1 * var(--form-input-field-height) / 2 - 5px)) translate(3.665px);
}
.input-row--compact .input {
  width: auto;
}

.input .button, .input .swym-add-to-wishlist,
.input .giftreggie-pdp-registry-cta, .input .shopify-payment-button__button--unbranded, .input .drawer .popover .wk-button, .drawer .popover .input .wk-button {
  margin: 0;
}
.input .input__label {
  letter-spacing: var(---label-letter-spacing);
  font-weight: var(---label-font-weight);
  color: var(---label-color);
  font-size: var(---font-size-label--mobile);
}
@media only screen and (min-width: 741px) {
  .input .input__label {
    font-size: var(---font-size-label--desktop);
  }
}
.input:not(.input--floating) {
  width: 100%;
}
.input.input--floating {
  display: flex;
  align-items: center;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  height: 100%;
}

.input__field {
  --text-color: var(---color-text);
  --background: var(---background-color--content-2);
  border: 1px solid var(---color-line);
  /*

  // Removing this as Focal already has good base styles.

  background: var(---input-background);
  border-style: solid;
  color: var(---input-color);

  outline: none;

  */
}
.input__field::-moz-placeholder {
  color: var(---color-text--light);
  opacity: 1;
}
.input__field::placeholder {
  color: var(---color-text--light);
  opacity: 1;
}
.input__field:not([disabled]):focus, .input__field:not([disabled]):hover {
  border-color: var(---color--primary);
}
.input__field.input--rounded {
  border-radius: var(---input-border-radius);
}

.input__field--xtiny {
  height: var(--form-input-field-height);
  padding: 0.8em 1.2em;
  min-width: unset;
  text-transform: none;
  letter-spacing: var(---letter-spacing-body--mobile);
  font-size: var(---font-size-button-small--mobile);
}

select {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background: transparent;
  background-image: var(---icon--chevron-down);
  background-position: right 1em top 50%;
  background-repeat: no-repeat;
  background-size: 14px;
  --text-color: var(---color-text);
  --background: var(---background-color--content-2);
  border: 1px solid var(---color-line);
  /*

  // Removing this as Focal already has good base styles.

  background: var(---input-background);
  border-style: solid;
  color: var(---input-color);

  outline: none;

  */
}
select::-moz-placeholder {
  color: var(---color-text--light);
  opacity: 1;
}
select::placeholder {
  color: var(---color-text--light);
  opacity: 1;
}
select:not([disabled]):focus, select:not([disabled]):hover {
  border-color: var(---color--primary);
}
select.input--rounded {
  border-radius: var(---input-border-radius);
}

option,
optgroup {
  font-size: 1rem;
}

.hint {
  margin-top: 0.5em;
  font-size: var(---font-size-body-small--mobile);
  font-style: italic;
}
@media only screen and (min-width: 1001px) {
  .hint {
    font-size: var(---font-size-body-small--desktop);
  }
}

.select-wrapper .select {
  color: var(---color-text--dark);
  border: 1px solid var(---color-line);
  border-radius: 0;
}

input[type=search] {
  background-image: var(---icon-search);
  background-repeat: no-repeat;
  background-position: right 15px center;
}

/*  ------------------------------
    2. Labels
    ------------------------------ */
/*  ------------------------------
    3. Fieldsets
    ------------------------------ */
fieldset {
  display: block;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border: none;
  margin: 0;
  padding: 0;
}

.italic {
  font-style: italic;
}

.icon.icon--fill * {
  stroke: none;
  fill: currentColor;
}

img, .img {
  width: 100%;
  vertical-align: top;
}

.rte table,
.table {
  width: 100%;
  border-collapse: collapse;
  text-align: left;
}
.rte table th,
.table th {
  padding: calc(var(--table-spacing) / 2);
}
.rte table td,
.table td {
  vertical-align: top;
  padding: calc(var(--table-spacing) / 2);
}
.rte table th:not([class*=text--]):first-child,
.rte table td:not([class*=text--]):first-child,
.table th:not([class*=text--]):first-child,
.table td:not([class*=text--]):first-child {
  text-align: left;
}
.rte table th:not([class*=text--]):last-child,
.rte table td:not([class*=text--]):last-child,
.table th:not([class*=text--]):last-child,
.table td:not([class*=text--]):last-child {
  text-align: right;
}
.rte table td:first-child,
.table td:first-child {
  font-weight: bold;
}
.rte table tr:hover,
.table tr:hover {
  background: var(---background-color--default);
}
.rte table tr:hover td,
.table tr:hover td {
  background: transparent;
}
/* 5. Layout */
html.supports-no-cookies .supports-no-cookies {
  display: none;
}
html.supports-cookies .supports-cookies {
  display: none;
}

/* 6. Sections */
store-header.header {
  border-bottom: 1px solid var(---color-line--light);
}
store-header.header .header__logo {
  display: flex;
}
store-header.header .header__logo-link svg {
  height: auto !important;
}
@media only screen and (min-width: 1201px) {
  store-header.header .header__logo-link {
    min-height: 70px;
    display: flex;
    align-items: center;
  }
}
store-header.header .header__wrapper {
  padding: 6px 0;
}
store-header.header .header__linklist-link {
  font-family: var(---font-family-body--alternate);
  font-style: var(---font-style-body--alternate);
  letter-spacing: var(---letter-spacing--body);
  font-variation-settings: "wght" 500;
  font-size: var(---font-size-body--mobile);
  line-height: var(---line-height-body--mobile);
}
@media only screen and (min-width: 1001px) {
  store-header.header .header__linklist-link {
    font-size: var(---font-size-body--desktop);
    line-height: var(---line-height-body--desktop);
  }
}
store-header.header .header__cross-border .popover-button {
  font-size: var(---font-size-body-small--desktop);
  margin-bottom: 0px;
}
store-header.header .header__logo-container {
  margin: 0;
}
store-header.header .shop-by-color__content {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 10px 20px;
  padding-top: 0.5em;
}
@media (min-width: 1400px) {
  store-header.header .shop-by-color__content {
    max-height: 250px;
  }
}
@media (max-width: 1300px) {
  store-header.header .shop-by-color__content {
    grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
  }
}

/* ============================== TABBED HEADER ============================== */
store-header.tabbed-header {
  --transition-duration: 0.25s;
  --text-color--hover: var(--tabbed-header-color-text--hover);
  /* -------------------- LEFT PART -------------------- */
  /* ----- Expanding Logo ----- */
  /* -------------------- RIGHT PART -------------------- */
  /* ----- Search Bar ----- */
  /* ----- Icons ----- */
}
store-header.tabbed-header .header__secondary-links .icon {
  color: RGB(var(--icon-color));
}
store-header.tabbed-header .header__secondary-links .icon * {
  fill: currentColor;
}
store-header.tabbed-header .header__secondary-links .icon.icon--header-search * {
  fill: transparent;
  stroke: currentColor;
  stroke-width: 2px;
}
store-header.tabbed-header .header__wrapper {
  min-height: 70px;
  gap: 20px;
}
store-header.tabbed-header .header__linklist {
  flex-wrap: wrap;
}
store-header.tabbed-header .header__linklist-item {
  margin-right: 24px;
}
store-header.tabbed-header .header__inline-navigation {
  gap: 0;
}
store-header.tabbed-header .header__secondary-links {
  gap: var(--tabbed-header-gap);
  flex: 0 1 0;
}
store-header.tabbed-header .header__expanding-logo {
  transition: width 0.7s;
  overflow: hidden;
  height: 100%;
  width: 0;
  position: relative;
}
store-header.tabbed-header .header__expanding-logo:after {
  content: "";
  position: absolute;
  width: 20px;
  height: 100%;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgb(255, 255, 255) 100%);
  pointer-events: none;
}
store-header.tabbed-header .header__expanding-logo img,
store-header.tabbed-header .header__expanding-logo svg {
  width: 100%;
}
store-header.tabbed-header .header__expanding-logo .header__logo-link {
  padding-left: 0;
  padding-right: 10px;
  opacity: 0;
  transition: opacity 0.5s;
}
store-header.tabbed-header .header__logo-link-static {
  width: 100%;
}
store-header.tabbed-header .header__logo-link {
  display: flex;
  align-items: center;
  padding-left: 20px;
  max-width: 160px;
}
store-header.tabbed-header .header__logo-link svg * {
  fill: RGB(var(--logo-color));
}
store-header.tabbed-header .header__logo-link-inner {
  height: 100%;
  display: block;
}
store-header.tabbed-header .header__logo-image {
  width: 100%;
}
store-header.tabbed-header.header--scrolled .header__expanding-logo {
  width: 180px;
}
store-header.tabbed-header.header--scrolled .header__expanding-logo .header__logo-link {
  opacity: 1;
}
store-header.tabbed-header .predictive-search-input-wrapper {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  border-radius: var(--tabbed-header-input-radius);
  background-color: RGB(var(--background-2));
  color: var(--text-color);
  cursor: pointer;
  transition: background-color var(--transition-duration), color var(--transition-duration);
  /* ----- Hover ----- */
}
store-header.tabbed-header .predictive-search-input-wrapper svg {
  color: RGB(var(--background-2));
}
store-header.tabbed-header .predictive-search-input-wrapper svg * {
  transition: stroke var(--transition-duration), fill var(--transition-duration);
}
store-header.tabbed-header .predictive-search-input-wrapper .predictive-search__input {
  min-width: 250px;
}
store-header.tabbed-header .predictive-search-input-wrapper .predictive-search__input::-moz-placeholder {
  color: RGB(var(--text-color));
}
store-header.tabbed-header .predictive-search-input-wrapper .predictive-search__input::placeholder {
  color: RGB(var(--text-color));
}
store-header.tabbed-header .predictive-search-input-wrapper:hover {
  color: RGB(var(--background-2));
  background-color: RGB(var(--primary-button-background));
}
store-header.tabbed-header .predictive-search-input-wrapper:hover svg * {
  stroke: RGB(var(--background-2));
}
store-header.tabbed-header .predictive-search-input-wrapper:hover .predictive-search__input::-moz-placeholder {
  color: RGB(var(--background-2));
}
store-header.tabbed-header .predictive-search-input-wrapper:hover .predictive-search__input::placeholder {
  color: RGB(var(--background-2));
}
store-header.tabbed-header .header__icon-list {
  gap: var(--tabbed-header-gap);
}
store-header.tabbed-header .mega-menu {
  --padding: 32px;
  background-color: RGB(var(--block-background, var(--background)));
  padding-block: var(--padding);
}
@media only screen and (min-width: 1201px) {
  store-header.tabbed-header .mega-menu {
    --padding: 48px;
  }
}
store-header.tabbed-header .mega-menu__inner {
  padding: 0;
}
store-header.tabbed-header .mega-menu-header {
  padding-bottom: calc(var(--padding) / 2);
  text-align: center;
}
store-header.tabbed-header .mega-menu-footer {
  padding-top: calc(var(--padding) / 2);
  text-align: center;
}

/* ============================== FLOATING ICON ============================== */
.header-floating-badge {
  --top: calc(var(--header-height) + 20px + var(--product-sticky-form-height) + var(--sticky-product-form-spacer));
  position: absolute;
  z-index: -1;
  top: var(--top);
  right: calc(var(--container-gutter));
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70px;
  height: 70px;
  border-radius: 100%;
  padding: var(--badge-border-width);
  background: var(--badge-color-border);
  opacity: 0;
  transform: scale(0);
}
@media only screen and (min-width: 1201px) {
  .header-floating-badge {
    right: calc(var(--container-gutter) * 2);
  }
}
.header-floating-badge:hover .header-floating-badge__inner {
  transform: rotate(20deg);
}

.header-floating-badge--loaded {
  -webkit-animation: floating-badge-in 1s cubic-bezier(0.175, 0.885, 0.32, 1.275) both;
          animation: floating-badge-in 1s cubic-bezier(0.175, 0.885, 0.32, 1.275) both;
}

.header-floating-badge__inner {
  border-radius: 100%;
  background-color: RGB(var(--badge-color-background));
  transition: 0.25s ease transform;
}

.header-floating-badge__image {
  width: 100%;
  overflow: hidden;
}
.header-floating-badge__image img {
  display: block;
  width: 100%;
  height: 100%;
}

.header-floating-badge__label {
  position: absolute;
  bottom: 0;
  padding: 0.3em 1em;
  transform: translateY(calc(100% - 15px));
  font-size: var(--font-size-xs);
  font-weight: bold;
  background-color: RGB(var(--badge-color-label-background));
  color: RGB(var(--badge-color-label-text));
  border-radius: 100px;
}

.header-floating-badge__label-inner {
  white-space: nowrap;
}

.shopify-section--announcement-bar {
  /* ----- Rewards Points ----- */
}
.shopify-section--announcement-bar .announcement-bar__message {
  padding: 10px 0;
}
.shopify-section--announcement-bar .announcement-bar__content-image {
  padding: 30px;
}
.shopify-section--announcement-bar .announcement-bar__content-image img {
  border-radius: var(--block-border-radius);
  overflow: hidden;
}
.shopify-section--announcement-bar .announcement-bar__message-inner {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 0.5em;
}
.shopify-section--announcement-bar .announcement-bar__message-inner button {
  margin: 0 !important;
}
.shopify-section--announcement-bar .announcement-bar__rewards-points svg,
.shopify-section--announcement-bar .announcement-bar__rewards-points img {
  display: inline-block;
  mix-blend-mode: screen;
  max-height: 14px;
  max-width: 14px;
}
@media only screen and (min-width: 1001px) {
  .shopify-section--announcement-bar .announcement-bar__rewards-points svg,
.shopify-section--announcement-bar .announcement-bar__rewards-points img {
    width: 18px;
    height: 18px;
  }
}
.shopify-section--announcement-bar .announcement-bar__divider:before {
  display: inline-block;
  content: "|";
  margin: 0 0.25em;
  opacity: 0.25;
}

.mega-menu .heading.heading--small, .mega-menu .tolstoy-stories .tolstoy-stories-title, .tolstoy-stories .mega-menu .tolstoy-stories-title {
  --heading-color: var(---color-heading-3--rgb);
}
.mega-menu .link--highlight {
  font-weight: var(---font-weight-body--bold);
}
.mega-menu .mega-menu__columns-wrapper--no-sublinks {
  flex-wrap: unset;
  flex-direction: column;
  gap: 15px;
  margin: 0;
}
.mega-menu .mega-menu__columns-wrapper--no-sublinks .mega-menu__column {
  margin: 0 !important;
}
.mega-menu .mega-menu__shop-by-color {
  margin-left: 10px;
}

.shopify-section--mini-cart {
  /* ----- Footer ----- */
  /* ----- Cart Notice Message ----- */
  /* ----- Gift Wrap ----- */
  /* ----- Recommendations ----- */
  /* ----- Checkout Button ----- */
  /* ----- Terms Checkbox ----- */
}
.shopify-section--mini-cart .mini-cart__drawer-footer {
  border: 0 !important;
  padding-top: 0;
}
.shopify-section--mini-cart .cart-notice-message {
  display: flex;
  gap: 10px;
}
.shopify-section--mini-cart .cart-notice-message__icon {
  max-width: 30px;
}
.shopify-section--mini-cart .mini-cart__actions {
  padding-top: 1em;
  border-top: 1px solid var(---color-line);
}
.shopify-section--mini-cart #order-note-toggle {
  margin-left: auto;
}
.shopify-section--mini-cart .line-item {
  margin-top: 20px;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(---color-line);
  /* ----- Line Items ----- */
  /* ----- Quantity Selectors ----- */
  /* ----- Addons ----- */
}
.shopify-section--mini-cart .line-item .price {
  white-space: nowrap;
}
.shopify-section--mini-cart .line-item .line-item__info {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.shopify-section--mini-cart .line-item .line-item__remove-button {
  margin-left: 0;
}
.shopify-section--mini-cart .line-item .line-item__image-wrapper {
  border: 1px solid var(---color-line);
  width: 150px;
  border-radius: 8px;
  overflow: hidden;
}
@media only screen and (max-width: 1000px) {
  .shopify-section--mini-cart .line-item .line-item__image-wrapper {
    width: 85px;
  }
}
.shopify-section--mini-cart .line-item .line-item__content-wrapper {
  margin: 10px 0;
}
.shopify-section--mini-cart .line-item .product-item-meta__title {
  font-weight: 400;
  margin-bottom: 0;
}
.shopify-section--mini-cart .line-item .product-item-meta__badges {
  padding: 0;
}
.shopify-section--mini-cart .line-item .product-item-meta__property-list {
  margin-top: 0;
}
.shopify-section--mini-cart .line-item .quantity-selector {
  margin-right: 10px;
}
.shopify-section--mini-cart .line-item .quantity-selector__button {
  background-color: var(---color--kyte-light-grey);
}
.shopify-section--mini-cart .line-item .quantity-selector__input,
.shopify-section--mini-cart .line-item .quantity-selector__button {
  --quantity-selector-height: 45px;
  height: var(--quantity-selector-height);
  width: var(--quantity-selector-height);
}
.shopify-section--mini-cart .line-item .quantity-selector.quantity-selector--unstyled {
  padding: 0;
  border: 0;
  display: inline-flex;
  align-items: center;
  outline: none !important;
}
.shopify-section--mini-cart .line-item .quantity-selector.quantity-selector--unstyled:before {
  content: "x";
}
.shopify-section--mini-cart .line-item .quantity-selector.quantity-selector--unstyled .quantity-selector__input {
  --quantity-selector-height: auto;
  padding: 0;
  cursor: default;
}
.shopify-section--mini-cart .line-item .cart-product-addons {
  display: flex;
  flex-direction: column;
  margin-top: 15px;
  gap: 15px;
}
.shopify-section--mini-cart .line-item .cart-product-addon {
  padding: 10px 20px;
  background: var(---background-color--content-2);
  border-radius: var(--block-border-radius);
}
.shopify-section--mini-cart .line-item .cart-product-addon__header {
  display: flex;
  align-items: center;
  gap: 1em;
}
.shopify-section--mini-cart .line-item .cart-product-addon__remove {
  font-size: var(---font-size-body-small--desktop);
}
.shopify-section--mini-cart .line-item .cart-product-addon__price {
  margin-left: auto;
}
.shopify-section--mini-cart .line-item .cart-product-addon-details {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  flex: 1 0 auto;
  margin-top: 10px;
  margin-bottom: 5px;
}
@media only screen and (min-width: 1001px) {
  .shopify-section--mini-cart .line-item .cart-product-addon-details {
    grid: minmax(0, 1fr)/auto-flow;
  }
}
.shopify-section--mini-cart .line-item .cart-product-addon-detail {
  display: flex;
  gap: 10px;
}
.shopify-section--mini-cart .line-item .cart-product-addon-detail__icon {
  display: flex;
  gap: 10px;
  width: 30px;
  min-width: 30px;
  height: 30px;
  border: 2px solid var(---color-line);
  background-color: #fff;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 16px;
}
.shopify-section--mini-cart .line-item .cart-product-addon-detail--font .cart-product-addon-detail__icon,
.shopify-section--mini-cart .line-item .cart-product-addon-detail--color .cart-product-addon-detail__icon {
  border-radius: 100%;
}
.shopify-section--mini-cart .line-item .cart-product-addon-detail--color .cart-product-addon-detail__icon {
  background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9.99998 2C5.60003 2 2 5.60004 2 10C2 14.4002 5.60003 18 9.99998 18C10.7556 18 11.3333 17.4223 11.3333 16.6667C11.3333 16.3112 11.2001 16 10.9779 15.7778C10.7556 15.5555 10.6224 15.2443 10.6224 14.8889C10.6224 14.1333 11.2001 13.5555 11.9557 13.5555H13.5556C16 13.5555 18 11.5555 18 9.11113C18 5.20008 14.4001 2 9.99998 2ZM5.11112 10C4.35545 10 3.77779 9.42233 3.77779 8.66667C3.77779 7.911 4.35545 7.33333 5.11112 7.33333C5.86674 7.33333 6.44445 7.911 6.44445 8.66667C6.44445 9.42233 5.86674 10 5.11112 10ZM7.77778 6.44446C7.02211 6.44446 6.44445 5.86675 6.44445 5.11113C6.44445 4.35546 7.02211 3.77779 7.77778 3.77779C8.5334 3.77779 9.11111 4.35546 9.11111 5.11113C9.11111 5.86675 8.5334 6.44446 7.77778 6.44446ZM12.2222 6.44446C11.4666 6.44446 10.8889 5.86675 10.8889 5.11113C10.8889 4.35546 11.4666 3.77779 12.2222 3.77779C12.9778 3.77779 13.5555 4.35546 13.5555 5.11113C13.5555 5.86675 12.9778 6.44446 12.2222 6.44446ZM14.8888 10C14.1332 10 13.5555 9.42233 13.5555 8.66667C13.5555 7.911 14.1332 7.33333 14.8888 7.33333C15.6445 7.33333 16.2222 7.911 16.2222 8.66667C16.2222 9.42233 15.6445 10 14.8888 10Z' fill='black'/%3E%3C/svg%3E%0A");
}
.shopify-section--mini-cart .line-item .cart-product-addon-detail--font .cart-product-addon-detail__icon {
  background-image: url("data:image/svg+xml,%3Csvg width='15' height='21' viewBox='0 0 15 21' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M2.86133 20.5439C2.18815 20.5439 1.6582 20.3721 1.27148 20.0283C0.877604 19.6846 0.680664 19.2692 0.680664 18.7822C0.680664 18.4313 0.798828 18.1484 1.03516 17.9336C1.26432 17.7188 1.52572 17.6113 1.81934 17.6113C2.10579 17.6113 2.36361 17.708 2.59277 17.9014C2.81478 18.1019 2.92578 18.3704 2.92578 18.707C2.92578 18.8861 2.86849 19.0615 2.75391 19.2334C2.63216 19.4124 2.57129 19.5342 2.57129 19.5986C2.57129 19.6774 2.61068 19.7383 2.68945 19.7812C2.76107 19.8242 2.85059 19.8457 2.95801 19.8457C3.50944 19.8457 3.92839 19.4053 4.21484 18.5244C4.5013 17.6507 5.0957 15.0941 5.99805 10.8545L6.90039 7.04102H5.28906L5.49316 6.12793H7.10449C7.2334 5.58366 7.50553 4.84603 7.9209 3.91504C8.34342 2.97689 8.88053 2.22135 9.53223 1.64844C10.32 0.968099 11.1292 0.62793 11.96 0.62793C12.626 0.62793 13.1702 0.77832 13.5928 1.0791C14.0225 1.37988 14.2373 1.82747 14.2373 2.42188C14.2373 2.77279 14.1263 3.05566 13.9043 3.27051C13.6895 3.47819 13.4316 3.58203 13.1309 3.58203C12.8372 3.58203 12.583 3.47819 12.3682 3.27051C12.1533 3.06283 12.0459 2.81934 12.0459 2.54004C12.0459 2.34668 12.1032 2.14974 12.2178 1.94922C12.3395 1.74154 12.4004 1.61979 12.4004 1.58398C12.4004 1.53385 12.3753 1.4694 12.3252 1.39062C12.2679 1.31901 12.1676 1.2832 12.0244 1.2832C11.6234 1.2832 11.2581 1.5625 10.9287 2.12109C10.6064 2.67969 10.3057 3.49609 10.0264 4.57031L9.61816 6.12793H11.6592L11.4551 7.04102H9.46777C8.4222 12.1829 7.55924 15.556 6.87891 17.1602C5.91927 19.416 4.58008 20.5439 2.86133 20.5439Z' fill='black'/%3E%3C/svg%3E%0A");
}
.shopify-section--mini-cart .line-item .cart-product-addon-detail--embroidery-text .cart-product-addon-detail__icon {
  background-image: url("data:image/svg+xml,%3Csvg width='17' height='17' viewBox='0 0 17 17' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10.6205 10.5156L7.77314 3.56273L4.96184 10.5156H10.6205ZM0 17V16.5439C0.632744 16.4699 1.1053 16.2234 1.41767 15.8042C1.73804 15.3851 2.28269 14.2468 3.05159 12.3894L8.18163 0H8.66219L14.7894 14.3125C15.1979 15.2659 15.5223 15.8576 15.7625 16.0877C16.0108 16.3096 16.4233 16.4617 17 16.5439V17H10.7406V16.5439C11.4615 16.4781 11.926 16.4 12.1343 16.3096C12.3425 16.211 12.4466 15.9768 12.4466 15.607C12.4466 15.4837 12.4066 15.2659 12.3265 14.9536C12.2464 14.6413 12.1343 14.3125 11.9901 13.9674L10.9689 11.5388H4.52933C3.88857 13.1907 3.50412 14.2016 3.37597 14.5714C3.25583 14.933 3.19576 15.2207 3.19576 15.4344C3.19576 15.8617 3.36396 16.1576 3.70035 16.322C3.9086 16.4206 4.30106 16.4946 4.87774 16.5439V17H0Z' fill='black'/%3E%3C/svg%3E%0A");
}
.shopify-section--mini-cart .mini-cart__recommendations-list {
  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: minmax(64vw, 1fr);
  grid-gap: var(--grid-gap);
}
.shopify-section--mini-cart .mini-cart__recommendations-inner {
  margin-top: 0;
  padding-top: 0;
  background: transparent;
}
.shopify-section--mini-cart .mini-cart__recommendations-inner .scroller {
  overflow-x: auto;
  overflow-y: hidden;
  scroll-snap-type: x mandatory;
  margin-left: calc(-1 * var(--container-gutter));
  margin-right: calc(-1 * var(--container-gutter));
  scrollbar-width: none;
  -ms-overflow-style: none;
  /* Internet Explorer 10+ */
  scrollbar-width: none;
  /* Firefox */
}
.shopify-section--mini-cart .mini-cart__recommendations-inner .scroller::-webkit-scrollbar {
  display: none;
  /* Safari and Chrome */
}
.shopify-section--mini-cart .mini-cart__recommendations-inner .scroller__inner {
  min-width: -webkit-min-content;
  min-width: -moz-min-content;
  min-width: min-content;
  padding-left: var(--container-gutter);
  padding-right: var(--container-gutter);
  margin-left: auto;
  margin-right: auto;
}
.shopify-section--mini-cart .mini-cart__recommendations-inner .product-item.product-item--upsell {
  background: var(---background-color--content-2);
  margin: 0;
  max-width: 300px;
}
.shopify-section--mini-cart .mini-cart__recommendations-inner .product-item.product-item--upsell .product-item__info {
  flex-direction: row;
  width: 100%;
}
.shopify-section--mini-cart .mini-cart__recommendations-inner .product-item.product-item--upsell .product-item__image-wrapper {
  width: 80px;
  height: 100%;
  margin-right: 15px;
}
.shopify-section--mini-cart .mini-cart__recommendations-inner .mini-cart__recommendations-list {
  grid-auto-columns: minmax(40vw, 1fr);
  grid-auto-columns: 300px;
}
.shopify-section--mini-cart .mini-cart__recommendations-inner .mini-cart__recommendations-list .product-item {
  max-width: 300px;
  padding: 15px;
  background-color: var(---color--kyte-cream);
  border-radius: var(--block-border-radius);
}
.shopify-section--mini-cart .mini-cart__recommendations-inner .mini-cart__recommendations-list > div {
  display: inline-block;
  max-width: 100%;
}
.shopify-section--mini-cart .mini-cart__recommendations-inner .product-item-meta__title {
  white-space: normal;
}
.shopify-section--mini-cart .checkout-button__cart .icon {
  position: relative;
  top: 0px;
  width: 20px;
  height: 20px;
  max-height: 20px;
  stroke: none;
}
.shopify-section--mini-cart .checkout-button-message {
  margin-top: 10px;
}
.shopify-section--mini-cart .mini-cart__drawer-footer {
  padding-top: 15px;
}
.shopify-section--mini-cart .mini-cart__checkbox {
  margin-top: 12px;
  display: flex;
  align-items: center;
}

#mobile-menu-drawer .linklist__item-swatch {
  display: inline-block;
  height: 18px;
  width: 18px;
  border-radius: 100%;
}
#mobile-menu-drawer .linklist__item > a {
  display: flex;
  align-items: center;
  gap: 8px;
}
#mobile-menu-drawer .drawer__header {
  display: flex;
  align-items: center;
  gap: 20px;
  border-bottom: 1px solid rgba(var(--text-color), 0.15);
}
#mobile-menu-drawer .drawer__logo-link {
  display: flex;
  align-items: center;
  max-width: 150px;
  color: RGB(var(--logo-color));
}
#mobile-menu-drawer .drawer__logo-link img,
#mobile-menu-drawer .drawer__logo-link svg {
  width: 100%;
}
#mobile-menu-drawer .drawer__logo-link svg * {
  fill: currentColor;
}
#mobile-menu-drawer .mobile-nav__link-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
}
#mobile-menu-drawer .mobile-nav__image-push .mobile-nav__image-heading {
  --heading-color: var(---color--primary--rgb) ;
}
#mobile-menu-drawer .mobile-nav__link.color-link {
  justify-content: start;
}
#mobile-menu-drawer .mobile-nav__link-inner {
  display: flex;
  align-items: center;
  gap: 0.5em;
}
#mobile-menu-drawer .mobile-nav__link-inner-icon {
  opacity: 0.5;
  display: flex;
  align-items: center;
}
#mobile-menu-drawer .mobile-secondary-menu .mobile-secondary_menu__item {
  border: none !important;
  font-size: var(---font-size-body--mobile);
}
#mobile-menu-drawer .mobile-secondary-menu .mobile-secondary_menu__link {
  padding: 8px 0;
}

.sub-brand-slider {
  padding: 20px 0;
  margin: 20px calc(-1 * var(--container-gutter));
  background-color: RGB(var(--background-2));
  border-top: 1px solid rgba(var(--text-color), 0.15);
  border-bottom: 1px solid rgba(var(--text-color), 0.15);
}
.sub-brand-slider .sub-brand-slider__title {
  margin-bottom: 0.5em;
}
.sub-brand-slider .sub-brand-slider__images-wrapper {
  align-items: flex-start;
  gap: 12px;
  grid-auto-flow: column;
  display: grid;
}
.sub-brand-slider .sub-brand-slider__images-scroller {
  padding: 6px 0;
  padding-inline: var(--container-gutter);
  grid-auto-flow: column;
  gap: 12px;
  width: -moz-fit-content;
  width: -webkit-fit-content;
  width: fit-content;
  display: grid;
}
.sub-brand-slider .sub-brand-slider-logo {
  --transition-duration: 0.25s;
  position: relative;
  display: inline-flex;
  min-width: 220px;
  max-width: 220px;
  padding: 10px 25px;
  background-color: #fff;
  box-shadow: var(---shadow--section);
  border-radius: var(--block-border-radius);
  transition: var(--transition-duration) background-color;
}
.sub-brand-slider .sub-brand-slider-logo img,
.sub-brand-slider .sub-brand-slider-logo svg {
  display: block;
  width: 100%;
}
.sub-brand-slider .sub-brand-slider-logo svg * {
  fill: RGB(var(--logo-color));
}
.sub-brand-slider .sub-brand-slider-logo .sub-brand-slider-logo__inner {
  display: flex;
  align-items: center;
  opacity: 0.5;
  transition: var(--transition-duration) opacity;
}
.sub-brand-slider .sub-brand-slider-logo:hover .sub-brand-slider-logo__inner, .sub-brand-slider .sub-brand-slider-logo.sub-brand-slider-logo--active .sub-brand-slider-logo__inner {
  opacity: 1;
}
.footer--kyte {
  --footer-block-spacing: 20px;
  padding-block: 20px;
}
@media only screen and (min-width: 741px) {
  .footer--kyte {
    --footer-block-spacing: 30px;
  }
}
.footer--kyte .social-media__link {
  width: 50px;
  height: 50px;
}
.footer--kyte .footer-navigation__header,
.footer--kyte .footer-info-block__title,
.footer--kyte .footer-signup-block__title {
  font-size: var(---font-size-subheading-small) !important;
}
@media only screen and (max-width: 1000px) {
  .footer--kyte .footer-navigation__header,
.footer--kyte .footer-info-block__title,
.footer--kyte .footer-signup-block__title {
    font-size: var(---font-size-subheading) !important;
  }
}
.footer--kyte .button:not(.button--outline), .footer--kyte .swym-add-to-wishlist:not(.button--outline),
.footer--kyte .giftreggie-pdp-registry-cta:not(.button--outline), .footer--kyte .shopify-payment-button__button--unbranded:not(.button--outline), .footer--kyte .drawer .popover .wk-button:not(.button--outline), .drawer .popover .footer--kyte .wk-button:not(.button--outline) {
  color: RGB(var(--button-text));
}
.footer--kyte .input__field {
  background: RGB(var(--input-background));
}

.footer-block {
  padding: var(--footer-block-spacing);
  background-color: RGB(var(--block-background));
  border-radius: var(--block-border-radius);
}

.footer__inner {
  display: grid;
  grid-auto-flow: row;
  gap: var(--footer-block-spacing);
}

/* ----- Top - Brand and Marketing ----- */
.footer__top {
  display: grid;
  gap: var(--footer-block-spacing);
  grid-auto-flow: row;
}
@media only screen and (min-width: 1001px) {
  .footer__top {
    grid-template-columns: minmax(200px, 300px) minmax(0, 1fr);
  }
}
.footer__top .social-media {
  flex-wrap: wrap;
}
@media only screen and (max-width: 1000px) {
  .footer__top .social-media {
    justify-content: center;
  }
}

.footer__logo-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
@media only screen and (max-width: 1000px) {
  .footer__logo-container {
    text-align: center;
  }
}

.footer__logo img {
  max-width: 200px;
}

.footer-signup-blocks {
  display: grid;
  gap: var(--footer-block-spacing);
}
@media only screen and (min-width: 1001px) {
  .footer-signup-blocks {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

.footer-signup-block {
  display: flex;
  flex-direction: column;
  gap: 0.5em;
  border-radius: var(--block-border-radius);
}
.footer-signup-block form {
  margin: 0;
}
.footer-signup-block .banner {
  margin: 1em 0;
}
.footer-signup-block .button:not(.button--text), .footer-signup-block .swym-add-to-wishlist:not(.button--text),
.footer-signup-block .giftreggie-pdp-registry-cta:not(.button--text), .footer-signup-block .shopify-payment-button__button--unbranded:not(.button--text), .footer-signup-block .drawer .popover .wk-button:not(.button--text), .drawer .popover .footer-signup-block .wk-button:not(.button--text) {
  margin: 0;
}
.footer-signup-block .newsletter__form .button, .footer-signup-block .newsletter__form .swym-add-to-wishlist,
.footer-signup-block .newsletter__form .giftreggie-pdp-registry-cta, .footer-signup-block .newsletter__form .shopify-payment-button__button--unbranded, .footer-signup-block .newsletter__form .drawer .popover .wk-button, .drawer .popover .footer-signup-block .newsletter__form .wk-button {
  height: calc(var(--form-input-field-height) - 2px) !important;
}
@media only screen and (max-width: 1000px) {
  .footer-signup-block {
    border: 1px solid var(---color-line);
    gap: 0;
  }
}

.footer-signup-block__header {
  display: flex;
  justify-content: space-between;
}

@media only screen and (max-width: 1000px) {
  .footer-signup-block__title {
    margin-bottom: 0;
    width: 100%;
    padding: 15px var(--footer-block-spacing);
  }
}

.footer-signup-block__expander {
  display: none;
}
.footer-signup-block__expander svg {
  transition: transform 0.4s ease-in-out, opacity 0.4s ease-in-out;
}
.footer-signup-block__expander[aria-expanded=true] svg {
  transform: rotate(180deg);
}
@media only screen and (max-width: 1000px) {
  .footer-signup-block__expander {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 50px;
  }
}

@media only screen and (min-width: 1001px) {
  .footer-signup-block__collapsible {
    height: auto;
    visibility: visible;
    overflow: visible;
  }
}

.footer-signup-block__body {
  display: grid;
  grid-auto-flow: row;
  gap: 0.5em;
}
@media only screen and (max-width: 1000px) {
  .footer-signup-block__body {
    padding: 15px var(--footer-block-spacing);
    border-top: 1px solid var(---color-line);
  }
}

.footer-signup-block__title {
  font-size: var(---font-size-subheading-small) !important;
}
@media only screen and (max-width: 1000px) {
  .footer-signup-block__title {
    font-size: var(---font-size-subheading) !important;
  }
}

/* ----- Middle - Navigation and Info ----- */
.footer__middle {
  display: grid;
  gap: var(--footer-block-spacing);
}
@media only screen and (min-width: 1201px) {
  .footer__middle {
    grid-template-columns: minmax(0, 2fr) minmax(0, 1fr);
  }
}

.footer__info-blocks {
  order: 0;
}
@media only screen and (min-width: 1201px) {
  .footer__info-blocks {
    order: 1;
  }
}

.footer__navigation {
  order: 1;
}
@media only screen and (min-width: 1201px) {
  .footer__navigation {
    order: 0;
  }
}

.footer-navigation {
  overflow: hidden;
}
@media (max-width: 479px) {
  .footer-navigation {
    padding: 0;
  }
}

.footer-navigation__inner {
  display: grid;
  gap: calc(var(--footer-block-spacing) + 1em) var(--footer-block-spacing);
}
@media (min-width: 500px) {
  .footer-navigation__inner {
    grid-template-columns: repeat(2, minmax(200px, 300px));
  }
}
@media only screen and (min-width: 741px) {
  .footer-navigation__inner {
    grid-template-columns: repeat(3, minmax(180px, 300px));
  }
}
@media (max-width: 479px) {
  .footer-navigation__inner {
    gap: 0;
  }
}

.footer-navigation__header {
  width: 100%;
}
@media (max-width: 479px) {
  .footer-navigation__header {
    margin-bottom: 0;
    padding: 15px var(--footer-block-spacing);
  }
}

@media (max-width: 479px) {
  .footer-navigation__column {
    border-bottom: 1px solid var(---color-line);
  }
  .footer-navigation__column:last-child {
    border-bottom: 0;
  }
}

.footer-navigation__linklist {
  display: grid;
  grid-auto-flow: row;
  gap: 0.75em;
}

.footer-info-blocks {
  display: flex;
  flex-direction: column;
  gap: var(--footer-block-spacing);
  height: 100%;
}

.footer-info-block {
  --image-block-size: 95px;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 0.5em;
}
@media only screen and (min-width: 1001px) {
  .footer-info-block {
    --image-block-size: 120px;
  }
}
@media only screen and (min-width: 1201px) {
  .footer-info-block {
    --image-block-size: 150px;
  }
}

.footer-info-block--text-offset {
  padding-right: calc(var(--image-block-size) * 0.75);
}

.footer-info-block__image {
  position: absolute;
  top: 0;
  right: 0;
  transform: translate(15%, -15%);
  display: block;
  width: var(--image-block-size);
}
@media only screen and (min-width: 1001px) {
  .footer-info-block__image {
    transform: translate(25%, -25%);
  }
}

.footer-info-block__image-zone {
  display: block;
  width: 75px;
  height: 75px;
  float: right;
  shape-outside: circle(50%);
}

/* ----- Bottom - Legal, Credit and Selectors ----- */
.footer__bottom {
  display: flex;
  align-items: start;
  justify-content: space-between;
  gap: 20px;
}
@media (max-width: 1200px) {
  .footer__bottom {
    flex-direction: column;
    align-items: center;
  }
}

.footer__bottom-left {
  display: grid;
  grid-auto-flow: row;
  gap: var(--footer-block-spacing);
}

.footer__legal {
  display: flex;
  gap: var(--footer-block-spacing);
}
@media only screen and (max-width: 740px) {
  .footer__legal {
    flex-direction: column;
    align-items: center;
  }
}

.footer__legal,
.footer__localization {
  min-height: 36px;
}

.footer__credit {
  display: inline-flex;
  flex-wrap: wrap;
  gap: 0.5em;
  align-items: center;
}
@media only screen and (max-width: 740px) {
  .footer__credit {
    justify-content: center;
    text-align: center;
    gap: var(--footer-block-spacing);
  }
}
@media (max-width: 479px) {
  .footer__credit {
    justify-content: center;
    text-align: center;
    gap: 0.5em;
  }
}

.footer__legal-nav {
  display: inline-flex;
  gap: 1em;
  align-items: center;
}
@media (max-width: 479px) {
  .footer__legal-nav {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
}

.footer__country-selector {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1em;
  white-space: nowrap;
}
@media (max-width: 479px) {
  .footer__country-selector {
    flex-wrap: wrap;
  }
}

.footer-popover-container {
  gap: 0.5em;
}
@media only screen and (max-width: 740px) {
  .footer-popover-container {
    display: flex;
    flex-direction: column;
  }
}

.footer-selector {
  --heading-color: var(--root-heading-color);
  --text-color: var(--root-text-color);
  --background: var(--root-background);
  --primary-button-background: var(--root-primary-button-background);
  --primary-button-text-color: var(--root-primary-button-text-color);
  --section-background: var(--root-background);
  z-index: 10;
  color: RGB(var(--text-color));
  background: RGB(var(--background));
  width: auto;
}

.footer__localization {
  display: inline-flex;
  flex-direction: column;
  gap: 1em;
  flex-wrap: wrap;
  justify-content: end;
}

.footer-accordion {
  display: grid;
  grid-auto-flow: row;
  gap: 0.75em;
}
@media (max-width: 479px) {
  .footer-accordion {
    gap: 0;
  }
}

.footer-accordion__header {
  display: flex;
}

.footer-accordion__expander {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 50px;
}
@media (min-width: 480px) {
  .footer-accordion__expander {
    display: none;
  }
}

@media (min-width: 480px) {
  .footer-accordion__collapsible {
    height: auto;
    visibility: visible;
  }
}

@media (max-width: 479px) {
  .footer-accordion-content {
    padding: var(--footer-block-spacing);
    border-top: 1px solid var(---color-line);
  }
}

.newsletter-modal__form {
  margin-bottom: 20px;
}

@media only screen and (max-width: 1000px) {
  .image-with-text-block:not(.image-with-text-block--overlap-left):not(.image-with-text-block--overlap-right) .image-with-text-block__content {
    border-radius: var(--block-border-radius);
  }
}

.image-with-text .image-with-text__wrapper {
  overflow: visible;
}
@media screen and (min-width: 1400px) {
  .image-with-text .image-with-text__content-wrapper {
    width: calc(var(--grid-column-width) * 7 + var(--grid-gap) * 7);
  }
}

.shopify-section--product-content .product-content {
  justify-content: space-between;
}
.shopify-section--product-content .product-content .product-content__featured-products-list {
  margin-top: 0;
}
.shopify-section--product-content .product-content .product-item {
  margin: 0;
}
.shopify-section--product-content .product-content .product-content__featured-products-title {
  line-height: 1;
  margin-bottom: 18px;
}

.multi-column .multi-column__image-wrapper {
  margin-bottom: 15px;
}

.section.custom-featured-collections {
  position: relative;
}
.section.custom-featured-collections .product-list__inner-wrapper {
  position: relative;
}
.section.custom-featured-collections .product-list__header {
  margin-bottom: 40px;
}
.section.custom-featured-collections .section__header .button-wrapper {
  margin-top: 30px;
}
.section.custom-featured-collections.custom-featured-collection--horizontal-layout.custom-featured-collection--border-bottom .horizontal-layout-wrapper {
  padding-bottom: 50px;
  border-bottom: 1px solid var(---color-line);
}
@media only screen and (min-width: 1001px) {
  .section.custom-featured-collections.custom-featured-collection--horizontal-layout .horizontal-layout-wrapper {
    display: flex;
    width: 100%;
  }
  .section.custom-featured-collections.custom-featured-collection--horizontal-layout .horizontal-layout-wrapper .featured-collections {
    --card-width: 330px;
    width: calc(var(--container-max-width) - var(--container-gutter) - var(--container-gutter) - var(--card-width)) !important;
  }
  .section.custom-featured-collections.custom-featured-collection--horizontal-layout .horizontal-layout-wrapper .section__header {
    min-width: 330px;
    max-width: 330px;
    padding-right: 30px;
    text-align: left;
  }
  .section.custom-featured-collections.custom-featured-collection--horizontal-layout .horizontal-layout-wrapper .product-list__inner {
    margin: 0;
  }
  .section.custom-featured-collections.custom-featured-collection--horizontal-layout .horizontal-layout-wrapper .prev-next-button--next {
    position: relative;
    right: -20px;
  }
  .section.custom-featured-collections.custom-featured-collection--horizontal-layout .horizontal-layout-wrapper .prev-next-button--prev {
    position: relative;
    left: -20px;
  }
  .section.custom-featured-collections.custom-featured-collection--horizontal-layout .horizontal-layout-wrapper .product-list__inner--scroller {
    grid-auto-columns: 330px;
  }
}

.section.custom-image-with-text image-with-text {
  position: relative;
  z-index: 1;
}
@media only screen and (max-width: 1000px) {
  .section.custom-image-with-text image-with-text > .container {
    padding: 0;
  }
  .section.custom-image-with-text image-with-text .image-with-text__image-wrapper {
    margin: auto auto 20px auto;
  }
}
.section.custom-image-with-text image-with-text .image-with-text__wrapper {
  overflow: visible;
}
.section.custom-image-with-text image-with-text .image-with-text__content-wrapper {
  margin-top: 40px;
  margin-bottom: 40px;
}

.custom-image-with-text-overlay {
  position: relative;
  overflow: hidden;
}
.custom-image-with-text-overlay .button-wrapper {
  margin-top: 20px;
}
.custom-image-with-text-overlay.custom-image-with-text-overlay--border-radius .image-overlay {
  border-radius: var(--block-border-radius);
  overflow: hidden;
}
@media only screen and (max-width: 740px) {
  .custom-image-with-text-overlay .image-overlay {
    min-height: 600px;
    min-height: calc(80vh - var(--announcement-bar-height) - var(--header-height) - var(--vertical-breather) - var(--vertical-breather));
  }
  .custom-image-with-text-overlay .image-overlay .image-overlay__content-wrapper {
    display: flex;
  }
  .custom-image-with-text-overlay .image-overlay .image-overlay__content-wrapper .image-overlay__content {
    height: auto;
    margin: auto 24px;
    width: 100%;
  }
}

.section.custom-slideshow {
  position: relative;
  overflow: hidden;
}
.section.custom-slideshow .button-group {
  margin-top: 20px;
}
.section.custom-slideshow .rte {
  color: RGB(var(--text-color));
}
.section.custom-slideshow .button-group .button-group__wrapper {
  display: flex;
  justify-content: var(--section-blocks-justify);
}

.section.custom-text-with-icons .text-with-icons__item .text-with-icons__content-wrapper {
  max-width: 75%;
  margin: auto;
}
.section.custom-text-with-icons .text-with-icons__icon-wrapper {
  margin-bottom: 0;
}
.section.custom-text-with-icons .heading.heading--small, .section.custom-text-with-icons .tolstoy-stories .tolstoy-stories-title, .tolstoy-stories .section.custom-text-with-icons .tolstoy-stories-title {
  margin: 0 0 6px;
}
.section.custom-text-with-icons .text-with-icons__icon-wrapper svg * {
  stroke: RGB(var(--icon-color));
}
.section.custom-text-with-icons .text-with-icons__description p {
  margin-top: 10px;
  font-size: var(---font-size-body-small--desktop);
}

.section.custom-logo-list {
  --vertical-breather: 60px;
}
@media only screen and (min-width: 1001px) {
  .section.custom-logo-list.section--use-padding {
    padding-bottom: 120px;
  }
}
@media only screen and (max-width: 1000px) {
  .section.custom-logo-list .logo-list {
    padding: 0;
    margin-left: calc(var(--container-gutter) * -1);
    margin-right: calc(var(--container-gutter) * -1);
    overflow-x: scroll;
    overflow-y: hidden;
  }
  .section.custom-logo-list .logo-list .logo-list__list {
    display: flex;
    justify-content: flex-start;
  }
  .section.custom-logo-list .logo-list .logo-list__item {
    min-width: 40%;
    max-height: 80px;
  }
}

.section.custom-video .video-wrapper__poster-content {
  position: static;
}
.section.custom-video .video-wrapper__poster-content .text-container {
  display: flex;
  position: absolute;
  z-index: 1;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  margin: auto;
  max-width: var(--container-max-width);
  margin-left: auto;
  margin-right: auto;
  padding: var(--container-gutter);
}
.section.custom-video .text-container .heading, .section.custom-video .text-container .tolstoy-stories .tolstoy-stories-title, .tolstoy-stories .section.custom-video .text-container .tolstoy-stories-title {
  margin: 10px 0;
}
.section.custom-video .text-container .button-wrapper {
  margin-top: 30px;
}
.section.custom-video .custom-video__content--text-left {
  text-align: left;
}
.section.custom-video .custom-video__content--text-right {
  text-align: right;
}
.section.custom-video .custom-video__content--text-center {
  text-align: center;
}
.section.custom-video .custom-video__content--top_left .text-container {
  align-items: flex-start;
  justify-content: flex-start;
}
.section.custom-video .custom-video__content--top_center .text-container {
  align-items: flex-start;
  justify-content: center;
}
.section.custom-video .custom-video__content--top_right .text-container {
  align-items: flex-start;
  justify-content: flex-end;
}
.section.custom-video .custom-video__content--middle_left .text-container {
  align-items: center;
  justify-content: flex-start;
}
.section.custom-video .custom-video__content--middle_center .text-container {
  align-items: center;
  justify-content: center;
}
.section.custom-video .custom-video__content--middle_right .text-container {
  align-items: center;
  justify-content: flex-end;
}
.section.custom-video .custom-video__content--bottom_left .text-container {
  align-items: flex-end;
  justify-content: flex-start;
}
.section.custom-video .custom-video__content--bottom_center .text-container {
  align-items: flex-end;
  justify-content: center;
}
.section.custom-video .custom-video__content--bottom_right .text-container {
  align-items: flex-end;
  justify-content: flex-end;
}

.section.custom-video-quote.video-quote-background--overlap {
  position: relative;
}
.section.custom-video-quote.video-quote-background--overlap .section__color-wrapper {
  position: relative;
  z-index: 1;
  background: transparent !important;
}
.section.custom-video-quote.video-quote-background--overlap:before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 0;
  display: block;
  width: 100%;
  height: 75%;
  background: RGB(var(--section-accent-background));
}
.section.custom-video-quote .video-wrapper {
  border-radius: var(--block-border-radius);
  overflow: hidden;
}
.section.custom-video-quote .section__header {
  margin-top: 60px;
  margin-bottom: 60px;
}
.section.custom-video-quote .gallery__caption-cite > p:not(last-child) {
  margin-bottom: 0;
}

.section.custom-blog-posts .article-list {
  margin: 0;
}
@media only screen and (max-width: 740px) {
  .section.custom-blog-posts .article-list {
    gap: 10px;
  }
}
@media only screen and (max-width: 740px) {
  .section.custom-blog-posts .article-item {
    display: flex;
    gap: 15px;
  }
  .section.custom-blog-posts .article-item .article-item__image-container {
    max-width: 120px;
    width: 100%;
  }
  .section.custom-blog-posts .article-item .article-item__content .heading, .section.custom-blog-posts .article-item .article-item__content .tolstoy-stories .tolstoy-stories-title, .tolstoy-stories .section.custom-blog-posts .article-item .article-item__content .tolstoy-stories-title {
    margin: 6px 0;
  }
  .section.custom-blog-posts .article-item .horizontal-header {
    padding-bottom: 20px;
    border-bottom: 1px solid var(---color-line);
  }
}

.section.custom-product-feature-icons .section__color-wrapper {
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}
@media (max-width: 1000px) {
  .section.custom-product-feature-icons .section__color-wrapper {
    background-position: center 0px;
  }
}
.section.custom-product-feature-icons .product-feature-icons-container {
  margin-top: 30px;
}
.section.custom-product-feature-icons .product-feature-icons {
  display: flex;
  flex-wrap: nowrap;
  justify-content: center;
  max-width: 1050px;
  margin: auto;
  flex-direction: column;
}
@media only screen and (min-width: 741px) {
  .section.custom-product-feature-icons .product-feature-icons {
    flex-wrap: wrap;
    flex-direction: row;
  }
}
.section.custom-product-feature-icons .product-feature-icon {
  gap: 20px;
}
@media only screen and (min-width: 741px) {
  .section.custom-product-feature-icons .product-feature-icon {
    width: 20%;
    gap: 0;
    margin-bottom: 30px;
    margin-top: 30px;
  }
}
.section.custom-product-feature-icons .product-feature-icon {
  display: flex;
}
@media only screen and (max-width: 740px) {
  .section.custom-product-feature-icons .product-feature-icon {
    align-items: flex-start;
    margin-bottom: 20px;
    align-items: center;
  }
}
@media only screen and (min-width: 741px) {
  .section.custom-product-feature-icons .product-feature-icon {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: column;
    width: 20%;
    margin-bottom: 30px;
    margin-top: 30px;
  }
}
.section.custom-product-feature-icons .product-feature-icon__title {
  width: 100%;
}
.section.custom-product-feature-icons .product-feature-icon__image {
  min-width: 60px;
}
@media only screen and (min-width: 741px) {
  .section.custom-product-feature-icons .product-feature-icon__image {
    min-width: unset;
  }
}
.section.custom-product-feature-icons .section__color-wrapper--boxed {
  padding: 0 30px;
}
@media only screen and (max-width: 740px) {
  .section.custom-product-feature-icons .content-box {
    padding-top: 30px;
  }
}

.section.custom-newsletter .newsletter__form .input-row {
  grid-template-columns: auto;
}

.section.custom-testimonials-slider {
  overflow: hidden;
}
.section.custom-testimonials-slider .gallery__item {
  flex-direction: column;
  flex: 1 0 80%;
  background: RGB(var(--block-background));
  border-radius: var(--block-border-radius);
  color: RGB(var(--text-color));
}
@media only screen and (min-width: 741px) {
  .section.custom-testimonials-slider .gallery__item {
    flex: 1 0 60%;
    flex-direction: row;
  }
}
@media only screen and (min-width: 1001px) {
  .section.custom-testimonials-slider .gallery__item {
    flex: 1 0 70%;
  }
}
@media only screen and (min-width: 1201px) {
  .section.custom-testimonials-slider .gallery__item {
    flex: 1 0 50%;
    max-width: 800px;
  }
}
.section.custom-testimonials-slider .gallery__figure {
  display: flex;
  flex-direction: row;
  height: 100%;
  border-radius: var(--block-border-radius);
  overflow: hidden;
}
@media only screen and (max-width: 1000px) {
  .section.custom-testimonials-slider .gallery__figure {
    flex-direction: column;
  }
}
.section.custom-testimonials-slider .gallery__image {
  border-radius: unset;
  flex: 1 1 50%;
  height: 100%;
  border-radius: 0 !important;
  -o-object-fit: cover;
     object-fit: cover;
}
@media only screen and (max-width: 1000px) {
  .section.custom-testimonials-slider .gallery__image {
    max-height: 40vh;
  }
}
.section.custom-testimonials-slider .gallery__caption {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: var(--container-gutter);
  flex: 1;
  margin-top: 0;
  padding: var(--container-gutter);
  border-radius: 0;
}
.section.custom-testimonials-slider .gallery__caption .gallery__caption-name {
  margin-bottom: 0;
}
@media only screen and (min-width: 1001px) {
  .section.custom-testimonials-slider .gallery__caption {
    height: 100%;
    flex: 1 1 50%;
  }
}
.section.custom-testimonials-slider .gallery__caption-top {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.section.custom-testimonials-slider .gallery__caption-top > * {
  margin: 0;
}
.section.custom-testimonials-slider .gallery__caption-rating {
  display: flex;
  gap: 4px;
}
.section.custom-testimonials-slider .gallery__caption-rating svg {
  width: 12px;
  height: 12px;
}
.section.custom-testimonials-slider .gallery__caption-rating svg * {
  stroke: RGB(var(--star-color));
  fill: RGB(var(--star-color));
}
@media only screen and (min-width: 1001px) {
  .section.custom-testimonials-slider .gallery__caption-rating svg {
    width: 16px;
    height: 16px;
  }
}

.section.custom-featured-promotions .special-promotions__list {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: var(--container-gutter);
}
.section.custom-featured-promotions .special-promotions__list > native-carousel-item {
  display: block;
}
@media only screen and (max-width: 1000px) {
  .section.custom-featured-promotions .special-promotions__list {
    display: flex;
    scroll-snap-type: x mandatory;
    margin-left: calc(-1 * var(--container-gutter));
    margin-right: calc(-1 * var(--container-gutter));
  }
  .section.custom-featured-promotions .special-promotions__list > native-carousel-item {
    display: block;
    padding: 0 var(--container-gutter);
    width: 100%;
    flex: none;
    scroll-snap-align: center;
    scroll-snap-stop: always;
  }
}
.section.custom-featured-promotions .special-promotion-item {
  display: block;
  background: #FFFFFF;
  flex: 1 0 30%;
  margin: 0;
  border: 1px solid var(---color-line);
  border-radius: var(--block-border-radius);
  overflow: hidden;
  box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.04);
}
.section.custom-featured-promotions .special-promotion-item img {
  -o-object-fit: cover;
     object-fit: cover;
}
.section.custom-featured-promotions .special-promotion-item__caption {
  padding: 30px;
}
.section.custom-featured-promotions .special-promotion-item__figure {
  margin: -1px;
}
.section.custom-featured-promotions .special-promotion-item__figure-inner {
  overflow: hidden;
}
.section.custom-featured-promotions .special-promotion-item__title {
  margin-bottom: 8px;
  text-align: left;
}
.section.custom-featured-promotions .special-promotion-item__icon-wrapper {
  display: flex;
  align-items: center;
  gap: 15px;
  height: 40px;
}
.section.custom-featured-promotions .special-promotion-item__icon-title {
  margin-bottom: 0;
  color: var(---color-heading-3);
}
.section.custom-featured-promotions .special-promotion-item__icon-image {
  height: 28px;
  width: 32px;
}
@media only screen and (min-width: 1001px) {
  .section.custom-featured-promotions .special-promotion-item__icon-image {
    height: 40px;
  }
}
.section.custom-featured-promotions .special-promotion-item__icon-image img {
  margin: 0;
}
.section.custom-featured-promotions a.special-promotion-item .special-promotion-item__figure {
  position: relative;
}

.section.custom-content-with-carousel .heading, .section.custom-content-with-carousel .tolstoy-stories .tolstoy-stories-title, .tolstoy-stories .section.custom-content-with-carousel .tolstoy-stories-title {
  -webkit-margin-after: 0.25em;
          margin-block-end: 0.25em;
  max-width: 80%;
}
.section.custom-content-with-carousel .container {
  padding-left: var(--container-gutter);
  padding-right: var(--container-gutter);
}
.section.custom-content-with-carousel .container--extra-padding {
  padding-left: calc(var(--container-gutter) * 2);
  padding-right: calc(var(--container-gutter) * 2);
}
@media only screen and (max-width: 1000px) {
  .section.custom-content-with-carousel .horizontal-header {
    border-bottom: 0;
  }
}

@media only screen and (max-width: 1000px) {
  .usp-icons-carousel {
    border-top: 0;
  }
}
.usp-icons-carousel .usp-icons-carousel__list {
  display: flex;
  gap: var(--container-gutter);
  scroll-snap-type: x mandatory;
}
@media only screen and (min-width: 1001px) {
  .usp-icons-carousel .usp-icons-carousel__list {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
.usp-icons-carousel .usp-icons-carousel__item {
  display: block;
  flex: none;
  scroll-snap-align: center;
  scroll-snap-stop: always;
  width: 100%;
  margin-bottom: 20px;
}
.usp-icons-carousel .usp-icons-carousel__item-inner {
  display: flex;
  gap: 1em;
  padding: 12px;
}
@media only screen and (max-width: 1000px) {
  .usp-icons-carousel .usp-icons-carousel__item-inner {
    border: 1px solid var(---color-line);
    border-radius: var(--block-border-radius);
    box-shadow: var(--block-shadow);
  }
}
.usp-icons-carousel .usp-icons-carousel__icon {
  min-width: 50px;
  max-width: 50px;
  max-height: 50px;
}
.usp-icons-carousel .usp-icons-carousel__icon img {
  height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
}
.usp-icons-carousel .usp-icons-carousel__item-description {
  margin-left: 1em;
}
@media only screen and (max-width: 1000px) {
  .usp-icons-carousel .usp-icons-carousel__item-description {
    margin-left: 0;
  }
}
.usp-icons-carousel .usp-icons-carousel__dots {
  margin-top: 20px;
}
@media only screen and (max-width: 1000px) {
  .usp-icons-carousel .usp-icons-carousel__dots {
    margin-top: 0;
    margin-bottom: 20px;
  }
}

section.custom-collection-banner .image-overlay {
  border-radius: var(--block-border-radius);
  overflow: hidden;
}

section.custom-collection-banner--split .image-overlay {
  border-radius: var(--block-border-radius);
  overflow: hidden;
}
section.custom-collection-banner--split .image-overlay.image-overlay--small {
  --image-height: 300px;
}
section.custom-collection-banner--split .image-overlay.image-overlay--medium {
  --image-height: 375px;
}
section.custom-collection-banner--split .image-overlay.image-overlay--large {
  --image-height: 600px;
}
section.custom-collection-banner--split .image-overlay--no-image .text-container {
  max-width: unset !important;
  text-align: center;
}
section.custom-collection-banner--split .collection-grid-banner__overlay-subheading {
  margin: 0;
}
section.custom-collection-banner--split .collection-grid-banner__overlay-subheading + .heading, section.custom-collection-banner--split .tolstoy-stories .collection-grid-banner__overlay-subheading + .tolstoy-stories-title, .tolstoy-stories section.custom-collection-banner--split .collection-grid-banner__overlay-subheading + .tolstoy-stories-title {
  margin-top: 0;
}
section.custom-collection-banner--split .image-overlay__split-images {
  display: flex;
  width: 100%;
}
@media only screen and (max-width: 1000px) {
  section.custom-collection-banner--split .image-overlay__split-images {
    flex-direction: column;
  }
}
section.custom-collection-banner--split .image-overlay__split-image {
  width: 100%;
}
@media only screen and (max-width: 1000px) {
  section.custom-collection-banner--split .image-overlay__image-wrapper {
    position: static;
  }
}
section.custom-collection-banner--split .image-overlay__image {
  position: static;
}
@media only screen and (max-width: 1000px) {
  section.custom-collection-banner--split .image-overlay__image {
    max-height: 30vh;
  }
}
section.custom-collection-banner--split .image-overlay__split-image {
  position: relative;
  z-index: 1;
  width: 100%;
  flex: 1 1 auto;
}
section.custom-collection-banner--split .image-overlay__split-image--content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 30px;
}
@media only screen and (min-width: 1001px) {
  section.custom-collection-banner--split .image-overlay__split-image--content {
    padding: 50px;
  }
}
section.custom-collection-banner--split .image-overlay__split-image--content .heading, section.custom-collection-banner--split .image-overlay__split-image--content .tolstoy-stories .tolstoy-stories-title, .tolstoy-stories section.custom-collection-banner--split .image-overlay__split-image--content .tolstoy-stories-title {
  margin-bottom: 0;
}

:root {
  --scrolling-text-direction: 1;
  --scrolling-text-font-size: 18px;
  --scrolling-text-small-font-size: 12px;
  --scrolling-text-large-font-size: 24px;
}
@media screen and (min-width: 741px) {
  :root {
    --scrolling-text-font-size: 24px;
    --scrolling-text-small-font-size: 18px;
    --scrolling-text-large-font-size: 40px;
  }
}

.scrolling-text {
  --text-font-size: var(--scrolling-text-font-size);
  display: flex;
  justify-content: center;
  overflow: hidden;
  padding-block: 1.8em;
  color: RGB(var(--text-color));
  background-color: RGB(var(--background));
}
.scrolling-text.scrolling-text--font-large {
  --text-font-size: var(--scrolling-text-large-font-size);
}
.scrolling-text.scrolling-text--font-small {
  --text-font-size: var(--scrolling-text-small-font-size);
}
.scrolling-text.scrolling-text--inner-shadow {
  box-shadow: var(--section-shadow-inner);
}
.scrolling-text.scrolling-text--text-shadow {
  text-shadow: 0px 0px 10px rgba(0, 0, 0, 0.05);
}
.scrolling-text .scrolling-text__text p {
  font-weight: 500;
}

.scrolling-text__wrapper {
  display: grid;
}

.scrolling-text__text {
  -webkit-padding-start: min(1em, 2rem);
          padding-inline-start: min(1em, 2rem);
  -webkit-padding-end: min(1em, 2rem);
          padding-inline-end: min(1em, 2rem);
  font-size: var(--text-font-size);
}
.scrolling-text__text > * {
  line-height: 1.6;
}

@supports (overflow: clip) {
  .scrolling-text {
    overflow: clip visible;
  }
  .scrolling-text__text {
    line-height: 1;
  }
}
@media screen and (min-width: 700px) {
  .scrolling-text__text {
    -webkit-padding-start: min(1.5em, 4rem);
            padding-inline-start: min(1.5em, 4rem);
    -webkit-padding-end: min(1.5em, 4rem);
            padding-inline-end: min(1.5em, 4rem);
  }
}
@media (prefers-reduced-motion: no-preference) {
  .scrolling-text__wrapper {
    grid: auto/auto-flow -webkit-max-content;
    grid: auto/auto-flow max-content;
  }
  .scrolling-text--auto.scrolling-text--backwards .scrolling-text__text {
    -webkit-animation: marquee var(--marquee-animation-duration, 0s) linear infinite;
            animation: marquee var(--marquee-animation-duration, 0s) linear infinite;
  }
  .scrolling-text--auto.scrolling-text--forwards .scrolling-text__text {
    -webkit-animation: marquee-reverse var(--marquee-animation-duration, 0s) linear infinite;
            animation: marquee-reverse var(--marquee-animation-duration, 0s) linear infinite;
  }
  .scrolling-text--backwards {
    transform-origin: center left;
  }
  .scrolling-text--backwards .scrolling-text__text {
    animation-direction: reverse !important;
  }
  .scrolling-text--forwards {
    transform-origin: center right;
  }
  .scrolling-text--scroll .scrolling-text__wrapper {
    transform: translateX(calc(var(--scrolling-text-direction) * (50vw - 10% * var(--visibility-progress, 0))));
    min-width: -webkit-min-content;
    min-width: -moz-min-content;
    min-width: min-content;
    transition: transform 50ms linear;
  }
}
@media (prefers-reduced-motion: reduce) {
  .scrolling-text__wrapper {
    text-align: center;
    justify-content: center;
  }
}
@-webkit-keyframes marquee {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(calc(100% * var(--scrolling-text-direction)));
  }
}
@keyframes marquee {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(calc(100% * var(--scrolling-text-direction)));
  }
}
@-webkit-keyframes marquee-reverse {
  from {
    transform: translateX(calc(100% * var(--scrolling-text-direction)));
  }
  to {
    transform: translateX(0);
  }
}
@keyframes marquee-reverse {
  from {
    transform: translateX(calc(100% * var(--scrolling-text-direction)));
  }
  to {
    transform: translateX(0);
  }
}
.shopify-section--custom-multi-column .multi-column-image--circle {
  border-radius: 100%;
}
.shopify-section--custom-multi-column .multi-column__image-wrapper {
  position: relative;
}
.shopify-section--custom-multi-column .multi-column__image-content {
  display: block;
  position: absolute;
  bottom: var(--bottom-offset, 0);
  left: 0;
  right: 0;
  padding: 15px 20px;
  background-color: RGBA(0, 0, 0, 0.25);
}
.shopify-section--custom-multi-column .multi-column__image-overlay {
  background-color: RGBA(var(--image-overlay-color), var(--image-overlay-opacity));
}

.shopify-section--main-product {
  /* ----- Featured Products ----- */
  /* ----- Product Meta ----- */
  /* ----- Price List + Afterpay ----- */
}
@media only screen and (min-width: 1001px) {
  .shopify-section--main-product .product {
    padding-top: 24px;
  }
}
.shopify-section--main-product .product-content__featured-products {
  margin-top: 40px;
}
.shopify-section--main-product .product-content__featured-products .product-content__featured-products-list {
  margin-top: 15px;
  gap: 10px;
}
.shopify-section--main-product .product-meta__top {
  display: flex;
  justify-content: space-between;
  gap: 20px;
}
.shopify-section--main-product .product-meta__price-list-container {
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-content: start;
  gap: 3px;
  line-height: 1;
  min-height: 26px;
}
.shopify-section--main-product .product-meta__price-list-container square-placement {
  margin: 0;
  scale: 0.9;
  transform-origin: left;
  text-align: left;
  opacity: 0.5;
}

@media only screen and (min-width: 1001px) {
  .bespoke-faq-videos {
    margin-left: calc(var(--grid-column-width) + var(--grid-gap));
    margin-right: calc(var(--grid-column-width) + var(--grid-gap));
  }
}
.bespoke-faq-videos .section__color-wrapper {
  overflow: hidden;
}
.bespoke-faq-videos .faq-videos__inner {
  display: flex;
}
@media (max-width: 1000px) {
  .bespoke-faq-videos .faq-videos__inner {
    flex-direction: column;
  }
}
.bespoke-faq-videos .faq-videos__playback {
  min-width: 40%;
  background: var(---color--brand-7);
}
@media only screen and (min-width: 1001px) {
  .bespoke-faq-videos .faq-videos__playback {
    max-width: 40%;
  }
}
.bespoke-faq-videos .faq-videos__playback native-video {
  width: 100%;
  height: 100%;
}
.bespoke-faq-videos .faq-videos__playback native-video img,
.bespoke-faq-videos .faq-videos__playback native-video svg {
  cursor: pointer;
  vertical-align: top;
  -o-object-fit: cover;
     object-fit: cover;
  height: 100%;
}
.bespoke-faq-videos .faq-videos__content {
  width: 100%;
}
.bespoke-faq-videos .faq-videos__content-inner {
  max-width: 600px;
  margin: auto;
  padding: var(--vertical-breather);
}
.bespoke-faq-videos .faq-videos-list {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 30px 50px;
}
.bespoke-faq-videos .native-video--stopped:before {
  content: "";
  display: block;
  width: 60px;
  height: 60px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  z-index: 1;
  transform: scale(1);
  transition: transform 0.25s;
  background-image: url("data:image/svg+xml,%3Csvg width='58' height='58' viewBox='0 0 58 58' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M28.9987 53.1667C42.3456 53.1667 53.1654 42.3469 53.1654 29C53.1654 15.6532 42.3456 4.83337 28.9987 4.83337C15.6518 4.83337 4.83203 15.6532 4.83203 29C4.83203 42.3469 15.6518 53.1667 28.9987 53.1667Z' stroke='white' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M24.168 19.3334L38.668 29L24.168 38.6667V19.3334Z' stroke='white' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
  pointer-events: none;
}
.bespoke-faq-videos .native-video--stopped:after {
  position: absolute;
  display: block;
  content: "";
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background: rgba(0, 0, 0, 0.1);
  transition: 0.25s background;
}
.bespoke-faq-videos .native-video--stopped:hover:before {
  transform: scale(1.2);
}
.bespoke-faq-videos .native-video--stopped:hover:after {
  background: rgba(0, 0, 0, 0.3);
}

.faq-video:focus .faq-video__image .faq-video__image-overlay, .faq-video:hover .faq-video__image .faq-video__image-overlay {
  opacity: 1;
}

.faq-video__image {
  position: relative;
  overflow: hidden;
  border-radius: var(--block-border-radius);
  overflow: hidden;
}
.faq-video__image img,
.faq-video__image svg {
  vertical-align: top;
}
.faq-video__image .faq-video__image-overlay {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
  opacity: 0;
  transition: opacity 0.25s;
  display: flex;
  justify-content: center;
  align-items: center;
}
.faq-video__image .faq-video__image-overlay svg {
  width: 30px;
  height: 30px;
}

.faq-video__text {
  margin-top: 0.5em;
}

.section.section--use-padding {
  margin: 0;
  padding: var(--vertical-breather) 0;
}
.section.section--half-padding {
  --vertical-breather: calc(var(--vertical-breather) / 2);
}
.section.section--extra-padding {
  --vertical-breather: var(--vertical-breather-large);
}
.section.section--no-padding {
  margin: 0;
  padding: 0;
}
.section.section--show-shadow {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1) inset;
}
.section.section--contain-and-clip {
  position: relative;
  overflow: hidden;
}
.section.section--inner-shadow .section__shadow-wrapper {
  box-shadow: inset 0px 0px 10px rgba(0, 0, 0, 0.1);
}
@media only screen and (max-width: 740px) {
  .section .container--no-horizontal-padding--mobile {
    padding-left: 0;
    padding-right: 0;
  }
}
.section .section__header,
.section .section__footer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1em;
}
.section .section__header > *,
.section .section__footer > * {
  margin: 0 !important;
}
.section .text-container .subheading,
.section .text-container .heading--small,
.section .text-container .tolstoy-stories .tolstoy-stories-title,
.tolstoy-stories .section .text-container .tolstoy-stories-title {
  --heading-color: var(---color-heading-3--rgb);
}
.section .horizontal-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--vertical-breather-tight);
}
@media only screen and (max-width: 740px) {
  .section .horizontal-header {
    padding-bottom: 20px;
    border-bottom: 1px solid var(---color-line);
  }
}
.section .horizontal-header.horizontal-header--tabs {
  border-bottom: 1px solid var(---color-line);
  padding-bottom: 0;
}
.section .horizontal-header.horizontal-header--tabs .tabs-nav {
  margin-bottom: 0;
  margin-top: auto;
}
.section .horizontal-header.horizontal-header--tabs .tabs-nav__item-list {
  box-shadow: none;
}
.section .horizontal-header.horizontal-header--tabs .text-container .h3 {
  margin-bottom: 10px !important;
}
@media only screen and (max-width: 740px) {
  .section .horizontal-header.horizontal-header--tabs {
    flex-direction: column;
    border-bottom: 0;
  }
  .section .horizontal-header.horizontal-header--tabs .heading, .section .horizontal-header.horizontal-header--tabs .tolstoy-stories .tolstoy-stories-title, .tolstoy-stories .section .horizontal-header.horizontal-header--tabs .tolstoy-stories-title {
    text-align: center;
  }
  .section .horizontal-header.horizontal-header--tabs .tabs-nav {
    margin-top: 20px;
  }
  .section .horizontal-header.horizontal-header--tabs .tabs-nav--edge2edge {
    border-bottom: 1px solid var(---color-line);
  }
}
.section .horizontal-header.horizontal-header--equal {
  display: grid;
  align-items: flex-start;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 50px;
  width: 100%;
}
.section .horizontal-header.horizontal-header--equal > * {
  width: 100%;
}
@media (max-width: 1000px) {
  .section .horizontal-header.horizontal-header--equal {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
}

.section-header-images {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
}
.section-header-images .section-header-image {
  margin: 0;
}
.section-header-images img {
  max-width: 200px;
}

.section-footer-images {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
}
.section-footer-images .section-footer-image {
  margin: 0;
}
@media only screen and (max-width: 740px) {
  .section-footer-images .section-footer-image {
    max-width: 30vw;
  }
}

.section--show-texture {
  position: relative;
}
.section--show-texture:before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(---pattern-wood);
  opacity: 0.15;
}

.section--texture .section__color-wrapper {
  background-blend-mode: multiply;
}
.section--texture .section__color-wrapper:after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: var(---pattern-wood);
  opacity: 0.15;
  z-index: 0;
}
.section--texture .section__color-wrapper .container {
  position: relative;
  z-index: 1;
}

.section--image-border .image-with-text__image {
  border: 5px solid #fff;
  box-shadow: 0 0 10px 5px rgba(0, 0, 0, 0.25);
}

.image-with-text-block .button-wrapper {
  margin-top: 20px;
}

.vertical-breather.vertical-breather--extra-tight {
  padding-top: var(--container-gutter);
  padding-bottom: var(--container-gutter);
}

/* ----- Content Boxes ----- */
@media only screen and (max-width: 1000px) {
  .content-box--larger {
    margin-left: 24px;
    margin-right: 24px;
  }
}
@media only screen and (min-width: 1001px) {
  .content-box--larger {
    width: calc(var(--grid-column-width) * 17 + var(--grid-gap) * 16);
  }
}
@media only screen and (min-width: 1201px) {
  .content-box--larger {
    width: calc(var(--grid-column-width) * 17 + var(--grid-gap) * 16);
  }
}
/* 7. Page-Specific Styles */
body.template-collection {
  background: #fff;
}

[dir=ltr] .icon-text svg,
[dir=ltr] .icon-text img {
  margin-right: 8px;
}

body.template-product {
  /* ----- Product Gallery ----- */
  /* ----- Trust Buttons ----- */
  /* ----- Inventory ----- */
}
body.template-product .product {
  margin-bottom: var(--vertical-breather);
}
@media only screen and (min-width: 1401px) {
  body.template-product .product__media {
    --product-media-width: calc(var(--grid-column-width) * 10 + var(--grid-gap) * 10);
  }
}
body.template-product .product__info {
  --vertical-gap: 20px;
}
body.template-product .product__info .product-content {
  display: block;
}
body.template-product .product__info .product-meta__label-list {
  display: flex;
  justify-content: space-between;
}
@media only screen and (min-width: 1401px) {
  body.template-product .product__info {
    --product-info-width: calc(var(--grid-column-width) * 7 + var(--grid-gap) * 6);
  }
}
body.template-product .product-meta__title {
  font-family: var(---font-family-body);
  font-weight: var(---font-weight-body--bold);
  margin-top: 0.25em;
  margin-bottom: 0;
  font-size: var(---font-size-product-title--major--mobile);
}
@media (min-width: 1000px) {
  body.template-product .product-meta__title {
    font-size: var(---font-size-product-title--major--desktop);
  }
}
body.template-product .product-meta__short_description p {
  font-weight: 300;
}
body.template-product .product-meta__vendor-link {
  display: inline-flex;
  align-items: center;
  gap: 1em;
}
body.template-product .product-meta__vendor-logo {
  max-height: 60px;
  width: auto;
}
body.template-product .product-meta__share-button-item {
  color: var(---color-text);
}
body.template-product .product-tabs__trust-title {
  color: var(---color-text);
  text-decoration: none;
  margin: 0 !important;
  font-size: var(---font-size-body-small--mobile) !important;
}
@media only screen and (min-width: 741px) {
  body.template-product .product-tabs__trust-title {
    font-size: var(---font-size-body-small--desktop) !important;
  }
}
body.template-product .product-tabs__trust-list:not(:first-child) {
  margin-top: 30px;
}
body.template-product .product-tabs__tab-item-wrapper:last-child {
  border-bottom: 0;
}
body.template-product .product__info .product-meta__label-list {
  margin-left: 0;
  pointer-events: unset;
}
body.template-product .product__info .product-content {
  margin: 20px 0;
}
body.template-product .product-collection-breadcrumbs {
  line-height: 1.2;
}
body.template-product .product__zoom-button {
  color: var(---color--secondary);
  border-color: RGBA(var(---color--secondary--rgb), 0.25);
  background-color: var(---background-color--content-1);
  transition: 0.25s color, 0.25s background-color;
}
body.template-product .product__zoom-button:focus, body.template-product .product__zoom-button:hover {
  color: var(---color-text--reversed);
  background-color: var(---color--secondary);
}
body.template-product .trust-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
body.template-product .trust-button {
  --button-color--1: var(---color--secondary);
  --button-color--1--hover: var(---background-color--default);
  display: flex;
  align-items: center;
  gap: 10px;
}
body.template-product .trust-button:focus .trust-button__icon svg, body.template-product .trust-button:focus-within .trust-button__icon svg, body.template-product .trust-button:hover .trust-button__icon svg {
  color: var(--button-color--1);
}
body.template-product .trust-button:focus .trust-button__text, body.template-product .trust-button:focus-within .trust-button__text, body.template-product .trust-button:hover .trust-button__text {
  color: var(--button-color--1);
}
body.template-product .trust-button:focus .trust-button__icon-right, body.template-product .trust-button:focus-within .trust-button__icon-right, body.template-product .trust-button:hover .trust-button__icon-right {
  transform: translateX(0);
}
body.template-product .trust-button__icon {
  border: 1px solid var(---color-line);
  background: var(--button-color--1--hover);
  border-radius: 100%;
  padding: 4px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: 0.25s background-color;
}
body.template-product .trust-button__icon svg {
  width: 16px;
  height: 16px;
  color: var(---color--primary);
}
body.template-product .trust-button__icon svg * {
  transition: 0.25s color;
}
body.template-product .trust-button__icon-right {
  margin-left: auto;
  color: var(---color--primary);
  transition: transform 0.25s;
  transform: translateX(-50%);
}
body.template-product .trust-button__icon-right svg {
  width: 10px;
  height: 10px;
}
body.template-product .trust-button__text {
  font-size: var(---font-size-body--mobile);
  font-weight: var(---font-weight-body--bolder);
  transition: 0.25s color;
}
@media only screen and (min-width: 741px) {
  body.template-product .trust-button__text {
    font-size: var(---font-size-body--desktop);
  }
}
body.template-product .inventory {
  display: inline-flex;
  align-items: center;
  color: RGB(var(---color-text));
  font-size: var(---font-size-body-small--mobile);
}
@media only screen and (min-width: 1001px) {
  body.template-product .inventory {
    font-size: var(---font-size-body-small--desktop);
  }
}
body.template-product .inventory:before {
  width: 16px;
  height: 16px;
  content: "";
  display: inline-block;
  margin-right: 0.5em;
  background-color: currentColor;
  border-radius: 100%;
  color: RGB(var(--product-in-stock-text-color));
}
body.template-product .inventory.inventory--low:before {
  color: RGB(var(--product-low-stock-text-color));
}
body.template-product .inventory.inventory--sold-out:before {
  color: RGB(var(--product-no-stock-text-color));
}
body.template-product .tog-button {
  margin-left: 25px !important;
}
body.template-product .product-meta {
  --vertical-gap: 10px;
  position: relative;
  display: grid !important;
  grid-auto-flow: row;
  gap: var(--vertical-gap);
  padding-bottom: var(--vertical-gap);
}
body.template-product .product-meta > * {
  margin: 0 !important;
}
body.template-product .product-meta .oke-sr-count {
  white-space: nowrap;
}
body.template-product .product-meta .okeReviews[data-oke-container],
body.template-product .product-meta div.okeReviews {
  font-weight: inherit !important;
  text-underline-offset: 2px;
  text-decoration: underline;
  -webkit-text-decoration-color: rgba(var(--text-color), 0.35);
  text-decoration-color: rgba(var(--text-color), 0.35);
  transition: color 0.2s ease-in-out, -webkit-text-decoration-color 0.2s ease-in-out;
  transition: text-decoration-color 0.2s ease-in-out, color 0.2s ease-in-out;
  transition: text-decoration-color 0.2s ease-in-out, color 0.2s ease-in-out, -webkit-text-decoration-color 0.2s ease-in-out;
}
body.template-product product-payment-terms {
  display: none !important;
}

body.template-cart {
  /* ----- Shipping Bar ----- */
  /* ----- Terms ----- */
}
body.template-cart .shipping-bar .shipping-bar__progress {
  box-shadow: 0 0 0 1px var(---color--tertiary) inset;
}
body.template-cart .cart-body {
  background: var(---background-color--content-1);
  padding: calc(var(--vertical-breather) / 2) 0;
}
body.template-cart .cart-body .page-content {
  margin-bottom: 0;
}
body.template-cart .cart-body .cart__recap {
  background: var(---background-color--tertiary);
}
body.template-cart .terms {
  margin: 12px 0;
  padding: 12px 0;
  border-top: 1px solid #e6e6e6;
  border-bottom: 1px solid #e6e6e6;
}
body.template-cart .terms__checkbox {
  margin-top: 12px;
  display: flex;
  align-items: center;
}
body.template-cart #cart-order-terms {
  flex: 1 0 auto;
}

body.template-article {
  --secondary-background: var(---background-color--content-2--rgb);
  --section-background: var(---background-color--content-1--rgb);
  --section-block-background: var(---background-color--content-1--rgb);
  background: RGB(var(--section-background));
}
body.template-article .article__nav {
  --text-color: var(---color--primary--rgb);
}
body.template-article .article__nav:before {
  content: "";
  display: block;
  position: absolute;
  height: 3px;
  bottom: -1px;
  width: 100%;
  background: currentColor;
  color: RGB(var(--text-color));
  opacity: 0.2;
}
body.template-article .article__nav:after {
  color: RGB(var(--text-color));
}
body.template-article .article__nav .article__nav-item {
  --text-color: var(---color--primary--rgb);
  color: RGB(var(--text-color));
}
body.template-article .article__prev-next {
  background: transparent;
}
body.template-article .article__nav {
  background: var(---background-color--content-1);
  z-index: 2;
}
body.template-article .article__nav-item-label,
body.template-article .article__nav-item-title,
body.template-article .article__reading-time {
  color: var(---color-text);
}
@media screen and (min-width: 1400px) {
  body.template-article .article__info {
    width: 240px;
  }
}
body.template-article .article__upsells .product-item {
  margin-bottom: 30px;
}
body.template-article .article__comment-form .button--primary {
  --button-background: var(---color--primary--rgb);
}
@media only screen and (max-width: 1000px) {
  body.template-article .breadcrumb--floating {
    position: static;
    text-align: center;
  }
  body.template-article .breadcrumb--floating .breadcrumb__list {
    padding: 1.5em 0;
  }
  body.template-article .article__tags,
body.template-article .article__meta {
    margin-bottom: 10px;
    display: grid;
    grid-gap: 4px;
    gap: 6px;
    justify-content: flex-start;
  }
  body.template-article .article__meta-item:before {
    content: none !important;
  }
}

@media screen and (min-width: 1200px) {
  body.template-page-about .section--kyte-baby-family .image-with-text-block {
    min-height: 600px;
  }
  body.template-page-about .section--kyte-baby-family .image-with-text-block__content {
    width: 60%;
  }
}

.template-page-rewards {
  /* ===== Icons ===== */
  /* ----- Earn ----- */
  /* ----- Rewards ----- */
  /* ----- Tiers ----- */
}
.template-page-rewards .page-header__text-wrapper .heading, .template-page-rewards .page-header__text-wrapper .tolstoy-stories .tolstoy-stories-title, .tolstoy-stories .template-page-rewards .page-header__text-wrapper .tolstoy-stories-title,
.template-page-rewards .breadcrumb {
  display: none;
}
.template-page-rewards .lion-integrated-page--guest .lion-header__join-buttons,
.template-page-rewards .lion-integrated-page--guest .lion-header__join-today {
  display: none !important;
}
.template-page-rewards .lion-integrated-page-section {
  margin-bottom: 0 !important;
}
.template-page-rewards .lion-integrated-page-section__heading-text {
  margin-top: 0.5em !important;
  margin-bottom: 0.5em !important;
  color: var(---color-heading--1) !important;
  font-size: var(---font-size-h2--mobile) !important;
}
.template-page-rewards .lion-integrated-page-section__heading-text::before, .template-page-rewards .lion-integrated-page-section__heading-text::after {
  display: none !important;
}
@media only screen and (min-width: 1001px) {
  .template-page-rewards .lion-integrated-page-section__heading-text {
    font-size: var(---font-size-h2--desktop) !important;
  }
}
.template-page-rewards .lion-rules-list,
.template-page-rewards .lion-rewards-list,
.template-page-rewards .lion-tier-overview {
  background-color: var(---color--brand-5) !important;
}
.template-page-rewards .lion-rule-item__content {
  padding: 30px 20px !important;
}
.template-page-rewards .lion-rule-item__title {
  line-height: 1.2 !important;
  margin-bottom: 0.5em !important;
}
.template-page-rewards .lion-rule-item__points,
.template-page-rewards .lion-rule-item__title {
  color: var(---color-text) !important;
}
.template-page-rewards .lion-tier-box__title {
  padding-bottom: 0 !important;
}
.template-page-rewards .lion-tier-box__purchase-rule {
  background-color: var(---color--brand-7) !important;
  border-color: var(---color--brand-7) !important;
  color: var(---color-text) !important;
}
.template-page-rewards .lion-tier-benefits-list {
  padding: 20px 30px !important;
  text-align: left !important;
  margin-bottom: 0 !important;
}
.template-page-rewards .lion-reward-item__links a {
  text-decoration: underline !important;
}
.template-page-rewards .lion-rule-item__icon {
  background-color: transparent !important;
  background-size: 42px;
  -webkit-mask-image: unset !important;
  -mask-image: unset !important;
  -webkit-mask: unset !important;
  mask: unset !important;
}
.template-page-rewards .lion-icon__rule--purchase {
  background-image: url("https://cdn.shopify.com/s/files/1/0019/7106/0847/files/kyte_icon-purchase.png?v=**********") !important;
}
.template-page-rewards .lion-icon__rule--signup {
  background-image: url("https://cdn.shopify.com/s/files/1/0019/7106/0847/files/kyte_icon-account.png?v=**********") !important;
}
.template-page-rewards .lion-icon__rule--birthday {
  background-image: url("https://cdn.shopify.com/s/files/1/0019/7106/0847/files/kyte_icon-birthday.png?v=**********") !important;
}
.template-page-rewards .lion-icon__rule--instagram-follow {
  background-image: url("https://cdn.shopify.com/s/files/1/0019/7106/0847/files/kyte_icon-follow.png?v=**********") !important;
}
.template-page-rewards .lion-icon__rule--referral {
  background-image: url("https://cdn.shopify.com/s/files/1/0019/7106/0847/files/kyte_icon-refer-friends.png?v=**********") !important;
}
.template-page-rewards .lion-icon__rule--yotpo-review {
  background-image: url("https://cdn.shopify.com/s/files/1/0019/7106/0847/files/kyte_icon-write-review.png?v=**********") !important;
}
.template-page-rewards .lion-rule-item--173351 .lion-icon__rule--custom {
  background-image: url("https://cdn.shopify.com/s/files/1/0019/7106/0847/files/kyte_icon-photo-review.png?v=**********") !important;
}
.template-page-rewards .lion-rule-item--173354 .lion-icon__rule--custom {
  background-image: url("https://cdn.shopify.com/s/files/1/0019/7106/0847/files/kyte_icon-video-review.png?v=**********") !important;
}
.template-page-rewards .lion-rule-item--176431 .lion-icon__rule--custom {
  background-image: url("https://cdn.shopify.com/s/files/1/0019/7106/0847/files/kyte_icon-ambassador-post1.png?v=1666041724") !important;
}
.template-page-rewards .lion-rule-item--176450 .lion-icon__rule--custom {
  background-image: url("https://cdn.shopify.com/s/files/1/0019/7106/0847/files/kyte_icon-ambassador-post5.png?v=1666041724") !important;
}
.template-page-rewards .lion-rule-item--176456 .lion-icon__rule--custom {
  background-image: url("https://cdn.shopify.com/s/files/1/0019/7106/0847/files/kyte_icon-ambassador-post10.png?v=1666041724") !important;
}
.template-page-rewards .lion-rule-item--176458 .lion-icon__rule--custom {
  background-image: url("https://cdn.shopify.com/s/files/1/0019/7106/0847/files/kyte_icon-ambassador-post15.png?v=1666041724") !important;
}
.template-page-rewards .lion-rule-item--176459 .lion-icon__rule--custom {
  background-image: url("https://cdn.shopify.com/s/files/1/0019/7106/0847/files/kyte_icon-ambassador-post20.png?v=1666041724") !important;
}
.template-page-rewards .lion-rule-item--176460 .lion-icon__rule--custom {
  background-image: url("https://cdn.shopify.com/s/files/1/0019/7106/0847/files/kyte_icon-story-bonus.png?v=**********") !important;
}
.template-page-rewards .lion-rule-item--177966 .lion-icon__rule--custom {
  background-image: url("https://cdn.shopify.com/s/files/1/0019/7106/0847/files/kyte_icon-tiktok-journey.png?v=**********") !important;
}
.template-page-rewards .lion-reward-item__icon {
  background-color: transparent !important;
  background-size: 42px;
  -webkit-mask-image: unset !important;
  -mask-image: unset !important;
  -webkit-mask: unset !important;
  mask: unset !important;
}
.template-page-rewards .lion-reward-item[data-reward-id="140818"] .lion-reward-item__icon {
  background-image: url("https://cdn.shopify.com/s/files/1/0019/7106/0847/files/kyte_icon-voucher-2.png?v=**********") !important;
}
.template-page-rewards .lion-reward-item[data-reward-id="140819"] .lion-reward-item__icon {
  background-image: url("https://cdn.shopify.com/s/files/1/0019/7106/0847/files/kyte_icon-voucher-2.png?v=**********") !important;
}
.template-page-rewards .lion-reward-item[data-reward-id="140820"] .lion-reward-item__icon {
  background-image: url("https://cdn.shopify.com/s/files/1/0019/7106/0847/files/kyte_icon-voucher-2.png?v=**********") !important;
}
.template-page-rewards .lion-reward-item[data-reward-id="140821"] .lion-reward-item__icon {
  background-image: url("https://cdn.shopify.com/s/files/1/0019/7106/0847/files/kyte_icon-voucher-2.png?v=**********") !important;
}
.template-page-rewards .lion-reward-item[data-reward-id="152543"] .lion-reward-item__icon {
  background-image: url("https://cdn.shopify.com/s/files/1/0019/7106/0847/files/kyte_icon-blanket.png?v=**********") !important;
}
.template-page-rewards .lion-reward-item[data-reward-id="152544"] .lion-reward-item__icon {
  background-image: url("https://cdn.shopify.com/s/files/1/0019/7106/0847/files/kyte_icon-blanket.png?v=**********") !important;
}
.template-page-rewards .lion-reward-item[data-reward-id="152545"] .lion-reward-item__icon {
  background-image: url("https://cdn.shopify.com/s/files/1/0019/7106/0847/files/kyte_icon-blanket.png?v=**********") !important;
}
.template-page-rewards .lion-tier-box .lion-tier-box__position {
  background-color: transparent !important;
  background-size: 42px;
  width: 42px;
  height: 42px;
  -webkit-mask-image: unset !important;
  -mask-image: unset !important;
  -webkit-mask: unset !important;
  mask: unset !important;
  color: transparent;
}
.template-page-rewards .lion-tier-box--0 .lion-tier-box__position {
  background-image: url("https://cdn.shopify.com/s/files/1/0019/7106/0847/files/kyte_icon-bronze.png?v=**********") !important;
}
.template-page-rewards .lion-tier-box--1 .lion-tier-box__position {
  background-image: url("https://cdn.shopify.com/s/files/1/0019/7106/0847/files/kyte_icon-silver.png?v=**********") !important;
}
.template-page-rewards .lion-tier-box--2 .lion-tier-box__position {
  background-image: url("https://cdn.shopify.com/s/files/1/0019/7106/0847/files/kyte_icon-gold.png?v=**********") !important;
}
.template-page-rewards .lion-tier-box--3 .lion-tier-box__position {
  background-image: url("https://cdn.shopify.com/s/files/1/0019/7106/0847/files/kyte_icon-platinum.png?v=**********") !important;
}

body.template-search .mobile-toolbar.is-collapsed {
  margin-top: 0;
}
body.template-search .search-header {
  background: var(---background-color--tertiary);
}
body.template-search .search-header .tabs-nav,
body.template-search .search-header .page-header__text-wrapper {
  margin-bottom: 0;
}
body.template-search .search-header .tabs-nav .tabs-nav__item {
  --heading-color: var(---color--tertiary--rgb);
}
body.template-search .search-header .tabs-nav .tabs-nav__position {
  color: RGB(var(---color--tertiary--rgb));
}
body.template-search .search-body {
  padding: var(--vertical-breather) 0;
}
body.template-search .search-body .product-facet {
  margin-top: 0;
}
body.template-search .search-body.search-body--has-results {
  background: var(---background-color--content-1);
}
body.template-search .search-body.search-body--no-results {
  background: var(---background-color--tertiary);
}
body.template-search .main-search__submit {
  top: 50%;
  transform: translateY(-100%);
}
body.template-search .main-search__input {
  background: var(---input-background);
  border-radius: var(---border-radius--inputs);
}
/* 8. Components */
/* ----- Color Link ----- */
.color-link {
  align-items: center;
  flex: 2 0 0;
  gap: 7px;
}

.color-link__swatch {
  width: 18px;
  height: 18px;
  min-width: 18px;
  min-height: 18px;
  border-radius: 100%;
  background-color: black;
  background: var(--swatch-color);
}

.color-link__title {
  white-space: nowrap;
  font-size: var(---font-size-body-small--desktop) !important;
}

.color-link {
  display: flex;
  gap: 10px;
}

.drawer {
  --root-background: var(---background-color--content-1--rgb);
  box-shadow: var(---shadow--modal);
  /*  ----- Popovers ----- */
}
.drawer[open] > .drawer__overlay {
  opacity: 1;
}
@media only screen and (max-width: 1000px) {
  .drawer .drawer--large {
    max-width: 100vw;
  }
}
.drawer .drawer__overlay {
  background: var(---banner-overlay-background);
}
.drawer .drawer__header {
  padding-top: 20px;
  padding-bottom: 20px;
}
.drawer .drawer__header .drawer__close-button {
  top: calc(50% - 12px);
}
.drawer .drawer__footer {
  border-top: 1px solid var(---color-line);
  box-shadow: none;
  transform: none;
}
.drawer .drawer__content {
  background: var(---background-color--content-1);
}
.drawer .drawer__title {
  font-family: var(---font-family-body);
  font-weight: var(---font-weight-body--bolder);
  font-size: var(---font-size-h5--mobile);
  text-transform: none;
  color: var(---color-heading--2);
  gap: 7px;
}
@media only screen and (min-width: 741px) {
  .drawer .drawer__title {
    font-size: var(---font-size-h5--desktop);
  }
}
.drawer .drawer__title .heading--alternate {
  color: RGB(var(---color--secondary--rgb));
}
.drawer .drawer__title .icon {
  margin: 0;
  color: var(---color--tertiary);
}
.drawer .product-form__add-button {
  --button-background: var(---color--primary--rgb);
}
.drawer .popover .wk-button {
  transition: 0.25s color;
  color: var(---color--primary);
}
.drawer .popover .wk-button .wk-button__label {
  color: var(---color--primary);
  transition: 0.25s color;
}
.drawer .popover .wk-button .wk-button__icon {
  transition: 0.25s color;
}
.drawer .popover .wk-button:focus, .drawer .popover .wk-button:focus-within, .drawer .popover .wk-button:hover {
  color: var(---color-text--reversed-strong);
}
.drawer .popover .wk-button:focus .wk-button__icon,
.drawer .popover .wk-button:focus .wk-button__label, .drawer .popover .wk-button:focus-within .wk-button__icon,
.drawer .popover .wk-button:focus-within .wk-button__label, .drawer .popover .wk-button:hover .wk-button__icon,
.drawer .popover .wk-button:hover .wk-button__label {
  color: var(---color-text--reversed-strong);
}
.drawer .popover .product-form__add-button {
  --button-background: var(---color--primary--rgb);
}
/*  -----------------------------------
    Quick Buy Drawer
    ----------------------------------- */
/*  -----------------------------------
    Drawer Cart
    ----------------------------------- */
.drawer.mini-cart {
  --container-gutter: 30px;
}
.drawer.mini-cart .checkout-button {
  margin: 0;
}
.drawer.mini-cart .mini-cart__recommendations:after {
  content: "";
  right: 0;
  top: 0;
  bottom: 0;
  width: 20px;
  height: 100%;
  position: absolute;
  background: linear-gradient(to right, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.025));
}

.modal {
  --root-background: var(---background-color--content-1--rgb);
  box-shadow: var(---shadow--modal);
}
.modal[open] .modal__overlay {
  opacity: 0.9;
}
.modal .modal__overlay {
  background: var(---banner-overlay-background);
  opacity: 0;
}

.shopify-section--popup {
  --section-block-background: var(---background-color--content-2--rgb);
}
.shopify-section--popup .modal__close-button {
  top: 15px;
  right: 15px;
}
.shopify-section--popup .modal__content {
  max-height: 95vh !important;
}

.newsletter-modal {
  background: RGB(var(--section-block-background));
}
@media only screen and (min-width: 1001px) {
  .newsletter-modal {
    padding: 30px;
  }
}
@media only screen and (min-width: 1001px) {
  .newsletter-modal .newsletter-modal__content {
    padding: 0 0 0 30px;
  }
}
.newsletter-modal .newsletter-modal__content .heading, .newsletter-modal .newsletter-modal__content .tolstoy-stories .tolstoy-stories-title, .tolstoy-stories .newsletter-modal .newsletter-modal__content .tolstoy-stories-title {
  margin-top: 0;
  margin-bottom: 12px;
}
.newsletter-modal .newsletter-modal__content,
.newsletter-modal .newsletter-modal__image {
  max-width: 400px;
  min-width: 400px;
}
.newsletter-modal .newsletter-modal__form {
  margin-bottom: 20px;
}
.newsletter-modal .newsletter-modal__image-content-wrapper {
  max-width: 180px;
  margin: auto;
  display: inline-block;
}
.newsletter-modal .newsletter-modal__image-content-wrapper img {
  margin: 0 !important;
}
@media only screen and (max-width: 1000px) {
  .newsletter-modal .newsletter-modal__image {
    margin: 0;
    max-width: unset;
    max-height: unset;
    min-width: unset;
    display: block;
    border-radius: var(--block-border-radius);
  }
  .newsletter-modal .newsletter-modal__content--extra {
    padding-top: 20px;
    padding-bottom: 20px;
  }
  .newsletter-modal .newsletter-modal__image-wrapper {
    width: calc(100% - 60px);
  }
  .newsletter-modal .newsletter-modal__image {
    border-radius: var(--block-border-radius);
  }
  .newsletter-modal .tiny {
    padding-bottom: 20px;
  }
}

.preorder-modal .preorder-modal__content {
  padding: 30px;
  max-width: 400px;
}

.button, .swym-add-to-wishlist,
.giftreggie-pdp-registry-cta, .shopify-payment-button__button--unbranded, .drawer .popover .wk-button {
  /*

  &.button--tertiary {
  	@include button-style($color-tertiary);
  }

  &.button--success {
  	@include button-style($color-success);
  }

  &.button--warning {
  	@include button-style($color-warning);
  }

  &.button--danger {
  	@include button-style($color-danger);
  }

  &.button--info {
  	@include button-style($color-info);
  }

  */
}
.button:not(.button--text), .swym-add-to-wishlist:not(.button--text),
.giftreggie-pdp-registry-cta:not(.button--text), .shopify-payment-button__button--unbranded:not(.button--text), .drawer .popover .wk-button:not(.button--text) {
  overflow: hidden;
  position: relative;
  display: inline-flex;
  gap: 20px;
  align-items: center;
  justify-content: space-between;
  margin: 0;
  width: auto;
  height: auto;
  min-height: 0;
  max-height: none;
  border: 0;
  font-family: var(---font-family-heading-alternate);
  font-style: var(---font-style-heading-alternate);
  font-weight: var(---font-weight-heading-alternate);
  line-height: 1.2;
  text-align: center;
  text-transform: unset;
  letter-spacing: var(---button-letter-spacing);
  vertical-align: top;
  border-radius: var(--button-border-radius);
  transition: all 0.2s ease-in-out;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-smoothing: antialiased;
  cursor: pointer;
  padding: 1.5em 2.2em;
  margin-top: 0.25em;
  margin-bottom: 0.25em;
  margin-right: 0.5em;
  margin-left: 0;
  font-size: var(---font-size-button--mobile);
}
@media only screen and (min-width: 741px) {
  .button:not(.button--text), .swym-add-to-wishlist:not(.button--text),
.giftreggie-pdp-registry-cta:not(.button--text), .shopify-payment-button__button--unbranded:not(.button--text), .drawer .popover .wk-button:not(.button--text) {
    font-size: var(---font-size-button--desktop);
  }
}
.button:not(.button--text):not(.button--link), .swym-add-to-wishlist:not(.button--text):not(.button--link),
.giftreggie-pdp-registry-cta:not(.button--text):not(.button--link), .shopify-payment-button__button--unbranded:not(.button--text):not(.button--link), .drawer .popover .wk-button:not(.button--text):not(.button--link) {
  min-width: var(---button-min-width);
}
.button:not(.button--text):last-child, .swym-add-to-wishlist:not(.button--text):last-child,
.giftreggie-pdp-registry-cta:not(.button--text):last-child, .shopify-payment-button__button--unbranded:not(.button--text):last-child, .drawer .popover .wk-button:not(.button--text):last-child {
  margin-right: 0 !important;
}
.button:not(.button--text):only-child, .swym-add-to-wishlist:not(.button--text):only-child,
.giftreggie-pdp-registry-cta:not(.button--text):only-child, .shopify-payment-button__button--unbranded:not(.button--text):only-child, .drawer .popover .wk-button:not(.button--text):only-child {
  margin-bottom: 0 !important;
}
@media only screen and (max-width: 1000px) {
  .button:not(.button--text), .swym-add-to-wishlist:not(.button--text),
.giftreggie-pdp-registry-cta:not(.button--text), .shopify-payment-button__button--unbranded:not(.button--text), .drawer .popover .wk-button:not(.button--text) {
    padding: 1em 2em;
  }
}
.button:not(.button--text).button--xtiny, .swym-add-to-wishlist:not(.button--text).button--xtiny,
.giftreggie-pdp-registry-cta:not(.button--text).button--xtiny, .shopify-payment-button__button--unbranded:not(.button--text).button--xtiny, .drawer .popover .wk-button:not(.button--text).button--xtiny {
  padding: 0.8em 1.2em;
  min-width: unset;
  text-transform: none;
  letter-spacing: var(---letter-spacing-body--mobile);
  font-size: var(---font-size-button-small--mobile);
}
@media only screen and (min-width: 1001px) {
  .button:not(.button--text).button--xtiny, .swym-add-to-wishlist:not(.button--text).button--xtiny,
.giftreggie-pdp-registry-cta:not(.button--text).button--xtiny, .shopify-payment-button__button--unbranded:not(.button--text).button--xtiny, .drawer .popover .wk-button:not(.button--text).button--xtiny {
    font-size: var(---font-size-button-large--mobile);
  }
}
.button:not(.button--text).button--tiny, .swym-add-to-wishlist:not(.button--text).button--tiny,
.giftreggie-pdp-registry-cta:not(.button--text).button--tiny, .shopify-payment-button__button--unbranded:not(.button--text).button--tiny, .drawer .popover .wk-button:not(.button--text).button--tiny {
  padding: 0.6em 1em;
  min-width: unset;
  font-size: var(---font-size-button-small--mobile);
}
@media only screen and (min-width: 1001px) {
  .button:not(.button--text).button--tiny, .swym-add-to-wishlist:not(.button--text).button--tiny,
.giftreggie-pdp-registry-cta:not(.button--text).button--tiny, .shopify-payment-button__button--unbranded:not(.button--text).button--tiny, .drawer .popover .wk-button:not(.button--text).button--tiny {
    font-size: var(---font-size-button--desktop);
  }
}
.button:not(.button--text).button--small, .swym-add-to-wishlist:not(.button--text),
.giftreggie-pdp-registry-cta:not(.button--text), .shopify-payment-button__button--unbranded:not(.button--text).button--small, .drawer .popover .wk-button:not(.button--text).button--small, .drawer .popover .wk-button.swym-add-to-wishlist:not(.button--text),
.drawer .popover .wk-button.giftreggie-pdp-registry-cta:not(.button--text) {
  padding: 1.2em 1.8em;
}
.button:not(.button--text).button--xxs, .swym-add-to-wishlist:not(.button--text).button--xxs,
.giftreggie-pdp-registry-cta:not(.button--text).button--xxs, .shopify-payment-button__button--unbranded:not(.button--text).button--xxs, .drawer .popover .wk-button:not(.button--text).button--xxs {
  font-size: var(---font-size-body-small--desktop);
  padding: 0.5em 0.8em;
  min-width: 0;
  letter-spacing: 0;
}
.button:not(.button--text).button--large, .swym-add-to-wishlist:not(.button--text).button--large,
.giftreggie-pdp-registry-cta:not(.button--text).button--large, .shopify-payment-button__button--unbranded:not(.button--text).button--large, .drawer .popover .wk-button:not(.button--text).button--large {
  padding: 2em 2.8em;
}
.button:not(.button--text).button--huge, .swym-add-to-wishlist:not(.button--text).button--huge,
.giftreggie-pdp-registry-cta:not(.button--text).button--huge, .shopify-payment-button__button--unbranded:not(.button--text).button--huge, .drawer .popover .wk-button:not(.button--text).button--huge {
  padding: 1.4em 1.8em;
  font-size: var(---font-size-button-large--mobile);
}
@media only screen and (min-width: 1001px) {
  .button:not(.button--text).button--huge, .swym-add-to-wishlist:not(.button--text).button--huge,
.giftreggie-pdp-registry-cta:not(.button--text).button--huge, .shopify-payment-button__button--unbranded:not(.button--text).button--huge, .drawer .popover .wk-button:not(.button--text).button--huge {
    font-size: var(---font-size-button-large--mobile);
  }
}
.button:not(.button--text).button--full, .swym-add-to-wishlist:not(.button--text).button--full,
.giftreggie-pdp-registry-cta:not(.button--text).button--full, .shopify-payment-button__button--unbranded:not(.button--text), .drawer .popover .wk-button:not(.button--text) {
  display: flex;
  width: 100%;
  margin-left: 0 !important;
  margin-right: 0 !important;
  text-align: center;
  justify-content: center;
}
.button:not(.button--text).button--disabled, .swym-add-to-wishlist:not(.button--text).button--disabled,
.giftreggie-pdp-registry-cta:not(.button--text).button--disabled, .shopify-payment-button__button--unbranded:not(.button--text).button--disabled, .drawer .popover .wk-button:not(.button--text).button--disabled, .button:not(.button--text).disabled, .swym-add-to-wishlist:not(.button--text).disabled,
.giftreggie-pdp-registry-cta:not(.button--text).disabled, .shopify-payment-button__button--unbranded:not(.button--text).disabled, .drawer .popover .wk-button:not(.button--text).disabled, .button:not(.button--text)[disabled], .swym-add-to-wishlist:not(.button--text)[disabled],
.giftreggie-pdp-registry-cta:not(.button--text)[disabled], .shopify-payment-button__button--unbranded:not(.button--text)[disabled], .drawer .popover .wk-button:not(.button--text)[disabled] {
  cursor: not-allowed;
  pointer-events: none;
}
.button:not(.button--text) .loader-button__text, .swym-add-to-wishlist:not(.button--text) .loader-button__text,
.giftreggie-pdp-registry-cta:not(.button--text) .loader-button__text, .shopify-payment-button__button--unbranded:not(.button--text) .loader-button__text, .drawer .popover .wk-button:not(.button--text) .loader-button__text {
  gap: 0.5em;
}
.button:not(.button--text) svg, .swym-add-to-wishlist:not(.button--text) svg,
.giftreggie-pdp-registry-cta:not(.button--text) svg, .shopify-payment-button__button--unbranded:not(.button--text) svg, .drawer .popover .wk-button:not(.button--text) svg {
  position: relative;
  top: -1px;
  max-height: 16px;
  fill: currentColor;
  stroke: currentColor;
}
.button .button--inline-block, .swym-add-to-wishlist .button--inline-block,
.giftreggie-pdp-registry-cta .button--inline-block, .shopify-payment-button__button--unbranded .button--inline-block, .drawer .popover .wk-button .button--inline-block {
  display: inline-block;
}
.button.button--primary, .button--primary.swym-add-to-wishlist,
.button--primary.giftreggie-pdp-registry-cta, .button--primary.shopify-payment-button__button--unbranded, .drawer .popover .button--primary.wk-button {
  background: var(---color--primary);
  color: var(---color-text--reversed);
  box-shadow: 0 0 0 1px var(---color--primary);
}
.button.button--primary.button--disabled, .button--primary.button--disabled.swym-add-to-wishlist,
.button--primary.button--disabled.giftreggie-pdp-registry-cta, .button--primary.button--disabled.shopify-payment-button__button--unbranded, .drawer .popover .button--primary.button--disabled.wk-button, .button.button--primary.disabled, .button--primary.disabled.swym-add-to-wishlist,
.button--primary.disabled.giftreggie-pdp-registry-cta, .button--primary.disabled.shopify-payment-button__button--unbranded, .drawer .popover .button--primary.disabled.wk-button, .button.button--primary[disabled], .button--primary[disabled].swym-add-to-wishlist,
.button--primary[disabled].giftreggie-pdp-registry-cta, .button--primary[disabled].shopify-payment-button__button--unbranded, .drawer .popover .button--primary[disabled].wk-button {
  background: RGB(var(---color--primary--disabled--rgb));
  box-shadow: 0 0 0 1px RGB(var(---color--primary--disabled--rgb));
}
.button.button--primary:not([disabled]):focus, .button--primary.swym-add-to-wishlist:not([disabled]):focus,
.button--primary.giftreggie-pdp-registry-cta:not([disabled]):focus, .button--primary.shopify-payment-button__button--unbranded:not([disabled]):focus, .drawer .popover .button--primary.wk-button:not([disabled]):focus, .button.button--primary:not([disabled]):focus-within, .button--primary.swym-add-to-wishlist:not([disabled]):focus-within,
.button--primary.giftreggie-pdp-registry-cta:not([disabled]):focus-within, .button--primary.shopify-payment-button__button--unbranded:not([disabled]):focus-within, .drawer .popover .button--primary.wk-button:not([disabled]):focus-within, .button.button--primary:not([disabled]):hover, .button--primary.swym-add-to-wishlist:not([disabled]):hover,
.button--primary.giftreggie-pdp-registry-cta:not([disabled]):hover, .button--primary.shopify-payment-button__button--unbranded:not([disabled]):hover, .drawer .popover .button--primary.wk-button:not([disabled]):hover {
  background: var(---color--brand-7);
  color: var(---color-text);
  box-shadow: 0 0 0 1px var(---color--brand-7);
}
.button.button--secondary, .button--secondary.swym-add-to-wishlist,
.button--secondary.giftreggie-pdp-registry-cta, .button--secondary.shopify-payment-button__button--unbranded, .drawer .popover .button--secondary.wk-button {
  background: transparent;
  color: var(---color-text);
  box-shadow: 0 0 0 1px var(---color-text);
}
.button.button--secondary.button--disabled, .button--secondary.button--disabled.swym-add-to-wishlist,
.button--secondary.button--disabled.giftreggie-pdp-registry-cta, .button--secondary.button--disabled.shopify-payment-button__button--unbranded, .drawer .popover .button--secondary.button--disabled.wk-button, .button.button--secondary.disabled, .button--secondary.disabled.swym-add-to-wishlist,
.button--secondary.disabled.giftreggie-pdp-registry-cta, .button--secondary.disabled.shopify-payment-button__button--unbranded, .drawer .popover .button--secondary.disabled.wk-button, .button.button--secondary[disabled], .button--secondary[disabled].swym-add-to-wishlist,
.button--secondary[disabled].giftreggie-pdp-registry-cta, .button--secondary[disabled].shopify-payment-button__button--unbranded, .drawer .popover .button--secondary[disabled].wk-button {
  background: RGB(var(---color--secondary--disabled--rgb));
}
.button.button--secondary:not([disabled]):focus, .button--secondary.swym-add-to-wishlist:not([disabled]):focus,
.button--secondary.giftreggie-pdp-registry-cta:not([disabled]):focus, .button--secondary.shopify-payment-button__button--unbranded:not([disabled]):focus, .drawer .popover .button--secondary.wk-button:not([disabled]):focus, .button.button--secondary:not([disabled]):focus-within, .button--secondary.swym-add-to-wishlist:not([disabled]):focus-within,
.button--secondary.giftreggie-pdp-registry-cta:not([disabled]):focus-within, .button--secondary.shopify-payment-button__button--unbranded:not([disabled]):focus-within, .drawer .popover .button--secondary.wk-button:not([disabled]):focus-within, .button.button--secondary:not([disabled]):hover, .button--secondary.swym-add-to-wishlist:not([disabled]):hover,
.button--secondary.giftreggie-pdp-registry-cta:not([disabled]):hover, .button--secondary.shopify-payment-button__button--unbranded:not([disabled]):hover, .drawer .popover .button--secondary.wk-button:not([disabled]):hover {
  background: var(---color--brand-7);
  color: var(---color-text);
  box-shadow: 0 0 0 1px var(---color--brand-7);
}
.button.button--tertiary, .button--tertiary.swym-add-to-wishlist,
.button--tertiary.giftreggie-pdp-registry-cta, .button--tertiary.shopify-payment-button__button--unbranded, .drawer .popover .button--tertiary.wk-button {
  background: var(---color--brand-3);
  color: var(---color-text);
}
.button.button--tertiary.button--disabled, .button--tertiary.button--disabled.swym-add-to-wishlist,
.button--tertiary.button--disabled.giftreggie-pdp-registry-cta, .button--tertiary.button--disabled.shopify-payment-button__button--unbranded, .drawer .popover .button--tertiary.button--disabled.wk-button, .button.button--tertiary.disabled, .button--tertiary.disabled.swym-add-to-wishlist,
.button--tertiary.disabled.giftreggie-pdp-registry-cta, .button--tertiary.disabled.shopify-payment-button__button--unbranded, .drawer .popover .button--tertiary.disabled.wk-button, .button.button--tertiary[disabled], .button--tertiary[disabled].swym-add-to-wishlist,
.button--tertiary[disabled].giftreggie-pdp-registry-cta, .button--tertiary[disabled].shopify-payment-button__button--unbranded, .drawer .popover .button--tertiary[disabled].wk-button {
  background: RGB(var(---color--tertiary--disabled--rgb));
}
.button.button--tertiary:not([disabled]):focus, .button--tertiary.swym-add-to-wishlist:not([disabled]):focus,
.button--tertiary.giftreggie-pdp-registry-cta:not([disabled]):focus, .button--tertiary.shopify-payment-button__button--unbranded:not([disabled]):focus, .drawer .popover .button--tertiary.wk-button:not([disabled]):focus, .button.button--tertiary:not([disabled]):focus-within, .button--tertiary.swym-add-to-wishlist:not([disabled]):focus-within,
.button--tertiary.giftreggie-pdp-registry-cta:not([disabled]):focus-within, .button--tertiary.shopify-payment-button__button--unbranded:not([disabled]):focus-within, .drawer .popover .button--tertiary.wk-button:not([disabled]):focus-within, .button.button--tertiary:not([disabled]):hover, .button--tertiary.swym-add-to-wishlist:not([disabled]):hover,
.button--tertiary.giftreggie-pdp-registry-cta:not([disabled]):hover, .button--tertiary.shopify-payment-button__button--unbranded:not([disabled]):hover, .drawer .popover .button--tertiary.wk-button:not([disabled]):hover {
  background: var(---color--brand-7);
  color: var(---color-text);
}
.button.button--outline, .button--outline.swym-add-to-wishlist,
.button--outline.giftreggie-pdp-registry-cta, .button--outline.shopify-payment-button__button--unbranded, .drawer .popover .button--outline.wk-button {
  background: transparent;
  color: RGB(var(--button-background));
  box-shadow: 0 0 0 1px RGBA(var(--button-background), 1);
}
.button.button--outline:not([disabled]):focus, .button--outline.swym-add-to-wishlist:not([disabled]):focus,
.button--outline.giftreggie-pdp-registry-cta:not([disabled]):focus, .button--outline.shopify-payment-button__button--unbranded:not([disabled]):focus, .drawer .popover .button--outline.wk-button:not([disabled]):focus, .button.button--outline:not([disabled]):focus-within, .button--outline.swym-add-to-wishlist:not([disabled]):focus-within,
.button--outline.giftreggie-pdp-registry-cta:not([disabled]):focus-within, .button--outline.shopify-payment-button__button--unbranded:not([disabled]):focus-within, .drawer .popover .button--outline.wk-button:not([disabled]):focus-within, .button.button--outline:not([disabled]):hover, .button--outline.swym-add-to-wishlist:not([disabled]):hover,
.button--outline.giftreggie-pdp-registry-cta:not([disabled]):hover, .button--outline.shopify-payment-button__button--unbranded:not([disabled]):hover, .drawer .popover .button--outline.wk-button:not([disabled]):hover {
  background: RGB(var(--button-background));
  color: RGB(var(--button-text-color));
}
.button.button--outline-faint, .button--outline-faint.swym-add-to-wishlist,
.button--outline-faint.giftreggie-pdp-registry-cta, .button--outline-faint.shopify-payment-button__button--unbranded, .drawer .popover .button--outline-faint.wk-button {
  background: transparent;
  color: RGB(var(--button-background));
  box-shadow: 0 0 0 1px RGBA(var(--button-background), 0.1);
}
.button.button--outline-faint:not([disabled]):focus, .button--outline-faint.swym-add-to-wishlist:not([disabled]):focus,
.button--outline-faint.giftreggie-pdp-registry-cta:not([disabled]):focus, .button--outline-faint.shopify-payment-button__button--unbranded:not([disabled]):focus, .drawer .popover .button--outline-faint.wk-button:not([disabled]):focus, .button.button--outline-faint:not([disabled]):focus-within, .button--outline-faint.swym-add-to-wishlist:not([disabled]):focus-within,
.button--outline-faint.giftreggie-pdp-registry-cta:not([disabled]):focus-within, .button--outline-faint.shopify-payment-button__button--unbranded:not([disabled]):focus-within, .drawer .popover .button--outline-faint.wk-button:not([disabled]):focus-within, .button.button--outline-faint:not([disabled]):hover, .button--outline-faint.swym-add-to-wishlist:not([disabled]):hover,
.button--outline-faint.giftreggie-pdp-registry-cta:not([disabled]):hover, .button--outline-faint.shopify-payment-button__button--unbranded:not([disabled]):hover, .drawer .popover .button--outline-faint.wk-button:not([disabled]):hover {
  background: RGB(var(--button-background));
  color: RGB(var(--button-text-color));
}
.button.button--subdued, .button--subdued.swym-add-to-wishlist,
.button--subdued.giftreggie-pdp-registry-cta, .button--subdued.shopify-payment-button__button--unbranded, .drawer .popover .button--subdued.wk-button {
  background: var(---color--brand-7);
  box-shadow: 0 0 0 1px var(---color-line);
}
.button.button--subdued.button--disabled, .button--subdued.button--disabled.swym-add-to-wishlist,
.button--subdued.button--disabled.giftreggie-pdp-registry-cta, .button--subdued.button--disabled.shopify-payment-button__button--unbranded, .drawer .popover .button--subdued.button--disabled.wk-button, .button.button--subdued.disabled, .button--subdued.disabled.swym-add-to-wishlist,
.button--subdued.disabled.giftreggie-pdp-registry-cta, .button--subdued.disabled.shopify-payment-button__button--unbranded, .drawer .popover .button--subdued.disabled.wk-button, .button.button--subdued[disabled], .button--subdued[disabled].swym-add-to-wishlist,
.button--subdued[disabled].giftreggie-pdp-registry-cta, .button--subdued[disabled].shopify-payment-button__button--unbranded, .drawer .popover .button--subdued[disabled].wk-button {
  background: RGB(var(---color--disabled--rgb));
}
.button.button--subdued:not([disabled]):focus, .button--subdued.swym-add-to-wishlist:not([disabled]):focus,
.button--subdued.giftreggie-pdp-registry-cta:not([disabled]):focus, .button--subdued.shopify-payment-button__button--unbranded:not([disabled]):focus, .drawer .popover .button--subdued.wk-button:not([disabled]):focus, .button.button--subdued:not([disabled]):focus-within, .button--subdued.swym-add-to-wishlist:not([disabled]):focus-within,
.button--subdued.giftreggie-pdp-registry-cta:not([disabled]):focus-within, .button--subdued.shopify-payment-button__button--unbranded:not([disabled]):focus-within, .drawer .popover .button--subdued.wk-button:not([disabled]):focus-within, .button.button--subdued:not([disabled]):hover, .button--subdued.swym-add-to-wishlist:not([disabled]):hover,
.button--subdued.giftreggie-pdp-registry-cta:not([disabled]):hover, .button--subdued.shopify-payment-button__button--unbranded:not([disabled]):hover, .drawer .popover .button--subdued.wk-button:not([disabled]):hover {
  background: var(---color--primary);
  color: var(---color-text--reversed);
  box-shadow: 0 0 0 1px var(---color--primary);
}
.button.button--disabled, .button--disabled.swym-add-to-wishlist,
.button--disabled.giftreggie-pdp-registry-cta, .button--disabled.shopify-payment-button__button--unbranded, .drawer .popover .button--disabled.wk-button {
  cursor: default;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.button .button--outline, .swym-add-to-wishlist .button--outline,
.giftreggie-pdp-registry-cta .button--outline, .shopify-payment-button__button--unbranded .button--outline, .drawer .popover .wk-button .button--outline {
  border: 1px solid var(---color-line) !important;
  background: transparent;
}
.button.button--link, .button--link.swym-add-to-wishlist,
.button--link.giftreggie-pdp-registry-cta, .button--link.shopify-payment-button__button--unbranded, .drawer .popover .button--link.wk-button {
  padding: 0;
  margin: 0;
}
.button.button--inverted, .button--inverted.swym-add-to-wishlist,
.button--inverted.giftreggie-pdp-registry-cta, .button--inverted.shopify-payment-button__button--unbranded, .drawer .popover .wk-button {
  background-color: transparent;
  --button-text-color: var(--button--background);
  box-shadow: 0 0 0 1px RGBA(var(--button--background), 1) inset;
}
.button.button--inverted:not([disabled]), .button--inverted.swym-add-to-wishlist:not([disabled]),
.button--inverted.giftreggie-pdp-registry-cta:not([disabled]), .button--inverted.shopify-payment-button__button--unbranded:not([disabled]), .drawer .popover .wk-button:not([disabled]) {
  background-image: linear-gradient(178deg, rgba(var(--button--background), 0), rgba(var(--button--background), 0) 10%, rgba(var(--button--background), 1) 10%, rgba(var(--button--background), 1) 100%), linear-gradient(rgba(var(--button--background), 0), rgba(var(--button--background), 0));
  --button-text-color: var(--button--background);
  background-size: 100% 200%, 100% 100%;
  background-position: 100% -100%, 100% 100%;
  background-repeat: no-repeat;
  transition: background-position 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  transform: translateZ(0); /* Make sure to promote the button on its own layer */
}
.button.button--inverted:not([disabled]):focus, .button--inverted.swym-add-to-wishlist:not([disabled]):focus,
.button--inverted.giftreggie-pdp-registry-cta:not([disabled]):focus, .button--inverted.shopify-payment-button__button--unbranded:not([disabled]):focus, .drawer .popover .wk-button:not([disabled]):focus, .button.button--inverted:not([disabled]):focus-within, .button--inverted.swym-add-to-wishlist:not([disabled]):focus-within,
.button--inverted.giftreggie-pdp-registry-cta:not([disabled]):focus-within, .button--inverted.shopify-payment-button__button--unbranded:not([disabled]):focus-within, .drawer .popover .wk-button:not([disabled]):focus-within, .button.button--inverted:not([disabled]):hover, .button--inverted.swym-add-to-wishlist:not([disabled]):hover,
.button--inverted.giftreggie-pdp-registry-cta:not([disabled]):hover, .button--inverted.shopify-payment-button__button--unbranded:not([disabled]):hover, .drawer .popover .wk-button:not([disabled]):hover {
  --button-text-color: var(---color-text--reversed--rgb);
  background-position: 100% 25%, 100% 100%;
}
.button.button--inverted:not([disabled]):active, .button--inverted.swym-add-to-wishlist:not([disabled]):active,
.button--inverted.giftreggie-pdp-registry-cta:not([disabled]):active, .button--inverted.shopify-payment-button__button--unbranded:not([disabled]):active, .drawer .popover .wk-button:not([disabled]):active {
  background-position: 100% 25%, 100% 100%;
}
.button.button--light, .button--light.swym-add-to-wishlist,
.button--light.giftreggie-pdp-registry-cta, .button--light.shopify-payment-button__button--unbranded, .drawer .popover .button--light.wk-button {
  --button-background: var(--master-color-background--rgb);
  --button-text-color: var(--button--background);
}
.button.button--light:not([disabled]), .button--light.swym-add-to-wishlist:not([disabled]),
.button--light.giftreggie-pdp-registry-cta:not([disabled]), .button--light.shopify-payment-button__button--unbranded:not([disabled]), .drawer .popover .button--light.wk-button:not([disabled]) {
  background-image: linear-gradient(178deg, rgb(var(--button-background)), rgb(var(--button-background)) 10%, rgba(var(--button-background--hover), 0.35) 10%, rgba(var(--button-background--hover), 0.35) 100%), linear-gradient(rgb(var(--button-background)), rgb(var(--button-background)));
  background-size: 100% 200%, 100% 100%;
  background-position: 100% -100%, 100% 100%;
  background-repeat: no-repeat;
  transition: background-position 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  transform: translateZ(0); /* Make sure to promote the button on its own layer */
}
.button.button--light:not([disabled]):active, .button--light.swym-add-to-wishlist:not([disabled]):active,
.button--light.giftreggie-pdp-registry-cta:not([disabled]):active, .button--light.shopify-payment-button__button--unbranded:not([disabled]):active, .drawer .popover .button--light.wk-button:not([disabled]):active, .button.button--light:not([disabled]):focus, .button--light.swym-add-to-wishlist:not([disabled]):focus,
.button--light.giftreggie-pdp-registry-cta:not([disabled]):focus, .button--light.shopify-payment-button__button--unbranded:not([disabled]):focus, .drawer .popover .button--light.wk-button:not([disabled]):focus, .button.button--light:not([disabled]):focus-within, .button--light.swym-add-to-wishlist:not([disabled]):focus-within,
.button--light.giftreggie-pdp-registry-cta:not([disabled]):focus-within, .button--light.shopify-payment-button__button--unbranded:not([disabled]):focus-within, .drawer .popover .button--light.wk-button:not([disabled]):focus-within, .button.button--light:not([disabled]):hover, .button--light.swym-add-to-wishlist:not([disabled]):hover,
.button--light.giftreggie-pdp-registry-cta:not([disabled]):hover, .button--light.shopify-payment-button__button--unbranded:not([disabled]):hover, .drawer .popover .button--light.wk-button:not([disabled]):hover {
  background-position: 100% 25%, 100% 100%;
}
.button .button__icon svg, .swym-add-to-wishlist .button__icon svg,
.giftreggie-pdp-registry-cta .button__icon svg, .shopify-payment-button__button--unbranded .button__icon svg, .drawer .popover .wk-button .button__icon svg {
  fill: currentColor;
  stroke: currentColor;
}
.button .button__icon:only-child(), .swym-add-to-wishlist .button__icon:only-child(),
.giftreggie-pdp-registry-cta .button__icon:only-child(), .shopify-payment-button__button--unbranded .button__icon:only-child(), .drawer .popover .wk-button .button__icon:only-child() {
  margin: 0;
}
.input-row {
  display: flex;
}
.input-row .input .button, .input-row .input .swym-add-to-wishlist,
.input-row .input .giftreggie-pdp-registry-cta, .input-row .input .drawer .popover .wk-button, .drawer .popover .input-row .input .wk-button, .input-row .input .shopify-payment-button__button--unbranded {
  margin: 0;
  padding: 1em 1.6em;
}

.pagination .pagination__nav-item {
  font-weight: var(---font-weight-body--bold);
}
.pagination .pagination__nav-arrow {
  background: var(---color--kyte-light-grey);
}
.pagination .pagination__nav-item[aria-current]:before {
  box-shadow: 0 0 0 1px currentColor;
}

.breadcrumb__list {
  padding-top: 0;
}

.breadcrumb {
  font-size: var(---font-size-body-xs--desktop);
  text-align: left;
}
.breadcrumb a {
  color: var(---color-link);
}

.collapsible-toggle svg {
  height: 16px;
  width: 16px;
  background-repeat: no-repeat;
  background-position: center;
}
.collapsible-toggle svg * {
  fill: transparent;
  stroke: transparent;
}
.collapsible-toggle[aria-expanded=false] svg {
  background-image: url("data:image/svg+xml,%3Csvg width='12' height='12' viewBox='0 0 12 12' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11.9953 5.94871C12.0245 6.57531 11.5218 7.08897 10.9024 7.08897H7.75855C7.3906 7.08897 7.08824 7.38769 7.08824 7.75928V10.9032C7.08824 11.5225 6.57093 12.0216 5.94798 11.9961C5.32503 11.9706 4.90244 11.4314 4.90244 10.834V7.75928C4.90244 7.39134 4.60372 7.08897 4.23213 7.08897H1.09188C0.472571 7.09261 -0.0265184 6.57531 -0.00101751 5.94871C0.0244834 5.32212 0.563646 4.90318 1.1611 4.90318H4.23578C4.60372 4.90318 4.90609 4.60445 4.90609 4.23287V1.09261C4.90609 0.473303 5.41975 -0.025786 6.04634 -0.000285093C6.67294 0.0252158 7.09188 0.564378 7.09188 1.16183V4.23651C7.09188 4.60445 7.3906 4.90682 7.76219 4.90682H10.8369C11.4343 4.90682 11.9735 5.35491 11.999 5.95236L11.9953 5.94871Z' fill='black'/%3E%3C/svg%3E%0A");
}
.collapsible-toggle[aria-expanded=true] svg {
  background-image: url("data:image/svg+xml,%3Csvg width='12' height='2' viewBox='0 0 12 2' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10.9071 0H1.0929C0.48816 0 0 0.446667 0 1C0 1.55333 0.48816 2 1.0929 2H10.9071C11.5118 2 12 1.55333 12 1C12 0.446667 11.5118 0 10.9071 0Z' fill='black'/%3E%3C/svg%3E%0A");
}

.tabs-nav .tabs-nav__item-list {
  box-shadow: 0 -2px RGB(var(--border-color)) inset;
}
.tabs-nav .tabs-nav__item {
  --heading-color: var(---color--default);
  opacity: 1;
  transition: color 0.25s;
  color: var(---color-products-title);
  font-weight: var(---font-weight--product-title);
  font-size: var(---font-size-product-title--mobile);
}
@media only screen and (min-width: 741px) {
  .tabs-nav .tabs-nav__item {
    font-size: var(---font-size-product-title--desktop);
  }
}
.tabs-nav .tabs-nav__item[aria-expanded=false] {
  color: var(---color--brand-2);
}
.tabs-nav .tabs-nav__item[aria-expanded=true] {
  --heading-color: var(---color--primary--rgb);
}
.tabs-nav .heading.heading--small, .tabs-nav .tolstoy-stories .tolstoy-stories-title, .tolstoy-stories .tabs-nav .tolstoy-stories-title {
  text-transform: unset;
  letter-spacing: 0.025em;
}
.tabs-nav .tabs-nav__item-list {
  gap: 30px;
}
.tabs-nav .tabs-nav__position {
  color: var(---color--primary);
}
.tabs-nav.tabs-nav--no-border.tabs-nav--narrow .tabs-nav__item {
  padding-bottom: 10px;
}

@media only screen and (max-width: 1200px) {
  product-list .product-list__inner--scroller,
.product-list .product-list__inner--scroller {
    grid-auto-columns: 65vw;
  }
}

.product-form {
  gap: var(--vertical-gap);
  /* ----- BIS ----- */
  /* ----- Inventory ----- */
  /* ----- Quantity Selector + ATC ----- */
  /* ----- Registry + Wish List Buttons ----- */
  /*  ------------------------------
      Customer Tier Banner
      ------------------------------ */
  /*  ----- Product Form Banner ----- */
}
.product-form .note {
  margin-top: 0;
  margin-bottom: 0;
}
.product-form .product-form__quantity-label {
  font-size: var(---font-size-body-small--desktop);
  font-weight: var(---font-weight-body--bold);
}
@media only screen and (min-width: 741px) {
  .product-form .product-form__payment-container {
    margin-top: 0;
  }
}
.product-form .product-form__text {
  text-align: center;
}
.product-form .product-form__option-selector + .product-form__option-selector {
  margin: 0;
}
.product-form .product-form__option-info {
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-size: var(---font-size-subheading-small);
  color: var(---color-text--dark);
}
.product-form .product-form__option-links {
  margin-left: auto;
}
.product-form .product-form__inventory-wrapper,
.product-form .product-form__option-info {
  margin: 0px 0 10px 0;
}
.product-form .product-form__inventory-wrapper:first-child,
.product-form .product-form__option-info:first-child {
  margin-top: 0;
}
.product-form .product-form__inventory-wrapper {
  display: block;
  margin-bottom: 0;
}
.product-form .product-form__text p {
  font-weight: 300;
}
.product-form #BIS_trigger {
  margin: 0;
}
.product-form #BIS_trigger:not([style="display: none;"]) + product-payment-container {
  display: none !important;
}
.product-form .product-form__inventory-block {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  line-height: 1;
}
.product-form .product-form__inventory-block .delivery-estimate__button {
  line-height: 1;
}
.product-form .product-form__inventory-block .delivery-estimate__tooltip {
  display: none;
}
.product-form .product-form__inventory-block .tippy-content .delivery-estimate__tooltip {
  display: block;
}
.product-form .quantity-atc {
  display: grid;
  grid-auto-flow: row;
  gap: var(--vertical-gap);
}
.product-form .product-form__wishlist-buttons {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: stretch;
  gap: var(--vertical-gap);
}
.product-form .product-form__wishlist-buttons .giftreggie-pdp-registry-cta,
.product-form .product-form__wishlist-buttons .swym-wishlist-cta {
  font-weight: 400;
  letter-spacing: 0;
  -webkit-text-decoration-color: rgba(var(--text-color), 0.35);
  text-decoration-color: rgba(var(--text-color), 0.35);
  transition: color 0.2s ease-in-out, -webkit-text-decoration-color 0.2s ease-in-out;
  transition: text-decoration-color 0.2s ease-in-out, color 0.2s ease-in-out;
  transition: text-decoration-color 0.2s ease-in-out, color 0.2s ease-in-out, -webkit-text-decoration-color 0.2s ease-in-out;
  font-size: var(---font-size-body--mobile);
}
@media only screen and (min-width: 1001px) {
  .product-form .product-form__wishlist-buttons .giftreggie-pdp-registry-cta,
.product-form .product-form__wishlist-buttons .swym-wishlist-cta {
    font-size: var(---font-size-body--desktop);
  }
}
.product-form .product-form__wishlist-buttons .giftreggie-pdp-registry-cta:hover, .product-form .product-form__wishlist-buttons .giftreggie-pdp-registry-cta:focus,
.product-form .product-form__wishlist-buttons .swym-wishlist-cta:hover,
.product-form .product-form__wishlist-buttons .swym-wishlist-cta:focus {
  opacity: 0.5;
}
.product-form .product-form__wishlist-buttons .giftreggie-pdp-registry-cta {
  margin: 0;
  gap: 10px;
  padding: 1.2em 1.8em !important;
}
@media only screen and (max-width: 740px) {
  .product-form .product-form__wishlist-buttons .giftreggie-pdp-registry-cta {
    padding: 1.2em 0 !important;
  }
}
.product-form .product-form__wishlist-buttons .giftreggie-pdp-registry-cta:before {
  display: block;
  content: "";
  width: 22px;
  height: 22px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none'%3E%3Cg clip-path='url(%23clip0_2663_1768)'%3E%3Cpath d='M20.6676 1.35046C19.506 0.183167 17.8661 6.10352e-05 15.9301 6.10352e-05H7.0472C5.13396 6.10352e-05 3.49404 0.183167 2.33244 1.35046C1.17079 2.51775 1 4.15428 1 6.06547V14.9004C1 16.8573 1.17079 18.4824 2.33244 19.6497C3.49404 20.817 5.13396 21.0001 7.08135 21.0001H15.9301C17.8661 21.0001 19.506 20.817 20.6676 19.6497C21.8293 18.4824 22 16.8573 22 14.9004V6.0998C22 4.14285 21.8293 2.50632 20.6676 1.35046ZM2.8335 5.81366C2.8335 4.61205 2.98156 3.3761 3.66488 2.68943C4.35957 1.99133 5.60086 1.8426 6.79666 1.8426H10.6801V6.03114C9.92845 4.56629 8.64154 3.66219 7.20662 3.66219C5.57812 3.66219 4.34816 4.88671 4.34816 6.55756C4.34816 7.99951 5.3617 9.1897 6.79666 9.842H2.8335V5.81366ZM20.1665 5.77934V9.842H15.9301C17.365 9.1897 18.3786 7.99951 18.3786 6.55756C18.3786 4.88671 17.1487 3.66219 15.5087 3.66219C14.0852 3.66219 12.7983 4.56629 12.0467 6.03114V1.8426H16.2375C17.4106 1.8426 18.6405 2.0028 19.3352 2.68943C20.0185 3.38753 20.1665 4.60062 20.1665 5.77934ZM10.122 9.67037C7.87853 9.67037 5.95392 8.12539 5.95392 6.68344C5.95392 5.81366 6.47777 5.28724 7.29773 5.28724C8.98322 5.28724 10.4637 7.17553 10.4637 9.34991V9.67037H10.122ZM12.6047 9.67037H12.2631V9.34991C12.2631 7.17553 13.7321 5.28724 15.4176 5.28724C16.249 5.28724 16.7728 5.81366 16.7728 6.68344C16.7728 8.12539 14.8482 9.67037 12.6047 9.67037ZM16.2375 19.1576H12.0467V11.902H12.1378C12.8894 14.2595 15.7023 16.3194 17.4561 16.5826C18.1053 16.6742 18.5038 16.205 18.5038 15.7014C18.5038 15.1521 18.1736 14.7859 17.5814 14.7287C16.0098 14.557 13.8119 12.497 13.1513 11.1009H20.1665V15.2093C20.1665 16.3995 20.0185 17.6126 19.3352 18.3107C18.6405 18.9974 17.4106 19.1576 16.2375 19.1576ZM6.76246 19.1576C5.58949 19.1576 4.35957 18.9974 3.67625 18.3107C2.98156 17.6126 2.8335 16.3995 2.8335 15.2093V11.1009H9.57539C8.91486 12.497 6.71693 14.557 5.14533 14.7287C4.55316 14.7859 4.22289 15.1521 4.22289 15.7014C4.22289 16.205 4.62148 16.6742 5.27059 16.5826C7.02441 16.3194 9.83734 14.2595 10.589 11.902H10.6801V19.1576H6.76246Z' fill='black' /%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='clip0_2663_1768'%3E%3Crect width='24' height='24' fill='white' /%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E");
}
.product-form .product-form__wishlist-buttons .giftreggie-pdp-cta-area {
  margin: 0;
}
.product-form .product-form__wishlist-buttons .swym-details-container {
  letter-spacing: 0 !important;
}
.product-form .product-form__wishlist-buttons .swym-details-container .swym-emphasis {
  letter-spacing: 0 !important;
}
.product-form .product-form__wishlist-buttons .swym-wishlist-button-bar {
  display: flex;
  align-items: stretch;
  justify-content: center;
}
.product-form .product-form__wishlist-buttons .swym-btn-container {
  display: flex;
  align-items: stretch;
}
.product-form .product-form__wishlist-buttons .swym-add-to-wishlist {
  align-items: center;
  display: flex !important;
  gap: 10px !important;
  height: 100%;
  padding: 1.2em 1.8em !important;
}
@media only screen and (max-width: 740px) {
  .product-form .product-form__wishlist-buttons .swym-add-to-wishlist {
    padding: 1.2em 0 !important;
  }
}
.product-form .product-form__wishlist-buttons .swym-add-to-wishlist.swym-iconbtnlink {
  height: auto !important;
}
.product-form .product-form__wishlist-buttons .swym-add-to-wishlist:after {
  position: relative;
  width: 18px !important;
  height: 18px !important;
  order: 0;
}
.product-form .product-form__wishlist-buttons .swym-add-to-wishlist .swym-wishlist-cta {
  text-indent: 0;
  order: 1;
}
.product-form product-payment-container .button, .product-form product-payment-container .swym-add-to-wishlist,
.product-form product-payment-container .giftreggie-pdp-registry-cta, .product-form product-payment-container .drawer .popover .wk-button, .drawer .popover .product-form product-payment-container .wk-button, .product-form product-payment-container .shopify-payment-button__button--unbranded {
  margin: 0 !important;
}
.product-form product-payment-container .product-form__add-button .loader-button__label {
  flex-grow: 1;
}
.product-form product-payment-container .product-form__add-button .loader-button__text .loader-button__label {
  white-space: nowrap;
}
.product-form product-payment-container .product-form__add-button .product-item__prices-wrapper {
  display: flex;
  flex-wrap: wrap;
}
.product-form product-payment-container .product-item__prices-wrapper {
  white-space: nowrap;
  display: flex;
  gap: 0.25em;
}
.product-form product-payment-container .regular-price-container {
  opacity: 0.5 !important;
  text-decoration: line-through !important;
}
.product-form .shopify-payment-button__button {
  width: 100% !important;
}
.product-form .tier-welcome-message-banner__icon {
  mix-blend-mode: multiply;
}
.product-form .product-form-banner {
  display: flex;
  gap: 15px;
  align-items: flex-start;
  border: 1px solid var(---color-line);
  padding: 20px;
  background: RGB(var(--secondary-background));
  border-radius: var(--block-border-radius);
}
.product-form .product-form-banner.product-form-banner--small {
  padding: 10px;
}
.product-form .product-form-banner.product-form-banner--color {
  border-color: var(--banner-color);
  background: var(--banner-color);
}
.product-form .product-form-banner.product-form-banner--danger {
  background: var(---background-color--danger);
}
.product-form .sale-stop-banner .product-form-banner__icon {
  width: 50px;
  max-width: 50px;
  flex: 1 0 50px;
}
.product-form .sale-stop-banner .product-form-banner__icon img {
  mix-blend-mode: multiply;
}
.product-form .product-form-banner__icon {
  display: inline-flex;
  max-width: 50px;
}
.product-form .product-form-banner__icon img {
  flex-basis: auto;
  width: revert-layer;
}
.product-form .product-form-banner__text .heading, .product-form .product-form-banner__text .tolstoy-stories .tolstoy-stories-title, .tolstoy-stories .product-form .product-form-banner__text .tolstoy-stories-title {
  margin-bottom: 0.25em !important;
}
.product-form .product-form-banner__heading {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}
.product-form .product-form-banner__actions {
  margin-top: 0.75em;
}
.product-form .product-form-accordion .product-form-accordion__title {
  -webkit-margin-end: auto;
          margin-inline-end: auto;
}
.product-form .product-form-accordion .product-form-accordion__toggle {
  border-bottom: 1px solid var(---color-line);
  gap: 10px;
  padding: 10px 0;
}
.product-form .product-form-accordion .product-form-accordion__content-inner {
  padding-block: 18px;
}
.product-form .product-form-accordion .product-form-accordion__icon img, .product-form .product-form-accordion .product-form-accordion__icon svg {
  max-width: 40px;
  width: 40px;
  height: 40px;
}

/*  ------------------------------------ 
	Color Swatches
	------------------------------------ */
.color-swatch-list {
  justify-content: flex-start;
}

color-swatch-list {
  display: block;
}
color-swatch-list.color-swatch-list--expandable {
  position: relative;
}
color-swatch-list.color-swatch-list--expandable.color-swatch-list--collapsed {
  overflow: hidden;
  max-height: 140px;
}
color-swatch-list.color-swatch-list--expandable.color-swatch-list--collapsed:after {
  background: linear-gradient(0deg, #fff, transparent);
}
color-swatch-list.color-swatch-list--expandable.color-swatch-list--collapsed .color-swatch:before, color-swatch-list.color-swatch-list--expandable.color-swatch-list--collapsed .color-swatch:after {
  display: none !important;
}
color-swatch-list.color-swatch-list--expandable .button.color-swatch-list__button-show-all, color-swatch-list.color-swatch-list--expandable .color-swatch-list__button-show-all.swym-add-to-wishlist,
color-swatch-list.color-swatch-list--expandable .color-swatch-list__button-show-all.giftreggie-pdp-registry-cta, color-swatch-list.color-swatch-list--expandable .drawer .popover .color-swatch-list__button-show-all.wk-button, .drawer .popover color-swatch-list.color-swatch-list--expandable .color-swatch-list__button-show-all.wk-button, color-swatch-list.color-swatch-list--expandable .color-swatch-list__button-show-all.shopify-payment-button__button--unbranded {
  position: absolute;
  bottom: 0px;
  left: 0;
  right: 0;
  max-width: -webkit-max-content;
  max-width: -moz-max-content;
  max-width: max-content;
  margin: auto !important;
  z-index: 1;
}
color-swatch-list.color-swatch-list--expandable:after {
  pointer-events: none;
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 50px;
}

.color-swatch.color-swatch--active .color-swatch__item::after {
  opacity: 1;
  transform: scale(1);
}
.color-swatch .color-swatch__item {
  border: 2px solid var(---color-line);
  background-size: contain;
}

/*  ------------------------------------ 
	Block Swatches
	------------------------------------ */
.block-swatch-list.block-swatch-list--small .block-swatch {
  padding: 0;
}
.block-swatch-list.block-swatch-list--small .block-swatch__item {
  min-height: 45px;
  padding: 0 1em;
  color: var(---color-text);
}

.block-swatch {
  --border-radius: var(--block-border-radius);
  --font-size: var(--font-size-sm);
  position: relative;
}
.block-swatch .block-swatch__item {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 50px;
  min-height: 50px;
  padding: 0.4em 1em;
  font-size: var(--font-size);
  border-radius: var(--border-radius);
}
.block-swatch .block-swatch__item:after {
  border-radius: var(--border-radius);
}
.block-swatch.is-disabled {
  --text-color: var(---color-text--rgb);
}
.block-swatch.is-disabled .block-swatch__item {
  background-color: transparent;
}
.block-swatch:not(.is-disabled) .block-swatch__item {
  border: 0;
  outline: 1px solid RGBA(var(--text-color), 0.25);
  box-shadow: inset 0 0 0 2px RGBA(var(--text-color) 0.25);
  background-color: RGB(var(--secondary-background));
}
.block-swatch:not(.is-disabled) .block-swatch__item:after {
  opacity: 0;
  transform: scale(0.8);
  transition: transform 0.2s, opacity 0.1;
}
.block-swatch:not(.is-disabled) .block-swatch__item:focus, .block-swatch:not(.is-disabled) .block-swatch__item:hover {
  background-color: RGB(var(--primary-button-background));
  color: var(---color-text--reversed);
}
.block-swatch:not(.is-disabled) input:checked + .block-swatch__item {
  box-shadow: 0 0 0 2px RGBA(var(--text-color) 0.25);
  background-color: RGB(var(--background));
}
.block-swatch:not(.is-disabled) input:checked + .block-swatch__item:after {
  opacity: 1;
  transform: scale(1);
}
.block-swatch:not(.is-disabled) input:checked + .block-swatch__item:focus, .block-swatch:not(.is-disabled) input:checked + .block-swatch__item:hover {
  color: RGB(var(--text-color));
}
.block-swatch.block-swatch--small {
  --border-radius: var(--block-border-radius-sm);
  --font-size: var(--font-size-xs);
}
.block-swatch.block-swatch--small .block-swatch__item {
  min-width: 40px;
  min-height: 40px;
}

.popover .popover__title {
  margin-bottom: 0;
}

/* ----- Ratings ----- */
.rating .rating__caption,
.rating .rating__star {
  color: RGB(var(--product-star-rating));
}
.rating .rating__caption {
  font-size: var(---font-size-body-xs--desktop);
}

.link-bar {
  box-shadow: none;
  margin-top: 10px;
}
@media only screen and (min-width: 1201px) {
  .link-bar .link-bar__title,
.link-bar .link-bar__link-item {
    padding-top: 20px;
    padding-bottom: 20px;
  }
}
.link-bar .link-bar__wrapper {
  width: 100%;
  justify-content: center;
  border-bottom: 1px solid var(---color-line);
}

.shopify-section--link-bar-nav .link-bar {
  background-color: RGB(var(--background));
  color: RGB(var(--text-color));
}

.tag-list .tag {
  padding: 0.8em 1.2em;
  font-size: var(---font-size-body-small--desktop);
  background: var(---color--brand-3);
  color: var(---color--secondary);
  border-radius: 8px;
}
.tag-list .tag svg {
  width: 16px;
  height: 16px;
}
.tag-list .tag a {
  margin-right: 5px;
}
.tag-list .tag-link {
  --text-color: var(---color-link);
  padding: 0.8em 1.2em;
  color: var(--text-color);
  font-size: var(---font-size-body-small--desktop);
}
.tag-list .tag-link:focus, .tag-list .tag-link:hover {
  --text-color: var(---color-link--hover);
}

/* ----- Range Group ----- */
.range-group {
  --border-color: var(---background-color--primary--rgb);
  --text-color: var(---color--primary--rgb);
}

/* ----- Price Range ----- */
.price-range__delimiter {
  margin: 1em;
}

/* ----- Checkbox ----- */
.checkbox {
  --heading-color: var(---color--primary--rgb);
}

/* ----- Checkbox Container ----- */
.checkbox-container input:not([disabled]) {
  --text-color: var(---color-link--rgb);
}
.checkbox-container input:not([disabled]) + label {
  transition: color 0.25s;
}
.checkbox-container input:not([disabled]) + label:hover {
  --text-color: var(---color-link--rgb);
}
.checkbox-container input[disabled] {
  opacity: 0.25;
}
.checkbox-container input[disabled] + label {
  opacity: 0.25;
}
.checkbox-container .checkbox {
  height: 18px;
  width: 18px;
  background-color: transparent;
  border-radius: 4px;
  border-color: var(---color-line--dark);
}
.checkbox-container .checkbox.checked, .checkbox-container .checkbox:checked {
  background-image: url("data:image/svg+xml,%3Csvg width='12' height='10' viewBox='0 0 12 10' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11.7647 1.50542L4.79326 9.6845C4.63079 9.87499 4.39309 9.99107 4.14035 10C4.12831 10 4.11628 10 4.10123 10C3.86353 10 3.63186 9.90773 3.46336 9.73808L0.264975 6.57122C-0.129182 6.18131 -0.0840494 5.52055 0.403381 5.19613C0.764441 4.95504 1.25789 5.02648 1.56479 5.33304L3.33699 7.08613C3.71911 7.46413 4.34796 7.43734 4.69698 7.02958L10.4198 0.317849C10.7507 -0.0720559 11.3465 -0.110749 11.7286 0.24344C12.0837 0.573818 12.0837 1.13933 11.7677 1.5084L11.7647 1.50542Z' fill='%239A9A9A'/%3E%3C/svg%3E%0A");
}
.checkbox-container label,
.checkbox-container .checkbox-label {
  color: RGB(var(--text-color));
  line-height: 1.2;
  position: relative;
  top: -1px;
}

.price {
  color: var(---color-price);
  font-weight: var(---font-weight--product-price);
  letter-spacing: -0.05em;
  white-space: nowrap;
  font-size: var(---font-size-product-title--mobile);
}
@media only screen and (min-width: 1001px) {
  .price {
    font-size: var(---font-size-product-title--desktop);
  }
}
.price.price--sale {
  color: var(---color-price--sale);
  font-weight: var(---font-weight--product-price--sale);
}
.price.price--compare {
  color: var(---color-price--compare);
  font-weight: var(---font-weight-body);
}
.price.price--highlight {
  font-weight: var(---font-weight-body--bolder);
  color: var(---color-price--sale);
}
.price.price--large {
  color: var(---color-text--light);
  text-transform: uppercase;
  font-size: var(---font-size-price--major--mobile);
}
@media (min-width: 1000px) {
  .price.price--large {
    font-size: var(---font-size-price--major--desktop);
  }
}

.dots-nav .dots-nav__item {
  border-radius: 100%;
}
.dots-nav .dots-nav__item[aria-current=true] {
  --text-color: var(---color--primary--rgb) ;
}

.bundle-variant-picker {
  --padding: 20px;
  align-items: flex-start;
  background: var(---background-color--content-2);
  border: 1px solid var(---color-line);
  border-radius: var(--block-border-radius);
  display: flex;
  flex-direction: column;
}

.bundle-option-selector {
  width: 100%;
  padding: var(--padding);
}
.bundle-option-selector + .bundle-option-selector {
  padding-top: var(--padding);
  border-top: 1px solid var(---color-line);
}
.bundle-option-selector .product-form__option-info-wrapper {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
.bundle-option-selector .product-form__option-info {
  margin-bottom: 0;
}

.bundle-widget {
  --gap: var(--agap, 15px);
  display: flex;
  align-items: flex-start;
  background: var(---background-color--content-2);
  border: 1px solid var(---color-line);
  border-radius: var(--block-border-radius);
  display: flex;
}

.bundle-widget__header {
  display: flex;
  gap: var(--gap);
  padding-bottom: var(--gap);
  border-bottom: 1px solid var(---color-line);
}

.bundle-widget__header-text > * {
  margin: 0 !important;
}

.bundle-widget__products {
  display: flex;
  flex-direction: column;
  gap: var(--gap);
}

.bundle-widget__inner {
  display: flex;
  flex-direction: column;
  width: 100%;
}
.bundle-widget__inner > * {
  width: 100%;
  padding: var(--gap);
}

.bundle-widget__footer {
  display: flex;
  justify-content: center;
}
.bundle-widget__footer .button, .bundle-widget__footer .swym-add-to-wishlist,
.bundle-widget__footer .giftreggie-pdp-registry-cta, .bundle-widget__footer .drawer .popover .wk-button, .drawer .popover .bundle-widget__footer .wk-button, .bundle-widget__footer .shopify-payment-button__button--unbranded {
  font-weight: 300;
  letter-spacing: 0;
  gap: 0.4em;
}
.bundle-widget__footer .button .price, .bundle-widget__footer .swym-add-to-wishlist .price,
.bundle-widget__footer .giftreggie-pdp-registry-cta .price, .bundle-widget__footer .drawer .popover .wk-button .price, .drawer .popover .bundle-widget__footer .wk-button .price, .bundle-widget__footer .shopify-payment-button__button--unbranded .price {
  color: currentColor;
}
.bundle-widget__footer .button.button--text, .bundle-widget__footer .button--text.swym-add-to-wishlist,
.bundle-widget__footer .button--text.giftreggie-pdp-registry-cta, .bundle-widget__footer .drawer .popover .button--text.wk-button, .drawer .popover .bundle-widget__footer .button--text.wk-button, .bundle-widget__footer .button--text.shopify-payment-button__button--unbranded {
  line-height: 1;
}

.bundle-widget-product {
  --gap: var(--agap, 15px);
  border-radius: var(--block-border-radius);
  overflow: hidden;
  border: 1px solid var(---color-line);
}
.bundle-widget-product .bundle-widget-product__inner {
  display: flex;
  flex-direction: colunn;
  gap: var(--gap);
}
.bundle-widget-product .bundle-widget-product__image {
  max-width: 80px;
  min-width: 80px;
  width: 80px;
  flex: 80px;
  border-right: 1px solid var(---color-line);
}
.bundle-widget-product .bundle-widget-product__image img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.bundle-widget-product .bundle-widget-product__details {
  width: 100%;
  padding-block: var(--gap);
}
.bundle-widget-product .bundle-widget-product__details > * {
  margin: 0;
}
.bundle-widget-product .bundle-widget-product__price {
  line-height: 1;
}
.bundle-widget-product .bundle-widget-product__price .price {
  font-size: var(--font-size-xs);
  color: var(---color-price);
}
.bundle-widget-product .bundle-widget-product__actions {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  gap: 0.5em;
  line-height: 1;
  align-items: center;
  margin-top: 0.5em !important;
}

.product-form__strip-banner .product-form-banner__content > * {
  margin: 0;
}

.top-menu-bar {
  /* Sub Brand */
}
.top-menu-bar .top-menu-bar__inner {
  border-bottom: 1px solid var(---color-line);
  color: var(---color-text);
}
.top-menu-bar .header__linklist {
  height: 48px;
  justify-content: flex-end;
  align-items: center;
  font-size: var(---font-size-body-small--desktop);
}
.top-menu-bar .top-menu-bar__info-links {
  gap: var(--tabbed-header-gap);
}
.top-menu-bar .top-menu-bar__inner {
  display: flex;
  justify-content: space-between;
}
.top-menu-bar .top-menu-bar__sub-brand-links {
  margin-right: auto;
}
.top-menu-bar .top-menu-bar__info-links {
  margin-left: auto;
}

/* -------------------- TABBED MENU BAR -------------------- */
.top-menu-bar.tabbed-top-menu-bar {
  color: RGB(var(--link-color));
  background-color: RGB(var(--background-color-2));
  border-bottom: 1px solid RGB(var(--border-color));
  /* ----- Basic Styles ----- */
  /* ----- Components ----- */
  /* ----- Menu ----- */
  /* ----- Sub Brands ----- */
}
.top-menu-bar.tabbed-top-menu-bar a:not(.tabbed-header-logo) {
  color: RGB(var(--link-color));
}
.top-menu-bar.tabbed-top-menu-bar .top-menu-bar__inner {
  min-height: 55px;
  border-bottom: 0;
  align-items: center;
}
.top-menu-bar.tabbed-top-menu-bar .header__linklist-item {
  margin: 0;
}
.top-menu-bar.tabbed-top-menu-bar .header__linklist-link {
  position: relative;
  display: flex;
  align-items: center;
  gap: 6px;
  color: RGB(var(--link-color));
}
.top-menu-bar.tabbed-top-menu-bar .header__linklist-link-icon {
  opacity: 0.3;
  color: RGB(var(--icon-color));
}
.top-menu-bar.tabbed-top-menu-bar .top-menu-bar__sub-brand-logos {
  display: flex;
  align-items: stretch;
  flex-wrap: wrap;
  padding: 0;
}

/* -------------------- Logos -------------------- */
.tabbed-header-logo {
  --transition-duration: 0.25s;
  position: relative;
  display: inline-flex;
  max-width: 200px;
  min-width: 200px;
  padding: 10px 25px;
  margin-right: -1px;
  border-left: 1px solid RGB(var(--border-color));
  border-right: 1px solid RGB(var(--border-color));
  border-top: 3px solid RGBA(var(--logo-color), 0.2);
  background-color: RGB(var(--background-color-2));
  transition: var(--transition-duration) background-color;
}
@media only screen and (min-width: 1201px) {
  .tabbed-header-logo {
    max-width: 200px;
    min-width: 200px;
  }
}
.tabbed-header-logo .tabbed-header-logo__logo {
  height: auto !important;
}
.tabbed-header-logo .tabbed-header-logo__logo svg, .tabbed-header-logo .tabbed-header-logo__logo img {
  height: auto !important;
}
.tabbed-header-logo img,
.tabbed-header-logo svg {
  display: block;
  width: 100%;
}
.tabbed-header-logo svg * {
  fill: RGB(var(--logo-color));
}
.tabbed-header-logo .tabbed-header-logo__inner {
  opacity: 0.5;
  transition: var(--transition-duration) opacity;
}
.tabbed-header-logo:hover, .tabbed-header-logo.tabbed-header-logo--active {
  background-color: RGB(var(--background-color));
}
.tabbed-header-logo:hover .tabbed-header-logo__inner, .tabbed-header-logo.tabbed-header-logo--active .tabbed-header-logo__inner {
  opacity: 1;
}
.tabbed-header-logo.tabbed-header-logo--active:after {
  content: "";
  position: absolute;
  display: block;
  width: 100%;
  height: 4px;
  left: 0;
  bottom: 0;
  transform: translateY(1px);
  background-color: RGB(var(--background));
}
.tabbed-header-logo.tabbed-header-logo--image:before {
  content: "";
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: RGB(var(--logo-color));
  mix-blend-mode: lighten;
}

.top-menu-bar__sub-brand-logos {
  margin: 0;
}

product-facet .h5 {
  --heading-color: var(---color--secondary--rgb);
  color: RGB(var(--heading-color));
}
.product-facet__active-list {
  font-size: var(---font-size-body-xs--desktop);
}

.product-facet__filters-header {
  padding-bottom: 12px;
}

.product-facet__product-list .product-list__inner {
  overflow: visible;
}

.product-item .product-form__option-link,
.product-item .product-usps-container,
.product-item .product-media-banner-carousel,
.product-item swatch-collections {
  display: none;
}

/*  -----------------------------------
    Product Item
    ----------------------------------- */
.product-item {
  /*  ----------------------------------------
      Standard
      ---------------------------------------- */
  /*  ========================================
      Standard
      ======================================== */
  /*  ========================================
      Swatch Buttons
      ======================================== */
  /*  ========================================
      Sliders
      ======================================== */
  /*  ========================================
      Upsells
      ======================================== */
}
.product-item .product-item__image-wrapper {
  border-radius: var(--block-border-radius);
  border: 1px solid var(---color-line);
  margin-bottom: 1em;
}
.product-item .product-item__label-list {
  right: 10px;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: flex-end;
}
.product-item .product-item-meta {
  text-align: left;
}
.product-item .product-item-meta__reviews-badge {
  margin: 0;
}
.product-item .product-item__secondary-image {
  -o-object-fit: contain;
     object-fit: contain;
  top: 50%;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 50%;
  transform: translate(-50%, -50%) !important;
  -o-object-fit: cover;
     object-fit: cover;
}
.product-item native-video.product-item__secondary-image {
  display: block;
}
.product-item .product-item__primary-image,
.product-item .product-item__secondary-image {
  background: var(---background-color--content-1);
  border-radius: 10px;
}
@media only screen and (max-width: 1000px) {
  .product-item .product-item__primary-image,
.product-item .product-item__secondary-image {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
  }
}
.product-item .product-item-meta__vendor {
  color: var(---color-text--light);
}
.product-item .product-item-meta__title {
  color: var(---color-products-title);
  font-weight: var(---font-weight--product-title);
  font-size: var(---font-size-product-title--mobile);
  white-space: wrap;
}
@media only screen and (min-width: 741px) {
  .product-item .product-item-meta__title {
    font-size: var(---font-size-product-title--desktop);
  }
}
.product-item .product-item-meta__property-list {
  gap: 0.25em;
}
.product-item .product-item-meta__property {
  font-weight: var(---font-weight-body);
}
.product-item .product-item-meta__title {
  margin: 0;
}
.product-item .product-item-meta__price-list-container {
  font-weight: var(---font-weight--product-title);
  margin-top: 0;
}
.product-item .button__icon:not(:only-child) {
  margin-left: auto;
}
.product-item .button__icon svg {
  background-image: url("data:image/svg+xml,%3Csvg width='12' height='12' viewBox='0 0 12 12' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11.9953 5.94871C12.0245 6.57531 11.5218 7.08897 10.9024 7.08897H7.75855C7.3906 7.08897 7.08824 7.38769 7.08824 7.75928V10.9032C7.08824 11.5225 6.57093 12.0216 5.94798 11.9961C5.32503 11.9706 4.90244 11.4314 4.90244 10.834V7.75928C4.90244 7.39134 4.60372 7.08897 4.23213 7.08897H1.09188C0.472571 7.09261 -0.0265184 6.57531 -0.00101751 5.94871C0.0244834 5.32212 0.563646 4.90318 1.1611 4.90318H4.23578C4.60372 4.90318 4.90609 4.60445 4.90609 4.23287V1.09261C4.90609 0.473303 5.41975 -0.025786 6.04634 -0.000285093C6.67294 0.0252158 7.09188 0.564378 7.09188 1.16183V4.23651C7.09188 4.60445 7.3906 4.90682 7.76219 4.90682H10.8369C11.4343 4.90682 11.9735 5.35491 11.999 5.95236L11.9953 5.94871Z' fill='black'/%3E%3C/svg%3E%0A");
}
.product-item .loader-button__text {
  width: 100%;
}
.product-item .loader-button__icon {
  margin-left: auto;
}
.product-item .product-item__primary-image {
  -o-object-fit: cover;
     object-fit: cover;
}
.product-item .popover .block-swatch-list {
  padding-left: 0;
  padding-right: 0;
}
.product-item .block-swatch[disabled] .block-swatch__item {
  cursor: default;
}
.product-item .product-item-meta__additional-text-fields {
  margin: 0.25em 0;
}
.product-item .popover .loader-button__text {
  justify-content: center;
}
.product-item.product-item--swatch-buttons {
  position: relative;
  padding: calc(var(--grid-gap) / 2);
}
.product-item.product-item--swatch-buttons .product-item__quick-add--mobile {
  border-top: 1px solid var(---color-line);
}
.product-item.product-item--swatch-buttons .product-item__cta.product-item__cta--single-variant .button__icon svg {
  background: none;
}
@media only screen and (min-width: 1001px) {
  .product-item.product-item--swatch-buttons:hover .product-item__content-hover {
    display: block;
  }
}
@media only screen and (max-width: 1000px) {
  .product-item.product-item--swatch-buttons {
    padding: 0;
    border: 1px solid var(---color-line);
    border-radius: var(--block-border-radius);
  }
  .product-item.product-item--swatch-buttons .product-item__image-wrapper {
    border: 0;
    border-bottom: 1px solid var(---color-line);
    border-radius: 0;
    margin-bottom: 8px;
  }
  .product-item.product-item--swatch-buttons .product-item__info {
    padding: 10px;
    padding-top: 0;
  }
  .product-item.product-item--swatch-buttons .product-item__cta {
    margin-top: 0;
    margin-bottom: 0;
    padding: 10px;
    width: 100%;
    align-items: center;
    background: var(---background-color--content-1);
    box-shadow: none;
    color: var(---color--primary);
    font-size: var(---font-size-body-small--mobile);
    border-radius: 0;
    border-bottom-left-radius: var(--block-border-radius);
    border-bottom-right-radius: var(--block-border-radius);
  }
  .product-item.product-item--swatch-buttons .quantity-selector-atc {
    margin: 0;
    gap: 16px;
  }
  .product-item.product-item--swatch-buttons .giftreggie-pdp-cta-area {
    margin-top: 10px;
    margin-bottom: 0;
  }
  .product-item.product-item--swatch-buttons .product-form__option-link,
.product-item.product-item--swatch-buttons .product-usps-container,
.product-item.product-item--swatch-buttons .product-media-banner-carousel,
.product-item.product-item--swatch-buttons swatch-collections {
    display: none;
  }
}
.product-item.product-item--swatch-buttons .product-item__content {
  position: relative;
  z-index: 0;
}
.product-item.product-item--swatch-buttons .product-item__content .product-item__quick-add {
  display: none;
}
.product-item.product-item--swatch-buttons .product-item__quick-add {
  text-align: left;
  margin-top: 1em;
}
.product-item.product-item--swatch-buttons .product-item__content-hover {
  padding: calc(var(--grid-gap) / 2);
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 2;
  display: none;
}
.product-item.product-item--swatch-buttons .product-item__content-hover__inner {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  background-color: var(---background-color--content-1);
  border-radius: var(--block-border-radius);
  overflow: hidden;
  padding: 20px;
  margin-left: -20px;
  margin-right: -20px;
  margin-top: -20px;
}
.product-item.product-item--swatch-buttons .product-item__image-wrapper:hover .product-item__badge {
  transform: rotate(10deg);
}
.product-item.product-item--swatch-buttons .product-item__badge {
  position: absolute;
  z-index: 1;
  width: 40px;
  height: 40px;
  bottom: 10px;
  right: 10px;
  pointer-events: none;
  transition: transform 0.25s;
  transform: rotate(0deg);
  will-change: transform;
}
@media (min-width: 1000px) {
  .product-item.product-item--swatch-buttons .product-item__badge {
    width: 60px;
    height: 60px;
    bottom: 20px;
    right: 20px;
  }
}
.product-item.product-item--swatch-buttons .block-swatch, .product-item.product-item--slider .block-swatch {
  margin: 0;
}
.product-item.product-item--swatch-buttons .block-swatch .block-swatch__item, .product-item.product-item--slider .block-swatch .block-swatch__item {
  min-height: 40px;
  font-size: var(---font-size-body-small--desktop);
}
.product-item.product-item--swatch-buttons .block-swatch:not(.is-disabled) .block-swatch__item, .product-item.product-item--slider .block-swatch:not(.is-disabled) .block-swatch__item {
  background-color: RGB(var(--secondary-background));
  color: RGB(var(---color-text--rgb));
}
.product-item.product-item--swatch-buttons .block-swatch:not(.is-disabled) .block-swatch__item:focus, .product-item.product-item--swatch-buttons .block-swatch:not(.is-disabled) .block-swatch__item:hover, .product-item.product-item--slider .block-swatch:not(.is-disabled) .block-swatch__item:focus, .product-item.product-item--slider .block-swatch:not(.is-disabled) .block-swatch__item:hover {
  background-color: RGB(var(--primary-button-background));
  color: RGB(var(--primary-button-text-color));
}
.product-item.product-item--swatch-buttons .block-swatch.is-disabled .block-swatch__item, .product-item.product-item--slider .block-swatch.is-disabled .block-swatch__item {
  background-color: transparent;
  cursor: default;
}
.product-item.product-item--upsell .product-item__info {
  background-color: RGB(var(--secondary-background));
}
.product-item.product-item--upsell .block-swatch {
  margin: 0;
}
.product-item.product-item--upsell .block-swatch .block-swatch__item {
  min-height: 40px;
  font-size: var(---font-size-body-xs--desktop);
}
.product-item.product-item--upsell .block-swatch:not(.is-disabled) .block-swatch__item {
  background-color: RGB(var(--background));
  color: RGB(var(---color-text--rgb));
}
.product-item.product-item--upsell .block-swatch:not(.is-disabled) .block-swatch__item:focus, .product-item.product-item--upsell .block-swatch:not(.is-disabled) .block-swatch__item:hover {
  background-color: RGB(var(--primary-button-background));
  color: RGB(var(--primary-button-text-color));
}
.product-item.product-item--upsell .block-swatch.is-disabled .block-swatch__item {
  background-color: transparent;
  cursor: default;
}
.product-item.product-item--slider {
  margin: 5px;
  background-color: var(---background-color--content-1);
  border-radius: var(--block-border-radius);
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.075);
  overflow: hidden;
}
.product-item.product-item--slider .product-item__info {
  padding: 15px !important;
  height: 100%;
}
.product-item.product-item--slider .product-item__cta-wrapper {
  border-top: 1px solid var(---color-line);
}
.product-item.product-item--slider .product-item__cta-wrapper > form > .button, .product-item.product-item--slider .product-item__cta-wrapper > form > .swym-add-to-wishlist,
.product-item.product-item--slider .product-item__cta-wrapper > form > .giftreggie-pdp-registry-cta, .product-item.product-item--slider .drawer .popover .product-item__cta-wrapper > form > .wk-button, .drawer .popover .product-item.product-item--slider .product-item__cta-wrapper > form > .wk-button, .product-item.product-item--slider .product-item__cta-wrapper > form > .shopify-payment-button__button--unbranded,
.product-item.product-item--slider .product-item__cta-wrapper > .button,
.product-item.product-item--slider .product-item__cta-wrapper > .swym-add-to-wishlist,
.product-item.product-item--slider .product-item__cta-wrapper > .giftreggie-pdp-registry-cta,
.product-item.product-item--slider .drawer .popover .product-item__cta-wrapper > .wk-button,
.drawer .popover .product-item.product-item--slider .product-item__cta-wrapper > .wk-button,
.product-item.product-item--slider .product-item__cta-wrapper > .shopify-payment-button__button--unbranded {
  padding: 15px 20px;
  margin: 0;
  border-radius: 0;
  width: 100%;
  background-color: var(---background-color--content-1);
  color: var(---color--primary);
  border: 0;
  box-shadow: none;
  font-size: var(---font-size-body-small--mobile);
}
@media only screen and (min-width: 1001px) {
  .product-item.product-item--slider .product-item__cta-wrapper > form > .button, .product-item.product-item--slider .product-item__cta-wrapper > form > .swym-add-to-wishlist,
.product-item.product-item--slider .product-item__cta-wrapper > form > .giftreggie-pdp-registry-cta, .product-item.product-item--slider .drawer .popover .product-item__cta-wrapper > form > .wk-button, .drawer .popover .product-item.product-item--slider .product-item__cta-wrapper > form > .wk-button, .product-item.product-item--slider .product-item__cta-wrapper > form > .shopify-payment-button__button--unbranded,
.product-item.product-item--slider .product-item__cta-wrapper > .button,
.product-item.product-item--slider .product-item__cta-wrapper > .swym-add-to-wishlist,
.product-item.product-item--slider .product-item__cta-wrapper > .giftreggie-pdp-registry-cta,
.product-item.product-item--slider .drawer .popover .product-item__cta-wrapper > .wk-button,
.drawer .popover .product-item.product-item--slider .product-item__cta-wrapper > .wk-button,
.product-item.product-item--slider .product-item__cta-wrapper > .shopify-payment-button__button--unbranded {
    font-size: var(---font-size-body-small--desktop);
  }
}
.product-item.product-item--slider .product-item__cta-wrapper > form > .button:focus, .product-item.product-item--slider .product-item__cta-wrapper > form > .swym-add-to-wishlist:focus,
.product-item.product-item--slider .product-item__cta-wrapper > form > .giftreggie-pdp-registry-cta:focus, .product-item.product-item--slider .drawer .popover .product-item__cta-wrapper > form > .wk-button:focus, .drawer .popover .product-item.product-item--slider .product-item__cta-wrapper > form > .wk-button:focus, .product-item.product-item--slider .product-item__cta-wrapper > form > .shopify-payment-button__button--unbranded:focus, .product-item.product-item--slider .product-item__cta-wrapper > form > .button:active, .product-item.product-item--slider .product-item__cta-wrapper > form > .swym-add-to-wishlist:active,
.product-item.product-item--slider .product-item__cta-wrapper > form > .giftreggie-pdp-registry-cta:active, .product-item.product-item--slider .drawer .popover .product-item__cta-wrapper > form > .wk-button:active, .drawer .popover .product-item.product-item--slider .product-item__cta-wrapper > form > .wk-button:active, .product-item.product-item--slider .product-item__cta-wrapper > form > .shopify-payment-button__button--unbranded:active, .product-item.product-item--slider .product-item__cta-wrapper > form > .button:hover, .product-item.product-item--slider .product-item__cta-wrapper > form > .swym-add-to-wishlist:hover,
.product-item.product-item--slider .product-item__cta-wrapper > form > .giftreggie-pdp-registry-cta:hover, .product-item.product-item--slider .drawer .popover .product-item__cta-wrapper > form > .wk-button:hover, .drawer .popover .product-item.product-item--slider .product-item__cta-wrapper > form > .wk-button:hover, .product-item.product-item--slider .product-item__cta-wrapper > form > .shopify-payment-button__button--unbranded:hover,
.product-item.product-item--slider .product-item__cta-wrapper > .button:focus,
.product-item.product-item--slider .product-item__cta-wrapper > .swym-add-to-wishlist:focus,
.product-item.product-item--slider .product-item__cta-wrapper > .giftreggie-pdp-registry-cta:focus,
.product-item.product-item--slider .drawer .popover .product-item__cta-wrapper > .wk-button:focus,
.drawer .popover .product-item.product-item--slider .product-item__cta-wrapper > .wk-button:focus,
.product-item.product-item--slider .product-item__cta-wrapper > .shopify-payment-button__button--unbranded:focus,
.product-item.product-item--slider .product-item__cta-wrapper > .button:active,
.product-item.product-item--slider .product-item__cta-wrapper > .swym-add-to-wishlist:active,
.product-item.product-item--slider .product-item__cta-wrapper > .giftreggie-pdp-registry-cta:active,
.product-item.product-item--slider .drawer .popover .product-item__cta-wrapper > .wk-button:active,
.drawer .popover .product-item.product-item--slider .product-item__cta-wrapper > .wk-button:active,
.product-item.product-item--slider .product-item__cta-wrapper > .shopify-payment-button__button--unbranded:active,
.product-item.product-item--slider .product-item__cta-wrapper > .button:hover,
.product-item.product-item--slider .product-item__cta-wrapper > .swym-add-to-wishlist:hover,
.product-item.product-item--slider .product-item__cta-wrapper > .giftreggie-pdp-registry-cta:hover,
.product-item.product-item--slider .drawer .popover .product-item__cta-wrapper > .wk-button:hover,
.drawer .popover .product-item.product-item--slider .product-item__cta-wrapper > .wk-button:hover,
.product-item.product-item--slider .product-item__cta-wrapper > .shopify-payment-button__button--unbranded:hover {
  box-shadow: none;
}
.product-item.product-item--slider .variant-container > .block-swatch-list {
  padding: 20px;
  padding-top: 0;
}
.product-item.product-item--upsell {
  overflow: hidden;
  padding: 0 !important;
  flex-direction: column;
  margin-top: 10px;
  padding: 20px;
  border-radius: 12px;
  background: RGB(var(--secondary-background));
}
.product-item.product-item--upsell .product-item__info {
  padding: 15px !important;
  margin-top: 5px;
  height: 100%;
}
@media only screen and (min-width: 741px) {
  .product-item.product-item--upsell .product-item__info {
    flex-direction: column;
  }
}
@media only screen and (max-width: 740px) {
  .product-item.product-item--upsell .product-item__info {
    width: 100%;
    flex-direction: row;
  }
}
@media only screen and (max-width: 740px) {
  .product-item.product-item--upsell .product-item__image-wrapper {
    width: 70px;
    margin-right: 15px;
  }
}
@media only screen and (min-width: 741px) {
  .product-item.product-item--upsell .product-item__image-wrapper {
    width: 100%;
  }
}
@media only screen and (max-width: 740px) {
  .product-item.product-item--upsell {
    align-items: flex-start;
  }
  .product-item.product-item--upsell .product-item__info {
    margin-top: 5px;
    height: 100%;
  }
  .product-item.product-item--upsell .product-item__aspect-ratio {
    width: 70px;
    margin: 0;
    aspect-ratio: 2/3;
  }
}
.product-item.product-item--upsell .product-item__cta-wrapper {
  margin-top: auto;
  width: 100%;
  border-top: 1px solid var(---color-line);
}
.product-item.product-item--upsell .product-item__cta-wrapper .product-item__link {
  align-items: center;
}
.product-item.product-item--upsell button.product-item__link {
  position: relative;
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 0;
  margin: 0;
  padding: 10px 20px;
  background: RGB(var(--secondary-background));
  transition: 0.25s border-color, 0.25s color, 0.25s background-color;
}
.product-item.product-item--upsell button.product-item__link:hover {
  border-color: var(---color-line--dark);
}
.product-item.product-item--upsell button.product-item__link .loader-button__text {
  width: 100%;
}
.product-item.product-item--upsell button.product-item__link .button__text {
  justify-content: flex-start;
}
.product-item.product-item--upsell .block-swatch-list {
  padding: 5px 20px 20px;
}

[dir=ltr] .product-item__label-list {
  left: auto;
  right: 10px;
}

.product-usps-container {
  padding: 20px;
  border: 1px solid var(---color-line);
  border-radius: 12px;
  margin-bottom: 20px;
}
@media only screen and (max-width: 1000px) {
  .product-usps-container {
    margin-top: 0;
  }
}
.product-usps-container .product-usps {
  display: flex;
  flex-direction: column;
}
@media only screen and (min-width: 741px) {
  .product-usps-container .product-usps {
    flex-direction: row;
    margin-block: 10px;
  }
}
.product-usps-container .product-usps.product-usps--images-only {
  flex-direction: row;
  justify-content: space-around;
}
.product-usps-container .product-usps.product-usps--images-only .product-usp {
  width: auto;
}
.product-usps-container .product-usps.product-usps--images-only .product-usp__image {
  max-width: 100px;
}
.product-usps-container .product-usps.product-usps--images-only .product-usp__image svg {
  width: unset;
}
.product-usps-container .product-usp {
  display: flex;
  width: 100%;
  gap: 10px;
  text-align: center;
  align-items: center;
}
@media only screen and (min-width: 741px) {
  .product-usps-container .product-usp {
    flex-direction: column;
  }
}
@media only screen and (min-width: 741px) {
  .product-usps-container .product-usp__image {
    max-width: 100px;
  }
}
.product-usps-container .product-usp__image svg {
  width: 50px;
}
.product-usps-container .product-usp__title {
  line-height: 1.2;
  width: 100%;
}
.product-sticky-form {
  background-color: var(---background-color--content-1);
  z-index: 3;
}
.product-sticky-form .square-separator {
  background-color: var(---color--primary);
}

@media (max-width: 1000px) {
  .product-sticky-form__form product-payment-container .button, .product-sticky-form__form product-payment-container .swym-add-to-wishlist,
.product-sticky-form__form product-payment-container .giftreggie-pdp-registry-cta, .product-sticky-form__form product-payment-container .drawer .popover .wk-button, .drawer .popover .product-sticky-form__form product-payment-container .wk-button, .product-sticky-form__form product-payment-container .shopify-payment-button__button--unbranded {
    width: 100%;
  }
}

.product-sticky-form .select {
  height: 100%;
}

.product-sticky-form__title {
  font-size: var(---font-size-product-title--desktop);
  font-weight: var(---font-weight--product-title);
}

.product-sticky-form__bottom-info {
  display: flex;
  align-items: center;
  gap: 5px;
}

/* ========== Custom (with jump links ========== */
.product-sticky-form.product-sticky-form--custom {
  z-index: 3;
  display: flex;
  align-items: center;
  margin: auto;
  border: 0 !important;
  box-shadow: none;
  border-radius: var(--block-border-radius);
  background-color: transparent;
}
@media only screen and (min-width: 1001px) {
  .product-sticky-form.product-sticky-form--custom {
    left: 40px !important;
    right: 40px;
    top: calc(var(--header-height) + 20px);
    width: unset;
    padding: 10px 0 !important;
    background-color: RGB(var(--secondary-background));
  }
}
.product-sticky-form.product-sticky-form--custom .container {
  padding: 0 20px;
}
.product-sticky-form.product-sticky-form--custom .select {
  height: 32px;
  min-height: 32px;
  min-width: unset;
  padding: 0 10px;
  font-size: var(---font-size-body-small--desktop);
  background-color: var(---background-color--content-1);
  border-radius: var(--block-border-radius);
}
.product-sticky-form.product-sticky-form--custom .combo-box__option-item {
  font-size: var(---font-size-body-small--desktop);
}
.product-sticky-form.product-sticky-form--custom .button, .product-sticky-form.product-sticky-form--custom .swym-add-to-wishlist,
.product-sticky-form.product-sticky-form--custom .giftreggie-pdp-registry-cta, .product-sticky-form.product-sticky-form--custom .drawer .popover .wk-button, .drawer .popover .product-sticky-form.product-sticky-form--custom .wk-button, .product-sticky-form.product-sticky-form--custom .shopify-payment-button__button--unbranded {
  margin: 0;
  text-align: center;
  justify-content: center;
}
@media only screen and (max-width: 1000px) {
  .product-sticky-form.product-sticky-form--custom .button, .product-sticky-form.product-sticky-form--custom .swym-add-to-wishlist,
.product-sticky-form.product-sticky-form--custom .giftreggie-pdp-registry-cta, .product-sticky-form.product-sticky-form--custom .drawer .popover .wk-button, .drawer .popover .product-sticky-form.product-sticky-form--custom .wk-button, .product-sticky-form.product-sticky-form--custom .shopify-payment-button__button--unbranded {
    padding: 15px !important;
  }
}
.product-sticky-form.product-sticky-form--custom .product-sticky-form__bottom-info {
  gap: 15px;
}
.product-sticky-form.product-sticky-form--custom .product-sticky-form__form {
  order: 1;
  margin: 0;
}
.product-sticky-form.product-sticky-form--custom .product-sticky-form__inner {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 15px;
}
@media only screen and (max-width: 1000px) {
  .product-sticky-form.product-sticky-form--custom .product-sticky-form__inner {
    flex-direction: column;
  }
}
.product-sticky-form.product-sticky-form--custom .product-sticky-form__jump-links {
  display: flex;
  gap: 15px;
  margin-left: auto;
  order: 1;
}
@media only screen and (max-width: 1000px) {
  .product-sticky-form.product-sticky-form--custom .product-sticky-form__jump-links {
    background: var(---background-color--content-2);
    padding: 1em 1.5em;
    border-radius: var(--block-border-radius);
    margin-right: auto;
    order: 0;
  }
}

facet-filters .checkbox-container {
  margin-bottom: 1em;
}

.collection-sidebar-menu {
  margin-bottom: 36px;
  font-size: var(---font-size-body-small--desktop);
  color: var(---color-text);
}
@media only screen and (max-width: 1000px) {
  .collection-sidebar-menu {
    display: none;
  }
}
@media only screen and (min-width: 1001px) {
  .collection-sidebar-menu {
    font-size: var(---font-size-body-small--desktop);
  }
}
.collection-sidebar-menu .collapsible__content {
  padding-left: 24px;
  padding-bottom: 2px;
  padding-top: 4px;
}
.collection-sidebar-menu .collection-sidebar-menu__header {
  padding-bottom: 12px;
  margin-bottom: 24px;
  border-bottom: 1px solid var(---color-line--light);
}
.collection-sidebar-menu .collection-sidebar-menu__item-icon {
  transition: var(---transition-duration--general) transform;
}
.collection-sidebar-menu .collection-sidebar-menu__item {
  width: 100%;
  margin: 6px 0;
}
.collection-sidebar-menu .collection-sidebar-menu__item:hover {
  color: var(---color-text--dark);
}
.collection-sidebar-menu .collection-sidebar-menu__link {
  width: 100%;
  margin: 6px 0;
  transition: var(---transition-duration--general) color;
}
.collection-sidebar-menu .collection-sidebar-menu__link:focus, .collection-sidebar-menu .collection-sidebar-menu__link:hover {
  color: var(---color-link);
}
.collection-sidebar-menu .collection-sidebar-menu__toggle {
  display: flex;
  gap: 6px;
}
.collection-sidebar-menu .collection-sidebar-menu__toggle[aria-expanded=false] .collection-sidebar-menu__item-icon {
  transform: rotate(-90deg);
}
.collection-sidebar-menu .collection-sidebar-menu__toggle[aria-expanded=true] .collection-sidebar-menu__item-icon {
  transform: rotate(0deg);
}

.collection-grid-banner {
  display: flex;
  flex-direction: column;
  height: 100%;
  border-radius: var(--block-border-radius);
  overflow: hidden;
  /* ----- Header ----- */
  /* ----- Media ----- */
}
.collection-grid-banner hr {
  margin-top: 0;
}
@media only screen and (min-width: 1001px) {
  .collection-grid-banner.collection-grid-banner--span-1 {
    grid-column-end: span 1;
  }
}
@media only screen and (min-width: 1001px) {
  .collection-grid-banner.collection-grid-banner--span-2 {
    grid-column-end: span 2;
  }
}
@media only screen and (min-width: 1001px) {
  .collection-grid-banner.collection-grid-banner--span-3 {
    grid-column-end: span 3;
  }
}
@media only screen and (min-width: 1001px) {
  .collection-grid-banner.collection-grid-banner--span-4 {
    grid-column-end: span 4;
  }
}
@media only screen and (max-width: 1000px) {
  .collection-grid-banner.collection-grid-banner--mobile-fullwidth {
    grid-column-end: span 2;
    margin-left: 0;
    margin-right: 0;
  }
  .collection-grid-banner.collection-grid-banner--mobile-fullwidth:first-child {
    margin-top: calc(var(--container-gutter) / 2 * -1);
  }
}
.collection-grid-banner.collection-grid-banner--cover img {
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.collection-grid-banner.collection-grid-banner--cover .collection-grid-banner__video {
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.collection-grid-banner .collection-grid-banner__video {
  margin-top: auto;
}
.collection-grid-banner .collection-grid-banner__header {
  margin-bottom: auto;
}
.collection-grid-banner .collection-grid-banner__title {
  margin-top: 0.5em;
  margin-bottom: 0.25em;
}
.collection-grid-banner .collection-grid-banner__text {
  margin-bottom: 1em;
}
.collection-grid-banner .collection-grid-banner__media {
  position: relative;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.collection-grid-banner .collection-grid-banner__media video,
.collection-grid-banner .collection-grid-banner__media img {
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.collection-grid-banner a.collection-grid-banner__media video,
.collection-grid-banner a.collection-grid-banner__media img {
  transform: scale(1);
  z-index: 0;
  position: relative;
  transition: transform 1s ease-in-out;
}
.collection-grid-banner a.collection-grid-banner__media:hover video,
.collection-grid-banner a.collection-grid-banner__media:hover img {
  transform: scale(1.05);
}
.collection-grid-banner a.collection-grid-banner__media .video-wrapper {
  height: 100%;
}
.collection-grid-banner .collection-grid-banner__overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  width: 100%;
  height: 100%;
  padding: 40px;
}
.collection-grid-banner .collection-grid-banner__overlay-subheading {
  margin: 0;
}
.collection-grid-banner .collection-grid-banner__overlay-subheading + .heading, .collection-grid-banner .tolstoy-stories .collection-grid-banner__overlay-subheading + .tolstoy-stories-title, .tolstoy-stories .collection-grid-banner .collection-grid-banner__overlay-subheading + .tolstoy-stories-title {
  margin-top: 0;
}
.collection-grid-banner .collection-grid-banner__overlay-inner {
  max-width: 400px;
}
.collection-grid-banner .collection-grid-banner__overlay_title {
  margin-bottom: 0.25em;
}
.collection-grid-banner .button-wrapper {
  margin-top: 0.5em;
  color: #fff;
}
.collection-grid-banner .button-wrapper .link {
  --text-color: var(--text-color);
}
@media only screen and (max-width: 740px) {
  .collection-grid-banner .collection-grid-banner__overlay {
    padding: 20px;
  }
}

.collection-promotional-banner {
  display: flex;
  padding: 0;
  width: 100%;
  background: var(--section-block-background);
}
@media (max-width: 1000px) {
  .collection-promotional-banner {
    flex-direction: column-reverse;
  }
}
.collection-promotional-banner .collection-promotional-banner__content-wrapper {
  width: 50%;
  padding: var(--promotion-block-padding);
}
@media (max-width: 1000px) {
  .collection-promotional-banner .collection-promotional-banner__content-wrapper {
    width: 100%;
  }
}
.collection-promotional-banner .collection-promotional-banner__image-container {
  width: 50%;
  height: 100%;
  display: flex;
  overflow: hidden;
}
@media (max-width: 1000px) {
  .collection-promotional-banner .collection-promotional-banner__image-container {
    width: 100%;
  }
}
.collection-promotional-banner .collection-promotional-banner__image-container img {
  -o-object-fit: cover;
     object-fit: cover;
}

.gallery-review-product {
  display: flex;
  gap: 15px;
}

.gallery-review-product__image {
  min-width: 60px;
  width: 60px;
  height: 60px;
  border-radius: 300px;
  overflow: hidden;
}
.gallery-review-product__image img {
  height: 100%;
  width: 100%;
  min-width: 100%;
  margin: 0 !important;
}

.gallery-review-product__text {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 2px;
}
.gallery-review-product__text > * {
  margin: 0 !important;
}
.gallery-review-product__text .price {
  font-size: var(---font-size-subheading-small);
  font-family: var(---font-family-subheading);
  font-size: var(---font-size-subheading--mobile);
  font-style: var(---font-style-subheading);
  font-variation-settings: "wght" 400;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}
.gallery-review-product__text .price--highlight {
  color: #fff !important;
}

@media only screen and (max-width: 740px) {
  .product-tabs {
    margin-top: 0;
  }
}
@media only screen and (max-width: 1000px) {
  .product-tabs {
    border-bottom: 1px solid var(---color-line);
  }
}
.product-tabs .tabs-nav__arrows {
  top: 0;
}
.product-tabs .product-tabs__tab-item-wrapper[hidden] {
  display: none;
}
@media only screen and (max-width: 1000px) {
  .product-tabs .product-tabs__tab-item-wrapper .product-tabs__tab-item-content,
.product-tabs .product-tabs__tab-item-wrapper .collapsible-toggle {
    padding-left: var(--container-gutter);
    padding-right: var(--container-gutter);
  }
}
.product-tabs .product-tabs__content em strong {
  font-size: 28px;
  font-style: normal;
}
@media only screen and (max-width: 740px) {
  .product-tabs .product-tabs__trust-list {
    overflow: scroll;
    margin: 0 calc(-1 * var(--container-gutter));
    width: 100vw;
  }
}
.product-tabs .product-tabs__trust-list-inner {
  display: flex;
  padding: 10px calc(var(--container-gutter));
  scroll-snap-type: both mandatory;
  overscroll-behavior-x: contain;
}
@media only screen and (max-width: 740px) {
  .product-tabs .product-tabs__trust-list-inner {
    gap: 30px;
    white-space: nowrap;
  }
}
@media only screen and (min-width: 741px) {
  .product-tabs .product-tabs__trust-list-inner {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 20px;
    padding: 0;
  }
}
.product-tabs .product-tabs__trust-title {
  scroll-snap-align: start;
  scroll-snap-stop: always;
  scroll-margin: var(--container-gutter);
}
@media only screen and (max-width: 1000px) {
  .product-tabs .product-tabs__trust-list,
.product-tabs .product-tabs__tab-item-wrapper {
    padding: 0;
  }
}
@media only screen and (max-width: 1000px) {
  .product-tabs .product-tabs__tab-item-wrapper[hidden] {
    display: block !important;
  }
}

.product.product--thumbnails-bottom {
  margin-top: 0;
}

product-media .product__media-image-wrapper {
  border-radius: 0;
}
product-media .product__media-list-wrapper {
  margin-left: calc(var(--container-gutter) * -1);
  margin-right: calc(var(--container-gutter) * -1);
  overflow: hidden;
}

.product-media-banners {
  grid-column: 1/span 2;
  min-height: 50px;
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: var(--grid-gap);
}

.product-media-banner {
  border: 1px solid var(---color-line);
  padding: 35px 20px;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 0.5em;
}
.product-media-banner .product-media-banner__link {
  justify-content: center;
}

.product-media-badges {
  display: flex;
  position: absolute;
  left: 20px;
  top: 20px;
  z-index: 1;
  pointer-events: none;
}

.product-media-badge {
  width: 40px;
  transform: rotate(-10deg);
}
@media only screen and (min-width: 1201px) {
  .product-media-badge {
    width: 60px;
  }
}

.product__media {
  position: relative;
}

.product-media-banner-carousel {
  border: 1px solid var(---color-line);
  overflow: hidden;
  border-radius: 12px;
  position: relative;
}
.product-media-banner-carousel .dots-nav {
  transform: translateY(-50%);
  margin: auto;
  padding-bottom: 10px;
}
.product-media-banner-carousel .product-media-banner {
  margin: auto;
  border: 0;
}
.product-media-banner-carousel .product-media-banner-carousel__list {
  display: flex;
  scroll-snap-type: x mandatory;
}
@media (max-width: 1000px) {
  .product-media-banner-carousel .product-media-banner-carousel__list {
    margin-left: calc(var(--container-gutter) * -4);
    margin-right: calc(var(--container-gutter) * -4);
  }
}
@media (min-width: 1000px) {
  .product-media-banner-carousel .product-media-banner-carousel__list {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 50px;
  }
}
.product-media-banner-carousel .product-media-banner-carousel__item {
  display: block;
  flex: none;
  scroll-snap-align: center;
  scroll-snap-stop: always;
  width: 100%;
}
.product-media-banner-carousel .product-media-banner-carousel__item-inner {
  display: flex;
}
.product-media-banner-carousel .product-media-banner-carousel__icon {
  min-width: 50px;
  max-width: 50px;
  max-height: 50px;
}
.product-media-banner-carousel .product-media-banner-carousel__icon img {
  height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
}
.product-media-banner-carousel .product-media-banner-carousel__item-description {
  margin-left: 1em;
}
.product-media-banner-carousel .product-media-banner-carousel__item-heading {
  margin-bottom: 0.5em;
}
.product-media-banner-carousel .product-media-banner-carousel__dots {
  margin-top: 20px;
}

/* ========== Product Media ========== */
.product__media .product-media__label-list {
  position: absolute;
  z-index: 1;
  top: 10px;
  right: 10px;
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  gap: 10px;
}
@media only screen and (max-width: 740px) {
  .product__media .product-media__label-list {
    top: 24px;
  }
}

/* ========== Product Media - Tiled ========== */
.product__media.product-media--tiled {
  padding: 0 !important;
  /*----- Gallery ----- */
}
@media only screen and (max-width: 1000px) {
  .product__media.product-media--tiled {
    margin-left: calc(var(--container-gutter) * -1);
    margin-right: calc(var(--container-gutter) * -1);
  }
}
@media only screen and (max-width: 1000px) {
  .product__media.product-media--tiled .product__media-list-wrapper {
    display: flex;
    gap: var(--container-gutter);
    overflow: scroll;
    -ms-overflow-style: none; /* Internet Explorer 10+ */
    scrollbar-width: none; /* Firefox */
  }
  .product__media.product-media--tiled .product__media-list-wrapper::-webkit-scrollbar {
    display: none; /* Safari and Chrome */
  }
}
@media only screen and (min-width: 1001px) {
  .product__media.product-media--tiled .product__media-list-wrapper {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: var(--grid-gap);
  }
}
@media only screen and (min-width: 1001px) {
  .product__media.product-media--tiled .product__media-item .product__zoom-button {
    opacity: 0;
    transition: opacity 0.25s;
  }
  .product__media.product-media--tiled .product__media-item:hover .product__zoom-button {
    opacity: 1;
  }
}
@media only screen and (min-width: 1001px) {
  .product__media.product-media--tiled.product-media--tiled-full-width .product__media-list-wrapper {
    display: flex;
    flex-direction: column;
    gap: var(--grid-gap);
  }
}
@media only screen and (min-width: 1001px) {
  .product__media.product-media--tiled.product-media--tiled-large-first .product__media-list-wrapper .product__media-item:first-child {
    grid-column: span 2;
  }
}
.product__media.product-media--tiled .product__media-image-wrapper {
  background: transparent;
}
.product__media.product-media--tiled .product__media-image-wrapper img {
  transition: transform 0.5s;
  transform: scale(1);
  will-change: transform;
}
.product__media.product-media--tiled .product__media-image-wrapper img:hover {
  transform: scale(1.05);
}
.product__media.product-media--tiled .product__media-item {
  position: relative;
  overflow: hidden;
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.product__media.product-media--tiled .product__media-item video {
  -o-object-fit: cover;
     object-fit: cover;
}
.product__media.product-media--tiled .product__media-item template,
.product__media.product-media--tiled .product__media-item img {
  width: 100%;
  height: 100%;
  display: block;
}
.product__media.product-media--tiled .product__media-item span.media-caption {
  position: absolute;
  bottom: 10px;
  right: 10px;
  padding: 0.5em 0.75em;
  background: var(---background-color--content-1);
  font-size: 11px;
  text-transform: uppercase;
}
@media only screen and (max-width: 1000px) {
  .product__media.product-media--tiled .product__media-item {
    padding: 0;
    min-width: 70vw;
  }
  .product__media.product-media--tiled .product__media-item:first-child {
    margin-left: var(--container-gutter);
  }
  .product__media.product-media--tiled .product__media-item:last-child {
    margin-right: var(--container-gutter);
  }
}
.product__media.product-media--tiled .video-wrapper {
  height: 100%;
  width: 100%;
  overflow: hidden;
  border-radius: var(--block-border-radius-reduced);
}

.swatch-collections-tabs {
  display: flex;
  flex-wrap: nowrap;
  width: 100%;
  margin-bottom: 20px;
}
.swatch-collections-tabs .swatch-collections-tab {
  position: relative;
  padding: 0.8em 1.4em;
  width: 100%;
  margin-right: 1px;
  font-size: var(---font-size-body-small--mobile);
  text-align: center;
  line-height: 1.2;
  box-shadow: 0 0 0 1px var(---color--primary);
  transition: 0.25s color, 0.25s background;
}
@media only screen and (min-width: 1001px) {
  .swatch-collections-tabs .swatch-collections-tab {
    padding: 0.5em 1.4em;
    font-size: var(---font-size-body-small--desktop);
  }
}
.swatch-collections-tabs .swatch-collections-tab[aria-hidden=true] {
  display: none;
}
.swatch-collections-tabs .swatch-collections-tab:focus, .swatch-collections-tabs .swatch-collections-tab:focus-within, .swatch-collections-tabs .swatch-collections-tab:hover {
  background: var(---color--brand-7);
}
.swatch-collections-tabs .swatch-collections-tab:first-child {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}
.swatch-collections-tabs .swatch-collections-tab:last-child {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.swatch-collections-tabs .swatch-collections-tab[aria-expanded=true] {
  background-color: var(---color--primary);
  color: var(---color-text--reversed);
}
.swatch-collections-tabs .swatch-collections-tab[aria-expanded=true]:after {
  top: 100%;
  left: 50%;
  border: solid transparent;
  content: "";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-color: rgba(var(---color--primary--rgb), 0);
  border-top-color: var(---color--primary);
  border-width: 8px;
  margin-left: -8px;
}

.swatch-collections-contents .swatch-collections-content hidden {
  display: none;
}

.color-swatch-list__loading-message {
  background: RGB(var(---background-color--content-2--rgb));
  padding: 0.5em 1em;
  border-radius: 8px;
  text-align: center;
  width: 100%;
}

.quantity-selector {
  overflow: visible;
  border: 0 !important;
  outline: 1px solid RGB(var(--border-color)) !important;
}
.quantity-selector .quantity-selector__button:first-child {
  border-top-left-radius: var(--button-border-radius);
  border-bottom-left-radius: var(--button-border-radius);
}
.quantity-selector .quantity-selector__button:last-child {
  border-top-right-radius: var(--button-border-radius);
  border-bottom-right-radius: var(--button-border-radius);
}
.quantity-selector span.quantity-selector__button {
  opacity: 0.8;
  cursor: not-allowed;
}
@media only screen and (max-width: 1000px) {
  .quantity-selector .quantity-selector__input {
    padding: 0 !important;
    min-width: 50px !important;
  }
}
.quantity-selector.quantity-selector--small {
  --quantity-selector-height: 40px;
}

.quantity-selector-atc-container {
  display: grid;
  grid-auto-flow: row;
  gap: var(--vertical-gap);
}

.quantity-selector-atc {
  display: flex;
  gap: 10px;
}
.quantity-selector-atc .product-form__payment-container {
  width: 100%;
}
.quantity-selector-atc .button, .quantity-selector-atc .swym-add-to-wishlist,
.quantity-selector-atc .giftreggie-pdp-registry-cta, .quantity-selector-atc .drawer .popover .wk-button, .drawer .popover .quantity-selector-atc .wk-button, .quantity-selector-atc .shopify-payment-button__button--unbranded {
  height: 60px;
  max-height: 60px;
}
.quantity-selector-atc .product-form__add-button {
  display: flex;
  gap: 20px;
}
.quantity-selector-atc .product-form__add-button .icon--custom-cart {
  min-width: 22px;
  min-height: 22px;
  align-items: center;
  color: inherit;
  stroke: none;
}
.quantity-selector-atc .product-form__add-button .loader-button__text {
  gap: 15px;
}
.quantity-selector-atc .price {
  color: var(---color-text--reversed);
}
@media only screen and (max-width: 740px) {
  .quantity-selector-atc {
    flex-direction: column;
    align-items: flex-start;
  }
  .quantity-selector-atc .quantity-selector__button {
    height: 100%;
    min-height: 48px;
  }
  .quantity-selector-atc .quantity-selector {
    display: inline-flex;
  }
}

.quantity-selector-atc__qty {
  display: flex;
}
.quantity-selector-atc__qty input {
  min-width: 64px !important;
}
@media only screen and (max-width: 1000px) {
  .quantity-selector-atc__qty input {
    min-width: 50px !important;
    padding: 0 !important;
  }
}
.quantity-selector-atc__qty .quantity-selector__button {
  margin: 0;
  padding: 0;
  height: 100%;
  text-align: center !important;
  justify-content: center;
  min-width: 64px !important;
}
@media only screen and (max-width: 1000px) {
  .quantity-selector-atc__qty .quantity-selector__button {
    min-width: 50px !important;
  }
}
.quantity-selector-atc__qty .quantity-selector__button:first-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.quantity-selector-atc__qty .quantity-selector__button:last-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.product-form__gift-card-recipient {
  --background: var(---background-color--content-2--rgb);
  --section-block-background: var(---background-color--content-2--rgb);
  padding: 20px;
  margin: 20px 0;
  background-color: RGB(var(---background-color--content-2--rgb));
  border-radius: var(--block-border-radius);
}
.label-list.product-item__label-list .label {
  font-size: var(---font-size-body-xs--mobile);
}
@media only screen and (min-width: 741px) {
  .label-list.product-item__label-list .label {
    font-size: var(---font-size-body-xs--desktop);
  }
}
.label-list.label-list--flex {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 0.25em 0.5em;
}

.label {
  font-size: var(---font-size-body-xs--desktop);
  font-weight: 400;
  letter-spacing: 0;
  text-transform: uppercase;
  padding: 0.2em 0.7em;
  transition: var(---transition-duration--general) background-color;
  border-radius: 50px;
  font-size: var(---font-size-body-small--mobile);
}
@media only screen and (min-width: 741px) {
  .label {
    font-size: var(---font-size-body-small--desktop);
  }
}
.label.label--subdued {
  background-color: var(---color--tertiary);
}
.label.label--highlight {
  background-color: var(---color-price--sale);
}
.label.label--custom {
  background: var(---background-color--content-2);
  color: var(---color--secondary);
}
.label.label--custom2 {
  background: var(---color--brand-7);
  color: var(---color--secondary);
}
.label.label--small {
  padding: 0.2em 0.7em;
}
.label.label--tiny {
  padding: 0.2em 0.7em;
  font-size: var(--font-size-xs);
  border-radius: var(---border-radius--inputs);
}

a.label.label--primary:hover {
  background-color: var(---color--primary--light);
}
a.label.label--secondary:hover {
  background-color: var(---color--secondary--light);
}
a.label.label--tertiary:hover {
  background-color: var(---color--tertiary--light);
}

/*  -----------------------------------
    Addons
    ----------------------------------- */
.product-form__addons {
  --background: var(---background-color--content-2);
  border-radius: var(--block-border-radius);
  background-color: var(---background-color--content-2);
  padding: 20px;
}

.product-addon-container {
  display: grid;
  grid-template-rows: auto;
  gap: 0.5em;
}
.product-addon-container .checkbox-container .checkbox {
  background-color: #fff;
}

.product-addon__details-link {
  margin-left: auto;
  display: none;
}

.product-addon {
  --background: var(---background-color--content-2--rgb);
  display: flex;
  gap: 0.5em;
}
.product-addon label {
  display: inline-block;
  padding: 0;
  cursor: pointer;
}
.product-addon button[disabled] {
  opacity: 0.3;
  pointer-events: none;
}
.product-addon button[disabled] + label {
  opacity: 0.3;
  pointer-events: none;
}
.product-addon.product-addon--added .product-addon__details-link {
  display: inline-block;
}
.product-addon.product-addon--added .checkbox {
  background-repeat: no-repeat;
  background-position: center center;
  background-image: url("data:image/svg+xml,%3Csvg width='12' height='10' viewBox='0 0 12 10' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11.7647 1.50542L4.79326 9.6845C4.63079 9.87499 4.39309 9.99107 4.14035 10C4.12831 10 4.11628 10 4.10123 10C3.86353 10 3.63186 9.90773 3.46336 9.73808L0.264975 6.57122C-0.129182 6.18131 -0.0840494 5.52055 0.403381 5.19613C0.764441 4.95504 1.25789 5.02648 1.56479 5.33304L3.33699 7.08613C3.71911 7.46413 4.34796 7.43734 4.69698 7.02958L10.4198 0.317849C10.7507 -0.0720559 11.3465 -0.110749 11.7286 0.24344C12.0837 0.573818 12.0837 1.13933 11.7677 1.5084L11.7647 1.50542Z' fill='%239A9A9A'/%3E%3C/svg%3E%0A");
}

.product-addon-form {
  display: grid;
  grid-template-rows: auto;
  gap: 30px;
}

.addon-option__heading {
  display: flex;
  justify-content: space-between;
  -webkit-margin-after: 0.5em;
          margin-block-end: 0.5em;
}

.example-gallery {
  display: grid;
  gap: 10px;
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.example-gallery-item {
  position: relative;
  padding: 10px;
  border: 1px solid var(---color-line);
  border-radius: 12px;
}
.example-gallery-item img {
  border-radius: 8px;
}

.example-gallery-item__title {
  display: inline-block;
  margin: 0.5em 0 0 0;
  font-size: var(---font-size-body-small--desktop);
  text-align: center;
  line-height: 1.2;
}

.product-key-ingredients {
  margin-bottom: 20px;
}

.product-key-ingredients__inner {
  --gap: 20px;
  --padding: 30px;
}
@media only screen and (min-width: 1001px) {
  .product-key-ingredients__inner {
    display: grid;
    grid-auto-flow: row;
    gap: var(--gap);
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media only screen and (max-width: 1000px) {
  .product-key-ingredient {
    margin-bottom: 20px;
  }
}
.product-key-ingredient .product-key-ingredient__inner {
  display: flex;
  gap: 0.5em;
}
.product-key-ingredient .product-key-ingredient__title {
  -webkit-margin-after: 0.25em;
          margin-block-end: 0.25em;
}
.product-key-ingredient .product-key-ingredient__icon svg,
.product-key-ingredient .product-key-ingredient__icon img {
  margin: 0;
  width: 50px;
  min-width: 50px;
}
@media only screen and (min-width: 1001px) {
  .product-key-ingredient .product-key-ingredient__icon svg,
.product-key-ingredient .product-key-ingredient__icon img {
    width: 60px;
    min-width: 60px;
  }
}
.line-item .line-item__label-list {
  margin-bottom: 10px;
}
.line-item .product-item-meta__property-list {
  gap: 5px;
  margin-top: 8px !important;
}
.line-item .product-item-meta__property {
  line-height: 1em;
}
.line-item .product-item-meta__property li {
  margin-top: 5px;
  margin-bottom: 5px;
}

.line-item--offer .line-item__quantity {
  display: none;
}
.line-item--offer .line-item-quantity--display:before {
  content: "x";
}
.line-item--offer .line-item-quantity--display [data-quantity]:before {
  content: attr(data-quantity);
}

.shipping-bar .shipping-bar__progress {
  border: 0;
  height: 10px;
  border-radius: 10px;
  overflow: hidden;
  background: var(---color--kyte-light-grey);
  box-shadow: 0 0 0 1px var(---color--kyte-dark-grey) inset;
}
.shipping-bar .shipping-bar__progress:after {
  background: var(---color--kyte-dark-grey);
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
}
.shipping-bar .shipping-bar__text {
  display: inline;
  font-size: 13px;
}
.shipping-bar .country-code-flag {
  font-size: 1.6em;
  line-height: 1em;
}
.shipping-bar .shipping-bar__flag {
  line-height: 1;
  position: relative;
  top: 5px;
}

[dir=ltr] .rte,
[dir=rtl] .rte {
  /* ----- Links ----- */
  /* ----- Blockquote ----- */
  /* ----- Lists ----- */
}
[dir=ltr] .rte a:not(.button):not(.swym-add-to-wishlist):not(.giftreggie-pdp-registry-cta):not(.shopify-payment-button__button--unbranded), [dir=ltr] .rte .a:not(.button):not(.swym-add-to-wishlist):not(.giftreggie-pdp-registry-cta):not(.shopify-payment-button__button--unbranded),
[dir=rtl] .rte a:not(.button):not(.swym-add-to-wishlist):not(.giftreggie-pdp-registry-cta):not(.shopify-payment-button__button--unbranded),
[dir=rtl] .rte .a:not(.button):not(.swym-add-to-wishlist):not(.giftreggie-pdp-registry-cta):not(.shopify-payment-button__button--unbranded) {
  cursor: pointer;
  transition: color 0.1s ease-in-out;
}
[dir=ltr] .rte a:not(.button):not(.swym-add-to-wishlist):not(.giftreggie-pdp-registry-cta):not(.shopify-payment-button__button--unbranded):hover, [dir=ltr] .rte .a:not(.button):not(.swym-add-to-wishlist):not(.giftreggie-pdp-registry-cta):not(.shopify-payment-button__button--unbranded):hover,
[dir=rtl] .rte a:not(.button):not(.swym-add-to-wishlist):not(.giftreggie-pdp-registry-cta):not(.shopify-payment-button__button--unbranded):hover,
[dir=rtl] .rte .a:not(.button):not(.swym-add-to-wishlist):not(.giftreggie-pdp-registry-cta):not(.shopify-payment-button__button--unbranded):hover {
  color: var(---color-link--hover);
}
[dir=ltr] .rte a:not(.button):not(.swym-add-to-wishlist):not(.giftreggie-pdp-registry-cta):not(.shopify-payment-button__button--unbranded).subheading, [dir=ltr] .rte .a:not(.button):not(.swym-add-to-wishlist):not(.giftreggie-pdp-registry-cta):not(.shopify-payment-button__button--unbranded).subheading,
[dir=rtl] .rte a:not(.button):not(.swym-add-to-wishlist):not(.giftreggie-pdp-registry-cta):not(.shopify-payment-button__button--unbranded).subheading,
[dir=rtl] .rte .a:not(.button):not(.swym-add-to-wishlist):not(.giftreggie-pdp-registry-cta):not(.shopify-payment-button__button--unbranded).subheading {
  text-decoration: none;
}
[dir=ltr] .rte a:not(.button):not(.swym-add-to-wishlist):not(.giftreggie-pdp-registry-cta):not(.shopify-payment-button__button--unbranded).subheading:hover, [dir=ltr] .rte .a:not(.button):not(.swym-add-to-wishlist):not(.giftreggie-pdp-registry-cta):not(.shopify-payment-button__button--unbranded).subheading:hover,
[dir=rtl] .rte a:not(.button):not(.swym-add-to-wishlist):not(.giftreggie-pdp-registry-cta):not(.shopify-payment-button__button--unbranded).subheading:hover,
[dir=rtl] .rte .a:not(.button):not(.swym-add-to-wishlist):not(.giftreggie-pdp-registry-cta):not(.shopify-payment-button__button--unbranded).subheading:hover {
  color: var(---color--primary);
}
[dir=ltr] .rte blockquote, [dir=ltr] .rte .blockquote,
[dir=ltr] .rte blockquote p, [dir=ltr] .rte .blockquote p,
[dir=rtl] .rte blockquote,
[dir=rtl] .rte .blockquote,
[dir=rtl] .rte blockquote p,
[dir=rtl] .rte .blockquote p {
  font-family: var(---font-family-body);
  font-style: var(---font-style-body);
  font-weight: var(---font-weight-body--bolder);
  letter-spacing: var(---letter-spacing-body--mobile);
  line-height: var(---line-height-body--mobile);
  font-size: var(---font-size-body-large--mobile);
}
[dir=ltr] .rte blockquote a, [dir=ltr] .rte .blockquote a,
[dir=ltr] .rte blockquote p a, [dir=ltr] .rte .blockquote p a,
[dir=rtl] .rte blockquote a,
[dir=rtl] .rte .blockquote a,
[dir=rtl] .rte blockquote p a,
[dir=rtl] .rte .blockquote p a {
  color: var(---color-link);
}
@media only screen and (min-width: 741px) {
  [dir=ltr] .rte blockquote, [dir=ltr] .rte .blockquote,
[dir=ltr] .rte blockquote p, [dir=ltr] .rte .blockquote p,
[dir=rtl] .rte blockquote,
[dir=rtl] .rte .blockquote,
[dir=rtl] .rte blockquote p,
[dir=rtl] .rte .blockquote p {
    letter-spacing: var(---letter-spacing-heading--desktop);
    line-height: var(---line-height-heading--desktop);
  }
}
@media only screen and (min-width: 741px) {
  [dir=ltr] .rte blockquote, [dir=ltr] .rte .blockquote,
[dir=ltr] .rte blockquote p, [dir=ltr] .rte .blockquote p,
[dir=rtl] .rte blockquote,
[dir=rtl] .rte .blockquote,
[dir=rtl] .rte blockquote p,
[dir=rtl] .rte .blockquote p {
    font-size: var(---font-size-body-large--desktop);
  }
}
[dir=ltr] .rte ul, [dir=ltr] .rte .ul,
[dir=ltr] .rte ol, [dir=ltr] .rte .ol,
[dir=rtl] .rte ul,
[dir=rtl] .rte .ul,
[dir=rtl] .rte ol,
[dir=rtl] .rte .ol {
  margin: 1.5em 0;
  padding-left: 1.5em;
}
[dir=ltr] .rte ul:first-child, [dir=ltr] .rte ul:last-child, [dir=ltr] .rte .ul:first-child, [dir=ltr] .rte .ul:last-child,
[dir=ltr] .rte ol:first-child,
[dir=ltr] .rte ol:last-child, [dir=ltr] .rte .ol:first-child, [dir=ltr] .rte .ol:last-child,
[dir=rtl] .rte ul:first-child,
[dir=rtl] .rte ul:last-child,
[dir=rtl] .rte .ul:first-child,
[dir=rtl] .rte .ul:last-child,
[dir=rtl] .rte ol:first-child,
[dir=rtl] .rte ol:last-child,
[dir=rtl] .rte .ol:first-child,
[dir=rtl] .rte .ol:last-child {
  margin: 0.5em 0;
}
[dir=ltr] .rte ul ul:first-child, [dir=ltr] .rte ul .ul:first-child,
[dir=ltr] .rte ul ol:first-child, [dir=ltr] .rte ul .ol:first-child, [dir=ltr] .rte .ul ul:first-child, [dir=ltr] .rte .ul .ul:first-child,
[dir=ltr] .rte .ul ol:first-child, [dir=ltr] .rte .ul .ol:first-child,
[dir=ltr] .rte ol ul:first-child,
[dir=ltr] .rte ol .ul:first-child,
[dir=ltr] .rte ol ol:first-child,
[dir=ltr] .rte ol .ol:first-child, [dir=ltr] .rte .ol ul:first-child, [dir=ltr] .rte .ol .ul:first-child,
[dir=ltr] .rte .ol ol:first-child, [dir=ltr] .rte .ol .ol:first-child,
[dir=rtl] .rte ul ul:first-child,
[dir=rtl] .rte ul .ul:first-child,
[dir=rtl] .rte ul ol:first-child,
[dir=rtl] .rte ul .ol:first-child,
[dir=rtl] .rte .ul ul:first-child,
[dir=rtl] .rte .ul .ul:first-child,
[dir=rtl] .rte .ul ol:first-child,
[dir=rtl] .rte .ul .ol:first-child,
[dir=rtl] .rte ol ul:first-child,
[dir=rtl] .rte ol .ul:first-child,
[dir=rtl] .rte ol ol:first-child,
[dir=rtl] .rte ol .ol:first-child,
[dir=rtl] .rte .ol ul:first-child,
[dir=rtl] .rte .ol .ul:first-child,
[dir=rtl] .rte .ol ol:first-child,
[dir=rtl] .rte .ol .ol:first-child {
  margin-top: 0.5em !important;
}
[dir=ltr] .rte ol li::marker, [dir=ltr] .rte .ol li::marker,
[dir=rtl] .rte ol li::marker,
[dir=rtl] .rte .ol li::marker {
  color: var(---color--secondary);
}
[dir=ltr] .rte ul li, [dir=ltr] .rte .ul li,
[dir=rtl] .rte ul li,
[dir=rtl] .rte .ul li {
  list-style-type: "●";
  padding-left: 0.75em;
}
[dir=ltr] .rte ul li::marker, [dir=ltr] .rte .ul li::marker,
[dir=rtl] .rte ul li::marker,
[dir=rtl] .rte .ul li::marker {
  content: "●";
  color: var(---color--secondary);
}

.note {
  --note-color: var(---color--default);
  --note-border-color: var(---color--secondary);
  --note-background-color: var(---background-color--tertiary);
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 1em 1.2em;
  margin: 1em auto;
  border-radius: 4px;
  color: var(--note-color);
  background-color: var(--note-background-color);
  font-size: var(---font-size-body--mobile);
  /* ----- Styles ----- */
  /* ----- Sizes ----- */
}
@media only screen and (min-width: 1001px) {
  .note {
    font-size: var(---font-size-body--desktop);
  }
}
.note:first-child {
  margin-top: 0;
}
.note:last-child {
  margin-bottom: 0;
}
.note p {
  color: var(--note-color);
}
.note p:last-child {
  margin: 0;
}
.note.note--primary {
  --note-color: var(---color--primary);
  --note-border-color: var(---color--primary);
  --note-background-color: var(---background-color--primary);
}
.note.note--secondary {
  --note-color: var(---color--secondary);
  --note-border-color: var(---color--secondary);
  --note-background-color: var(---background-color--secondary);
}
.note.note--tertiary {
  --note-color: var(---color--tertiary);
  --note-border-color: var(---color--tertiary);
  --note-background-color: var(---background-color--tertiary);
}
.note.note--brand-1 {
  --note-color: var(---color--tertiary);
  --note-border-color: var(---color--tertiary);
  --note-background-color: var(---background-color--tertiary);
}
.note.note--success {
  --note-color: var(---color--success);
  --note-border-color: var(---color--success);
  --note-background-color: var(---background-color--success);
}
.note.note--warning {
  --note-color: var(---color--warning);
  --note-border-color: var(---color--warning);
  --note-background-color: var(---background-color--warning);
}
.note.note--danger {
  --note-color: var(---color--danger);
  --note-border-color: var(---color--danger);
  --note-background-color: var(---background-color--danger);
}
.note.note--info {
  --note-color: var(---color--info);
  --note-border-color: var(---color--info);
  --note-background-color: var(---background-color--info);
}
.note.note--error {
  --note-color: var(---color--danger);
  --note-border-color: var(---color--danger);
  --note-background-color: var(---background-color--danger);
}
.note.note--large {
  padding: 1.4em 1.8em;
  font-size: var(---font-size-body-large--mobile);
}
@media only screen and (min-width: 1001px) {
  .note.note--large {
    font-size: var(---font-size-body-large--desktop);
  }
}
.note.note--small {
  padding: 0.8em 1.4em;
  font-size: var(---font-size-body-small--mobile);
}
@media only screen and (min-width: 1001px) {
  .note.note--small {
    font-size: var(---font-size-body-small--desktop);
  }
}
.note.note--tiny {
  padding: 0.6em 0.8em;
  font-size: var(---font-size-body-xs--mobile);
}
@media only screen and (min-width: 1001px) {
  .note.note--tiny {
    font-size: var(---font-size-body-xs--desktop);
  }
}

.banner {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 12px 16px;
  border: 1px solid var(--text-color);
}
.banner.banner--small {
  display: flex;
  gap: 0.5em;
  margin: 10px 0;
  padding: 0.5em 1em;
  align-items: center;
}
.banner.banner--small .banner__ribbon {
  margin: 0;
}
.banner.banner--small .banner__content {
  font-size: var(---font-size-body-small--mobile);
}
@media only screen and (min-width: 1001px) {
  .banner.banner--small .banner__content {
    font-size: var(---font-size-body-small--desktop);
  }
}

.kyte-seal {
  position: absolute;
  right: 15%;
  top: 0;
  transform: translateY(-20%);
}
@media only screen and (max-width: 1000px) {
  .kyte-seal {
    width: 110px;
    height: 110px;
    right: 10%;
    transform: translateY(-55%);
  }
}
.kyte-seal .kyte-seal__text {
  -webkit-animation: rotate 10000ms linear infinite;
          animation: rotate 10000ms linear infinite;
  transform-origin: center;
}

.kyte-text-animation-container svg path {
  fill: transparent;
}
.kyte-text-animation-container text {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.kyte-text-animation.kyte-text-animation--1 {
  position: absolute;
  z-index: 2;
  bottom: 0;
  right: 0;
  margin: auto;
  transform: translate(0%, 80%) rotate(165deg);
  width: 650px;
  overflow: visible;
  pointer-events: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
@media (max-width: 1000px) {
  .kyte-text-animation.kyte-text-animation--1 {
    width: 300px;
  }
}
.kyte-text-animation.kyte-text-animation--1 text {
  font-size: 240px;
  fill: var(--text-animation-color, #ffffff) !important;
  font-weight: 400;
  letter-spacing: -0.025em;
  opacity: 0.8;
}
.kyte-text-animation.kyte-text-animation--2 {
  position: absolute;
  z-index: 2;
  bottom: 0;
  right: 0;
  margin: auto;
  width: 40% !important;
  top: 10% !important;
  right: 5% !important;
  bottom: unset !important;
  transform: translateY(0) rotate(160deg);
  z-index: 2;
  overflow: visible;
}
.kyte-text-animation.kyte-text-animation--2 text {
  font-size: 240px;
  font-weight: 400;
  letter-spacing: -0.025em;
  fill: var(--text-animation-color, #D9D9D9) !important;
  opacity: 0.5 !important;
}
@media (max-width: 1000px) {
  .kyte-text-animation.kyte-text-animation--2 {
    width: 60% !important;
    left: 0 !important;
    right: 0 !important;
  }
}
@media (max-width: 740px) {
  .kyte-text-animation.kyte-text-animation--2 {
    width: 80% !important;
    left: 0 !important;
    right: 0 !important;
    top: 5% !important;
  }
}

.section--meet-our-founder {
  position: relative;
  overflow: hidden;
}

.usp-icons {
  --gap: 20px;
  --padding: 30px;
}

.usp-icons__inner {
  display: flex;
  gap: var(--gap);
  -webkit-padding-start: var(--padding);
          padding-inline-start: var(--padding);
  -webkit-padding-end: 100vw;
          padding-inline-end: 100vw;
  -webkit-margin-start: calc(-1 * var(--padding));
          margin-inline-start: calc(-1 * var(--padding));
  -webkit-margin-end: calc(-1 * var(--padding));
          margin-inline-end: calc(-1 * var(--padding));
  flex-wrap: nowrap;
  overflow: scroll;
  gap: 10px;
  scroll-snap-type: x mandatory;
}
@media only screen and (min-width: 1001px) {
  .usp-icons__inner {
    flex-wrap: wrap;
    -webkit-padding-start: 0;
            padding-inline-start: 0;
    -webkit-padding-end: 0;
            padding-inline-end: 0;
  }
}

.usp-icon {
  display: flex;
  gap: 0.5em;
  scroll-snap-align: start;
  scroll-snap-stop: always;
  -webkit-padding-start: var(--padding);
          padding-inline-start: var(--padding);
}
@media only screen and (max-width: 1000px) {
  .usp-icon {
    flex: 1 0 auto;
  }
}
@media only screen and (min-width: 1001px) {
  .usp-icon {
    max-width: calc(50% - var(--gap));
    -webkit-padding-start: var(--padding);
            padding-inline-start: var(--padding);
    gap: 0.25em;
  }
}
.usp-icon .usp-icon__icon svg,
.usp-icon .usp-icon__icon img {
  margin: 0;
  width: 50px;
  min-width: 50px;
}
.usp-icon .usp-icon__details {
  display: flex;
  justify-content: center;
  flex-direction: column;
  flex: 1 0 auto;
  gap: 0.25em;
}

.image-overlay__usp-container {
  margin-top: 24px;
}

.key-ingredient-item {
  --card-width: 300px;
  display: block;
  width: 80vw;
  max-width: var(--card-width);
  border-radius: var(--block-border-radius-reduced);
  border: 1px solid var(---color-line);
  box-shadow: var(--block-shadow--card);
  scroll-snap-align: start;
  scroll-snap-stop: always;
  overflow: hidden;
  background: #fff;
}
@media (min-width: 800px) {
  .key-ingredient-item {
    --card-width: 360px;
    width: 40vw;
  }
}
.key-ingredient-item .key-ingredient-item__inner {
  display: flex;
  flex-direction: column;
}
.key-ingredient-item .key-ingredient-item__image {
  border-radius: 0;
  -o-object-fit: cover;
     object-fit: cover;
}
.key-ingredient-item .key-ingredient-item__caption {
  --padding: 30px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: var(--padding);
  margin-top: 0;
}
.key-ingredient-item .key-ingredient-item__caption > * {
  margin: 0;
}

.custom-ingredients-slider .gallery__list {
  padding-block: 20px;
  scroll-snap-type: x mandatory;
}

.benefit-icons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.4em 1em;
}

.benefit-icon {
  display: flex;
  align-items: center;
  gap: 0.4em;
}

.benefit-icon__icon img {
  width: 32px;
}

/* ----- Bubbles ----- */
.bubble-count {
  background: var(---color--primary);
  color: var(---color-text--reversed);
  font-size: var(---font-size-body-xs--mobile);
  font-weight: var(---font-weight-body--bold);
  letter-spacing: -1px;
}
.bubble-count[data-cart-count="0"], .bubble-count:empty {
  display: none;
}

/* ----- Carousel Buttons ----- */
.prev-next-button {
  --prev-next-button-background: var(---background-color--content-1--rgb);
  --prev-next-button-border-color: var(---color-line--rgb);
  --prev-next-button-text-color: var(---color-text--rgb);
  width: 40px;
  height: 40px;
  border: 1px solid rgba(var(--prev-next-button-border-color), 1);
  border-radius: 100%;
}
.prev-next-button svg {
  color: RGB(var(--prev-next-button-text-color));
}
.prev-next-button:not(.prev-next-button--small) {
  width: 40px;
  height: 40px;
}

/* ----- Tables ----- */
.line-item-table .line-item > td {
  padding-top: 2em;
}
.line-item-table .line-item:hover {
  background: transparent;
}

/* ----- HRs ----- */
hr, .hr {
  width: 100%;
  margin: 2em 0;
  border-width: 0.5px;
  border-bottom: 0;
  border-style: solid;
  border-color: var(---color-line);
}
hr.hr--light, .hr.hr--light {
  border-color: var(---color-line--light);
}
hr.hr--dark, .hr.hr--dark {
  border-color: var(---color-line--dark);
}
hr.hr--clear, .hr.hr--clear {
  border-color: transparent;
}
hr.hr--small, .hr.hr--small {
  margin: 1em 0;
}
hr.hr--nomargin, .hr.hr--nomargin {
  margin: 0;
}

/* ----- Button Wrapper ----- */
.button-wrapper {
  display: inline-flex;
  flex-wrap: wrap;
  gap: 1em;
  margin: 0;
  justify-content: center;
}
.button-wrapper .button, .button-wrapper .swym-add-to-wishlist,
.button-wrapper .giftreggie-pdp-registry-cta, .button-wrapper .drawer .popover .wk-button, .drawer .popover .button-wrapper .wk-button, .button-wrapper .shopify-payment-button__button--unbranded {
  margin-top: 0;
  margin-bottom: 0;
  margin-right: 0;
  margin-left: 0;
}
.button-wrapper .button:last-child, .button-wrapper .swym-add-to-wishlist:last-child,
.button-wrapper .giftreggie-pdp-registry-cta:last-child, .button-wrapper .drawer .popover .wk-button:last-child, .drawer .popover .button-wrapper .wk-button:last-child, .button-wrapper .shopify-payment-button__button--unbranded:last-child {
  margin-right: 0;
}
.button-wrapper .button:only-child, .button-wrapper .swym-add-to-wishlist:only-child,
.button-wrapper .giftreggie-pdp-registry-cta:only-child, .button-wrapper .drawer .popover .wk-button:only-child, .drawer .popover .button-wrapper .wk-button:only-child, .button-wrapper .shopify-payment-button__button--unbranded:only-child {
  margin-bottom: 0;
}

.text--left .button-wrapper {
  justify-content: flex-start;
}

.text--right .button-wrapper {
  justify-content: flex-end;
}

.heading + .button-wrapper, .tolstoy-stories .tolstoy-stories-title + .button-wrapper,
.heading + .button-group,
.tolstoy-stories .tolstoy-stories-title + .button-group,
p + .button-wrapper,
p + .button-group,
.button-wrapper + p,
.button-group + p {
  margin-top: 0;
}

/* ----- Captcha ----- */
.square-separator {
  width: 6px;
  height: 6px;
  border-radius: 100%;
  opacity: 1;
}

/* ----- Captcha ----- */
.mobile-share-buttons__item--pinterest svg *,
.mobile-share-buttons__item--facebook svg * {
  fill: var(---color-text);
}

@media only screen and (min-width: 1001px) {
  .text-container p + form,
.text-container .rte p + form {
    margin-top: 16px;
  }
}

/* ----- Tooltips ----- */
[data-tooltip-position=bottom]:before {
  bottom: calc(-100% + 6px);
  left: 50%;
  transform: translateX(-50%);
}

[data-tooltip-position=bottom]:after {
  bottom: calc(-30% + 1px);
  left: calc(50% - 7px);
  transform: rotate(135deg);
}

prev-next-buttons {
  top: 50% !important;
  transform: translateY(-50%);
}

.section-shadow {
  box-shadow: var(--section-shadow);
}

.section-botanicals-bottom-1 {
  background-image: url(https://cdn.shopify.com/s/files/1/0019/7106/0847/files/section-botanicals-bottom-1-combined.svg?v=1761058266);
  background-position: bottom;
  background-size: 100%;
  background-repeat: no-repeat;
}

.section-botanicals-top-1 {
  background-image: url(https://cdn.shopify.com/s/files/1/0019/7106/0847/files/section-botanicals-top-1-combined.svg?v=1761058366);
  background-position: top;
  background-size: 100%;
  background-repeat: no-repeat;
}

/* 9. Apps  */
.loop-returns-activated .loop--hide {
  display: none !important;
}
.loop-returns-activated .loop--display-flex {
  display: flex !important;
}
.loop-returns-activated .loop-returns-mode-only {
  display: block !important;
}
.loop-returns-activated .product-sticky-form__payment-container,
.loop-returns-activated .quantity-atc--bundle #MainPaymentContainer,
.loop-returns-activated .loop--disabled {
  opacity: 0.5;
  pointer-events: none;
}

.product-form--has-unavailable-for-returns-tag .giftreggie-pdp-cta-area,
.product-form--has-unavailable-for-returns-tag square-placement,
.product-form--has-unavailable-for-returns-tag .loop--unavailable-for-returns {
  display: none !important;
}

.loop-returns-mode-only {
  display: none !important;
}

@media only screen and (max-width: 740px) {
  #insta-feed {
    display: flex !important;
    padding: 0 var(--container-gutter);
    overflow: scroll;
    gap: 10px;
    -ms-overflow-style: none;
    /* Internet Explorer 10+ */
    scrollbar-width: none;
    /* Firefox */
  }
  #insta-feed::-webkit-scrollbar {
    display: none;
    /* Safari and Chrome */
  }
  #insta-feed > a {
    min-height: 110px;
    min-width: 110px;
    z-index: 9998 !important;
  }
  #insta-feed > a > div {
    width: 100% !important;
    height: 100%;
  }
}
#insta-feed .instafeed-container .instafeed-overlay:before,
#insta-feed .instafeed-container .instafeed-overlay:after,
#insta-feed .instafeed-container img {
  border-radius: 12px;
}

@media only screen and (max-width: 1000px) {
  .instafeed-section {
    margin-top: 0;
  }
}
@media only screen and (max-width: 1000px) {
  .instafeed-section p {
    padding-left: var(--container-gutter);
    padding-right: var(--container-gutter);
  }
}

/* ========== Product Page ========== */
/* ----- Points per Product Widget ----- */
.product-loyalty-earnings {
  display: flex;
  gap: 10px;
}

.product-loyalty-earnings__content {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.product-loyalty-earnings__content-links {
  display: flex;
  align-items: center;
  gap: 10px;
}

.okeReviews {
  padding-block: 0 !important;
}

[data-oke-star-rating] .okeReviews > div {
  display: flex;
  align-items: center;
}
[data-oke-star-rating] .oke-sr-count {
  font-size: var(---font-size-body-small--desktop);
  color: var(---color-text--light);
}

.product__info .oke-stars svg {
  width: 65px;
}

.product-item .oke-stars svg {
  width: 65px;
}

.tolstoy-stories .tolstoy-stories-title {
  text-align: center !important;
}

/*  ==============================
    Gift Reggie
    ============================== */
.giftreggie-front {
  --section-background: 251, 246, 243;
  /* ----- Input Fixes ----- */
}
.giftreggie-front .input__field + .input__label {
  transform: scale(0.733) translateY(calc(-24px - 0.5em)) translateX(3.665px);
}

/*  -----------------------------------
    Landing
    ----------------------------------- */
.giftreggie-landing .giftreggie-landing-overview {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  border-radius: var(--block-border-radius);
  overflow: hidden;
}
.giftreggie-landing .giftreggie-landing-overview .section__color-wrapper {
  --section-background: 251, 246, 243;
}
@media (max-width: 1000px) {
  .giftreggie-landing .giftreggie-landing-overview {
    display: flex;
    flex-direction: column;
  }
}
.giftreggie-landing .giftreggie-landing-overview__links-inner {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: var(--container-gutter);
}
.giftreggie-landing .giftreggie-landing-overview__image img {
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.giftreggie-landing .giftreggie-landing-overview__links {
  align-items: center;
  display: flex;
  width: 100%;
}
.giftreggie-landing .giftreggie-landing-overview__links:hover .gift-reggie-landing-overview__link, .giftreggie-landing .giftreggie-landing-overview__links:focus-within .gift-reggie-landing-overview__link {
  opacity: 0.5;
}
.giftreggie-landing .giftreggie-landing-overview__links:hover .gift-reggie-landing-overview__link:hover, .giftreggie-landing .giftreggie-landing-overview__links:hover .gift-reggie-landing-overview__link:focus, .giftreggie-landing .giftreggie-landing-overview__links:focus-within .gift-reggie-landing-overview__link:hover, .giftreggie-landing .giftreggie-landing-overview__links:focus-within .gift-reggie-landing-overview__link:focus {
  opacity: 1;
}
.giftreggie-landing .gift-reggie-landing-overview__link {
  width: 100%;
  padding: 20px;
  margin: 10px 0;
  background: #fff;
  border: 1px solid var(---color-line);
  border-radius: var(--block-border-radius);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.04);
  transition: transform 0.25s, opacity 0.25s;
}
.giftreggie-landing .gift-reggie-landing-overview__link h4 {
  margin: 0;
}
.giftreggie-landing .gift-reggie-landing-overview__link p {
  color: var(---color-heading-3);
  margin: 0;
}
.giftreggie-landing .gift-reggie-landing-overview__link:hover {
  transform: translateX(10px);
}

/*  -----------------------------------
    Find Registry
    ----------------------------------- */
/*  -----------------------------------
    View Registry
    ----------------------------------- */
.giftreggie-view-registry.giftreggie-desktop {
  text-align: left;
  border: 1px solid var(---color-line);
  margin-bottom: var(--container-gutter);
}
.giftreggie-view-registry.giftreggie-desktop th,
.giftreggie-view-registry.giftreggie-desktop td {
  padding: 1em 1.5em;
}
.giftreggie-view-registry.giftreggie-desktop td {
  padding-top: 0;
}
.giftreggie-view-registry.giftreggie-desktop .input__field {
  min-width: unset;
  width: unset;
  max-width: 60px;
}
/*  -----------------------------------
    Edit Registry
    ----------------------------------- */
.giftreggie-registry-summary .giftreggie-desktop {
  text-align: left;
  border: 1px solid var(---color-line);
  margin-bottom: var(--container-gutter);
}
.giftreggie-registry-summary .giftreggie-desktop th,
.giftreggie-registry-summary .giftreggie-desktop td {
  padding: 1em 1.5em;
}
.giftreggie-registry-summary .giftreggie-desktop td {
  padding-top: 0;
}
.giftreggie-registry-summary .giftreggie-desktop th {
  padding-bottom: 0;
}
.giftreggie-registry-summary .giftreggie-mobile {
  margin-top: var(--container-gutter);
  padding: var(--container-gutter);
  border: 1px solid var(---color-line);
}

/*  -----------------------------------
    Components
    ----------------------------------- */
/* ----- Profile Block ----- */
.registry-profile-block {
  padding: var(--container-gutter);
  margin: var(--container-gutter) 0;
  background: RGB(var(--section-background));
  border-radius: var(--block-border-radius);
}
.registry-profile-block .input {
  --section-background: var(---background-color--content-1--rgb);
}
.registry-profile-block .input select,
.registry-profile-block .input textarea,
.registry-profile-block .input textarea,
.registry-profile-block .input input[type=file],
.registry-profile-block .input input[type=password],
.registry-profile-block .input input[type=text] {
  background: RGB(var(--section-background));
}

/* ----- Entry Form ----- */
.giftreggie-entry-form {
  margin: 20px 0;
  padding: var(--container-gutter);
  background: RGB(var(--section-background));
  border-radius: var(--block-border-radius);
}
.giftreggie-entry-form .input {
  --section-background: var(---background-color--content-1--rgb);
}
.giftreggie-entry-form .input select,
.giftreggie-entry-form .input textarea,
.giftreggie-entry-form .input input[type=file],
.giftreggie-entry-form .input input[type=password],
.giftreggie-entry-form .input input[type=text] {
  background: RGB(var(--section-background));
}

/* ----- Mobile Tables ----- */
.giftreggie-mobile ul {
  padding: 0;
}
.giftreggie-mobile li {
  margin-top: -1px;
  border: 1px solid var(---color-line);
  padding: var(--container-gutter);
  list-style: none;
}

/* ----- Grid ----- */
.registry-profile-grid {
  display: grid;
  width: 100%;
  gap: var(--container-gutter);
}
.registry-profile-grid span {
  display: block;
  width: 100% !important;
}
@media (min-width: 1000px) {
  .registry-profile-grid {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
.registry-profile-grid .input__field {
  width: 100% !important;
}

/* ----- Floating Header (Registry) ----- */
.giftreggie-floating-header {
  position: -webkit-sticky;
  position: sticky;
  z-index: 1;
  top: var(--header-height);
  background: var(---background-color--content-2);
}
.giftreggie-floating-header h3 {
  margin: 0;
}
.giftreggie-floating-header .giftreggie-header {
  display: flex;
  justify-content: space-between;
  padding: 20px 0;
}
.giftreggie-floating-header .giftreggie-body {
  padding: 0 !important;
}
.giftreggie-floating-header .giftreggie-admin-menu {
  margin-left: auto;
}
@media (max-width: 1000px) {
  .giftreggie-floating-header .giftreggie-header {
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }
  .giftreggie-floating-header .giftreggie-header h3 {
    text-align: center;
  }
}

.giftreggie-no-products {
  padding: 10px;
  border-radius: var(--block-border-radius);
  background: RGB(var(---background-color--content-2--rgb));
}

/* ----- Notice Banner ----- */
.giftreggie-notice-banner {
  background: var(---background-color--content-2);
  padding: 20px 30px;
  border-radius: var(--block-border-radius);
}

/*  -----------------------------------
    Extends
    ----------------------------------- */
.swym-add-to-wishlist,
.giftreggie-pdp-registry-cta {
  justify-content: center !important;
}

@media only screen and (max-width: 740px) {
  .product-meta__price-list-container square-placement {
    display: none !important;
  }
}

.shopify-app-block square-placement {
  opacity: 0.5;
}
@media only screen and (min-width: 741px) {
  .shopify-app-block square-placement {
    display: none !important;
  }
}

.shopify-section--apps .section__box-color-wrapper {
  background: RGB(var(--box-background));
  border-radius: var(--block-border-radius);
}
.shopify-section--apps .section--apps .text-container {
  margin-bottom: 30px;
}

@media only screen and (max-width: 740px) {
  .shopify-app-block .container--no-horizontal-padding--mobile p {
    padding-left: var(--container-gutter);
    padding-right: var(--container-gutter);
  }
}

/* --HIDE APPS-- */
.octane-plugin__checkbox,
shopify-payment-terms {
  display: none !important;
}

.swym-wishlist-button-bar {
  padding: 30px 0;
  text-align: center;
}

.swym-added.swym-add-to-wishlist,
.swym-added {
  opacity: 1 !important;
  color: var(---color--tertiary) !important;
}

.product__info square-placement {
  text-align: center;
}

.tolstoy-stories-main-container .tolstoy-stories-container .tolstoy-stories-previous-button,
.tolstoy-stories-main-container .tolstoy-stories-container .tolstoy-stories-next-button {
  z-index: 2;
}

/* 10. Utility Classes */
/*================ Build Base Grid Classes ================*/
.shown {
  display: block !important;
}

.hidden {
  display: none !important;
}

.text-left {
  text-align: left !important;
}

.text-right {
  text-align: right !important;
}

.text-center {
  text-align: center !important;
}

/*================ Build Responsive Grid Classes ================*/
@media only screen and (min-width: 741px) and (max-width: 999px) {
  .small--shown {
    display: block !important;
  }
  .small--hidden {
    display: none !important;
  }
  .small--text-left {
    text-align: left !important;
  }
  .small--text-right {
    text-align: right !important;
  }
  .small--text-center {
    text-align: center !important;
  }
  .br--small {
    display: block;
  }
}
@media only screen and (min-width: 741px) {
  .small-up--shown {
    display: block !important;
  }
  .small-up--hidden {
    display: none !important;
  }
  .small-up--text-left {
    text-align: left !important;
  }
  .small-up--text-right {
    text-align: right !important;
  }
  .small-up--text-center {
    text-align: center !important;
  }
  .br--small-up {
    display: block;
  }
}
@media only screen and (max-width: 740px) {
  .small-down--shown {
    display: block !important;
  }
  .small-down--hidden {
    display: none !important;
  }
  .small-down--text-left {
    text-align: left !important;
  }
  .small-down--text-right {
    text-align: right !important;
  }
  .small-down--text-center {
    text-align: center !important;
  }
  .br--small-down {
    display: block;
  }
}
@media only screen and (min-width: 1001px) and (max-width: 1199px) {
  .medium--shown {
    display: block !important;
  }
  .medium--hidden {
    display: none !important;
  }
  .medium--text-left {
    text-align: left !important;
  }
  .medium--text-right {
    text-align: right !important;
  }
  .medium--text-center {
    text-align: center !important;
  }
  .br--medium {
    display: block;
  }
}
@media only screen and (min-width: 1001px) {
  .medium-up--shown {
    display: block !important;
  }
  .medium-up--hidden {
    display: none !important;
  }
  .medium-up--text-left {
    text-align: left !important;
  }
  .medium-up--text-right {
    text-align: right !important;
  }
  .medium-up--text-center {
    text-align: center !important;
  }
  .br--medium-up {
    display: block;
  }
}
@media only screen and (max-width: 1000px) {
  .medium-down--shown {
    display: block !important;
  }
  .medium-down--hidden {
    display: none !important;
  }
  .medium-down--text-left {
    text-align: left !important;
  }
  .medium-down--text-right {
    text-align: right !important;
  }
  .medium-down--text-center {
    text-align: center !important;
  }
  .br--medium-down {
    display: block;
  }
}
@media only screen and (min-width: 1201px) and (max-width: 1399px) {
  .large--shown {
    display: block !important;
  }
  .large--hidden {
    display: none !important;
  }
  .large--text-left {
    text-align: left !important;
  }
  .large--text-right {
    text-align: right !important;
  }
  .large--text-center {
    text-align: center !important;
  }
  .br--large {
    display: block;
  }
}
@media only screen and (min-width: 1201px) {
  .large-up--shown {
    display: block !important;
  }
  .large-up--hidden {
    display: none !important;
  }
  .large-up--text-left {
    text-align: left !important;
  }
  .large-up--text-right {
    text-align: right !important;
  }
  .large-up--text-center {
    text-align: center !important;
  }
  .br--large-up {
    display: block;
  }
}
@media only screen and (min-width: 1401px) {
  .wide-up--shown {
    display: block !important;
  }
  .wide-up--hidden {
    display: none !important;
  }
  .wide-up--text-left {
    text-align: left !important;
  }
  .wide-up--text-right {
    text-align: right !important;
  }
  .wide-up--text-center {
    text-align: center !important;
  }
  .br--wide-up {
    display: block;
  }
}
.clearfix {
  *zoom: 1;
}
.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

.fallback-text,
.visually-hidden {
  position: absolute !important;
  overflow: hidden;
  clip: rect(0 0 0 0);
  height: 1px;
  width: 1px;
  max-width: 1px;
  max-height: 1px;
  font-size: 0;
  margin: -1px;
  padding: 0;
  border: 0;
}

.hidden {
  display: none;
}

.flex-column {
  flex-direction: column;
}

.justify-content-start {
  justify-content: start;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-end {
  justify-content: end;
}

.align-items-start {
  align-items: start;
}

.align-items-center {
  align-items: center;
}

.align-items-end {
  align-items: end;
}

.uppercase,
.text-transform--uppercase {
  text-transform: uppercase !important;
}

.text-transform--none {
  text-transform: none !important;
}

.strikethrough {
  text-decoration: line-through;
}

.color--primary {
  color: var(---color--primary) !important;
}

.color--secondary {
  color: var(---color--secondary) !important;
}

.color--tertiary {
  color: var(---color--tertiary) !important;
}

.color--brand-1 {
  color: var(---color--brand-1) !important;
}

.color--brand-2 {
  color: var(---color--brand-2) !important;
}

.color--brand-3 {
  color: var(---color--brand-3) !important;
}

.color--brand-4 {
  color: var(---color--brand-4) !important;
}

.color--brand-5 {
  color: var(---color--brand-5) !important;
}

.color--brand-6 {
  color: var(---color--brand-6) !important;
}

.color--brand-7 {
  color: var(---color--brand-7) !important;
}

.color--kyte-black {
  color: var(---color--kyte-black) !important;
}

.color--kyte-dark-grey {
  color: var(---color--kyte-dark-grey) !important;
}

.color--kyte-light-grey {
  color: var(---color--kyte-light-grey) !important;
}

.color--kyte-white {
  color: var(---color--kyte-white) !important;
}

.color--kyte-cream {
  color: var(---color--kyte-cream) !important;
}

.color--kyte-dark-cream {
  color: var(---color--kyte-dark-cream) !important;
}

.weight-normal {
  font-weight: normal !important;
}

.background-color--default {
  background: var(---color--default);
}

.background-color--primary {
  background: var(---color--primary);
}

.background-color--secondary {
  background: var(---color--secondary);
}

.background-color--tertiary {
  background: var(---color--tertiary);
}

.background-color--success {
  background: var(---color--success);
}

.background-color--warning {
  background: var(---color--warning);
}

.background-color--danger {
  background: var(---color--danger);
}

.background-color--info {
  background: var(---color--info);
}

.background-color--link {
  background: var(---color--link);
}

.justify-content-center {
  justify-content: center !important;
}

.object-position--top {
  -o-object-position: top !important;
     object-position: top !important;
}

.object-position--bottom {
  -o-object-position: bottom !important;
     object-position: bottom !important;
}

.object-position--center {
  -o-object-position: center !important;
     object-position: center !important;
}

.object-position--left {
  -o-object-position: left !important;
     object-position: left !important;
}

.object-position--right {
  -o-object-position: right !important;
     object-position: right !important;
}

.text-align--center {
  text-align: center !important;
}

.text-align--left {
  text-align: left !important;
}

.text-align--right {
  text-align: right !important;
}

@media only screen and (max-width: 740px) {
  .text-align--center--mobile {
    text-align: center !important;
  }
}

@media only screen and (max-width: 740px) {
  .text-align--left--mobile {
    text-align: left !important;
  }
}

@media only screen and (max-width: 740px) {
  .text-align--right--mobile {
    text-align: right !important;
  }
}

.mix-blend-mode--multiply {
  mix-blend-mode: multiply;
}

.no-margin {
  margin: 0 !important;
}

.no-margin--top {
  margin-top: 0 !important;
}

.no-margin--right {
  margin-right: 0 !important;
}

.no-margin--left {
  margin-left: 0 !important;
}

.no-margin--bottom {
  margin-bottom: 0 !important;
}

.no-padding {
  padding: 0 !important;
}

.no-padding--top {
  padding-top: 0 !important;
}

.no-padding--right {
  padding-right: 0 !important;
}

.no-padding--left {
  padding-left: 0 !important;
}

.no-padding--bottom {
  padding-bottom: 0 !important;
}

@media only screen and (max-width: 740px) {
  .no-padding--mobile {
    padding: 0 !important;
  }
}

@media only screen and (max-width: 740px) {
  .no-margin--mobile {
    margin: 0 !important;
  }
}

.padding-top--10 {
  padding-top: 10px !important;
}

.padding-right--10 {
  padding-right: 10px !important;
}

.padding-bottom--10 {
  padding-bottom: 10px !important;
}

.padding-left--10 {
  padding-left: 10px !important;
}

.margin-top--10 {
  margin-top: 10px !important;
}

.margin-right--10 {
  margin-right: 10px !important;
}

.margin-bottom--10 {
  margin-bottom: 10px !important;
}

.margin-left--10 {
  margin-left: 10px !important;
}

.padding-top--20 {
  padding-top: 20px !important;
}

.padding-right--20 {
  padding-right: 20px !important;
}

.padding-bottom--20 {
  padding-bottom: 20px !important;
}

.padding-left--20 {
  padding-left: 20px !important;
}

.margin-top--20 {
  margin-top: 20px !important;
}

.margin-right--20 {
  margin-right: 20px !important;
}

.margin-bottom--20 {
  margin-bottom: 20px !important;
}

.margin-left--20 {
  margin-left: 20px !important;
}

.padding-top--30 {
  padding-top: 30px !important;
}

.padding-right--30 {
  padding-right: 30px !important;
}

.padding-bottom--30 {
  padding-bottom: 30px !important;
}

.padding-left--30 {
  padding-left: 30px !important;
}

.margin-top--30 {
  margin-top: 30px !important;
}

.margin-right--30 {
  margin-right: 30px !important;
}

.margin-bottom--30 {
  margin-bottom: 30px !important;
}

.margin-left--30 {
  margin-left: 30px !important;
}

.padding-top--40 {
  padding-top: 40px !important;
}

.padding-right--40 {
  padding-right: 40px !important;
}

.padding-bottom--40 {
  padding-bottom: 40px !important;
}

.padding-left--40 {
  padding-left: 40px !important;
}

.margin-top--40 {
  margin-top: 40px !important;
}

.margin-right--40 {
  margin-right: 40px !important;
}

.margin-bottom--40 {
  margin-bottom: 40px !important;
}

.margin-left--40 {
  margin-left: 40px !important;
}

.padding-top--50 {
  padding-top: 50px !important;
}

.padding-right--50 {
  padding-right: 50px !important;
}

.padding-bottom--50 {
  padding-bottom: 50px !important;
}

.padding-left--50 {
  padding-left: 50px !important;
}

.margin-top--50 {
  margin-top: 50px !important;
}

.margin-right--50 {
  margin-right: 50px !important;
}

.margin-bottom--50 {
  margin-bottom: 50px !important;
}

.margin-left--50 {
  margin-left: 50px !important;
}

body.logged-in .logged-in--hidden {
  display: none !important;
}

body.logged-out .logged-out--hidden {
  display: none !important;
}

/* 11. Third-Party Styles */
.tippy-content {
  padding: 1em;
  text-align: center;
}
.tippy-content p {
  font-size: var(---font-size-body-small--mobile);
}

.tippy-box {
  --tooltip-box-shadow: 0 3px 10px rgba(0, 0, 0, .10);
  font-size: var(---font-size-body--mobile);
  background: var(--tooltip-background-color);
  color: var(--tooltip-text-color);
  border-radius: var(--tooltip-border-radius);
  box-shadow: var(--tooltip-box-shadow);
}
@media only screen and (min-width: 1001px) {
  .tippy-box {
    font-size: var(---font-size-body--desktop);
  }
}
.tippy-box[data-placement^=bottom] > .tippy-arrow:before {
  border-bottom-color: var(--tooltip-background-color);
}
.tippy-box[data-theme~=kyte--light] {
  --tooltip-background-color: var(---color--brand-4);
  --tooltip-text-color: var(---color-text);
  --tooltip-border-radius: var(--block-border-radius);
}
.tippy-box[data-theme~=kyte--dark] {
  --tooltip-text-color: var(---color--brand-3);
  --tooltip-background-color: var(---color-text);
  --tooltip-border-radius: var(--block-border-radius);
}

/* 12. Animations */
@-webkit-keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@-webkit-keyframes fade-out {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes fade-out {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@-webkit-keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@-webkit-keyframes floating-badge-in {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
@keyframes floating-badge-in {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}