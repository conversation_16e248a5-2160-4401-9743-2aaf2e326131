{"version": 3, "sources": ["custom.js"], "names": ["window", "theme", "utils", "compact", "array", "index", "length", "resIndex", "result", "value", "Shopify", "Theme", "selectors", "productForm", "sections", "sectionName", "_init", "section", "this", "container", "console", "log", "onLoad", "functions", "fadeOut", "elem", "ms", "opacity", "timer", "setInterval", "clearInterval", "style", "display", "visibility", "filter", "components", "tooltips", "tippy", "placement", "addEventListener"], "mappings": "AAAAA,OAAAC,MAAAD,OAAAC,OAAA,GAMAA,MAAAC,MAAA,CAQAC,QAAA,SAAAC,GAMA,IALA,IAAAC,GAAA,EACAC,EAAA,MAAAF,EAAA,EAAAA,EAAAE,OACAC,EAAA,EACAC,EAAA,KAEAH,EAAAC,GAAA,CACA,IAAAG,EAAAL,EAAAC,GACAI,IACAD,EAAAD,KAAAE,GAGA,OAAAD,IAMAE,QAAAC,MAAA,CAEAC,UAAA,CAEAC,YAAA,iBAIAC,SAAA,CAEAC,YAAA,CAEAC,MAAA,WAIA,IAAAC,EAAAC,KAAAC,UAEAC,QAAAC,IAAAJ,IAKAK,OAAA,WAEAJ,KAAAF,WAQAO,UAAA,CAEAC,QAAA,SAAAC,EAAAC,GACA,IAKAC,EACAC,EANAH,IAGAC,GAEAC,EAAA,EACAC,EAAAC,YAAA,YACAF,GAAA,GAAAD,IACA,IAEAI,cAAAF,GACAD,EAAA,EACAF,EAAAM,MAAAC,QAAA,OACAP,EAAAM,MAAAE,WAAA,UAEAR,EAAAM,MAAAJ,QAAAA,EACAF,EAAAM,MAAAG,OAAA,iBAAA,IAAAP,EAAA,KACA,MAIAF,EAAAM,MAAAJ,QAAA,EACAF,EAAAM,MAAAG,OAAA,mBACAT,EAAAM,MAAAC,QAAA,OACAP,EAAAM,MAAAE,WAAA,aAMAE,WAAA,CAEAC,SAAA,WAEAC,MAAA,uBAAA,CACApC,MAAA,OACAqC,UAAA,cAWAtC,OAAAuC,iBAAA,eAAA", "file": "custom.js", "sourcesContent": ["window.theme = window.theme || {};\n\n/* ==============================\n  UTILITIES\n  ============================== */\n\ntheme.utils = {\n\n  // _.compact from lodash\n  // Creates an array with all falsey values removed. The values `false`, `null`,\n  // `0`, `\"\"`, `undefined`, and `NaN` are falsey.\n  // _.compact([0, 1, false, 2, '', 3]);\n  // => [1, 2, 3]\n\n  compact: function(array) {\n    var index = -1,\n        length = array == null ? 0 : array.length,\n        resIndex = 0,\n        result = [];\n\n    while (++index < length) {\n      var value = array[index];\n      if (value) {\n        result[resIndex++] = value;\n      }\n    }\n    return result;\n\n  }\n\n};\n\nShopify.Theme = {\n\n  selectors: {\n\n    productForm: '.product-form',\n\n  },\n\n  sections: {\n\n    sectionName: {\n\n      _init: function() {\n\n        // Custom private section method\n\n        var section = this.container;\n\n        console.log(section);\n\n      },\n\n      // Shortcut function called when a section is loaded via 'sections.load()' or by the Theme Editor 'shopify:section:load' event.\n      onLoad: function() {\n\n        this._init();\n\n      }\n\n    }\n\n  },\n\n  functions: {\n\n    fadeOut: function( elem, ms ) {\n      if( ! elem )\n        return;\n\n      if( ms )\n      {\n        var opacity = 1;\n        var timer = setInterval( function() {\n          opacity -= 50 / ms;\n          if( opacity <= 0 )\n          {\n            clearInterval(timer);\n            opacity = 0;\n            elem.style.display = \"none\";\n            elem.style.visibility = \"hidden\";\n          }\n          elem.style.opacity = opacity;\n          elem.style.filter = \"alpha(opacity=\" + opacity * 100 + \")\";\n        }, 50 );\n      }\n      else\n      {\n        elem.style.opacity = 0;\n        elem.style.filter = \"alpha(opacity=0)\";\n        elem.style.display = \"none\";\n        elem.style.visibility = \"hidden\";\n      }\n    }\n\n  },\n\n  components: {\n\n    tooltips: function(){\n\n      tippy(\"[data-tippy-content]\", {\n        theme: \"dark\",\n        placement: \"bottom\"\n      });\n\n    },\n\n  }\n\n};\n\n// Shopify.Theme.components.tooltips();\n\nwindow.addEventListener('beforeunload', (event) => {\n\n\n});\n"]}