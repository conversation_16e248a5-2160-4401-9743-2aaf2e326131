[{"name": "theme_info", "theme_name": "Focal", "theme_author": "Maestrooo", "theme_version": "12.2.0", "theme_documentation_url": "https://support.maestrooo.com/", "theme_support_url": "https://support.maestrooo.com/article/203-contact-us"}, {"name": "🪁 Sub-Brands", "settings": [{"type": "checkbox", "id": "subbrands_hide_inactive", "label": "Hide inactive sub-brands", "default": true, "info": "If a sub-brand is inactive, hide it in the header and mobile menus."}]}, {"name": "🪁 Ta<PERSON>er", "settings": [{"type": "header", "content": "Images"}, {"type": "image_picker", "id": "logo_image", "label": "Logo"}, {"type": "header", "content": "Colors"}, {"type": "color", "id": "tabbed_header_background_1", "label": "Background", "default": "#ffffff"}, {"type": "color", "id": "tabbed_header_background_2", "label": "Secondary background", "default": "#ECEEED"}, {"type": "color", "id": "tabbed_header_link_color", "label": "Links", "default": "#425A51"}, {"type": "color", "id": "tabbed_header_logo_color", "label": "Logo", "default": "#425A51"}, {"type": "color", "id": "tabbed_header_heading_color", "label": "Headings", "default": "#425A51"}, {"type": "color", "id": "tabbed_header_text_color", "label": "Text", "default": "#000000"}, {"type": "color", "id": "tabbed_header_text_hover_color", "label": "Text (Hover)", "default": "#ffffff"}, {"type": "color", "id": "tabbed_header_icon_color", "label": "Icons", "default": "#97A49F"}, {"type": "color", "id": "tabbed_header_button_color", "label": "Primary (Buttons)", "default": "#425A51"}, {"type": "color", "id": "tabbed_header_border_color", "label": "Borders", "default": "#E5E5E4"}, {"type": "color", "id": "tabbed_header_bubble_color", "label": "Cart count bubble", "default": "#425A51"}]}, {"name": "🪁 Tags", "settings": [{"type": "textarea", "id": "tag_disable_quickadd", "label": "Disable Quick-Add", "placeholder": "Disable Quick-<PERSON><PERSON>, <PERSON><PERSON><PERSON>", "default": "Disable Quick-<PERSON><PERSON>, <PERSON><PERSON><PERSON>", "info": "Comma-separated tags. If a product has this tag, disable its quick-add buttons (small desktop views only)."}]}, {"name": "🪁 Embroidery", "settings": [{"type": "header", "content": "Name Embroidery Options"}, {"type": "textarea", "id": "embroidery_swatch_colors", "label": "Swatch color list", "placeholder": "Red:#ff0000\nLight Blue:#add8e6\nHot Pink:hot-pink.png\nRed Blue Stripes:#e22#22e", "info": "One color name:value per line. Value after : can be color hex code or image url."}]}, {"name": "🪁 Rewards", "settings": [{"type": "header", "content": "Icons"}, {"type": "image_picker", "id": "rewards_points_icon", "label": "Points icon"}]}, {"name": "🪁 Bookmarks", "settings": [{"type": "header", "content": "Rewards"}, {"type": "url", "id": "page_customer_rewards", "label": "Customer Account <PERSON><PERSON><PERSON> Page"}, {"type": "url", "id": "page_rewards_landing", "label": "Rewards Landing Page"}, {"type": "header", "content": "Policies"}, {"type": "url", "id": "page_accessibility_statement", "label": "Accessibility Statement"}]}, {"name": "🪁 Global Settings", "settings": [{"type": "checkbox", "id": "collapsing_color_swatch_collections", "label": "Collapsing Color Swatch Collections", "default": true, "info": "Collapse remotely fetched color swatch lists on the product page to three columns and show a \"View All\" button."}, {"type": "header", "content": "Special Tag Functions"}, {"type": "text", "id": "loop_returns_unavailable_tag", "label": "Unavailable for Returns Tag", "default": "Unavailable for Returns", "info": "If a product has this tag, when a customer is in the Loop returns experience, its product card will be hidden in collections, and the add to cart buttons will be hidden on product pages."}, {"type": "richtext", "id": "loop_returns_unavailable_message", "label": "Unavailable Message", "default": "<p>This item is not available for returns or exchanges.</p>", "info": "If a product has this tag, when a customer is in the Loop returns experience, its product card will be hidden in collections, and the add to cart buttons will be hidden on product pages."}, {"type": "header", "content": "Disable Add to Cart"}, {"type": "paragraph", "content": "The Add to Cart button can be disabled on a per product basis with the Disable Add to Cart metafield."}, {"type": "text", "id": "disable_add_to_cart_label", "label": "Disable Add to Cart - Label", "default": "Coming Soon", "info": "If a product's Add to Cart is disabled with the metafield, replace the label with this text."}]}, {"name": "🪁 Tier Messages", "settings": [{"type": "select", "id": "tier_display", "label": "Tier Display", "options": [{"value": "label", "label": "Label"}, {"value": "badge", "label": "Badge"}], "default": "badge", "info": "If a product is associated with a tier, choose how to show the tier on the product card. \n\n Label with show a text label. \n Badge shows an image badge on the product."}, {"type": "paragraph", "content": "These settings are for Kyte's Frequent Flyer loyalty program. These settings will control badges that show on the product, which customers have access to products based on which loyalty tier they are on, and what messages they see if they do or don't have access."}, {"type": "header", "content": "🎟️ Misc. Tier 1", "info": "A generic access tier, not part of the loyalty tiers."}, {"type": "image_picker", "id": "tier_0_icon", "label": "Badge", "info": "Shown on product cards and galleries."}, {"type": "image_picker", "id": "tier_0_simple_icon", "label": "Simple Icon", "info": "Shown on the banner on the product page when the customer isn't on the tier / doesn't qualify for the product. \n\n If no simple icon is uplodaded, the Badge image is used."}, {"type": "checkbox", "id": "tier_0_icon_enabled_collection", "label": "Icon on Collection Page.", "default": true}, {"type": "checkbox", "id": "tier_0_icon_enabled_product", "label": "Icon on Product Page.", "default": true}, {"type": "text", "id": "tier_0_tag", "label": "Tag", "default": "EarlyAccess"}, {"type": "paragraph", "content": "Not Qualified"}, {"type": "text", "id": "tier_0_atc_disabled_label", "label": "Disabled Add to Cart Button Label", "info": "Replaces the button label with custom text if the button is disabled. Leave blank to use the default \"Add to Cart\" text."}, {"type": "text", "id": "tier_0_message_heading", "label": "Message - Heading", "default": "Bronze Exclusive"}, {"type": "richtext", "id": "tier_0_message_text", "label": "Message - Message", "default": "<p>This pattern is only available for our Bronze tier rewards members.</p>"}, {"type": "text", "id": "tier_0_cta_text", "label": "CTA - Text", "default": "Learn More"}, {"type": "url", "id": "tier_0_cta_link", "label": "CTA - Link"}, {"type": "paragraph", "content": "Qualified"}, {"type": "checkbox", "id": "tier_0_welcome_message_enable", "label": "Show a banner on the product form if the customer qualifies to buy this product.", "default": false}, {"type": "text", "id": "tier_0_welcome_message_title", "label": "Welcome Banner - Title", "default": "Hi there - you’re on our list!"}, {"type": "richtext", "id": "tier_0_welcome_message_text", "label": "Welcome Banner - Message", "default": "<p>That means you can buy this Platinum Exclusive product, at our best price. Thanks for being here! 🎔︎</p>"}, {"type": "text", "id": "tier_0_welcome_message_cta_text", "label": "Welcome Banner - Link Text", "default": "Learn More"}, {"type": "url", "id": "tier_0_welcome_message_cta_link", "label": "Welcome Banner - <PERSON>"}, {"type": "paragraph", "content": "COMING SOON"}, {"type": "checkbox", "id": "tier_0_disable_atc", "label": "Disable Add to <PERSON><PERSON> button", "default": false, "info": "Disables the add to cart button. (Will be disabled even if the customer qualifies for the tier.)"}, {"type": "text", "id": "tier_0_disable_atc_button_label", "label": "Disable Add to Cart button - Label", "info": "Override the ATC button label if the product is Coming Soon."}, {"type": "header", "content": "🎟️ Misc. Tier 2", "info": "A generic access tier, not part of the loyalty tiers."}, {"type": "image_picker", "id": "tier_6_icon", "label": "Icon"}, {"type": "image_picker", "id": "tier_6_simple_icon", "label": "Simple Icon", "info": "Shown on the banner on the product page when the customer isn't on the tier / doesn't qualify for the product. \n\n If no simple icon is uplodaded, the Badge image is used."}, {"type": "checkbox", "id": "tier_6_icon_enabled_collection", "label": "Icon on Collection Page.", "default": true}, {"type": "checkbox", "id": "tier_6_icon_enabled_product", "label": "Icon on Product Page.", "default": true}, {"type": "text", "id": "tier_6_tag", "label": "Tag", "default": "EarlyAccess"}, {"type": "paragraph", "content": "Not Qualified"}, {"type": "text", "id": "tier_6_atc_disabled_label", "label": "Disabled Add to Cart Button Label", "info": "Replaces the button label with custom text if the button is disabled. Leave blank to use the default \"Add to Cart\" text."}, {"type": "text", "id": "tier_6_message_heading", "label": "Message - Heading", "default": "Bronze Exclusive"}, {"type": "richtext", "id": "tier_6_message_text", "label": "Message - Message", "default": "<p>This pattern is only available for our Bronze tier rewards members.</p>"}, {"type": "text", "id": "tier_6_cta_text", "label": "CTA - Text", "default": "Learn More"}, {"type": "url", "id": "tier_6_cta_link", "label": "CTA - Link"}, {"type": "paragraph", "content": "Qualified"}, {"type": "checkbox", "id": "tier_6_welcome_message_enable", "label": "Show a banner on the product form if the customer qualifies to buy this product.", "default": false}, {"type": "text", "id": "tier_6_welcome_message_title", "label": "Welcome Banner - Title", "default": "Hi there - you’re on our list!"}, {"type": "richtext", "id": "tier_6_welcome_message_text", "label": "Welcome Banner - Message", "default": "<p>That means you can buy this Platinum Exclusive product, at our best price. Thanks for being here! 🎔︎</p>"}, {"type": "text", "id": "tier_6_welcome_message_cta_text", "label": "Welcome Banner - Link Text", "default": "Learn More"}, {"type": "url", "id": "tier_6_welcome_message_cta_link", "label": "Welcome Banner - <PERSON>"}, {"type": "header", "content": "🎟️ Misc. Tier 3", "info": "A generic access tier, not part of the loyalty tiers."}, {"type": "image_picker", "id": "tier_7_icon", "label": "Icon"}, {"type": "image_picker", "id": "tier_7_simple_icon", "label": "Simple Icon", "info": "Shown on the banner on the product page when the customer isn't on the tier / doesn't qualify for the product. \n\n If no simple icon is uplodaded, the Badge image is used."}, {"type": "checkbox", "id": "tier_7_icon_enabled_collection", "label": "Icon on Collection Page.", "default": true}, {"type": "checkbox", "id": "tier_7_icon_enabled_product", "label": "Icon on Product Page.", "default": true}, {"type": "text", "id": "tier_7_tag", "label": "Tag", "default": "EarlyAccess"}, {"type": "paragraph", "content": "Not Qualified"}, {"type": "text", "id": "tier_7_atc_disabled_label", "label": "Disabled Add to Cart Button Label", "info": "Replaces the button label with custom text if the button is disabled. Leave blank to use the default \"Add to Cart\" text."}, {"type": "text", "id": "tier_7_message_heading", "label": "Message - Heading", "default": "Bronze Exclusive"}, {"type": "richtext", "id": "tier_7_message_text", "label": "Message - Message", "default": "<p>This pattern is only available for our Bronze tier rewards members.</p>"}, {"type": "text", "id": "tier_7_cta_text", "label": "CTA - Text", "default": "Learn More"}, {"type": "url", "id": "tier_7_cta_link", "label": "CTA - Link"}, {"type": "paragraph", "content": "Qualified"}, {"type": "checkbox", "id": "tier_7_welcome_message_enable", "label": "Show a banner on the product form if the customer qualifies to buy this product.", "default": false}, {"type": "text", "id": "tier_7_welcome_message_title", "label": "Welcome Banner - Title", "default": "Hi there - you’re on our list!"}, {"type": "richtext", "id": "tier_7_welcome_message_text", "label": "Welcome Banner - Message", "default": "<p>That means you can buy this Platinum Exclusive product, at our best price. Thanks for being here! 🎔︎</p>"}, {"type": "text", "id": "tier_7_welcome_message_cta_text", "label": "Welcome Banner - Link Text", "default": "Learn More"}, {"type": "url", "id": "tier_7_welcome_message_cta_link", "label": "Welcome Banner - <PERSON>"}, {"type": "header", "content": "🥉 TIER 1 (Bronze)", "info": "For products that customers on the Bronze tier has access to."}, {"type": "image_picker", "id": "tier_1_icon", "label": "Icon"}, {"type": "image_picker", "id": "tier_1_simple_icon", "label": "Simple Icon", "info": "Shown on the banner on the product page when the customer isn't on the tier / doesn't qualify for the product. \n\n If no simple icon is uplodaded, the Badge image is used."}, {"type": "checkbox", "id": "tier_1_icon_enabled_collection", "label": "Icon on Collection Page.", "default": true}, {"type": "checkbox", "id": "tier_1_icon_enabled_product", "label": "Icon on Product Page.", "default": true}, {"type": "paragraph", "content": "COMING SOON"}, {"type": "checkbox", "id": "tier_1_disable_atc", "label": "Disable Add to <PERSON><PERSON> button", "default": true, "info": "Disables the add to cart button. (Will be disabled even if the customer qualifies for the tier.)"}, {"type": "text", "id": "tier_1_disable_atc_button_label", "label": "Disable Add to Cart button - Label", "info": "Override the ATC button label if the product is Coming Soon."}, {"type": "text", "id": "tier_1_tag", "label": "Tag", "default": "tier: Bronze"}, {"type": "paragraph", "content": "Not Qualified"}, {"type": "text", "id": "tier_1_atc_disabled_label", "label": "Disabled Add to Cart Button Label", "info": "Replaces the button label with custom text if the button is disabled. Leave blank to use the default \"Add to Cart\" text."}, {"type": "text", "id": "tier_1_message_heading", "label": "Message - Heading", "default": "Bronze Exclusive"}, {"type": "richtext", "id": "tier_1_message_text", "label": "Message - Message", "default": "<p>This pattern is only available for our Bronze tier rewards members.</p>"}, {"type": "text", "id": "tier_1_cta_text", "label": "CTA - Text", "default": "Learn More"}, {"type": "url", "id": "tier_1_cta_link", "label": "CTA - Link"}, {"type": "paragraph", "content": "Qualified"}, {"type": "checkbox", "id": "tier_1_welcome_message_enable", "label": "Show a banner on the product form if the customer qualifies to buy this product.", "default": false}, {"type": "text", "id": "tier_1_welcome_message_title", "label": "Welcome Banner - Title", "default": "Hi there - you’re on our list!"}, {"type": "richtext", "id": "tier_1_welcome_message_text", "label": "Welcome Banner - Message", "default": "<p>That means you can buy this Platinum Exclusive product, at our best price. Thanks for being here! 🎔︎</p>"}, {"type": "text", "id": "tier_1_welcome_message_cta_text", "label": "Welcome Banner - Link Text", "default": "Learn More"}, {"type": "url", "id": "tier_1_welcome_message_cta_link", "label": "Welcome Banner - <PERSON>"}, {"type": "paragraph", "content": "🥈 TIER 2 (Silver)"}, {"type": "paragraph", "content": "For products that customers on the Gold tier has access to."}, {"type": "image_picker", "id": "tier_2_icon", "label": "Icon"}, {"type": "image_picker", "id": "tier_2_simple_icon", "label": "Simple Icon", "info": "Shown on the banner on the product page when the customer isn't on the tier / doesn't qualify for the product. \n\n If no simple icon is uplodaded, the Badge image is used."}, {"type": "checkbox", "id": "tier_2_icon_enabled_collection", "label": "Icon on Collection Page.", "default": true}, {"type": "checkbox", "id": "tier_2_icon_enabled_product", "label": "Icon on Product Page.", "default": true}, {"type": "text", "id": "tier_2_tag", "label": "Tag", "default": "tier: Gold"}, {"type": "paragraph", "content": "Not Qualified"}, {"type": "text", "id": "tier_2_atc_disabled_label", "label": "Disabled Add to Cart Button Label", "info": "Replaces the button label with custom text if the button is disabled. Leave blank to use the default \"Add to Cart\" text."}, {"type": "text", "id": "tier_2_message_heading", "label": "Message - Heading", "default": "tier: Gold"}, {"type": "richtext", "id": "tier_2_message_text", "label": "Message - Message", "default": "<p>This pattern is only available for our Gold tier rewards members.</p>"}, {"type": "text", "id": "tier_2_cta_text", "label": "CTA - Text", "default": "Learn More"}, {"type": "url", "id": "tier_2_cta_link", "label": "CTA - Link"}, {"type": "paragraph", "content": "Qualified"}, {"type": "checkbox", "id": "tier_2_welcome_message_enable", "label": "Show a banner on the product form if the customer qualifies to buy this product.", "default": true}, {"type": "text", "id": "tier_2_welcome_message_title", "label": "Welcome Banner - Title", "default": "Hi there - you’re on our list!"}, {"type": "richtext", "id": "tier_2_welcome_message_text", "label": "Welcome Banner - Message", "default": "<p>That means you can buy this Platinum Exclusive product, at our best price. Thanks for being here! 🎔︎</p>"}, {"type": "text", "id": "tier_2_welcome_message_cta_text", "label": "Welcome Banner - Link Text", "default": "Learn More"}, {"type": "url", "id": "tier_2_welcome_message_cta_link", "label": "Welcome Banner - <PERSON>"}, {"type": "paragraph", "content": "🥇 TIER 3 (Gold)"}, {"type": "paragraph", "content": "For products that customers on the Platinum tier has access to."}, {"type": "image_picker", "id": "tier_3_icon", "label": "Icon"}, {"type": "image_picker", "id": "tier_3_simple_icon", "label": "Simple Icon", "info": "Shown on the banner on the product page when the customer isn't on the tier / doesn't qualify for the product. \n\n If no simple icon is uplodaded, the Badge image is used."}, {"type": "checkbox", "id": "tier_3_icon_enabled_collection", "label": "Icon on Collection Page.", "default": true}, {"type": "checkbox", "id": "tier_3_icon_enabled_product", "label": "Icon on Product Page.", "default": true}, {"type": "text", "id": "tier_3_tag", "label": "Tag", "default": "tier: Platinum"}, {"type": "paragraph", "content": "Not Qualified"}, {"type": "text", "id": "tier_3_atc_disabled_label", "label": "Disabled Add to Cart Button Label", "info": "Replaces the button label with custom text if the button is disabled. Leave blank to use the default \"Add to Cart\" text."}, {"type": "text", "id": "tier_3_message_heading", "label": "Message - Heading", "default": "Platinum Exclusive"}, {"type": "richtext", "id": "tier_3_message_text", "label": "Message - Message", "default": "<p>This pattern is only available for our Platinum tier rewards members.</p>"}, {"type": "text", "id": "tier_3_cta_text", "label": "CTA - Text", "default": "Learn More"}, {"type": "url", "id": "tier_3_cta_link", "label": "CTA - Link"}, {"type": "paragraph", "content": "Qualified"}, {"type": "checkbox", "id": "tier_3_welcome_message_enable", "label": "Show a banner on the product form if the customer qualifies to buy this product.", "default": true}, {"type": "text", "id": "tier_3_welcome_message_title", "label": "Welcome Banner - Title", "default": "Hi there - you’re on our list!"}, {"type": "richtext", "id": "tier_3_welcome_message_text", "label": "Welcome Banner - Message", "default": "<p>That means you can buy this Platinum Exclusive product, at our best price. Thanks for being here! 🎔︎</p>"}, {"type": "text", "id": "tier_3_welcome_message_cta_text", "label": "Welcome Banner - Link Text", "default": "Learn More"}, {"type": "url", "id": "tier_3_welcome_message_cta_link", "label": "Welcome Banner - <PERSON>"}, {"type": "paragraph", "content": "💍 TIER 4 (Platinum)"}, {"type": "paragraph", "content": "For products that customers on the Platinum tier has access to."}, {"type": "image_picker", "id": "tier_4_icon", "label": "Icon"}, {"type": "image_picker", "id": "tier_4_simple_icon", "label": "Simple Icon", "info": "Shown on the banner on the product page when the customer isn't on the tier / doesn't qualify for the product. \n\n If no simple icon is uplodaded, the Badge image is used."}, {"type": "checkbox", "id": "tier_4_icon_enabled_collection", "label": "Icon on Collection Page.", "default": true}, {"type": "checkbox", "id": "tier_4_icon_enabled_product", "label": "Icon on Product Page.", "default": true}, {"type": "text", "id": "tier_4_tag", "label": "Tag", "default": "tier: Platinum"}, {"type": "paragraph", "content": "Not Qualified"}, {"type": "text", "id": "tier_4_atc_disabled_label", "label": "Disabled Add to Cart Button Label", "info": "Replaces the button label with custom text if the button is disabled. Leave blank to use the default \"Add to Cart\" text."}, {"type": "text", "id": "tier_4_message_heading", "label": "Message - Heading", "default": "Platinum Exclusive"}, {"type": "richtext", "id": "tier_4_message_text", "label": "Message - Message", "default": "<p>This pattern is only available for our Platinum tier rewards members.</p>"}, {"type": "text", "id": "tier_4_cta_text", "label": "CTA - Text", "default": "Learn More"}, {"type": "url", "id": "tier_4_cta_link", "label": "CTA - Link"}, {"type": "paragraph", "content": "Qualified"}, {"type": "checkbox", "id": "tier_4_welcome_message_enable", "label": "Show a banner on the product form if the customer qualifies to buy this product.", "default": true}, {"type": "text", "id": "tier_4_welcome_message_title", "label": "Welcome Banner - Title", "default": "Hi there - you’re on our list!"}, {"type": "richtext", "id": "tier_4_welcome_message_text", "label": "Welcome Banner - Message", "default": "<p>That means you can buy this Platinum Exclusive product, at our best price. Thanks for being here! 🎔︎</p>"}, {"type": "text", "id": "tier_4_welcome_message_cta_text", "label": "Welcome Banner - Link Text", "default": "Learn More"}, {"type": "url", "id": "tier_4_welcome_message_cta_link", "label": "Welcome Banner - <PERSON>"}, {"type": "header", "content": "🎖️ All Tier Access", "info": "For products that customers on any loyalty tier have access to."}, {"type": "image_picker", "id": "tier_5_icon", "label": "Icon"}, {"type": "image_picker", "id": "tier_5_simple_icon", "label": "Simple Icon", "info": "Shown on the banner on the product page when the customer isn't on the tier / doesn't qualify for the product. \n\n If no simple icon is uplodaded, the Badge image is used."}, {"type": "checkbox", "id": "tier_5_icon_enabled_collection", "label": "Icon on Collection Page.", "default": true}, {"type": "checkbox", "id": "tier_5_icon_enabled_product", "label": "Icon on Product Page.", "default": true}, {"type": "text", "id": "tier_5_tag", "label": "Tag", "default": "Any Tier"}, {"type": "paragraph", "content": "Not Qualified"}, {"type": "text", "id": "tier_5_atc_disabled_label", "label": "Disabled Add to Cart Button Label", "info": "Replaces the button label with custom text if the button is disabled. Leave blank to use the default \"Add to Cart\" text."}, {"type": "text", "id": "tier_5_message_heading", "label": "Message - Heading", "default": "Bronze Exclusive"}, {"type": "richtext", "id": "tier_5_message_text", "label": "Message - Message", "default": "<p>This pattern is only available for our Bronze tier rewards members.</p>"}, {"type": "text", "id": "tier_5_cta_text", "label": "CTA - Text", "default": "Learn More"}, {"type": "url", "id": "tier_5_cta_link", "label": "CTA - Link"}, {"type": "paragraph", "content": "Qualified"}, {"type": "checkbox", "id": "tier_5_welcome_message_enable", "label": "Show a banner on the product form if the customer qualifies to buy this product.", "default": false}, {"type": "text", "id": "tier_5_welcome_message_title", "label": "Welcome Banner - Title", "default": "Hi there - you’re on our list!"}, {"type": "richtext", "id": "tier_5_welcome_message_text", "label": "Welcome Banner - Message", "default": "<p>That means you can buy this Platinum Exclusive product, at our best price. Thanks for being here! 🎔︎</p>"}, {"type": "text", "id": "tier_5_welcome_message_cta_text", "label": "Welcome Banner - Link Text", "default": "Learn More"}, {"type": "url", "id": "tier_5_welcome_message_cta_link", "label": "Welcome Banner - <PERSON>"}]}, {"name": "⚡️ Pre-Orders", "settings": [{"type": "header", "content": "📝 Pre-Order Messaging - General"}, {"type": "textarea", "id": "pre_order_warning_message", "label": "⚠️ Preorder Item Removal Warning Message", "info": "Warning message shown on preorder items if there are preorder items in the cart.", "default": "<strong>Heads-up!</strong> If you add this pre-order item to your cart, your non pre-order items will be removed from your cart."}, {"type": "textarea", "id": "non_pre_order_warning_message", "label": "⚠️ Non-Preorder Removal Warning Message", "info": "Warning message shown on non-preorder items if there are preorder items in the cart.", "default": "<strong>Heads-up!</strong> You have preorder items in your cart. To add this in-stock item to your cart, first remove your preorder products."}, {"type": "header", "content": "📝 Pre-Order Messaging - #1 & #2"}, {"type": "paragraph", "content": "Pre-Order #1 - To show pre-order messaging on product page and cart"}, {"type": "text", "id": "pre_order_one_tag", "label": "🏷 Tag", "info": "Products tagged with this tag will show the messages below on the product page and in the cart."}, {"type": "text", "id": "pre_order_one_date", "label": "📆 Date", "info": "Message For 'Pre-Order1' tag. (e.g.: \"Nov 15\"). Will appear in the pre-Order button and message. Please only use an abbrivated date."}, {"type": "richtext", "id": "pre_order_one_message", "label": "💬 Message", "info": "The message below a 'Pre-Order' headline & date."}, {"type": "select", "id": "pre_order_one_icon", "label": "Icon", "options": [{"value": "picto-coupon", "label": "Coupon", "group": "Shop"}, {"value": "picto-gift", "label": "Gift", "group": "Shop"}, {"value": "picto-taxes", "label": "Taxes", "group": "Shop"}, {"value": "picto-warranty", "label": "Warranty", "group": "Shop"}, {"value": "picto-like", "label": "Like", "group": "Shop"}, {"value": "picto-store", "label": "Store", "group": "Shop"}, {"value": "picto-store-pickup", "label": "Store pickup", "group": "Shop"}, {"value": "picto-love", "label": "Love", "group": "Shop"}, {"value": "picto-donation", "label": "Donation", "group": "Shop"}, {"value": "picto-box", "label": "Box", "group": "Shipping"}, {"value": "picto-address", "label": "Address", "group": "Shipping"}, {"value": "picto-address-pin", "label": "Address pin", "group": "Shipping"}, {"value": "picto-fast-delivery", "label": "Fast delivery", "group": "Shipping"}, {"value": "picto-delivery-truck", "label": "Delivery truck", "group": "Shipping"}, {"value": "picto-return-box", "label": "Returns", "group": "Shipping"}, {"value": "picto-worldwide", "label": "World", "group": "Shipping"}, {"value": "picto-plane", "label": "Plane", "group": "Shipping"}, {"value": "picto-credit-card", "label": "Credit card", "group": "Payment & Security"}, {"value": "picto-lock", "label": "Lock", "group": "Payment & Security"}, {"value": "picto-shield", "label": "Shield", "group": "Payment & Security"}, {"value": "picto-secure-payment", "label": "Secure payment", "group": "Payment & Security"}, {"value": "picto-mobile", "label": "Mobile", "group": "Communication"}, {"value": "picto-phone", "label": "Phone", "group": "Communication"}, {"value": "picto-chat", "label": "Cha<PERSON>", "group": "Communication"}, {"value": "picto-send", "label": "Send", "group": "Communication"}, {"value": "picto-email", "label": "Email", "group": "Communication"}, {"value": "picto-customer-support", "label": "Customer support", "group": "Communication"}, {"value": "picto-operator", "label": "Support operator", "group": "Communication"}, {"value": "picto-virus", "label": "Virus", "group": "Health"}, {"value": "picto-mask", "label": "Mask", "group": "Health"}, {"value": "custom-rewards", "label": "Rewards", "group": "Kyte Baby"}, {"value": "custom-shipping", "label": "Shipping", "group": "Kyte Baby"}, {"value": "custom-returns", "label": "Returns", "group": "Kyte Baby"}, {"value": "custom-support", "label": "Support", "group": "Kyte Baby"}], "default": "picto-box"}, {"type": "paragraph", "content": "Pre-Order #2 - To show pre-order messaging on product page and cart"}, {"type": "text", "id": "pre_order_two_tag", "label": "🏷 Tag", "info": "Products tagged with this tag will show the messages below on the product page and in the cart."}, {"type": "text", "id": "pre_order_two_date", "label": "📆 Date", "info": "Date for 'Pre-order' tag. (e.g.: \"Nov 15\"). Will appear in the pre-order button and message. Please only use an abbrivated date."}, {"type": "richtext", "id": "pre_order_two_message", "label": "💬 Message", "info": "The message below a 'Pre-order' headline & date."}, {"type": "select", "id": "pre_order_two_icon", "label": "Icon", "options": [{"value": "picto-coupon", "label": "Coupon", "group": "Shop"}, {"value": "picto-gift", "label": "Gift", "group": "Shop"}, {"value": "picto-taxes", "label": "Taxes", "group": "Shop"}, {"value": "picto-warranty", "label": "Warranty", "group": "Shop"}, {"value": "picto-like", "label": "Like", "group": "Shop"}, {"value": "picto-store", "label": "Store", "group": "Shop"}, {"value": "picto-store-pickup", "label": "Store pickup", "group": "Shop"}, {"value": "picto-love", "label": "Love", "group": "Shop"}, {"value": "picto-donation", "label": "Donation", "group": "Shop"}, {"value": "picto-box", "label": "Box", "group": "Shipping"}, {"value": "picto-address", "label": "Address", "group": "Shipping"}, {"value": "picto-address-pin", "label": "Address pin", "group": "Shipping"}, {"value": "picto-fast-delivery", "label": "Fast delivery", "group": "Shipping"}, {"value": "picto-delivery-truck", "label": "Delivery truck", "group": "Shipping"}, {"value": "picto-return-box", "label": "Returns", "group": "Shipping"}, {"value": "picto-worldwide", "label": "World", "group": "Shipping"}, {"value": "picto-plane", "label": "Plane", "group": "Shipping"}, {"value": "picto-credit-card", "label": "Credit card", "group": "Payment & Security"}, {"value": "picto-lock", "label": "Lock", "group": "Payment & Security"}, {"value": "picto-shield", "label": "Shield", "group": "Payment & Security"}, {"value": "picto-secure-payment", "label": "Secure payment", "group": "Payment & Security"}, {"value": "picto-mobile", "label": "Mobile", "group": "Communication"}, {"value": "picto-phone", "label": "Phone", "group": "Communication"}, {"value": "picto-chat", "label": "Cha<PERSON>", "group": "Communication"}, {"value": "picto-send", "label": "Send", "group": "Communication"}, {"value": "picto-email", "label": "Email", "group": "Communication"}, {"value": "picto-customer-support", "label": "Customer support", "group": "Communication"}, {"value": "picto-operator", "label": "Support operator", "group": "Communication"}, {"value": "picto-virus", "label": "Virus", "group": "Health"}, {"value": "picto-mask", "label": "Mask", "group": "Health"}, {"value": "custom-rewards", "label": "Rewards", "group": "Kyte Baby"}, {"value": "custom-shipping", "label": "Shipping", "group": "Kyte Baby"}, {"value": "custom-returns", "label": "Returns", "group": "Kyte Baby"}, {"value": "custom-support", "label": "Support", "group": "Kyte Baby"}], "default": "picto-box"}]}, {"name": "⚡️ Clearance Messages", "settings": [{"type": "header", "content": "🆑 Clearance", "info": "To show clearance or final sale messaging on product page and cart."}, {"type": "header", "content": "<PERSON><PERSON>", "info": "The clearance message will be shown on any product tagged with the Clearance Tag, OR any product in one of the Clearance Collections."}, {"type": "text", "id": "clearance_tag", "label": "Clearance Tag", "info": "Products tagged with this tag will show the messages below on the product page and in the cart."}, {"type": "collection_list", "id": "clearance_collections", "label": "Clearance Collections", "info": "Any products in this collection(s) will be marked as clearance items"}, {"type": "header", "content": "Product Page"}, {"type": "select", "id": "clearance_icon", "label": "Icon", "options": [{"value": "picto-coupon", "label": "Coupon", "group": "Shop"}, {"value": "picto-gift", "label": "Gift", "group": "Shop"}, {"value": "picto-taxes", "label": "Taxes", "group": "Shop"}, {"value": "picto-warranty", "label": "Warranty", "group": "Shop"}, {"value": "picto-like", "label": "Like", "group": "Shop"}, {"value": "picto-store", "label": "Store", "group": "Shop"}, {"value": "picto-store-pickup", "label": "Store pickup", "group": "Shop"}, {"value": "picto-love", "label": "Love", "group": "Shop"}, {"value": "picto-donation", "label": "Donation", "group": "Shop"}, {"value": "picto-box", "label": "Box", "group": "Shipping"}, {"value": "picto-address", "label": "Address", "group": "Shipping"}, {"value": "picto-address-pin", "label": "Address pin", "group": "Shipping"}, {"value": "picto-fast-delivery", "label": "Fast delivery", "group": "Shipping"}, {"value": "picto-delivery-truck", "label": "Delivery truck", "group": "Shipping"}, {"value": "picto-return-box", "label": "Returns", "group": "Shipping"}, {"value": "picto-worldwide", "label": "World", "group": "Shipping"}, {"value": "picto-plane", "label": "Plane", "group": "Shipping"}, {"value": "picto-credit-card", "label": "Credit card", "group": "Payment & Security"}, {"value": "picto-lock", "label": "Lock", "group": "Payment & Security"}, {"value": "picto-shield", "label": "Shield", "group": "Payment & Security"}, {"value": "picto-secure-payment", "label": "Secure payment", "group": "Payment & Security"}, {"value": "picto-mobile", "label": "Mobile", "group": "Communication"}, {"value": "picto-phone", "label": "Phone", "group": "Communication"}, {"value": "picto-chat", "label": "Cha<PERSON>", "group": "Communication"}, {"value": "picto-send", "label": "Send", "group": "Communication"}, {"value": "picto-email", "label": "Email", "group": "Communication"}, {"value": "picto-customer-support", "label": "Customer support", "group": "Communication"}, {"value": "picto-operator", "label": "Support operator", "group": "Communication"}, {"value": "picto-virus", "label": "Virus", "group": "Health"}, {"value": "picto-mask", "label": "Mask", "group": "Health"}, {"value": "custom-rewards", "label": "Rewards", "group": "Kyte Baby"}, {"value": "custom-shipping", "label": "Shipping", "group": "Kyte Baby"}, {"value": "custom-returns", "label": "Returns", "group": "Kyte Baby"}, {"value": "custom-support", "label": "Support", "group": "Kyte Baby"}], "default": "picto-store"}, {"type": "richtext", "id": "clearance_message", "label": "Clearance Message", "info": "Message to show above ATC for products with the tag entered above. Example: FINAL SALE Item - May take up to 4 Weeks to Ship."}, {"type": "header", "content": "<PERSON><PERSON>"}, {"type": "richtext", "id": "clearance_lineitem_message", "label": "Clearance Line Item Message", "info": "Message to show in cart."}]}, {"name": "⚡️ Colors", "settings": [{"type": "header", "content": "Brand"}, {"type": "color", "id": "color__brand__1", "label": "Brand Colour 1", "default": "#FFFFFF"}, {"type": "color", "id": "color__brand__2", "label": "Brand Colour 2", "default": "#EBE9E4"}, {"type": "color", "id": "color__brand__3", "label": "Brand Colour 3", "default": "#CFC6C1"}, {"type": "color", "id": "color__brand__4", "label": "Brand Colour 4", "default": "#777968"}, {"type": "color", "id": "color__brand__5", "label": "Brand Colour 5", "default": "#AF8663"}, {"type": "color", "id": "color__brand__6", "label": "Brand Colour 6", "default": "#35464C"}, {"type": "color", "id": "color__brand__7", "label": "Brand Colour 7", "default": "#2B2B2B"}, {"type": "header", "content": "Colors"}, {"type": "paragraph", "content": "<PERSON><PERSON><PERSON>"}, {"type": "color", "id": "color__default", "label": "<PERSON><PERSON><PERSON>", "default": "#2B2B2B"}, {"type": "color", "id": "color__default__light", "label": "Default - Light", "default": "#474747"}, {"type": "color", "id": "color__default__dark", "label": "Default - Dark", "default": "#121212"}, {"type": "color", "id": "background_color__default", "label": "<PERSON><PERSON><PERSON>", "default": "#F7F7F7"}, {"type": "paragraph", "content": "Primary"}, {"type": "color", "id": "color__primary", "label": "Primary", "default": "#AF8663"}, {"type": "color", "id": "color__primary__light", "label": "Primary - Tint Light", "default": "#C99A72"}, {"type": "color", "id": "color__primary__dark", "label": "Primary - <PERSON><PERSON>", "default": "#967355"}, {"type": "color", "id": "background_color__primary", "label": "Primary", "default": "#FEF5EE"}, {"type": "paragraph", "content": "Secondary"}, {"type": "color", "id": "color__secondary", "label": "Secondary", "default": "#35464C"}, {"type": "color", "id": "color__secondary__light", "label": "Secondary - Tint Light", "default": "#475E66"}, {"type": "color", "id": "color__secondary__dark", "label": "Secondary - Tint Dark", "default": "#242F33"}, {"type": "color", "id": "background_color__secondary", "label": "Secondary", "default": "#F5F7F8"}, {"type": "paragraph", "content": "Tertiary"}, {"type": "color", "id": "color__tertiary", "label": "Tertiary", "default": "#777968"}, {"type": "color", "id": "color__tertiary__light", "label": "Tertiary - Tint Light", "default": "#8F917D"}, {"type": "color", "id": "color__tertiary__dark", "label": "Tertiary - Tint Dark", "default": "#5D5E51"}, {"type": "color", "id": "background_color__tertiary", "label": "Tertiary", "default": "#F3F3EF"}, {"type": "paragraph", "content": "Success"}, {"type": "color", "id": "color__success", "label": "Success", "default": "#2E9E7B"}, {"type": "color", "id": "color__success__light", "label": "Success - Tint Light", "default": "#55D7AE"}, {"type": "color", "id": "color__success__dark", "label": "Success - <PERSON><PERSON>", "default": "#2A8468"}, {"type": "color", "id": "background_color__success", "label": "Success", "default": "#D8F3EB"}, {"type": "paragraph", "content": "Warning"}, {"type": "color", "id": "color__warning", "label": "Warning", "default": "#FF8717"}, {"type": "color", "id": "color__warning__light", "label": "Warning - Tint Light", "default": "#FFB269"}, {"type": "color", "id": "color__warning__dark", "label": "Warning - <PERSON><PERSON>", "default": "#DF7716"}, {"type": "color", "id": "background_color__warning", "label": "Warning", "default": "#FFE7D1"}, {"type": "paragraph", "content": "Danger"}, {"type": "color", "id": "color__danger", "label": "Danger", "default": "#DE2A2A"}, {"type": "color", "id": "color__danger__light", "label": "Danger - Tint Light", "default": "#FF3A3A"}, {"type": "color", "id": "color__danger__dark", "label": "Danger - <PERSON><PERSON>", "default": "#D00606"}, {"type": "color", "id": "background_color__danger", "label": "Danger", "default": "#FFEBEB"}, {"type": "paragraph", "content": "Info"}, {"type": "color", "id": "color__info", "label": "Info", "default": "#4CA1ED"}, {"type": "color", "id": "color__info__light", "label": "Info - Tint Light", "default": "#63B5FF"}, {"type": "color", "id": "color__info__dark", "label": "Info - Tint Dark", "default": "#3E8FD8"}, {"type": "color", "id": "background_color__info", "label": "Info", "default": "#D9EDFF"}, {"type": "paragraph", "content": "Content Colors"}, {"type": "color", "id": "background_color_body", "label": "Body", "default": "#FFFFFF"}, {"type": "color", "id": "background_color_content_1", "label": "Content 1", "default": "#FFFFFF"}, {"type": "color", "id": "background_color_content_2", "label": "Content 2", "default": "#F7F7F7"}, {"type": "color", "id": "background_color_content_3", "label": "Content 3", "default": "#F7F7F7"}, {"type": "color", "id": "background_color_content_reversed_1", "label": "Reversed - Content 1", "default": "#232323"}, {"type": "color", "id": "background_color_content_reversed_2", "label": "Reversed - Content 2", "default": "#999999"}, {"type": "color_background", "id": "overlay_color", "label": "Overlay Background"}, {"type": "header", "content": "Content Colors"}, {"type": "color", "id": "color_text", "label": "Body Text", "default": "#121212"}, {"type": "color", "id": "color_text_dark", "label": "Body Text (Strong)", "default": "#000000"}, {"type": "color", "id": "color_body_light", "label": "Body Text (Light)", "default": "#5C5C5C"}, {"type": "color", "id": "color_body_reversed", "label": "Body Text - Reversed", "default": "#DDDDDD"}, {"type": "color", "id": "color_body_reversed_strong", "label": "Body Text - <PERSON><PERSON><PERSON> (Strong)", "default": "#FFFFFF"}, {"type": "color", "id": "color_link", "label": "Links", "default": "#8F917D"}, {"type": "color", "id": "color_heading_1", "label": "Heading 1", "default": "#AF8663"}, {"type": "color", "id": "color_heading_2", "label": "Heading 2", "default": "#333333"}, {"type": "color", "id": "color_heading_3", "label": "Heading 3", "default": "#999999"}, {"type": "header", "content": "Layout Colors"}, {"type": "color", "id": "color_line", "label": "Lines", "default": "#E6E6E6"}, {"type": "color", "id": "color_line_light", "label": "Lines - Light", "default": "#f2f2f2"}, {"type": "color", "id": "color_line_dark", "label": "Lines - Dark", "default": "#C8C8C8"}, {"type": "header", "content": "Product Colors"}, {"type": "color", "id": "color_prices", "label": "Prices", "default": "#232323"}, {"type": "color", "id": "color_prices_sale", "label": "Prices - Sale", "default": "#232323"}, {"type": "color", "id": "color_prices_compare", "label": "Prices - Compare At", "default": "#CCCCCC"}, {"type": "header", "content": "Product Colors"}, {"type": "color", "id": "color_product_title", "label": "Title", "default": "#333333"}, {"type": "header", "content": "Other Colors"}, {"type": "color", "id": "color_reviews", "label": "Reviews", "default": "#FFE381"}]}, {"name": "⚡️ Currency format", "settings": [{"type": "header", "content": "Currency codes"}, {"type": "paragraph", "content": "Cart and checkout prices always show currency codes. Example: $1.00 USD."}, {"type": "checkbox", "id": "currency_code_enabled", "label": "Show currency codes", "default": false}, {"type": "checkbox", "id": "currency_no_trailing_zeroes", "label": "When possible, remove trailing zeroes.", "default": true}]}, {"name": "Appearance", "settings": [{"type": "paragraph", "content": "Adapt the theme to your brand style by changing rounded elements, spacing and icons style."}, {"type": "range", "id": "icon_stroke_width", "min": 1, "max": 2, "step": 0.1, "unit": "px", "label": "Icon thickness", "default": 2}, {"type": "range", "id": "button_border_radius", "min": 0, "max": 30, "step": 2, "unit": "px", "label": "Button/input border radius", "default": 0}, {"type": "select", "id": "block_border_radius", "label": "Block border radius", "options": [{"value": "none", "label": "None"}, {"value": "small", "label": "Small"}, {"value": "medium", "label": "Medium"}, {"value": "large", "label": "Large"}], "default": "none"}, {"type": "select", "id": "vertical_spacing", "label": "Vertical spacing", "info": "Impact the vertical spacing separating each section.", "options": [{"value": "small", "label": "Small"}, {"value": "medium", "label": "Medium"}, {"value": "large", "label": "Large"}], "default": "large"}]}, {"name": "Colors", "settings": [{"type": "header", "content": "General"}, {"type": "color", "id": "subheading_color", "label": "Subheading", "default": "#1c1b1b"}, {"type": "color", "id": "heading_color", "label": "Heading", "default": "#ffffff"}, {"type": "color", "id": "text_color", "label": "Body text", "default": "#677279"}, {"type": "color", "id": "background", "label": "Background", "default": "#ffffff"}, {"type": "color", "id": "secondary_background", "label": "Secondary background", "info": "Use a color close to the main background.", "default": "#f7f8fd"}, {"type": "color", "id": "success_color", "label": "Success", "default": "#168342"}, {"type": "color", "id": "error_color", "label": "Error", "default": "#e00000"}, {"type": "header", "content": "Header"}, {"type": "color", "id": "header_background", "label": "Background", "default": "#1e316a"}, {"type": "color", "id": "header_text_color", "label": "Text", "default": "#ffffff"}, {"type": "header", "content": "Footer"}, {"type": "color", "id": "footer_background", "label": "Background", "default": "#f3f5f6"}, {"type": "color", "id": "footer_text_color", "label": "Text", "default": "#677279"}, {"type": "header", "content": "Primary button"}, {"type": "color", "id": "primary_button_background", "label": "Background", "default": "#00badb"}, {"type": "color", "id": "primary_button_text_color", "label": "Text", "default": "#ffffff"}, {"type": "header", "content": "Secondary button"}, {"type": "color", "id": "secondary_button_background", "label": "Background", "default": "#1e2d7d"}, {"type": "color", "id": "secondary_button_text_color", "label": "Text", "default": "#ffffff"}, {"type": "header", "content": "Product"}, {"type": "color", "id": "product_rating_color", "label": "Star rating", "default": "#f6a429"}, {"type": "color", "id": "product_on_sale_accent", "label": "On sale accent", "default": "#e00000"}, {"type": "color", "id": "product_sold_out_accent", "label": "Sold out accent", "default": "#6f719b"}, {"type": "color", "id": "product_custom_label_background", "label": "Custom label", "info": "[Learn more](https://support.maestrooo.com/article/719-custom-badges)", "default": "#1e316a"}, {"type": "color", "id": "product_custom_label_2_background", "label": "Custom label 2", "info": "[Learn more](https://support.maestrooo.com/article/719-custom-badges)", "default": "#1e316a"}, {"type": "color", "id": "product_in_stock_text_color", "label": "In stock accent", "default": "#168342"}, {"type": "color", "id": "product_low_stock_text_color", "label": "Low stock accent", "default": "#e00000"}]}, {"name": "Typography", "settings": [{"type": "header", "content": "Headings"}, {"type": "font_picker", "id": "heading_font", "label": "Font", "info": "Selecting different fonts can affect the speed of your online store. Learn more about [system fonts.](https://help.shopify.com/en/manual/online-store/os/store-speed/improving-speed#fonts)", "default": "helvetica_n4"}, {"type": "select", "id": "heading_font_size", "label": "Heading size", "options": [{"value": "small", "label": "Small"}, {"value": "medium", "label": "Medium"}, {"value": "large", "label": "Large"}], "default": "medium"}, {"type": "select", "id": "heading_text_transform", "label": "Heading style", "options": [{"value": "normal", "label": "Normal"}, {"value": "lowercase", "label": "Lowercase"}, {"value": "uppercase", "label": "Uppercase"}], "default": "uppercase"}, {"type": "header", "content": "Body text"}, {"type": "font_picker", "id": "text_font", "label": "Font", "info": "Selecting different fonts can affect the speed of your online store. Learn more about [system fonts.](https://help.shopify.com/en/manual/online-store/os/store-speed/improving-speed#fonts)", "default": "helvetica_n4"}, {"type": "range", "id": "text_font_size", "label": "Base size", "min": 12, "max": 20, "unit": "px", "default": 15}]}, {"name": "Animation", "settings": [{"type": "paragraph", "content": "Users who configured their preferences to minimize non-essential motion will automatically experience reduced animations."}, {"type": "checkbox", "id": "show_image_zoom", "label": "Show image zoom", "info": "Affect various images that are slowly zoomed on hover", "default": true}, {"type": "checkbox", "id": "stagger_products_apparition", "label": "Reveal products one by one", "info": "Affect any product grid listing", "default": true}, {"type": "checkbox", "id": "stagger_blog_posts_apparition", "label": "Reveal blog posts one by one", "info": "Affect any blog post listing", "default": true}, {"type": "checkbox", "id": "reveal_product_media", "label": "Reveal product media", "info": "Affect product pages and featured product section", "default": true}]}, {"name": "Color swatch", "settings": [{"type": "checkbox", "id": "round_color_swatches", "label": "Round color swatches", "default": false}, {"type": "textarea", "id": "color_swatch_config", "label": "Configuration (deprecated)", "placeholder": "Red:#ff0000\nGreen:#00ff00\nBlue:#0000ff", "info": "Native color swatch should be used instead. [Learn more](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches). Configuration based color swatches is deprecated and will be removed in the future. If you still need to use the configuration based system, you can [learn more](https://support.maestrooo.com/article/718-configuring-color-swatches) about the exact naming convention."}]}, {"name": "Products grid", "settings": [{"type": "checkbox", "id": "products_in_collection", "label": "Product card links include the collection in the URL", "default": false}, {"type": "checkbox", "id": "show_vendor", "label": "Show vendor", "default": false}, {"type": "checkbox", "id": "show_secondary_image", "label": "Show secondary image on hover", "default": false}, {"type": "checkbox", "id": "product_add_to_cart", "label": "Enable quick add to cart", "default": true}, {"type": "checkbox", "id": "show_product_rating", "label": "Show product rating", "info": "To display a rating, add a product rating app. [Learn more](https://help.shopify.com/en/manual/products/product-reviews/installation)", "default": false}, {"type": "checkbox", "id": "show_discount", "label": "Show discount label", "default": true}, {"type": "select", "id": "discount_mode", "label": "Show discount as...", "options": [{"value": "percentage", "label": "Percentage"}, {"value": "saving", "label": "Saving"}], "default": "saving"}, {"type": "select", "id": "product_color_display", "label": "Color display mode", "options": [{"value": "hide", "label": "<PERSON>de"}, {"value": "count", "label": "Count"}, {"value": "swatch", "label": "Swatch"}], "default": "count"}, {"type": "select", "id": "product_image_size", "label": "Product image size", "options": [{"value": "natural", "label": "Natural"}, {"value": "short", "label": "Short (4:3)"}, {"value": "square", "label": "Square (1:1)"}, {"value": "tall", "label": "Tall (2:3)"}], "default": "natural"}, {"type": "header", "content": "Swatch Buttons"}, {"type": "checkbox", "id": "show_product_grid_swatches", "label": "Show Swatches", "info": "Show swatch buttons for products with only one option (buttons trigger an add to cart).", "default": true}]}, {"name": "<PERSON><PERSON>", "settings": [{"type": "select", "id": "cart_type", "label": "Cart type", "options": [{"value": "drawer", "label": "Drawer"}, {"value": "message", "label": "Message"}, {"value": "page", "label": "Page"}], "default": "message"}, {"type": "select", "id": "cart_icon", "label": "Cart icon", "options": [{"value": "shopping_bag", "label": "Shopping bag"}, {"value": "shopping_cart", "label": "Shopping cart"}, {"value": "tote_bag", "label": "Tote bag"}], "default": "shopping_bag"}, {"type": "checkbox", "id": "cart_show_free_shipping_threshold", "label": "Show free shipping minimum amount", "info": "Make sure that you have properly configured your [shipping rates](/admin/settings/shipping).", "default": false}, {"type": "textarea", "id": "cart_free_shipping_threshold", "label": "Free shipping minimum amount", "info": "Enter amount using number only. If for different locales, separate each locale code with its amount, with the final amount being the default for international shipping. (eg.: US:85,CA:120,INT:300)", "default": "50"}]}, {"name": "Social media", "settings": [{"type": "header", "content": "Accounts"}, {"type": "text", "id": "social_facebook", "label": "Facebook"}, {"type": "text", "id": "social_twitter", "label": "X (formerly Twitter)"}, {"type": "text", "id": "social_threads", "label": "Threads"}, {"type": "text", "id": "social_pinterest", "label": "Pinterest"}, {"type": "text", "id": "social_instagram", "label": "Instagram"}, {"type": "text", "id": "social_vimeo", "label": "Vimeo"}, {"type": "text", "id": "social_tumblr", "label": "Tumblr"}, {"type": "text", "id": "social_youtube", "label": "YouTube"}, {"type": "text", "id": "social_tiktok", "label": "TikTok"}, {"type": "text", "id": "social_linkedin", "label": "LinkedIn"}, {"type": "text", "id": "social_snapchat", "label": "Snapchat"}, {"type": "text", "id": "social_fancy", "label": "Fancy"}, {"type": "text", "id": "social_wechat", "label": "WeChat"}, {"type": "text", "id": "social_reddit", "label": "Reddit"}, {"type": "text", "id": "social_line", "label": "LINE"}, {"type": "text", "id": "social_spotify", "label": "Spotify"}]}, {"name": "Favicon", "settings": [{"type": "image_picker", "id": "favicon", "label": "Image", "info": "96 x 96px .png recommended"}]}]