<drawer-content id="{{ section.settings.drawer_id }}" class="drawer drawer--large">
  <span class="drawer__overlay"></span>

  <header class="drawer__header">
    <h3 class="drawer__title heading h6 uppercase">{{ section.settings.drawer_title }}</h3>
    
    <button type="button" class="drawer__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
      {%- render 'icon' with 'close' -%}
    </button>
  </header>

  <div class="drawer__content drawer__content--padded-start">

    {% comment %} Section 1 {% endcomment %}

    {% if section.settings.description %}
      {{ section.settings.description }}
    {% endif %}

    {%- capture example_gallery -%}
      {%- for block in section.blocks -%}
        {%- if block.type == "image" -%}
          <div class="example-gallery-item">
            <div class="example-gallery-item__image">
              {{ block.settings.image | image_url: width: block.settings.image.width | image_tag: loading: 'lazy', sizes: '(max-width: 999px) calc(100vw - 48px), 640px', widths: '400,500,600' }}
            </div>
            <span class="example-gallery-item__title">{{ block.settings.title }}</span>
          </div>
        {%- endif -%}
      {%- endfor -%}
    {%- endcapture -%}

    {%- if example_gallery != blank -%}
      <div class="example-gallery">
        {{ example_gallery }}
      </div>
    {%- endif -%}

  </div>

</drawer-content>

{% schema %}
{
  "name": "Examples Drawer",
  "class": "shopify-section--example-drawer",
  "enabled_on": {
    "groups": ["custom.productdrawers"]
  },
  "blocks": [
    {
      "type": "image",
      "name": "Image",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "text",
      "id": "drawer_id",
      "label": "Drawer ID",
      "info": "The ID of the drawer. (This is used elsewhere to target and open the drawer.)",
      "placeholder": "example-drawer-embroidery"
    },
    {
      "type": "header",
      "content": "Drawer Header"
    },
    {
      "type": "text",
      "id": "drawer_title",
      "label": "Title",
      "info": "The ID of the drawer. (This is used elsewhere to target and open the drawer.)",
      "default": "Example"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description"
    }
  ],
  "presets": [
    {
      "name": "Product Example Drawer"
    }
  ]
}
{% endschema %}