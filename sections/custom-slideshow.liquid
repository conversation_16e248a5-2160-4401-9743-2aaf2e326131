<style>
  #shopify-section-{{ section.id }} {
    
    --progress-bar-color: {{ section.blocks.first.settings.text_color.red }}, {{ section.blocks.first.settings.text_color.green }}, {{ section.blocks.first.settings.text_color.blue }};
    --section-autoplay-duration: {% if section.settings.autoplay %}{{ section.settings.cycle_speed }}s{% else %}0s{% endif %};
    --section-animation-play-state: paused;

    background-color: {{ section.settings.background }}; /* Allows to set a placeholder color while loading */

  }

  {%- for block in section.blocks -%}
    
    {%- assign text_position = block.settings.text_position | split: '_' | first -%}
    {%- assign text_alignment = block.settings.text_position | split: '_' | last -%}

    {%- case text_position -%}
      {%- when 'top' -%}
        {%- assign section_items_alignment = 'flex-start' -%}
      {%- when 'middle' -%}
        {%- assign section_items_alignment = 'center' -%}
      {%- when 'bottom' -%}
        {%- assign section_items_alignment = 'flex-end' -%}
    {%- endcase -%}

    {%- case text_alignment -%}
      {%- when 'left' -%}
        {%- assign section_items_justify = 'flex-start' -%}
      {%- when 'center' -%}
        {%- assign section_items_justify = 'center' -%}
      {%- when 'right' -%}
        {%- assign section_items_justify = 'flex-end' -%}
    {%- endcase -%}

    #block-{{ section.id }}-{{ block.id }} {

      {%- if block.settings.heading_color != blank and block.settings.heading_color != 'rgba(0,0,0,0)' -%}
        {%- assign heading_color = block.settings.heading_color -%}
      {%- else -%}
        {%- assign heading_color = settings.heading_color -%}
      {%- endif -%}

      {%- if block.settings.subheading_color != blank and block.settings.subheading_color != 'rgba(0,0,0,0)' -%}
        {%- assign subheading_color = block.settings.subheading_color -%}
      {%- else -%}
        {%- assign subheading_color = settings.subheading_color -%}
      {%- endif -%}

      {%- if block.settings.text_color != blank and block.settings.text_color != 'rgba(0,0,0,0)' -%}
        {%- assign text_color = block.settings.text_color -%}
      {%- else -%}
        {%- assign text_color = settings.text_color -%}
      {%- endif -%}

      {%- if block.settings.button_background_color != blank and block.settings.button_background_color != 'rgba(0,0,0,0)' -%}
        {%- assign button_background = block.settings.button_background_color -%}
      {%- else -%}
        {%- assign button_background = settings.primary_button_background -%}
      {%- endif -%}

      {%- if block.settings.button_text_color != blank and block.settings.button_text_color != 'rgba(0,0,0,0)' -%}
        {%- assign button_text_color = block.settings.button_text_color -%}
      {%- else -%}
        {%- assign button_text_color = settings.primary_button_text_color -%}
      {%- endif -%}

      --subheading-color: {{ subheading_color.red }}, {{ subheading_color.green }}, {{ subheading_color.blue }};
      --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
      --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
      --button-background: {{ button_background.red }}, {{ button_background.green }}, {{ button_background.blue }};
      --button-text-color: {{ button_text_color.red }}, {{ button_text_color.green }}, {{ button_text_color.blue }};

      --section-blocks-alignment: {{ section_items_alignment }};
      --section-blocks-justify: {{ section_items_justify }};
      --section-blocks-overlay-color: {{ block.settings.overlay_color.red }}, {{ block.settings.overlay_color.green }}, {{ block.settings.overlay_color.blue }};
      --section-blocks-overlay-opacity: {{ block.settings.overlay_opacity | divided_by: 100.0 }};
      
    }

    #block-{{ section.id }}-{{ block.id }} .heading {
      --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    }

    #block-{{ section.id }}-{{ block.id }} .subheading {
      --subheading-color: {{ subheading_color.red }}, {{ subheading_color.green }}, {{ subheading_color.blue }};
    }
    
    #block-{{ section.id }}-{{ block.id }} .button-group .button {
      --button-background: {{ button_background.red }}, {{ button_background.green }}, {{ button_background.blue }};
      --button-text-color: {{ button_text_color.red }}, {{ button_text_color.green }}, {{ button_text_color.blue }};
    }

  {%- endfor -%}
</style>

{%- if section.settings.override_blend == true -%}
  {%- assign blends_with_background = false -%}
{%- endif -%}

{%- capture section_classes -%}
  {% render 'section-classes', section: section %}
{%- endcapture -%}

<section class="section {{ section_classes }} section--flush custom-slideshow">

  {% if section.settings.extra_code != blank %}
    {{ section.settings.extra_code }}
  {% endif %}

  {%- if section.settings.anchor -%}
    <a class="visually-hidden anchor" id="{{ section.settings.anchor }}" name="{{ section.settings.anchor }}"></a>
  {%- endif -%}

  <slide-show {% if section.settings.show_initial_transition %}reveal-on-scroll{% endif %} {% if section.settings.autoplay %}auto-play{% endif %} transition-type="{{ section.settings.transition_type | escape }}" class="slideshow slideshow--{{ section.settings.section_height }}">
    <div class="slideshow__slide-list">
      {%- for block in section.blocks -%}
        {%- comment -%}
        ------------------------------------------------------------------------------------------------------------------
        IMAGE PART
        ------------------------------------------------------------------------------------------------------------------
        {%- endcomment -%}

        {%- assign image = block.settings.image -%}
        {%- assign split_image = block.settings.split_image -%}
        {%- assign mobile_image = block.settings.mobile_image -%}

        {%- assign image_aspect_ratio = image.aspect_ratio | default: 2.63 -%}

        {%- comment -%}
        If the image is split and that we want to preserve the ratio, we have to multiply the ratio by 2 (as each image
        only account for half the screen size)
        {%- endcomment -%}

        {%- if image != blank and split_image != blank and section.settings.section_height == 'auto' -%}
          {%- assign image_aspect_ratio = image_aspect_ratio | times: 2.0 -%}
        {%- endif -%}

        {%- if forloop.index > 2 -%}
          {%- assign loading_attribute_value = 'lazy' -%}
        {%- else -%}
          {%- assign loading_attribute_value = 'eager' -%}
        {%- endif -%}

        {%- capture slide_content -%}
          
          {%- if block.type == "video" -%}

            {%- if block.settings.video_url != blank -%}

              <external-video autoplay provider="{{ block.settings.video_url.type | escape }}" class="video-wrapper video-wrapper--{{ section.settings.media_size }}">
                <template>
                  {%- if block.settings.video_url.type == 'youtube' -%}
                    <iframe title="Video" id="player-{{ section.id }}" src="https://www.youtube.com/embed/{{ block.settings.video_url.id }}?playsinline=1&autoplay=1&mute=1&loop=1&playlist={{ block.settings.video_url.id }}&enablejsapi=1&controls=0&rel=0&modestbranding=1&origin=https://{{ request.host }}" allow="autoplay; fullscreen"></iframe>
                  {%- elsif block.settings.video_url.type == 'vimeo' -%}
                    <iframe title="Video" id="player-{{ section.id }}" src="https://player.vimeo.com/video/{{ block.settings.video_url.id }}?background=1&loop=1&transparent=0&responsive=1&portrait=0&title=0&byline=0&color={{ settings.text_color | remove_first: '#' }}" allow="autoplay; fullscreen"></iframe>
                  {%- endif -%}
                </template>
              </external-video>

            {%- elsif block.settings.video != blank -%}

              {%- capture video_poster -%}
                <div class="video-wrapper__poster">
                  {%- unless section.settings.autoplay -%}
                    {%- if block.settings.image != blank -%}
                      {{- block.settings.image | image_url: width: block.settings.image.width | image_tag: loading: 'lazy', sizes: '(max-width: 740px) calc(100vw - 24px * 2), 1600px', widths: '400,500,600,700,800,900,1000,1200,1400,1600,1800,2000,2200,2400,2600,2800', class: 'video-wrapper__poster-image' -}}
                    {%- else -%}
                      {{- 'lifestyle-1' | placeholder_svg_tag: 'video-wrapper__poster-image placeholder-background' -}}
                    {%- endif -%}
                  {%- endunless -%}

                  <div class="video-wrapper__poster-content {% if section.settings.background_type != 'boxed' %}content-box content-box--large content-box--text-center{% endif %}">
                    {%- unless section.settings.autoplay -%}
                      <button type="button" class="video-wrapper__play-button video-wrapper__play-button--large video-wrapper__play-button--ripple" title="{{ 'general.accessibility.play_video' | t | escape }}">
                        {%- render 'icon' with 'play', width: 72, height: 72 -%}
                      </button>
                    {%- endunless -%}
                  </div>
                </div>
              {%- endcapture -%}

              <div class="slideshow__image-wrapper">
                <native-video {% if block.settings.autoplay %}autoplay{% endif %} class="image-overlay__image video-wrapper video-wrapper--native" style="--aspect-ratio: {{ block.settings.video.aspect_ratio }}">
                  {{- video_poster -}} 

                  <template>
                    {{- block.settings.video | video_tag: controls: block.settings.show_video_controls, autoplay: block.settings.autoplay, muted: block.settings.autoplay, playsinline: block.settings.autoplay, loop: block.settings.autoplay -}}
                  </template>
                </native-video>
              </div>

            {%- endif -%}

          {%- elsif block.type == "image" -%}

            <div class="slideshow__image-wrapper {% if mobile_image != blank %}hidden-pocket{% endif %}" {% if section.settings.transition_type == 'reveal' and section.settings.show_initial_transition or forloop.first == false %}reveal-visibility{% endif %}>
              {%- if image != blank -%}
                {%- capture sizes_attributes -%}{% if split_image != blank %}(min-width: 1000px) 50vw{% else %}100vw{% endif %}{%- endcapture -%}
                {{- image | image_url: width: 3000 | image_tag: loading: loading_attribute_value, draggable: false, class: 'slideshow__image', sizes: sizes_attributes, widths: '600,700,800,1000,1200,1400,1600,1800,2000,2200,2400,2600,2800,3000' -}}
              {%- else -%}
                {% capture image_name %}lifestyle-{% cycle '1', '2' %}{%- endcapture -%}
                {{- image_name | placeholder_svg_tag: 'slideshow__image slideshow__image--placeholder' -}}
              {%- endif -%}
            </div>

            {%- comment -%}If a second image (split image) is uploaded, we use it. Note that we use the same ratio as the first image (to have equal column height){% endcomment %}
            {%- if image != blank and split_image != blank -%}
              <div class="slideshow__image-wrapper slideshow__image-wrapper--secondary hidden-pocket" {% if section.settings.transition_type == 'reveal' and section.settings.show_initial_transition or forloop.first == false %}reveal-visibility{% endif %}>
                {{- split_image | image_url: width: 3000 | image_tag: loading: loading_attribute_value, draggable: false, class: 'slideshow__image', sizes: '50vw', widths: '600,700,800,1000,1200,1400,1600,1800,2000,2200,2400,2600,2800,3000' -}}
              </div>
            {%- endif -%}

            {%- if mobile_image != blank -%}
              <div class="slideshow__image-wrapper hidden-lap-and-up" {% if section.settings.transition_type == 'reveal' and section.settings.show_initial_transition or forloop.first == false %}reveal-visibility{% endif %}>
                {{- mobile_image | image_url: width: 3000 | image_tag: loading: loading_attribute_value, draggable: false, class: 'slideshow__image', sizes: '100vw', widths: '600,700,800,1000,1200,1400,1600,1800,2000,2200,2400,2600,2800,3000' -}}
              </div>
            {%- endif -%}

          {%- endif -%}


          {%- comment -%}
          ----------------------------------------------------------------------------------------------------------------
          CONTENT PART
          ----------------------------------------------------------------------------------------------------------------
          {%- endcomment -%}

          {%- capture section_content -%}

            {%- if block.settings.subheading != blank -%}
              <h2 class="subheading heading heading--small">
                <split-lines {% if section.settings.transition_type != 'fade' and section.settings.show_initial_transition or forloop.first == false %}reveal{% endif %}>{{ block.settings.subheading | escape }}</split-lines>
              </h2>
            {%- endif -%}

            {%- if block.settings.title != blank -%}
              <h3 class="heading {{ block.settings.heading_style }}">
                <split-lines {% if section.settings.transition_type != 'fade' and section.settings.show_initial_transition or forloop.first == false %}reveal{% endif %}>{{ block.settings.title | escape }}</split-lines>
              </h3>
            {%- endif -%}

            {%- if block.settings.content != blank or block.settings.button_text != blank -%}
              <div class="image-overlay__text-container">
                
                {%- if block.settings.content != blank-%}
                  <div class="rte {{ block.settings.content_style }}">
                    <split-lines {% if section.settings.transition_type != 'fade' and section.settings.show_initial_transition or forloop.first == false %}reveal{% endif %}>
                      {{- block.settings.content -}}
                    </split-lines>
                  </div>
                {%- endif -%}

                {%- capture buttons -%}
                  {%- if block.settings.button_1_text != blank -%}
                    <a href="{{ block.settings.button_1_link }}" class="button {{ block.settings.button_1_style }}">
                      <span class="button__text">{{ block.settings.button_1_text | escape }}</span>
                      {% if block.settings.button_1_icon != "" %}
                        <span class="button__icon">{%- include 'icon' with block.settings.button_1_icon, direction_aware: true -%}</span>
                      {% endif %}
                    </a>
                  {%- endif -%}

                  {%- if block.settings.button_2_text != blank -%}
                    <a href="{{ block.settings.button_2_link }}" class="button {{ block.settings.button_2_style }}"">
                      <span class="button__text">{{ block.settings.button_2_text | escape }}</span>
                      {% if block.settings.button_2_icon != "" %}
                        <span class="button__icon">{%- include 'icon' with block.settings.button_2_icon, direction_aware: true -%}</span>
                      {% endif %}
                    </a>
                  {%- endif -%}
                {%- endcapture -%}

                {%- if block.settings.button_1_text != blank or block.settings.button_2_text != blank -%}
                  <div class="button-group" {% if section.settings.transition_type != 'fade' and section.settings.show_initial_transition or forloop.first == false %}reveal{% endif %}>
                    {{- buttons -}}
                  </div>
                {%- endif -%}

              </div>
            {%- endif -%}

          {%- endcapture -%}

          {%- if section_content != blank -%}
            <div class="container">
              {%- assign text_alignment = block.settings.text_position | split: '_' | first -%}
              {%- assign text_position = block.settings.text_position | split: '_' | last -%}

              <div class="slideshow__text-wrapper slideshow__text-wrapper--{{ text_alignment }} vertical-breather">
                <div class="content-box content-box--medium content-box--text-{{ text_position }} content-box--{{ text_position }} text-container">
                  {{- section_content -}}
                </div>
              </div>
            </div>
          {%- endif -%}
        {%- endcapture -%}

        {%- comment -%}If only the button 1 link is filled, then we make the whole slide clickable{%- endcomment -%}

        {%- capture slide_attributes -%}
          {{ block.shopify_attributes }}
          id="block-{{ section.id }}-{{ block.id }}"
          class="slideshow__slide {% if split_image != blank %}slideshow__slide--split{% endif %} {% if section.settings.transition_type == 'sweep' %}slideshow__slide--sweep{% endif %}"
          {% unless forloop.first %}hidden{% endunless %}
          {% if section.settings.section_height == 'auto' %}
            style="--image-aspect-ratio: {{ image_aspect_ratio }}; --mobile-image-aspect-ratio: {{ mobile_image.aspect_ratio | default: image_aspect_ratio | default: 1 }};"
          {% endif %}
        {%- endcapture -%}

        <slide-show-item {% if section.settings.transition_type != 'reveal' and section.settings.show_initial_transition or forloop.first == false %}reveal-visibility{% endif %} {{ slide_attributes }}>
          
          {% if block.settings.button_1_link != blank and block.settings.button_1_text == blank and block.settings.button_2_text == blank and block.settings.button_2_link == blank  %}
            <a class="slideshow__slide-inner" href="{{ block.settings.button_1_link }}">
          {% else %}
            <div class="slideshow__slide-inner">
          {% endif %}

            {{- slide_content -}}
            
          {% if block.settings.button_1_link != blank and block.settings.button_1_text == blank and block.settings.button_2_text == blank and block.settings.button_2_link == blank  %}
            </a>
          {% else %}
            </div>
          {% endif %}

        </slide-show-item>
      {%- endfor -%}
    </div>

    {%- if section.blocks.size > 1 -%}
      <page-dots {% if section.settings.autoplay %}animation-timer{% endif %} class="slideshow__nav container">
        {%- for block in section.blocks -%}
          <button class="slideshow__progress-bar" aria-controls="block-{{ section.id }}-{{ block.id }}" {% if forloop.first %}aria-current="true"{% endif%}>
            <span class="visually-hidden">{{ 'general.accessibility.go_to_slide' | t: num: forloop.index }}</span>
          </button>
        {%- endfor -%}
      </page-dots>
    {%- endif -%}
  </slide-show>
</section>

{% schema %}
{
  "name": "🪁 Slideshow",
  "class": "shopify-section--slideshow",
  "disabled_on": {
    "groups": ["header", "custom.overlay"]
  },
  "max_blocks": 5,
  "settings": [
    {
      "type": "header",
      "content": "🪁 Kyte Settings"
    },
    {
      "type": "text",
      "id": "styling_class",
      "label": "Styling Class",
      "info": "For advanced use."
    },
    {
      "type": "text",
      "id": "anchor",
      "label": "Anchor",
      "info": "Anchor name used for jump links.",
      "placeholder": "#anchor"
    },
    {
      "type": "checkbox",
      "id": "use_padding",
      "label": "Use Padding"
    },
    {
      "type": "checkbox",
      "id": "box_shadow",
      "label": "Shadow"
    },
    {
      "type": "checkbox",
      "id": "section_extra_padding",
      "label": "Extra Vertical Padding"
    },
    {
      "type": "checkbox",
      "id": "override_blend",
      "label": "Stop section merging with previous section",
      "default": false,
      "info": "If the section \"merges\" with the previous and next sections, this separates them again."
    },
    {
      "type": "liquid",
      "id": "extra_code",
      "label": "Extra Code",
      "info": "Misc. Liquid code for embellishments."
    },
    {
      "type": "header",
      "content": "Section Settings"
    },
    {
      "type": "select",
      "id": "section_height",
      "label": "Section height",
      "options": [
        {
          "value": "auto",
          "label": "Original image ratio"
        },
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        },
        {
          "value": "fit",
          "label": "Fit screen height"
        }
      ],
      "info": "Choose \"Original image ratio\" to not cut images. [Learn more](https://help.shopify.com/en/manual/online-store/images/theme-images#best-practices-for-slideshows-and-full-width-images)",
      "default": "auto"
    },
    {
      "type": "select",
      "id": "transition_type",
      "label": "Transition type",
      "options": [
        {
          "value": "sweep",
          "label": "Sweep"
        },
        {
          "value": "reveal",
          "label": "Reveal"
        },
        {
          "value": "fade",
          "label": "Fade"
        }
      ],
      "default": "sweep"
    },
    {
      "type": "checkbox",
      "id": "show_initial_transition",
      "label": "Show initial transition",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Auto rotate between slides",
      "default": true
    },
    {
      "type": "range",
      "id": "cycle_speed",
      "min": 4,
      "max": 20,
      "step": 1,
      "unit": "sec",
      "label": "Change slides every",
      "default": 5
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background",
      "info": "Used while slideshow image is loading",
      "default": "rgba(0,0,0,0)"
    }
  ],
  "blocks": [
    {
      "type": "image",
      "name": "Image",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "info": "2160 x 1080px .jpg recommended, 1080 x 1080px .jpg recommended if split",
          "label": "Image"
        },
        {
          "type": "image_picker",
          "id": "split_image",
          "info": "1080 x 1080px .jpg recommended. Won't show up on mobile.",
          "label": "Split image"
        },
        {
          "type": "image_picker",
          "id": "mobile_image",
          "label": "Mobile image",
          "info": "1000 x 1400px .jpg recommended. If none is set, desktop image will be used."
        },
        {
          "type": "header",
          "content": "Content"
        },
        {
          "type": "select",
          "id": "text_width",
          "label": "Text width",
          "options": [
            {
              "value": "small",
              "label": "Small"
            },
            {
              "value": "medium",
              "label": "Medium"
            },
            {
              "value": "large",
              "label": "Large"
            },
            {
              "value": "fill",
              "label": "Fill screen"
            }
          ],
          "default": "medium"
        },
        {
          "type": "select",
          "id": "text_position",
          "label": "Text position",
          "options": [
            {
              "value": "top_left",
              "label": "Top left"
            },
            {
              "value": "top_center",
              "label": "Top center"
            },
            {
              "value": "top_right",
              "label": "Top right"
            },
            {
              "value": "middle_left",
              "label": "Middle left"
            },
            {
              "value": "middle_center",
              "label": "Middle center"
            },
            {
              "value": "middle_right",
              "label": "Middle right"
            },
            {
              "value": "bottom_left",
              "label": "Bottom left"
            },
            {
              "value": "bottom_center",
              "label": "Bottom center"
            },
            {
              "value": "bottom_right",
              "label": "Bottom right"
            }
          ],
          "default": "middle_center"
        },
        {
          "type": "checkbox",
          "id": "image_border_radius",
          "label": "Round Banner Corners",
          "default": true
        },
        {
          "type": "text",
          "id": "subheading",
          "label": "Subheading",
          "default": "Subheading"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Image with text overlay"
        },
        {
          "type": "select",
          "id": "heading_style",
          "label": "Heading Style",
          "options": [
            {
              "value": "",
              "label": "Default"
            },
            {
              "value": "h1",
              "label": "Heading 1"
            },
            {
              "value": "h2",
              "label": "Heading 2"
            },
            {
              "value": "h3",
              "label": "Heading 3"
            },
            {
              "value": "h4",
              "label": "Heading 4"
            },
            {
              "value": "h5",
              "label": "Heading 5"
            }
          ]
        },
        {
          "type": "text",
          "id": "content",
          "label": "Content",
          "default": "<p>Use overlay text to give your customers insight into your brand. Select imagery and text that relates to your style and story.</p>"
        },
        {
          "type": "select",
          "id": "content_style",
          "label": "Content Style",
          "options": [
            {
              "value": "",
              "label": "Default"
            },
            {
              "value": "text--large",
              "label": "Large"
            },
            {
              "value": "text--small",
              "label": "Small"
            }
          ]
        },
        {
          "type": "select",
          "id": "background_type",
          "label": "Background",
          "options": [
            {
              "value": "full_width",
              "label": "Full width"
            },
            {
              "value": "boxed",
              "label": "Boxed"
            }
          ],
          "default": "full_width"
        },
        {
          "type": "header",
          "content": "Button 1"
        },
        {
          "type": "text",
          "id": "button_1_text",
          "label": "Button 1 text"
        },
        {
          "type": "url",
          "id": "button_1_link",
          "label": "Button 1 link",
          "info": "Leave the \"Button 1 text\" and \"Button 2\" settings empty to make the slide fully clickable."
        },
        {
          "type": "select",
          "id": "button_1_icon",
          "label": "Button Icon",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "nav-arrow-right",
              "label": "Nav Arrow Right",
              "group": "Navigation"
            },
            {
              "value": "nav-arrow-right-small",
              "label": "Nav Arrow Right - Small",
              "group": "Navigation"
            }
          ]
        },
        {
          "type": "select",
          "id": "button_1_style",
          "label": "Button Style",
          "options": [
            {
              "value": "",
              "label": "Default"
            },
            {
              "group": "Styles",
              "value": "button--outline",
              "label": "Outline"
            },
            {
              "group": "Kyte Colors",
              "value": "button--primary",
              "label": "Primary"
            },
            {
              "group": "Kyte Colors",
              "value": "button--secondary",
              "label": "Secondary"
            },
            {
              "group": "Kyte Colors",
              "value": "button--tertiary",
              "label": "Tertiary"
            }
          ],
          "default": ""
        },
        {
          "type": "header",
          "content": "Button 2"
        },
        {
          "type": "text",
          "id": "button_2_text",
          "label": "Button 2 text"
        },
        {
          "type": "url",
          "id": "button_2_link",
          "label": "Button 2 link"
        },
        {
          "type": "select",
          "id": "button_2_icon",
          "label": "Button Icon",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "nav-arrow-right",
              "label": "Nav Arrow Right",
              "group": "Navigation"
            },
            {
              "value": "nav-arrow-right-small",
              "label": "Nav Arrow Right - Small",
              "group": "Navigation"
            }
          ]
        },
        {
          "type": "select",
          "id": "button_2_style",
          "label": "Button Style",
          "options": [
            {
              "value": "",
              "label": "Default"
            },
            {
              "group": "Styles",
              "value": "button--outline",
              "label": "Outline"
            },
            {
              "group": "Kyte Colors",
              "value": "button--primary",
              "label": "Primary"
            },
            {
              "group": "Kyte Colors",
              "value": "button--secondary",
              "label": "Secondary"
            },
            {
              "group": "Kyte Colors",
              "value": "button--tertiary",
              "label": "Tertiary"
            }
          ],
          "default": ""
        },
        {
          "type": "header",
          "content": "Colors"
        },
        {
          "type": "color",
          "id": "subheading_color",
          "label": "Subheading color",
          "default": "#ffffff"
        },
        {
          "type": "color",
          "id": "heading_color",
          "label": "Heading color",
          "default": "#ffffff"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text",
          "default": "#ffffff"
        },
        {
          "type": "color",
          "id": "button_background_color",
          "label": "Button background",
          "default": "#ffffff"
        },
        {
          "type": "color",
          "id": "button_text_color",
          "label": "Button text",
          "default": "#000000"
        },
        {
          "type": "color",
          "id": "overlay_color",
          "label": "Overlay",
          "default": "#000000"
        },
        {
          "type": "range",
          "id": "overlay_opacity",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "%",
          "label": "Overlay opacity",
          "default": 30
        }
      ]
    },
    {
      "type": "video",
      "name": "Video",
      "settings": [
        {
          "type": "video",
          "id": "video",
          "label": "Video"
        },
        {
          "type": "video_url",
          "id": "video_url",
          "accept": ["vimeo", "youtube"],
          "label": "Video URL"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Cover image",
          "info": "2000 x 1125px .jpg recommended. Required if you turn off autoplay."
        },
        {
          "type": "select",
          "id": "background_type",
          "label": "Video mode",
          "options": [
            {
              "value": "full_width",
              "label": "Full width"
            },
            {
              "value": "boxed",
              "label": "Boxed"
            }
          ],
          "default": "full_width"
        },
        {
          "type": "select",
          "id": "video_size",
          "label": "Video size",
          "options": [
            {
              "value": "auto",
              "label": "Original ratio"
            },
            {
              "value": "small",
              "label": "Small"
            },
            {
              "value": "medium",
              "label": "Medium"
            },
            {
              "value": "large",
              "label": "Large"
            }
          ],
          "default": "auto"
        },
        {
          "type": "checkbox",
          "id": "autoplay",
          "label": "Autoplay",
          "info": "Video is muted automatically to allow autoplay.",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_video_controls",
          "label": "Show video controls",
          "info": "Only applicable with native video.",
          "default": false
        },
        {
          "type": "header",
          "content": "Content"
        },
        {
          "type": "select",
          "id": "text_width",
          "label": "Text width",
          "options": [
            {
              "value": "small",
              "label": "Small"
            },
            {
              "value": "medium",
              "label": "Medium"
            },
            {
              "value": "large",
              "label": "Large"
            },
            {
              "value": "fill",
              "label": "Fill screen"
            }
          ],
          "default": "medium"
        },
        {
          "type": "select",
          "id": "text_position",
          "label": "Text position",
          "options": [
            {
              "value": "top_left",
              "label": "Top left"
            },
            {
              "value": "top_center",
              "label": "Top center"
            },
            {
              "value": "top_right",
              "label": "Top right"
            },
            {
              "value": "middle_left",
              "label": "Middle left"
            },
            {
              "value": "middle_center",
              "label": "Middle center"
            },
            {
              "value": "middle_right",
              "label": "Middle right"
            },
            {
              "value": "bottom_left",
              "label": "Bottom left"
            },
            {
              "value": "bottom_center",
              "label": "Bottom center"
            },
            {
              "value": "bottom_right",
              "label": "Bottom right"
            }
          ],
          "default": "middle_center"
        },
        {
          "type": "checkbox",
          "id": "image_border_radius",
          "label": "Round Banner Corners",
          "default": true
        },
        {
          "type": "text",
          "id": "subheading",
          "label": "Subheading",
          "default": "Subheading"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Image with text overlay"
        },
        {
          "type": "select",
          "id": "heading_style",
          "label": "Heading Style",
          "options": [
            {
              "value": "",
              "label": "Default"
            },
            {
              "value": "h1",
              "label": "Heading 1"
            },
            {
              "value": "h2",
              "label": "Heading 2"
            },
            {
              "value": "h3",
              "label": "Heading 3"
            },
            {
              "value": "h4",
              "label": "Heading 4"
            },
            {
              "value": "h5",
              "label": "Heading 5"
            }
          ]
        },
        {
          "type": "text",
          "id": "content",
          "label": "Content",
          "default": "<p>Use overlay text to give your customers insight into your brand. Select imagery and text that relates to your style and story.</p>"
        },
        {
          "type": "select",
          "id": "content_style",
          "label": "Content Style",
          "options": [
            {
              "value": "",
              "label": "Default"
            },
            {
              "value": "text--large",
              "label": "Large"
            },
            {
              "value": "text--small",
              "label": "Small"
            }
          ]
        },
        {
          "type": "header",
          "content": "Button 1"
        },
        {
          "type": "text",
          "id": "button_1_text",
          "label": "Button 1 text"
        },
        {
          "type": "url",
          "id": "button_1_link",
          "label": "Button 1 link",
          "info": "Leave the \"Button 1 text\" and \"Button 2\" settings empty to make the slide fully clickable."
        },
        {
          "type": "select",
          "id": "button_1_icon",
          "label": "Button Icon",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "nav-arrow-right",
              "label": "Nav Arrow Right",
              "group": "Navigation"
            },
            {
              "value": "nav-arrow-right-small",
              "label": "Nav Arrow Right - Small",
              "group": "Navigation"
            }
          ]
        },
        {
          "type": "select",
          "id": "button_1_style",
          "label": "Button Style",
          "options": [
            {
              "value": "",
              "label": "Default"
            },
            {
              "value": "button--primary",
              "label": "Primary"
            },
            {
              "value": "button--secondary",
              "label": "Secondary"
            },
            {
              "value": "button--tertiary",
              "label": "Tertiary"
            }
          ],
          "default": ""
        },
        {
          "type": "header",
          "content": "Button 2"
        },
        {
          "type": "text",
          "id": "button_2_text",
          "label": "Button 2 text"
        },
        {
          "type": "url",
          "id": "button_2_link",
          "label": "Button 2 link"
        },
        {
          "type": "select",
          "id": "button_2_icon",
          "label": "Button Icon",
          "options": [
            {
              "value": "",
              "label": "Default"
            },
            {
              "group": "Styles",
              "value": "button--outline",
              "label": "Outline"
            },
            {
              "group": "Kyte Colors",
              "value": "button--primary",
              "label": "Primary"
            },
            {
              "group": "Kyte Colors",
              "value": "button--secondary",
              "label": "Secondary"
            },
            {
              "group": "Kyte Colors",
              "value": "button--tertiary",
              "label": "Tertiary"
            }
          ]
        },
        {
          "type": "select",
          "id": "button_2_style",
          "label": "Button Style",
          "options": [
            {
              "value": "",
              "label": "Default"
            },
            {
              "group": "Styles",
              "value": "button--outline",
              "label": "Outline"
            },
            {
              "group": "Kyte Colors",
              "value": "button--primary",
              "label": "Primary"
            },
            {
              "group": "Kyte Colors",
              "value": "button--secondary",
              "label": "Secondary"
            },
            {
              "group": "Kyte Colors",
              "value": "button--tertiary",
              "label": "Tertiary"
            }
          ],
          "default": ""
        },
        {
          "type": "header",
          "content": "Colors"
        },
        {
          "type": "color",
          "id": "subheading_color",
          "label": "Subheading color",
          "default": "#ffffff"
        },
        {
          "type": "color",
          "id": "heading_color",
          "label": "Heading color",
          "default": "#ffffff"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text",
          "default": "#ffffff"
        },
        {
          "type": "color",
          "id": "button_background_color",
          "label": "Button background",
          "default": "#ffffff"
        },
        {
          "type": "color",
          "id": "button_text_color",
          "label": "Button text",
          "default": "#000000"
        },
        {
          "type": "color",
          "id": "overlay_color",
          "label": "Overlay",
          "default": "#000000"
        },
        {
          "type": "range",
          "id": "overlay_opacity",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "%",
          "label": "Overlay opacity",
          "default": 30
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "🪁 Slideshow",
      "blocks": [
        {
          "type": "image",
          "settings": {
            "title": "Slide 1"
          }
        },
        {
          "type": "image",
          "settings": {
            "title": "Slide 2"
          }
        }
      ]
    }
  ]
}
{% endschema %}