{%- if section.settings.background != settings.background and section.settings.background != 'rgba(0,0,0,0)' -%}
  {%- assign blends_with_background = false -%}
{%- else -%}
  {%- assign blends_with_background = true -%}
{%- endif -%}

{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
  CSS
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}

<style>
  #shopify-section-{{ section.id }} {
    {%- if section.settings.background == 'rgba(0,0,0,0)' -%}
      {%- assign section_background = settings.background -%}
    {%- else -%}
      {%- assign section_background = section.settings.background -%}
    {%- endif -%}

    {%- if section.settings.block_background == 'rgba(0,0,0,0)' -%}
      {%- assign block_background = settings.secondary_background -%}
    {%- else -%}
      {%- assign block_background = section.settings.block_background -%}
    {%- endif -%}


    {%- if section.settings.heading_color != blank and section.settings.heading_color != 'rgba(0,0,0,0)' -%}
      {%- assign heading_color = section.settings.heading_color -%}
    {%- else -%}
      {%- assign heading_color = settings.heading_color -%}
    {%- endif -%}

    {%- if section.settings.subheading_color != blank and section.settings.subheading_color != 'rgba(0,0,0,0)' -%}
      {%- assign subheading_color = section.settings.subheading_color -%}
    {%- else -%}
      {%- assign subheading_color = settings.subheading_color -%}
    {%- endif -%}

    {%- if section.settings.text_color != blank and section.settings.text_color != 'rgba(0,0,0,0)' -%}
      {%- assign text_color = section.settings.text_color -%}
    {%- else -%}
      {%- assign text_color = settings.text_color -%}
    {%- endif -%}


    {%- if section.settings.button_background != blank and section.settings.button_background != 'rgba(0,0,0,0)' -%}
      {%- assign button_background = section.settings.button_background -%}
    {%- else -%}
      {%- assign button_background = settings.button_background -%}
    {%- endif -%}

    {%- if section.settings.button_text_color != blank and section.settings.button_text_color != 'rgba(0,0,0,0)' -%}
      {%- assign button_text_color = section.settings.button_text_color -%}
    {%- else -%}
      {%- assign button_text_color = settings.primary_button_text_color -%}
    {%- endif -%}

    {%- if section.settings.star_color != blank and section.settings.star_color != 'rgba(0,0,0,0)' -%}
      {%- assign star_color = section.settings.star_color -%}
    {%- else -%}
      {%- assign star_color = heading_color -%}
    {%- endif -%}

    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    --subheading-color: {{ subheading_color.red }}, {{ subheading_color.green }}, {{ subheading_color.blue }};
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
    --star-color: {{ star_color.red }}, {{ star_color.green }}, {{ star_color.blue }};

    --button-background: {{ button_background.red }}, {{ button_background.green }}, {{ button_background.blue }};
    --button-text-color: {{ button_text_color.red }}, {{ button_text_color.green }}, {{ button_text_color.blue }};

    --prev-next-button-background: var(--text-color);
    --prev-next-button-color: var(--section-background);

    --section-background: {{ section_background.red }}, {{ section_background.green }}, {{ section_background.blue }};
    --block-background: {{ block_background.red }}, {{ block_background.green }}, {{ block_background.blue }};

  }

  {% comment %} Blocks {% endcomment %}

  #shopify-section-{{ section.id }} .gallery__item {

    {% if section.settings.block_background != blank and section.settings.block_background != 'rgba(0,0,0,0)' %}
      --block-background: {{ section.settings.block_background.red }}, {{ section.settings.block_background.green }}, {{ section.settings.block_background.blue }};
    {% endif %}
    {% if section.settings.block_text_color != blank and section.settings.block_text_color != 'rgba(0,0,0,0)' %}
      --text-color: {{ section.settings.block_text_color.red }}, {{ section.settings.block_text_color.green }}, {{ section.settings.block_text_color.blue }};
    {% endif %}
    {% if section.settings.block_heading_color != blank and section.settings.block_heading_color != 'rgba(0,0,0,0)' %}
      --heading-color: {{ section.settings.block_heading_color.red }}, {{ section.settings.block_heading_color.green }}, {{ section.settings.block_heading_color.blue }};
    {% endif %}
    {% if section.settings.block_subheading_color != blank and section.settings.block_subheading_color != 'rgba(0,0,0,0)' %}
      --subheading-color: {{ section.settings.block_subheading_color.red }}, {{ section.settings.block_subheading_color.green }}, {{ section.settings.block_subheading_color.blue }};
    {% endif %}
    {% if section.settings.block_star_color != blank and section.settings.block_star_color != 'rgba(0,0,0,0)' %}
      --star-color: {{ section.settings.block_star_color.red }}, {{ section.settings.block_star_color.green }}, {{ section.settings.block_star_color.blue }};
    {% endif %}

  }

  {%- for block in section.blocks -%}

    #shopify-section-{{ section.id }} #section-{{ section.id }}--gallery-item--{{ block.id }} {
      {% if block.settings.background != blank and block.settings.background != 'rgba(0,0,0,0)' %}
        --block-background: {{ block.settings.background.red }}, {{ block.settings.background.green }}, {{ block.settings.background.blue }};
      {% endif %}
      {% if block.settings.text_color != blank and block.settings.text_color != 'rgba(0,0,0,0)' %}
        --text-color: {{ block.settings.text_color.red }}, {{ block.settings.text_color.green }}, {{ block.settings.text_color.blue }};
      {% endif %}
      {% if block.settings.heading_color != blank and block.settings.heading_color != 'rgba(0,0,0,0)' %}
        --heading-color: {{ block.settings.heading_color.red }}, {{ block.settings.heading_color.green }}, {{ block.settings.heading_color.blue }};
      {% endif %}
      {% if block.settings.subheading_color != blank and block.settings.subheading_color != 'rgba(0,0,0,0)' %}
        --subheading-color: {{ block.settings.subheading_color.red }}, {{ block.settings.subheading_color.green }}, {{ block.settings.subheading_color.blue }};
      {% endif %}
      {% if block.settings.star_color != blank and block.settings.star_color != 'rgba(0,0,0,0)' %}
        --star-color: {{ block.settings.star_color.red }}, {{ block.settings.star_color.green }}, {{ block.settings.star_color.blue }};
      {% endif %}
    }

  {%- endfor -%}

</style>

{%- if section.settings.override_blend == true -%}
  {%- assign blends_with_background = false -%}
{%- endif -%}

{%- capture section_classes -%}
  {% render 'section-classes', section: section %}
{%- endcapture -%}

<section class="section custom-testimonials-slider {{ section_classes }} {% unless blends_with_background %}section--flush{% endunless %}">
  <div class="section__color-wrapper {% unless blends_with_background %}vertical-breather{% endunless %}">

    {% if section.settings.extra_code != blank %}
      {{ section.settings.extra_code }}
    {% endif %}

    {%- if section.settings.anchor -%}
      <a class="visually-hidden anchor" id="{{ section.settings.anchor }}" name="{{ section.settings.anchor }}"></a>
    {%- endif -%}

    {% comment %} Header {% endcomment %}

    {%- capture header_content -%}

      {%- if section.settings.subheading != blank -%}
        <h2 class="{% if section.settings.subheading_style contains 'heading' %}heading{% endif %} {{ section.settings.subheading_style }}">{{ section.settings.subheading | escape }}</h2>
      {%- endif -%}

      {%- if section.settings.title != blank -%}
        <h3 class="heading {{ section.settings.heading_style }}">{{ section.settings.title | escape }}</h3>
      {%- endif -%}

      {%- if section.settings.intro_text != blank -%}
        <div class="{{ section.settings.intro_text_style }}">
          {{- section.settings.intro_text -}}
        </div>
      {%- endif -%}

      {%- if section.settings.content != blank -%}
        <div class="{{ section.settings.content_style }}">
          {{- section.settings.content -}}
        </div>
      {%- endif -%}

    {%- endcapture -%}

    {%- if header_content != blank -%}
      <header class="section__header container text-container">
        {{ header_content }}
      </header>
    {%- endif -%}


    {% comment %} Content {% endcomment %}

    {%- comment -%}
    If we have more than 3 blocks we assume that by default the content may be scrollable. This may not be the case and
    the JavaScript will fired up to remove it in case it is needed, but if it is indeed scrollable this will avoid
    a reflow of the browser rendering engine. From our tests 3 is a sane default.
    {%- endcomment -%}

    <gallery-list class="gallery">
      
      <scrollable-content {% unless section.settings.show_arrows %}draggable{% endunless %} class="gallery__list-wrapper {% if section.blocks.size >= 3 %}is-scrollable{% endif %} hide-scrollbar">
        <div class="container">
          <div class="gallery__list">
            {%- for block in section.blocks -%}

              {%- assign filter_product = block.settings.product | default: product -%}

              {%- if section.settings.product_filter_enable == true and block.settings.product != blank and block.settings.product != filter_product -%}
                {%- continue -%}
              {%- endif -%}

              <gallery-item class="gallery__item" id="section-{{ section.id }}--gallery-item--{{ block.id }}" {{ block.shopify_attributes }}>
                
                <figure class="gallery__figure">

                  {%- if block.settings.image != blank -%}
                    {%- assign mobile_size = 370 | times: block.settings.image.aspect_ratio | ceil -%}
                    {%- assign tablet_size = 520 | times: block.settings.image.aspect_ratio | ceil -%}
                    {%- assign desktop_size = 600 | times: block.settings.image.aspect_ratio | ceil -%}
                    <img loading="lazy" sizes="(max-width: 740px) {{ mobile_size }}px, (max-width: 999px) {{ tablet_size }}px, {{ desktop_size }}px" class="gallery__image" {% render 'image-attributes', image: block.settings.image, sizes: '300,400,500,600,700,800,900,1000,1100,1200,1300,1400,1500,1600,1700,1800,1900,2000' %}>
                  {%- endif -%}

                  {%- if block.settings.caption != blank -%}

                    <figcaption class="gallery__caption">

                      {%- capture caption_content -%}

                        <div class="gallery__caption-rating">
                          {% for i in (i..4) %}
                            {% render 'rating-star' %}
                          {% endfor %}
                        </div>

                        {%- if block.settings.caption != blank -%}
                          <h4 class="gallery__caption-content heading h3">{{- block.settings.caption -}}</h4>
                        {%- endif -%}

                        {%- if block.settings.text != blank -%}
                          <div class="gallery__caption-text">{{- block.settings.text -}}</div>
                        {%- endif -%}

                        {%- capture caption_cite -%}
                          {%- if block.settings.name != blank -%}
                            <p class="gallery__caption-name subheading heading heading--small">{{- block.settings.name -}}</p>
                          {%- endif -%}
                          {%- if block.settings.location != blank -%}
                            <p class="gallery__caption-location subheading heading heading--xsmall">{{- block.settings.location -}}</p>
                          {%- endif -%}
                        {%- endcapture -%}

                        {%- if caption_cite != blank -%}
                          <div class="gallery__caption-cite">
                            {{ caption_cite }}
                          </div>
                        {%- endif -%}

                      {%- endcapture -%}

                      {%- if caption_content != blank -%}
                        <div class="gallery__caption-top">
                          {{ caption_content }}
                        </div>
                      {%- endif -%}

                      {%- assign review_product = all_products[block.settings.review_product] -%}

                      {%- capture caption_meta -%}

                        {%- if review_product != blank -%}

                          {%- if block.settings.review_product_heading != blank -%}
                            <p class="gallery__product-heading subheading heading heading--xsmall">
                              {{ block.settings.review_product_heading }}
                            </p>
                          {%- endif -%}
                          
                          <a class="gallery-review-product" href="{{ review_product.url }}" target="_blank">
                            <div class="gallery-review-product__image">
                              {{ review_product.featured_image | image_url: width: 200 | image_tag: sizes: '200px', widths: '200,300,400' }}
                            </div>
                            <div class="gallery-review-product__text">
                              <div class="gallery-review-product__title h5">
                                {{ review_product.title }}
                              </div>
                              <div class="gallery-review-product__prices subheading heading heading--xsmall">
                                {% render 'price', product: review_product %}
                              </div>
                            </div>
                          </a>

                        {%- endif -%}

                      {%- endcapture -%}

                      {%- if caption_meta != blank -%}
                        <div class="gallery__caption-bottom">
                          {{ caption_meta }}
                        </div>
                      {%- endif -%}
                      
                    </figcaption>
                  {%- endif -%}

                </figure>

              </gallery-item>
            {%- endfor -%}
          </div>
        </div>
      </scrollable-content>

      {%- if section.blocks.size > 1 and section.settings.show_arrows -%}
        <prev-next-buttons class="gallery__prev-next-buttons prev-next-buttons">
          <button class="gallery__arrow prev-next-button prev-next-button--prev">
            <span class="visually-hidden">{{ 'general.accessibility.previous' | t }}</span>
            {%- include 'icon' with 'nav-arrow-left', direction_aware: true -%}
          </button>

          <button class="gallery__arrow prev-next-button prev-next-button--next">
            <span class="visually-hidden">{{ 'general.accessibility.next' | t }}</span>
            {%- include 'icon' with 'nav-arrow-right', direction_aware: true -%}
          </button>
        </prev-next-buttons>
      {% endif %}

      <div class="gallery__progress-bar-wrapper container">
        <span class="gallery__progress-bar progress-bar" style="--divider: {{ section.blocks.size }}"></span>
      </div>

    </gallery-list>

    {% comment %} Footer {% endcomment %}

    {%- capture footer_content -%}
      {%- if section.settings.button_text != blank -%}
        <div class="button-wrapper">
          <a href="{{ section.settings.button_link }}" class="button {{ section.settings.button_style }} {{ section.settings.button_size }}">
            <span class="button__text">{{ section.settings.button_text | escape }}</span>
            {% if section.settings.button_icon != "" %}
              <span class="button__icon">{%- include 'icon' with section.settings.button_icon, direction_aware: true -%}</span>
            {% endif %}
          </a>
        </div>
      {%- endif -%}

      {%- if section.settings.buttons_text != blank -%}
        <div class="{{ section.settings.buttons_text_style }}">
          {{ section.settings.buttons_text }}
        </div>
      {%- endif -%}        
    {%- endcapture -%}

    {%- if footer_content != blank -%}
      <div class="section__footer container text-container">
        {{ footer_content }}
      </div>
    {%- endif -%}

  </div>
</section>

{% schema %}
{
  "name": "🪁 Reviews Slider",
  "class": "shopify-section--gallery",
  "blocks": [
    {
      "type": "image",
      "name": "Image",
      "settings": [
        {
          "type": "header",
          "content": "Media"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "1500 x 1800 px .jpg recommended"
        },
        {
          "type": "header",
          "content": "Review Author"
        },
        {
          "type": "text",
          "id": "name",
          "label": "Full Name",
          "placeholder": "Jessica Alba"
        },
        {
          "type": "text",
          "id": "first_name",
          "label": "First Name",
          "placeholder": "Jessica"
        },
        {
          "type": "text",
          "id": "last_name",
          "label": "Last Name",
          "placeholder": "Alba"
        },
        {
          "type": "text",
          "id": "location",
          "label": "Location",
          "default": "Los Angeles, CA"
        },
        {
          "type": "header",
          "content": "Review Content"
        },
        {
          "type": "text",
          "id": "caption",
          "label": "Caption",
          "default": "Lorem ipsum dolor sit amet."
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Rerum eos quia quae perspiciatis itaque numquam perferendis quisquam voluptates velit deleniti.</p>"
        },
        {
          "type": "header",
          "content": "Review Product"
        },
        {
          "type": "text",
          "id": "review_product_heading",
          "label": "Heading"
        },
        {
          "type": "product",
          "id": "review_product",
          "label": "Review product (optional)"
        },
        {
          "type": "header",
          "content": "Colors"
        },
        {
          "type": "color",
          "id": "star_color",
          "label": "Star color"
        },
        {
          "type": "color",
          "id": "background",
          "label": "Background"
        },
        {
          "type": "color",
          "id": "heading_color",
          "label": "Headings"
        },
        {
          "type": "color",
          "id": "subheading_color",
          "label": "Subheadings"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "header",
      "content": "🪁 Kyte Settings"
    },
    {
      "type": "checkbox",
      "id": "use_padding",
      "label": "Use Padding"
    },
    {
      "type": "checkbox",
      "id": "box_shadow",
      "label": "Shadow"
    },
    {
      "type": "checkbox",
      "id": "show_arrows",
      "label": "Show navigation arrows",
      "default": true
    },
    {
      "type": "header",
      "content": "Product Filter",
      "info": "These settings filter all the reviews to only show reviews for a specific product. This defaults to the current product if on a product page, but has to be set on other pages."
    },
    {
      "type": "checkbox",
      "id": "product_filter_enable",
      "label": "Enable product filter",
      "default": false
    },
    {
      "type": "product",
      "id": "product_filter_product",
      "label": "Filter for product",
      "info": "Show only reviews for this product. Defaults to current product (if on product page)."
    },
    {
      "type": "header",
      "content": "Subheading",
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "Text",
      "default": "Subheading"
    },
    {
      "type": "select",
      "id": "subheading_style",
      "label": "Style",
      "options": [
        {
          "group": "Subheading",
          "value": "",
          "label": "Default"
        },
        {
          "group": "Subheading",
          "value": "heading--small",
          "label": "Subheading"
        },
        {
          "group": "Subheading",
          "value": "heading--xsmall",
          "label": "Subheading - Small"
        },
        {
          "group": "Text",
          "value": "text--xxsmall",
          "label": "Tiny"
        },
        {
          "group": "Text",
          "value": "text--xsmall",
          "label": "Extra Small"
        },
        {
          "group": "Text",
          "value": "text--small",
          "label": "Small"
        },
        {
          "group": "Text",
          "value": "text--large",
          "label": "Large"
        }
      ],
      "default": "heading--small"
    },
    {
      "type": "header",
      "content": "Heading",
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Talk about your brand"
    },
    {
      "type": "select",
      "id": "heading_style",
      "label": "Style",
      "default": "h2",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "group": "Subheading",
          "value": "heading--small",
          "label": "Subheading"
        },
        {
          "group": "Subheading",
          "value": "heading--xsmall",
          "label": "Subheading - Small"
        },
        {
          "group": "Heading",
          "value": "h1",
          "label": "Heading 1"
        },
        {
          "group": "Heading",
          "value": "h2",
          "label": "Heading 2"
        },
        {
          "group": "Heading",
          "value": "h3",
          "label": "Heading 3"
        },
        {
          "group": "Heading",
          "value": "h4",
          "label": "Heading 4"
        },
        {
          "group": "Heading",
          "value": "h5",
          "label": "Heading 5"
        },
        {
          "group": "Heading",
          "value": "h6",
          "label": "Heading 6"
        }
      ]
    },
    {
      "type": "header",
      "content": "Intro Text",
    },
    {
      "type": "richtext",
      "id": "intro_text",
      "label": "Text"
    },
    {
      "type": "select",
      "id": "intro_text_style",
      "label": "Style",
      "default": "text--large",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "group": "Text",
          "value": "text--xxsmall",
          "label": "Tiny"
        },
        {
          "group": "Text",
          "value": "text--xsmall",
          "label": "Extra Small"
        },
        {
          "group": "Text",
          "value": "text--small",
          "label": "Small"
        },
        {
          "group": "Text",
          "value": "text--large",
          "label": "Large"
        }
      ]
    },
    {
      "type": "header",
      "content": "Content",
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "Text"
    },
    {
      "type": "select",
      "id": "content_style",
      "label": "Style",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "group": "Text",
          "value": "text--xxsmall",
          "label": "Tiny"
        },
        {
          "group": "Text",
          "value": "text--xsmall",
          "label": "Extra Small"
        },
        {
          "group": "Text",
          "value": "text--small",
          "label": "Small"
        },
        {
          "group": "Text",
          "value": "text--large",
          "label": "Large"
        }
      ],
      "default": ""
    },
    {
      "type": "header",
      "content": "Button",
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    },
    {
      "type": "select",
      "id": "button_size",
      "label": "Button size",
      "options": [
        {
          "value": "button--small",
          "label": "Small"
        },
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "button--large",
          "label": "Large"
        }
      ],
      "default": ""
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "Button style",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "button--primary",
          "label": "Primary"
        },
        {
          "value": "button--secondary",
          "label": "Secondary"
        }
      ],
      "default": ""
    },
    {
      "type": "select",
      "id": "button_icon",
      "label": "Button Icon",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "nav-arrow-right",
          "label": "Nav Arrow Right",
          "group": "Navigation"
        },
        {
          "value": "nav-arrow-right-small",
          "label": "Nav Arrow Right - Small",
          "group": "Navigation"
        }
      ]
    },
    {
      "type": "liquid",
      "id": "button_extra_attributes",
      "label": "Button Attributes"
    },
    {
      "type": "header",
      "content": "Section Colors"
    },
    {
      "type": "color",
      "id": "inner_background",
      "label": "Inner Background (Boxed Only)",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color"
    },
    {
      "type": "color",
      "id": "subheading_color",
      "label": "Subheading color"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text"
    },
    {
      "type": "header",
      "content": "Card Colors",
      "info": "Review colours. You can override these on the review blocks."
    },
    {
      "type": "color",
      "id": "block_star_color",
      "label": "Star color"
    },
    {
      "type": "color",
      "id": "block_heading_color",
      "label": "Heading color"
    },
    {
      "type": "color",
      "id": "block_subheading_color",
      "label": "Subeading color"
    },
    {
      "type": "color",
      "id": "block_text_color",
      "label": "Text color"
    },
    {
      "type": "color",
      "id": "block_background",
      "label": "Background"
    },
    {
      "type": "header",
      "content": "🪁 Advanced"
    },
    {
      "type": "text",
      "id": "styling_class",
      "label": "Styling Class",
      "info": "For advanced use."
    },
    {
      "type": "text",
      "id": "anchor",
      "label": "Anchor",
      "info": "Anchor name used for jump links.",
      "placeholder": "#anchor"
    },
    {
      "type": "checkbox",
      "id": "contain",
      "label": "Contain and clip content",
      "default": false,
      "info": "Check this if this section has a scrolling text animation or botanical illustrations."
    },
    {
      "type": "checkbox",
      "id": "override_blend",
      "label": "Stop section merging with previous section",
      "default": false,
      "info": "If the section \"merges\" with the previous and next sections, this separates them again."
    },
    {
      "type": "liquid",
      "id": "extra_code",
      "label": "Extra Code",
      "info": "Misc. Liquid code for embellishments."
    }
  ],
  "presets": [
    {
      "name": "🪁 Reviews Slider",
      "blocks": [
        {
          "type": "image"
        },
        {
          "type": "image"
        },
        {
          "type": "image"
        }
      ],
      "settings": {}
    }
  ]
}
{% endschema %}