{%- liquid
  assign geo_block = false
  assign hidden_markets = section.settings.hide_markets | split: ','
  if hidden_markets contains localization.country.iso_code
    assign geo_block = true
  endif
-%}

{%- unless geo_block -%}

{%- if section.settings.background != settings.background and section.settings.background != 'rgba(0,0,0,0)' -%}
  {%- assign blends_with_background = false -%}
{%- else -%}
  {%- assign blends_with_background = true -%}
{%- endif -%}

{%- if section.settings.override_blend == true -%}
  {%- assign blends_with_background = false -%}
{%- endif -%}

{% comment %} 
--------------------------------------------------------------------------------
Additional text Metafields
--------------------------------------------------------------------------------
{% endcomment %}

{%- assign show_additional_text_fields = false -%}
{%- if section.settings.show_additional_text_fields == true or collection.metafields.custom.show_additional_text_fields == true -%}
  {%- assign show_additional_text_fields = true -%}
{%- endif -%}

<style>
  #shopify-section-{{ section.id }} {
    {%- if section.settings.background == 'rgba(0,0,0,0)' -%}
      {%- assign section_background = settings.background -%}
    {%- else -%}
      {%- assign section_background = section.settings.background -%}
    {%- endif -%}

    {%- if section.settings.text_color == 'rgba(0,0,0,0)' -%}
      {%- assign heading_color = settings.heading_color -%}
      {%- assign text_color = settings.text_color -%}
    {%- else -%}
      {%- assign heading_color = section.settings.text_color -%}
      {%- assign text_color = section.settings.text_color -%}
    {%- endif -%}

    {%- if section.settings.button_background == 'rgba(0,0,0,0)' -%}
      {%- assign button_background = settings.primary_button_background -%}
    {%- else -%}
      {%- assign button_background = section.settings.button_background -%}
    {%- endif -%}

    {%- if section.settings.button_text_color == 'rgba(0,0,0,0)' -%}
      {%- assign button_text_color = settings.primary_button_text_color -%}
    {%- else -%}
      {%- assign button_text_color = section.settings.button_text_color -%}
    {%- endif -%}

    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
    --primary-button-background: {{ button_background.red }}, {{ button_background.green }}, {{ button_background.blue }};
    --primary-button-text-color: {{ button_text_color.red }}, {{ button_text_color.green }}, {{ button_text_color.blue }};

    --prev-next-button-background: {{ settings.background.red }}, {{ settings.background.green }}, {{ settings.background.blue }};
    --prev-next-button-color: {{ settings.text_color.red }}, {{ settings.text_color.green }}, {{ settings.text_color.blue }};

    --section-background: {{ section_background.red }}, {{ section_background.green }}, {{ section_background.blue }};
    --section-products-per-row: 2;
  }

  @media screen and (min-width: 741px) {
    #shopify-section-{{ section.id }} {
      --section-products-per-row: {{ section.settings.products_per_row | at_most: 3 }};
    }
  }

  @media screen and (min-width: 1000px) {
    #shopify-section-{{ section.id }} {
      --section-products-per-row: {{ section.settings.products_per_row | at_most: 4 }};
    }
  }

  @media screen and (min-width: 1200px) {
    #shopify-section-{{ section.id }} {
      --section-products-per-row: {{ section.settings.products_per_row }};
    }
  }

  #shopify-section-{{ section.id }} .section__footer .button {
    --button-background: {{ button_background.red }}, {{ button_background.green }}, {{ button_background.blue }};
    --button-text-color: {{ button_text_color.red }}, {{ button_text_color.green }}, {{ button_text_color.blue }};
  }

  {%- for block in section.blocks -%}

    #block-{{ section.id }}-{{ block.id }} {

    --text-color: {{ block.settings.text_color.red }}, {{ block.settings.text_color.green }}, {{ block.settings.text_color.blue }};
    --overlay-color: {{ block.settings.overlay_color }};
    --overlay-opacity: {{ block.settings.overlay_opacity }};

    }

    #block-{{ section.id }}-{{ block.id }} .collection-grid-banner__media .heading,
    #block-{{ section.id }}-{{ block.id }} .collection-grid-banner__media .rte {
      color: rgb(var(--text-color));
    }

    #block-{{ section.id }}-{{ block.id }} .collection-grid-banner__media img,
    #block-{{ section.id }}-{{ block.id }} .collection-grid-banner__media video {
      object-fit: cover;
      {% if section.settings.image_position != "" %}
        object-position: {{ section.settings.mobile_image_position }};
      {% endif %}
    }

    #block-{{ section.id }}-{{ block.id }} .collection-grid-banner__overlay {
      background: var(--overlay-color);
    }

  {% endfor %}

</style>

{%- if section.settings.override_blend == true -%}
  {%- assign blends_with_background = false -%}
{%- endif -%}

{%- capture section_classes -%}
  {% render 'section-classes', section: section %}
{%- endcapture -%}

<section class="section {{ section_classes }} custom-featured-collections {% if section.settings.horizontal_layout %}custom-featured-collection--horizontal-layout{% endif %} {% if section.settings.bottom_border %}custom-featured-collection--border-bottom{% endif %} {% unless blends_with_background %}section--flush{% endunless %}">

  {% if section.settings.extra_code != blank %}
    {{ section.settings.extra_code }}
  {% endif %}

  {%- if section.settings.anchor -%}
    <a class="visually-hidden anchor" id="{{ section.settings.anchor }}" name="{{ section.settings.anchor }}"></a>
  {%- endif -%}

  <div class="section__color-wrapper">
    <div class="container {% unless blends_with_background %}vertical-breather{% endunless %}">

      {% if section.settings.horizontal_layout == true %}
        <div class="horizontal-layout-wrapper">
      {% endif %}

      {%- if section.settings.title != blank or section.settings.subheading != blank or section.settings.content != blank -%}

        {% if section.settings.horizontal_header == true %}
          <header class="horizontal-header horizontal-header--tabs">
        {% else %}
          <header class="section__header {% if section.settings.content != blank %}section__header--tight{% endif %}">
        {% endif %}

          <div class="text-container">
            
            {%- if section.settings.subheading != blank -%}
              <h2 class="heading heading--small">{{ section.settings.subheading }}</h2>
            {%- endif -%}

            {%- if section.settings.title != blank -%}
              <h3 class="heading h3 h2--mobile">{{ section.settings.title }}</h3>
            {%- endif -%}

            {%- if section.settings.content != blank -%}
              <div class="rte {{ section.settings.content_style }}">{{- section.settings.content -}}</div>
            {%- endif -%}

            {%- if section.settings.button_text != blank -%}
              <div class="button-wrapper">
                <a class="button {{ section.settings.button_size }} {{ section.settings.button_style }}" href="{{ section.settings.button_url | default: collection.url }}">
                  <span class="button__text">{{ section.settings.button_text }}</span>
                  {% if section.settings.button_icon != "" %}
                    <span class="button__icon">{%- include 'icon' with section.settings.button_icon, block: true, direction_aware: true -%}</span>
                  {% endif %}
                </a>
              </div>
            {%- endif -%}

          </div>

          {%- if section.blocks.size > 1 -%}
            <tabs-nav class="tabs-nav tabs-nav--center tabs-nav--edge2edge">
              <scrollable-content class="tabs-nav__scroller hide-scrollbar">
                <div class="tabs-nav__scroller-inner">
                  <div class="tabs-nav__item-list">
                    {%- for block in section.blocks -%}
                      {%- capture on_boarding_title -%}{{ 'general.onboarding.collection_title' | t }} {{ forloop.index }}{%- endcapture -%}

                      <button type="button" class="tabs-nav__item heading heading--small" aria-expanded="{% if forloop.first %}true{% else %}false{% endif %}" aria-controls="block-{{ section.id }}-{{ block.id }}" {{ block.shopify_attributes }}>
                        {{- block.settings.label | default: block.settings.collection.title | default: on_boarding_title -}}
                      </button>
                    {%- endfor -%}
                  </div>
                </div>
              </scrollable-content>
            </tabs-nav>
          {%- endif -%}

        </header>
      {%- endif -%}

      <div class="featured-collections">
        {%- for block in section.blocks -%}
          {%- assign collection = block.settings.collection -%}
          {%- assign smallest_image_aspect_ratio = 0 -%}

          {%- assign text_position = block.settings.text_position -%}
          {%- assign text_alignment = block.settings.text_alignment -%}

          <product-list {% if settings.stagger_products_apparition %}stagger-apparition{% endif %} {% unless forloop.first %}hidden{% endunless %} id="block-{{ section.id }}-{{ block.id }}" class="product-list product-list--center">

            {% if block.settings.collection_heading != blank or block.settings.collection_description != blank %}

              <div class="product-list__header content-box content-box--{{ block.settings.text_width }} content-box--text-{{ text_alignment }} content-box--{{ text_position }} text-container">

                {% if block.settings.collection_heading != blank %}
                  <h3 class="heading h2 h3--mobile text-align--center">{{ block.settings.collection_heading }}</h3>
                {% endif %}

                {% if block.settings.collection_description != blank %}
                  <div class="rte {{ block.settings.text_style }}">{{ block.settings.collection_description }}</div>
                {% endif %}

              </div>
              
            {% endif %}

            <div class="product-list__inner-wrapper">

              <div {% unless section.settings.stack_products %}class="scroller"{% endunless %}>

                <div class="product-list__inner {% unless section.settings.stack_products %}product-list__inner--scroller hide-scrollbar{% endunless %}">

                  {%- if block.settings.image != blank or block.settings.video_url != blank or block.settings.video_filename != blank -%}

                    <div class="collection-grid-banner collection-grid-banner--span-{{ block.settings.column_span }} {% if block.settings.column_span >= 2 %}collection-grid-banner--mobile-fullwidth{% endif %} collection-grid-banner--{{ block.settings.media_size }}" id="block-{{ section.id }}-{{ block.id }}" {% if reveal %}reveal{% endif %} {{ block.shopify_attributes }}>

                      {% assign image_size_string = 350 | times: block.settings.column_span | append: "x" %}

                      {%- if block.settings.link_url != blank -%}
                        <a class="collection-grid-banner__media" href="{{ block.settings.link_url }}">
                      {%- else -%}
                        <a class="collection-grid-banner__media" href="{{ collection.url }}">
                      {%- endif -%}

                        {%- if block.settings.overlay_subheading != blank or block.settings.overlay_title != blank or block.settings.overlay_text != blank -%}

                          <div class="collection-grid-banner__overlay">
        
                            <div class="collection-grid-banner__overlay-inner">

                              {%- if block.settings.overlay_subheading != blank -%}
                                <p class="collection-grid-banner__overlay-subheading heading heading--xsmall">{{ block.settings.overlay_subheading }}</p>
                              {%- endif -%}

                              {%- if block.settings.overlay_title != blank -%}
                                <p class="collection-grid-banner__overlay_title heading h3">{{ block.settings.overlay_title }}</p>
                              {%- endif -%}

                              {%- if block.settings.overlay_text != blank -%}
                                <div class="collection-grid-banner__overlay_text rte">{{ block.settings.overlay_text }}</div>
                              {%- endif -%}

                              {%- if block.settings.overlay_link_text != blank -%}
                                <div class="button-wrapper">
                                  <div class="link link--animted">{{ block.settings.overlay_link_text }}</div>
                                </div>
                              {%- endif -%}

                            </div>

                          </div>

                        {%- endif -%}

                        {%- if block.settings.video_filename != blank -%}

                          <video
                            playsinline
                            loop
                            muted
                            autoplay
                            {% if block.settings.image != blank %}
                            poster="{{ block.settings.image | img_url: image_size_string }}"
                            {% endif %}
                            id="CollectionGridVideo-{{ block.id }}"
                            class="collection-grid-banner__video">
                              <source src="{{ block.settings.video_filename | file_url }}" type="{{ source.mime_type }}">
                              Your browser does not support the video tag.
                          </video>

                        {%- elsif block.settings.video_url != blank -%}

                          <external-video autoplay provider="{{ block.settings.video_url.type | escape }}" class="video-wrapper video-wrapper--{{ block.settings.media_size }}">
                            <template>
                              {%- if block.settings.video_url.type == 'youtube' -%}
                                <iframe title="Video" id="player-{{ section.id }}" src="https://www.youtube.com/embed/{{ block.settings.video_url.id }}?playsinline=1&autoplay=1&mute=1&loop=1&playlist={{ block.settings.video_url.id }}&enablejsapi=1&controls=0&rel=0&modestbranding=1&origin=https://{{ request.host }}" allow="autoplay; fullscreen"></iframe>
                              {%- elsif block.settings.video_url.type == 'vimeo' -%}
                                <iframe title="Video" id="player-{{ section.id }}" src="https://player.vimeo.com/video/{{ block.settings.video_url.id }}?background=1&loop=1&transparent=0&responsive=1&portrait=0&title=0&byline=0&color={{ settings.text_color | remove_first: '#' }}" allow="autoplay; fullscreen"></iframe>
                              {%- endif -%}
                            </template>
                          </external-video>

                        {%- elsif block.settings.image != blank -%}

                          <img 
                            {% render 'image-attributes', image: block.settings.image, sizes: '200,300,400,500,600,700,800,900,1000,1100,1200,1300,1400,1500,1600' %}
                            alt="{{ block.settings.image.alt }}">

                        {%- endif -%}

                      {%- if block.settings.link_url != blank -%}
                        </a>
                      {%- else -%}
                        </a>
                      {%- endif -%}

                    </div>
                    
                  {%- endif -%}

                  {%- assign number_of_products_minus_one = section.settings.products_per_row | minus: 1 -%}
                  {%- assign gap_width = 24.0 | divided_by: section.settings.products_per_row | times: number_of_products_minus_one -%}
                  {%- capture sizes_attribute -%}(max-width: 740px) 52vw, calc(min(100vw - 80px, 1520px) / {{ section.settings.products_per_row }} - {{ gap_width | ceil }}px){%- endcapture -%}

                  {%- for product in collection.products limit: section.settings.products_count -%}

                    {%- if product.featured_media -%}
                      {%- assign smallest_image_aspect_ratio = smallest_image_aspect_ratio | at_least: product.featured_media.aspect_ratio -%}
                    {%- endif -%}

                    {% if section.settings.stack_products == true %}
                      {% if section.settings.product_in_collection %}
                        {%- render 'product-item', product: product, collection: collection, show_cta: section.settings.show_cta, block: block, sizes_attribute: sizes_attribute, reveal: settings.stagger_products_apparition, show_additional_text_fields: show_additional_text_fields, aspect_ratio: section.settings.aspect_ratio -%}
                      {% else %}
                        {%- render 'product-item', product: product, show_cta: section.settings.show_cta, block: block, sizes_attribute: sizes_attribute, reveal: settings.stagger_products_apparition, show_additional_text_fields: show_additional_text_fields, aspect_ratio: section.settings.aspect_ratio -%}
                      {% endif %}
                    {% else %}
                      <div class="product-item-container">
                        {% if section.settings.product_in_collection %}
                          {%- render 'product-item--slider', product: product, collection: collection, show_cta: true, block: block, sizes_attribute: sizes_attribute, reveal: settings.stagger_products_apparition, show_additional_text_fields: show_additional_text_fields, aspect_ratio: section.settings.aspect_ratio -%}
                        {% else %}
                          {%- render 'product-item--slider', product: product, show_cta: true, block: block, sizes_attribute: sizes_attribute, reveal: settings.stagger_products_apparition, show_additional_text_fields: show_additional_text_fields, aspect_ratio: section.settings.aspect_ratio -%}
                        {% endif %}
                        
                      </div>
                    {% endif %}
                  {%- else -%}
                    {%- assign smallest_image_aspect_ratio = 1 -%}

                    {%- for i in (1..section.settings.products_count) -%}
                      {%- capture product_image -%}product-{% cycle '1', '2', '3', '4', '5' %}{% endcapture %}
                      {%- render 'product-item-placeholder', product_image: product_image, show_cta: section.settings.show_cta, reveal: settings.stagger_products_apparition -%}
                    {%- endfor -%}
                  {%- endfor -%}
                </div>
              </div>

              {%- unless section.settings.stack_products -%}
                {%- if collection.products_count == 0 -%}
                  {%- assign products_shown = section.settings.products_count -%}
                {%- else -%}
                  {%- assign products_shown = collection.products_count | default: section.settings.products_count | at_most: section.settings.products_count -%}
                {%- endif -%}

                {%- if products_shown > section.settings.products_per_row -%}
                  {%- if smallest_image_aspect_ratio == 0 -%}
                    {%- assign smallest_image_aspect_ratio = 1 -%}
                  {%- endif -%}

                  <prev-next-buttons class="product-list__prev-next hidden-pocket" style="--smallest-image-aspect-ratio: {{ smallest_image_aspect_ratio }}">
                    <button class="product-list__arrow prev-next-button prev-next-button--prev" disabled>
                      <span class="visually-hidden">{{ 'general.accessibility.previous' | t }}</span>
                      {%- include 'icon' with 'nav-arrow-left', block: true, direction_aware: true -%}
                    </button>

                    <button class="product-list__arrow prev-next-button prev-next-button--next">
                      <span class="visually-hidden">{{ 'general.accessibility.next' | t }}</span>
                      {%- include 'icon' with 'nav-arrow-right', block: true, direction_aware: true -%}
                    </button>
                  </prev-next-buttons>
                {%- endif -%}
              {%- endunless -%}

            </div>

            {%- if block.settings.button_text != blank -%}
              <div class="section__footer">
                <a class="button {{ block.settings.button_size }}  {{ block.settings.button_style }}" href="{{ block.settings.button_url | default: collection.url }}">
                  <span class="button__text">{{ block.settings.button_text }}</span>
                  {% if block.settings.button_icon != "" %}
                    <span class="button__icon">{%- include 'icon' with block.settings.button_icon, block: true, direction_aware: true -%}</span>
                  {% endif %}
                </a>
              </div>
            {%- endif -%}

          </product-list>
        {%- endfor -%} 
      </div>

      {% if section.settings.horizontal_layout == true %}
        </div>
      {% endif %}

    </div>
  </div>
</section>

{%- endunless -%}
{% schema %}
{
  "name": "🪁 Featured collections",
  "class": "shopify-section--featured-collections",
  "max_blocks": 5,
  "disabled_on": {
    "templates": ["password"],
    "groups": ["header", "custom.overlay"]
  },
  "blocks": [
    {
      "type": "collection",
      "name": "Collection",
      "settings": [
        {
          "type": "collection",
          "id": "collection",
          "label": "Collection"
        },
        {
          "type": "text",
          "id": "label",
          "label": "Tab label",
          "info": "Shown when more than 1 collection is featured. Collection title is used if none is set."
        },
        {
          "type": "text",
          "id": "collection_heading",
          "label": "Collection Heading",
          "info": "Shown above the products, hidden when not in use."
        },
        {
          "type": "richtext",
          "id": "collection_description",
          "label": "Collection Description",
          "info": "Shown above the products, hidden when not in use."
        },
        {
          "type": "select",
          "id": "text_width",
          "label": "Text width",
          "options": [
            {
              "value": "small",
              "label": "Small"
            },
            {
              "value": "medium",
              "label": "Medium"
            },
            {
              "value": "large",
              "label": "Large"
            },
            {
              "value": "fill",
              "label": "Fill screen"
            }
          ],
          "default": "medium"
        },
        {
          "type": "select",
          "id": "text_position",
          "label": "Text position",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "center"
        },
        {
          "type": "select",
          "id": "text_alignment",
          "label": "Text alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "center"
        },
        {
          "type": "select",
          "id": "text_style",
          "label": "Text style",
          "options": [
            {
              "value": "",
              "label": "Default"
            },
            {
              "value": "text--large",
              "label": "Large"
            },
            {
              "value": "text--small",
              "label": "Small"
            }
          ],
          "default": ""
        },
        {
          "type": "text",
          "id": "button_text",
          "label": "Button text"
        },
        {
          "type": "select",
          "id": "button_icon",
          "label": "Button Icon",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "nav-arrow-right",
              "label": "Nav Arrow Right",
              "group": "Navigation"
            },
            {
              "value": "nav-arrow-right-small",
              "label": "Nav Arrow Right - Small",
              "group": "Navigation"
            }
          ]
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Button style",
          "options": [
            {
              "value": "",
              "label": "Default"
            },
            {
              "value": "button--primary",
              "label": "Primary"
            },
            {
              "value": "button--secondary",
              "label": "Secondary"
            }
          ],
          "default": ""
        },
        {
          "type": "select",
          "id": "button_size",
          "label": "Button size",
          "options": [
            {
              "value": "button--small",
              "label": "Small"
            },
            {
              "value": "",
              "label": "Default"
            },
            {
              "value": "button--large",
              "label": "Large"
            }
          ],
          "default": ""
        },
        {
          "type": "url",
          "id": "button_url",
          "label": "Button link",
          "info": "If empty, the collection URL is used."
        },
        {
          "type": "header",
          "content": "Banner"
        },
        {
          "type": "text",
          "id": "video_filename",
          "label": "Video Filename",
          "info": "Enter the filename of a file uploaded to the [Files dashboard](/admin/settings/files). (e.g. 'banner.mp4')"
        },
        {
          "type": "video_url",
          "id": "video_url",
          "accept": ["vimeo", "youtube"],
          "label": "Video URL"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "Also used as a placeholder image for uploaded videos. 1200 x 600px .jpg recommended (if two blocks), 2400 x 800px .jpg recommended (if one block)"
        },
        {
          "type": "select",
          "id": "image_position",
          "label": "Image Position",
          "options": [
            {
              "value": "",
              "label": "Default"
            },
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "right",
              "label": "Right"
            },
            {
              "value": "top",
              "label": "Top"
            },
            {
              "value": "bottom",
              "label": "Bottom"
            }
          ]
        },
        {
          "type": "url",
          "id": "link_url",
          "label": "Link URL"
        },
        {
          "type": "text",
          "id": "overlay_subheading",
          "label": "Subheading"
        },
        {
          "type": "text",
          "id": "overlay_title",
          "label": "Heading"
        },
        {
          "type": "text",
          "id": "overlay_link_text",
          "label": "Link text"
        },
        {
          "type": "text",
          "id": "overlay_text",
          "label": "Text" 
        },
        {
          "type": "color_background",
          "id": "overlay_color",
          "label": "Overlay Color",
          "default": "rgba(0,0,0,.2)"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text color",
          "default": "#ffffff"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "header",
      "content": "🪁 Kyte Settings"
    },
    {
      "type": "text",
      "id": "styling_class",
      "label": "Styling Class",
      "info": "For advanced use."
    },
    {
      "type": "text",
      "id": "anchor",
      "label": "Anchor",
      "info": "Anchor name used for jump links.",
      "placeholder": "#anchor"
    },
    {
      "type": "checkbox",
      "id": "use_padding",
      "label": "Use Padding"
    },
    {
      "type": "checkbox",
      "id": "box_shadow",
      "label": "Shadow"
    },
    {
      "type": "checkbox",
      "id": "section_extra_padding",
      "label": "Extra Vertical Padding"
    },
    {
      "type": "checkbox",
      "id": "override_blend",
      "label": "Stop section merging with previous section",
      "default": false,
      "info": "If the section \"merges\" with the previous and next sections, this separates them again."
    },
    {
      "type": "liquid",
      "id": "extra_code",
      "label": "Extra Code",
      "info": "Misc. Liquid code for embellishments."
    },
    {
      "type": "select",
      "id": "aspect_ratio",
      "label": "Product Image Aspect Ratio",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "natural",
          "label": "Natural"
        },
        {
          "value": "short",
          "label": "Short (4:3)"
        },
        {
          "value": "square",
          "label": "Square (1:1)"
        },
        {
          "value": "tall",
          "label": "Tall (2:3)"
        }
      ],
      "default": ""
    },
    {
      "type": "header",
      "content": "Section Settings"
    },
    {
      "type": "checkbox",
      "id": "show_additional_text_fields",
      "label": "Show Fabric Text Field",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "product_in_collection",
      "label": "Link to product in collection",
      "default": false,
      "info": "Product card links will go to a URL that contains the collection and product."
    },
    {
      "type": "text",
      "id": "hide_markets",
      "label": "Hide in countries",
      "info": "Important! Use 2-country letter codes and commas. For example: CA,FR,UK"
    },
    {
      "type": "checkbox",
      "id": "horizontal_header",
      "label": "Horizontal Header",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "horizontal_layout",
      "label": "Horizontal Layout",
      "default": false,
      "info": "Move header to the left, inline with the collection slider."
    },
    {
      "type": "checkbox",
      "id": "bottom_border",
      "label": "Bottom Border",
      "default": true
    },
    {
      "type": "select",
      "id": "content_style",
      "label": "Content Style",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "text--large",
          "label": "Large"
        },
        {
          "value": "text--small",
          "label": "Small"
        }
      ]
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text"
    },
    {
      "type": "select",
      "id": "button_icon",
      "label": "Button Icon",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "nav-arrow-right",
          "label": "Nav Arrow Right",
          "group": "Navigation"
        },
        {
          "value": "nav-arrow-right-small",
          "label": "Nav Arrow Right - Small",
          "group": "Navigation"
        }
      ]
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "Button style",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "button--primary",
          "label": "Primary"
        },
        {
          "value": "button--secondary",
          "label": "Secondary"
        }
      ],
      "default": ""
    },
    {
      "type": "select",
      "id": "button_size",
      "label": "Button size",
      "options": [
        {
          "value": "button--small",
          "label": "Small"
        },
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "button--large",
          "label": "Large"
        }
      ],
      "default": ""
    },
    {
      "type": "url",
      "id": "button_url",
      "label": "Button link",
      "info": "If empty, the collection URL is used."
    },
    {
      "type": "header",
      "content": "Section Settings"
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "Subheading",
      "default": "Your title"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Collection"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "Content"
    },
    {
      "type": "range",
      "id": "products_count",
      "label": "Products to show",
      "min": 4,
      "max": 50,
      "step": 1,
      "default": 8
    },
    {
      "type": "range",
      "id": "products_per_row",
      "label": "Products per row (desktop)",
      "min": 2,
      "max": 6,
      "step": 1,
      "default": 4
    },
    {
      "type": "checkbox",
      "id": "stack_products",
      "label": "Stack products",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_cta",
      "label": "Show add to cart below info",
      "info": "If enabled, we recommend using 4 products per row at maximum.",
      "default": false
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text",
      "default": "rgba(0,0,0,0)"
    }
  ],
  "presets": [
    {
      "name": "🪁 Featured collections",
      "blocks": [
        {
          "type": "collection"
        }
      ],
      "settings": {}
    }
  ]
}
{% endschema %}