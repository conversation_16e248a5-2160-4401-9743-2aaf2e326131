{%- if section.settings.enable -%}

  <preorder-modal section="{{ section.id }}" id="preorder-popup" class="modal">

    <div class="modal__overlay"></div>

    <div class="modal__content section__color-wrapper">
      <button type="button" class="modal__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
        {%- render 'icon' with 'close' -%}
      </button>

      <div class="preorder-modal">

        <div class="preorder-modal__content text-container text--center">

          {%- if section.settings.title != blank -%}
            <h2 class="heading h3">{{ section.settings.title | escape }}</h2>
          {%- endif -%}

          {%- if section.settings.content != blank -%}
            {{- section.settings.content -}}
          {%- endif -%}

          <div class="input">
            <button type="button" data-action="close" data-preorder-notice-add-to-cart is="loader-button" class="button button--primary button--small button--full">
              <span class="button__text">
                {{ section.settings.button_text }}
              </span>
            </button>
          </div>

        </div>
      </div>
    </div>

  </preorder-modal>
  
{%- endif -%}

{% schema %}
{
  "name": "Preorder Popup",
  "class": "shopify-section--popup",
  "settings": [
    {
      "type": "checkbox",
      "id": "enable",
      "label": "Enable",
      "default": false
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Hey!"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button Text",
      "default": "OK!"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "Text",
      "default": "<p>You have preorder items in your cart. To add this in-stock item to your cart, first remove your preorder products.</p>"
    }
  ]
}
{% endschema %}