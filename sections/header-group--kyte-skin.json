{"type": "header", "name": "🪁 Kyte Skin - Header group", "sections": {"announcement-bar": {"type": "tabbed-announcement-bar", "blocks": {"message_THaYyY": {"type": "message", "disabled": true, "settings": {"text": "<p><strong>BOGO 50% OFF</strong> all socks, undies, or bras 🧦 </p>", "learn_more_text": "More Details", "title": "", "content": "<p>Buy One, Get One 50% Off all socks, undies, or bras. Discount applies automatically to every second item of equal or lesser value within the same category. Ends Monday at 11:59 PM CT.</p>", "button_text": "", "button_link": "#", "rewards_points_enable": true}}, "15314d8d-28f1-40b2-b1b7-2473d46461ba": {"type": "message", "settings": {"text": "<p>Free Shipping On US Orders $85+</p>", "learn_more_text": "", "title": "", "content": "", "button_text": "", "button_link": "shopify://pages/shipping-details", "rewards_points_enable": true}}, "message_f9barg": {"type": "message", "settings": {"text": "<p>Become a <a href=\"/pages/frequent-flyer-rewards\" title=\"Frequent Flyer Rewards\">Frequent Flyer</a> and earn 10 points for every $1 spent</p>", "learn_more_text": "", "title": "", "content": "", "button_text": "", "button_link": "", "rewards_points_enable": false}}, "message_G9WXHH": {"type": "message", "settings": {"text": "<p>Butter-soft bamboo fabric with 500,000+ 5-star reviews</p>", "learn_more_text": "", "title": "", "content": "", "button_text": "", "button_link": "", "rewards_points_enable": true}}, "message_GYMXFp": {"type": "message", "disabled": true, "settings": {"text": "<p>Sale orders may take up to <strong>4 weeks </strong>to be fulfilled. </p>", "learn_more_text": "Learn more", "title": "", "content": "", "button_text": "", "button_link": "", "rewards_points_enable": true}}}, "block_order": ["message_THaYyY", "15314d8d-28f1-40b2-b1b7-2473d46461ba", "message_f9barg", "message_G9WXHH", "message_GYMXFp"], "settings": {"announcement_bar_position": "non_sticky", "announcement_text_size": "small", "autoplay": true, "cycle_speed": 5, "background": "#4a5244", "text_color": "#ffffff", "button_background": "#000000", "button_text_color": "#ffffff"}}, "top-menu-bar": {"type": "tabbed-top-menu-bar", "settings": {"show_rewards_points": true, "show_icons": true, "top_menu_bar_menu": "2022-voltage-top-menu-bar", "top_menu_bar_position": "non_sticky", "background_2": "", "background": "rgba(0,0,0,0)", "link_color": "", "logo_color": "", "heading_color": "", "text_color": "#4a5244", "icon_color": "", "border_color": ""}}, "header": {"type": "tabbed-header", "blocks": {"mega_menu_iF9BpY": {"type": "mega_menu_products", "settings": {"header_title": "Discover Kyte Skin", "footer_button_text": "Shop All", "footer_button_link": "shopify://collections/shop-kyte-skin", "footer_button_size": "button--small", "footer_button_style": "button--primary", "footer_button_icon": "nav-arrow-right", "colors_enable": true, "colors_link_group_handle": "", "menu_item": "Shop Skin", "images_position": "right", "product_1": "eczema-cream-1", "product_1_hide_market": "", "product_2": "sensitive-shampoo-body-wash", "product_2_hide_market": "", "product_3": "sensitive-lotion", "product_3_hide_market": "", "product_4": "baby-massage-oil", "product_4_hide_market": "", "product_5": "solid-body-wash-bar", "product_5_hide_market": "", "product_6": "", "product_6_hide_market": "", "header_title_style": "heading h3", "image_heading_style": "", "image_text_style": "heading heading--<PERSON><PERSON><PERSON> subheading", "background": "#eef0ea", "heading_color": "#4a5244", "subheading_color": "#a3ac9b", "text_color": "#a3ac9b", "button_background": "", "button_text_color": ""}}, "mega_menu_EUc9FB": {"type": "mega_menu", "settings": {"header_title": "", "footer_button_text": "", "footer_button_link": "", "footer_button_size": "", "footer_button_style": "", "footer_button_icon": "", "colors_enable": true, "colors_link_group_handle": "", "menu_item": "Shop Bath", "images_position": "right", "hide_markets_1": "", "image_1": "shopify://shop_images/Kyte_Baby_r1_5117_1x1_661e7a7a-a5cd-4ca3-9231-ea2606c8b278.jpg", "image_1_heading": "Bathing", "image_1_text": "Shop All", "image_1_link": "shopify://collections/bathing-kyte-skin", "hide_markets_2": "", "image_2": "shopify://shop_images/CradleCapBrushtexture-01_3.jpg", "image_2_heading": "Brushes", "image_2_text": "Shop All", "image_2_link": "shopify://collections/brushes-kyte-skin", "hide_markets_3": "", "image_3": "shopify://shop_images/Infant_Bath_Robe_in_Cloud-01.jpg", "image_3_heading": "Robes", "image_3_text": "Shop All", "image_3_link": "shopify://collections/robes-kyte-skin", "hide_markets_4": "", "image_4": "shopify://shop_images/Mom_in_Bisque_Gingham_Pajama_Baby_in_Sage_Hooded_Towel-2.jpg", "image_4_heading": "Towels & Washcloths", "image_4_text": "Shop All", "image_4_link": "shopify://collections/towels-washcloths-kyte-skin", "hide_markets_5": "", "image_5_heading": "", "image_5_text": "", "image_5_link": "", "image_6_heading": "", "image_6_text": "", "image_6_link": "", "header_title_style": "heading h3", "image_heading_style": "", "image_text_style": "heading heading--<PERSON><PERSON><PERSON> subheading", "background": "rgba(0,0,0,0)", "heading_color": "rgba(0,0,0,0)", "subheading_color": "rgba(0,0,0,0)", "text_color": "rgba(0,0,0,0)", "button_background": "", "button_text_color": ""}}}, "block_order": ["mega_menu_iF9BpY", "mega_menu_EUc9FB"], "custom_css": ["#desktop-menu-1 {background-image: url(https://cdn.shopify.com/s/files/1/0019/7106/0847/files/section-botanicals-top-1-combined.svg?v=1761058366); background-position: top; background-size: 100%; background-repeat: no-repeat;}"], "settings": {"enable_sticky_header": true, "logo_link": "shopify://pages/kyte-skin-splash", "logo": "shopify://shop_images/logo-black-horizontal_da29d415-a8b2-480b-8164-0fae0c870880.png", "logo_max_width": 200, "mobile_logo_max_width": 145, "navigation_menu": "kyte-skin-main-menu", "sidebar_navigation_menu": "", "secondary_menu": "2022-voltage-top-menu-bar", "show_locale_selector": false, "show_country_selector": false, "search_menu": "2022-voltage-search-menu", "floating_badge_enable": true, "floating_badge_image": "shopify://shop_images/Holiday-Shoppe-Badge.png", "floating_badge_link": "shopify://pages/holiday-market", "floating_badge_label_text": "", "floating_badge_border_width": 1, "floating_badge_color_background": "#ffffff", "floating_badge_color_border": "linear-gradient(54deg, rgba(227, 222, 210, 1) 4%, rgba(227, 222, 210, 1) 100%)", "floating_badge_color_label_background": "#eef0ea", "floating_badge_color_label_text": "#ffffff", "background": "", "background_2": "#eef0ea", "link_color": "", "logo_color": "", "heading_color": "", "text_color": "", "icon_color": "", "button_color": "", "border_color": "", "bubble_color": ""}}}, "order": ["announcement-bar", "top-menu-bar", "header"]}