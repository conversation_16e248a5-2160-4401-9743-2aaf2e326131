{% assign icon_list = 'nav-arrow-left, nav-arrow-right, nav-arrow-left-small, nav-arrow-right-small, chevron, chevron-back, dropdown-arrow-right, close, check, minus, plus, minus-big, plus-big, form-success, form-error, filters, product-tab-left, product-tab-right, image-zoom, spinner, discount-badge, play, share, header-hamburger, header-search, header-customer, header-cart, header-shopping-cart, header-tote-bag, header-email, quick-buy-shopping-bag, quick-buy-shopping-cart, quick-buy-tote-bag, lock, comment, rating-star, rating-star-half, picto-coupon, picto-gift, picto-taxes, picto-warranty, picto-like, picto-store, picto-love, picto-donation, picto-store-pickup, picto-box, picto-address, picto-address-pin, picto-fast-delivery, picto-delivery-truck, picto-return-box, picto-worldwide, picto-plane, picto-credit-card, picto-lock, picto-secure-payment, picto-shield, picto-mobile, picto-phone, picto-chat, picto-send, picto-email, picto-customer-support, picto-operator, picto-mask, picto-virus, facebook, instagram, pinterest, twitter, fancy, linkedin, snapchat, tiktok, tumblr, vimeo, wechat, youtube, line, reddit, spotify, facebook-share-mobile, pinterest-share-mobile, twitter-share-mobile, email-share-mobile, store-availability-in-stock, store-availability-out-of-stock, media-model-badge, media-video-badge, media-view-in-space' %}

{% assign icon_list__ui = 'nav-arrow-left, nav-arrow-right, nav-arrow-left-small, nav-arrow-right-small, chevron, chevron-back, dropdown-arrow-right, close, check, minus, plus, minus-big, plus-big, form-success, form-error, filters, product-tab-left, product-tab-right, image-zoom, spinner, discount-badge, play, share, header-hamburger, header-search, header-customer, header-cart, header-shopping-cart, header-tote-bag, header-email, quick-buy-shopping-bag, quick-buy-shopping-cart, quick-buy-tote-bag, lock, comment, rating-star, rating-star-half' %}
{% assign icon_list__picto = 'picto-coupon, picto-gift, picto-taxes, picto-warranty, picto-like, picto-store, picto-love, picto-donation, picto-store-pickup, picto-box, picto-address, picto-address-pin, picto-fast-delivery, picto-delivery-truck, picto-return-box, picto-worldwide, picto-plane, picto-credit-card, picto-lock, picto-secure-payment, picto-shield, picto-mobile, picto-phone, picto-chat, picto-send, picto-email, picto-customer-support, picto-operator, picto-mask, picto-virus' %}
{% assign icon_list__social = 'facebook, instagram, pinterest, twitter, fancy, linkedin, snapchat, tiktok, tumblr, vimeo, wechat, youtube, line, reddit, spotify' %}
{% assign icon_list__social_share = 'pinterest-share-mobile, twitter-share-mobile, email-share-mobile' %}
{% assign icon_list__pickup = 'store-availability-in-stock, store-availability-out-of-stock' %}
{% assign icon_list__media = 'media-model-badge, media-video-badge, media-view-in-space' %}
{% assign icon_list__other = 'shopify-logo' %}

{% assign icon_list__store = '' %}
{% assign icon_list__store_universal = '' %}


{% assign icon_list_array = icon_list | split: ", " %}

{% assign icon_list__ui_array = icon_list__ui | split: ", " %}
{% assign icon_list__picto_array = icon_list__picto | split: ", " %}
{% assign icon_list__social_array = icon_list__social | split: ", " %}
{% assign icon_list__share_social_array = icon_list__social_share | split: ", " %}
{% assign icon_list__pickup_array = icon_list__pickup | split: ", " %}
{% assign icon_list__media_array = icon_list__media | split: ", " %}
{% assign icon_list__other_array = icon_list__other | split: ", " %}

{% assign icon_list__store_universal_array = icon_list__store_universal | split: ", " %}
{% assign icon_list__store_array = icon_list__store | split: ", " %}

<a class="section-anchor" id="styleguide-icons" name="styleguide-icons"></a>

<section class="styleguide-section styleguide-icons" data-section-type="styleguide-icons" data-section-id="{{ section.id }}">

  <div class="container">

    <header class="page-header" role="banner">
      <h2>Icons</h2>
    </header>

    <div class="page-content page-content--large">

      {% comment %}

      <h3 class="docs-subtitle">Product Badges</h3>

      <div class="product-badges badges" style="position: static !important;">

        <div class="badge badge--sale">
          <span class="badge__label">{{- 'products.product.on_sale' | t -}}</span>
        </div>

        <div class="badge badge--new">
          <span class="badge__label">{{- 'products.product.new' | t -}}</span>
        </div>

        <div class="badge badge--low-stock">
          <span class="badge__label">{{- 'products.product.low_stock' | t -}}</span>
        </div>

        <div class="badge badge--sold-out">
          <span class="badge__label">{{- 'products.product.sold_out' | t -}}</span>
        </div>

        <div class="badge badge--support-a-cause">
          <span class="badge__label">{{- 'products.product.support_a_cause' | t -}}</span>
        </div>

        <div class="badge badge--limited-edition">
          <span class="badge__label">{{- 'products.product.limited_edition' | t -}}</span>
        </div>

        <div class="badge badge--best-seller">
          <span class="badge__label">{{- 'products.product.best_seller' | t -}}</span>
        </div>

        <div class="badge badge--staff-pick">
          <span class="badge__label">{{- 'products.product.staff_pick' | t -}}</span>
        </div>

        <div class="badge badge--seasonal">
          <span class="badge__label">{{- 'products.product.seasonal' | t -}}</span>
        </div>

      </div>

      {% endcomment %}

      <h3 class="docs-subtitle">UI</h3>

      <div class="styleguide-logos styleguide-icons--small">

        {%- for icon in icon_list__ui_array -%}
          <div title="{{ icon }}">{% include 'icon' with icon %}</div>
        {%- endfor -%}

      </div>

      <h3 class="docs-subtitle">Picto</h3>

      <div class="styleguide-logos styleguide-icons--small">

        {%- for icon in icon_list__picto_array -%}
          <div title="{{ icon }}">{% include 'icon' with icon %}</div>
        {%- endfor -%}

      </div>

      <h3 class="docs-subtitle">Social</h3>

      <div class="styleguide-logos styleguide-icons--small">

        {%- for icon in icon_list__social_array -%}
          <div title="{{ icon }}">{% include 'icon' with icon %}</div>
        {%- endfor -%}

      </div>

      <h3 class="docs-subtitle">Social - Sharing</h3>

      <div class="styleguide-logos styleguide-icons--small">

        {%- for icon in icon_list__share_social_array -%}
          <div title="{{ icon }}">{% include 'icon' with icon %}</div>
        {%- endfor -%}

      </div>

      <h3 class="docs-subtitle">Pickup in Store</h3>

      <div class="styleguide-logos styleguide-icons--small">

        {%- for icon in icon_list__pickup_array -%}
          <div title="{{ icon }}">{% include 'icon' with icon %}</div>
        {%- endfor -%}

      </div>

      <h3 class="docs-subtitle">Media</h3>

      <div class="styleguide-logos styleguide-icons--small">

        {%- for icon in icon_list__media_array -%}
          <div title="{{ icon }}">{% include 'icon' with icon %}</div>
        {%- endfor -%}

      </div>

      <h3 class="docs-subtitle">Other</h3>

      <div class="styleguide-logos styleguide-icons--small">

        {%- for icon in icon_list__other_array -%}
          <div title="{{ icon }}">{% include 'icon' with icon %}</div>
        {%- endfor -%}

      </div>

      <h2>Store Icons</h2>

      <h3 class="docs-subtitle">Universal</h3>

      <div class="styleguide-logos styleguide-icons--small">

        {%- for icon in icon_list__store_universal_array -%}
          <div title="{{ icon }}">{% include 'icon' with icon %}</div>
        {%- endfor -%}

      </div>

    </div>

  </div>

</section>

{% schema %}

  {
    "name": "Icons",
    "class": "styleguide-icons",
    "settings": []
  }

{% endschema %}
