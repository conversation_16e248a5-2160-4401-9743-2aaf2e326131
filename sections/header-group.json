/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "type": "header",
  "name": "Header group",
  "sections": {
    "announcement-bar": {
      "type": "tabbed-announcement-bar",
      "blocks": {
        "message_THaYyY": {
          "type": "message",
          "disabled": true,
          "settings": {
            "text": "<p>20% Off Exclusively In-App | <a href=\"https://kytebaby.onelink.me/ZEcT/kxvhgloj\" title=\"https://kytebaby.onelink.me/ZEcT/kxvhgloj\">Download the App</a></p>",
            "learn_more_text": "",
            "title": "",
            "content": "",
            "button_text": "",
            "button_link": "#",
            "rewards_points_enable": true
          }
        },
        "15314d8d-28f1-40b2-b1b7-2473d46461ba": {
          "type": "message",
          "settings": {
            "text": "<p>Free Shipping On US Orders $85+</p>",
            "learn_more_text": "",
            "title": "",
            "content": "",
            "button_text": "",
            "button_link": "shopify://pages/shipping-details",
            "rewards_points_enable": true
          }
        },
        "message_f9barg": {
          "type": "message",
          "settings": {
            "text": "<p>Join <a href=\"/pages/frequent-flyer-rewards\" title=\"Frequent Flyer Rewards\">rewards</a> to earn 10 points for every $1 spent</p>",
            "learn_more_text": "",
            "title": "",
            "content": "",
            "button_text": "",
            "button_link": "",
            "rewards_points_enable": false
          }
        },
        "message_G9WXHH": {
          "type": "message",
          "settings": {
            "text": "<p>Buttery bamboo with 500,000+ 5-star reviews</p>",
            "learn_more_text": "",
            "title": "",
            "content": "",
            "button_text": "",
            "button_link": "",
            "rewards_points_enable": true
          }
        },
        "message_GYMXFp": {
          "type": "message",
          "disabled": true,
          "settings": {
            "text": "<p>Sale orders may take up to <strong>4 weeks </strong>to be fulfilled. </p>",
            "learn_more_text": "Learn more",
            "title": "",
            "content": "",
            "button_text": "",
            "button_link": "",
            "rewards_points_enable": true
          }
        }
      },
      "block_order": [
        "message_THaYyY",
        "15314d8d-28f1-40b2-b1b7-2473d46461ba",
        "message_f9barg",
        "message_G9WXHH",
        "message_GYMXFp"
      ],
      "settings": {
        "announcement_bar_position": "sticky_mobile",
        "announcement_text_size": "small",
        "autoplay": true,
        "cycle_speed": 5,
        "background": "#e3ded2",
        "text_color": "#000000",
        "button_background": "#000000",
        "button_text_color": "#ffffff"
      }
    },
    "top-menu-bar": {
      "type": "tabbed-top-menu-bar",
      "settings": {
        "show_rewards_points": true,
        "show_icons": true,
        "top_menu_bar_menu": "2022-voltage-top-menu-bar",
        "top_menu_bar_position": "non_sticky",
        "background_2": "",
        "background": "rgba(0,0,0,0)",
        "link_color": "",
        "logo_color": "",
        "heading_color": "",
        "text_color": "rgba(0,0,0,0)",
        "icon_color": "",
        "border_color": ""
      }
    },
    "header": {
      "type": "tabbed-header",
      "blocks": {
        "d2136cfb-1190-4e20-a417-68eceddfc2ef": {
          "type": "mega_menu",
          "settings": {
            "header_title": "",
            "footer_button_text": "",
            "footer_button_link": "",
            "footer_button_size": "",
            "footer_button_style": "",
            "footer_button_icon": "",
            "colors_enable": true,
            "colors_link_group_handle": "",
            "menu_item": "Featured",
            "images_position": "right",
            "hide_markets_1": "",
            "image_1": "shopify://shop_images/Holiday-Shoppe-1x1.jpg",
            "image_1_heading": "",
            "image_1_text": "",
            "image_1_link": "shopify://pages/holiday-market",
            "hide_markets_2": "",
            "image_2": "shopify://shop_images/KS-Shop.png",
            "image_2_heading": "",
            "image_2_text": "",
            "image_2_link": "shopify://pages/kyte-skin",
            "hide_markets_3": "",
            "image_3_heading": "",
            "image_3_text": "",
            "image_3_link": "",
            "hide_markets_4": "",
            "image_4_heading": "",
            "image_4_text": "",
            "image_4_link": "",
            "hide_markets_5": "",
            "image_5_heading": "",
            "image_5_text": "",
            "image_5_link": "",
            "image_6_heading": "",
            "image_6_text": "",
            "image_6_link": "",
            "header_title_style": "heading h3",
            "image_heading_style": "",
            "image_text_style": "",
            "background": "",
            "heading_color": "",
            "subheading_color": "",
            "text_color": "",
            "button_background": "",
            "button_text_color": ""
          }
        },
        "d54221fa-e205-4cd2-ab3b-6d463448544f": {
          "type": "mega_menu",
          "settings": {
            "header_title": "",
            "footer_button_text": "",
            "footer_button_link": "",
            "footer_button_size": "",
            "footer_button_style": "",
            "footer_button_icon": "",
            "colors_enable": true,
            "colors_link_group_handle": "",
            "menu_item": "Sleep Bags",
            "images_position": "right",
            "hide_markets_1": "",
            "image_1": "shopify://shop_images/2_eca2cfde-c775-4ac3-b503-feaa70a6801b.png",
            "image_1_heading": "Sleep Bag Swaddlers",
            "image_1_text": "",
            "image_1_link": "shopify://collections/sleep-bag-swaddler",
            "hide_markets_2": "",
            "image_2": "shopify://shop_images/1_d589b1af-4cb0-40c8-a315-cf82a4a4ac61.png",
            "image_2_heading": "Swaddle Bags",
            "image_2_text": "",
            "image_2_link": "shopify://collections/swaddle-bag",
            "hide_markets_3": "CA",
            "image_3": "shopify://shop_images/Untitled_design_-_2025-06-27T154551.370.png",
            "image_3_heading": "Sleep Bags",
            "image_3_text": "",
            "image_3_link": "shopify://collections/sleep-bags",
            "hide_markets_4": "",
            "image_4": "shopify://shop_images/2_30b898df-e784-4726-8386-bab05540366b.png",
            "image_4_heading": "Sleep Bag Walkers",
            "image_4_text": "",
            "image_4_link": "shopify://collections/sleep-bag-walkers",
            "hide_markets_5": "",
            "image_5": "shopify://shop_images/Untitled_design_-_2025-09-22T203627.404.png",
            "image_5_heading": "Slumber Bags",
            "image_5_text": "",
            "image_5_link": "shopify://collections/slumber-bag",
            "image_6_heading": "",
            "image_6_text": "",
            "image_6_link": "",
            "header_title_style": "heading h3",
            "image_heading_style": "",
            "image_text_style": "",
            "background": "",
            "heading_color": "",
            "subheading_color": "",
            "text_color": "",
            "button_background": "",
            "button_text_color": ""
          }
        },
        "f17ed72e-9f7b-4cd9-b198-4e33e504b8ca": {
          "type": "mega_menu",
          "settings": {
            "header_title": "",
            "footer_button_text": "",
            "footer_button_link": "",
            "footer_button_size": "",
            "footer_button_style": "",
            "footer_button_icon": "",
            "colors_enable": true,
            "colors_link_group_handle": "",
            "menu_item": "Baby",
            "images_position": "right",
            "hide_markets_1": "",
            "image_1": "shopify://shop_images/IMG_4483_2.png",
            "image_1_heading": "",
            "image_1_text": "",
            "image_1_link": "shopify://collections/baby",
            "hide_markets_2": "",
            "image_2_heading": "",
            "image_2_text": "",
            "image_2_link": "",
            "hide_markets_3": "",
            "image_3_heading": "",
            "image_3_text": "",
            "image_3_link": "",
            "hide_markets_4": "",
            "image_4_heading": "",
            "image_4_text": "",
            "image_4_link": "",
            "hide_markets_5": "",
            "image_5_heading": "",
            "image_5_text": "",
            "image_5_link": "",
            "image_6_heading": "",
            "image_6_text": "",
            "image_6_link": "",
            "header_title_style": "heading h3",
            "image_heading_style": "",
            "image_text_style": "",
            "background": "",
            "heading_color": "",
            "subheading_color": "",
            "text_color": "",
            "button_background": "",
            "button_text_color": ""
          }
        },
        "d4f1d72d-81cd-493f-a1e7-57e0a1de5326": {
          "type": "mega_menu",
          "settings": {
            "header_title": "",
            "footer_button_text": "",
            "footer_button_link": "",
            "footer_button_size": "",
            "footer_button_style": "",
            "footer_button_icon": "",
            "colors_enable": true,
            "colors_link_group_handle": "",
            "menu_item": "Toddler",
            "images_position": "right",
            "hide_markets_1": "",
            "image_1": "shopify://shop_images/IMG_4511.png",
            "image_1_heading": "",
            "image_1_text": "",
            "image_1_link": "shopify://collections/toddler",
            "hide_markets_2": "",
            "image_2_heading": "",
            "image_2_text": "",
            "image_2_link": "",
            "hide_markets_3": "",
            "image_3_heading": "",
            "image_3_text": "",
            "image_3_link": "",
            "hide_markets_4": "",
            "image_4_heading": "",
            "image_4_text": "",
            "image_4_link": "",
            "hide_markets_5": "",
            "image_5_heading": "",
            "image_5_text": "",
            "image_5_link": "",
            "image_6_heading": "",
            "image_6_text": "",
            "image_6_link": "",
            "header_title_style": "heading h3",
            "image_heading_style": "",
            "image_text_style": "",
            "background": "",
            "heading_color": "",
            "subheading_color": "",
            "text_color": "",
            "button_background": "",
            "button_text_color": ""
          }
        },
        "c4de0306-8f63-4e86-84a8-dfbbc44b87d0": {
          "type": "mega_menu",
          "settings": {
            "header_title": "",
            "footer_button_text": "",
            "footer_button_link": "",
            "footer_button_size": "",
            "footer_button_style": "",
            "footer_button_icon": "",
            "colors_enable": true,
            "colors_link_group_handle": "",
            "menu_item": "Adult",
            "images_position": "right",
            "hide_markets_1": "",
            "image_1": "shopify://shop_images/KB-Shop-All-Adult_494f58f0-995d-4c07-a85e-903e135bdb4f.jpg",
            "image_1_heading": "",
            "image_1_text": "",
            "image_1_link": "shopify://collections/adult",
            "hide_markets_2": "",
            "image_2": "shopify://shop_images/KB-Shop-Intimates-1.jpg",
            "image_2_heading": "",
            "image_2_text": "",
            "image_2_link": "shopify://collections/kyte-intimates",
            "hide_markets_3": "",
            "image_3_heading": "",
            "image_3_text": "",
            "image_3_link": "",
            "hide_markets_4": "",
            "image_4_heading": "",
            "image_4_text": "",
            "image_4_link": "",
            "hide_markets_5": "",
            "image_5_heading": "",
            "image_5_text": "",
            "image_5_link": "",
            "image_6_heading": "",
            "image_6_text": "",
            "image_6_link": "",
            "header_title_style": "heading h3",
            "image_heading_style": "",
            "image_text_style": "",
            "background": "",
            "heading_color": "",
            "subheading_color": "",
            "text_color": "",
            "button_background": "",
            "button_text_color": ""
          }
        },
        "21593564-6126-430b-b897-83b0134a0d8a": {
          "type": "mega_menu",
          "settings": {
            "header_title": "",
            "footer_button_text": "",
            "footer_button_link": "",
            "footer_button_size": "",
            "footer_button_style": "",
            "footer_button_icon": "",
            "colors_enable": true,
            "colors_link_group_handle": "",
            "menu_item": "Nursery & Home",
            "images_position": "right",
            "hide_markets_1": "",
            "image_1": "shopify://shop_images/IMG_2021_9f35283f-972c-475c-9d31-c13d25f73e94.jpg",
            "image_1_heading": "",
            "image_1_text": "",
            "image_1_link": "shopify://collections/nursery-home",
            "hide_markets_2": "",
            "image_2_heading": "",
            "image_2_text": "",
            "image_2_link": "",
            "hide_markets_3": "",
            "image_3_heading": "",
            "image_3_text": "",
            "image_3_link": "",
            "hide_markets_4": "",
            "image_4_heading": "",
            "image_4_text": "",
            "image_4_link": "",
            "hide_markets_5": "",
            "image_5_heading": "",
            "image_5_text": "",
            "image_5_link": "",
            "image_6_heading": "",
            "image_6_text": "",
            "image_6_link": "",
            "header_title_style": "heading h3",
            "image_heading_style": "",
            "image_text_style": "",
            "background": "",
            "heading_color": "",
            "subheading_color": "",
            "text_color": "",
            "button_background": "",
            "button_text_color": ""
          }
        },
        "mega_menu_AKMdkq": {
          "type": "mega_menu",
          "settings": {
            "header_title": "",
            "footer_button_text": "",
            "footer_button_link": "",
            "footer_button_size": "",
            "footer_button_style": "",
            "footer_button_icon": "",
            "colors_enable": true,
            "colors_link_group_handle": "",
            "menu_item": "Family Matching",
            "images_position": "right",
            "hide_markets_1": "",
            "image_1_heading": "Holiday Matching",
            "image_1_text": "",
            "image_1_link": "shopify://collections/holiday-family-pajamas",
            "hide_markets_2": "",
            "image_2": "shopify://shop_images/Atlantic_Family_Matching-010_1.jpg",
            "image_2_heading": "Family Pajamas",
            "image_2_text": "",
            "image_2_link": "shopify://collections/family-pajamas",
            "hide_markets_3": "",
            "image_3": "shopify://shop_images/Mommy_Mematching_1_d44912ce-1114-445c-9b4b-eb11e6d18271.jpg",
            "image_3_heading": "Welcome Home Sets",
            "image_3_text": "",
            "image_3_link": "shopify://collections/mommy-and-me-hospital-sets",
            "hide_markets_4": "",
            "image_4": "shopify://shop_images/Silly_Goose_Family-08.jpg",
            "image_4_heading": "Mommy & Me",
            "image_4_text": "",
            "image_4_link": "shopify://collections/mommy-and-me",
            "hide_markets_5": "",
            "image_5": "shopify://shop_images/SLCDFamilyMatchingDaywear-01.jpg",
            "image_5_heading": "Daddy & Me",
            "image_5_text": "",
            "image_5_link": "shopify://collections/daddy-and-me",
            "image_6": "shopify://shop_images/Fir_Gingham_Dog_Bandana_and_Long_Sleeve_Twirl_Dress-01.jpg",
            "image_6_heading": "Fur Family",
            "image_6_text": "",
            "image_6_link": "shopify://collections/fur-family",
            "header_title_style": "heading h3",
            "image_heading_style": "",
            "image_text_style": "",
            "background": "",
            "heading_color": "",
            "subheading_color": "",
            "text_color": "",
            "button_background": "",
            "button_text_color": ""
          }
        },
        "mega_menu_iF9BpY": {
          "type": "mega_menu",
          "settings": {
            "header_title": "",
            "footer_button_text": "",
            "footer_button_link": "",
            "footer_button_size": "",
            "footer_button_style": "",
            "footer_button_icon": "",
            "colors_enable": true,
            "colors_link_group_handle": "",
            "menu_item": "Collaborations",
            "images_position": "right",
            "hide_markets_1": "",
            "image_1": "shopify://shop_images/HWxKB_Shop-Graphic_1.jpg",
            "image_1_heading": "",
            "image_1_text": "",
            "image_1_link": "shopify://collections/hot-wheels™-x-kyte-baby",
            "hide_markets_2": "",
            "image_2": "shopify://shop_images/GGxKB_Shop-Graphic_1_1.jpg",
            "image_2_heading": "",
            "image_2_text": "",
            "image_2_link": "shopify://collections/gilmore-girls-x-kyte-baby",
            "hide_markets_3": "",
            "image_3": "shopify://shop_images/ECxKB_Shop-Graphic_1x1_1.jpg",
            "image_3_heading": "",
            "image_3_text": "",
            "image_3_link": "shopify://collections/kyte-baby-x-the-world-of-eric-carle™",
            "hide_markets_4": "",
            "image_4": "shopify://shop_images/SupermanxKB_Shop-Graphic_1.jpg",
            "image_4_heading": "",
            "image_4_text": "",
            "image_4_link": "shopify://collections/superman™-x-kyte-baby",
            "hide_markets_5": "",
            "image_5_heading": "",
            "image_5_text": "",
            "image_5_link": "",
            "image_6_heading": "",
            "image_6_text": "",
            "image_6_link": "",
            "header_title_style": "heading h3",
            "image_heading_style": "",
            "image_text_style": "",
            "background": "",
            "heading_color": "",
            "subheading_color": "",
            "text_color": "",
            "button_background": "",
            "button_text_color": ""
          }
        }
      },
      "block_order": [
        "d2136cfb-1190-4e20-a417-68eceddfc2ef",
        "d54221fa-e205-4cd2-ab3b-6d463448544f",
        "f17ed72e-9f7b-4cd9-b198-4e33e504b8ca",
        "d4f1d72d-81cd-493f-a1e7-57e0a1de5326",
        "c4de0306-8f63-4e86-84a8-dfbbc44b87d0",
        "21593564-6126-430b-b897-83b0134a0d8a",
        "mega_menu_AKMdkq",
        "mega_menu_iF9BpY"
      ],
      "settings": {
        "enable_sticky_header": true,
        "logo_link": "/",
        "logo": "shopify://shop_images/logo-black-horizontal_da29d415-a8b2-480b-8164-0fae0c870880.png",
        "logo_max_width": 200,
        "mobile_logo_max_width": 145,
        "navigation_menu": "2022-voltage-main-menu",
        "sidebar_navigation_menu": "",
        "secondary_menu": "2022-voltage-top-menu-bar",
        "show_locale_selector": false,
        "show_country_selector": false,
        "search_menu": "2022-voltage-search-menu",
        "floating_badge_enable": true,
        "floating_badge_image": "shopify://shop_images/Holiday-Shoppe-Badge.png",
        "floating_badge_link": "shopify://pages/holiday-market",
        "floating_badge_label_text": "",
        "floating_badge_border_width": 1,
        "floating_badge_color_background": "#ffffff",
        "floating_badge_color_border": "linear-gradient(54deg, rgba(227, 222, 210, 1) 4%, rgba(227, 222, 210, 1) 100%)",
        "floating_badge_color_label_background": "#e3ded2",
        "floating_badge_color_label_text": "#ffffff",
        "background": "",
        "background_2": "",
        "link_color": "",
        "logo_color": "",
        "heading_color": "",
        "text_color": "",
        "icon_color": "",
        "button_color": "",
        "border_color": "",
        "bubble_color": ""
      }
    }
  },
  "order": [
    "announcement-bar",
    "top-menu-bar",
    "header"
  ]
}
