
{%- comment -%}
  {
    "Name": "Rebuy for Wonderment",
    "Version": "1.02 | Updated March 4th 2023"
  }
{%- endcomment -%}

<script type="text/javascript">
  document.addEventListener('wonderment:shipments_loaded', function(event) {
  
    document.addEventListener('rebuy.ready', function(event){
      if(event.detail.widget.id == "{{section.settings.rebuy-widget-id}}") {
        const widget = event.detail.widget;
        const orderId = window.wonderment.shipments[0].order.id;
        if (orderId) {
          widget.data.shopify_order_ids = [orderId];
          widget.getWidgetProducts()
        }
      }
    });
    
    let rebuyContainer = document.createElement('div');
    rebuyContainer.dataset.rebuyId = "{{section.settings.rebuy-widget-id}}";
    rebuyContainer.dataset.rebuyShopifyOrderIds = event.shipments[0].order.id;
  document.getElementById('wonderment-rebuy').appendChild(rebuyContainer);
        
    Rebuy.init();
  });
</script>
 
<div id="wonderment-rebuy"></div>


{% schema %}
  {
    "name": "📦 Rebuy",
	"tag": "section",
    "settings": [
    {
      "type": "header",
      "content": "Rebuy | v1.02"
    },
    {
      "type": "paragraph",
      "content": "AI-powered upsell personalization for Shopify and Shopify Plus."
    },
    {
      "type": "paragraph",
      "content": "The world’s top brands use Rebuy’s personalization platform to fuel data-driven shopping experiences, win more customers, keep more customers and accelerate growth."
    },
    {
      "type": "paragraph",
      "content": "[Learn more about Rebuy](https://www.rebuyengine.com/) or view our [documentation](https://docs.wonderment.com/article/76awjdvzkd-rebuy-section-setup)."
    },
    {
      "type": "paragraph",
      "content": "Note: Rebuy only loads when the tracking block is displaying shipment info or a preview."
    },
    {
      "type": "text",
      "id": "rebuy-widget-id",
      "label": "Rebuy Widget ID",
      "info": "Add your widget ID from your Wonderment widget, found in your [Rebuy account](https://rebuyengine.com/onlinestore/third_party)."
	}
  ],
    
	"presets": [
    {
      "name": "📦 Rebuy"
    }
  ]
  }
{% endschema %}


{% stylesheet %}
{% endstylesheet %}

{% javascript %}
{% endjavascript %}
