<style>
  #shopify-section-{{ section.id }} .footer {
    {%- assign border_color = settings.footer_background | color_mix: settings.footer_text_color, 85 -%}

    --background: {{ settings.footer_background.red }}, {{ settings.footer_background.green }}, {{ settings.footer_background.blue }};
    --heading-color: {{ settings.footer_text_color.red }}, {{ settings.footer_text_color.green }}, {{ settings.footer_text_color.blue }};
    --text-color: {{ settings.footer_text_color.red }}, {{ settings.footer_text_color.green }}, {{ settings.footer_text_color.blue }};
    --border-color: {{ border_color.red }}, {{ border_color.green }}, {{ border_color.blue }};
  }
</style>

<footer class="footer footer--custom {% if settings.footer_background == settings.background %}footer--bordered{% endif %}">
  <div class="container">
    <div class="footer__inner">
      <div class="footer__item-list">
        {%- for block in section.blocks -%}
          {%- capture block_content -%}
            {%- case block.type -%}
              {%- when 'image' -%}
                {%- if block.settings.image != blank -%}
                  {%- capture sizes_attribute -%}{{ block.settings.image_width }}px{%- endcapture -%}
                  {%- capture style_attribute -%}width: {{ block.settings.image_width }}px{%- endcapture -%}
                  {{- block.settings.image | image_url: width: block.settings.image.width | image_tag: loading: 'lazy', style: style_attribute, sizes: sizes_attribute, widths: '100,200,300,400,500,600', class: 'footer__image' -}}
                {%- endif -%}

              {%- when 'text' -%}

                {% if section.settings.show_block_headings == true %}
                  {%- if block.settings.title != blank -%}
                    <p class="footer__item-title heading heading--small">{{ block.settings.title | escape }}</p>
                  {%- endif -%}
                {% endif %}

                {%- if block.settings.content != blank -%}
                  <div class="footer__item-content">
                    {{- block.settings.content -}}
                  </div>
                {%- endif -%}

              {%- when 'links' -%}
                {%- assign menu = block.settings.menu -%}

                {%- if menu != blank -%}
                  {% if section.settings.show_block_headings == true %}
                    <p class="footer__item-title heading heading--small">{{ menu.title }}</p>
                  {% endif %}

                  <div class="footer__item-content">
                    <ul class="linklist list--unstyled" role="list">
                      {%- for link in menu.links -%}
                        <li class="linklist__item">
                          <a href="{{ link.url }}" {% render 'link-attributes', link: link %} class="link--faded">{{ link.title }}</a>
                        </li>
                      {%- endfor -%}
                    </ul>
                  </div>
                {%- endif -%}

              {%- when 'social_media' -%}
              
                {% if section.settings.show_block_headings == true %}
                  {%- if block.settings.title != blank -%}
                      <p class="footer__item-title heading heading--small">{{ block.settings.title | escape }}</p>
                    {%- endif -%}
                {% endif %}

                <div class="footer__item-content">
                  {%- if block.settings.content != blank -%}
                    {{- block.settings.content -}}
                  {%- endif -%}

                  {%- render 'social-media' -%}
                </div>

              {%- when 'newsletter' -%}

                {% if section.settings.show_block_headings == true %}
                  {%- if block.settings.title != blank -%}
                    <p class="footer__item-title heading heading--small">{{ block.settings.title | escape }}</p>
                  {%- endif -%}
                {% endif %}

                <div class="footer__item-content">
                  {%- if block.settings.content != blank -%}
                    {{- block.settings.content -}}
                  {%- endif -%}

                  {%- form 'customer', id: 'footer-newsletter', class: 'footer__newsletter-form form' -%}
                    {%- if form.posted_successfully? -%}
                      <div class="form__banner banner banner--success">
                        <span class="banner__ribbon">{% render 'icon' with 'form-success' %}</span>
                        <p class="banner__content">{{ 'footer.newsletter.success' | t }}</p>
                      </div>
                    {%- else -%}
                      {%- if form.errors -%}
                        <div class="form__banner banner banner--error">
                          <span class="banner__ribbon">{% render 'icon' with 'form-error' %}</span>
                          <p class="banner__content">{{ form.errors.translated_fields['email'] }} {{ form.errors.messages['email'] }}</p>
                        </div>
                      {%- endif -%}

                      <input type="hidden" name="contact[tags]" value="newsletter">

                      <div class="input">
                        <input type="email" id="footer[contact][email]" name="contact[email]" class="input__field input__field--text">
                        <label for="footer[contact][email]" class="input__label">{{ 'footer.newsletter.email' | t }}</label>
                        <button type="submit" class="input__submit-icon tap-area" title="{{ 'footer.newsletter.submit' | t }}">{% render 'icon' with 'nav-arrow-right', direction_aware: true %}</button>
                      </div>
                    {%- endif -%}
                  {%- endform -%}
                </div>
              
              {%- when 'button_with_text' -%}

                {%- if block.settings.title != blank -%}
                  <p class="footer__item-title heading heading--small">{{ block.settings.title | escape }}</p>
                {%- endif -%}

                <div class="footer__item-content">
                  {%- if block.settings.content != blank -%}
                    {{- block.settings.content -}}
                  {%- endif -%}

                  {% if block.settings.button_text != blank %}
                    <a class="button button--secondary button--small" target="_blank" href="{{ block.settings.button_link }}">{{ block.settings.button_text }}</a>
                  {% endif %}

                </div>
            {%- endcase -%}
          {%- endcapture -%}

          {%- if block_content != blank -%}
            <div class="footer__item footer__item--{{ block.type | replace: '_', '-' }} {% if forloop.first %}is-first{% endif %}" {{ block.shopify_attributes }}>
              {{- block_content -}}
            </div>
          {%- endif -%}
        {%- endfor -%}
      </div>

      <div class="footer__aside">
        {%- if section.settings.show_country_selector and localization.available_countries.size > 1 -%}
          {%- assign country_selector = true -%}
        {%- endif -%}

        {%- if section.settings.show_locale_selector and localization.available_languages.size > 1 -%}
          {%- assign locale_selector = true -%}
        {%- endif -%}

        {%- if country_selector or locale_selector -%}
          {%- form 'localization', id: 'localization_form_footer', class: 'footer__cross-border' -%}
            {%- if country_selector -%}
              <div class="popover-container">
                <input type="hidden" name="country_code" value="{{ localization.country.iso_code }}">
                <span class="visually-hidden">{{ 'footer.general.country' | t }}</span>

                <button type="button" is="toggle-button" class="select select--small {% if locale_selector and settings.button_border_radius > 0 %}select--collapse-end{% endif %} text--xsmall" aria-expanded="false" aria-controls="footer-currency-selector">
                  {{- localization.country.name }} ({{ localization.country.currency.iso_code }} {% if localization.country.currency.symbol %}{{ localization.country.currency.symbol }}{%- endif -%})
                  {%- render 'icon' with 'chevron', width: 9, height: 6, inline: true -%}
                </button>

                <popover-content id="footer-currency-selector" class="popover popover--top popover--left popover--small">
                  <span class="popover__overlay"></span>

                  <header class="popover__header">
                    <span class="popover__title heading h6">{{- 'footer.general.country' | t -}}</span>

                    <button type="button" class="popover__close-button tap-area tap-area--large" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
                      {%- render 'icon' with 'close' -%}
                    </button>
                  </header>

                  <div class="popover__content popover__content--restrict">
                    <div class="popover__choice-list">
                      {%- for country in localization.available_countries -%}
                        <button type="submit" name="country_code" value="{{ country.iso_code }}" class="popover__choice-item">
                          <span class="popover__choice-label" {% if country.iso_code == localization.country.iso_code %}aria-current="true"{% endif %}>
                            {{- country.name }} ({{ country.currency.iso_code }} {% if country.currency.symbol %}{{ country.currency.symbol }}{%- endif -%})
                          </span>
                        </button>
                      {%- endfor -%}
                    </div>
                  </div>
                </popover-content>
              </div>
            {%- endif -%}

            {%- if locale_selector -%}
              <div class="popover-container">
                <input type="hidden" name="locale_code" value="{{ localization.language.iso_code }}">
                <span class="visually-hidden">{{ 'footer.general.language' | t }}</span>

                <button type="button" is="toggle-button" class="select select--small {% if country_selector and settings.button_border_radius > 0 %}select--collapse-start{% endif %} text--xsmall" aria-expanded="false" aria-controls="footer-locale-selector">
                  {{- localization.language.endonym_name | capitalize -}}
                  {%- render 'icon' with 'chevron', width: 9, height: 6, inline: true -%}
                </button>

                <popover-content id="footer-locale-selector" class="popover popover--top popover--small">
                  <span class="popover__overlay"></span>

                  <header class="popover__header">
                    <span class="popover__title heading h6">{{- 'footer.general.language' | t -}}</span>

                    <button type="button" class="popover__close-button tap-area tap-area--large" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
                      {%- render 'icon' with 'close' -%}
                    </button>
                  </header>

                  <div class="popover__content">
                    <div class="popover__choice-list">
                      {%- for language in localization.available_languages -%}
                        <button type="submit" name="locale_code" value="{{ language.iso_code }}" class="popover__choice-item">
                          <span class="popover__choice-label" {% if language.iso_code == localization.language.iso_code %}aria-current="true"{% endif %}>
                            {{- language.endonym_name | capitalize -}}
                          </span>
                        </button>
                      {%- endfor -%}
                    </div>
                  </div>
                </popover-content>
              </div>
            {%- endif -%}
          {%- endform -%}
        {%- endif -%}

        <span class="footer__copyright text--xxsmall text--subdued">

          &copy; {{ shop.name }} {{ "now" | date: "%Y" }}. All rights reserved.
          
          <span class="separator">|</span> 
          
          {% assign copyright_menu = section.settings.copyright_menu %}

          {% if copyright_menu != blank %}
            
            <span>
              {% assign size_less_one = copyright_menu.links.size | minus: 1 %}
              {% for link in copyright_menu.links %}
                {% if forloop.last %}&amp;{% endif %}
                <a href="{{ link.url }}" class="link link--subdued">{{ link.title }}{% unless forloop.index >= size_less_one %},{% endunless %}</a>
              {% endfor %}
            </span>
            
          {% endif %}
          
        </span>

        {%- if section.settings.show_footer_social == true -%}
          <div class="footer__social">
            {%- render 'footer-social-media' -%}
            {%- if shop.features.follow_on_shop? and section.settings.enable_follow_on_shop -%}
              {{- shop | login_button: action: 'follow' -}}
            {%- endif -%}
          </div>
        {%- endif -%}

        <div class="footer__voltage-credit">
          <span class="text--xxsmall">On Shopify by <a href="https://www.voltagenewmedia.com/" class="link link--subdued" target="_blank">Voltage</a></span>
        </div>

        {%- if section.settings.show_payment_icons and shop.enabled_payment_types.size > 0 -%}
          <div class="footer__payment-methods">
            <span class="footer__payment-methods-label text--xsmall text--subdued">{{ 'footer.general.we_accept' | t }}</span>

            <div class="payment-methods-list payment-methods-list--auto">
              {% for type in shop.enabled_payment_types %}
                {{ type | payment_type_svg_tag }}
              {% endfor %}
            </div>
          </div>
        {%- endif -%}

      </div>
    </div>
  </div>
</footer>

{% schema %}
{
  "name": "Footer",
  "class": "shopify-section--footer",
  "max_blocks": 5,
  "settings": [
    {
      "type": "checkbox",
      "id": "show_block_headings",
      "label": "Show Block Headings",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_payment_icons",
      "label": "Show payment icons",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_footer_social",
      "label": "Show Footer Social Icons",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "enable_follow_on_shop",
      "label": "Show Follow on Shop",
      "info": "To allow customers to follow your store on the Shop app from your storefront, Shop Pay must be enabled. [Learn more](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)",
      "default": true
    },
    {
      "type": "link_list",
      "id": "copyright_menu",
      "label": "Copyright Menu"
    },
    {
      "type": "header",
      "content": "Country/region selector",
      "info": "To add a country/region, go to your [currency settings.](/admin/settings/payments)"
    },
    {
      "type": "checkbox",
      "id": "show_country_selector",
      "label": "Show country/region selector",
      "default": true
    },
    {
      "type": "header",
      "content": "Language selector",
      "info": "To add a language, go to your [language settings.](/admin/settings/languages)"
    },
    {
      "type": "checkbox",
      "id": "show_locale_selector",
      "label": "Show language selector",
      "default": true
    }
  ],
  "blocks": [
    {
      "type": "image",
      "name": "Image",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "300 x 300px .png recommended"
        },
        {
          "type": "range",
          "id": "image_width",
          "min": 50,
          "max": 300,
          "step": 10,
          "unit": "px",
          "label": "Image width",
          "default": 150
        }
      ]
    },
    {
      "type": "text",
      "name": "Text",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "About our store"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Content",
          "default": "<p>Use this text area to tell your customers about your brand and vision. You can change it in the theme editor.</p>"
        }
      ]
    },
    {
      "type": "links",
      "name": "Links",
      "settings": [
        {
          "type": "link_list",
          "id": "menu",
          "label": "Menu",
          "info": "This menu won't show dropdown items.",
          "default": "footer"
        }
      ]
    },
    {
      "type": "social_media",
      "name": "Social media",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "To configure social media, go to your social media settings."
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Follow us"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Content",
          "default": "<p>To configure social media, go to your social media settings. Change this text in the theme editor.</p>"
        }
      ]
    },
    {
      "type": "newsletter",
      "name": "Newsletter",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "Customers who subscribe will have their email address added to the \"accepts marketing\" [customer list](/admin/customers?query=&accepts_marketing=1)."
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Newsletter"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Content",
          "default": "<p>A short sentence describing what someone will receive by subscribing</p>"
        }
      ]
    },
    {
      "type": "button_with_text",
      "name": "Button with Text",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Newsletter"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Content",
          "default": "<p>A short sentence describing what someone will receive by subscribing</p>"
        },
        {
          "type": "text",
          "id": "button_text",
          "label": "Button Text"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "Button Link"
        }
      ]
    }
  ],
  "default": {
    "blocks": [
      {
        "type": "text",
        "settings": {}
      },
      {
        "type": "links",
        "settings": {}
      },
      {
        "type": "newsletter",
        "settings": {}
      },
      {
        "type": "social_media",
        "settings": {}
      }
    ]
  }
}
{% endschema %}