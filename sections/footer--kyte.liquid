<style>
  #shopify-section-{{ section.id }} .footer {
    
    {%- assign border_color = settings.footer_background | color_mix: settings.footer_text_color, 85 %}
    
    --border-color: {{ border_color.red }}, {{ border_color.green }}, {{ border_color.blue }};

    {%- assign button_background = section.settings.button_background -%}
    {%- assign button_text = section.settings.button_text -%}
    {%- assign footer_background = section.settings.background | default: settings.footer_background -%}
    {%- assign block_background = section.settings.block_background | default: settings.background -%}
    {%- assign input_background = section.settings.input_background | default: settings.background -%}
    {%- assign heading_color = section.settings.heading_color | default: settings.footer_text_color -%}
    {%- assign text_color = section.settings.text_color | default: settings.footer_text_color -%}

    {%- if section.settings.subheading_color != blank and section.settings.subheading_color != 'rgba(0,0,0,0)' -%}
      {%- assign subheading_color = section.settings.subheading_color -%}
    {%- else -%}
      {%- assign subheading_color = settings.subheading_color -%}
    {%- endif -%}

    {%- if button_background != blank and button_background != 'rgba(0,0,0,0);' -%}
      --button-background: {{ button_background.red }}, {{ button_background.green }}, {{ button_background.blue }};
    {%- endif -%}

    {%- if button_text != blank and button_text != 'rgba(0,0,0,0);' -%}
      --button-text-color: {{ button_text.red }}, {{ button_text.green }}, {{ button_text.blue }};
    {%- endif -%}

    {%- if footer_background != blank and footer_background != 'rgba(0,0,0,0);' -%}
      --background: {{ footer_background.red }}, {{ footer_background.green }}, {{ footer_background.blue }};
    {%- endif -%}

    {%- if block_background != blank and block_background != 'rgba(0,0,0,0);' -%}
      --block-background: {{ block_background.red }}, {{ block_background.green }}, {{ block_background.blue }};
    {%- endif -%}

    {%- if input_background != blank and input_background != 'rgba(0,0,0,0);' -%}
      --input-background: {{ input_background.red }}, {{ input_background.green }}, {{ input_background.blue }};
    {%- endif -%}

    {%- if heading_color != blank and heading_color != 'rgba(0,0,0,0);' -%}
      --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    {%- endif -%}

    {%- if text_color != blank and text_color != 'rgba(0,0,0,0);' -%}
      --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
    {%- endif -%}

    {%- if text_color != blank and text_color != 'rgba(0,0,0,0);' -%}
      --subheading-color: {{ subheading_color.red }}, {{ subheading_color.green }}, {{ subheading_color.blue }};
    {%- endif -%}
    
  }
</style>

<footer class="footer footer--kyte 
  {% if settings.footer_background == settings.background %}footer--bordered{% endif %}
  {% comment %} {% if section.settings.no_padding == true %}no-padding{% endif %} {% endcomment %}
  ">
  
  <div class="container">
    
    <div class="footer__inner">

      <div class="footer__top">

        {% comment %} Logo {% endcomment %}

        <div class="footer__logo-container">

          <div class="footer__logo">

            {% if section.settings.logo_link %}
              {%- assign logo_link = section.settings.logo_link -%}
            {% else %}
              {%- assign logo_link = routes.root_url -%}
            {% endif %}

            <a href="{{ logo_link }}">
              {{- section.settings.logo | image_url: width: '400', height: '400' | image_tag: preload: false, alt: shop.name -}}
            </a>

          </div>

          {%- if section.settings.show_footer_social == true -%}
            <div class="footer__social">
              {%- render 'footer-social-media' -%}
            </div>
          {%- endif -%}

        </div>

        {% comment %} Signup Blocks {% endcomment %}

        {%- capture footer_signups -%}

          {% for block in section.blocks %}

            {%- if block.type == "signup_block" -%}

              {%- capture accordion_id -%}
                {{- section.id }}--signup-block--{{ forloop.index -}}
              {%- endcapture  -%}

              <div class="footer-signup-block" {{ block.shopify_attributes }}>

                <div class="footer-signup-block__header">

                  <span class="footer-signup-block__title heading heading--small">
                    {{- block.settings.title -}}
                  </span>

                  <button class="footer-signup-block__expander" type="button" is="toggle-button" aria-controls="{{ accordion_id }}" aria-expanded="false">
                    {%- render 'icon' with 'chevron', inline: true -%}
                    <span class="visually-hidden">Expand</span>
                  </button>

                </div>

                <collapsible-content class="collapsible footer-signup-block__collapsible" id="{{ accordion_id }}">

                  <div class="footer-signup-block__body">

                    {%- if block.settings.content != blank -%}
                      <div class="footer-signup-block__content text--xsmall">
                        {{- block.settings.content -}}
                      </div>
                    {%- endif -%}

                    {%- capture signup_block_ctas -%}

                      {%- if block.settings.newsletter_form -%}

                        {%- assign newsletter_id = 'newsletter-' | append: section.id -%}

                        {%- form 'customer', id: newsletter_id, class: 'form newsletter__form' -%}

                          <input type="hidden" name="contact[tags]" value="newsletter">
                          <input type="hidden" name="contact[context]" value="{{ newsletter_id }}">

                          <div class="input-row--compact">

                            <div class="input">
                              <input type="email" id="newsletter[{{ section.id }}][contact][email]" name="contact[email]" class="input__field input__field--xtiny" placeholder="{{ 'general.newsletter.email' | t }}" required>
                              <label for="newsletter[{{ section.id }}][contact][email]" class="input__label visually-hidden">{{ 'general.newsletter.email' | t }}</label>
                            </div>

                            <div class="input">
                              <button type="submit" is="loader-button" class="button {{ section.settings.button_style }} button--xtiny">{{ 'general.newsletter.subscribe' | t }}</button>
                            </div>
                          </div>

                          {%- if form.posted_successfully? -%}

                            <div class="form__banner banner banner--small banner--success">
                              <span class="banner__ribbon">{% render 'icon' with 'form-success' %}</span>
                              <p class="banner__content">{{ 'general.newsletter.success' | t }}</p>
                            </div>

                            <script>
                              window.addEventListener('DOMContentLoaded', () => {
                                if (history.scrollRestoration) {
                                  history.scrollRestoration = 'manual'; // Prevent the browser to scroll on captcha page
                                }
                                document.getElementById('shopify-section-{{ section.id }}').scrollIntoView();
                              });
                            </script>

                          {%- else -%}

                            {%- if form.errors -%}
                              
                              <div class="form__banner banner banner--small banner--error">
                                <span class="banner__ribbon">{% render 'icon' with 'form-error' %}</span>
                                <p class="banner__content">{{ form.errors.messages['email'] }}</p>
                              </div>

                              <script>
                                window.addEventListener('DOMContentLoaded', () => {
                                  if (history.scrollRestoration) {
                                    history.scrollRestoration = 'manual'; // Prevent the browser to scroll on captcha page
                                  }
                                  document.getElementById('shopify-section-{{ section.id }}').scrollIntoView();
                                });
                              </script>

                            {%- endif -%}

                          {%- endif -%}

                          <div class="text--xxsmall">
                            <p>{{ 'shopify.online_store.spam_detection.disclaimer_html'  | t }}</p>
                          </div>

                        {%- endform -%}

                      {%- elsif block.settings.cta_button_text != blank -%}

                        {%- capture button_link -%}
                          {%- if block.settings.cta_button_link_advanced != blank -%}
                            {{- block.settings.cta_button_link_advanced -}}
                          {%- elsif block.settings.cta_button_link != blank -%}
                            {{- block.settings.cta_button_link -}}
                          {%- endif -%}
                        {%- endcapture -%}

                        <a class="button {{ section.settings.button_style }} {{ block.settings.cta_button_classes }}" href="{{- button_link -}}" {% if block.settings.cta_button_link contains "https://" or block.settings.button_link contains "http://" %}target="_blank"{% endif %}>
                          {{- block.settings.cta_button_text -}}
                        </a>

                      {%- elsif block.settings.cta_text != blank -%}
                        
                        {{ block.settings.cta_text }}

                      {%- elsif block.settings.cta_custom != blank -%}

                        {{- block.settings.cta_custom -}}

                      {%- endif -%}
                    {%- endcapture -%}

                    {%- if signup_block_ctas != blank -%}
                      <div class="footer-signup-block__actions">
                        {{ signup_block_ctas }}
                      </div>
                    {%- endif -%}

                    {%- if block.settings.terms_text != blank -%}
                      <div class="footer-signup-block__terms-text text--xxsmall">
                        {{- block.settings.terms_text -}}
                      </div>
                    {%- endif -%}

                  </div>

                </collapsible-content>

              </div>

            {%- endif -%}

          {% endfor %}

        {%- endcapture -%}

        {%- if footer_signups != blank -%}
          <div class="footer__signups">
            <div class="footer-signup-blocks">
              {{ footer_signups }}
            </div>
          </div>
        {%- endif -%}

      </div>

      <div class="footer__middle">

        {% comment %} Navigation {% endcomment %}

        {%- capture footer_navigation -%}
          {%- if section.settings.menu != blank -%}

            <div class="footer-navigation footer-block">
              <div class="footer-navigation__inner">

                {% for link in section.settings.menu.links %}

                  {%- assign has_sublinks = false -%}
                  {%- if link.links.size > 0 -%}
                    {%- assign has_sublinks = true -%}
                  {%- endif -%}

                  <div class="footer-navigation__column">

                    {%- capture column_links -%}

                      {%- if link.links.size > 0 -%}
                        <ul class="footer-navigation__linklist linklist list--unstyled text--xsmall" role="list">
                          {% for sub_link in link.links %}

                            {%- assign external_link = false -%}
                            {%- if sub_link.url contains "https://" or sub_link.url contains "http://" -%}
                              {%- assign external_link = true -%}
                            {%- endif -%}

                            <li class="footer-navigation__link-wrapper">
                              <a class="footer-navigation__link link--animated text--subdued" href="{{ sub_link.url }}" {% if external_link == true %}target="blank"{% endif %}>{{ sub_link.title }}</a>
                            </li>
                          {% endfor %}
                        </ul>
                      {%- endif -%}

                    {%- endcapture -%}

                    {%- if has_sublinks == true -%}

                      {%- capture accordion_id -%}
                        {{ section.id }}--info-block--{{ forloop.index }}
                      {%- endcapture  -%}

                      <footer-accordion class="footer-accordion">

                        <div class="footer-accordion__header">

                          {%- if link.url == "#" -%}
                            <span class="footer-navigation__header heading heading--small">{{ link.title }}</span>
                          {%- else -%}
                            <a class="footer-navigation__header heading heading--small" href="{{ link.url }}">{{ link.title }}</a>
                          {%- endif -%}

                          <button class="footer-accordion__expander" type="button" is="toggle-button" aria-controls="{{ accordion_id }}" aria-expanded="false">
                            <span class="animated-plus"></span>
                            <span class="visually-hidden">Expand {{ link.title }}</span>
                          </button>

                        </div>

                        <collapsible-content class="collapsible footer-accordion__collapsible" id="{{ accordion_id }}">

                          <div class="footer-accordion-content footer-accordion__content">
                            {{ column_links }}
                          </div>

                        </collapsible-content>
                        
                      </footer-accordion>

                    {%- else -%}

                      {%- if link.url == "#" -%}
                        <span class="footer-navigation__header heading heading--small">{{ link.title }}</span>
                      {%- else -%}
                        <a class="footer-navigation__header heading heading--small" href="{{ link.url }}">{{ link.title }}</a>
                      {%- endif -%}

                      {{ column_links }}

                    {%- endif -%}

                  </div>
                {% endfor %}

              </div>
            </div>

          {%- endif -%}
        {%- endcapture -%}

        {%- if footer_navigation != blank -%}
          <div class="footer__navigation">
            {{ footer_navigation }}
          </div>
        {%- endif -%}
        
        {% comment %} Info Blocks {% endcomment %}

        {%- capture footer_info_blocks -%}
          {%- if section.settings.menu != blank -%}

          <div class="footer-info-blocks">

              {% for block in section.blocks %}

                {%- if block.type == "info_block" -%}

                  <div class="footer-info-block footer-block {% if block.settings.image %}footer-info-block--text-offset{% endif %}" {{ block.shopify_attributes }}>

                    {%- if block.settings.content -%}

                      <span class="footer-info-block__content text--xsmall">
                        {%- if block.settings.image -%}
                          <span class="footer-info-block__image">
                            {{- block.settings.image | image_url: width: '300', height: '300' | image_tag: preload: false -}}
                          </span>
                        {%- endif -%}
                        {%- if block.settings.title -%}
                          <span class="footer-info-block__title heading heading--small">
                            {{- block.settings.title -}}
                          </span>
                        {%- endif -%}
                        {{- block.settings.content -}}
                      </span>
                      
                    {%- endif -%}

                    {%- if block.settings.button_text != blank and block.settings.button_link != blank -%}
                      
                      <span class="footer-info-block__actions">
                        <a class="button button--xtiny {{ section.settings.button_style }}" href="{{ block.settings.button_link }}" {% if block.settings.button_link contains "https://" or block.settings.button_link contains "http://" %}target="_blank"{% endif %}>
                          {{- block.settings.button_text -}}
                        </a>
                      </span>
                    {%- endif -%}
                    
                  </div>

                {%- endif -%}

              {% endfor %}

            </div>

          {%- endif -%}
        {%- endcapture -%}

        {%- if footer_info_blocks != blank -%}
          <div class="footer__info-blocks">
            {{ footer_info_blocks }}
          </div>
        {%- endif -%}

      </div>

      <div class="footer__bottom">

        {% comment %} Left - Legal Footer {% endcomment %}

        <div class="footer__bottom-left">

          <div class="footer__legal text--xsmall">

            {% comment %} Copyright + Credit {% endcomment %}

            <div class="footer__credit text--subdued">
              <span>&copy; {{ shop.name }} {{ "now" | date: "%Y" }}. All rights reserved.</span>
              <span>On <a href="https://www.shopify.com/" class="link text--subdued">Shopify</a> by <a href="https://www.voltagenewmedia.com/" class="link text--subdued" target="_blank">Voltage</a></span>
            </div>

            {% comment %} Legal Navigation {% endcomment %}

            {%- capture legal_navigation -%}
              {%- for link in section.settings.legal_menu.links -%}

                {%- assign external_link = false -%}
                {%- if link.url contains "https://" or link.url contains "http://" -%}
                  {%- assign external_link = true -%}
                {%- endif -%}

                <a href="{{ link.url }}" class="link text--subdued" {% if external_link == true %}target="blank"{% endif %}>{{ link.title }}</a>
              {%- endfor %}
            {%- endcapture -%}

            {%- if legal_navigation != blank -%}
              <div class="footer__legal-nav">
                {{ legal_navigation }}
              </div>
            {%- endif -%}

          </div>

          {%- if section.settings.show_payment_icons and shop.enabled_payment_types.size > 0 -%}
            <div class="footer__payment-icons">
              <div class="footer__payment-methods">
                <span class="footer__payment-methods-label text--xsmall text--subdued">{{ 'footer.general.we_accept' | t }}</span>

                <div class="payment-methods-list payment-methods-list--auto">
                  {% for type in shop.enabled_payment_types %}
                    {{ type | payment_type_svg_tag }}
                  {% endfor %}
                </div>
              </div>
            </div>
          {%- endif -%}

        </div>

        {% comment %} Right -Localization Form {% endcomment %}

        {%- capture localization_forms -%}

          {%- if section.settings.show_country_selector and localization.available_countries.size > 1 -%}
            {%- assign country_selector = true -%}
          {%- endif -%}

          {%- if section.settings.show_locale_selector and localization.available_languages.size > 1 -%}
            {%- assign locale_selector = true -%}
          {%- endif -%}

          {%- if country_selector or locale_selector -%}
            {%- form 'localization', id: 'localization_form_footer', class: 'footer__localization' -%}
              {%- if country_selector -%}
                <div class="footer-popover-container popover-container footer__country-selector">
                  <input type="hidden" name="country_code" value="{{ localization.country.iso_code }}">
                  <span class="popover-container__label text--xsmall">{{ 'footer.general.country' | t }}</span>

                  <button type="button" is="toggle-button" class="footer-selector select select--small {% if locale_selector and settings.button_border_radius > 0 %}select--collapse-end{% endif %} text--xsmall" aria-expanded="false" aria-controls="footer-currency-selector">
                    {{- localization.country.name }} ({{ localization.country.currency.iso_code }} {% if localization.country.currency.symbol %}{{ localization.country.currency.symbol }}{%- endif -%})
                    {%- render 'icon' with 'chevron', width: 9, height: 6, inline: true -%}
                  </button>

                  <popover-content id="footer-currency-selector" class="popover popover--top popover--left popover--small">
                    <span class="popover__overlay"></span>

                    <header class="popover__header">
                      <span class="popover__title heading h6">{{- 'footer.general.country' | t -}}</span>

                      <button type="button" class="popover__close-button tap-area tap-area--large" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
                        {%- render 'icon' with 'close' -%}
                      </button>
                    </header>

                    <div class="popover__content popover__content--restrict">
                      <div class="popover__choice-list">
                        {%- for country in localization.available_countries -%}
                          <button type="submit" name="country_code" value="{{ country.iso_code }}" class="popover__choice-item">
                            <span class="popover__choice-label" {% if country.iso_code == localization.country.iso_code %}aria-current="true"{% endif %}>
                              {{- country.name }} ({{ country.currency.iso_code }} {% if country.currency.symbol %}{{ country.currency.symbol }}{%- endif -%})
                            </span>
                          </button>
                        {%- endfor -%}
                      </div>
                    </div>
                  </popover-content>
                </div>
              {%- endif -%}

              {%- if locale_selector -%}
                <div class="footer-popover-container popover-container">
                  <input type="hidden" name="locale_code" value="{{ localization.language.iso_code }}">
                  <span class="popover-container__label text--xsmall">{{ 'footer.general.language' | t }}</span>

                  <button type="button" is="toggle-button" class="footer-selector select select--small {% if country_selector and settings.button_border_radius > 0 %}select--collapse-start{% endif %} text--xsmall" aria-expanded="false" aria-controls="footer-locale-selector">
                    {{- localization.language.endonym_name | capitalize -}}
                    {%- render 'icon' with 'chevron', width: 9, height: 6, inline: true -%}
                  </button>

                  <popover-content id="footer-locale-selector" class="popover popover--top popover--small">
                    <span class="popover__overlay"></span>

                    <header class="popover__header">
                      <span class="popover__title heading h6">{{- 'footer.general.language' | t -}}</span>

                      <button type="button" class="popover__close-button tap-area tap-area--large" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
                        {%- render 'icon' with 'close' -%}
                      </button>
                    </header>

                    <div class="popover__content">
                      <div class="popover__choice-list">
                        {%- for language in localization.available_languages -%}
                          <button type="submit" name="locale_code" value="{{ language.iso_code }}" class="popover__choice-item">
                            <span class="popover__choice-label" {% if language.iso_code == localization.language.iso_code %}aria-current="true"{% endif %}>
                              {{- language.endonym_name | capitalize -}}
                            </span>
                          </button>
                        {%- endfor -%}
                      </div>
                    </div>
                  </popover-content>
                </div>
              {%- endif -%}
            {%- endform -%}
          {%- endif -%}

        {%- endcapture -%}

        {%- if localization_forms != blank -%}
          <div class="footer__bottom-right">
            {{- localization_forms -}}
          </div>
        {%- endif -%}

      </div>

    </div>
  </div>
</footer>

{% schema %}
{
  "name": "🪁 Kyte Footer",
  "class": "shopify-section--footer-kyte",
  "settings": [
    {
      "type": "checkbox",
      "id": "no_padding",
      "label": "No vertical spacing",
      "default": true
    },
    {
      "type": "header",
      "content": "Logo"
    },
    {
      "type": "url",
      "id": "logo_link",
      "label": "Logo link",
      "info": "Overrides standard home link."
    },
    {
      "type": "image_picker",
      "id": "logo",
      "label": "Logo"
    },
    {
      "type": "header",
      "content": "Footer Menu"
    },
    {
      "type": "link_list",
      "id": "menu",
      "label": "Footer Menu",
      "default": "footer"
    },
    {
      "type": "checkbox",
      "id": "show_footer_social",
      "label": "Show Footer Social Icons",
      "default": true
    },
    {
      "type": "header",
      "content": "Copyright, Legal and Credit"
    },
    {
      "type": "link_list",
      "id": "legal_menu",
      "label": "Copyright Menu"
    },
    {
      "type": "checkbox",
      "id": "enable_follow_on_shop",
      "label": "Show Follow on Shop",
      "info": "To allow customers to follow your store on the Shop app from your storefront, Shop Pay must be enabled. [Learn more](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_payment_icons",
      "label": "Show payment icons",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_country_selector",
      "label": "Show country/region selector",
      "info": "To add a country/region, go to your [currency settings.](/admin/settings/payments)",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_locale_selector",
      "label": "Show language selector",
      "info": "To add a language, go to your [language settings.](/admin/settings/languages)",
      "default": true
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "Button style",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "button--outline",
          "label": "Outline"
        },
        {
          "value": "button--outline-faint",
          "label": "Outline (faint)"
        },
      ],
      "default": "button--outline-faint"
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background"
    },
    {
      "type": "color",
      "id": "block_background",
      "label": "Block background"
    },
    {
      "type": "color",
      "id": "input_background",
      "label": "Input background"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading"
    },
    {
      "type": "color",
      "id": "subheading_color",
      "label": "Subheading"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background"
    },
    {
      "type": "color",
      "id": "button_text",
      "label": "Button text"
    }
  ],
  "blocks": [
    {
      "type": "info_block",
      "name": "Info Block",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Info block heading"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Content",
          "default": "<p>A short sentence describing what someone will receive by subscribing</p>"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "Positioned overlapping the top-right corner."
        },
        {
          "type": "text",
          "id": "button_text",
          "label": "Button Text"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "Button Link"
        }
      ]
    },
    {
      "type": "signup_block",
      "name": "Signup Block",
      "settings": [
        {
          "type": "header",
          "content": "Content"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Info block heading"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Content",
          "default": "<p>A short sentence describing what someone will receive by subscribing</p>"
        },
        {
          "type": "richtext",
          "id": "terms_text",
          "label": "Terms",
          "info": "Smaller text that appears below the call to action."
        },
        {
          "type": "header",
          "content": "Newsletter"
        },
        {
          "type": "checkbox",
          "id": "newsletter_form",
          "label": "Newsletter Signup Form",
          "default": false,
          "info": "Use the standard Shopify newsletter signup form. (Overrides other options)"
        },
        {
          "type": "header",
          "content": "Button / Link"
        },
        {
          "type": "text",
          "id": "cta_button_text",
          "label": "Button Text"
        },
        {
          "type": "url",
          "id": "cta_button_link",
          "label": "Button Link"
        },
        {
          "type": "text",
          "id": "cta_button_link_advanced",
          "label": "Button link (Advanced)",
          "info": "Used for tel, email and SMS call to actions. Replaces button link."
        },
        {
          "type": "text",
          "id": "cta_button_classes",
          "label": "Custom button classes",
          "info": "Advanced: add classes to button."
        },
        {
          "type": "header",
          "content": "Text CTA"
        },
        {
          "type": "text",
          "id": "cta_text",
          "label": "Text"
        },
        {
          "type": "header",
          "content": "Custom CTA"
        },
        {
          "type": "liquid",
          "id": "cta_custom",
          "label": "Custom CTA"
        }
      ]
    }
  ]
}
{% endschema %}