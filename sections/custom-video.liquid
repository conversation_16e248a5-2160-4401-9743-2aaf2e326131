{%- if section.settings.background != settings.background and section.settings.background != 'rgba(0,0,0,0)' -%}
  {%- assign blends_with_background = false -%}
{%- else -%}
  {%- assign blends_with_background = true -%}
{%- endif -%}

{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
  CSS
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}

<style>
  #shopify-section-{{ section.id }} {
    {%- if section.settings.background == 'rgba(0,0,0,0)' -%}
      {%- assign section_background = settings.background -%}
    {%- else -%}
      {%- assign section_background = section.settings.background -%}
    {%- endif -%}

    {%- if section.settings.text_color == 'rgba(0,0,0,0)' -%}
      {%- assign heading_color = settings.heading_color -%}
      {%- assign text_color = settings.text_color -%}
    {%- else -%}
      {%- assign heading_color = section.settings.text_color -%}
      {%- assign text_color = section.settings.text_color -%}
    {%- endif -%}

    {%- if section.settings.button_color != 'rgba(0,0,0,0)' -%}
      {%- assign button_color = section.settings.button_color -%}
    {%- endif -%}

    {%- if section.settings.button_text_color != 'rgba(0,0,0,0)' -%}
      {%- assign button_text_color = section.settings.button_text_color -%}
    {%- endif -%}

    {%- if section.settings.background_type == 'boxed' -%}
      {%- assign play_button_background = section_background -%}
      {%- assign play_button_arrow = text_color -%}
    {%- else -%}
      {%- assign play_button_background = text_color -%}
      {%- assign play_button_arrow = settings.heading_color -%}
    {%- endif -%}

    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
    --play-button-background: {{ play_button_background.red }}, {{ play_button_background.green }}, {{ play_button_background.blue }};
    --play-button-arrow: {{ play_button_arrow.red }}, {{ play_button_arrow.green }}, {{ play_button_arrow.blue }};

    --section-background: {{ section_background.red }}, {{ section_background.green }}, {{ section_background.blue }};
    --section-items-alignment: {{ section_items_alignment }};
    --section-overlay-color: {{ section.settings.overlay_color.red }}, {{ section.settings.overlay_color.green }}, {{ section.settings.overlay_color.blue }};
    --section-overlay-opacity: {{ section.settings.overlay_opacity | divided_by: 100.0 }}; 

    --button-color: {{ button_color.red }}, {{ button_color.green }}, {{ button_color.blue }};
    --button-text-color: {{ button_text_color.red }}, {{ button_text_color.green }}, {{ button_text_color.blue }};

  }

  #shopify-section-{{ section.id }} .text-container .heading {
    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
  }

  #shopify-section-{{ section.id }} .text-container,
  #shopify-section-{{ section.id }} .text-container p {
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
  }

  #shopify-section-{{ section.id }} .button-wrapper {
    
  }

  #shopify-section-{{ section.id }} .heading {
    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
  }

  #shopify-section-{{ section.id }} .image-overlay__text-container {
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
  }

</style>

{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
  JavaScript: This section composes the "external-video" element, but does not have dedicated custom element
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}

{%- if section.settings.override_blend == true -%}
  {%- assign blends_with_background = false -%}
{%- endif -%}

{%- capture section_classes -%}
  {% render 'section-classes', section: section %}
{%- endcapture -%}

{%- if section.settings.anchor -%}
  <a class="visually-hidden anchor" id="{{ section.settings.anchor }}" name="{{ section.settings.anchor }}"></a>
{%- endif -%}

{% if section.settings.button_link != blank and section.settings.button_text == blank %}
  <a href="{{ section.settings.button_link  }}">
{% endif %}

<section class="section {{ section_classes }} custom-video {% if section.settings.background_type == 'full_width' or section.settings.background_type == 'boxed' and blends_with_background == false %}section--flush{% endif %}">

  {% if section.settings.extra_code != blank %}
    {{ section.settings.extra_code }}
  {% endif %}

  {%- capture section_header -%}

    {%- assign text_alignment = section.settings.text_position | split: '_' | last -%}
    {%- assign text_position = section.settings.text_position -%}

    {%- if section.settings.subheading != blank or section.settings.title != blank -%}
      
      <div class="text-container">
        
        <div class="custom-video__content-wrapper">

          {%- if section.settings.subheading != blank -%}
            <h2 class="heading heading--small">
              <split-lines {% if section.settings.reveal_on_scroll %}reveal{% endif %}>{{ section.settings.subheading | escape }}</split-lines>
            </h2>
          {%- endif -%}

          {%- if section.settings.title != blank -%}
            <h3 class="heading h1">
              <split-lines {% if section.settings.reveal_on_scroll %}reveal{% endif %}>{{ section.settings.title | escape }}</split-lines>
            </h3>
          {%- endif -%}

          {%- if section.settings.content != blank or section.settings.button_text != blank -%}
          
            <div class="custom-video__text-container" {% if section.settings.reveal_on_scroll %}reveal{% endif %}>
              
              {%- if section.settings.text != blank -%}
                <div class="major" {% if section.settings.reveal_on_scroll %}reveal{% endif %}>{{ section.settings.text }}</div>
              {%- endif -%}

            </div>
          {%- endif -%}

          {%- if section.settings.button_text != blank and section.settings.button_link != blank -%}
            {%- if section.settings.link_style == 'link' -%}
              <a href="{{ section.settings.button_link }}" class="multi-column__link heading heading--small link">{{ section.settings.button_text | escape }}</a>
            {%- else -%}
              <div class="button-wrapper">
                <a href="{{ section.settings.button_link }}" class="button button--primary">
                  {{ section.settings.button_text | escape }}
                    <span class="button__text">{% if section.settings.button_icon != "" %}</span>
                    <span class="button__icon">{%- include 'icon' with section.settings.button_icon, block: true, direction_aware: true -%}</span>
                  {% endif %}
                </a>
              </div>
            {%- endif -%}
          {%- endif -%}

        </div>

      </div>

    {%- endif -%}

  {%- endcapture -%}

  {%- capture video_content -%}
    <div class="video-section video-section--{% if section.settings.background_type == 'boxed' %}boxed{% else %}full{% endif %} video-section--{{ section.settings.video_size }}">
      {%- capture video_poster -%}
        <div class="video-wrapper__poster">
          {%- unless section.settings.autoplay -%}
            {%- if section.settings.image != blank -%}
              {{- section.settings.image | image_url: width: section.settings.image.width | image_tag: loading: 'lazy', sizes: '(max-width: 740px) calc(100vw - 24px * 2), 1600px', widths: '400,500,600,700,800,900,1000,1200,1400,1600,1800,2000,2200,2400,2600,2800', class: 'video-wrapper__poster-image' -}}
            {%- else -%}
              {{- 'lifestyle-1' | placeholder_svg_tag: 'video-wrapper__poster-image placeholder-background' -}}
            {%- endif -%}
          {%- endunless -%}

          <div class="video-wrapper__poster-content custom-video__content custom-video__content--text-{{ text_alignment }} custom-video__content--{{ text_position }}">
            {%- unless section.settings.autoplay -%}
              <button type="button" class="video-wrapper__play-button video-wrapper__play-button--large video-wrapper__play-button--ripple" title="{{ 'general.accessibility.play_video' | t | escape }}">
                {%- render 'icon' with 'play', width: 72, height: 72 -%}
              </button>
            {%- endunless -%}

            {%- if section.settings.background_type != 'boxed' -%}
              {{- section_header -}}
            {%- endif -%}
          </div>
        </div>
      {%- endcapture -%}

      {%- if section.settings.video != blank -%}
        <native-video {% if section.settings.autoplay %}autoplay{% endif %} class="video-wrapper video-wrapper--native" style="--aspect-ratio: {{ section.settings.video.aspect_ratio }}">
          {{- video_poster -}}

          <template>
            {{- section.settings.video | video_tag: controls: section.settings.show_video_controls, autoplay: section.settings.autoplay, muted: section.settings.autoplay, playsinline: section.settings.autoplay, loop: section.settings.autoplay -}}
          </template>
        </native-video>
      {%- else -%}
        <external-video {% if section.settings.autoplay %}autoplay{% endif %} provider="{{ section.settings.video_url.type | escape }}" class="video-wrapper {% if section.settings.autoplay %}video-wrapper--inert{% endif %} {% if section.settings.background_type == 'full_width' and section.settings.video_size != 'auto' %}video-wrapper--cover{% endif %}">
          {{- video_poster -}}

          <template>
            {%- if section.settings.video_url.type == 'youtube' -%}
              <iframe title="{{ section.settings.title | escape }}" src="https://www.youtube.com/embed/{{ section.settings.video_url.id }}?playsinline=1&autoplay=1{% if section.settings.autoplay %}&controls=0&mute=1&loop=1{% endif %}&playlist={{ section.settings.video_url.id }}&enablejsapi=1&rel=0&modestbranding=1&origin={{ 'https://' | append: request.host | url_encode }}" allow="autoplay; encrypted-media" allowfullscreen="allowfullscreen"></iframe>
              {%- elsif section.settings.video_url.type == 'vimeo' -%}
              <iframe title="{{ section.settings.title | escape }}" src="https://player.vimeo.com/video/{{ section.settings.video_url.id }}?autoplay=1&autopause=1{% if section.settings.autoplay %}&background=1&loop=1&muted=1{% endif %}&transparent=0&responsive=1&portrait=0&title=0&byline=0&color={{ settings.text_color | remove_first: '#' }}" allow="autoplay; encrypted-media;" allowfullscreen="allowfullscreen"></iframe>
            {%- endif -%}
          </template>
        </external-video>
      {%- endif -%}
    </div>
  {%- endcapture -%}

  {%- if section.settings.background_type == 'boxed' -%}
    <div class="section__color-wrapper">
      <div class="container">
        <div {% unless blends_with_background %}class="vertical-breather"{% endunless %}>
          {{- section_header -}}
          {{- video_content -}}
        </div>
      </div>
    </div>
  {%- else -%}
    {{- video_content -}}
  {%- endif -%}
</section>

{% if section.settings.button_link != blank and section.settings.button_text == blank %}
</a>
{% endif %}

{% schema %}
{
  "name": "🪁 Video",
  "class": "shopify-section--video",
  "disabled_on": {
    "groups": ["header", "custom.overlay"]
  },
  "settings": [
    {
      "type": "header",
      "content": "🪁 Kyte Settings"
    },
    {
      "type": "text",
      "id": "styling_class",
      "label": "Styling Class",
      "info": "For advanced use."
    },
    {
      "type": "text",
      "id": "anchor",
      "label": "Anchor",
      "info": "Anchor name used for jump links.",
      "placeholder": "#anchor"
    },
    {
      "type": "checkbox",
      "id": "use_padding",
      "label": "Use Padding"
    },
    {
      "type": "checkbox",
      "id": "box_shadow",
      "label": "Shadow"
    },
    {
      "type": "checkbox",
      "id": "section_extra_padding",
      "label": "Extra Vertical Padding"
    },
    {
      "type": "checkbox",
      "id": "override_blend",
      "label": "Stop section merging with previous section",
      "default": false,
      "info": "If the section \"merges\" with the previous and next sections, this separates them again."
    },
    {
      "type": "liquid",
      "id": "extra_code",
      "label": "Extra Code",
      "info": "Misc. Liquid code for embellishments."
    },
    {
      "type": "header",
      "content": "Section Settings"
    },
    {
      "type": "select",
      "id": "text_width",
      "label": "Text width",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        },
        {
          "value": "fill",
          "label": "Fill screen"
        }
      ],
      "default": "medium"
    },
    {
      "type": "select",
      "id": "text_position",
      "label": "Text position",
      "options": [
        {
          "value": "top_left",
          "label": "Top left"
        },
        {
          "value": "top_center",
          "label": "Top center"
        },
        {
          "value": "top_right",
          "label": "Top right"
        },
        {
          "value": "middle_left",
          "label": "Middle left"
        },
        {
          "value": "middle_center",
          "label": "Middle center"
        },
        {
          "value": "middle_right",
          "label": "Middle right"
        },
        {
          "value": "bottom_left",
          "label": "Bottom left"
        },
        {
          "value": "bottom_center",
          "label": "Bottom center"
        },
        {
          "value": "bottom_right",
          "label": "Bottom right"
        }
      ],
      "default": "middle_center"
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "Subheading"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading"
    },
    {
      "type": "video",
      "id": "video",
      "label": "Video",
      "info": "Replaces the external video if both are set."
    },
    {
      "type": "richtext",
      "id": "text",
      "label": "Text"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    },
    {
      "type": "select",
      "id": "button_icon",
      "label": "Button Icon",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "nav-arrow-right",
          "label": "Nav Arrow Right",
          "group": "Navigation"
        },
        {
          "value": "nav-arrow-right-small",
          "label": "Nav Arrow Right - Small",
          "group": "Navigation"
        }
      ]
    },
    {
      "type": "header",
      "content": "Video Settings"
    },
    {
      "type": "video_url",
      "id": "video_url",
      "accept": ["vimeo", "youtube"],
      "label": "External video",
      "default": "https://www.youtube.com/watch?v=_9VUPq3SxOc"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Cover image",
      "info": "2000 x 1125px .jpg recommended. Required if you turn off autoplay."
    },
    {
      "type": "select",
      "id": "background_type",
      "label": "Video mode",
      "options": [
        {
          "value": "full_width",
          "label": "Full width"
        },
        {
          "value": "boxed",
          "label": "Boxed"
        }
      ],
      "default": "full_width"
    },
    {
      "type": "select",
      "id": "video_size",
      "label": "Video size",
      "options": [
        {
          "value": "auto",
          "label": "Original ratio"
        },
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ],
      "default": "auto"
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Autoplay",
      "info": "Video is muted automatically to allow autoplay.",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_video_controls",
      "label": "Show video controls",
      "info": "Only applicable with native video.",
      "default": false
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background",
      "default": "rgba(0,0,0,0)",
      "info": "Only used for boxed background."
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "button_color",
      "label": "Button color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "overlay_color",
      "label": "Overlay",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "overlay_opacity",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "label": "Overlay opacity",
      "default": 30
    }
  ],
  "presets": [
    {
      "name": "🪁 Video"
    }
  ]
}
{% endschema %}