{%- if section.settings.background != settings.background and section.settings.background != 'rgba(0,0,0,0)' -%}
  {%- assign blends_with_background = false -%}
{%- else -%}
  {%- assign blends_with_background = true -%}
{%- endif -%}

{%- if section.settings.override_blend == true -%}
  {%- assign blends_with_background = false -%}
{%- endif -%}

<style>
  #shopify-section-{{ section.id }} {
    {%- if section.settings.background == 'rgba(0,0,0,0)' -%}
      {%- assign section_background = settings.background -%}
    {%- else -%}
      {%- assign section_background = section.settings.background -%}
    {%- endif -%}

    {%- if section.settings.heading_color != blank and section.settings.heading_color != 'rgba(0,0,0,0)' -%}
      {%- assign heading_color = section.settings.heading_color -%}
    {%- else -%}
      {%- assign heading_color = settings.heading_color -%}
    {%- endif -%}

    {%- if section.settings.subheading_color != blank and section.settings.subheading_color != 'rgba(0,0,0,0)' -%}
      {%- assign subheading_color = section.settings.subheading_color -%}
    {%- else -%}
      {%- assign subheading_color = settings.subheading_color -%}
    {%- endif -%}

    {%- if section.settings.text_color != blank and section.settings.text_color != 'rgba(0,0,0,0)' -%}
      {%- assign text_color = section.settings.text_color -%}
    {%- else -%}
      {%- assign text_color = settings.text_color -%}
    {%- endif -%}

    {%- if section.settings.button_background == 'rgba(0,0,0,0)' -%}
      {%- assign button_background = settings.primary_button_background -%}
    {%- else -%}
      {%- assign button_background = section.settings.button_background -%}
    {%- endif -%}

    {%- if section.settings.button_text_color == 'rgba(0,0,0,0)' -%}
      {%- assign button_text_color = settings.primary_button_text_color -%}
    {%- else -%}
      {%- assign button_text_color = section.settings.button_text_color -%}
    {%- endif -%}

    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
    --primary-button-background: {{ button_background.red }}, {{ button_background.green }}, {{ button_background.blue }};
    --primary-button-text-color: {{ button_text_color.red }}, {{ button_text_color.green }}, {{ button_text_color.blue }};

    --prev-next-button-background: {{ settings.background.red }}, {{ settings.background.green }}, {{ settings.background.blue }};
    --prev-next-button-color: {{ settings.text_color.red }}, {{ settings.text_color.green }}, {{ settings.text_color.blue }};

    --section-background: {{ section_background.red }}, {{ section_background.green }}, {{ section_background.blue }};
    --section-products-per-row: 2;
  }

  @media screen and (min-width: 741px) {
    #shopify-section-{{ section.id }} {
      --section-products-per-row: {{ section.settings.products_per_row | at_most: 3 }};
    }
  }

  @media screen and (min-width: 1000px) {
    #shopify-section-{{ section.id }} {
      --section-products-per-row: {{ section.settings.products_per_row | at_most: 4 }};
    }
  }

  @media screen and (min-width: 1200px) {
    #shopify-section-{{ section.id }} {
      --section-products-per-row: {{ section.settings.products_per_row }};
    }
  }

  #shopify-section-{{ section.id }} .heading {
    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
  }

  #shopify-section-{{ section.id }} .image-overlay__text-container {
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
  }
  
</style>

{% assign tabs_string = "" %}

{%- for block in section.blocks -%}
  {%- unless tabs_string contains block.settings.tab -%}
    {%- assign tabs_string = tabs_string | append: block.settings.tab | append: "," -%}
  {%- endunless -%} 
{%- endfor -%}

{% assign tabs_array = tabs_string | split: "," %}

{%- if section.settings.override_blend == true -%}
  {%- assign blends_with_background = false -%}
{%- endif -%}

{%- capture section_classes -%}
  {% render 'section-classes', section: section %}
{%- endcapture -%}

<section class="section {{ section_classes }} custom-featured-promotions {% unless blends_with_background %}section--flush{% endunless %}">

  {% if section.settings.extra_code != blank %}
    {{ section.settings.extra_code }}
  {% endif %}

  {%- if section.settings.anchor -%}
    <a class="visually-hidden anchor" id="{{ section.settings.anchor }}" name="{{ section.settings.anchor }}"></a>
  {%- endif -%}

  <div class="section__color-wrapper">
    <div class="container {% unless blends_with_background %}vertical-breather{% endunless %}">
      
      {%- if section.settings.title != blank or section.settings.subheading != blank or section.settings.content != blank -%}

        {% if section.settings.horizontal_header == true %}
          <header class="horizontal-header horizontal-header--tabs">
        {% else %}
          <header class="section__header {% if section.settings.content != blank %}section__header--tight{% endif %}">
        {% endif %}
          
          <div class="text-container">
            {%- if section.settings.subheading != blank -%}
              <h2 class="heading heading--small">{{ section.settings.subheading }}</h2>
            {%- endif -%}

            {%- if section.settings.title != blank -%}
              <h3 class="heading h2--mobile padding-bottom--10">{{ section.settings.title }}</h3>
            {%- endif -%}

            {%- if section.settings.content != blank -%}
              {{- section.settings.content -}}
            {%- endif -%}
          </div>

          {%- if tabs_array.size > 1 -%}
            <tabs-nav class="tabs-nav tabs-nav--center tabs-nav--edge2edge">
              <scrollable-content class="tabs-nav__scroller">
                <div class="tabs-nav__scroller-inner">
                  <div class="tabs-nav__item-list">
                    {%- for tab in tabs_array -%}
                      <button type="button" class="tabs-nav__item heading heading--small" aria-expanded="{% if forloop.first %}true{% else %}false{% endif %}" aria-controls="{{ section.id }}-tab-{{ forloop.index }}" {{ block.shopify_attributes }}>
                        {{- tab -}}
                      </button>
                    {%- endfor -%}
                  </div>
                </div>
              </scrollable-content>
            </tabs-nav>
          {%- endif -%}

        </header>
      {%- endif -%}

      <div class="featured-promotions">

        {%- for tab in tabs_array -%}
            
          <div class="special-promotions" id="{{ section.id }}-tab-{{ forloop.index }}" {% unless forloop.first %}hidden{% endunless %}>

            <native-carousel>

              <div class="special-promotions__list hide-scrollbar">

                {%- for block in section.blocks -%}

                  {% unless block.settings.tab == tab %}
                    {% continue %}
                  {% endunless %}

                  <native-carousel-item id="block-{{ section.id }}-{{ block.id }}">

                    {% if block.settings.link != blank %}
                      <a href="{{ block.settings.link }}" class="special-promotion-item image-zoom" {{ block.shopify_attributes }}>  
                    {% else %}
                      <div class="special-promotion-item" {{ block.shopify_attributes }}>  
                    {% endif %}

                      <figure class="special-promotion-item__figure">

                        <div class="special-promotion-item__figure-inner">

                          {%- if block.settings.image -%}
                            {%- assign mobile_size = 370 | times: block.settings.image.aspect_ratio | ceil -%}
                            {%- assign tablet_size = 520 | times: block.settings.image.aspect_ratio | ceil -%}
                            {%- assign desktop_size = 600 | times: block.settings.image.aspect_ratio | ceil -%}

                            <img loading="lazy" sizes="(max-width: 740px) {{ mobile_size }}px, (max-width: 999px) {{ tablet_size }}px, {{ desktop_size }}px" class="special-promotion-item__image" {% render 'image-attributes', image: block.settings.image, sizes: '300,400,500,600,700,800,900,1000,1100,1200,1300,1400,1500,1600,1700,1800,1900,2000' %}>
                          {%- else -%}
                            {{- 'image' | placeholder_svg_tag: 'special-promotion-item__image special-promotion-item__image placeholder-background' -}}
                          {%- endif -%}

                        </div>

                        <figcaption class="special-promotion-item__caption rte">

                          {% if block.settings.title != blank %}
                            <p class="special-promotion-item__title heading h3">{{- block.settings.title -}}</p>
                          {% endif %}

                          {% if block.settings.icon_title != blank %}
                            <div class="special-promotion-item__icon-wrapper">
                              {% if block.settings.icon != blank %}
                                <div class="special-promotion-item__icon-image">
                                  {%- assign mobile_size = 100 | times: block.settings.image.aspect_ratio | ceil -%}
                                  {%- assign tablet_size = 100 | times: block.settings.image.aspect_ratio | ceil -%}
                                  {%- assign desktop_size = 100 | times: block.settings.image.aspect_ratio | ceil -%}
                                  <img loading="lazy" sizes="(max-width: 740px) {{ mobile_size }}px, (max-width: 999px) {{ tablet_size }}px, {{ desktop_size }}px" class="special-promotion-item__image" {% render 'image-attributes', image: block.settings.icon, sizes: '300,400,500,600,700,800,900,1000,1100,1200,1300,1400,1500,1600,1700,1800,1900,2000' %}>
                                </div>
                              {% endif %}
                              <div class="special-promotion-item__icon-title text--subdued h5">{{- block.settings.icon_title -}}</div>
                            </div>
                          {% endif %}

                        </figcaption>

                      </figure>

                    {% if block.settings.link != blank %}
                      </a>
                    {% else %}
                      </div>
                    {% endif %}

                  </native-carousel-item>

                {%- endfor -%}

              </div>

              {%- if tabs_array.size > 1 -%}
                <page-dots class="text-with-icons__dots dots-nav dots-nav--centered hidden-lap-and-up">
                  {%- for tab in tabs_array -%}
                    <button class="dots-nav__item tap-area" {% if forloop.first %}aria-current="true"{% endif%}>
                      <span class="visually-hidden">{{ 'general.accessibility.go_to_slide' | t: num: forloop.index }}</span>
                    </button>
                  {%- endfor -%}
                </page-dots>
              {%- endif -%}

            </native-carousel>
          
          </div>

        {%- endfor -%} 
                
      </div>
    </div>
  </div>
</section>

{% schema %}
{
  "name": "🪁 Featured promotions",
  "class": "shopify-section--featured-collections",
  "blocks": [
    {
      "type": "promotion",
      "name": "Promotion",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "1500 x 1800 px .jpg recommended"
        },
        {
          "type": "text",
          "id": "tab",
          "label": "Tab",
          "info": "The tab this promotion will be shown in.",
          "default": "Promotions"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Promotion Title"
        },
        {
          "type": "url",
          "id": "link",
          "label": "Link"
        },
        {
          "type": "image_picker",
          "id": "icon",
          "label": "Icon",
          "info": "80 x 80 px .png recommended"
        },
        {
          "type": "text",
          "id": "icon_title",
          "label": "Icon Title"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "header",
      "content": "🪁 Kyte Settings"
    },
    {
      "type": "text",
      "id": "styling_class",
      "label": "Styling Class",
      "info": "For advanced use."
    },
    {
      "type": "text",
      "id": "anchor",
      "label": "Anchor",
      "info": "Anchor name used for jump links.",
      "placeholder": "#anchor"
    },
    {
      "type": "checkbox",
      "id": "use_padding",
      "label": "Use Padding"
    },
    {
      "type": "checkbox",
      "id": "box_shadow",
      "label": "Shadow"
    },
    {
      "type": "checkbox",
      "id": "section_extra_padding",
      "label": "Extra Vertical Padding"
    },
    {
      "type": "checkbox",
      "id": "override_blend",
      "label": "Stop section merging with previous section",
      "default": false,
      "info": "If the section \"merges\" with the previous and next sections, this separates them again."
    },
    {
      "type": "liquid",
      "id": "extra_code",
      "label": "Extra Code",
      "info": "Misc. Liquid code for embellishments."
    },
    {
      "type": "checkbox",
      "id": "horizontal_header",
      "label": "Horizontal Header",
      "default": false
    },
    {
      "type": "header",
      "content": "Section Settings"
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "Subheading",
      "default": "Your title"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Promotions"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "Content"
    },
    {
      "type": "range",
      "id": "products_count",
      "label": "Products to show",
      "min": 4,
      "max": 50,
      "step": 1,
      "default": 8
    },
    {
      "type": "range",
      "id": "products_per_row",
      "label": "Products per row (desktop)",
      "min": 2,
      "max": 6,
      "step": 1,
      "default": 4
    },
    {
      "type": "checkbox",
      "id": "stack_products",
      "label": "Stack products",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_cta",
      "label": "Show add to cart below info",
      "info": "If enabled, we recommend using 4 products per row at maximum.",
      "default": false
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "subheading_color",
      "label": "Subheading color"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text",
      "default": "rgba(0,0,0,0)"
    }
  ],
  "presets": [
    {
      "name": "🪁 Featured promotions",
      "blocks": [
        {
          "type": "promotion"
        }
      ],
      "settings": {}
    }
  ]
}
{% endschema %}