{%- if section.settings.background != settings.background and section.settings.background != 'rgba(0,0,0,0)' -%}
  {%- assign blends_with_background = false -%}
{%- else -%}
  {%- assign blends_with_background = true -%}
{%- endif -%}

{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
  CSS
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}

<style>
  #shopify-section-{{ section.id }} {
    {%- if section.settings.background == 'rgba(0,0,0,0)' -%}
      {%- assign section_background = settings.background -%}
    {%- else -%}
      {%- assign section_background = section.settings.background -%}
    {%- endif -%}

    {%- if section.settings.block_background == 'rgba(0,0,0,0)' -%}
      {%- assign block_background = settings.secondary_background -%}
    {%- else -%}
      {%- assign block_background = section.settings.block_background -%}
    {%- endif -%}


    {%- if section.settings.heading_color != blank and section.settings.heading_color != 'rgba(0,0,0,0)' -%}
      {%- assign heading_color = section.settings.heading_color -%}
    {%- else -%}
      {%- assign heading_color = settings.heading_color -%}
    {%- endif -%}

    {%- if section.settings.subheading_color != blank and section.settings.subheading_color != 'rgba(0,0,0,0)' -%}
      {%- assign subheading_color = section.settings.subheading_color -%}
    {%- else -%}
      {%- assign subheading_color = settings.subheading_color -%}
    {%- endif -%}

    {%- if section.settings.text_color != blank and section.settings.text_color != 'rgba(0,0,0,0)' -%}
      {%- assign text_color = section.settings.text_color -%}
    {%- else -%}
      {%- assign text_color = settings.text_color -%}
    {%- endif -%}


    {%- if section.settings.button_background != blank and section.settings.button_background != 'rgba(0,0,0,0)' -%}
      {%- assign button_background = section.settings.button_background -%}
    {%- else -%}
      {%- assign button_background = settings.button_background -%}
    {%- endif -%}

    {%- if section.settings.button_text_color != blank and section.settings.button_text_color != 'rgba(0,0,0,0)' -%}
      {%- assign button_text_color = section.settings.button_text_color -%}
    {%- else -%}
      {%- assign button_text_color = settings.primary_button_text_color -%}
    {%- endif -%}

    {%- if section.settings.star_color != blank and section.settings.star_color != 'rgba(0,0,0,0)' -%}
      {%- assign star_color = section.settings.star_color -%}
    {%- else -%}
      {%- assign star_color = heading_color -%}
    {%- endif -%}

    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    --subheading-color: {{ subheading_color.red }}, {{ subheading_color.green }}, {{ subheading_color.blue }};
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
    --star-color: {{ star_color.red }}, {{ star_color.green }}, {{ star_color.blue }};

    --button-background: {{ button_background.red }}, {{ button_background.green }}, {{ button_background.blue }};
    --button-text-color: {{ button_text_color.red }}, {{ button_text_color.green }}, {{ button_text_color.blue }};

    --prev-next-button-background: var(--text-color);
    --prev-next-button-color: var(--section-background);

    --section-background: {{ section_background.red }}, {{ section_background.green }}, {{ section_background.blue }};
    --block-background: {{ block_background.red }}, {{ block_background.green }}, {{ block_background.blue }};

  }

  {% comment %} Blocks {% endcomment %}

  #shopify-section-{{ section.id }} .gallery__item {

    {% if section.settings.block_background != blank and section.settings.block_background != 'rgba(0,0,0,0)' %}
      --block-background: {{ section.settings.block_background.red }}, {{ section.settings.block_background.green }}, {{ section.settings.block_background.blue }};
    {% endif %}
    {% if section.settings.block_text_color != blank and section.settings.block_text_color != 'rgba(0,0,0,0)' %}
      --text-color: {{ section.settings.block_text_color.red }}, {{ section.settings.block_text_color.green }}, {{ section.settings.block_text_color.blue }};
    {% endif %}
    {% if section.settings.block_heading_color != blank and section.settings.block_heading_color != 'rgba(0,0,0,0)' %}
      --heading-color: {{ section.settings.block_heading_color.red }}, {{ section.settings.block_heading_color.green }}, {{ section.settings.block_heading_color.blue }};
    {% endif %}
    {% if section.settings.block_subheading_color != blank and section.settings.block_subheading_color != 'rgba(0,0,0,0)' %}
      --subheading-color: {{ section.settings.block_subheading_color.red }}, {{ section.settings.block_subheading_color.green }}, {{ section.settings.block_subheading_color.blue }};
    {% endif %}
    {% if section.settings.block_star_color != blank and section.settings.block_star_color != 'rgba(0,0,0,0)' %}
      --star-color: {{ section.settings.block_star_color.red }}, {{ section.settings.block_star_color.green }}, {{ section.settings.block_star_color.blue }};
    {% endif %}

  }

</style>

{%- if section.settings.override_blend == true -%}
  {%- assign blends_with_background = false -%}
{%- endif -%}

{%- capture section_classes -%}
  {% render 'section-classes', section: section %}
{%- endcapture -%}

{%- assign key_ingredients = product.metafields.custom.key_ingredients.value -%}

{%- if key_ingredients != blank -%}

  <section class="section custom-ingredients-slider {{ section_classes }} {% unless blends_with_background %}section--flush{% endunless %}">
    <div class="section__color-wrapper {% unless blends_with_background %}vertical-breather{% endunless %}">

      {% if section.settings.extra_code != blank %}
        {{ section.settings.extra_code }}
      {% endif %}

      {%- if section.settings.anchor -%}
        <a class="visually-hidden anchor" id="{{ section.settings.anchor }}" name="{{ section.settings.anchor }}"></a>
      {%- endif -%}

      {% comment %} Header {% endcomment %}

      {%- capture header_content -%}

        {%- if section.settings.subheading != blank -%}
          <h2 class="{% if section.settings.subheading_style contains 'heading' %}heading{% endif %} {{ section.settings.subheading_style }}">{{ section.settings.subheading | escape }}</h2>
        {%- endif -%}

        {%- if section.settings.title != blank -%}
          <h3 class="heading {{ section.settings.heading_style }}">{{ section.settings.title | escape }}</h3>
        {%- endif -%}

        {%- if section.settings.intro_text != blank -%}
          <div class="{{ section.settings.intro_text_style }}">
            {{- section.settings.intro_text -}}
          </div>
        {%- endif -%}

        {%- if section.settings.content != blank -%}
          <div class="{{ section.settings.content_style }}">
            {{- section.settings.content -}}
          </div>
        {%- endif -%}

        {%- capture tag_labels -%}

          {%- if section.settings.tag_list != blank -%}

            {%- assign tag_list_array = section.settings.tag_list.tags.value -%}
            {%- for tag in tag_list_array -%}
              <span class="label {{ section.settings.label_style }}">{{ tag }}</span>
            {%- endfor -%}

          {%- elsif section.settings.tag_list_text != blank -%}

            {%- assign tag_list_array = section.settings.tag_list_text | newline_to_br | split: '<br />' -%}
            {%- for tag in tag_list_array -%}
              <span class="label {{ section.settings.label_style }}">{{ tag }}</span>
            {%- endfor -%}

          {%- endif -%}

        {%- endcapture -%}

        {%- if tag_labels != blank -%}
          <div class="product-form__tag-list-list label-list label-list--flex align-items-start justify-content-center">
            {{- tag_labels -}}
          </div>
        {%- endif -%}

      {%- endcapture -%}

      {%- if header_content != blank -%}
        <header class="section__header container text-container">
          {{ header_content }}
        </header>
      {%- endif -%}

      {% comment %} Content {% endcomment %}

      {%- comment -%}
      If we have more than 3 blocks we assume that by default the content may be scrollable. This may not be the case and
      the JavaScript will fired up to remove it in case it is needed, but if it is indeed scrollable this will avoid
      a reflow of the browser rendering engine. From our tests 3 is a sane default.
      {%- endcomment -%}


      <gallery-list class="gallery">

        <scrollable-content {% unless section.settings.show_arrows %}draggable{% endunless %} class="gallery__list-wrapper {% if key_ingredients.size >= 3 %}is-scrollable{% endif %} hide-scrollbar">
          <div class="container">
            <div class="gallery__list">

              {%- for key_ingredient in key_ingredients -%}

                {%- assign filter_product = key_ingredient.product | default: product -%}

                {%- if section.settings.product_filter_enable == true and key_ingredient.product != blank and key_ingredient.product != filter_product -%}
                  {%- continue -%}
                {%- endif -%}

                <gallery-item class="gallery__item key-ingredient-item" id="section-{{ section.id }}--gallery-item--{{ block.id }}" {{ block.shopify_attributes }}>

                  <figure class="gallery__figure key-ingredient-item__inner">

                    <div class="key-ingredient-item__image-container">
                      {{- key_ingredient.image | image_url: width: key_ingredient.image.width | image_tag: loading: 'lazy', sizes: sizes_attribute, widths: '400,500,600,700,800,900,1000,1200,1400,1600,1800,2000,2200,2400,2600,2800,3000', class: 'key-ingredient-item__image' -}}
                    </div>

                    {%- capture caption_content -%}

                      {%- if key_ingredient.scientific_name != blank -%}
                        <p class="key-ingredient-item__subheading gallery__caption-subheading subheading text--xsmall">{{- key_ingredient.scientific_name -}}</p>
                      {%- endif -%}

                      {%- if key_ingredient.title != blank -%}
                        <p class="key-ingredient-item__title gallery__caption-title text--large">{{- key_ingredient.title -}}</p>
                      {%- endif -%}

                      {%- if key_ingredient.description != blank -%}
                        <div class="key-ingredient-item__text gallery__caption-text text--xsmall">{{- key_ingredient.description | metafield_tag -}}</div>
                      {%- endif -%}

                      {%- if key_ingredient.benefits != blank -%}

                        {%- assign benefits = key_ingredient.benefits.value -%}

                        {%- capture benefit_content -%}
                          {%- for benefit in benefits -%}
                            {%- if benefit.title == blank -%}
                              {%- continue -%}
                            {%- endif -%}
                            <div class="benefit-icon">
                              {%- if benefit.icon_image != blank -%}
                                <div class="benefit-icon__icon">
                                  {{ benefit.icon_image | image_url: width: 200 | image_tag: sizes: '200px', widths: '200,300,400', width: 50, height: 50, alt: benefit.icon_image.alt }}
                                </div>
                              {%- endif -%}
                              <div class="benefit-icon__title text--xsmall heading strong">
                                {{ benefit.title }}
                              </div>
                            </div>
                          {%- endfor -%}
                        {%- endcapture -%}

                        {%- if benefit_content != blank -%}
                          <div class="benefit-icons">
                            {{ benefit_content }}
                          </div>
                        {%- endif -%}

                      {%- endif -%}

                    {%- endcapture -%}

                    {%- if caption_content != blank -%}
                      <figcaption class="gallery__caption key-ingredient-item__caption">
                        {{ caption_content }}
                      </figcaption>
                    {%- endif -%}

                  </figure>

                </gallery-item>
              {%- endfor -%}
            </div>
          </div>
        </scrollable-content>

        {%- if key_ingredients.size > 1 and section.settings.show_arrows -%}
          <prev-next-buttons class="gallery__prev-next-buttons prev-next-buttons">
            <button class="gallery__arrow prev-next-button prev-next-button--prev">
              <span class="visually-hidden">{{ 'general.accessibility.previous' | t }}</span>
              {%- include 'icon' with 'nav-arrow-left', direction_aware: true -%}
            </button>

            <button class="gallery__arrow prev-next-button prev-next-button--next">
              <span class="visually-hidden">{{ 'general.accessibility.next' | t }}</span>
              {%- include 'icon' with 'nav-arrow-right', direction_aware: true -%}
            </button>
          </prev-next-buttons>
        {% endif %}

        <div class="gallery__progress-bar-wrapper container">
          <span class="gallery__progress-bar progress-bar" style="--divider: {{ key_ingredients.count }}"></span>
        </div>

      </gallery-list>

      {% comment %} Footer {% endcomment %}

      {%- capture footer_content -%}
        {%- if section.settings.button_text != blank -%}
          <div class="button-wrapper">
            <a href="{{ section.settings.button_link }}" class="button {{ section.settings.button_style }} {{ section.settings.button_size }}">
              <span class="button__text">{{ section.settings.button_text | escape }}</span>
              {% if section.settings.button_icon != "" %}
                <span class="button__icon">{%- include 'icon' with section.settings.button_icon, direction_aware: true -%}</span>
              {% endif %}
            </a>
          </div>
        {%- endif -%}

        {%- if section.settings.buttons_text != blank -%}
          <div class="{{ section.settings.buttons_text_style }}">
            {{ section.settings.buttons_text }}
          </div>
        {%- endif -%}        
      {%- endcapture -%}

      {%- if footer_content != blank -%}
        <div class="section__footer container text-container">
          {{ footer_content }}
        </div>
      {%- endif -%}

    </div>
  </section>

{%- endif -%}

{% schema %}
{
  "name": "🪁 Key Ingredients Slider",
  "class": "shopify-section--gallery",
  "settings": [
    {
      "type": "header",
      "content": "🪁 Kyte Settings"
    },
    {
      "type": "checkbox",
      "id": "use_padding",
      "label": "Use Padding"
    },
    {
      "type": "checkbox",
      "id": "box_shadow",
      "label": "Shadow"
    },
    {
      "type": "checkbox",
      "id": "show_arrows",
      "label": "Show navigation arrows",
      "default": true
    },
    {
      "type": "header",
      "content": "Tags"
    },
    {
      "type": "metaobject",
      "metaobject_type": "tag_list",
      "id": "tag_list",
      "label": "Tag List"
    },
    {
      "type": "textarea",
      "id": "tag_list_text",
      "label": "Tag List",
      "info": "Each tag on a new line."
    },
    {
      "type": "select",
      "id": "label_style",
      "label": "Tag Style",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "label--subdued",
          "label": "Subdued"
        },
        {
          "value": "label--highlight",
          "label": "Highlight"
        },
        {
          "value": "label--custom",
          "label": "Custom"
        },
        {
          "value": "label--custom2",
          "label": "Custom 2"
        },
        {
          "value": "label--light",
          "label": "Light"
        }
      ],
      "default": ""
    },
    {
      "type": "header",
      "content": "Subheading",
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "Text",
      "default": "Subheading"
    },
    {
      "type": "select",
      "id": "subheading_style",
      "label": "Style",
      "options": [
        {
          "group": "Subheading",
          "value": "",
          "label": "Default"
        },
        {
          "group": "Subheading",
          "value": "heading--small",
          "label": "Subheading"
        },
        {
          "group": "Subheading",
          "value": "heading--xsmall",
          "label": "Subheading - Small"
        },
        {
          "group": "Text",
          "value": "text--xxsmall",
          "label": "Tiny"
        },
        {
          "group": "Text",
          "value": "text--xsmall",
          "label": "Extra Small"
        },
        {
          "group": "Text",
          "value": "text--small",
          "label": "Small"
        },
        {
          "group": "Text",
          "value": "text--large",
          "label": "Large"
        }
      ],
      "default": "heading--small"
    },
    {
      "type": "header",
      "content": "Heading",
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Talk about your brand"
    },
    {
      "type": "select",
      "id": "heading_style",
      "label": "Style",
      "default": "h2",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "group": "Subheading",
          "value": "heading--small",
          "label": "Subheading"
        },
        {
          "group": "Subheading",
          "value": "heading--xsmall",
          "label": "Subheading - Small"
        },
        {
          "group": "Heading",
          "value": "h1",
          "label": "Heading 1"
        },
        {
          "group": "Heading",
          "value": "h2",
          "label": "Heading 2"
        },
        {
          "group": "Heading",
          "value": "h3",
          "label": "Heading 3"
        },
        {
          "group": "Heading",
          "value": "h4",
          "label": "Heading 4"
        },
        {
          "group": "Heading",
          "value": "h5",
          "label": "Heading 5"
        },
        {
          "group": "Heading",
          "value": "h6",
          "label": "Heading 6"
        }
      ]
    },
    {
      "type": "header",
      "content": "Intro Text",
    },
    {
      "type": "richtext",
      "id": "intro_text",
      "label": "Text"
    },
    {
      "type": "select",
      "id": "intro_text_style",
      "label": "Style",
      "default": "text--large",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "group": "Text",
          "value": "text--xxsmall",
          "label": "Tiny"
        },
        {
          "group": "Text",
          "value": "text--xsmall",
          "label": "Extra Small"
        },
        {
          "group": "Text",
          "value": "text--small",
          "label": "Small"
        },
        {
          "group": "Text",
          "value": "text--large",
          "label": "Large"
        }
      ]
    },
    {
      "type": "header",
      "content": "Content",
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "Text"
    },
    {
      "type": "select",
      "id": "content_style",
      "label": "Style",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "group": "Text",
          "value": "text--xxsmall",
          "label": "Tiny"
        },
        {
          "group": "Text",
          "value": "text--xsmall",
          "label": "Extra Small"
        },
        {
          "group": "Text",
          "value": "text--small",
          "label": "Small"
        },
        {
          "group": "Text",
          "value": "text--large",
          "label": "Large"
        }
      ],
      "default": ""
    },
    {
      "type": "header",
      "content": "Button",
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    },
    {
      "type": "select",
      "id": "button_size",
      "label": "Button size",
      "options": [
        {
          "value": "button--small",
          "label": "Small"
        },
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "button--large",
          "label": "Large"
        }
      ],
      "default": ""
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "Button style",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "button--primary",
          "label": "Primary"
        },
        {
          "value": "button--secondary",
          "label": "Secondary"
        }
      ],
      "default": ""
    },
    {
      "type": "select",
      "id": "button_icon",
      "label": "Button Icon",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "nav-arrow-right",
          "label": "Nav Arrow Right",
          "group": "Navigation"
        },
        {
          "value": "nav-arrow-right-small",
          "label": "Nav Arrow Right - Small",
          "group": "Navigation"
        }
      ]
    },
    {
      "type": "liquid",
      "id": "button_extra_attributes",
      "label": "Button Attributes"
    },
    {
      "type": "header",
      "content": "Section Colors"
    },
    {
      "type": "color",
      "id": "inner_background",
      "label": "Inner Background (Boxed Only)",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color"
    },
    {
      "type": "color",
      "id": "subheading_color",
      "label": "Subheading color"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text"
    },
    {
      "type": "header",
      "content": "Card Colors",
      "info": "Review colours. You can override these on the review blocks."
    },
    {
      "type": "color",
      "id": "block_star_color",
      "label": "Star color"
    },
    {
      "type": "color",
      "id": "block_heading_color",
      "label": "Heading color"
    },
    {
      "type": "color",
      "id": "block_subheading_color",
      "label": "Subeading color"
    },
    {
      "type": "color",
      "id": "block_text_color",
      "label": "Text color"
    },
    {
      "type": "color",
      "id": "block_background",
      "label": "Background"
    },
    {
      "type": "header",
      "content": "🪁 Advanced"
    },
    {
      "type": "text",
      "id": "styling_class",
      "label": "Styling Class",
      "info": "For advanced use."
    },
    {
      "type": "text",
      "id": "anchor",
      "label": "Anchor",
      "info": "Anchor name used for jump links.",
      "placeholder": "#anchor"
    },
    {
      "type": "checkbox",
      "id": "contain",
      "label": "Contain and clip content",
      "default": false,
      "info": "Check this if this section has a scrolling text animation or botanical illustrations."
    },
    {
      "type": "checkbox",
      "id": "override_blend",
      "label": "Stop section merging with previous section",
      "default": false,
      "info": "If the section \"merges\" with the previous and next sections, this separates them again."
    },
    {
      "type": "liquid",
      "id": "extra_code",
      "label": "Extra Code",
      "info": "Misc. Liquid code for embellishments."
    }
  ],
  "presets": [
    {
      "name": "🪁 Key Ingredients Slider",
      "settings": {}
    }
  ]
}
{% endschema %}