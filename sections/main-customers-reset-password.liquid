<section>
  <div class="container">
    <div class="page-header">
      <div class="page-header__text-wrapper text-container">
        <h1 class="heading h2">{{ 'customer.reset_password.title' | t }}</h1>
        <p>{{ 'customer.reset_password.instructions' | t }}</p>
      </div>
    </div>

    <div class="page-content page-content--small">
      {%- form 'reset_customer_password', name: 'reset', class: 'form', id: 'reset-customer-password' -%}
        {%- if form.errors -%}
          <div class="banner banner--error form__banner" id="login-form-error">
            <span class="banner__ribbon">{% render 'icon' with 'form-error' %}</span>
            <div class="banner__content">{{ form.errors | default_errors }}</div>
          </div>
        {%- endif -%}

        <div class="input">
          <input type="password" id="customer[password]" class="input__field" name="customer[password]" required="required" autocomplete="new-password">
          <label for="customer[password]" class="input__label">{{ 'customer.reset_password.password' | t }}</label>
        </div>

        <div class="input">
          <input type="password" id="customer[password]" class="input__field" name="customer[password_confirmation]" required="required" autocomplete="new-password">
          <label for="customer[password_confirmation]" class="input__label">{{ 'customer.reset_password.password_confirmation' | t }}</label>
        </div>

        <button type="submit" class="form__submit button button--primary button--full">{{ 'customer.reset_password.submit' | t }}</button>
      {%- endform -%}
    </div>
  </div>
</section>

{% schema %}
{
  "name": "Customer reset password",
  "class": "shopify-section--main-customers-reset-password"
}
{% endschema %}