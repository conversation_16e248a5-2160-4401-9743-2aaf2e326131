{%- if section.settings.text != blank -%}

  <style>
    #shopify-section-{{ section.id }} {
      --section-spacing-inline: 0;

      {% if section.settings.scrolling_direction == 'forwards' %}
        --scrolling-text-direction: -1;
      {% else %}
        --scrolling-text-direction: 1;
      {% endif %}

    }

    {% if section.settings.border_top_color != blank and section.settings.border_top_color != 'rgba(0,0,0,0)' %}
      #shopify-section-{{ section.id }} {
        --border-top-color: {{ section.settings.border_top_color.rgb }};
      }
    {% endif %}

    {% if section.settings.border_bottom_color != blank and section.settings.border_bottom_color != 'rgba(0,0,0,0)' %}
      #shopify-section-{{ section.id }} {
        --border-bottom-color: {{ section.settings.border_bottom_color.rgb }};
      }
    {% endif %}

    #shopify-section-{{ section.id }} .scrolling-text {
      
      {% if section.settings.text != blank %}

        {% if section.settings.background != blank and section.settings.background != 'rgba(0,0,0,0)' %}
          --background: {{ section.settings.background.red }}, {{ section.settings.background.green }}, {{ section.settings.background.blue }};
        {% comment %} {% else %} {% endcomment %}
          {% comment %} --background: 255, 255, 255, 0; {% endcomment %}
        {% endif %}

        --heading-color: {{ section.settings.text_color.rgb }};
        --text-color: {{ section.settings.text_color.rgb }};

      {% endif %}
      
    }

  </style>

  {%- capture section_classes -%}
    {% render 'section-classes', section: section %}
  {%- endcapture -%}

  <section class="section {% if section_classes != blank %}{{ section_classes }}{% endif %} section--flush">
    <div class="section__color-wrapper">
      {%- capture section_content -%}
        {%- for i in (1..10) -%}
          <span class="scrolling-text__text {% unless forloop.first %}motion-reduce:hidden{% endunless %}" {% unless forloop.first %}aria-hidden="true"{% endunless %}>
            {{ section.settings.text | replace: "<p", '<p class="heading"' }}
          </span>
        {%- endfor -%}
      {%- endcapture -%}

      <div class="
        scrolling-text 
        scrolling-text--{{ section.settings.scrolling_direction }}
        {% if section.settings.text_size != "" %}scrolling-text--font-{{ section.settings.text_size }}{% endif %}
        {% if section.settings.scrolling_mode == 'scroll' %}scrolling-text--scroll{% else %}scrolling-text--auto{% endif %}
        {% if section.settings.inner_shadow %}scrolling-text--inner-shadow{% endif %}
        {% if section.settings.text_shadow %}scrolling-text--text-shadow{% endif %}
        "
        >

        <marquee-text scrolling-speed="{{ section.settings.scrolling_speed | times: 10 }}" class="scrolling-text__wrapper">
          {{- section_content -}}
        </marquee-text>
      </div>
    </div>
  </section>
{%- endif -%}

{% schema %}
{
  "name": "🪁 Scrolling text",
  "class": "shopify-section--scrolling-text",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "custom.overlay"]
  },
  "settings": [
    {
      "type": "header",
      "content": "🪁 Kyte Settings"
    },
    {
      "type": "text",
      "id": "styling_class",
      "label": "Styling Class"
    },
    {
      "type": "header",
      "content": "Section Settings"
    },
    {
      "type": "richtext",
      "id": "text",
      "label": "Text",
      "default": "<p>Promotion text</p>"
    },
    {
      "type": "select",
      "id": "text_size",
      "label": "Text size",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ],
      "default": ""
    },
    {
      "type": "range",
      "id": "scrolling_speed",
      "min": 3,
      "max": 20,
      "step": 1,
      "unit": "s",
      "label": "Automatic scrolling speed",
      "default": 10
    },
    {
      "type": "select",
      "id": "scrolling_direction",
      "label": "Scroll direction",
      "options": [
        {
          "value": "forwards",
          "label": "Forwards"
        },
        {
          "value": "backwards",
          "label": "Backwards"
        }
      ],
      "default": "forwards",
    },
    {
      "type": "checkbox",
      "id": "inner_shadow",
      "label": "Show quote inner shadow",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "text_shadow",
      "label": "Show quote text shadow",
      "default": false
    },
    {
      "type": "header",
      "content": "Colors",
      "info": "Gradient replaces solid colors when set. Gradient text outline and gradient background cannot be combined."
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text"
    }
  ],
  "presets": [
    {
      "name": "🐧 Scrolling text"
    }
  ]
}
{% endschema %}