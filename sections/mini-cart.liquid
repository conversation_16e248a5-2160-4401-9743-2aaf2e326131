{%- for block in section.blocks -%}

  {%- if block.type contains "gift" -%}

    {%- capture debug -%}
      {{ debug }}
      -----------
      {{ block.type }}
    {%- endcapture -%}

    {%- assign offer_settings_valid = true -%}
    {%- case block.type -%}
      {%- when "cart_based_gift" -%}
        {%- if block.settings.trigger_value <= 0 -%}
          {%- assign offer_settings_valid = false -%}
        {%- endif -%}
      {%- when "product_based_gift" -%}
        {%- if block.settings.trigger_product != blank or block.settings.trigger_product.available == true -%}
          {%- assign offer_settings_valid = false -%}
        {%- endif -%}
      {%- when "product_value_based_gift" -%}
        {%- if block.settings.trigger_product == blank and block.settings.trigger_product_type == blank and block.settings.trigger_product_title_contains == blank or block.settings.trigger_value == blank -%}
          {%- assign offer_settings_valid = false -%}
        {%- endif -%}
    {%- endcase -%}

    {%- if offer_settings_valid == false -%}
      {%- capture debug -%}
        {{ debug }}
        BLOCK INVALID
      {%- endcapture -%}
      {%- continue -%}
    {%- endif -%}

    {%- assign gift_product = block.settings.gift_product -%}
    
    {%- if block.settings.gift_product_variant != blank -%}
      {%- assign gift_variant_id = block.settings.gift_product_variant | times: 1 -%}
    {%- else -%}
      {%- assign gift_variant_id = gift_product.selected_or_first_available_variant.id | times: 1 -%}
    {%- endif -%}

    {%- if gift_variant_id != blank -%}
      {% assign gift_variant = gift_product.variants | where: 'id', gift_variant_id | first %}
    {%- endif -%}

    {%- capture debug -%}
      {{ debug }}
      gift_product - {{ gift_product }}
      gift_variant_id - {{ gift_variant_id }}
      gift_variant - {{ gift_variant }}
    {%- endcapture -%}

    {%- comment -%} Validate Gift Types {%- endcomment -%}

    {%- assign cart_continue = false -%}
    {%- assign offer_ignore = false -%}
    {%- assign offer_invalid = false -%}

    {%- if offer_ignore == true -%}
      {%- continue -%}
    {%- endif -%}


    {% comment %} Invalid Rules {% endcomment %}

    {%- for item in cart.items -%}

      {%- if block.settings.gift_product_invalid_type != blank -%}
        {%- if item.product.type == block.settings.gift_product_invalid_type -%}
          {%- assign offer_invalid = true -%}
          {%- break -%}
        {%- endif -%}
      {%- endif -%}

      {%- if block.settings.gift_product_invalid_tag != blank -%}
        {%- for tag in item.product.tags -%}
          {%- if tag == block.settings.gift_product_invalid_tag -%}
            {%- assign offer_invalid = true -%}
            {%- break -%}
          {%- endif -%}
        {%- endfor -%}
      {%- endif -%}

    {%- endfor -%}

    {%- if cart_continue == true -%}
      {%- continue -%}
    {%- endif -%}


    {%- comment -%} Offer Settings {%- endcomment -%}

    {%- capture offer_trigger -%}
      {
      {%- case block.type -%}
        {%- when "cart_based_gift" -%}
          "value": {{- block.settings.trigger_value | times: 100 -}}
        {%- when "product_based_gift" -%}
          {%- if block.settings.trigger_product == blank -%}
            {%- break -%}
          {%- endif -%}
          {%- assign trigger_product = block.settings.trigger_product -%}
          {%- assign trigger_variant_id = block.settings.trigger_variant | times: 1 -%}
          {%- if block.settings.trigger_variant != blank -%}
            {%- assign trigger_variant = all_products[trigger_product].variants | where: 'id', trigger_variant_id | first -%}
          {%- endif -%}
          {%- assign trigger_quantity = block.settings.trigger_quantity | default: 1 -%}
          {% if trigger_quantity %}
            "quantity": {{- trigger_quantity -}},
          {% endif %}
          "product": {
            "id": {{- trigger_product.id -}},
            "available": "{{- trigger_product.available -}}",
            "handle": "{{- trigger_product -}}",
            "title": "{{- trigger_product.title -}}",
            "price": {{- trigger_product.price -}},
            "variant": {{ trigger_product.selected_or_first_available_variant | json }}
          }
          {%- if trigger_variant != blank -%},
          "variant": {{ trigger_variant | json }}
          {%- endif -%}
        {%- when "product_value_based_gift" -%}
          {%- if block.settings.trigger_product == blank and block.settings.trigger_product_type == blank and block.settings.trigger_product_title_contains == blank or block.settings.trigger_value == blank -%}
            {%- break -%}
          {%- endif -%}
          {%- assign trigger_product = block.settings.trigger_product -%}
          {%- assign trigger_variant_id = block.settings.trigger_variant | times: 1 -%}
          "value": {{- block.settings.trigger_value | times: 100 -}},
          {%- if block.settings.trigger_product != blank -%}
            "product": {{ trigger_product | json }},
            {%- if block.settings.trigger_variant != blank -%}
              {%- assign trigger_variant = all_products[trigger_product].variants | where: 'id', trigger_variant_id | first -%}
              "variant": {{ trigger_variant | json }}
            {%- endif -%}
          {%- elsif block.settings.trigger_product_type -%}
            "product_type": "{{ block.settings.trigger_product_type }}"
          {%- elsif block.settings.trigger_product_title_contains -%}
            "title_contains": "{{ block.settings.trigger_product_title_contains }}"
          {%- endif -%}
      {%- endcase -%}
      }
    {%- endcapture -%}

    {%- capture offer_gift -%}
      {
        "variant": {
          "id": {{- gift_variant_id | times: 1 -}}
        },
        "product": {
          "id": {{- gift_product.id | default: 0 -}},
          "handle": "{{- gift_product.handle -}}",
          "title": "{{- gift_product.title -}}",
          "price": {{- gift_product.price | default: 0 -}}
        }
      }
    {%- endcapture -%}

    {%- capture offer_excludes -%}
      {%- if block.settings.gift_product_exclude_type != blank -%}
        {{- offer_excludes -}}{%- if offer_excludes != blank -%},{%- endif -%}
        "type": "{{ block.settings.gift_product_exclude_type }}"
      {%- endif -%}
    {%- endcapture -%}

    {%- capture offer_excludes -%}
      {%- if block.settings.gift_product_exclude_tag != blank -%}
        {{- offer_excludes -}}{%- if offer_excludes != blank -%},{%- endif -%}
        "tag": "{{ block.settings.gift_product_exclude_tag }}"
      {%- endif -%}
    {%- endcapture -%}

    {%- capture offer_excludes -%}
      {%- if block.settings.gift_product_exclude_collection != blank -%}
        {{- offer_excludes -}}{%- if offer_excludes != blank -%},{%- endif -%}
        "tag": "{{ block.settings.gift_product_exclude_collection }}"
      {%- endif -%}
    {%- endcapture -%}

    {%- capture offer_excludes -%}
      {%- if block.settings.gift_product_exclude_collection != blank -%}
        {%- assign exclude_collection = collections[block.settings.gift_product_exclude_collection] -%}
        {{- offer_excludes -}}{%- if offer_excludes != blank -%},{%- endif -%}
        "collection": {
          "title": "{{ exclude_collection.title }}",
          "products": [
            {% for product in exclude_collection.products %}
              {{ product | json | escape }}{% unless forloop.last %},{% endunless %}
            {% endfor %}
          ]
        }
      {%- endif -%}
    {%- endcapture -%}

    {%- capture offers_json -%}
    {{ offers_json }}{%- if offers_json != blank -%},{%- endif -%}

      {%- if gift_product.available == false -%}
        {%- assign offer_invalid = true -%}
      {%- endif -%}

      {%- if offer_gift != blank and offer_trigger != blank and gift_variant.available != false -%}
        {
          {% if block.settings.title -%}
            "title": "{{- block.settings.title -}}",
          {%- endif %}
          {% if block.settings.limit -%}
            "limit": {{- block.settings.limit -}},
          {%- endif %}
          {%- if offer_excludes != blank -%}
            "excludes": { {{- offer_excludes -}} },
          {%- endif %}
          {% if offer_invalid == true -%}
            "invalid": true,
          {%- endif %}
          "available": {{ gift_variant.available | default: false }},
          "type": "{{- block.type -}}",
          "id": "{{- block.id -}}",
          "trigger": {{- offer_trigger -}},
          "gift": {{- offer_gift -}}
        }
      {%- endif -%}
    {%- endcapture -%}

  {%- endif -%}

{%- endfor -%}

{%- capture free_gift_settings_json -%}
  {%- if offers_json != blank -%}
    {
      "offers": [
        {{ offers_json }}
      ]
    }
  {%- endif -%}
{%- endcapture -%}

{% assign gift_wrap_product = section.settings.gift_wrap_product %}

{%- liquid
  
  assign cart_count = 0

  for line_item in cart.items

    assign exclude_from_cart_count = false
    assign product_has_addon_token = false

    for prop in line_item.properties
      if prop contains '_addonToken'
        assign product_has_addon_token = true
        break
      endif
    endfor
    if product_has_addon_token == true
      assign exclude_from_cart_count = true
      for prop in line_item.properties
        if prop contains '_hasAddons'
          assign exclude_from_cart_count = false
          break
        endif
      endfor
    endif
    
    unless exclude_from_cart_count == true
      assign cart_count = cart_count | plus: line_item.quantity
    endunless

  endfor

-%}

{%- if free_gift_settings_json != blank -%}
  <free-gift-settings id="mini-cart--free-gift-settings" data-settings='{{ free_gift_settings_json }}'></free-gift-settings>
{%- endif -%}

<!-- 
DEBUGGING
{{ debug }}
-->

<cart-drawer section="{{ section.id }}" id="mini-cart" class="mini-cart drawer drawer--large" {% if free_gift_settings_json != blank %}data-gift-settings-id="mini-cart--free-gift-settings"{% endif %}>

  <cart-notification hidden class="cart-notification cart-notification--drawer"></cart-notification>
  
  <span class="drawer__overlay"></span>

  <header class="drawer__header">
    <p class="drawer__title heading h6">
      {% comment %}
        {%- case settings.cart_icon -%}
          {%- when 'shopping_bag' -%}
            {%- render 'icon' with 'header-cart' -%}

          {%- when 'shopping_cart' -%}
            {%- render 'icon' with 'header-shopping-cart' -%}

          {%- when 'tote_bag' -%}
            {%- render 'icon' with 'header-tote-bag' -%}
        {%- endcase -%}
      {% endcomment %}

      <span class="heading heading--small">{{- 'cart.general.title' | t -}}:</span>
      <span class="heading heading--small color--kyte-dark-grey">{{- 'cart.general.item_count' | t: count: cart_count -}}</span>

    </p>

    <button type="button" class="drawer__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
      {%- render 'icon' with 'close' -%}
    </button>
  </header>

  {%- if cart.item_count == 0 -%}
    <div class="drawer__content drawer__content--center">
      <p>{{ 'cart.general.empty' | t }}</p>

      <div class="button-wrapper">
        <a href="{{ section.settings.empty_button_link }}" class="button button--primary">{{ 'cart.general.start_shopping' | t }}</a>
      </div>
    </div>
  {%- else -%}
    <div class="drawer__content">

      {% if localization.country %}

        {% assign customer_country = localization.country %}
        {% assign customer_country_name = localization.country.name %}
        {% assign customer_country_code = localization.country.iso_code %}
        {% assign customer_currency = localization.country.currency.iso_code %}

      {% else %}

        {% assign customer_country = shop.address.country %}
        {% assign customer_country_name = "United States" %}
        {% assign customer_country_code = "US" %}
        {% assign customer_currency = "USD" %}

      {% endif %}

      {%- if customer and customer.default_address and customer.default_address.country_code -%}

        {% assign customer_country_code = customer.default_address.country_code %}
        
        {%- for country in localization.available_countries -%}
          
          {%- if customer.default_address.country_code == country.iso_code -%}

            {%- assign customer_country = country -%}
            {%- assign customer_country_name = country.name -%}
            {%- assign customer_country_code = country.iso_code -%}
            {%- assign customer_currency = country.currency.iso_code -%}
            
            {%- break -%}

          {%- endif -%}

        {%- endfor -%}

      {% endif %}

      {%- if settings.cart_show_free_shipping_threshold and settings.cart_free_shipping_threshold != '' -%}

        {%- assign free_shipping_thresholds = settings.cart_free_shipping_threshold | remove: ' ' | split: ',' -%}
        {%- assign has_found_matching_threshold = false -%}

        {%- if free_shipping_thresholds.size > 1 -%}
          {%- for threshold in free_shipping_thresholds -%}
            {%- assign threshold_parts = threshold | split: ':' -%}
            {%- assign country_code = threshold_parts | first | upcase -%}

            {%- if country_code == customer_country_code -%}
              {%- assign free_shipping_calculated_threshold = threshold_parts | last -%}
              {%- assign has_found_matching_threshold = true -%}
              {%- break -%}
            {%- endif -%}
          {%- endfor -%}

          {% if has_found_matching_threshold == false %}

            {%- assign free_shipping_calculated_threshold = free_shipping_thresholds | last | split: ":" | last -%}
            {%- assign has_found_matching_threshold = true -%}

          {% endif %}

        {%- else -%}

          {%- assign free_shipping_calculated_threshold = free_shipping_thresholds | last | split: ":" | last -%}
          {%- assign has_found_matching_threshold = true -%}
        {%- endif -%}

        {%- if has_found_matching_threshold -%}
          {%- assign threshold_in_cents = free_shipping_calculated_threshold | times: 100.0 -%}

          {%- for block in section.blocks -%}

            {%- if block.type == "shipping_rate" -%}

              {% assign special_threshold = block %}

              {%- if block.settings.country_codes != blank -%}
                {%- assign country_codes = block.settings.country_codes | split: ',' -%}
                {%- assign available_in_market = false -%}
                {%- for country_code in country_codes -%}
                  {%- assign country_code_stripped = country_code | strip -%}
                  {%- if country_code_stripped == localization.country.iso_code -%}
                    {%- assign available_in_market = true -%}
                  {%- endif -%}
                {%- endfor -%}
                {%- if available_in_market != true -%}
                  {%- continue -%}
                {%- endif -%}
              {%- endif -%}

              {%- if special_threshold.settings.title == customer_country_code -%}

                {% if customer.tags contains 'tier: Gold' %}
                  {%- if special_threshold.settings.rate_gold != blank -%}
                    {%- assign threshold_in_cents = special_threshold.settings.rate_gold | times: 100 -%}
                    {%- assign tier_name = "Gold" -%}
                  {%- endif -%}
                {% elsif customer.tags contains 'tier: Silver' %}
                  {%- if special_threshold.settings.rate_silver != blank -%}
                    {%- assign threshold_in_cents = special_threshold.settings.rate_silver | times: 100 -%}
                    {%- assign tier_name = "Silver" -%}
                  {%- endif -%}
                {% else %}
                  {%- assign tier_name = "Bronze" -%}
                  {%- assign threshold_in_cents = free_shipping_calculated_threshold | times: 100 -%}
                {% endif %}

                {%- break -%}

              {%- endif -%}

            {%- endif -%}
            
          {%- endfor -%}

         {%- if threshold_in_cents == blank -%}
          {%- assign threshold_in_cents = free_shipping_calculated_threshold | times: 100 -%}
         {%- endif -%}
         
         {%- if tier_name == blank -%}
          {%- assign tier_name = "Bronze" -%}
         {%- endif -%}

          {% comment %}
            {%- assign threshold_in_cents = free_shipping_calculated_threshold | times: 100 -%}
          {% endcomment %}

          {%- assign calculated_total_price = cart.total_price -%}
          {%- assign free_shipping_amount_excluded = 0 -%}
          
          {%- for line_item in cart.items -%}
            
            {%- assign counts_toward_free_shipping = false -%}
            {%- if line_item.product.tags contains 'no-free-ship' -%}
              {%- assign has_free_shipping_exclude_tag = true -%}
            {%- else -%}
              {%- assign has_free_shipping_exclude_tag = false -%}
            {%- endif -%}

            {%- if line_item.requires_shipping -%}
              {% assign counts_toward_free_shipping = true %}
            {%- else -%}
              {% assign counts_toward_free_shipping = false %}
            {%- endif -%}

            {%- if has_free_shipping_exclude_tag == true -%}
              {% assign counts_toward_free_shipping = false %}
            {%- endif -%}

            {%- unless counts_toward_free_shipping == true -%}
              {%- assign calculated_total_price = calculated_total_price | minus: line_item.final_line_price -%}
              {%- assign free_shipping_amount_excluded = free_shipping_amount_excluded | plus: line_item.final_line_price -%}
            {%- endunless -%}
          {%- endfor -%}

          <free-shipping-bar threshold="{{ threshold_in_cents }}" class="shipping-bar" style="--progress: {{ calculated_total_price | times: 1.0 | divided_by: threshold_in_cents | at_most: 1 }}">
            {%- if calculated_total_price >= threshold_in_cents -%}
              
              <span class="shipping-bar__inner">
                <span class="shipping-bar__flag">{% render 'country-code-flag', country_code: customer_country_code %}</span>
                <span class="shipping-bar__text text--small">{{ 'cart.general.free_shipping' | t }}</span>
              </span>

            {%- else -%}
              {%- capture remaining_amount -%}{{ calculated_total_price | minus: threshold_in_cents | abs | money }}{%- endcapture -%}

              {% capture free_shipping_message %}
                {{ 'cart.general.free_shipping_remaining_html' | t: remaining_amount: remaining_amount }}
                {%- if customer_country -%}
                  {{- 'cart.general.free_shipping_to_html' | t: country_name: customer_country_name -}}
                {%- endif -%}! 
              {% endcapture %}

              <span class="shipping-bar__inner">
                <span class="shipping-bar__flag">{% render 'country-code-flag', country_code: customer_country_code %}</span>
                <span class="shipping-bar__text text--small">
                  {{ free_shipping_message }}
                </span>
              </span>

            {%- endif -%}

            <span class="shipping-bar__progress"></span>
          </free-shipping-bar>
        {%- endif -%}
      {%- endif -%}

      {%- if section.settings.cart_notice_message != blank and section.settings.cart_notice_enable == true -%}

        {%- capture cart_notice_icon -%}
          {%- if section.settings.cart_notice_custom_icon != blank -%}
            {{ section.settings.cart_notice_custom_icon | image_url: width: 60 | image_tag: class: "mix-blend-mode--multiply"  }}
          {%- elsif section.settings.cart_notice_icon != '' -%}
            {% render 'icon', icon: section.settings.cart_notice_icon %}
          {%- endif -%}
        {%- endcapture -%}

        {%- capture cart_notice_classes -%}
          {{ section.settings.cart_notice_style }}
          {% if section.settings.cart_notice_text_align == "center" %}
            flex-column
            justify-content-center
            align-items-center
            text-center
          {% else %}
          {% endif %}
        {%- endcapture -%}

        <div class="cart-notice-message margin-top--20 {{ cart_notice_classes }}">
          {%- if cart_notice_icon != "" -%}
            <span class="cart-notice-message__icon">
              {{ cart_notice_icon }}
            </span>
          {%- endif -%}
          <span class="cart-notice-message__text">{{ section.settings.cart_notice_message }}</span>
        </div>
        
      {%- endif -%}

      <form id="mini-cart-form" action="{{ routes.cart_url }}" novalidate method="post">
        <input type="hidden" name="checkout">

        {%- for line_item in cart.items -%}

          {%- liquid

            assign product = line_item.product
            assign product_limit = ""
            assign locale_limit = ""
            assign disable_remove = false
            assign disable_quantity_inputs = false

            comment
              Product Limit
            endcomment

            for tag in product.tags
              if tag contains '_per_customer'
                assign split_tag = tag | split: '_'
                assign product_limit_word = split_tag[0]
                case product_limit_word
                  when 'one'
                    assign product_limit = "1"
                  when 'two'
                    assign product_limit = "2"
                  when 'three'
                    assign product_limit = "3"
                  when 'four'
                    assign product_limit = "4"
                  when 'five'
                    assign product_limit = "5"
                  when 'six'
                    assign product_limit = "6"
                  when 'seven'
                    assign product_limit = "7"
                  when 'eight'
                    assign product_limit = "8"
                  when 'nine'
                    assign product_limit = "9"
                  when 'ten'
                    assign product_limit = "10"
                  else
                    assign product_limit = ""
                endcase
              endif
              if tag == "CA-only"
                assign locale_limit = "Only available in Canada"
              endif
            endfor

            assign product_limit = product_limit | plus: 0

            assign line_item_gift_wrap_disabled = false
            if product.tags contains 'disable-gift-wrap'
              assign line_item_gift_wrap_disabled = true
            endif

            comment 
              Hiding Inputs for Free Gifts
            endcomment

            for block in section.blocks
              if block.type contains "gift" and block.settings.gift_product
                if line_item.product == block.settings.gift_product
                  assign disable_quantity_inputs = true
                  assign disable_remove = true
                endif
              endif
            endfor

            comment 
              Hiding Addons from Cart
            endcomment

            assign hide_in_cart = false
            for prop in line_item.properties
              if prop contains 'Addon For '
                assign hide_in_cart = true
                break
              endif
            endfor

            comment 
              ADdon Products
            endcomment

            assign product_addon_token = blank
            assign product_has_addons = false

            for prop in line_item.properties
              if prop contains '_hasAddons'
                assign product_has_addons = true
                break
              endif
            endfor

            if product_has_addons == true
              for prop in line_item.properties
                if prop contains '_addonToken'
                  assign product_addon_token = prop.last
                  break
                endif
              endfor
            endif
            
            assign is_offer_item = false
            for property in line_item.properties
              if property.first contains "Offer"
                assign is_offer_item = true
              endif
            endfor

            assign badges = blank
            assign properties = blank

          -%}

          {%- if hide_in_cart == true -%}
            {%- continue -%}
          {%- endif -%}

          <line-item class="line-item {% if is_offer_item == true %}line-item--offer{% endif %}" data-key="{{ line_item.key }}">
            <div class="line-item__content-wrapper">
              <a href="{{ line_item.url }}" class="line-item__image-wrapper" tabindex="-1" aria-hidden="true">
                <span class="line-item__loader" hidden>
                  <span class="line-item__loader-spinner spinner" hidden>{% render 'icon' with 'spinner', width: 16, height: 16, stroke_width: 6 %}</span>
                  <span class="line-item__loader-mark" hidden>{% render 'icon' with 'check', width: 20, height: 20 %}</span>
                </span>

                {%- if line_item.image != blank -%}
                  {{- line_item.image | image_url: width: line_item.image.width | image_tag: loading: 'lazy', sizes: '(max-width: 740px) 80px, 92px', widths: '80,92,160,184,240,276', class: 'line-item__image' -}}
                {%- endif -%}
              </a>

              {%- capture unit_price -%}
                {%- if line_item.unit_price_measurement -%}
                  <div class="price text--subdued">
                    <div class="unit-price-measurement">
                      <span class="unit-price-measurement__price">{{ line_item.unit_price | money }}</span>
                      <span class="unit-price-measurement__separator">/</span>

                      {%- if line_item.unit_price_measurement.reference_value != 1 -%}
                        <span class="unit-price-measurement__reference-value">{{ line_item.unit_price_measurement.reference_value }}</span>
                      {%- endif -%}

                      <span class="unit-price-measurement__reference-unit">{{ line_item.unit_price_measurement.reference_unit }}</span>
                    </div>
                  </div>
                {%- endif -%}
              {%- endcapture -%}

              {%- capture line_price -%}
                {%- comment -%}
                IMPLEMENTATION NOTE: The designer wanted to show the "compare at price" on cart. In case an automatic discount is applied
                  to a line item though, the "real" discount takes precedence over the compare at price
                {%- endcomment -%}

                <span class="price {% if line_item.original_line_price > line_item.final_line_price or line_item.final_line_price == 0 or line_item.variant.compare_at_price > line_item.variant.price %}price--highlight{% endif %}">
                  <span class="visually-hidden">{{ 'product.general.sale_price' | t }}</span>

                  {%- if line_item.final_line_price == 0 -%}
                    {{- 'cart.general.free' | t -}}
                  {%- else -%}
                    {{- line_item.final_line_price | money -}}
                  {%- endif -%}
                </span>

                {%- if line_item.original_line_price > line_item.final_line_price or line_item.variant.compare_at_price > line_item.variant.price -%}
                  <span class="price price--compare">
                    <span class="visually-hidden">{{ 'product.general.regular_price' | t }}</span>

                    {%- if line_item.original_line_price > line_item.final_line_price -%}
                      {{- line_item.original_line_price | money -}}
                    {%- else -%}
                      {{- line_item.variant.compare_at_price | times: line_item.quantity | money -}}
                    {%- endif -%}
                  </span>
                {%- endif -%}
              {%- endcapture -%}

              <div class="line-item__info">

                {%- comment -%}
                  {%- capture badges -%}
                    {%- if product.metafields.custom.kyte_curate == true and collection.handle != settings.kyte_curates_collection -%}
                      <span class="label label--small label--kyte-curates">{{ 'kyte.general.kyte_curates' | t }}</span>
                    {%- endif -%}
                  {%- endcapture -%}
                {%- endcomment -%}

                {%- capture line_item_properties -%}
                  {%- unless line_item.product.has_only_default_variant -%}
                    <span class="product-item-meta__property text--xsmall text--subdued">{{ line_item.variant.title }}</span>
                  {%- endunless -%}

                  {%- if line_item.selling_plan_allocation -%}
                    <span class="product-item-meta__property text--xsmall text--subdued">{{ line_item.selling_plan_allocation.selling_plan.name }}</span>
                  {%- endif -%}

                  {%- unless line_item.properties == blank -%}

                    {%- for property in line_item.properties -%}
                      
                      {%- assign hide_first_part = false -%}
                      {%- if property.first contains "Offer" -%}
                        {%- assign hide_first_part = true -%}
                      {%- endif -%}

                      {%- assign first_character_in_key = property.first | truncate: 1, '' -%}
                      {%- if property.last == blank or first_character_in_key == '_' -%}
                        {%- continue -%}
                      {%- endif -%}

                      {%- assign is_badge = false -%}
                      {%- if property.first == "Offer" -%}
                        {%- assign is_badge = true -%}
                      {%- endif -%}

                      {%- capture property_content -%}
                        {%- unless property.first contains "Offer" -%}
                          {{ property.first }}
                        {%- endunless -%}
                        {%- unless hide_first_part == true -%}
                          :
                        {%- endunless -%}
                        {% if property.last %}{{ property.last }}{% endif %}
                      {%- endcapture -%}

                      {%- if is_badge == true -%}
                        {%- capture badges -%}
                          {{ badges }}
                          <span class="label label--custom label--small">{{ property_content }}</span>
                        {%- endcapture -%}
                      {%- else -%}
                        {%- capture properties -%}
                          {{ properties }}
                          <li>
                            {{ property_content }}
                          </li>
                        {%- endcapture -%}
                      {%- endif -%}

                    {%- endfor -%}

                  {%- endunless -%}

                {%- endcapture -%}

                <div class="product-item-meta">
                  {%- if settings.show_vendor -%}
                    {%- assign vendor_handle = line_item.vendor | handle -%}
                    {%- assign collection_for_vendor = collections[vendor_handle] -%}

                    {%- unless collection_for_vendor.empty? -%}
                      <a class="product-item-meta__vendor heading heading--xxsmall" href="{{ collection_for_vendor.url }}">{{ line_item.vendor }}</a>
                      {%- else -%}
                      <a class="product-item-meta__vendor heading heading--xxsmall" href="{{ line_item.vendor | url_for_vendor }}">{{ line_item.vendor }}</a>
                    {%- endunless -%}
                  {%- endif -%}

                  <a href="{{ line_item.url }}" class="product-item-meta__title text--small">{{ line_item.product.title }}</a>

                  {%- if properties != blank -%}
                    <ul class="product-item-meta__property list--unstyled text--subdued" role="list">
                      {{ properties }}
                    </ul>
                  {%- endif -%}

                  {%- if line_item_properties != blank -%}
                    <div class="product-item-meta__property-list">
                      {{- line_item_properties -}}
                    </div>
                  {%- endif -%}

                  <div class="product-item-meta__price-list-container text--small">
                    <div class="price-list">
                      {{- line_price -}}
                      {{- unit_price -}}
                    </div>

                    {%- if unit_price != blank -%}
                      <div class="price-list hidden-phone">
                        {{- unit_price -}}
                      </div>
                    {%- endif -%}
                  </div>

                </div>

                {%- if line_item.line_level_discount_allocations != blank -%}
                  <ul class="line-item__discount-list list--unstyled" role="list">
                    {%- for discount_allocation in line_item.line_level_discount_allocations -%}
                      <li class="line-item__discount-badge discount-badge">
                        {%- render 'icon' with 'discount-badge' -%}{{ discount_allocation.discount_application.title }} (-{{ discount_allocation.amount | money }})
                      </li>
                    {%- endfor -%}
                  </ul>
                {%- endif -%}

                {%- assign max_allowed_quantity = '' -%}
                {%- assign allow_more = true -%}
                {%- capture stock_tooltip -%}
                  {{- 'cart.general.no_more_stock' | t | escape -}}
                {%- endcapture -%}

                {%- if line_item.variant.inventory_management == 'shopify' and line_item.variant.inventory_policy == 'deny' and line_item.variant.inventory_quantity <= line_item.quantity -%}
                  {%- assign max_allowed_quantity = line_item.variant.inventory_quantity -%}
                  {%- assign allow_more = false -%}
                {%- endif -%}

                {%- if product_limit != 0 and line_item.quantity >= product_limit -%}
                  {%- assign allow_more = false -%}
                  {%- assign max_allowed_quantity = product_limit -%}
                  {%- capture stock_tooltip -%}
                    {{- 'product.general.product_limit' | t: limit: product_limit -}}
                  {%- endcapture -%}
                {%- endif -%}

                <line-item-quantity class="line-item__quantity">

                  {% unless line_item.product.id == gift_wrap_product.id %}

                    <div class="quantity-selector quantity-selector--small {% if disable_quantity_inputs == true %}quantity-selector--unstyled{% endif %}">
                      {%- unless disable_quantity_inputs -%}
                      <a href="{{ routes.cart_change_url }}?quantity={{ line_item.quantity | minus: 1 }}&line={{ forloop.index }}" class="quantity-selector__button" aria-label="{{ 'cart.general.decrease_quantity' | t | escape }}" data-no-instant>
                        {%- render 'icon' with 'minus' -%}
                      </a>
                      {%- endunless -%}

                      <input is="input-number" class="quantity-selector__input text--xsmall" autocomplete="off" type="text" inputmode="numeric" name="updates[]" data-line="{{ forloop.index }}" value="{{ line_item.quantity }}" {% if max_allowed_quantity != '' %}max="{{ max_allowed_quantity }}"{% endif %} size="{{ line_item.quantity | append: '' | size | at_least: 2 }}" aria-label="{{ 'cart.general.change_quantity' | t | escape }}"{% if disable_quantity_inputs %}disabled{% endif %}>

                      {%- unless disable_quantity_inputs -%}
                      {%- if allow_more -%}
                        <a href="{{ routes.cart_change_url }}?quantity={{ line_item.quantity | plus: 1 }}&line={{ forloop.index }}" class="quantity-selector__button" aria-label="{{ 'cart.general.increase_quantity' | t | escape }}" data-no-instant>
                          {%- render 'icon' with 'plus' -%}
                        </a>
                      {%- else -%}
                        <span class="quantity-selector__button" aria-label="{{ stock_tooltip }}" data-tooltip="{{ stock_tooltip }}">
                          {%- render 'icon' with 'plus' -%}
                        </span>
                      {%- endif -%}
                      {%- endunless -%}
                    </div>

                  {% endunless %}

                  {%- unless disable_remove == true -%}
                  <a href="{{ line_item.url_to_remove }}" class="line-item__remove-button link text--subdued text--xxsmall" data-no-instant>{{ 'cart.general.remove' | t }}</a>
                  {%- endunless -%}

                </line-item-quantity>

                <span class="line-item-quantity--display">
                  <span data-quantity="{{ line_item.quantity }}"></span>
                </span>

                {%- if badges != blank -%}
                  <ul class="product-item-meta__badges badge-list" role="list">
                    {{ badges }}
                  </ul>
                {%- endif -%}

              </div>

              {%- comment -%}
              <div class="line-item__price-list-container text--small hidden-phone">
                {%- if settings.show_vendor -%}
                  {%- comment -%}
                    IMPLEMENTATION NOTE: in the design, the price is aligned in regards of the product title (not the brand). It was a
                    bit hard to do as we cannot set a fixed margin, so I am actually adding an empty vendor to simulate the same height
                  {%- endcomment -%}
                  <span class="product-item-meta__vendor heading heading--xxsmall" style="visibility: hidden">x</span>
                {%- endif -%}

                <div class="price-list price-list--stack">
                  {{- line_price -}}
                </div>
              </div>
              {%- endcomment -%}
               
            </div>

            {%- if product_limit != 0 -%}
              <div class="note note--tiny note--tertiary">
                <span class="note__icon">{% render 'icon' with 'picto-coupon' %}</span>
                <span class="note__text">{{ 'product.general.product_limit' | t: limit: product_limit }}</span>
              </div>
            {%- endif -%}

            {%- if line_item_gift_wrap_disabled == true -%}
              <div class="note note--tiny note--danger">
                <span class="note__icon">{% render 'icon' with 'picto-gift' %}</span>
                <span class="note__text">{{ 'cart.general.gift_wrap.disabled_line_item_message' | t }}</span>
              </div>
            {%- endif -%}

            {% comment %} SPECIAL MESSAGING {% endcomment %}

            {% capture special_messaging_content %}

              {% comment %}
                Checks for the clearance message tag or clearance collection, and sets a variable to true if present.
              {% endcomment %}

              {%- assign is_clearance = false -%}

              {%- for collection in settings.clearance_collections -%}
                {%- if product.collections contains collection -%}
                  {%- assign is_clearance = true -%}
                  {%- break -%}
                {%- endif -%}
              {%- endfor -%}

              {%- if product.tags contains settings.clearance_tag -%}
                {%- assign is_clearance = true -%}
              {%- endif -%}

              {% comment %}
                Display a banner
              {% endcomment %}

              {%- if is_clearance == true and settings.clearance_message != blank -%}
                <div class="note note--small note--primary text--xxsmall">
                  <span class="note__icon">{%- render 'icon' with settings.clearance_icon -%}</span>
                  <span class="note__text">
                    {{ settings.clearance_message }}
                  </span>
                </div>
              {%- endif -%}

            {% endcapture %}

            {% if special_messaging_content != blank %}
              <div class="product-item-meta__special-messaging" {{ block.shopify_attributes }}>
                {{ special_messaging_content }}
              </div>
            {% endif %}

            {% comment %} ADDONS {% endcomment %}

            {% if product_has_addons == true %}

              {%- capture addon_line_items -%}
                {%- for item2 in cart.items -%}
                  {%- assign addon_line_item = blank -%}
                  {%- for prop in item2.properties -%}
                    {%- if prop.last contains product_addon_token and item2 != line_item -%}
                      <cart-addon-item class="cart-product-addon" data-addon-key="{{ item2.key }}">
                        
                        <div class="cart-product-addon__header">
                          <span class="cart-product-addon__title heading--small">
                            <span class="cart-product-addon__title-text">{{ item2.title }}</span>
                            <span class="cart-product-addon__quantity heading--small">x {{ item2.quantity }}</span>
                          </span>
                          <a href="/cart/change?id={{item2.key}}&quantity=0" class="cart-product-addon__remove text--xsmall link--animated " data-addon-remove>{{ 'cart.general.remove' | t }}</a>
                          <span class="cart-product-addon__price price">{{ item2.final_line_price | money | replace: "USD", "" }}</span>
                        </div>

                        {%- capture addon_details -%}
                          {%- for prop in item2.properties -%}
                            {% unless prop.first contains '_' or prop.first contains "Addon For" %}

                              <div class="cart-product-addon-detail cart-product-addon-detail--{{ prop.first | handle }}">
                                <div class="cart-product-addon-detail__icon" {% if prop.first == "Color" %}{%- render 'addons-swatch-value', value_name: prop.last -%}{% endif %}></div>
                                <div class="cart-product-addon-detail__text">
                                  <div class="cart-product-addon-detail__label heading--small weight-normal">
                                    {% if prop.first == "Embroidery Text" %}{{ prop.first | replace: "Embroidery " }}{% else %}{{ prop.first }}{% endif %}
                                  </div>
                                  <div class="cart-product-addon-detail__value text--xsmall">{{ prop.last }}</div>
                                </div>
                              </div>
                            {% endunless %}
                          {%- endfor -%}
                        {%- endcapture -%}

                        {%- if addon_details != blank -%}
                          <div class="cart-product-addon__body">
                            <div class="cart-product-addon-details">
                              {{ addon_details }}
                            </div>
                          </div>
                        {%- endif -%}

                      </cart-addon-item>
                    {%- endif -%}
                  {%- endfor -%}
                {%- endfor -%}
              {%- endcapture -%}

              {% if addon_line_items %}
                <div class="line-item__adons">
                  <div class="cart-product-addons">
                    {{ addon_line_items }}
                  </div>
                </div>
              {% endif %}

            {% endif %}

          </line-item>
        {%- endfor -%}
      </form>

      {%- if section.settings.recommendations_mode == "" -%}
      
      {%- elsif section.settings.recommendations_mode == "theme" -%}

        {%- capture cart_recommendations -%}
          {%- for product in section.settings.recommendations_products -%}
            {%- unless product.title contains 'Route' or product.metafields.custom.exclusive_access.value != blank -%}
              <div>{%- render 'product-item--upsell', product: product, reduced_content: true, reduced_font_size: true, hide_secondary_image: true, sizes_attribute: '(max-width: 740px) 65px, 92px' -%}</div>
            {%- endunless -%}
          {%- endfor -%}
        {%- endcapture -%}

        {%- if cart_recommendations != blank -%}
          
          <div class="cart-drawer-recommendations-container">
            <div class="mini-cart__recommendations">

              {%- if section.settings.recommendations_title != blank -%}
                <p class="heading heading--small hidden-pocket">{{ section.settings.recommendations_title | escape }}</p>
                <p class="heading heading--small text--subdued hidden-lap-and-up">{{ section.settings.recommendations_title | escape }}</p>
              {%- endif -%}
              
              <div class="mini-cart__recommendations-inner">

                <div class="scroller">
                  <div class="scroller__inner">
                    <div class="mini-cart__recommendations-list">
                      {{ cart_recommendations }}                    
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>

        {%- endif -%}

      {%- else -%}

      <div class="cart-drawer-recommendations-container">

        <cart-drawer-recommendations section-id="{{ section.id }}" product-id="{{ cart.items.first.product_id }}" class="mini-cart__recommendations" intent="{{ section.settings.recommendations_mode }}">
          {%- assign acceptable_recommendations_count = 0 -%}

          {%- for product in recommendations.products -%}
            {%- assign matching_product = cart.items | where: 'product_id', product.id | first -%}

            {%- if matching_product == blank -%}
              {%- assign acceptable_recommendations_count = acceptable_recommendations_count | plus: 1 -%}
            {%- endif -%}
          {%- endfor -%}

          {%- if recommendations.performed -%}
            {%- if acceptable_recommendations_count > 0 -%}
              <div class="mini-cart__recommendations-inner">
                {%- if section.settings.recommendations_title != blank -%}
                  <p class="mini-cart__recommendations-heading heading heading--small hidden-pocket">{{ section.settings.recommendations_title | escape }}</p>
                  <p class="mini-cart__recommendations-heading heading heading--xsmall text--subdued hidden-lap-and-up">{{ section.settings.recommendations_title | escape }}</p>
                {%- endif -%}

                <div class="scroller">
                  <div class="scroller__inner">
                    <div class="mini-cart__recommendations-list">
                      {%- assign shown_products_count = 0 -%}

                      {%- for product in recommendations.products -%}
                        {%- if shown_products_count >= 7 -%}
                          {%- break -%}
                        {%- endif -%}

                        {%- assign matching_product = cart.items | where: 'product_id', product.id -%}

                        {%- if matching_product.size == 0 -%}
                          {%- assign shown_products_count = shown_products_count | plus: 1 -%}
                          {% unless product.title contains 'Route' or product.metafields.custom.exclusive_access.value != blank %}
                            <div>{%- render 'product-item--upsell', product: product, reduced_content: true, reduced_font_size: true, hide_secondary_image: true, sizes_attribute: '(max-width: 740px) 65px, 92px' -%}</div>
                          {% endunless %}
                        {%- endif -%}
                      {%- endfor -%}
                    </div>
                  </div>
                </div>
              </div>
            {%- endif -%}
          {%- else -%}
            <div class="mini-cart__recommendations-inner">
              <div class="spinner">
                {%- render 'icon' with 'spinner', stroke_width: 3, width: 40, height: 40 -%}
              </div>
            </div>
          {%- endif -%}
        </cart-drawer-recommendations>
      
      </div>

      {%- endif -%}
    </div>

    <footer class="mini-cart__drawer-footer drawer__footer drawer__footer--tight drawer__footer--bordered">
      
      {%- if section.settings.enable_shipping_tax_note -%}
        {%- capture shipping_tax_note -%}{{ 'cart.general.shipping_tax_note' | t }}{%- endcapture -%}
      {%- endif -%}

      {%- if cart.cart_level_discount_applications != blank -%}
        <ul class="mini-cart__discount-list list--unstyled" role="list">
          {%- for discount_application in cart.cart_level_discount_applications -%}
            <li class="mini-cart__discount">
              <span class="mini-cart__discount-badge discount-badge">{%- render 'icon' with 'discount-badge' -%}{{ discount_application.title }}</span>
              <span class="mini-cart__discount-price text--xsmall text--subdued">-{{ discount_application.total_allocated_amount | money }}</span>
            </li>
          {%- endfor -%}
        </ul>
      {%- endif -%}

      {%- if section.settings.show_order_terms_checkbox == true -%}

        {% assign order_terms_enabled = true %}

        <cart-terms class="mini-cart__terms">
          
          <div class="note">
            <div class="note__content">
              <div class="mini-cart__text text--xsmall">
                {{ section.settings.order_terms_text }}
              </div>
              <div class="mini-cart__checkbox">
                <input id="cart-order-terms" type="checkbox" class="switch-checkbox" name="cart-order-terms" value="1">
                <label for="cart-order-terms" class="label">{{ section.settings.order_terms_label }}</label>
              </div>
            </div>
          </div>
          
        </cart-terms>
        
      {%- endif -%}

      {%- if section.settings.show_order_note or shipping_tax_note != '' -%}
        <div class="mini-cart__actions text--subdued text--xsmall">

          

          {%- if section.settings.gift_wrap_enable and section.settings.gift_wrap_product != blank -%}

            {% assign gift_wrap_in_cart = false %}
            {% for item in cart.items %}
              {% if item.product.id == gift_wrap_product.id %}
                {% assign gift_wrap_in_cart = true %}
                {% break %}
              {% endif %}
            {% endfor %}
            
            {% assign gift_wrap_disabled = false %}
            {% for item in cart.items %}
              {% if item.product.tags contains 'disable-gift-wrap' %}
                {% assign gift_wrap_disabled = true %}
                {% break %}
              {% endif %}
            {% endfor %}

            <cart-gift-wrap 
              class="mini-cart__gift-wrap" 
              data-giftwrap-disabled="{{ gift_wrap_disabled }}" 
              data-product-incart="{{ gift_wrap_in_cart }}" 
              data-product-id="{{ section.settings.gift_wrap_product.id }}" 
              data-variant-id="{{ section.settings.gift_wrap_product.variants[0].id }}" 
              {% if gift_wrap_disabled == true %}data-tooltip="{{ 'cart.general.gift_wrap.disabled_tooltip' | t }}"{% endif %}>

              <div class="checkbox-container">
                <input class="checkbox" type="checkbox" name="gift-wrap" id="gift-wrap" {% if gift_wrap_in_cart == true %}checked="checked"{% endif %} {% if gift_wrap_disabled == true %}disabled{% endif %}>
                <label for="gift-wrap">
                  {% if gift_wrap_disabled == true %}
                    {{ 'cart.general.gift_wrap.checkbox_title' | t }}
                  {% else %}
                    {{ 'cart.general.gift_wrap.checkbox_title' | t }} ({{ gift_wrap_product.price | money }})
                  {% endif %}
                </label>
              </div>

            </cart-gift-wrap>
        
          {%- endif -%}

          {%- capture gift_note_toggle -%}
            <button type="button" is="toggle-button" id="order-note-toggle" class="link" data-action="toggle-order-note" aria-controls="mini-cart-note" aria-expanded="false">
              {%- if cart.note == blank -%}
                {{- 'cart.general.add_order_note' | t -}}
              {%- else -%}
                {{- 'cart.general.edit_order_note' | t -}}
              {%- endif -%}
            </button>
          {%- endcapture -%}

          {%- if section.settings.show_order_note -%}
            {%- if section.settings.gift_wrap_note_enable == true -%}
              {%- if gift_wrap_in_cart == true -%}
                {{ gift_note_toggle }}
              {%- endif -%}
            {%- else -%}
              {{ gift_note_toggle }}
            {%- endif -%}
          {%- endif -%}

          {%- if shipping_tax_note != '' -%}
            <span>{{ shipping_tax_note }}</span>
          {%- endif -%}
        </div>
      {%- endif -%}
        <!-- Route Code Edited 7/9/2024 -->
          <div class="route-div" data-allow-multiple-renders data-route-update-cart-button="cart__view"></div>
          <style>
             .preferred-checkout-wrapper {
            flex-basis: 5% !important;
          }
            small {
              display: unset;
              margin-top: -10px !important;
          }
          </style>
          <!-- End Route Code -->

      {%- if section.settings.show_checkout_button -%}
        <button form="mini-cart-form" type="submit" class="checkout-button button button--primary button--full cart__view" name="checkout" {% if order_terms_enabled == true %}disabled{% endif %}>
          <span class="checkout-button__cart">{%- render 'icon' with 'custom-cart' -%}</span>

          {{- 'cart.general.checkout' | t -}}
          {{- cart.total_price | money_with_currency -}}
        </button>
      {%- else -%}
        <a href="{{ routes.cart_url }}" class="button button--primary button--full" data-no-instant>{{ 'cart.general.go_to_cart' | t }}</a>
      {%- endif -%}

      {%- if section.settings.checkout_button_message -%}
        <div class="checkout-button-message minor text-center">
          {{ section.settings.checkout_button_message }}
        </div>
      {%- endif -%}

      
    </footer>
  {%- endif -%}

  {%- capture order_note_openable -%}
    <openable-element id="mini-cart-note" class="mini-cart__order-note">
      <span class="openable__overlay"></span>
      <label for="cart[note]" class="mini-cart__order-note-title heading heading--xsmall">{{- 'cart.general.add_order_note' | t -}}</label>
      <textarea is="cart-note" name="note" id="cart[note]" rows="3" aria-owns="order-note-toggle" class="input__field input__field--textarea" placeholder="{{ 'cart.general.order_note_placeholder' | t }}">{{ cart.note }}</textarea>
      <button type="button" data-action="close" class="form__submit form__submit--closer button button--secondary">{{ 'cart.general.order_note_save' | t }}</button>
    </openable-element>
  {%- endcapture -%}

  {%- if section.settings.show_order_note -%}
    {%- if section.settings.show_order_note -%}
      {%- if section.settings.gift_wrap_note_enable == true -%}
        {%- if gift_wrap_in_cart == true -%}
          {{ order_note_openable }}
        {%- endif -%}
      {%- else -%}
        {{ order_note_openable }}
      {%- endif -%}
    {%- endif -%}
    
  {%- endif -%}

</cart-drawer>

{% schema %}
{
  "name": "Cart drawer",
  "class": "shopify-section--mini-cart",
  "blocks": [
    {
      "type": "product_based_gift",
      "name": "Gift - Product Based",
      "settings": [
        {
          "type": "paragraph",
          "content": "If there's a specific product in the cart, add the gift."
        },
        {
          "type": "text",
          "id": "title",
          "label": "Offer Name",
          "info": "This will show up on the gift in the cart or in the order.",
          "default": "Special Offer"
        },
        {
          "type": "header",
          "content": "Gift",
          "info": "The gift the customer will receive for this offer."
        },
        {
          "type": "product",
          "id": "gift_product",
          "label": "Gift Product",
          "info": "The product the customer will receive as a gift."
        },
        {
          "type": "text",
          "id": "gift_product_variant",
          "label": "Variant",
          "info": "A specific variant ID (optional). Sets the gift's variant. This has to be a valid variant ID of the product - copy this from the admin."
        },
        {
          "type": "header",
          "content": "Trigger",
          "info": "The conditions that will trigger this offer."
        },
        {
          "type": "product",
          "id": "trigger_product",
          "label": "Product",
          "info": "Trigger based on presence of this product in the cart."
        },
        {
          "type": "text",
          "id": "trigger_variant",
          "label": "Variant",
          "info": "A specific variant ID (optional). Sets the gift's variant. This has to be a valid variant ID of the product - copy this from the admin. (Will default to first available variant if not valid)"
        },
        {
          "type": "number",
          "id": "trigger_quantity",
          "label": "Quantity",
          "info": "How many items need to be added to qualify."
        },
        {
          "type": "header",
          "content": "Limit"
        },
        {
          "type": "number",
          "id": "limit",
          "placeholder": "5",
          "label": "Limit",
          "info": "Cap the number of gifts for this offer. (e.g. 5, which will only let the customer get 5 gifts for this offer.)"
        },
        {
          "type": "header",
          "content": "⛔️ Exclusions"
        },
        {
          "type": "text",
          "id": "gift_product_exclude_type",
          "label": "Exclude based on type",
          "placeholder": "Gift Cards",
          "info": "Products in the cart with this type don't count towards the value."
        },
        {
          "type": "text",
          "id": "gift_product_exclude_tag",
          "label": "Exclude based on tag",
          "placeholder": "Gift Cards",
          "info": "(⚠️ Not currently working) Products in the cart with this tag don't count towards the value."
        },
        {
          "type": "collection",
          "id": "gift_product_exclude_collection",
          "label": "Exclude based on collection",
          "info": "(⚠️ Not currently working) Products in the cart in this collection don't count towards the value."
        },
        {
          "type": "header",
          "content": "❌ Invalid Rules",
          "info": "These rules will invalidate the offer and remove all gift products from the cart."
        },
        {
          "type": "text",
          "id": "gift_product_invalid_type",
          "label": "Invalidate based on type",
          "placeholder": "Gift Cards",
          "info": "If the cart has a gift of this type, the offer is not valid, and remove all gift products from the cart."
        },
        {
          "type": "text",
          "id": "gift_product_invalid_tag",
          "label": "Invalidate based on tag",
          "placeholder": "Gift Cards",
          "info": "(⚠️ Not currently working) If the cart has a gift of with this tag, the offer is not valid, and remove all gift products from the cart."
        }
      ]
    },
    {
      "type": "cart_based_gift",
      "name": "Gift - Cart Value Based",
      "settings": [
        {
          "type": "paragraph",
          "content": "Set up a free gift offering based on cart value, or total value of certain product in the cart."
        },
        {
          "type": "text",
          "id": "title",
          "label": "Offer Name",
          "info": "This will show up on the gift in the cart or in the order.",
          "default": "Special Offer"
        },
        {
          "type": "header",
          "content": "Gift",
          "info": "The gift the customer will receive for this offer."
        },
        {
          "type": "product",
          "id": "gift_product",
          "label": "Gift Product",
          "info": "The product the customer will receive as a gift."
        },
        {
          "type": "text",
          "id": "gift_product_variant",
          "label": "Variant",
          "info": "A specific variant ID (optional). Sets the gift's variant. This has to be a valid variant ID of the product - copy this from the admin."
        },
        {
          "type": "header",
          "content": "Trigger",
          "info": "The conditions that will trigger this offer."
        },
        {
          "type": "number",
          "id": "trigger_value",
          "label": "Value (Required)",
          "info": "The cart value required to add the gift to the cart. (e.g. add the gift product when the value of the customer's cart is $200)"
        },
        {
          "type": "header",
          "content": "Limit"
        },
        {
          "type": "number",
          "id": "limit",
          "placeholder": "5",
          "label": "Limit",
          "info": "Cap the number of gifts for this offer. (e.g. 5, which will only let the customer get 5 gifts for this offer.)"
        },
        {
          "type": "header",
          "content": "⛔️ Exclusions"
        },
        {
          "type": "text",
          "id": "gift_product_exclude_type",
          "label": "Exclude based on type",
          "placeholder": "Gift Cards",
          "info": "Products in the cart with this type don't count towards the value."
        },
        {
          "type": "text",
          "id": "gift_product_exclude_tag",
          "label": "Exclude based on tag",
          "placeholder": "Gift Cards",
          "info": "(⚠️ Not currently working) Products in the cart with this tag don't count towards the value."
        },
        {
          "type": "collection",
          "id": "gift_product_exclude_collection",
          "label": "Exclude based on collection",
          "info": "(⚠️ Not currently working) Products in the cart in this collection don't count towards the value."
        },
        {
          "type": "header",
          "content": "❌ Invalid Rules",
          "info": "These rules will invalidate the offer and remove all gift products from the cart."
        },
        {
          "type": "text",
          "id": "gift_product_invalid_type",
          "label": "Invalidate based on type",
          "placeholder": "Gift Cards",
          "info": "If the cart has a gift of this type, the offer is not valid, and remove all gift products from the cart."
        },
        {
          "type": "text",
          "id": "gift_product_invalid_tag",
          "label": "Invalidate based on tag",
          "placeholder": "Gift Cards",
          "info": "(⚠️ Not currently working) If the cart has a gift of with this tag, the offer is not valid, and remove all gift products from the cart."
        }
      ]
    },
    {
      "type": "shipping_rate",
      "name": "Special Shipping Rate",
      "settings": [
        {
          "type": "header",
          "content": "Country Codes"
        },
        {
          "type": "text",
          "id": "country_codes",
          "label": "Country Codes",
          "placeholder": "US,CA,UK",
          "info": "To make this offer exclusive to a country, enter its country codes here. (e.g. US, CA, UK)"
        },
        {
          "type": "header",
          "content": "Rates"
        },
        {
          "type": "paragraph",
          "content": "The standard shipping threshold for this country is in the \"Free shipping minimum amount\" under Cart settings.)"
        },
        {
          "type": "number",
          "id": "rate_silver",
          "label": "Special Rate - Silver",
          "info": "Rate for customers tagged with \"tier: Silver\""
        },
        {
          "type": "number",
          "id": "rate_gold",
          "label": "Special Rate - Gold",
          "info": "Rate for customers tagged with \"tier: Gold\""
        }
      ]
    },
    {
      "type": "product_value_based_gift",
      "name": "Gift - Product Value",
      "settings": [
        {
          "type": "paragraph",
          "content": "If there's a specific product in the cart, add the gift."
        },
        {
          "type": "text",
          "id": "title",
          "label": "Offer Name",
          "info": "This will show up on the gift in the cart or in the order.",
          "default": "Special Offer"
        },
        {
          "type": "header",
          "content": "Gift",
          "info": "The gift the customer will receive for this offer."
        },
        {
          "type": "product",
          "id": "gift_product",
          "label": "Gift Product",
          "info": "The product the customer will receive as a gift."
        },
        {
          "type": "text",
          "id": "gift_product_variant",
          "label": "Variant",
          "info": "A specific variant ID (optional). Sets the gift's variant. This has to be a valid variant ID of the product - copy this from the admin."
        },
        {
          "type": "header",
          "content": "Trigger",
          "info": "The conditions that will trigger this offer."
        },
        {
          "type": "number",
          "id": "trigger_value",
          "label": "Value (Required)",
          "info": "The value of products required to add the gift to the cart. (e.g. $200 of Gift Card products)"
        },
        {
          "type": "paragraph",
          "content": "Choose to trigger based on the value of a product, product type, or product title in the cart. Fill in your choice and leave the other fields blank."
        },
        {
          "type": "paragraph",
          "content": "PRODUCT"
        },
        {
          "type": "product",
          "id": "trigger_product",
          "label": "Product",
          "info": "Trigger based on $X of this product in the cart."
        },
        {
          "type": "text",
          "id": "trigger_variant",
          "label": "Variant",
          "info": "A specific variant ID (optional). Sets the gift's variant. This has to be a valid variant ID of the product - copy this from the admin. (Will default to first available variant if not valid)"
        },
        {
          "type": "paragraph",
          "content": "PRODUCT TYPE"
        },
        {
          "type": "text",
          "id": "trigger_product_type",
          "label": "Product Type",
          "info": "Trigger based on $X of this product type in the cart."
        },
        {
          "type": "paragraph",
          "content": "PRODUCT TITLE"
        },
        {
          "type": "text",
          "id": "trigger_product_title_contains",
          "label": "Product Title",
          "info": "Trigger based on $X of a product with this title in the cart. (e.g. 'Gift Card')"
        },
        {
          "type": "header",
          "content": "Limit"
        },
        {
          "type": "number",
          "id": "limit",
          "placeholder": "5",
          "label": "Limit",
          "info": "Cap the number of gifts for this offer. (e.g. 5, which will only let the customer get 5 gifts for this offer.)"
        },
        {
          "type": "header",
          "content": "⛔️ Exclusions"
        },
        {
          "type": "text",
          "id": "gift_product_exclude_type",
          "label": "Exclude based on type",
          "placeholder": "Gift Cards",
          "info": "Products in the cart with this type don't count towards the value."
        },
        {
          "type": "text",
          "id": "gift_product_exclude_tag",
          "label": "Exclude based on tag",
          "placeholder": "Gift Cards",
          "info": "(⚠️ Not currently working) Products in the cart with this tag don't count towards the value."
        },
        {
          "type": "collection",
          "id": "gift_product_exclude_collection",
          "label": "Exclude based on collection",
          "info": "(⚠️ Not currently working) Products in the cart in this collection don't count towards the value."
        },
        {
          "type": "header",
          "content": "❌ Invalid Rules",
          "info": "These rules will invalidate the offer and remove all gift products from the cart."
        },
        {
          "type": "text",
          "id": "gift_product_invalid_type",
          "label": "Invalidate based on type",
          "placeholder": "Gift Cards",
          "info": "If the cart has a gift of this type, the offer is not valid, and remove all gift products from the cart."
        },
        {
          "type": "text",
          "id": "gift_product_invalid_tag",
          "label": "Invalidate based on tag",
          "placeholder": "Gift Cards",
          "info": "(⚠️ Not currently working) If the cart has a gift of with this tag, the offer is not valid, and remove all gift products from the cart."
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "header",
      "content": "Free Shipping Notice"
    },
    {
      "type": "paragraph",
      "content": "Free shipping notice can be configured in global cart settings."
    },
    {
      "type": "header",
      "content": "Cart Notice"
    },
    {
      "type": "checkbox",
      "id": "cart_notice_enable",
      "label": "Enable"
    },
    {
      "type": "richtext",
      "id": "cart_notice_message",
      "label": "Text"
    },
    {
      "type": "select",
      "id": "cart_notice_style",
      "label": "Style",
      "options": [
        {
          "value": "tiny",
          "label": "Text"
        },
        {
          "value": "tiny note",
          "label": "Note"
        },
        {
          "value": "tiny note note--danger",
          "label": "Note - Danger"
        }
      ],
      "default": "tiny"
    },
    {
      "type": "select",
      "id": "cart_notice_text_align",
      "label": "Text alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        }
      ],
      "default": "center"
    },
    {
      "type": "select",
      "id": "cart_notice_icon",
      "label": "Icon",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "picto-coupon",
          "label": "Coupon",
          "group": "Shop"
        },
        {
          "value": "picto-gift",
          "label": "Gift",
          "group": "Shop"
        },
        {
          "value": "picto-taxes",
          "label": "Taxes",
          "group": "Shop"
        },
        {
          "value": "picto-warranty",
          "label": "Warranty",
          "group": "Shop"
        },
        {
          "value": "picto-like",
          "label": "Like",
          "group": "Shop"
        },
        {
          "value": "picto-store",
          "label": "Store",
          "group": "Shop"
        },
        {
          "value": "picto-store-pickup",
          "label": "Store pickup",
          "group": "Shop"
        },
        {
          "value": "picto-love",
          "label": "Love",
          "group": "Shop"
        },
        {
          "value": "picto-donation",
          "label": "Donation",
          "group": "Shop"
        },
        {
          "value": "picto-box",
          "label": "Box",
          "group": "Shipping"
        },
        {
          "value": "picto-address",
          "label": "Address",
          "group": "Shipping"
        },
        {
          "value": "picto-address-pin",
          "label": "Address pin",
          "group": "Shipping"
        },
        {
          "value": "picto-fast-delivery",
          "label": "Fast delivery",
          "group": "Shipping"
        },
        {
          "value": "picto-delivery-truck",
          "label": "Delivery truck",
          "group": "Shipping"
        },
        {
          "value": "picto-return-box",
          "label": "Returns",
          "group": "Shipping"
        },
        {
          "value": "picto-worldwide",
          "label": "World",
          "group": "Shipping"
        },
        {
          "value": "picto-plane",
          "label": "Plane",
          "group": "Shipping"
        },
        {
          "value": "picto-credit-card",
          "label": "Credit card",
          "group": "Payment & Security"
        },
        {
          "value": "picto-lock",
          "label": "Lock",
          "group": "Payment & Security"
        },
        {
          "value": "picto-shield",
          "label": "Shield",
          "group": "Payment & Security"
        },
        {
          "value": "picto-secure-payment",
          "label": "Secure payment",
          "group": "Payment & Security"
        },
        {
          "value": "picto-mobile",
          "label": "Mobile",
          "group": "Communication"
        },
        {
          "value": "picto-phone",
          "label": "Phone",
          "group": "Communication"
        },
        {
          "value": "picto-chat",
          "label": "Chat",
          "group": "Communication"
        },
        {
          "value": "picto-send",
          "label": "Send",
          "group": "Communication"
        },
        {
          "value": "picto-email",
          "label": "Email",
          "group": "Communication"
        },
        {
          "value": "picto-customer-support",
          "label": "Customer support",
          "group": "Communication"
        },
        {
          "value": "picto-operator",
          "label": "Support operator",
          "group": "Communication"
        },
        {
          "value": "picto-virus",
          "label": "Virus",
          "group": "Health"
        },
        {
          "value": "picto-mask",
          "label": "Mask",
          "group": "Health"
        },
        {
          "value": "custom-rewards",
          "label": "Rewards",
          "group": "Kyte Baby"
        },
        {
          "value": "custom-shipping",
          "label": "Shipping",
          "group": "Kyte Baby"
        },
        {
          "value": "custom-returns",
          "label": "Returns",
          "group": "Kyte Baby"
        },
        {
          "value": "custom-support",
          "label": "Support",
          "group": "Kyte Baby"
        }
      ],
      "default": ""
    },
    {
      "type": "image_picker",
      "id": "cart_notice_custom_icon",
      "label": "Custom icon",
      "info": "100 x 100px .png with transparency recommended"
    },
    {
      "type": "header",
      "content": "Order Terms Checkbox"
    },
    {
      "type": "checkbox",
      "id": "show_order_terms_checkbox",
      "label": "Show order terms checkbox",
      "default": false
    },
    {
      "type": "text",
      "id": "order_terms_label",
      "label": "Checkbox Label",
      "default": "I understand and accept these terms."
    },
    {
      "type": "richtext",
      "id": "order_terms_text",
      "label": "Order Terms Text",
      "default": "<p>Due to the high volume of orders, shipping for sale orders could take up to 4 WEEKS. Regular Priced orders will still ship within 3-7 Business days. ALL sale items purchased during the sale are FINAL. No Free or VIP Shipping will be available during the duration of the sale.</p>"
    },
    {
      "type": "header",
      "content": "Cart Settings"
    },
    {
      "type": "checkbox",
      "id": "show_order_note",
      "label": "Show order note",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_checkout_button",
      "label": "Show checkout button",
      "default": true
    },
    {
      "type": "url",
      "id": "empty_button_link",
      "label": "Empty button link",
      "default": "/collections/all"
    },
    {
      "type": "checkbox",
      "id": "enable_shipping_tax_note",
      "label": "Enable Shipping/Tax Note",
      "default": false
    },
    {
      "type": "header",
      "content": "Gifting"
    },
    {
      "type": "checkbox",
      "id": "gift_wrap_enable",
      "label": "Enable Gift Wrap",
      "default": true
    },
    {
      "type": "product",
      "id": "gift_wrap_product",
      "label": "Gift Wrap Product"
    },
    {
      "type": "checkbox",
      "id": "gift_wrap_note_enable",
      "label": "Only show gift note if gift wrap is added",
      "default": true
    },
    {
      "type": "header",
      "content": "Cross-sell",
      "info": "Dynamic recommendations are based on the items in your cart. They change and improve with time. [Learn more](https://help.shopify.com/en/themes/development/recommended-products)"
    },
    {
      "type": "checkbox",
      "id": "show_recommendations",
      "label": "Show cart recommendations",
      "default": true
    },
    {
      "type": "header",
      "content": "Product recommendations"
    },
    {
      "type": "select",
      "id": "recommendations_mode",
      "label": "Recommendations",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "theme",
          "label": "Theme"
        },
        {
          "value": "related",
          "label": "Related"
        },
        {
          "value": "complementary",
          "label": "Complementary"
        },
        {
          "value": "combined",
          "label": "Combined (Complementary + Related)"
        }
      ],
      "default": "",
      "info": "Choose if the recommendations shown in the cart. Auto modes pull results from Shopify Search & Discovery. Default is pulled from section settings."
    },
    {
      "type": "paragraph",
      "content": "Note: Setting to an \"Auto\" mode will show results from Shopify Search & Discovery. \"Combined\" will show complementary products first, then related products"
    },
    {
      "type": "text",
      "id": "recommendations_title",
      "label": "Heading",
      "default": "Trending this month"
    },
    {
      "type": "product_list",
      "id": "recommendations_products",
      "label": "Recommendations",
      "info": "(Only works if recommendations mode set to Theme) Suggest additional products to your customers. Recommendations already in the cart are automatically hidden.",
      "limit": 10
    }
  ]
}
{% endschema %}