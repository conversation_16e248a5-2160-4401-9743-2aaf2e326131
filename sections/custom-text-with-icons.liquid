{%- if section.settings.background != settings.background and section.settings.background != 'rgba(0,0,0,0)' -%}
  {%- assign blends_with_background = false -%}
{%- else -%}
  {%- assign blends_with_background = true -%}
{%- endif -%}

{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
  CSS
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}

<style>
  #shopify-section-{{ section.id }} {
    {%- if section.settings.background == 'rgba(0,0,0,0)' -%}
      {%- assign section_background = settings.background -%}
    {%- else -%}
      {%- assign section_background = section.settings.background -%}
    {%- endif -%}

    {%- if section.settings.text_color == 'rgba(0,0,0,0)' -%}
      {%- assign heading_color = settings.heading_color -%}
      {%- assign text_color = settings.text_color -%}
    {%- else -%}
      {%- assign heading_color = section.settings.text_color -%}
      {%- assign text_color = section.settings.text_color -%}
    {%- endif -%}

    {%- if section.settings.icon_color == 'rgba(0,0,0,0)' -%}
      {%- assign icon_color = settings.text_color -%}
    {%- else -%}
      {%- assign icon_color = section.settings.icon_color -%}
    {%- endif -%}

    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
    --icon-color: {{ icon_color.red }}, {{ icon_color.green }}, {{ icon_color.blue }};
    --section-background: {{ section_background.red }}, {{ section_background.green }}, {{ section_background.blue }};
    --vertical-breather: 40px; /* Inner spacing is smaller on this section */
  }

  #shopify-section-{{ section.id }} .heading {
    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
  }

  #shopify-section-{{ section.id }} .image-overlay__text-container {
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
  }
  
</style>

{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
  JavaScript: this section composes several custom elements
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}

{%- if section.settings.override_blend == true -%}
  {%- assign blends_with_background = false -%}
{%- endif -%}

{%- capture section_classes -%}
  {% render 'section-classes', section: section %}
{%- endcapture -%}

<section class="section {{ section_classes }} custom-text-with-icons {% unless blends_with_background or is_boxed %}section--flush{% endunless %}">

  {% if section.settings.extra_code != blank %}
    {{ section.settings.extra_code }}
  {% endif %}

  {%- if section.settings.anchor -%}
    <a class="visually-hidden anchor" id="{{ section.settings.anchor }}" name="{{ section.settings.anchor }}"></a>
  {%- endif -%}
  
  <div class="section__color-wrapper">
    <div class="container {% unless blends_with_background %}vertical-breather{% endunless %}">
      <native-carousel class="text-with-icons">
        <div class="text-with-icons__list hide-scrollbar">
          {%- for block in section.blocks -%}
            <native-carousel-item {% unless forloop.first %}hidden{% endunless %} id="block-{{ section.id }}-{{ block.id }}" class="text-with-icons__item" {{ block.shopify_attributes }}>
              <div class="text-with-icons__icon-wrapper">
                {%- if block.settings.custom_icon != blank -%}
                  {% capture custom_icon_width %}{{ block.settings.custom_icon_width | times: 2 }}x{% endcapture %}
                  <img class="text-with-icons__custom-icon" style="max-width: {{ block.settings.custom_icon_width }}px" width="{{ block.settings.custom_icon.width }}" height="{{ block.settings.custom_icon.height }}" src="{{ block.settings.custom_icon | img_url: custom_icon_width }}" alt="{{ block.settings.custom_icon.alt | escape }}">
                {%- else -%}
                  {%- render 'icon' with block.settings.icon -%}
                {%- endif -%}
              </div>

              {%- if block.settings.title != blank or block.settings.content != blank -%}
                <div class="text-with-icons__content-wrapper">
                  {%- if block.settings.title != blank -%}
                    <p class="heading heading--small">{{ block.settings.title | escape }}</p>
                  {%- endif -%}

                  {%- if block.settings.content != blank -%}
                    <div class="text-with-icons__description">{{- block.settings.content -}}</div>
                  {%- endif -%}
                </div>
              {%- endif -%}
            </native-carousel-item>
          {%- endfor -%}
        </div>

        {%- if section.blocks.size > 1 -%}
          <page-dots class="text-with-icons__dots dots-nav dots-nav--centered hidden-lap-and-up">
            {%- for block in section.blocks -%}
              <button class="dots-nav__item tap-area" {% if forloop.first %}aria-current="true"{% endif%}>
                <span class="visually-hidden">{{ 'general.accessibility.go_to_slide' | t: num: forloop.index }}</span>
              </button>
            {%- endfor -%}
          </page-dots>
        {%- endif -%}
      </native-carousel>
    </div>
  </div>
</section>

{% schema %}
{
  "name": "🪁 Text with icons",
  "class": "shopify-section--text-with-icons",
  "disabled_on": {
    "groups": ["header", "custom.overlay"]
  },
  "max_blocks": 4,
  "blocks": [
    {
      "type": "item",
      "name": "Item",
      "settings": [
        {
          "type": "select",
          "id": "icon",
          "label": "Icon",
          "options": [
            {
              "value": "picto-coupon",
              "label": "Coupon",
              "group": "Shop"
            },
            {
              "value": "picto-gift",
              "label": "Gift",
              "group": "Shop"
            },
            {
              "value": "picto-taxes",
              "label": "Taxes",
              "group": "Shop"
            },
            {
              "value": "picto-warranty",
              "label": "Warranty",
              "group": "Shop"
            },
            {
              "value": "picto-like",
              "label": "Like",
              "group": "Shop"
            },
            {
              "value": "picto-store",
              "label": "Store",
              "group": "Shop"
            },
            {
              "value": "picto-store-pickup",
              "label": "Store pickup",
              "group": "Shop"
            },
            {
              "value": "picto-love",
              "label": "Love",
              "group": "Shop"
            },
            {
              "value": "picto-donation",
              "label": "Donation",
              "group": "Shop"
            },
            {
              "value": "picto-box",
              "label": "Box",
              "group": "Shipping"
            },
            {
              "value": "picto-address",
              "label": "Address",
              "group": "Shipping"
            },
            {
              "value": "picto-address-pin",
              "label": "Address pin",
              "group": "Shipping"
            },
            {
              "value": "picto-fast-delivery",
              "label": "Fast delivery",
              "group": "Shipping"
            },
            {
              "value": "picto-delivery-truck",
              "label": "Delivery truck",
              "group": "Shipping"
            },
            {
              "value": "picto-return-box",
              "label": "Returns",
              "group": "Shipping"
            },
            {
              "value": "picto-worldwide",
              "label": "World",
              "group": "Shipping"
            },
            {
              "value": "picto-plane",
              "label": "Plane",
              "group": "Shipping"
            },
            {
              "value": "picto-credit-card",
              "label": "Credit card",
              "group": "Payment & Security"
            },
            {
              "value": "picto-lock",
              "label": "Lock",
              "group": "Payment & Security"
            },
            {
              "value": "picto-shield",
              "label": "Shield",
              "group": "Payment & Security"
            },
            {
              "value": "picto-secure-payment",
              "label": "Secure payment",
              "group": "Payment & Security"
            },
            {
              "value": "picto-mobile",
              "label": "Mobile",
              "group": "Communication"
            },
            {
              "value": "picto-phone",
              "label": "Phone",
              "group": "Communication"
            },
            {
              "value": "picto-chat",
              "label": "Chat",
              "group": "Communication"
            },
            {
              "value": "picto-send",
              "label": "Send",
              "group": "Communication"
            },
            {
              "value": "picto-email",
              "label": "Email",
              "group": "Communication"
            },
            {
              "value": "picto-customer-support",
              "label": "Customer support",
              "group": "Communication"
            },
            {
              "value": "picto-operator",
              "label": "Support operator",
              "group": "Communication"
            },
            {
              "value": "picto-virus",
              "label": "Virus",
              "group": "Health"
            },
            {
              "value": "picto-mask",
              "label": "Mask",
              "group": "Health"
            },
            {
              "value": "custom-rewards",
              "label": "Rewards",
              "group": "Kyte Baby"
            },
            {
              "value": "custom-shipping",
              "label": "Shipping",
              "group": "Kyte Baby"
            },
            {
              "value": "custom-returns",
              "label": "Returns",
              "group": "Kyte Baby"
            },
            {
              "value": "custom-support",
              "label": "Support",
              "group": "Kyte Baby"
            }
          ],
          "default": "picto-customer-support"
        },
        {
          "type": "image_picker",
          "id": "custom_icon",
          "label": "Custom icon",
          "info": "100 x 100px .png with transparency recommended"
        },
        {
          "type": "range",
          "id": "custom_icon_width",
          "min": 40,
          "max": 200,
          "step": 5,
          "unit": "px",
          "label": "Custom icon width",
          "info": "Only impact custom icons",
          "default": 50
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Your title"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Content",
          "default": "<p>Short content about your store</p>"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "header",
      "content": "🪁 Kyte Settings"
    },
    {
      "type": "text",
      "id": "styling_class",
      "label": "Styling Class",
      "info": "For advanced use."
    },
    {
      "type": "text",
      "id": "anchor",
      "label": "Anchor",
      "info": "Anchor name used for jump links.",
      "placeholder": "#anchor"
    },
    {
      "type": "checkbox",
      "id": "use_padding",
      "label": "Use Padding"
    },
    {
      "type": "checkbox",
      "id": "box_shadow",
      "label": "Shadow"
    },
    {
      "type": "checkbox",
      "id": "section_extra_padding",
      "label": "Extra Vertical Padding"
    },
    {
      "type": "checkbox",
      "id": "override_blend",
      "label": "Stop section merging with previous section",
      "default": false,
      "info": "If the section \"merges\" with the previous and next sections, this separates them again."
    },
    {
      "type": "liquid",
      "id": "extra_code",
      "label": "Extra Code",
      "info": "Misc. Liquid code for embellishments."
    },
    {
      "type": "header",
      "content": "Section Settings"
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "icon_color",
      "label": "Icon color",
      "default": "rgba(0,0,0,0)"
    }
  ],
  "presets": [
    {
      "name": "🪁 Text with icons",
      "blocks": [
        {
          "type": "item",
          "settings": {
            "icon": "picto-box",
            "title": "Free shipping",
            "content": "<p>Free worldwide shipping and returns - customs and duties taxes included</p>"
          }
        },
        {
          "type": "item",
          "settings": {
            "icon": "picto-phone",
            "title": "Customer service",
            "content": "<p>We are available from monday to friday to answer your questions.</p>"
          }
        },
        {
          "type": "item",
          "settings": {
            "icon": "picto-lock",
            "title": "Secure payment",
            "content": "<p>Your payment information is processed securely.</p>"
          }
        },
        {
          "type": "item",
          "settings": {
            "icon": "picto-email",
            "title": "Contact us",
            "content": "<p>Need to contact us ? Just send us an e-<NAME_EMAIL></p>"
          }
        }
      ]
    }
  ]
}
{% endschema %}