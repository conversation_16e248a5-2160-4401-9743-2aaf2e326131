
{% if section.settings.top_menu_bar_menu != blank %}

<style>

  :root {

    --enable-sticky-menu-bar: {% if section.settings.top_menu_bar_position == 'non_sticky' or section.settings.top_menu_bar_position == 'sticky_desktop' %}0{% else %}1{% endif %};

    {% comment %}
    --tabbed-header-color-background-1: {{ settings.tabbed_header_background_1.rgb }};
    --tabbed-header-color-background-2: {{ settings.tabbed_header_background_2.rgb }};
    --tabbed-header-color-logo: {{ settings.tabbed_header_logo_color.rgb }};
    --tabbed-header-color-icons: {{ settings.tabbed_header_icon_color.rgb }};
    --tabbed-header-color-border: {{ settings.tabbed_header_border_color.rgb }};
    --tabbed-header-color-button: var(--sub-brand--button-color--rgb, {{ settings.tabbed_header_button_color.rgb }});
    --tabbed-header-color-link: {{ settings.tabbed_header_link_color.rgb }};
    --tabbed-header-color-heading: var(--sub-brand--heading-color--rgb, {{ settings.tabbed_header_heading_color.rgb }});
    --tabbed-header-color-text: var(--sub-brand--text-color--rgb, {{ settings.tabbed_header_text_color.rgb }});
    --tabbed-header-color-text--hover: {{ settings.tabbed_header_text_hover_color.rgb }};
    --tabbed-header-color-bubble: {{ settings.tabbed_header_bubble_color.rgb }};
    {% endcomment %}

  }

  #shopify-section-{{ section.id }} {
    
    {% if section.settings.text_color != blank and section.settings.text_color != 'rgba(0,0,0,0)' -%}
      --heading-color: {{ section.settings.text_color.red }}, {{ section.settings.text_color.green }}, {{ section.settings.text_color.blue }};
    {%- else -%}
      --heading-color: var(--tabbed-header-color-text);
    {%- endif -%}

    {% if section.settings.link_color != blank and section.settings.link_color != 'rgba(0,0,0,0)' -%}
      --link-color: {{ section.settings.link_color.red }}, {{ section.settings.link_color.green }}, {{ section.settings.link_color.blue }};
    {%- else -%}
      --link-color: var(--tabbed-header-color-link);
    {%- endif -%}

    {% if section.settings.text_color != blank and section.settings.text_color != 'rgba(0,0,0,0)' -%}
      --text-color: {{ section.settings.text_color.red }}, {{ section.settings.text_color.green }}, {{ section.settings.text_color.blue }};
    {%- else -%}
      --text-color: var(--tabbed-header-color-text);
    {%- endif -%}

    {% if section.settings.logo_color != blank and section.settings.logo_color != 'rgba(0,0,0,0)' -%}
      --logo-color: {{ section.settings.logo_color.red }}, {{ section.settings.logo_color.green }}, {{ section.settings.logo_color.blue }};
    {%- else -%}
      --logo-color: var(--tabbed-header-color-logo);
    {%- endif -%}

    {% if section.settings.icon_color != blank and section.settings.icon_color != 'rgba(0,0,0,0)' -%}
      --icon-color: {{ section.settings.icon_color.red }}, {{ section.settings.icon_color.green }}, {{ section.settings.icon_color.blue }};
    {%- else -%}
      --icon-color: var(--tabbed-header-color-icon);
    {%- endif -%}

    {% if section.settings.border_color != blank and section.settings.border_color != 'rgba(0,0,0,0)' -%}
      --border-color: {{ section.settings.border_color.red }}, {{ section.settings.border_color.green }}, {{ section.settings.border_color.blue }};
    {%- else -%}
      --border-color: var(--tabbed-header-color-border);
    {%- endif -%}

    {% if section.settings.background != blank and section.settings.background != 'rgba(0,0,0,0)' -%}
      --background-color: {{ section.settings.background.red }}, {{ section.settings.background.green }}, {{ section.settings.background.blue }};
    {%- else -%}
      --background-color: var(--tabbed-header-color-background-1);
    {%- endif -%}

    {% if section.settings.background_2 != blank and section.settings.background_2 != 'rgba(0,0,0,0)' -%}
      --background-color-2: {{ section.settings.background_2.red }}, {{ section.settings.background_2.green }}, {{ section.settings.background_2.blue }};
      --section-background: {{ section.settings.background_2.red }}, {{ section.settings.background_2.green }}, {{ section.settings.background_2.blue }};
    {%- else -%}
      --background-color-2: var(--tabbed-header-color-background-2);
      --section-background: var(--tabbed-header-color-background-2);
    {%- endif -%}
    
    z-index: 5; /* Make sure it goes over header */

    {%- if section.settings.top_menu_bar_position == 'sticky' or section.settings.top_menu_bar_position == 'sticky_mobile' -%}
      position: -webkit-sticky;
      position: sticky;
    {%- else -%}
      position: relative;
    {%- endif -%}

    top: 0;
    
  }

  @media screen and (min-width: 741px) {
    :root {
      --enable-sticky-menu-bar: {% if section.settings.top_menu_bar_position == 'non_sticky' or section.settings.top_menu_bar_position == 'sticky_mobile' %}0{% else %}1{% endif %};
    }

    #shopify-section-{{ section.id }} {
      {%- if section.settings.top_menu_bar_position == 'sticky' or section.settings.top_menu_bar_position == 'sticky_desktop' -%}
        position: -webkit-sticky;
        position: sticky;
      {%- else -%}
        position: relative;
      {%- endif -%}
    }
  }
</style>

<section>

  <top-menu-bar class="top-menu-bar tabbed-top-menu-bar section__color-wrapper">

    <div class="container">

      <div class="top-menu-bar__inner">

        {%- capture logo_tabs -%}

          {%- liquid

            assign sub_brands = metaobjects['sub_brand'].values
            
            for sub_brand in sub_brands
              
              # Hides this sub-brand in the menu if it's not active.
              if settings.subbrands_hide_inactive == true and sub_brand.active != true
                continue
              endif

              if request.page_type == "page"
                if page.metafields.sub_brand.sub_brand
                  assign current_sub_brand = page.metafields.sub_brand.sub_brand.value
                endif
              elsif request.page_type == "collection"
                if collection.metafields.sub_brand.sub_brand
                  assign current_sub_brand = collection.metafields.sub_brand.sub_brand.value
                endif
              elsif request.page_type == "product"
                if collection.metafields.sub_brand.sub_brand
                  assign current_sub_brand = collection.metafields.sub_brand.sub_brand.value
                elsif product.metafields.sub_brand.sub_brand
                  assign current_sub_brand = product.metafields.sub_brand.sub_brand.value
                endif
              endif
              if sub_brand == current_sub_brand
                assign active_sub_brand = sub_brand
                break
              endif
            endfor

            if active_sub_brand != blank
              assign main_logo_active = false
            else
              assign main_logo_active = true
            endif

          -%}
          
          {%
            render 'tabbed-header-logo',
            title: shop.name,
            link: routes.root_url,
            logo_image: settings.logo_image,
            logo_svg: settings.logo_svg,
            active: main_logo_active
          %}

          {%- for sub_brand in sub_brands -%}

            {%- assign active = false -%}
            {% if active_sub_brand == sub_brand %}
              {%- assign active = true -%}
            {% endif %}

            {%- render 'tabbed-header-logo', sub_brand: sub_brand, active: active -%}

          {%- endfor -%}

        {%- endcapture -%}

        {%- if logo_tabs != blank -%}
          <ul class="top-menu-bar__sub-brand-logos" role="list">
            {{ logo_tabs }}
          </ul>
        {%- endif -%}

        {%- capture info_links -%}
          {%- assign top_menu_bar_menu = section.settings.top_menu_bar_menu -%}
          {%- for link in top_menu_bar_menu.links -%}

            {%- case link.title -%}
              {%- when 'Rewards' -%}
                {%- assign link_url = settings.page_rewards_landing -%}
              {%- else -%}
                {%- assign link_url = link.url -%}
            {%- endcase -%}

            <li class="header__linklist-item" data-item-title="{{ link.title | escape }}">
              <a class="header__linklist-link" {% render 'link-attributes', link: link %} href="{{ link_url }}" {% if link.links.size > 0 or mega_menu_block != '' %}aria-controls="desktop-menu-{{ forloop.index }}" aria-expanded="false"{% endif %}>
                
                {%- capture icon -%}
                  {%- assign icon_handle = link.title | handle | strip -%}
                  {%- render 'tabbed-header-icon', icon: icon_handle -%}
                {%- endcapture -%}

                {%- if icon != '' -%}
                  <span class="header__linklist-link-icon">
                    {{ icon }}
                  </span>
                {%- endif -%}

                <span class="header__linklist-link-text text--xs">
                  <span class="link--animated">{{- link.title -}}</span>
                  {%- if link.title == "Rewards" and section.settings.show_rewards_points == true and customer -%}
                    <span class="text--subdued">
                      (<span data-lion-points></span> Points)
                    </span>
                  {%- endif -%}
                </span>

              </a>
            </li>
          {%- endfor -%}
        {%- endcapture -%}

        {%- if info_links != blank -%}
          <ul class="top-menu-bar__info-links header__linklist list--unstyled" role="list">
            {{ info_links }}
          </ul>
        {%- endif -%}

      </div>

    </div>

  </top-menu-bar>

</section>

<script>
  document.documentElement.style.setProperty('--top-menu-bar-height', document.getElementById('shopify-section-{{ section.id }}').clientHeight + 'px');
</script>

{% endif %}

{% schema %}
{
  "name": "Top Menu Bar",
  "class": "shopify-section--top-menu-bar hidden-pocket hidden-lap",
  "settings": [
    {
      "type": "header",
      "content": "Rewards"
    },
    {
      "type": "checkbox",
      "id": "show_rewards_points",
      "label": "Show rewards points in Rewards link",
      "default": false
    },
    {
      "type": "header",
      "content": "Icons",
      "info": "Settings for showing icons on links."
    },
    {
      "type": "checkbox",
      "id": "show_icons",
      "label": "Show icons",
      "default": true,
      "info": "Note: Icons will show for links that we have defined icons for. Icons are hard-coded into the theme code. If the link text matches the icon's name exactly, an icon will show."
    },
    {
      "type": "header",
      "content": "Icons Supported",
      "info": "Wishlist, Reviews, Gifts, Rewards, Registry, Blog, Support"
    },
    {
      "type": "header",
      "content": "Sub Brand",
      "info": "Settings for sub brand links. (e.g. Kyte Living)"
    },
    {
      "type": "header",
      "content": "Top Bar Menu"
    },
    {
      "type": "link_list",
      "id": "top_menu_bar_menu",
      "label": "Menu"
    },
    {
      "type": "select",
      "id": "top_menu_bar_position",
      "label": "Position",
      "options": [
        {
          "value": "non_sticky",
          "label": "Non sticky"
        },
        {
          "value": "sticky_desktop",
          "label": "Sticky on desktop only"
        },
        {
          "value": "sticky_mobile",
          "label": "Sticky on mobile only"
        },
        {
          "value": "sticky",
          "label": "Sticky everywhere"
        }
      ],
      "default": "non_sticky"
    },
    {
      "type": "header",
      "content": "Colors",
      "info": "Overrides the colors under \"🪁 Tabbed Header\" theme settings."
    },
    {
      "type": "color",
      "id": "background_2",
      "label": "Background",
      "info": "Bar background"
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background",
      "info": "Active sub-brand / logo"
    },
    {
      "type": "color",
      "id": "link_color",
      "label": "Links",
      "info": "Info links"
    },
    {
      "type": "color",
      "id": "logo_color",
      "label": "Logo",
      "info": "Logo color"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Headings"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text"
    },
    {
      "type": "color",
      "id": "icon_color",
      "label": "Icons"
    },
    {
      "type": "color",
      "id": "border_color",
      "label": "Borders"
    }
  ],
  "default": {}
}
{% endschema %}