{%- assign offers_total = 0 -%}
{%- for block in section.blocks -%}
  {%- assign offer = block.settings -%}
  {%- if block.type == "cart_based" and offer.trigger_value != blank and offer.gift_product != blank -%}
    {%- assign offers_total = offers_total | plus: 1 -%}
  {%- endif -%}
{%- endfor -%}

{%- capture free_gift_settings_json -%}
  {
    "offers": [
      {% assign offer_index = 1 %}
      {%- for block in section.blocks -%}
        {%- assign offer = block.settings -%}
        {% if block.type == "cart_based" and offer.trigger_value != blank and offer.gift_product != blank %}
          {
            "type": "{{ block.type }}",
            {% if offer.limit %}
              "limit": {{ offer.limit | times: 100 }},
            {% endif %}
            "trigger": {
              "value": {{ offer.trigger_value }},
              {% if offer.trigger_product != blank %}
              "product": {
                "id": {{ offer.trigger_product.id }},
                "handle": "{{ offer.trigger_product }}",
                "title": "{{ offer.trigger_product.title }}",
                "price": {{ offer.trigger_product.price }}
              }
              {% endif %}
            },
            "gift": {
              {% if offer.gift_product_variant %}
              "variant": {{ offer.gift_product_variant }},
              {% else %}
              "variant": {{ offer.gift_product.variants[0] }},
              {% endif %}
              "product": {
                "id": {{ offer.gift_product.id }},
                "handle": "{{ offer.gift_product }}",
                "title": "{{ offer.gift_product.title }}",
                "price": {{ offer.gift_product.price }}
              }
            }
          }{% unless offer_index == offers_total %},{% endunless %}
          {% assign offer_index = offer_index | plus: 1 %}
        {% endif %}
      {%- endfor -%}
    ]
  }
{%- endcapture -%}

<free-gift-settings data-settings="{{ free_gift_settings_json }}"></free-gift-settings>

{% schema %}
{
  "name": "Free Gift Settings",
  "settings": [],
  "blocks": [
    {
      "type": "cart_based",
      "name": "Cart-Based Gift",
      "settings": [
        {
          "type": "paragraph",
          "content": "Set up a free gift offering based on cart value, or total value of certain product in the cart."
        },
        {
          "type": "header",
          "content": "Gift",
          "info": "The gift the customer will receive for this offer."
        },
        {
          "type": "product",
          "id": "gift_product",
          "label": "Gift Product",
          "info": "The product the customer will receive as a gift."
        },
        {
          "type": "number",
          "id": "gift_product_variant",
          "label": "Variant",
          "info": "A specific variant ID (optional). Sets the gift's variant. This has to be a valid variant ID of the product - copy this from the admin."
        },
        {
          "type": "header",
          "content": "Trigger",
          "info": "The conditions that will trigger this offer."
        },
        {
          "type": "text",
          "id": "trigger_value",
          "label": "Value (Required)",
          "placeholder": "US:100,CAD:135",
          "info": "The cart value required to add the gift to the cart. (e.g. add the gift product when the value of the customer's cart is $200)"
        },
        {
          "type": "paragraph",
          "content": "Use the locale format to assign country-specific amounts, separated by commas: USD:100,CAD:135"
        },
        {
          "type": "product",
          "id": "trigger_product",
          "label": "Based on Product",
          "info": "If you want the value to be based on the value of the total value of a certain product in the cart, select that product here."
        },
        {
          "type": "header",
          "content": "Limit"
        },
        {
          "type": "number",
          "id": "limit",
          "placeholder": "5",
          "label": "Limit",
          "info": "Cap the number of gifts for this offer. (e.g. 5, which will only let the customer get 5 gifts for this offer.)"
        }
      ]
    }
  ]
}
{% endschema %}
