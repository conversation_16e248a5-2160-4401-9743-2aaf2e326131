  <style>
    [aria-controls="newsletter-popup"] {
      display: block; /* Allows to show the toggle icon in the header if the section is disabled */
    }
  </style>

  {%- if section.settings.image != blank -%}
    <style>
      @media screen and (max-width: 999px) {
        .modal__close-button {
          color: rgb(255, 255, 255);
        }
      }
    </style>
  {%- endif -%}

  {%- assign should_appear_automatically = false -%}

  {%- unless section.settings.show_only_on_index and request.page_type != 'index' -%}
    {%- unless section.settings.show_only_for_visitors and customer -%}
      {%- assign should_appear_automatically = true -%}
    {%- endunless -%}
  {%- endunless -%}

<modal-content section="{{ section.id }}" {% if section.settings.show_only_once %}only-once{% endif %} {% if should_appear_automatically %}apparition-delay="{{ section.settings.apparition_delay }}"{% endif %} id="newsletter-popup" class="modal">
  <div class="modal__overlay"></div>

  <div class="modal__content section__color-wrapper">
    <button type="button" class="modal__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
      {%- render 'icon' with 'close' -%}
    </button>

    <div class="newsletter-modal {% if section.settings.image_position == 'right' %}newsletter-modal--reverse{% endif %}">
      
    {%- if section.settings.image != blank -%}
      {{- section.settings.image | image_url: width: section.settings.image.width | image_tag: loading: 'lazy', sizes: '(max-width: 740px) 100vw, 500px', widths: '300,400,500,600,700,800,900,1000', class: 'newsletter-modal__image' -}}
    {%- endif -%}

      <div class="newsletter-modal__content {% if section.settings.image != blank %}newsletter-modal__content--extra{% endif %} text-container text--center">

        {%- if section.settings.image_content != blank -%}
          <div class="newsletter-modal__image-content-wrapper hidden-pocket">
            <img class="newsletter-modal__image-content" loading="lazy" sizes="(max-width: 740px) 100vw, 500px" {% render 'image-attributes', image: section.settings.image_content, sizes: '300,400,500,600,700,800,900,1000' %}>
          </div>
        {%- endif -%}
          
        {%- if section.settings.title != blank -%}
          <h2 class="heading h3">{{ section.settings.title | escape }}</h2>
        {%- endif -%}

        {%- if section.settings.content != blank -%}
          {{- section.settings.content -}}
        {%- endif -%}

        {%- form 'customer', id: newsletter_id, class: 'form newsletter-modal__form' -%}
          {%- if form.posted_successfully? -%}
            <script>
              window.addEventListener('DOMContentLoaded', () => {
                document.getElementById('newsletter-popup').open = true;
              });
            </script>

            <div class="form__banner banner banner--success">
              <span class="banner__ribbon">{% render 'icon' with 'form-success' %}</span>
              <p class="banner__content">{{ 'general.newsletter.success' | t }}</p>
            </div>
          {%- else -%}
          {%- if form.errors -%}
            <div class="form__banner banner banner--error">
              <span class="banner__ribbon">{% render 'icon' with 'form-error' %}</span>
              <p class="banner__content">{{ form.errors.messages['email'] }}</p>
            </div>
          {%- endif -%}

          <input type="hidden" name="contact[tags]" value="newsletter">
          <input type="hidden" name="contact[context]" value="{{ newsletter_id }}">

            <div class="input">
              <input type="email" id="newsletter[{{ section.id }}][contact][email]" name="contact[email]" class="input__field" required>
              <label for="newsletter[{{ section.id }}][contact][email]" class="input__label">{{ 'general.newsletter.email' | t }}</label>
            </div>

            <div class="input">
              <button type="submit" is="loader-button" class="button button--primary button--small button--full">
                <span class="button__text">
                  {% if section.settings.button_text != blank %}
                    {{ section.settings.button_text }}
                  {% else %}
                    {{ 'general.newsletter.subscribe' | t }}
                  {% endif %}
                </span>
                <span class="button__icon">{%- include 'icon' with 'nav-arrow-right', block: true, direction_aware: true -%}</span>
              </button>
            </div>
          {%- endif -%}
        {%- endform -%}

        {%- if section.settings.terms != blank -%}
          <div class="tiny">{{- section.settings.terms -}}</div>
        {%- endif -%}

      </div>
    </div>
  </div>
</modal-content>

{% schema %}
{
  "name": "Popup",
  "class": "shopify-section--popup",
  "settings": [
    {
      "type": "paragraph",
      "content": "Customers who subscribe will have their email address added to the \"accepts marketing\" [customer list](/admin/customers?query=&accepts_marketing=1)."
    },
    {
      "type": "range",
      "id": "apparition_delay",
      "min": 0,
      "max": 15,
      "step": 1,
      "unit": "sec",
      "label": "Delay until the popup appears",
      "default": 5
    },
    {
      "type": "checkbox",
      "id": "show_only_on_index",
      "label": "Show only on home page",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_only_for_visitors",
      "label": "Disable for account holders",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_only_once",
      "label": "Show once to visitors",
      "default": true
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image",
      "info": "1000 x 1000px .jpg recommended"
    },
    {
      "type": "image_picker",
      "id": "image_content",
      "label": "Content Image"
    },
    {
      "type": "select",
      "id": "image_position",
      "label": "Image position",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "left"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Get 10% off"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button Text",
      "default": "Unlock 10% Off"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "Text",
      "default": "<p>Promotions, new products and sales. Directly to your inbox.</p>"
    },
    {
      "type": "richtext",
      "id": "terms",
      "label": "Terms",
      "default": "<p>By submitting this form, you agree to receive recurring promotional and personalized email marketing messages from Kyte Baby. You can unsubscribe at any time. View our Privacy Policy for more details.</p>"
    }
  ]
}
{% endschema %}