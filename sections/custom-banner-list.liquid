{%- if section.settings.background != settings.background and section.settings.background != 'rgba(0,0,0,0)' -%}
  {%- assign blends_with_background = false -%}
{%- else -%}
  {%- assign blends_with_background = true -%}
{%- endif -%}

<style>
  #shopify-section-{{ section.id }} {
    {%- if section.settings.background == 'rgba(0,0,0,0)' -%}
      {%- assign section_background = settings.background -%}
    {%- else -%}
      {%- assign section_background = section.settings.background -%}
    {%- endif -%}

    {%- if section.settings.text_color == 'rgba(0,0,0,0)' -%}
      {%- assign heading_color = settings.heading_color -%}
      {%- assign text_color = settings.text_color -%}
    {%- else -%}
      {%- assign heading_color = section.settings.text_color -%}
      {%- assign text_color = section.settings.text_color -%}
    {%- endif -%}

    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
    --prev-next-button-background: var(--text-color);
    --prev-next-button-color: var(--section-background);

    --section-background: {{ section_background.red }}, {{ section_background.green }}, {{ section_background.blue }};

    {%- if section.settings.layout == 'collage' -%}
      --section-collage-column: {{ section.blocks.size | at_most: 2 }};
    {%- endif -%}
  }

  {%- for block in section.blocks -%}
    #block-{{ section.id }}-{{ block.id }} {
      {% if block.settings.text_color == 'rgba(0,0,0,0)' %}
        {%- assign text_color_rgb = '255, 255, 255' -%}
      {%- else -%}
        {%- capture text_color_rgb -%}{{ block.settings.text_color.red }}, {{ block.settings.text_color.green }}, {{ block.settings.text_color.blue }}{%- endcapture -%}
      {%- endif -%}

      --heading-color: {{ text_color_rgb }};
      --text-color: {{ text_color_rgb }};
      --section-block-overlay: {{ block.settings.overlay_color.red }}, {{ block.settings.overlay_color.green }}, {{ block.settings.overlay_color.blue }};
      --section-block-overlay-opacity: {{ block.settings.overlay_opacity | divided_by: 100.0 }};
    }
  {%- endfor -%}
</style>

{%- if section.settings.override_blend == true -%}
  {%- assign blends_with_background = false -%}
{%- endif -%}

{%- capture section_classes -%}
  {% render 'section-classes', section: section %}
{%- endcapture -%}

<section class="section {{ section_classes }} section__color-wrapper {% unless blends_with_background %}section--flush{% endunless %}">

  {% if section.settings.extra_code != blank %}
    {{ section.settings.extra_code }}
  {% endif %}

  {%- if section.settings.anchor -%}
    <a class="visually-hidden anchor" id="{{ section.settings.anchor }}" name="{{ section.settings.anchor }}"></a>
  {%- endif -%}

  <div class="">
    <div class="{% unless blends_with_background %}vertical-breather{% endunless %}">

      {%- capture header_content -%}
        
        {%- if section.settings.subheading != blank -%}
          <h2 class="{% if section.settings.subheading_style contains 'heading' %}heading{% endif %} {{ section.settings.subheading_style }}">{{ section.settings.subheading | escape }}</h2>
        {%- endif -%}

        {%- if section.settings.title != blank -%}
          <h3 class="heading {{ section.settings.heading_style }}">{{ section.settings.title | escape }}</h3>
        {%- endif -%}

        {%- if section.settings.intro_text != blank -%}
          <div class="{{ section.settings.intro_text_style }}">
            {{- section.settings.intro_text -}}
          </div>
        {%- endif -%}

        {%- if section.settings.content != blank -%}
          <div class="{{ section.settings.content_style }}">
            {{- section.settings.content -}}
          </div>
        {%- endif -%}

      {%- endcapture -%}
      
      {%- if header_content != blank -%}
        <header class="section__header container text-container">
          {{ header_content }}
        </header>
      {%- endif -%}

      {%- capture section_content -%}
        <div class="list-collections__item-list">
          {%- for block in section.blocks -%}
            {%- capture collection_content -%}
              {%- if block.settings.subheading != blank -%}
                <p class="heading heading--small" {% if section.settings.reveal_on_scroll %}reveal{% endif %}>{{ block.settings.subheading | escape }}</p>
              {%- endif -%}

              {%- if block.settings.title != blank -%}
                <p class="heading {% if settings.heading_text_transform == 'uppercase' %}h4{% else %}h3{% endif %}" {% if section.settings.reveal_on_scroll %}reveal{% endif %}>{{ block.settings.title | escape }}</p>
              {%- endif -%}

              {%- if block.settings.link_text != blank -%}
                <span class="heading heading--small link" {% if section.settings.reveal_on_scroll %}reveal{% endif %}>{{ block.settings.link_text | escape }}</span>
              {%- endif -%}
            {%- endcapture -%}

            {%- if section.settings.layout == 'collage' -%}
              {%- comment -%}
              The logic is as follows:
                - If we have a modulo 3 (3, 6, 9...) we highlight every modulo 3, and every 6 is shifted
                - If we have a modulo 5 (5, 10, 15...) we highlight every modulo 5, and every 10 is shifted
                - If we have a modulo 7 (7, 14, 21...) we highlight every modulo 7, and every 14 is shifted
              {%- endcomment -%}

              {%- assign is_highlighted = false -%}
              {%- assign is_shifted = true -%}
              {%- assign modulo_3 = section.blocks.size | modulo: 3 -%}
              {%- assign modulo_5 = section.blocks.size | modulo: 5 -%}
              {%- assign modulo_7 = section.blocks.size | modulo: 7 -%}

              {%- if modulo_3 == 0 -%}
                {%- assign index_modulo_3 = forloop.index | modulo: 3 -%}
                {%- assign index_modulo_6 = forloop.index | modulo: 6 -%}

                {%- if index_modulo_3 == 1 -%}
                  {%- assign is_highlighted = true -%}
                {%- endif -%}

                {%- if index_modulo_6 == 1 -%}
                  {%- assign is_shifted = false -%}
                {%- endif -%}
              {%- elsif modulo_5 == 0 -%}
                {%- assign index_modulo_5 = forloop.index | modulo: 5 -%}
                {%- assign index_modulo_10 = forloop.index | modulo: 10 -%}

                {%- if index_modulo_5 == 1 -%}
                  {%- assign is_highlighted = true -%}
                {%- endif -%}

                {%- if index_modulo_10 == 1 -%}
                  {%- assign is_shifted = false -%}
                {%- endif -%}
              {%- elsif modulo_7 == 0 -%}
                {%- assign index_modulo_7 = forloop.index | modulo: 7 -%}
                {%- assign index_modulo_14 = forloop.index | modulo: 14 -%}

                {%- if index_modulo_7 == 1 -%}
                  {%- assign is_highlighted = true -%}
                {%- endif -%}

                {%- if index_modulo_14 == 1 -%}
                  {%- assign is_shifted = false -%}
                {%- endif -%}
              {%- endif -%}
            {%- endif -%}

            {%- assign collection = block.settings.collection -%}

            <a id="block-{{ section.id }}-{{ block.id }}" href="{{ block.settings.link_url | default: collection.url }}" class="list-collections__item {% if is_highlighted %}list-collections__item--highlight {% if is_shifted %}list-collections__item--shift{% endif %}{% endif %} {% if collection_content != blank %}has-overlay{% endif %} image-zoom" {{ block.shopify_attributes }}>
              <div class="list-collections__item-image-wrapper">
                {%- assign collection_image = block.settings.image | default: collection.featured_image -%}

                {%- if collection_image != blank -%}
                  {%- case section.settings.layout -%}
                    {%- when 'grid' -%}
                      {%- capture image_sizes -%}(max-width: 740px) calc(100vw - 48px), (max-width: 999px) calc(50vw - 60px), 480px{%- endcapture -%}

                    {%- when 'carousel' -%}
                      {%- capture image_sizes -%}(max-width: 740px) 80vw, (max-width: 999px) 60vw, 425px{%- endcapture -%}

                    {%- when 'collage' -%}
                      {%- capture image_sizes -%}(max-width: 740px) calc(100vw - 24px * 2), 660px{%- endcapture -%}
                  {%- endcase -%}

                  {%- if section.settings.layout == 'grid' and collection_image.aspect_ratio > 2.5 -%}
                    {%- assign height_constraint = 800 | at_most: collection_image.height -%}

                    {%- if section.settings.reveal_on_scroll -%}
                      {{- collection_image | image_url: width: collection_image.width, height: height_constraint, crop: 'center' | image_tag: loading: 'lazy', sizes: image_sizes, widths: '200,300,400,500,600,700,800,900,1000,1100,1200,1300,1400', reveal: true, class: 'list-collections__item-image' -}}
                    {%- else -%}
                      {{- collection_image | image_url: width: collection_image.width, height: height_constraint, crop: 'center' | image_tag: loading: 'lazy', sizes: image_sizes, widths: '200,300,400,500,600,700,800,900,1000,1100,1200,1300,1400', class: 'list-collections__item-image' -}}
                    {%- endif -%}
                  {%- else -%}
                    {%- if section.settings.reveal_on_scroll -%}
                      {{- collection_image | image_url: width: collection_image.width | image_tag: loading: 'lazy', sizes: image_sizes, widths: '200,300,400,500,600,700,800,900,1000,1100,1200,1300,1400', reveal: true, class: 'list-collections__item-image' -}}
                    {%- else -%}
                      {{- collection_image | image_url: width: collection_image.width | image_tag: loading: 'lazy', sizes: image_sizes, widths: '200,300,400,500,600,700,800,900,1000,1100,1200,1300,1400', class: 'list-collections__item-image' -}}
                    {%- endif -%}
                  {%- endif -%}
                {%- else -%}
                  {%- capture collection_image -%}collection-{% cycle '1', '2', '3', '4', '5', '6' %}{%- endcapture -%}

                  {%- if section.settings.reveal_on_scroll -%}
                    {{- collection_image | placeholder_svg_tag: 'list-collections__item-image placeholder-background' | replace: '<svg', '<svg reveal' -}}
                  {%- else -%}
                    {{- collection_image | placeholder_svg_tag: 'list-collections__item-image placeholder-background' -}}
                  {%- endif -%}
                {%- endif -%}
              </div>

              {%- if collection_content != blank -%}
                <div class="list-collections__item-info text-container">
                  {{- collection_content -}}
                </div>
              {%- endif -%}
            </a>
          {%- endfor -%}
        </div>
      {%- endcapture -%}

      {%- if section_content != blank -%}

        <div class="section__content">

          {%- if section.settings.layout == 'grid' or section.settings.layout == 'collage' -%}
            <collection-list {% if section.settings.reveal_on_scroll %}reveal-on-scroll{% endif %} class="list-collections list-collections--{{ section.settings.layout }}">
              <div class="container">
                {{- section_content -}}
              </div>
            </collection-list>
          {%- elsif section.settings.layout == 'carousel' -%}
            <collection-list scrollable {% if section.settings.reveal_on_scroll %}reveal-on-scroll{% endif %} class="list-collections list-collections--carousel">
              <scrollable-content class="list-collections__scroller hide-scrollbar">
                {{- section_content -}}
              </scrollable-content>
  
              <prev-next-buttons class="list-collections__prev-next prev-next-buttons hidden-pocket">
                <button class="list-collections__arrow prev-next-button prev-next-button--prev">
                  <span class="visually-hidden">{{ 'general.accessibility.previous' | t }}</span>
                  {%- render 'icon' with 'nav-arrow-left', block: true, direction_aware: true -%}
                </button>
  
                <button class="list-collections__arrow prev-next-button prev-next-button--next">
                  <span class="visually-hidden">{{ 'general.accessibility.next' | t }}</span>
                  {%- render 'icon' with 'nav-arrow-right', block: true, direction_aware: true -%}
                </button>
              </prev-next-buttons>
            </collection-list>
          {%- endif -%}

        </div>
        
      {%- endif -%}

      {%- capture footer_content -%}
        {%- if section.settings.button_text != blank -%}
          <div class="button-wrapper">
            <a href="{{ section.settings.button_link }}" class="button {{ section.settings.button_style }} {{ section.settings.button_size }}">
              <span class="button__text">{{ section.settings.button_text | escape }}</span>
              {% if block.settings.button_icon != "" %}
                <span class="button__icon">{%- include 'icon' with section.settings.button_icon, direction_aware: true -%}</span>
              {% endif %}
            </a>
          </div>
        {%- endif -%}

        {%- if section.settings.buttons_text != blank -%}
          <div class="{{ section.settings.buttons_text_style }}">
            {{ section.settings.buttons_text }}
          </div>
        {%- endif -%}        
      {%- endcapture -%}

      {%- if footer_content != blank -%}
        <div class="section__footer container text-container">
          {{ footer_content }}
        </div>
      {%- endif -%}

    </div>
  </div>
</section>

{% schema %}
{
  "name": "🪁 Banner list",
  "class": "shopify-section--banner-list",
  "disabled_on": {
    "templates": ["password"],
    "groups": ["header", "custom.overlay"]
  },
  "blocks": [
    {
      "name": "Banner",
      "type": "banner",
      "settings": [
        {
          "type": "url",
          "id": "link",
          "label": "Link"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "Grid and carousel modes: 1100 x 1400px .jpg recommended / Collage mode: 1320 x 1480px .jpg recommended (highlighted image) and 1320 x 700px .jpg recommended (normal image)"
        },
        {
          "type": "text",
          "id": "subheading",
          "label": "Subheading"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading"
        },
        {
          "type": "text",
          "id": "link_text",
          "label": "Link text"
        },
        {
          "type": "url",
          "id": "link_url",
          "label": "Link URL",
          "info": "If none is set, collection URL is used."
        },
        {
          "type": "header",
          "content": "Colors"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "overlay_color",
          "label": "Overlay",
          "default": "#000000"
        },
        {
          "type": "range",
          "id": "overlay_opacity",
          "label": "Overlay opacity",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "%",
          "default": 30
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "header",
      "content": "🪁 Kyte Settings"
    },
    {
      "type": "checkbox",
      "id": "use_padding",
      "label": "Use Padding"
    },
    {
      "type": "checkbox",
      "id": "box_shadow",
      "label": "Shadow"
    },
    {
      "type": "checkbox",
      "id": "section_extra_padding",
      "label": "Extra Vertical Padding"
    },
    {
      "type": "header",
      "content": "Section Settings"
    },
    {
      "type": "checkbox",
      "id": "reveal_on_scroll",
      "label": "Reveal on scroll",
      "info": "Show animation when section becomes visible.",
      "default": false
    },
    {
      "type": "select",
      "id": "layout",
      "label": "Layout",
      "info": "Collage mode only affects desktop, and will adapt based on number of items.",
      "options": [
        {
          "value": "grid",
          "label": "Grid"
        },
        {
          "value": "collage",
          "label": "Collage"
        },
        {
          "value": "carousel",
          "label": "Carousel"
        }
      ],
      "default": "grid"
    },
    {
      "type": "header",
      "content": "Subheading",
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "Text",
      "default": "Subheading"
    },
    {
      "type": "select",
      "id": "subheading_style",
      "label": "Style",
      "options": [
        {
          "group": "Subheading",
          "value": "",
          "label": "Default"
        },
        {
          "group": "Subheading",
          "value": "heading--small",
          "label": "Subheading"
        },
        {
          "group": "Subheading",
          "value": "heading--xsmall",
          "label": "Subheading - Small"
        },
        {
          "group": "Text",
          "value": "text--xxsmall",
          "label": "Tiny"
        },
        {
          "group": "Text",
          "value": "text--xsmall",
          "label": "Extra Small"
        },
        {
          "group": "Text",
          "value": "text--small",
          "label": "Small"
        },
        {
          "group": "Text",
          "value": "text--large",
          "label": "Large"
        }
      ],
      "default": "heading--small"
    },
    {
      "type": "header",
      "content": "Heading",
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Talk about your brand"
    },
    {
      "type": "select",
      "id": "heading_style",
      "label": "Style",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "group": "Subheading",
          "value": "heading--small",
          "label": "Subheading"
        },
        {
          "group": "Subheading",
          "value": "heading--xsmall",
          "label": "Subheading - Small"
        },
        {
          "group": "Heading",
          "value": "h1",
          "label": "Heading 1"
        },
        {
          "group": "Heading",
          "value": "h2",
          "label": "Heading 2"
        },
        {
          "group": "Heading",
          "value": "h3",
          "label": "Heading 3"
        },
        {
          "group": "Heading",
          "value": "h4",
          "label": "Heading 4"
        },
        {
          "group": "Heading",
          "value": "h5",
          "label": "Heading 5"
        },
        {
          "group": "Heading",
          "value": "h6",
          "label": "Heading 6"
        }
      ]
    },
    {
      "type": "header",
      "content": "Intro Text",
    },
    {
      "type": "richtext",
      "id": "intro_text",
      "label": "Text"
    },
    {
      "type": "select",
      "id": "intro_text_style",
      "label": "Style",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "group": "Text",
          "value": "text--xxsmall",
          "label": "Tiny"
        },
        {
          "group": "Text",
          "value": "text--xsmall",
          "label": "Extra Small"
        },
        {
          "group": "Text",
          "value": "text--small",
          "label": "Small"
        },
        {
          "group": "Text",
          "value": "text--large",
          "label": "Large"
        }
      ]
    },
    {
      "type": "header",
      "content": "Content",
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "Text",
      "default": "<p>Use this text to share information about your brand with your customers. Describe a product, share announcements, or welcome customers to your store.</p>"
    },
    {
      "type": "select",
      "id": "content_style",
      "label": "Style",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "group": "Text",
          "value": "text--xxsmall",
          "label": "Tiny"
        },
        {
          "group": "Text",
          "value": "text--xsmall",
          "label": "Extra Small"
        },
        {
          "group": "Text",
          "value": "text--small",
          "label": "Small"
        },
        {
          "group": "Text",
          "value": "text--large",
          "label": "Large"
        }
      ],
      "default": ""
    },
    {
      "type": "header",
      "content": "Text Below Buttons",
    },
    {
      "type": "richtext",
      "id": "buttons_text",
      "label": "Text"
    },
    {
      "type": "select",
      "id": "buttons_text_style",
      "label": "Style",
      "options": [
        {
          "group": "Subheading",
          "value": "",
          "label": "Default"
        },
        {
          "group": "Subheading",
          "value": "heading--small",
          "label": "Subheading"
        },
        {
          "group": "Subheading",
          "value": "heading--xsmall",
          "label": "Subheading - Small"
        },
        {
          "group": "Text",
          "value": "text--xxsmall",
          "label": "Tiny"
        },
        {
          "group": "Text",
          "value": "text--xsmall",
          "label": "Extra Small"
        },
        {
          "group": "Text",
          "value": "text--small",
          "label": "Small"
        },
        {
          "group": "Text",
          "value": "text--large",
          "label": "Large"
        }
      ],
      "default": "heading--small"
    },
    {
      "type": "header",
      "content": "Button",
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    },
    {
      "type": "select",
      "id": "button_size",
      "label": "Button size",
      "options": [
        {
          "value": "button--small",
          "label": "Small"
        },
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "button--large",
          "label": "Large"
        }
      ],
      "default": ""
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "Button style",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "button--primary",
          "label": "Primary"
        },
        {
          "value": "button--secondary",
          "label": "Secondary"
        }
      ],
      "default": ""
    },
    {
      "type": "select",
      "id": "button_icon",
      "label": "Button Icon",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "nav-arrow-right",
          "label": "Nav Arrow Right",
          "group": "Navigation"
        },
        {
          "value": "nav-arrow-right-small",
          "label": "Nav Arrow Right - Small",
          "group": "Navigation"
        }
      ]
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "header",
      "content": "🪁 Advanced"
    },
    {
      "type": "text",
      "id": "styling_class",
      "label": "Styling Class",
      "info": "For advanced use."
    },
    {
      "type": "text",
      "id": "anchor",
      "label": "Anchor",
      "info": "Anchor name used for jump links.",
      "placeholder": "#anchor"
    },
    {
      "type": "checkbox",
      "id": "contain",
      "label": "Contain and clip content",
      "default": false,
      "info": "Check this if this section has a scrolling text animation or botanical illustrations."
    },
    {
      "type": "checkbox",
      "id": "override_blend",
      "label": "Stop section merging with previous section",
      "default": false,
      "info": "If the section \"merges\" with the previous and next sections, this separates them again."
    },
    {
      "type": "liquid",
      "id": "extra_code",
      "label": "Extra Code",
      "info": "Misc. Liquid code for embellishments."
    }
  ],
  "presets": [
    {
      "name": "🪁 Banner list",
      "blocks": [
        {
          "type": "banner"
        },
        {
          "type": "banner"
        },
        {
          "type": "banner"
        },
        {
          "type": "banner"
        },
        {
          "type": "banner"
        },
        {
          "type": "banner"
        }
      ]
    }
  ]
}
{% endschema %}