{% comment %} 
--------------------------------------------------------------------------------
Sale Stop
--------------------------------------------------------------------------------
{% endcomment %}

{%- if collection.metafields.custom.sale_stop != blank -%}
  {%- assign sale_stop = collection.metafields.custom.sale_stop.value -%}
{%- elsif product.metafields.custom.sale_stop != blank -%}
  {%- assign sale_stop = product.metafields.custom.sale_stop.value -%}
{%- endif -%}

<style>
  :root {
    {% if section.settings.show_sticky_add_to_cart == false %}
      --enable-sticky-product-form: 0;
      --sticky-product-form-spacer: 0;
    {% else %}
      --enable-sticky-product-form: 1;
      --sticky-product-form-spacer: 20px;
    {% endif %};
  }
  #shopify-section-{{ section.id }} {
    {%- assign buy_buttons_block = section.blocks | where: 'type', 'buy_buttons' | first -%}

    {%- if buy_buttons_block.settings.show_payment_button -%}
      {%- if buy_buttons_block.settings.atc_button_background == 'rgba(0,0,0,0)' -%}
        {%- assign secondary_button_background = settings.secondary_button_background -%}
      {%- else -%}
        {%- assign secondary_button_background = buy_buttons_block.settings.atc_button_background -%}
      {%- endif -%}

      {%- if buy_buttons_block.settings.atc_button_text_color == 'rgba(0,0,0,0)' -%}
        {%- assign secondary_button_text_color = settings.secondary_button_text_color -%}
      {%- else -%}
        {%- assign secondary_button_text_color = buy_buttons_block.settings.atc_button_text_color -%}
      {%- endif -%}

      {%- if buy_buttons_block.settings.buy_now_button_background == 'rgba(0,0,0,0)' -%}
        {%- assign primary_button_background = settings.primary_button_background -%}
      {%- else -%}
        {%- assign primary_button_background = buy_buttons_block.settings.buy_now_button_background -%}
      {%- endif -%}

      {%- if buy_buttons_block.settings.buy_now_button_text_color == 'rgba(0,0,0,0)' -%}
        {%- assign primary_button_text_color = settings.primary_button_text_color -%}
      {%- else -%}
        {%- assign primary_button_text_color = buy_buttons_block.settings.buy_now_button_text_color -%}
      {%- endif -%}
    {%- else -%}
      {%- if buy_buttons_block.settings.atc_button_background == 'rgba(0,0,0,0)' -%}
        {%- assign primary_button_background = settings.primary_button_background -%}
      {%- else -%}
        {%- assign primary_button_background = buy_buttons_block.settings.atc_button_background -%}
      {%- endif -%}

      {%- if buy_buttons_block.settings.atc_button_text_color == 'rgba(0,0,0,0)' -%}
        {%- assign primary_button_text_color = settings.primary_button_text_color -%}
      {%- else -%}
        {%- assign primary_button_text_color = buy_buttons_block.settings.atc_button_text_color -%}
      {%- endif -%}
    {%- endif -%}

    {% comment %} --primary-button-background: {{ primary_button_background.red }}, {{ primary_button_background.green }}, {{ primary_button_background.blue }}; {% endcomment %}
    {% comment %} --primary-button-text-color: {{ primary_button_text_color.red }}, {{ primary_button_text_color.green }}, {{ primary_button_text_color.blue }}; {% endcomment %}
    {% comment %} --secondary-button-background: {{ secondary_button_background.red }}, {{ secondary_button_background.green }}, {{ secondary_button_background.blue }}; {% endcomment %}
    {% comment %} --secondary-button-text-color: {{ secondary_button_text_color.red }}, {{ secondary_button_text_color.green }}, {{ secondary_button_text_color.blue }}; {% endcomment %}
  }
</style>

{%- capture product_form_id -%}product-form-main-{{ product.id }}-{{ section.id }}{%- endcapture -%} 

<section>

  {%- if section.settings.anchor -%}
    <a class="visually-hidden anchor" id="{{ section.settings.anchor }}" name="{{ section.settings.anchor }}"></a>
  {%- endif -%}

  {%- if section.settings.show_sticky_add_to_cart and product.available -%}
    {%- render 'product-sticky-form--custom', product: product, product_form_id: product_form_id -%}
  {%- endif -%}

  <div class="container">

    {%- if section.settings.show_breadcrumbs -%}
      <nav aria-label="{{ 'general.breadcrumb.title' | t }}" class="breadcrumb text--xsmall text--subdued hidden-phone">
        <ol class="breadcrumb__list" role="list">
          <li class="breadcrumb__item">
            <a class="breadcrumb__link" href="{{ routes.root_url }}">{{ 'general.breadcrumb.home' | t }}</a>
          </li>
  
          {%- if collection -%}
            <li class="breadcrumb__item">
              <a class="breadcrumb__link" href="{{ collection.url }}">{{- collection.title -}}</a>
            </li>
          {%- endif -%}
  
          <li class="breadcrumb__item">
            <span class="breadcrumb__link" aria-current="page">{{ product.title }}</span>
          </li>
        </ol>
      </nav>
    {%- endif -%}

    <!-- PRODUCT TOP PART -->

    <product-rerender id="product-info-{{ product.id }}-{{ section.id }}" observe-form="{{ product_form_id }}" allow-partial-rerender>
      <div class="product product--thumbnails-{{ section.settings.desktop_thumbnails_position }}">
        {%- render 'product-media', product: product, product_form_id: product_form_id, sale_stop: sale_stop -%}
        {%- render 'product-media--tiled', product: product, product_form_id: product_form_id, sale_stop: sale_stop -%}
        {%- render 'product-info', product: product, product_form_id: product_form_id, sale_stop: sale_stop, update_url: true -%}
      </div>
    </product-rerender>
  </div>
</section>


{%- comment -%}
The quick shop HTML being very different, we render it here. On mobile and tablet/desktop, the product renders also
quite differently, as it is in a drawer on tablet/desktop, and a popover on mobile.
{%- endcomment -%}

{%- render 'product-quick-buy', product: product -%}

{% schema %}
{
  "name": "Product page",
  "class": "shopify-section--main-product",
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "strip_banner",
      "name": "Strip Banner",
      "settings": [
        {
          "type": "header",
          "content": "Content"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title"
        },
        {
          "type": "select",
          "id": "title_size",
          "label": "Title size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "h1",
              "label": "Heading 1",
              "group": "Headings"
            },
            {
              "value": "h2",
              "label": "Heading 2",
              "group": "Headings"
            },
            {
              "value": "h3",
              "label": "Heading 3",
              "group": "Headings"
            },
            {
              "value": "h4",
              "label": "Heading 4",
              "group": "Headings"
            },
            {
              "value": "h5",
              "label": "Heading 5",
              "group": "Headings"
            },
            {
              "value": "heading--small",
              "label": "Subheading",
              "group": "Subheadings"
            },
            {
              "value": "heading--xsmall",
              "label": "Subheading - Small",
              "group": "Subheadings"
            },
            {
              "value": "text",
              "label": "Default",
              "group": "Body Text"
            },
            {
              "value": "text--xsmall",
              "label": "Tiny",
              "group": "Body Text"
            },
            {
              "value": "text--small",
              "label": "Small",
              "group": "Body Text"
            },
            {
              "value": "text--large",
              "label": "Large",
              "group": "Body Text"
            }
          ],
          "default": "h5"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text"
        },
        {
          "type": "select",
          "id": "text_size",
          "label": "Text size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "h1",
              "label": "Heading 1",
              "group": "Headings"
            },
            {
              "value": "h2",
              "label": "Heading 2",
              "group": "Headings"
            },
            {
              "value": "h3",
              "label": "Heading 3",
              "group": "Headings"
            },
            {
              "value": "h4",
              "label": "Heading 4",
              "group": "Headings"
            },
            {
              "value": "h5",
              "label": "Heading 5",
              "group": "Headings"
            },
            {
              "value": "heading--small",
              "label": "Subheading",
              "group": "Subheadings"
            },
            {
              "value": "heading--xsmall",
              "label": "Subheading - Small",
              "group": "Subheadings"
            },
            {
              "value": "text",
              "label": "Default",
              "group": "Body Text"
            },
            {
              "value": "text--xsmall",
              "label": "Tiny",
              "group": "Body Text"
            },
            {
              "value": "text--small",
              "label": "Small",
              "group": "Body Text"
            },
            {
              "value": "text--large",
              "label": "Large",
              "group": "Body Text"
            }
          ],
          "default": "text--xsmall"
        },
        {
          "type": "select",
          "id": "text_alignment",
          "label": "Text alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "center"
        },
        {
          "type": "header",
          "content": "Banner Style"
        },
        {
          "type": "color",
          "id": "banner_color",
          "label": "Banner color",
          "default": "#E3DED2"
        },
        {
          "type": "select",
          "id": "banner_size",
          "label": "Banner size",
          "options": [
            {
              "value": "",
              "label": "Default"
            },
            {
              "value": "small",
              "label": "Small"
            }
          ],
          "default": ""
        },
        
      ]
    },
    {
      "type": "accordion",
      "name": "Accordion",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title"
        },
        {
          "type": "select",
          "id": "title_style",
          "label": "Title style",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "h4",
              "label": "Heading 4",
              "group": "Headings"
            },
            {
              "value": "h5",
              "label": "Heading 5",
              "group": "Headings"
            },
            {
              "value": "heading--small",
              "label": "Subheading",
              "group": "Subheadings"
            },
            {
              "value": "heading--xsmall",
              "label": "Subheading - Small",
              "group": "Subheadings"
            },
            {
              "value": "text",
              "label": "Default",
              "group": "Body Text"
            },
            {
              "value": "text--xsmall",
              "label": "Tiny",
              "group": "Body Text"
            },
            {
              "value": "text--small",
              "label": "Small",
              "group": "Body Text"
            },
            {
              "value": "text--large",
              "label": "Large",
              "group": "Body Text"
            }
          ],
          "default": "h5"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Content"
        },
        {
          "type": "select",
          "id": "content_style",
          "label": "Content style",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "text",
              "label": "Default",
              "group": "Body Text"
            },
            {
              "value": "text--xsmall",
              "label": "Tiny",
              "group": "Body Text"
            },
            {
              "value": "text--small",
              "label": "Small",
              "group": "Body Text"
            },
            {
              "value": "text--large",
              "label": "Large",
              "group": "Body Text"
            }
          ],
          "default": "text--xsmall"
        },
        {
          "type": "image_picker",
          "id": "icon_image",
          "label": "Icon (Image)"
        },
        {
          "type": "textarea",
          "id": "icon_svg",
          "label": "Icon (SVG)"
        }
      ]
    },
    {
      "type": "benefit-accordions",
      "name": "Benefit Accordions",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "title_style",
          "label": "Title style",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "h4",
              "label": "Heading 4",
              "group": "Headings"
            },
            {
              "value": "h5",
              "label": "Heading 5",
              "group": "Headings"
            },
            {
              "value": "heading--small",
              "label": "Subheading",
              "group": "Subheadings"
            },
            {
              "value": "heading--xsmall",
              "label": "Subheading - Small",
              "group": "Subheadings"
            },
            {
              "value": "text",
              "label": "Default",
              "group": "Body Text"
            },
            {
              "value": "text--xsmall",
              "label": "Tiny",
              "group": "Body Text"
            },
            {
              "value": "text--small",
              "label": "Small",
              "group": "Body Text"
            },
            {
              "value": "text--large",
              "label": "Large",
              "group": "Body Text"
            }
          ],
          "default": "h5"
        },
        {
          "type": "select",
          "id": "content_style",
          "label": "Content style",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "text",
              "label": "Default",
              "group": "Body Text"
            },
            {
              "value": "text--xsmall",
              "label": "Tiny",
              "group": "Body Text"
            },
            {
              "value": "text--small",
              "label": "Small",
              "group": "Body Text"
            },
            {
              "value": "text--large",
              "label": "Large",
              "group": "Body Text"
            }
          ],
          "default": "text--xsmall"
        }
      ]
    },
    {
    "type": "bundle_widget",
      "name": "Bundle Widget",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "Header"
        },
        {
          "type": "text",
          "id": "header_title",
          "label": "Title",
          "default": "Bundle Savings!"
        },
        {
          "type": "richtext",
          "id": "header_message",
          "label": "Text",
          "default": "<p>Save when you buy this set in the same size and color.</p>"
        },
        {
          "type": "header",
          "content": "Products"
        },
        {
          "type": "header",
          "content": "Appearance"
        }
      ]
    },
    {
      "type": "bundle_variant_picker_v1",
      "name": "Bundle Variant picker",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "hide_sold_out_variants",
          "label": "Hide sold out variants",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "ignore_color",
          "label": "Hide Color Option (used for collection swatch links)",
          "default": true
        },
        {
          "type": "select",
          "id": "selector_mode",
          "label": "Selector type",
          "options": [
            {
              "value": "block",
              "label": "Block"
            },
            {
              "value": "dropdown",
              "label": "Dropdown"
            }
          ],
          "default": "block"
        },
        {
          "type": "select",
          "id": "color_mode",
          "label": "Color selector type",
          "info": "Enable [swatches](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) on product options.",
          "options": [
            {
              "value": "swatch",
              "label": "Swatch"
            },
            {
              "value": "none",
              "label": "None"
            }
          ],
          "default": "swatch"
        },
        {
          "type": "text",
          "id": "variant_image_options",
          "label": "Show variant image for options"
        },
        {
          "type": "page",
          "id": "size_chart_page",
          "label": "Size chart page",
          "info": "Feature a page for size option"
        },
        {
          "type": "page",
          "id": "tog_rating_page",
          "label": "TOG Rating page",
          "info": "Feature a page for TOG Ratings"
        }
      ]
    },
    {
      "type": "divider",
      "name": "Divider"
    },
    {
      "type": "loyalty-earnings",
      "name": "Loyalty Point Earnings",
      "limit": 1,
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>You could earn <strong>$POINTS Frequent Flyer Points</strong> with this purchase!</p>",
          "info": "$POINTS is replaced with the number of points this product will earn the customer."
        },
        {
          "type": "header",
          "content": "Rewards Page"
        },
        {
          "type": "paragraph",
          "content": "Link for the rewards page found in theme settings."
        }
      ]
    },
    {
      "type": "variant_picker",
      "name": "Variant picker",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "hide_sold_out_variants",
          "label": "Hide sold out variants",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "ignore_color",
          "label": "Hide Color Option (used for collection swatch links)",
          "default": true
        },
        {
          "type": "select",
          "id": "selector_mode",
          "label": "Selector type",
          "options": [
            {
              "value": "block",
              "label": "Block"
            },
            {
              "value": "dropdown",
              "label": "Dropdown"
            }
          ],
          "default": "block"
        },
        {
          "type": "select",
          "id": "color_mode",
          "label": "Color selector type",
          "info": "Enable [swatches](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) on product options.",
          "options": [
            {
              "value": "swatch",
              "label": "Swatch"
            },
            {
              "value": "none",
              "label": "None"
            }
          ],
          "default": "swatch"
        },
        {
          "type": "text",
          "id": "variant_image_options",
          "label": "Show variant image for options"
        },
        {
          "type": "page",
          "id": "size_chart_page",
          "label": "Size chart page",
          "info": "Feature a page for size option"
        },
        {
          "type": "page",
          "id": "tog_rating_page",
          "label": "TOG Rating page",
          "info": "Feature a page for TOG Ratings"
        }
      ]
    },
    {
      "type": "quantity_selector",
      "name": "Quantity selector",
      "limit": 1
    },
    {
      "type": "collection_swatches",
      "name": "Collection Swatches",
      "limit": 1
    },
    {
      "type": "quantity_atc",
      "name": "Quantity with Add to Cart",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "Wishlist + Registry"
        },
        {
          "type": "checkbox",
          "id": "show_wishlist_button",
          "label": "Show Wishlist Button",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_registry_button",
          "label": "Show Registry Button",
          "default": true
        },
        {
          "type": "header",
          "content": "Add to Cart Buttons"
        },
        {
          "type": "checkbox",
          "id": "show_payment_button",
          "label": "Show dynamic checkout button",
          "info": "Each customer will see their preferred payment method from those available on your store, such as PayPal or Apple Pay. [Learn more](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_gift_card_recipient",
          "label": "Show recipient information form for gift card products",
          "info": "Gift card products can optionally be sent directly to a recipient along with a personal message. [Learn more](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)",
          "default": true
        },
        {
          "type": "color",
          "id": "atc_button_background",
          "label": "Add to cart background",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "atc_button_text_color",
          "label": "Add to cart color",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "buy_now_button_background",
          "label": "Buy now background",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "buy_now_button_text_color",
          "label": "Buy now color",
          "default": "rgba(0,0,0,0)"
        }
      ]
    },
    {
      "type": "buy_buttons",
      "name": "Buy buttons",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_payment_button",
          "label": "Show dynamic checkout button",
          "info": "Each customer will see their preferred payment method from those available on your store, such as PayPal or Apple Pay. [Learn more](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)",
          "default": true
        },
        {
          "type": "color",
          "id": "atc_button_background",
          "label": "Add to cart background",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "atc_button_text_color",
          "label": "Add to cart color",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "buy_now_button_background",
          "label": "Buy now background",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "buy_now_button_text_color",
          "label": "Buy now color",
          "default": "rgba(0,0,0,0)"
        }
      ]
    },
    {
      "type": "description",
      "name": "Description",
      "limit": 1
    },
    {
      "type": "special_description",
      "name": "Special Description",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "long_description_title",
          "label": "Title"
        },
        {
          "type": "richtext",
          "id": "long_description",
          "label": "Long Description"
        }
      ]
    },
    {
      "type": "inventory",
      "name": "Inventory",
      "limit": 1,
      "settings": [
        {
          "type": "range",
          "id": "low_inventory_threshold",
          "label": "Low inventory threshold",
          "info": "Use low stock color when quantity is below the threshold. Choose 0 to always show in stock.",
          "min": 0,
          "max": 100,
          "step": 1,
          "default": 0
        },
        {
          "type": "header",
          "content": "Delivery Estimate"
        },
        {
          "type": "checkbox",
          "id": "delivery_estimate_enable",
          "label": "Show delivery estimate"
        },
        {
          "type": "text",
          "id": "delivery_estimate_text",
          "label": "Label",
          "default": "When will my order arrive?"
        },
        {
          "type": "richtext",
          "id": "delivery_estimate_tooltip",
          "label": "Delivery Estimate"
        }
      ]
    },
    {
      "type": "product_limit",
      "name": "Product Limit Message",
      "limit": 1
    },
    {
      "type": "special_messaging",
      "name": "Special Messaging",
      "limit": 1,
      "settings": [
        {
          "type": "range",
          "id": "low_inventory_threshold",
          "label": "Low inventory threshold",
          "info": "Use low stock color when quantity is below the threshold. Choose 0 to always show in stock.",
          "min": 0,
          "max": 100,
          "step": 1,
          "default": 0
        }
      ]
    },
    {
      "type": "page_tabs",
      "name": "Page Tabs",
      "limit": 1
    },
    {
      "type": "text",
      "name": "Text",
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "label": "Text"
        }
      ]
    },
    {
      "type": "image",
      "name": "Image",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "select",
          "id": "image_alignment",
          "label": "Alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left"
        },
        {
          "type": "range",
          "id": "image_width",
          "min": 50,
          "max": 500,
          "step": 10,
          "unit": "px",
          "label": "Width",
          "default": 150
        }
      ]
    },
    {
      "type": "button",
      "name": "Button",
      "settings": [
        {
          "type": "paragraph",
          "content": "Create link to your contact page, external marketplace..."
        },
        {
          "type": "url",
          "id": "link",
          "label": "Link"
        },
        {
          "type": "text",
          "id": "text",
          "label": "Text"
        },
        {
          "type": "checkbox",
          "id": "stretch",
          "label": "Stretch button",
          "default": true
        },
        {
          "type": "color",
          "id": "background",
          "label": "Background",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text color",
          "default": "rgba(0,0,0,0)"
        }
      ]
    },
    {
      "type": "liquid",
      "name": "Custom Liquid",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title"
        },
        {
          "type": "liquid",
          "id": "liquid",
          "label": "Liquid",
          "info": "Add app snippets or other Liquid code to create advanced customizations."
        }
      ]
    },
    {
      "type": "tag_list",
      "name": "Tag List",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title"
        },
        {
          "type": "select",
          "id": "title_style",
          "label": "Title Style",
          "default": "text--large",
          "options": [
            {
              "value": "heading heading--small",
              "label": "Subheading",
              "group": "Subheadings"
            },
            {
              "value": "heading heading--xsmall",
              "label": "Subheading - Small",
              "group": "Subheadings"
            },
            {
              "value": "",
              "label": "Default",
              "group": "Body Text"
            },
            {
              "value": "text--xsmall",
              "label": "Tiny",
              "group": "Body Text"
            },
            {
              "value": "text--small",
              "label": "Small",
              "group": "Body Text"
            },
            {
              "value": "text--large",
              "label": "Large",
              "group": "Body Text"
            }
          ]
        },
        {
          "type": "header",
          "content": "Tags"
        },
        {
          "type": "metaobject",
          "metaobject_type": "tag_list",
          "id": "tag_list",
          "label": "Tag List"
        },
        {
          "type": "textarea",
          "id": "tag_list_text",
          "label": "Tag List",
          "info": "Each tag on a new line."
        },
        {
          "type": "select",
          "id": "label_style",
          "label": "Tag Style",
          "options": [
            {
              "value": "",
              "label": "Default"
            },
            {
              "value": "label--subdued",
              "label": "Subdued"
            },
            {
              "value": "label--highlight",
              "label": "Highlight"
            },
            {
              "value": "label--custom",
              "label": "Custom"
            },
            {
              "value": "label--custom2",
              "label": "Custom 2"
            },
            {
              "value": "label--light",
              "label": "Light"
            }
          ],
          "default": ""
        },
      ]
    },
    {
      "type": "usps",
      "name": "USPs",
      "settings": [
        {
          "type": "checkbox",
          "id": "images_only",
          "label": "Use images only",
          "default": false
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title"
        },
        {
          "type": "select",
          "id": "title_style",
          "label": "Title Style",
          "default": "heading heading--small",
          "options": [
            {
              "value": "heading heading--small",
              "label": "Subheading",
              "group": "Subheadings"
            },
            {
              "value": "heading heading--xsmall",
              "label": "Subheading - Small",
              "group": "Subheadings"
            },
            {
              "value": "",
              "label": "Default",
              "group": "Body Text"
            },
            {
              "value": "text--xsmall",
              "label": "Tiny",
              "group": "Body Text"
            },
            {
              "value": "text--small",
              "label": "Small",
              "group": "Body Text"
            },
            {
              "value": "text--large",
              "label": "Large",
              "group": "Body Text"
            }
          ]
        },
        {
          "type": "header",
          "content": "USP 1"
        },
        {
          "type": "text",
          "id": "usp_1_title",
          "label": "Title"
        },
        {
          "type": "image_picker",
          "id": "usp_1_image",
          "label": "Icon (Image)",
          "info": "Icon image."
        },
        {
          "type": "liquid",
          "id": "usp_1_liquid",
          "label": "Icon (SVG)",
          "info": "Icon SVG Code"
        },
        {
          "type": "header",
          "content": "USP 2"
        },
        {
          "type": "text",
          "id": "usp_2_title",
          "label": "Title"
        },
        {
          "type": "image_picker",
          "id": "usp_2_image",
          "label": "Icon (Image)",
          "info": "Icon image."
        },
        {
          "type": "liquid",
          "id": "usp_2_liquid",
          "label": "Icon (SVG)",
          "info": "Icon SVG Code"
        },
        {
          "type": "header",
          "content": "USP 3"
        },
        {
          "type": "text",
          "id": "usp_3_title",
          "label": "Title"
        },
        {
          "type": "image_picker",
          "id": "usp_3_image",
          "label": "Icon (Image)",
          "info": "Icon image."
        },
        {
          "type": "liquid",
          "id": "usp_3_liquid",
          "label": "Icon (SVG)",
          "info": "Icon SVG Code"
        },
      ]
    },
    {
      "type": "line_item_property",
      "name": "Line item property",
      "settings": [
        {
          "type": "paragraph",
          "content": "Line item properties are used to collect customization information for an item added to the cart."
        },
        {
          "type": "text",
          "id": "label",
          "label": "Label",
          "default": "Your label"
        },
        {
          "type": "select",
          "id": "type",
          "label": "Type",
          "options": [
            {
              "value": "text",
              "label": "Text"
            },
            {
              "value": "checkbox",
              "label": "Checkbox"
            }
          ],
          "default": "text"
        },
        {
          "type": "header",
          "content": "Text",
          "info": "Only applicable for line item property of type Text."
        },
        {
          "type": "checkbox",
          "id": "required",
          "label": "Required",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "allow_long_text",
          "label": "Allow long text",
          "default": false
        },
        {
          "type": "header",
          "content": "Checkbox",
          "info": "Only applicable for line item property of type Checkbox."
        },
        {
          "type": "text",
          "id": "checked_value",
          "label": "Checked value",
          "default": "Yes"
        },
        {
          "type": "text",
          "id": "unchecked_value",
          "label": "Unchecked value",
          "default": "No"
        }
      ]
    },
    {
      "name": "Content",
      "type": "content",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Content"
        },
        {
          "type": "page",
          "id": "page",
          "label": "Content from page",
          "info": "If specified, takes precedence over inline content."
        }
      ]
    },
    {
      "name": "Addon",
      "type": "addon",
      "settings": [
        {
          "type": "checkbox",
          "id": "addon_enabled",
          "label": "Addon Enabled",
          "default": true
        },
        {
          "type": "header",
          "content": "Addon Settings"
        },
        {
          "type": "number",
          "id": "embroidery_max_characters",
          "label": "Embroidery - Max Characters",
          "default": 13
        },
        {
          "type": "checkbox",
          "id": "addon_disabled",
          "label": "Disable Addon",
          "default": false,
          "info": "Greys out the addon and displays a message if hovered/tapped. (Useful for stopping customers from getting this addon temporarily)"
        },
        {
          "type": "checkbox",
          "id": "addon_has_options",
          "label": "Addon has options",
          "default": false,
          "info": "When adding an addon, opens a drawer for the customer to select options. (drawer contents are currently hard-coded for Embroidery)"
        },
        {
          "type": "text",
          "id": "addon_tag",
          "label": "Addon Tag",
          "default": "Has Embroidery Option",
          "info": "To show the addon only on products with a certain tag, enter the tag here. Leave blank to show on all products.",
          "placeholder": "e.g.\"Has Embroidery Option\""
        },
        {
          "type": "product",
          "id": "addon_product",
          "label": "Addon Product"
        },
        {
          "type": "text",
          "id": "addon_name",
          "label": "Addon Name",
          "info": "This is how the addon will be displayed on the order notes. Defaults to the product title."
        },
        {
          "type": "header",
          "content": "Product Form"
        },
        {
          "type": "text",
          "id": "addon_checkbox_label",
          "label": "Addon checkbox label",
          "info": "Defaults to the product title. Enter some text here to override."
        },
        {
          "type": "text",
          "id": "addon_unavailable_message",
          "label": "Unavailable Message",
          "info": "Message to show in a tooltip when an addon is disabled/unavailable. Defaults to \"Sold Out\""
        },
        {
          "type": "checkbox",
          "id": "addon_show_price",
          "label": "Show Price",
          "info": "Show the addon product price next to the checkbox label.",
          "default": true
        },
        {
          "type": "header",
          "content": "Addon Options",
          "info": "Settings for the addon's options."
        },
        {
          "type": "paragraph",
          "content": "Option 1 (Text)"
        },
        {
          "type": "paragraph",
          "content": "Option 2 (Fonts)"
        },
        {
          "type": "textarea",
          "id": "addon_option_2_values",
          "label": "Values",
          "info": "Separate values with newlines."
        },
        {
          "type": "paragraph",
          "content": "Option 3 (Colors)"
        },
        {
          "type": "paragraph",
          "content": "The color options are defined in global theme settings."
        },
        {
          "type": "header",
          "content": "Addon Drawer",
          "info": "Settings for the addon's options drawer (popover on mobile)."
        },
        {
          "type": "text",
          "id": "drawer_title",
          "label": "Drawer Title",
          "default": "Embroidery"
        },
        {
          "type": "richtext",
          "id": "drawer_intro",
          "label": "Intro Text"
        },
        {
          "type": "header",
          "content": "Examples Drawers",
          "info": "Settings for the drawers opened by 'See Examples' links."
        },
        {
          "type": "text",
          "id": "section_1_examples_drawer_id",
          "label": "Section 1 Examples Drawer ID"
        },
        {
          "type": "text",
          "id": "section_2_examples_drawer_id",
          "label": "Section 2 Examples Drawer ID"
        },
        {
          "type": "text",
          "id": "section_3_examples_drawer_id",
          "label": "Section 3 Examples Drawer ID"
        }
      ]
    },
    {
      "name": "Featured products",
      "type": "featured_products",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Featured products"
        },
        {
          "type": "product",
          "id": "product_1",
          "label": "Product 1"
        },
        {
          "type": "product",
          "id": "product_2",
          "label": "Product 2"
        }
      ]
    },
    {
      "name": "Upsells",
      "type": "featured_products_upsells",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Featured products"
        },
        {
          "type": "paragraph",
          "content": "This block pulls in upsells defined on the product's metafield schema."
        }
      ]
    },
    {
      "name": "Trust icons",
      "type": "trust",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "Show extra text next to your product description to improve trust."
        },
        {
          "type": "header",
          "content": "Text 1"
        },
        {
          "type": "select",
          "id": "icon_1",
          "label": "Icon",
          "options": [
            {
              "value": "picto-coupon",
              "label": "Coupon",
              "group": "Shop"
            },
            {
              "value": "picto-gift",
              "label": "Gift",
              "group": "Shop"
            },
            {
              "value": "picto-taxes",
              "label": "Taxes",
              "group": "Shop"
            },
            {
              "value": "picto-warranty",
              "label": "Warranty",
              "group": "Shop"
            },
            {
              "value": "picto-like",
              "label": "Like",
              "group": "Shop"
            },
            {
              "value": "picto-store",
              "label": "Store",
              "group": "Shop"
            },
            {
              "value": "picto-store-pickup",
              "label": "Store pickup",
              "group": "Shop"
            },
            {
              "value": "picto-love",
              "label": "Love",
              "group": "Shop"
            },
            {
              "value": "picto-donation",
              "label": "Donation",
              "group": "Shop"
            },
            {
              "value": "picto-box",
              "label": "Box",
              "group": "Shipping"
            },
            {
              "value": "picto-address",
              "label": "Address",
              "group": "Shipping"
            },
            {
              "value": "picto-address-pin",
              "label": "Address pin",
              "group": "Shipping"
            },
            {
              "value": "picto-fast-delivery",
              "label": "Fast delivery",
              "group": "Shipping"
            },
            {
              "value": "picto-delivery-truck",
              "label": "Delivery truck",
              "group": "Shipping"
            },
            {
              "value": "picto-return-box",
              "label": "Returns",
              "group": "Shipping"
            },
            {
              "value": "picto-worldwide",
              "label": "World",
              "group": "Shipping"
            },
            {
              "value": "picto-plane",
              "label": "Plane",
              "group": "Shipping"
            },
            {
              "value": "picto-credit-card",
              "label": "Credit card",
              "group": "Payment & Security"
            },
            {
              "value": "picto-lock",
              "label": "Lock",
              "group": "Payment & Security"
            },
            {
              "value": "picto-shield",
              "label": "Shield",
              "group": "Payment & Security"
            },
            {
              "value": "picto-secure-payment",
              "label": "Secure payment",
              "group": "Payment & Security"
            },
            {
              "value": "picto-mobile",
              "label": "Mobile",
              "group": "Communication"
            },
            {
              "value": "picto-phone",
              "label": "Phone",
              "group": "Communication"
            },
            {
              "value": "picto-chat",
              "label": "Chat",
              "group": "Communication"
            },
            {
              "value": "picto-send",
              "label": "Send",
              "group": "Communication"
            },
            {
              "value": "picto-email",
              "label": "Email",
              "group": "Communication"
            },
            {
              "value": "picto-customer-support",
              "label": "Customer support",
              "group": "Communication"
            },
            {
              "value": "picto-operator",
              "label": "Support operator",
              "group": "Communication"
            },
            {
              "value": "picto-virus",
              "label": "Virus",
              "group": "Health"
            },
            {
              "value": "picto-mask",
              "label": "Mask",
              "group": "Health"
            },
            {
              "value": "custom-shipping",
              "label": "Shipping",
              "group": "Kyte Baby"
            },
            {
              "value": "custom-returns",
              "label": "Returns",
              "group": "Kyte Baby"
            },
            {
              "value": "custom-fabric",
              "label": "Fabric",
              "group": "Kyte Baby"
            }
          ],
          "default": "picto-customer-support"
        },
        {
          "type": "image_picker",
          "id": "custom_icon_1",
          "label": "Custom icon",
          "info": "48 x 48px .png with transparency recommended"
        },
        {
          "type": "text",
          "id": "title_1",
          "label": "Heading",
          "default": "Shipping & Returns"
        },
        {
          "type": "richtext",
          "id": "content_1",
          "label": "Content",
          "default": "<p>Add text about your shipping policy</p>"
        },
        {
          "type": "header",
          "content": "Text 2"
        },
        {
          "type": "select",
          "id": "icon_2",
          "label": "Icon",
          "options": [
            {
              "value": "picto-coupon",
              "label": "Coupon",
              "group": "Shop"
            },
            {
              "value": "picto-gift",
              "label": "Gift",
              "group": "Shop"
            },
            {
              "value": "picto-taxes",
              "label": "Taxes",
              "group": "Shop"
            },
            {
              "value": "picto-warranty",
              "label": "Warranty",
              "group": "Shop"
            },
            {
              "value": "picto-like",
              "label": "Like",
              "group": "Shop"
            },
            {
              "value": "picto-store",
              "label": "Store",
              "group": "Shop"
            },
            {
              "value": "picto-store-pickup",
              "label": "Store pickup",
              "group": "Shop"
            },
            {
              "value": "picto-love",
              "label": "Love",
              "group": "Shop"
            },
            {
              "value": "picto-donation",
              "label": "Donation",
              "group": "Shop"
            },
            {
              "value": "picto-box",
              "label": "Box",
              "group": "Shipping"
            },
            {
              "value": "picto-address",
              "label": "Address",
              "group": "Shipping"
            },
            {
              "value": "picto-address-pin",
              "label": "Address pin",
              "group": "Shipping"
            },
            {
              "value": "picto-fast-delivery",
              "label": "Fast delivery",
              "group": "Shipping"
            },
            {
              "value": "picto-delivery-truck",
              "label": "Delivery truck",
              "group": "Shipping"
            },
            {
              "value": "picto-return-box",
              "label": "Returns",
              "group": "Shipping"
            },
            {
              "value": "picto-worldwide",
              "label": "World",
              "group": "Shipping"
            },
            {
              "value": "picto-plane",
              "label": "Plane",
              "group": "Shipping"
            },
            {
              "value": "picto-credit-card",
              "label": "Credit card",
              "group": "Payment & Security"
            },
            {
              "value": "picto-lock",
              "label": "Lock",
              "group": "Payment & Security"
            },
            {
              "value": "picto-shield",
              "label": "Shield",
              "group": "Payment & Security"
            },
            {
              "value": "picto-secure-payment",
              "label": "Secure payment",
              "group": "Payment & Security"
            },
            {
              "value": "picto-mobile",
              "label": "Mobile",
              "group": "Communication"
            },
            {
              "value": "picto-phone",
              "label": "Phone",
              "group": "Communication"
            },
            {
              "value": "picto-chat",
              "label": "Chat",
              "group": "Communication"
            },
            {
              "value": "picto-send",
              "label": "Send",
              "group": "Communication"
            },
            {
              "value": "picto-email",
              "label": "Email",
              "group": "Communication"
            },
            {
              "value": "picto-customer-support",
              "label": "Customer support",
              "group": "Communication"
            },
            {
              "value": "picto-operator",
              "label": "Support operator",
              "group": "Communication"
            },
            {
              "value": "picto-virus",
              "label": "Virus",
              "group": "Health"
            },
            {
              "value": "picto-mask",
              "label": "Mask",
              "group": "Health"
            },
            {
              "value": "custom-shipping",
              "label": "Shipping",
              "group": "Kyte Baby"
            },
            {
              "value": "custom-returns",
              "label": "Returns",
              "group": "Kyte Baby"
            },
            {
              "value": "custom-fabric",
              "label": "Fabric",
              "group": "Kyte Baby"
            }
          ],
          "default": "picto-warranty"
        },
        {
          "type": "image_picker",
          "id": "custom_icon_2",
          "label": "Custom icon",
          "info": "48 x 48px .png with transparency recommended"
        },
        {
          "type": "text",
          "id": "title_2",
          "label": "Heading",
          "default": "Warranty"
        },
        {
          "type": "richtext",
          "id": "content_2",
          "label": "Content",
          "default": "<p>Add text about your product warranty</p>"
        },
        {
          "type": "header",
          "content": "Text 3"
        },
        {
          "type": "select",
          "id": "icon_3",
          "label": "Icon",
          "options": [
            {
              "value": "picto-coupon",
              "label": "Coupon",
              "group": "Shop"
            },
            {
              "value": "picto-gift",
              "label": "Gift",
              "group": "Shop"
            },
            {
              "value": "picto-taxes",
              "label": "Taxes",
              "group": "Shop"
            },
            {
              "value": "picto-warranty",
              "label": "Warranty",
              "group": "Shop"
            },
            {
              "value": "picto-like",
              "label": "Like",
              "group": "Shop"
            },
            {
              "value": "picto-store",
              "label": "Store",
              "group": "Shop"
            },
            {
              "value": "picto-store-pickup",
              "label": "Store pickup",
              "group": "Shop"
            },
            {
              "value": "picto-love",
              "label": "Love",
              "group": "Shop"
            },
            {
              "value": "picto-donation",
              "label": "Donation",
              "group": "Shop"
            },
            {
              "value": "picto-box",
              "label": "Box",
              "group": "Shipping"
            },
            {
              "value": "picto-address",
              "label": "Address",
              "group": "Shipping"
            },
            {
              "value": "picto-address-pin",
              "label": "Address pin",
              "group": "Shipping"
            },
            {
              "value": "picto-fast-delivery",
              "label": "Fast delivery",
              "group": "Shipping"
            },
            {
              "value": "picto-delivery-truck",
              "label": "Delivery truck",
              "group": "Shipping"
            },
            {
              "value": "picto-return-box",
              "label": "Returns",
              "group": "Shipping"
            },
            {
              "value": "picto-worldwide",
              "label": "World",
              "group": "Shipping"
            },
            {
              "value": "picto-plane",
              "label": "Plane",
              "group": "Shipping"
            },
            {
              "value": "picto-credit-card",
              "label": "Credit card",
              "group": "Payment & Security"
            },
            {
              "value": "picto-lock",
              "label": "Lock",
              "group": "Payment & Security"
            },
            {
              "value": "picto-shield",
              "label": "Shield",
              "group": "Payment & Security"
            },
            {
              "value": "picto-secure-payment",
              "label": "Secure payment",
              "group": "Payment & Security"
            },
            {
              "value": "picto-mobile",
              "label": "Mobile",
              "group": "Communication"
            },
            {
              "value": "picto-phone",
              "label": "Phone",
              "group": "Communication"
            },
            {
              "value": "picto-chat",
              "label": "Chat",
              "group": "Communication"
            },
            {
              "value": "picto-send",
              "label": "Send",
              "group": "Communication"
            },
            {
              "value": "picto-email",
              "label": "Email",
              "group": "Communication"
            },
            {
              "value": "picto-customer-support",
              "label": "Customer support",
              "group": "Communication"
            },
            {
              "value": "picto-operator",
              "label": "Support operator",
              "group": "Communication"
            },
            {
              "value": "picto-virus",
              "label": "Virus",
              "group": "Health"
            },
            {
              "value": "picto-mask",
              "label": "Mask",
              "group": "Health"
            },
            {
              "value": "custom-shipping",
              "label": "Shipping",
              "group": "Kyte Baby"
            },
            {
              "value": "custom-returns",
              "label": "Returns",
              "group": "Kyte Baby"
            },
            {
              "value": "custom-fabric",
              "label": "Fabric",
              "group": "Kyte Baby"
            }
          ],
          "default": "picto-secure-payment"
        },
        {
          "type": "image_picker",
          "id": "custom_icon_3",
          "label": "Custom icon",
          "info": "48 x 48px .png with transparency recommended"
        },
        {
          "type": "text",
          "id": "title_3",
          "label": "Heading",
          "default": "Secure Payment"
        },
        {
          "type": "richtext",
          "id": "content_3",
          "label": "Content",
          "default": "<p>Add text about your payment</p>"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "header",
      "content": "Anchors"
    },
    {
      "type": "text",
      "id": "anchor",
      "label": "Anchor",
      "default": "main-product",
      "info": "Anchor name used for jump links.",
      "placeholder": "#anchor"
    },
    {
      "type": "textarea",
      "id": "sticky_form_jumplinks",
      "label": "Jump Links",
      "info": "Jump links to show to the right of the sticky form. (Format: \"Text: #link\")",
      "placeholder": "Buy: #buy\nDetails: #details"
    },
    {
      "type": "header",
      "content": "Addons"
    },
    {
      "type": "checkbox",
      "id": "addons_show_title",
      "label": "Show title",
      "default": false
    },
    {
      "type": "header",
      "content": "Banners"
    },
    {
      "type": "paragraph",
      "content": "Banner 1"
    },
    {
      "type": "liquid",
      "id": "media_banner_1_icon",
      "label": "Icon"
    },
    {
      "type": "text",
      "id": "media_banner_1_title",
      "label": "Title"
    },
    {
      "type": "richtext",
      "id": "media_banner_1_text",
      "label": "Text"
    },
    {
      "type": "text",
      "id": "media_banner_1_link_text",
      "label": "Link Text"
    },
    {
      "type": "richtext",
      "id": "media_banner_1_link_content_text",
      "label": "Drawer Content"
    },
    {
      "type": "paragraph",
      "content": "Banner 2"
    },
    {
      "type": "liquid",
      "id": "media_banner_2_icon",
      "label": "Icon"
    },
    {
      "type": "text",
      "id": "media_banner_2_title",
      "label": "Title"
    },
    {
      "type": "richtext",
      "id": "media_banner_2_text",
      "label": "Text"
    },
    {
      "type": "text",
      "id": "media_banner_2_link_text",
      "label": "Link Text"
    },
    {
      "type": "richtext",
      "id": "media_banner_2_link_content_text",
      "label": "Drawer Content"
    },
    {
      "type": "paragraph",
      "content": "Banner 3"
    },
    {
      "type": "liquid",
      "id": "media_banner_3_icon",
      "label": "Icon"
    },
    {
      "type": "text",
      "id": "media_banner_3_title",
      "label": "Title"
    },
    {
      "type": "richtext",
      "id": "media_banner_3_text",
      "label": "Text"
    },
    {
      "type": "text",
      "id": "media_banner_3_link_text",
      "label": "Link Text"
    },
    {
      "type": "richtext",
      "id": "media_banner_3_link_content_text",
      "label": "Drawer Content"
    },
    {
      "type": "header",
      "content": "Section Settings"
    },
    {
      "type": "checkbox",
      "id": "show_breadcrumbs",
      "label": "Show breadcrumbs",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_taxes_included",
      "label": "Show taxes included",
      "default": false
    },
    {
      "type": "header",
      "content": "Product Meta"
    },
    {
      "type": "checkbox",
      "id": "show_product_collection_breadcrumbs",
      "label": "Show Product in Collection breadcrumbs",
      "default": true,
      "info": "Shows a custom set of breadcrumbs showing collection and subcollection above the product title."
    },
    {
      "type": "select",
      "id": "breadcrumbs_type",
      "label": "Breadcrumbs Type",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "sub-collections",
          "label": "Sub-Collections"
        }
      ],
      "default": ""
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "label": "Show vendor",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_sku",
      "label": "Show SKU",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_product_rating",
      "label": "Show product rating",
      "info": "To display a rating, add a product rating app. [Learn more](https://help.shopify.com/en/manual/products/product-reviews/installation)",
      "default": true
    },
    {
      "type": "header",
      "content": "Sticky Form"
    },
    {
      "type": "checkbox",
      "id": "show_sticky_add_to_cart",
      "label": "Show sticky add to cart",
      "info": "Will be hidden if no Buy buttons block is added onto the page.",
      "default": true
    },
    {
      "type": "header",
      "content": "Page"
    },
    {
      "type": "checkbox",
      "id": "show_share_buttons",
      "label": "Show share buttons",
      "default": true
    },
    {
      "type": "page",
      "id": "help_page",
      "label": "Help page",
      "info": "Feature a page to help your customers"
    },
    {
      "type": "header",
      "content": "Media"
    },
    {
      "type": "paragraph",
      "content": "Learn more about [media types](https://help.shopify.com/en/manual/products/product-media)"
    },
    {
      "type": "checkbox",
      "id": "enable_video_autoplay",
      "label": "Enable video autoplay",
      "info": "Video are muted automatically to allow autoplay",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "enable_video_looping",
      "label": "Enable video looping",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "enable_image_zoom",
      "label": "Enable image zoom",
      "info": "Zoom does not show video nor 3D models.",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_thumbnails_on_mobile",
      "label": "Show thumbnails on mobile",
      "default": false
    },
    {
      "type": "select",
      "id": "label_list_location",
      "label": "Label list location",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "meta",
          "label": "Product Meta"
        },
        {
          "value": "media",
          "label": "Media"
        }
      ],
      "default": "media"
    },
    {
      "type": "select",
      "id": "desktop_thumbnails_position",
      "label": "Desktop thumbnails position",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "bottom",
          "label": "Bottom"
        }
      ],
      "default": "bottom"
    },
    {
      "type": "select",
      "id": "transition_effect",
      "label": "Transition effect",
      "options": [
        {
          "value": "slide",
          "label": "Slide"
        },
        {
          "value": "fade",
          "label": "Fade"
        }
      ],
      "default": "slide"
    }
  ]
}
{% endschema %}
