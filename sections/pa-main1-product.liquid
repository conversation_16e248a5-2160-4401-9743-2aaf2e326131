<style>
 
.product-meta__label-list,.header,.section--apps ,.product__media-image-wrapper, h1, .money,.recommendation-modal__backdrop, .uai, .uwi, .announcement-bar, .main-widget, .ui-admin-bar__body, .recommendation-modal__container, .product-tabs, #shopify-section-header,.flickity-slider,.product__media-nav,.footer,#PAFloaterContainer,.quantity-selector-atc,.product-media-banner-carousel,.product-usps-container,#shopify-block-aef4aa91-ba96-49ab-b44b-25f4e9c650ce,.section__color-wrapper,.product-form__option-selector,.product-form__inventory-wrapper,.minor,.fade-in-up-enter-done,.swym-wishlist-button-bar, #gorgias-chat-messenger-button, #chat-button,.image-with-text-block__image-wrapper,.container--flush,.content-box,#placement-1665743205615,.afterpay-main-text,.product-form, .container--no-horizontal-padding--mobile {display: none !important;}
.product-meta__label-list {
    margin-bottom: 0;
    min-height: 12px;
    pointer-events: unset;
    display: none;
}
.container {
      margin-top: 0px;
    margin-bottom: 0px;
}
.product-meta {
    display: block;
    margin: 0;
    padding-bottom: 0px;
    border-bottom: 0px;
}
  
</style>


{% capture variantDataJSON %}
  {% assign has_previous = false %}
  [
    {% for variant in product.variants %}
      {
        "id": {{ variant.id }},
        {% if variant.metafields.general.description != blank %}{% endif %}
        "metafields": {
          {% if variant.metafields.general.description != blank %}
          "description": "{{ variant.metafields.general.description }}"
          {%- assign has_previous = true -%}
          {% endif %}
          {% if variant.metafields.variant.includes != blank %}
          {%- if has_previous == true -%},{%- assign has_previous = false -%}{%- endif -%}
          "includes": "{{ variant.metafields.variant.includes }}"
          {% endif %}
        }
      }{% unless forloop.last %},{% endunless %}
    {% endfor %}
  ]
{% endcapture  %}

{%- unless template.suffix contains 'quick-buy' -%}
  <style>
    #shopify-section-{{ section.id }} {
      {%- assign buy_buttons_block = section.blocks | where: 'type', 'buy_buttons' | first -%}

      {%- if buy_buttons_block.settings.show_payment_button -%}
        {%- if buy_buttons_block.settings.atc_button_background == 'rgba(0,0,0,0)' -%}
          {%- assign secondary_button_background = settings.secondary_button_background -%}
        {%- else -%}
          {%- assign secondary_button_background = buy_buttons_block.settings.atc_button_background -%}
        {%- endif -%}

        {%- if buy_buttons_block.settings.atc_button_text_color == 'rgba(0,0,0,0)' -%}
          {%- assign secondary_button_text_color = settings.secondary_button_text_color -%}
        {%- else -%}
          {%- assign secondary_button_text_color = buy_buttons_block.settings.atc_button_text_color -%}
        {%- endif -%}

        {%- if buy_buttons_block.settings.buy_now_button_background == 'rgba(0,0,0,0)' -%}
          {%- assign primary_button_background = settings.primary_button_background -%}
        {%- else -%}
          {%- assign primary_button_background = buy_buttons_block.settings.buy_now_button_background -%}
        {%- endif -%}

        {%- if buy_buttons_block.settings.buy_now_button_text_color == 'rgba(0,0,0,0)' -%}
          {%- assign primary_button_text_color = settings.primary_button_text_color -%}
        {%- else -%}
          {%- assign primary_button_text_color = buy_buttons_block.settings.buy_now_button_text_color -%}
        {%- endif -%}
      {%- else -%}
        {%- if buy_buttons_block.settings.atc_button_background == 'rgba(0,0,0,0)' -%}
          {%- assign primary_button_background = settings.primary_button_background -%}
        {%- else -%}
          {%- assign primary_button_background = buy_buttons_block.settings.atc_button_background -%}
        {%- endif -%}

        {%- if buy_buttons_block.settings.atc_button_text_color == 'rgba(0,0,0,0)' -%}
          {%- assign primary_button_text_color = settings.primary_button_text_color -%}
        {%- else -%}
          {%- assign primary_button_text_color = buy_buttons_block.settings.atc_button_text_color -%}
        {%- endif -%}
      {%- endif -%}

      --primary-button-background: {{ primary_button_background.red }}, {{ primary_button_background.green }}, {{ primary_button_background.blue }};
      --primary-button-text-color: {{ primary_button_text_color.red }}, {{ primary_button_text_color.green }}, {{ primary_button_text_color.blue }};
      --secondary-button-background: {{ secondary_button_background.red }}, {{ secondary_button_background.green }}, {{ secondary_button_background.blue }};
      --secondary-button-text-color: {{ secondary_button_text_color.red }}, {{ secondary_button_text_color.green }}, {{ secondary_button_text_color.blue }};
    }
  </style>

  <section>
    {%- if section.settings.show_sticky_add_to_cart and product.available -%}
      {%- render 'product-sticky-form', product: product -%}
    {%- endif -%}

    <div class="container">
      <nav aria-label="{{ 'general.breadcrumb.title' | t }}" class="breadcrumb text--xsmall text--subdued hidden-phone">
        <ol class="breadcrumb__list" role="list">
          <li class="breadcrumb__item">
            <a class="breadcrumb__link" href="{{ routes.root_url }}">{{ 'general.breadcrumb.home' | t }}</a>
          </li>

          {%- if collection -%}
            <li class="breadcrumb__item">
              <a class="breadcrumb__link" href="{{ collection.url }}">{{- collection.title -}}</a>
            </li>
          {%- endif -%}

          <li class="breadcrumb__item">
            <span class="breadcrumb__link" aria-current="page">{{ product.title }}</span>
          </li>
        </ol>
      </nav>

      <!-- PRODUCT TOP PART -->
      <div class="product product--thumbnails-{{ section.settings.desktop_thumbnails_position }}">

        {%- render 'product-media', product: product -%}
        {%- render 'product-media--tiled', product: product -%}

        {%- render 'product-info', product: product, update_url: true, variantDataJSON: variantDataJSON -%}
      </div>
    </div>
  </section>
{%- else -%}
  {%- comment -%}
  The quick shop HTML being very different, we render it here. On mobile and tablet/desktop, the product renders also
  quite differently, as it is in a drawer on tablet/desktop, and a popover on mobile.
  {%- endcomment -%}

  {%- capture quick_buy_product_info -%}
    <div class="quick-buy-product">
      {%- assign featured_media = product.selected_or_first_available_variant.featured_media | default: product.featured_media -%}

      {%- if template.suffix == 'quick-buy-drawer' -%}
        <img loading="lazy" sizes="114px" class="quick-buy-product__image" {% render 'image-attributes', image: featured_media, sizes: '114,228,342' %}>
      {%- else -%}
        <img loading="lazy" sizes="65px" class="quick-buy-product__image" {% render 'image-attributes', image: featured_media, sizes: '65,130,195' %}>
      {%- endif -%}

      <div class="quick-buy-product__info {% if template.suffix == 'quick-buy-popover' %}text--small{% endif %}">
        <product-meta form-id="product-form-{{ section.id }}-{{ product.id }}" price-class="price" unit-price-class="text--xsmall" class="product-item-meta">

          {%- if section.settings.show_vendor -%}
            {%- assign vendor_handle = product.vendor | handle -%}
            {%- assign collection_for_vendor = collections[vendor_handle] -%}

            {%- unless collection_for_vendor.empty? -%}
              <a href="{{ collection_for_vendor.url }}" class="product-item-meta__vendor heading {% if template.suffix == 'quick-buy-drawer' %}heading--small{% else %}heading--xsmall{% endif %}">{{ product.vendor }}</a>
            {%- else -%}
              <a href="{{ product.vendor | url_for_vendor }}" class="product-item-meta__vendor heading {% if template.suffix == 'quick-buy-drawer' %}heading--small{% else %}heading--xsmall{% endif %}">{{ product.vendor }}</a>
            {%- endunless -%}
          {%- endif -%}

          <a href="{{ product.url }}" class="product-item-meta__title">{{ product.title }}</a>

          <div class="product-item-meta__price-list-container" role="region" aria-live="polite">
            <div class="price-list" data-product-price-list>
              {%- if product.selected_or_first_available_variant.compare_at_price > product.selected_or_first_available_variant.price -%}
                <span class="price price--highlight">
                  <span class="visually-hidden">{{ 'product.general.sale_price' | t }}</span>

                  {%- if settings.currency_code_enabled -%}
                    {{- product.selected_or_first_available_variant.price | money_with_currency -}}
                  {%- else -%}
                    {{- product.selected_or_first_available_variant.price | money -}}
                  {%- endif -%}
                </span>

                <span class="price price--compare">
                  <span class="visually-hidden">{{ 'product.general.regular_price' | t }}</span>

                  {%- if settings.currency_code_enabled -%}
                    {{- product.selected_or_first_available_variant.compare_at_price | money_with_currency -}}
                  {%- else -%}
                    {{- product.selected_or_first_available_variant.compare_at_price | money -}}
                  {%- endif -%}
                </span>
              {%- else -%}
                <span class="price">
                  <span class="visually-hidden">{{ 'product.general.sale_price' | t }}</span>

                  {%- if settings.currency_code_enabled -%}
                    {{- product.selected_or_first_available_variant.price | money_with_currency -}}
                  {%- else -%}
                    {{- product.selected_or_first_available_variant.price | money -}}
                  {%- endif -%}
                </span>
              {%- endif -%}

              {%- if product.selected_or_first_available_variant.unit_price_measurement -%}
                <div class="price text--subdued text--xsmall">
                  <div class="unit-price-measurement">
                    <span class="unit-price-measurement__price">{{ product.selected_or_first_available_variant.unit_price | money }}</span>
                    <span class="unit-price-measurement__separator">/</span>

                    {%- if product.selected_or_first_available_variant.unit_price_measurement.reference_value != 1 -%}
                      <span class="unit-price-measurement__reference-value">{{ product.selected_or_first_available_variant.unit_price_measurement.reference_value }}</span>
                    {%- endif -%}

                    <span class="unit-price-measurement__reference-unit">{{ product.selected_or_first_available_variant.unit_price_measurement.reference_unit }}</span>
                  </div>
                </div>
              {%- endif -%}
            </div>

            {%- if template.suffix == 'quick-buy-popover' -%}
              <a href="{{ product.url }}" class="link text--subdued">{{ 'product.general.view_details' | t }}</a>
            {%- endif -%}
          </div>
        </product-meta>
      </div>
    </div>
  {%- endcapture -%}

  {%- if template.suffix == 'quick-buy-drawer' -%}
    <quick-buy-drawer class="drawer drawer--large drawer--quick-buy">
      <cart-notification hidden class="cart-notification cart-notification--drawer"></cart-notification>

      <span class="drawer__overlay"></span>

      <header class="drawer__header">
        <p class="drawer__title heading h6">{{ 'product.form.choose_options' | t }}</p>

        <button type="button" class="drawer__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
          {%- render 'icon' with 'close' -%}
        </button>
    
      </header>

      <div class="drawer__content">
        {{- quick_buy_product_info -}}
        {%- render 'product-form', product: product -%}
      </div>
    </quick-buy-drawer>
  {%- elsif template.suffix == 'quick-buy-popover' -%}
    <quick-buy-popover class="popover popover--quick-buy">
      <span class="popover__overlay"></span>

      <header class="popover__header">
        {{- quick_buy_product_info -}}

      
      </header>

      <div class="popover__content popover__content--no-padding">
        {%- render 'product-form', product: product -%}
      </div>
    </quick-buy-popover>
  {%- endif -%}
{%- endunless -%}

{% schema %}
{
  "name": "Product page",
  "class": "shopify-section--main-product",
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "variant_picker",
      "name": "Variant picker",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "hide_sold_out_variants",
          "label": "Hide sold out variants",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "ignore_color",
          "label": "Hide Color Option (used for collection swatch links)",
          "default": true
        },
        {
          "type": "select",
          "id": "selector_mode",
          "label": "Selector type",
          "options": [
            {
              "value": "block",
              "label": "Block"
            },
            {
              "value": "dropdown",
              "label": "Dropdown"
            }
          ],
          "default": "block"
        },
        {
          "type": "select",
          "id": "color_mode",
          "label": "Color selector type",
          "info": "Variant image mode requires that all variant have an associated image. [Learn more](https://help.shopify.com/en/manual/products/product-media/add-images-variants#add-images-to-existing-variants)",
          "options": [
            {
              "value": "block",
              "label": "Block"
            },
            {
              "value": "dropdown",
              "label": "Dropdown"
            },
            {
              "value": "color",
              "label": "Color swatch"
            },
            {
              "value": "variant_image",
              "label": "Variant image"
            }
          ],
          "default": "color"
        },
        {
          "type": "page",
          "id": "size_chart_page",
          "label": "Size chart page",
          "info": "Feature a page for size option"
        },
        {
          "type": "page",
          "id": "tog_rating_page",
          "label": "TOG Rating page",
          "info": "Feature a page for TOG Ratings"
        }
      ]
    },
    {
      "type": "quantity_selector",
      "name": "Quantity selector",
      "limit": 1
    },
    {
      "type": "collection_swatches",
      "name": "Collection Swatches",
      "limit": 1
    },
    {
      "type": "quantity_atc",
      "name": "Quantity with Add to Cart",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_payment_button",
          "label": "Show dynamic checkout button",
          "info": "Each customer will see their preferred payment method from those available on your store, such as PayPal or Apple Pay. [Learn more](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)",
          "default": true
        },
        {
          "type": "color",
          "id": "atc_button_background",
          "label": "Add to cart background",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "atc_button_text_color",
          "label": "Add to cart color",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "buy_now_button_background",
          "label": "Buy now background",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "buy_now_button_text_color",
          "label": "Buy now color",
          "default": "rgba(0,0,0,0)"
        }
      ]
    },
    {
      "type": "buy_buttons",
      "name": "Buy buttons",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_payment_button",
          "label": "Show dynamic checkout button",
          "info": "Each customer will see their preferred payment method from those available on your store, such as PayPal or Apple Pay. [Learn more](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)",
          "default": true
        },
        {
          "type": "color",
          "id": "atc_button_background",
          "label": "Add to cart background",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "atc_button_text_color",
          "label": "Add to cart color",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "buy_now_button_background",
          "label": "Buy now background",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "buy_now_button_text_color",
          "label": "Buy now color",
          "default": "rgba(0,0,0,0)"
        }
      ]
    },
    {
      "type": "description",
      "name": "Description",
      "limit": 1
    },
    {
      "type": "special_description",
      "name": "Special Description",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "long_description_title",
          "label": "Title"
        },
        {
          "type": "richtext",
          "id": "long_description",
          "label": "Long Description"
        }
      ]
    },
    {
      "type": "inventory",
      "name": "Inventory",
      "limit": 1,
      "settings": [
        {
          "type": "range",
          "id": "low_inventory_threshold",
          "label": "Low inventory threshold",
          "info": "Use low stock color when quantity is below the threshold. Choose 0 to always show in stock.",
          "min": 0,
          "max": 100,
          "step": 1,
          "default": 0
        }
      ]
    },
    {
      "type": "product_limit",
      "name": "Product Limit Message",
      "limit": 1
    },
    {
      "type": "special_messaging",
      "name": "Special Messaging",
      "limit": 1
    },
    {
      "type": "page_tabs",
      "name": "Page Tabs",
      "limit": 1
    },
    {
      "type": "text",
      "name": "Text",
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "label": "Text"
        }
      ]
    },
    {
      "type": "image",
      "name": "Image",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "select",
          "id": "image_alignment",
          "label": "Alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left"
        },
        {
          "type": "range",
          "id": "image_width",
          "min": 50,
          "max": 500,
          "step": 10,
          "unit": "px",
          "label": "Width",
          "default": 150
        }
      ]
    },
    {
      "type": "button",
      "name": "Button",
      "settings": [
        {
          "type": "paragraph",
          "content": "Create link to your contact page, external marketplace..."
        },
        {
          "type": "url",
          "id": "link",
          "label": "Link"
        },
        {
          "type": "text",
          "id": "text",
          "label": "Text"
        },
        {
          "type": "checkbox",
          "id": "stretch",
          "label": "Stretch button",
          "default": true
        },
        {
          "type": "color",
          "id": "background",
          "label": "Background",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text color",
          "default": "rgba(0,0,0,0)"
        }
      ]
    },
    {
      "type": "liquid",
      "name": "Custom Liquid",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title"
        },
        {
          "type": "liquid",
          "id": "liquid",
          "label": "Liquid",
          "info": "Add app snippets or other Liquid code to create advanced customizations."
        }
      ]
    },
    {
      "type": "usps",
      "name": "USPs",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title"
        },
        {
          "type": "header",
          "content": "USP 1"
        },
        {
          "type": "text",
          "id": "usp_1_title",
          "label": "Title"
        },
        {
          "type": "liquid",
          "id": "usp_1_liquid",
          "label": "Liquid",
          "info": "Add app snippets or other Liquid code to create advanced customizations."
        },
        {
          "type": "header",
          "content": "USP 2"
        },
        {
          "type": "text",
          "id": "usp_2_title",
          "label": "Title"
        },
        {
          "type": "liquid",
          "id": "usp_2_liquid",
          "label": "Liquid",
          "info": "Add app snippets or other Liquid code to create advanced customizations."
        },
        {
          "type": "header",
          "content": "USP 3"
        },
        {
          "type": "text",
          "id": "usp_3_title",
          "label": "Title"
        },
        {
          "type": "liquid",
          "id": "usp_3_liquid",
          "label": "Liquid",
          "info": "Add app snippets or other Liquid code to create advanced customizations."
        }
      ]
    },
    {
      "type": "line_item_property",
      "name": "Line item property",
      "settings": [
        {
          "type": "paragraph",
          "content": "Line item properties are used to collect customization information for an item added to the cart."
        },
        {
          "type": "text",
          "id": "label",
          "label": "Label",
          "default": "Your label"
        },
        {
          "type": "select",
          "id": "type",
          "label": "Type",
          "options": [
            {
              "value": "text",
              "label": "Text"
            },
            {
              "value": "checkbox",
              "label": "Checkbox"
            }
          ],
          "default": "text"
        },
        {
          "type": "header",
          "content": "Text",
          "info": "Only applicable for line item property of type Text."
        },
        {
          "type": "checkbox",
          "id": "required",
          "label": "Required",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "allow_long_text",
          "label": "Allow long text",
          "default": false
        },
        {
          "type": "header",
          "content": "Checkbox",
          "info": "Only applicable for line item property of type Checkbox."
        },
        {
          "type": "text",
          "id": "checked_value",
          "label": "Checked value",
          "default": "Yes"
        },
        {
          "type": "text",
          "id": "unchecked_value",
          "label": "Unchecked value",
          "default": "No"
        }
      ]
    },
    {
      "name": "Content",
      "type": "content",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Content"
        },
        {
          "type": "page",
          "id": "page",
          "label": "Content from page",
          "info": "If specified, takes precedence over inline content."
        }
      ]
    },
    {
      "name": "Featured products",
      "type": "featured_products",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Featured products"
        },
        {
          "type": "product",
          "id": "product_1",
          "label": "Product 1"
        },
        {
          "type": "product",
          "id": "product_2",
          "label": "Product 2"
        }
      ]
    },
    {
      "name": "Upsells",
      "type": "featured_products_upsells",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Featured products"
        },
        {
          "type": "paragraph",
          "content": "This block pulls in upsells defined on the product's metafield schema."
        }
      ]
    },
    {
      "name": "Trust icons",
      "type": "trust",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "Show extra text next to your product description to improve trust."
        },
        {
          "type": "header",
          "content": "Text 1"
        },
        {
          "type": "select",
          "id": "icon_1",
          "label": "Icon",
          "options": [
            {
              "value": "picto-coupon",
              "label": "Coupon",
              "group": "Shop"
            },
            {
              "value": "picto-gift",
              "label": "Gift",
              "group": "Shop"
            },
            {
              "value": "picto-taxes",
              "label": "Taxes",
              "group": "Shop"
            },
            {
              "value": "picto-warranty",
              "label": "Warranty",
              "group": "Shop"
            },
            {
              "value": "picto-like",
              "label": "Like",
              "group": "Shop"
            },
            {
              "value": "picto-store",
              "label": "Store",
              "group": "Shop"
            },
            {
              "value": "picto-store-pickup",
              "label": "Store pickup",
              "group": "Shop"
            },
            {
              "value": "picto-love",
              "label": "Love",
              "group": "Shop"
            },
            {
              "value": "picto-donation",
              "label": "Donation",
              "group": "Shop"
            },
            {
              "value": "picto-box",
              "label": "Box",
              "group": "Shipping"
            },
            {
              "value": "picto-address",
              "label": "Address",
              "group": "Shipping"
            },
            {
              "value": "picto-address-pin",
              "label": "Address pin",
              "group": "Shipping"
            },
            {
              "value": "picto-fast-delivery",
              "label": "Fast delivery",
              "group": "Shipping"
            },
            {
              "value": "picto-delivery-truck",
              "label": "Delivery truck",
              "group": "Shipping"
            },
            {
              "value": "picto-return-box",
              "label": "Returns",
              "group": "Shipping"
            },
            {
              "value": "picto-worldwide",
              "label": "World",
              "group": "Shipping"
            },
            {
              "value": "picto-plane",
              "label": "Plane",
              "group": "Shipping"
            },
            {
              "value": "picto-credit-card",
              "label": "Credit card",
              "group": "Payment & Security"
            },
            {
              "value": "picto-lock",
              "label": "Lock",
              "group": "Payment & Security"
            },
            {
              "value": "picto-shield",
              "label": "Shield",
              "group": "Payment & Security"
            },
            {
              "value": "picto-secure-payment",
              "label": "Secure payment",
              "group": "Payment & Security"
            },
            {
              "value": "picto-mobile",
              "label": "Mobile",
              "group": "Communication"
            },
            {
              "value": "picto-phone",
              "label": "Phone",
              "group": "Communication"
            },
            {
              "value": "picto-chat",
              "label": "Chat",
              "group": "Communication"
            },
            {
              "value": "picto-send",
              "label": "Send",
              "group": "Communication"
            },
            {
              "value": "picto-email",
              "label": "Email",
              "group": "Communication"
            },
            {
              "value": "picto-customer-support",
              "label": "Customer support",
              "group": "Communication"
            },
            {
              "value": "picto-operator",
              "label": "Support operator",
              "group": "Communication"
            },
            {
              "value": "picto-virus",
              "label": "Virus",
              "group": "Health"
            },
            {
              "value": "picto-mask",
              "label": "Mask",
              "group": "Health"
            },
            {
              "value": "custom-shipping",
              "label": "Shipping",
              "group": "Kyte Baby"
            },
            {
              "value": "custom-returns",
              "label": "Returns",
              "group": "Kyte Baby"
            },
            {
              "value": "custom-fabric",
              "label": "Fabric",
              "group": "Kyte Baby"
            }
          ],
          "default": "picto-customer-support"
        },
        {
          "type": "image_picker",
          "id": "custom_icon_1",
          "label": "Custom icon",
          "info": "48 x 48px .png with transparency recommended"
        },
        {
          "type": "text",
          "id": "title_1",
          "label": "Heading",
          "default": "Shipping & Returns"
        },
        {
          "type": "richtext",
          "id": "content_1",
          "label": "Content",
          "default": "<p>Add text about your shipping policy</p>"
        },
        {
          "type": "header",
          "content": "Text 2"
        },
        {
          "type": "select",
          "id": "icon_2",
          "label": "Icon",
          "options": [
            {
              "value": "picto-coupon",
              "label": "Coupon",
              "group": "Shop"
            },
            {
              "value": "picto-gift",
              "label": "Gift",
              "group": "Shop"
            },
            {
              "value": "picto-taxes",
              "label": "Taxes",
              "group": "Shop"
            },
            {
              "value": "picto-warranty",
              "label": "Warranty",
              "group": "Shop"
            },
            {
              "value": "picto-like",
              "label": "Like",
              "group": "Shop"
            },
            {
              "value": "picto-store",
              "label": "Store",
              "group": "Shop"
            },
            {
              "value": "picto-store-pickup",
              "label": "Store pickup",
              "group": "Shop"
            },
            {
              "value": "picto-love",
              "label": "Love",
              "group": "Shop"
            },
            {
              "value": "picto-donation",
              "label": "Donation",
              "group": "Shop"
            },
            {
              "value": "picto-box",
              "label": "Box",
              "group": "Shipping"
            },
            {
              "value": "picto-address",
              "label": "Address",
              "group": "Shipping"
            },
            {
              "value": "picto-address-pin",
              "label": "Address pin",
              "group": "Shipping"
            },
            {
              "value": "picto-fast-delivery",
              "label": "Fast delivery",
              "group": "Shipping"
            },
            {
              "value": "picto-delivery-truck",
              "label": "Delivery truck",
              "group": "Shipping"
            },
            {
              "value": "picto-return-box",
              "label": "Returns",
              "group": "Shipping"
            },
            {
              "value": "picto-worldwide",
              "label": "World",
              "group": "Shipping"
            },
            {
              "value": "picto-plane",
              "label": "Plane",
              "group": "Shipping"
            },
            {
              "value": "picto-credit-card",
              "label": "Credit card",
              "group": "Payment & Security"
            },
            {
              "value": "picto-lock",
              "label": "Lock",
              "group": "Payment & Security"
            },
            {
              "value": "picto-shield",
              "label": "Shield",
              "group": "Payment & Security"
            },
            {
              "value": "picto-secure-payment",
              "label": "Secure payment",
              "group": "Payment & Security"
            },
            {
              "value": "picto-mobile",
              "label": "Mobile",
              "group": "Communication"
            },
            {
              "value": "picto-phone",
              "label": "Phone",
              "group": "Communication"
            },
            {
              "value": "picto-chat",
              "label": "Chat",
              "group": "Communication"
            },
            {
              "value": "picto-send",
              "label": "Send",
              "group": "Communication"
            },
            {
              "value": "picto-email",
              "label": "Email",
              "group": "Communication"
            },
            {
              "value": "picto-customer-support",
              "label": "Customer support",
              "group": "Communication"
            },
            {
              "value": "picto-operator",
              "label": "Support operator",
              "group": "Communication"
            },
            {
              "value": "picto-virus",
              "label": "Virus",
              "group": "Health"
            },
            {
              "value": "picto-mask",
              "label": "Mask",
              "group": "Health"
            },
            {
              "value": "custom-shipping",
              "label": "Shipping",
              "group": "Kyte Baby"
            },
            {
              "value": "custom-returns",
              "label": "Returns",
              "group": "Kyte Baby"
            },
            {
              "value": "custom-fabric",
              "label": "Fabric",
              "group": "Kyte Baby"
            }
          ],
          "default": "picto-warranty"
        },
        {
          "type": "image_picker",
          "id": "custom_icon_2",
          "label": "Custom icon",
          "info": "48 x 48px .png with transparency recommended"
        },
        {
          "type": "text",
          "id": "title_2",
          "label": "Heading",
          "default": "Warranty"
        },
        {
          "type": "richtext",
          "id": "content_2",
          "label": "Content",
          "default": "<p>Add text about your product warranty</p>"
        },
        {
          "type": "header",
          "content": "Text 3"
        },
        {
          "type": "select",
          "id": "icon_3",
          "label": "Icon",
          "options": [
            {
              "value": "picto-coupon",
              "label": "Coupon",
              "group": "Shop"
            },
            {
              "value": "picto-gift",
              "label": "Gift",
              "group": "Shop"
            },
            {
              "value": "picto-taxes",
              "label": "Taxes",
              "group": "Shop"
            },
            {
              "value": "picto-warranty",
              "label": "Warranty",
              "group": "Shop"
            },
            {
              "value": "picto-like",
              "label": "Like",
              "group": "Shop"
            },
            {
              "value": "picto-store",
              "label": "Store",
              "group": "Shop"
            },
            {
              "value": "picto-store-pickup",
              "label": "Store pickup",
              "group": "Shop"
            },
            {
              "value": "picto-love",
              "label": "Love",
              "group": "Shop"
            },
            {
              "value": "picto-donation",
              "label": "Donation",
              "group": "Shop"
            },
            {
              "value": "picto-box",
              "label": "Box",
              "group": "Shipping"
            },
            {
              "value": "picto-address",
              "label": "Address",
              "group": "Shipping"
            },
            {
              "value": "picto-address-pin",
              "label": "Address pin",
              "group": "Shipping"
            },
            {
              "value": "picto-fast-delivery",
              "label": "Fast delivery",
              "group": "Shipping"
            },
            {
              "value": "picto-delivery-truck",
              "label": "Delivery truck",
              "group": "Shipping"
            },
            {
              "value": "picto-return-box",
              "label": "Returns",
              "group": "Shipping"
            },
            {
              "value": "picto-worldwide",
              "label": "World",
              "group": "Shipping"
            },
            {
              "value": "picto-plane",
              "label": "Plane",
              "group": "Shipping"
            },
            {
              "value": "picto-credit-card",
              "label": "Credit card",
              "group": "Payment & Security"
            },
            {
              "value": "picto-lock",
              "label": "Lock",
              "group": "Payment & Security"
            },
            {
              "value": "picto-shield",
              "label": "Shield",
              "group": "Payment & Security"
            },
            {
              "value": "picto-secure-payment",
              "label": "Secure payment",
              "group": "Payment & Security"
            },
            {
              "value": "picto-mobile",
              "label": "Mobile",
              "group": "Communication"
            },
            {
              "value": "picto-phone",
              "label": "Phone",
              "group": "Communication"
            },
            {
              "value": "picto-chat",
              "label": "Chat",
              "group": "Communication"
            },
            {
              "value": "picto-send",
              "label": "Send",
              "group": "Communication"
            },
            {
              "value": "picto-email",
              "label": "Email",
              "group": "Communication"
            },
            {
              "value": "picto-customer-support",
              "label": "Customer support",
              "group": "Communication"
            },
            {
              "value": "picto-operator",
              "label": "Support operator",
              "group": "Communication"
            },
            {
              "value": "picto-virus",
              "label": "Virus",
              "group": "Health"
            },
            {
              "value": "picto-mask",
              "label": "Mask",
              "group": "Health"
            },
            {
              "value": "custom-shipping",
              "label": "Shipping",
              "group": "Kyte Baby"
            },
            {
              "value": "custom-returns",
              "label": "Returns",
              "group": "Kyte Baby"
            },
            {
              "value": "custom-fabric",
              "label": "Fabric",
              "group": "Kyte Baby"
            }
          ],
          "default": "picto-secure-payment"
        },
        {
          "type": "image_picker",
          "id": "custom_icon_3",
          "label": "Custom icon",
          "info": "48 x 48px .png with transparency recommended"
        },
        {
          "type": "text",
          "id": "title_3",
          "label": "Heading",
          "default": "Secure Payment"
        },
        {
          "type": "richtext",
          "id": "content_3",
          "label": "Content",
          "default": "<p>Add text about your payment</p>"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "header",
      "content": "Banners"
    },
    {
      "type": "paragraph",
      "content": "Banner 1"
    },
    {
      "type": "liquid",
      "id": "media_banner_1_icon",
      "label": "Icon"
    },
    {
      "type": "text",
      "id": "media_banner_1_title",
      "label": "Title"
    },
    {
      "type": "richtext",
      "id": "media_banner_1_text",
      "label": "Text"
    },
    {
      "type": "text",
      "id": "media_banner_1_link_text",
      "label": "Link Text"
    },
    {
      "type": "richtext",
      "id": "media_banner_1_link_content_text",
      "label": "Drawer Content"
    },
    {
      "type": "paragraph",
      "content": "Banner 2"
    },
    {
      "type": "liquid",
      "id": "media_banner_2_icon",
      "label": "Icon"
    },
    {
      "type": "text",
      "id": "media_banner_2_title",
      "label": "Title"
    },
    {
      "type": "richtext",
      "id": "media_banner_2_text",
      "label": "Text"
    },
    {
      "type": "text",
      "id": "media_banner_2_link_text",
      "label": "Link Text"
    },
    {
      "type": "richtext",
      "id": "media_banner_2_link_content_text",
      "label": "Drawer Content"
    },
    {
      "type": "paragraph",
      "content": "Banner 3"
    },
    {
      "type": "liquid",
      "id": "media_banner_3_icon",
      "label": "Icon"
    },
    {
      "type": "text",
      "id": "media_banner_3_title",
      "label": "Title"
    },
    {
      "type": "richtext",
      "id": "media_banner_3_text",
      "label": "Text"
    },
    {
      "type": "text",
      "id": "media_banner_3_link_text",
      "label": "Link Text"
    },
    {
      "type": "richtext",
      "id": "media_banner_3_link_content_text",
      "label": "Drawer Content"
    },
    {
      "type": "header",
      "content": "Section Settings"
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "label": "Show vendor",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_sku",
      "label": "Show SKU",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_taxes_included",
      "label": "Show taxes included",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_product_rating",
      "label": "Show product rating",
      "info": "To display a rating, add a product rating app. [Learn more](https://help.shopify.com/en/manual/products/product-reviews/installation)",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_sticky_add_to_cart",
      "label": "Show sticky add to cart",
      "info": "Will be hidden if no Buy buttons block is added onto the page.",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_share_buttons",
      "label": "Show share buttons",
      "default": true
    },
    {
      "type": "page",
      "id": "help_page",
      "label": "Help page",
      "info": "Feature a page to help your customers"
    },
    {
      "type": "header",
      "content": "Media"
    },
    {
      "type": "paragraph",
      "content": "Learn more about [media types](https://help.shopify.com/en/manual/products/product-media)"
    },
    {
      "type": "checkbox",
      "id": "enable_video_autoplay",
      "label": "Enable video autoplay",
      "info": "Video are muted automatically to allow autoplay",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "enable_video_looping",
      "label": "Enable video looping",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "enable_image_zoom",
      "label": "Enable image zoom",
      "info": "Zoom does not show video nor 3D models.",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_thumbnails_on_mobile",
      "label": "Show thumbnails on mobile",
      "default": false
    },
    {
      "type": "select",
      "id": "desktop_thumbnails_position",
      "label": "Desktop thumbnails position",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "bottom",
          "label": "Bottom"
        }
      ],
      "default": "bottom"
    },
    {
      "type": "select",
      "id": "transition_effect",
      "label": "Transition effect",
      "options": [
        {
          "value": "slide",
          "label": "Slide"
        },
        {
          "value": "fade",
          "label": "Fade"
        }
      ],
      "default": "slide"
    }
  ]
}
{% endschema %}

<script>
  (function(){
    window.PlobalBridge = {
        openURL:function(url,title,open_links_in){
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.openURL){
                    window.webkit.messageHandlers.openURL.postMessage({'type':open_links_in,'url':url,'title':title});
                }
            } catch(e) {
                console.log("ios error openURL");
                console.log(e);
            }
            try {
                if(window && window.AndroidBridge && window.AndroidBridge.openURL){
                    window.AndroidBridge.openURL(open_links_in,url,title);
                }
            } catch(e) {
                console.log("android error openURL");
                console.log(e);
            }
            if(!window.webkit && !window.AndroidBridge){
                window.location.assign(url);
            }
        },
        openURLNew:function(data){
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.openURLNew){
                    window.webkit.messageHandlers.openURLNew.postMessage(data);
                }
            } catch(e) {
                console.log("ios error openURLNew");
                console.log(e);
            }
            try {
                if(window && window.AndroidBridge && window.AndroidBridge.openURLNew){
                    window.AndroidBridge.openURLNew(JSON.stringify(data));
                }
            } catch(e) {
                console.log("android error openURLNew");
                console.log(e);
            }
            if(!window.webkit && !window.AndroidBridge){
                window.location.assign(data.url);
            }
            this.openURL(data.url,data.title,data.type);
        },
        openURLNewIOS:function(data){
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.openURLNew){
                    window.webkit.messageHandlers.openURLNew.postMessage(data);
                }
            } catch(e) {
                console.log("ios error openURLNew");
                console.log(e);
            }
            
            if(!window.webkit){
                window.location.assign(data.url);
            }
            this.openURL(data.url,data.title,data.type);
        },
        redirectTo:function(app_feature_id){
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.redirectTo){
                    window.webkit.messageHandlers.redirectTo.postMessage({'app_feature_id':app_feature_id});
                }
            } catch(e) {
                console.log("ios error redirectTo");
                console.log(e);
            }
            try {
                if(window && window.AndroidBridge && window.AndroidBridge.redirectTo){
                    window.AndroidBridge.redirectTo(JSON.stringify({'app_feature_id':app_feature_id}));
                }
            } catch(e) {
                console.log("android error redirectTo");
                console.log(e);
            }
            if(!window.webkit && !window.AndroidBridge){
                alert(app_feature_id);
            }
        },
        adjustWebViewHeight:function(container){
            if(!container){
                var height = document.body.clientHeight;
            }else if(container == 0){
                var height = 0;
            }
            else if(container == -25){
                var height = -25;
            }else{
                var height = document.getElementById(container).clientHeight;
            }
            height +=25;
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.adjustWebViewHeight){
                    window.webkit.messageHandlers.adjustWebViewHeight.postMessage(height);
                }
            } catch(e) {
                console.log("ios error adjustWebViewHeight");
                console.log(e);
            }
            if(!window.webkit && !window.AndroidBridge){
                console.log('adjustWebViewHeight  '+height);
            }
        },
        addLineItemProperty:function(obj){
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.addLineItemProperty){
                    window.webkit.messageHandlers.addLineItemProperty.postMessage(obj);
                }
            } catch(e) {
                console.log("ios error addLineItemProperty");
                console.log(e);
            }
            try {
                if(window && window.AndroidBridge && window.AndroidBridge.addLineItemProperty){
                    window.AndroidBridge.addLineItemProperty(JSON.stringify(obj));
                }
            } catch(e) {
                console.log("android error addLineItemProperty");
                console.log(e);
            }
            // if(!window.webkit && !window.AndroidBridge){
            //     console.log('addLineItemProperty  '+JSON.stringify(obj));
            // }
            console.log('addLineItemProperty  '+JSON.stringify(obj));
        },
        removeLineItemProperty:function(key){
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.removeLineItemProperty){
                    window.webkit.messageHandlers.removeLineItemProperty.postMessage(key);
                }
            } catch(e) {
                console.log("ios error addLineItemProperty");
                console.log(e);
            }
            try {
                if(window && window.AndroidBridge && window.AndroidBridge.removeLineItemProperty){
                    window.AndroidBridge.removeLineItemProperty(key);
                }
            } catch(e) {
                console.log("android error removeLineItemProperty");
                console.log(e);
            }
            if(!window.webkit && !window.AndroidBridge){
                console.log('removeLineItemProperty  '+key);
            }
        },
        setAddToCartBehaviour:function(obj){
            console.log("setAddToCartBehaviour");
            console.log(obj);
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.setAddToCartBehaviour){
                    window.webkit.messageHandlers.setAddToCartBehaviour.postMessage(obj);
                }
            } catch(e) {
                console.log("ios error setAddToCartBehaviour");
                console.log(e);
            }
            try {
                if(window && window.AndroidBridge && window.AndroidBridge.setAddToCartBehaviour){
                    window.AndroidBridge.setAddToCartBehaviour(JSON.stringify(obj));
                }
            } catch(e) {
                console.log("android error setAddToCartBehaviour");
                console.log(e);
            }
            if(!window.webkit && !window.AndroidBridge){
                console.log('setAddToCartBehaviour  '+JSON.stringify(obj));
            }
        },
        setBuyNowBehaviour:function(obj){
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.setBuyNowBehaviour){
                    window.webkit.messageHandlers.setBuyNowBehaviour.postMessage(obj);
                }
            } catch(e) {
                console.log("ios error setBuyNowBehaviour");
                console.log(e);
            }
            try {
                if(window && window.AndroidBridge && window.AndroidBridge.setBuyNowBehaviour){
                    window.AndroidBridge.setBuyNowBehaviour(JSON.stringify(obj));
                }
            } catch(e) {
                console.log("android error setBuyNowBehaviour");
                console.log(e);
            }
            if(!window.webkit && !window.AndroidBridge){
                console.log('setBuyNowBehaviour  '+JSON.stringify(obj));
            }
        },
        changeDisplayInfo:function(data){
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.changeDisplayInfo){
                    window.webkit.messageHandlers.changeDisplayInfo.postMessage(data);
                }
            } catch(e) {
                console.log("ios error changeDisplayInfo");
                console.log(e);
            }
            try {
                if(window && window.AndroidBridge && window.AndroidBridge.changeDisplayInfo){
                    window.AndroidBridge.changeDisplayInfo(JSON.stringify(data));
                }
            } catch(e) {
                console.log("android error changeDisplayInfo");
                console.log(e);
            }
            if(!window.webkit && !window.AndroidBridge){
                console.log('changeDisplayInfo '+JSON.stringify(data));
            }
        },
        addCustomerProperty:function(key,value){
            console.log(key,value);
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.addCustomerProperty){
                    window.webkit.messageHandlers.addCustomerProperty.postMessage({'key':key,'value':value});
                }
            } catch(e) {
                console.log("ios error addCustomerProperty");
                console.log(e);
            }
            try {
                if(window && window.AndroidBridge && window.AndroidBridge.addCustomerProperty){
                    window.AndroidBridge.addCustomerProperty(key,value);
                }
            } catch(e) {
                console.log("android error addCustomerProperty");
                console.log(e);
            }
            if(!window.webkit && !window.AndroidBridge){
                console.log('addCustomerProperty '+key+' '+ value);
            }
        },
        addToCart:function(variant){
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.addToCart){
                    window.webkit.messageHandlers.addToCart.postMessage(variant);
                }
            } catch(e) {
                console.log("ios error addToCart");
                console.log(e);
            }
            try {
                if(window && window.AndroidBridge && window.AndroidBridge.addToCart){
                    window.AndroidBridge.addToCart(JSON.stringify(variant));
                }
            } catch(e) {
                console.log("android error addToCart");
                console.log(e);
            }
            if(!window.webkit && !window.AndroidBridge){
                console.log('addToCart '+JSON.stringify(variant));
            }
        },
        changeAddToCartBehaviour:function(rules){

        },
        dataReceiver:function(data){
            console.log('dataReceiver');
        },
        requestData:function(data){
            console.log('requestData');
        },
        shareData:function(data){
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.shareData){
                    window.webkit.messageHandlers.shareData.postMessage({'data':data});
                }
            } catch(e) {
                console.log("ios error shareData");
                console.log(e);
            }
            try {
                if(window && window.AndroidBridge && window.AndroidBridge.shareData){
                    window.AndroidBridge.shareData(data);
                }
            } catch(e) {
                console.log("android error shareData");
                console.log(e);
            }
            if(!window.webkit && !window.AndroidBridge){
                console.log('shareData '+data);
            }
        },
        openFileChooser:function(){
            console.log('openFileChooser');
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.openFileChooser){
                    window.webkit.messageHandlers.openFileChooser.postMessage("");
                }
            } catch(e) {
                console.log("ios error openFileChooser");
                console.log(e);
            }
            try {
                if(window && window.AndroidBridge && window.AndroidBridge.openFileChooser){
                    window.AndroidBridge.openFileChooser();
                }
            } catch(e) {
                console.log("android error openFileChooser");
                console.log(e);
            }
            if(!window.webkit && !window.AndroidBridge){
                console.log('openFileChooser');
            }
        },
        callBridgeFunction:function(functionName, paramsIos, paramAndroid){
            try {
                if(window && window.AndroidBridge){
                    if(paramAndroid){
                        window.AndroidBridge[functionName](paramAndroid);
                    }
                    else{
                        window.AndroidBridge[functionName]();
                    }

                }
            } catch(e) {
                console.log("I got clicked android error");
                console.log(e);
            }

            try{
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers[functionName]){
                    if(paramsIos){
                        window.webkit.messageHandlers[functionName].postMessage(paramsIos);
                    }
                    else{
                        window.webkit.messageHandlers[functionName].postMessage();
                    }
                }
            }
            catch(e){
                console.log("I got clicked ios error");
                console.log(e);
            }
        },
        openProductDetail:function(product_id, title) {
            var jsonToSend ={
                product_id: product_id.toString(),
                title: title
            };

            this.callBridgeFunction('openProductDetails', jsonToSend, JSON.stringify(jsonToSend));
        },
        openCollection: function (collection_id, title) {
            var jsonToSend ={
                collection_id: collection_id.toString(),
                title: title
            };

            this.callBridgeFunction('openCollection', jsonToSend, JSON.stringify(jsonToSend));
        },
        addToCart:function(variantID){
            event.stopPropagation();
            this.callBridgeFunction('addToCart', variantID.toString(), variantID.toString());
        },
        openCart:function(event) {
            event.stopPropagation();
            this.callBridgeFunction('openCart');
        },
        operate:{
            '+': function(a, b) { return a + b },
            '<': function(a, b) { return a < b },
            '=': function(a, b) { return a < b },
            'equal': function(a, b) { return a == b; },
            'not_equal': function(a, b) { return a != b; }
        },
        isJson:function(str) {
            try {
                JSON.parse(str);
            } catch (e) {
                return false;
            }
            return true;
        },
        validateData:function(obj){
            try {
                if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.validateData){
                    window.webkit.messageHandlers.validateData.postMessage(obj);
                }
            } catch(e) {
                console.log("ios error validateData");
                console.log(e);
            }
            try {
                if(window && window.AndroidBridge && window.AndroidBridge.validateData){
                    window.AndroidBridge.validateData(JSON.stringify(obj));
                }
            } catch(e) {
                console.log("android error validateData");
                console.log(e);
            }
            // if(!window.webkit && !window.AndroidBridge){
            //
            // }
            console.log('validateData '+JSON.stringify(obj));
        },
        triggerEvent:function(el, event) {
            if ("createEvent" in document) {
                var evt = document.createEvent("HTMLEvents");
                evt.initEvent(event, false, true);
                el.dispatchEvent(evt);
            }
            else
                el.fireEvent(event);
        }
    };
})();
</script>
    
    <script>
       const resize_ob = new ResizeObserver(function(entries) {
  // since we are observing only a single element, so we access the first element in entries array
  let rect = entries[0].contentRect;

  // current width & height
  let width = rect.width;
  let height = rect.height;

  console.log('Current Width : ' + width);
  console.log('Current Height : ' + height);
    window.PlobalBridge.adjustWebViewHeight("main");
         try{
           if($("#attentive_overlay").length)  {console.log("attentive_overlay removed");$("#attentive_overlay").remove();}
         }catch(err){}
});

// start observing for resize
resize_ob.observe(document.querySelector("#main"));
    </script>