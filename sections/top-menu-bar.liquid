
{% if section.settings.top_menu_bar_menu != blank %}

<style>
  :root {
    --enable-sticky-menu-bar: {% if section.settings.top_menu_bar_position == 'non_sticky' or section.settings.top_menu_bar_position == 'sticky_desktop' %}0{% else %}1{% endif %};
  }

  #shopify-section-{{ section.id }} {
    --heading-color: {{ section.settings.text_color.red }}, {{ section.settings.text_color.green }}, {{ section.settings.text_color.blue }};
    --text-color: {{ section.settings.text_color.red }}, {{ section.settings.text_color.green }}, {{ section.settings.text_color.blue }};
    --primary-button-background: {{ section.settings.button_background.red }}, {{ section.settings.button_background.green }}, {{ section.settings.button_background.blue }};
    --primary-button-text-color: {{ section.settings.button_text_color.red }}, {{ section.settings.button_text_color.green }}, {{ section.settings.button_text_color.blue }};
    --section-background: {{ section.settings.background.red }}, {{ section.settings.background.green }}, {{ section.settings.background.blue }};
    z-index: 5; /* Make sure it goes over header */

    {%- if section.settings.top_menu_bar_position == 'sticky' or section.settings.top_menu_bar_position == 'sticky_mobile' -%}
      position: -webkit-sticky;
      position: sticky;
    {%- else -%}
      position: relative;
    {%- endif -%}

    top: 0;
    z-index: 0;
  }

  @media screen and (min-width: 741px) {
    :root {
      --enable-sticky-menu-bar: {% if section.settings.top_menu_bar_position == 'non_sticky' or section.settings.top_menu_bar_position == 'sticky_mobile' %}0{% else %}1{% endif %};
    }

    #shopify-section-{{ section.id }} {
      {%- if section.settings.top_menu_bar_position == 'sticky' or section.settings.top_menu_bar_position == 'sticky_desktop' -%}
        position: -webkit-sticky;
        position: sticky;
      {%- else -%}
        position: relative;
      {%- endif -%}
    }
  }
</style>

<section>

  <top-menu-bar class="top-menu-bar section__color-wrapper">

    <div class="container">

      <div class="top-menu-bar__inner">

        {%- capture sub_brand_links -%}
          
          {%- assign sub_brand_menu = section.settings.sub_brand_links -%}

          {%- for link in sub_brand_menu.links -%}

            {%- assign external_link = false -%}
            {%- if link.url contains "https://" or link.url contains "http://" -%}
              {%- assign external_link = true -%}
            {%- elsif link.url == '/' -%}
              {%- assign external_link = true -%}
            {%- elsif link.object.metafields.sub_brand.sub_brand != sub_brand -%}
              {%- assign external_link = true -%}
            {%- endif -%}

            <li class="header__linklist-item" data-item-title="{{ link.title | escape }}">
              <a class="header__linklist-link link--animated" {% render 'link-attributes', link: link %} href="{{ link.url }}" {% if link.links.size > 0 or mega_menu_block != '' %}aria-controls="desktop-menu-{{ forloop.index }}" aria-expanded="false"{% endif %} {% if external_link == true and section.settings.sub_brand_links_external_open_tab == true %}target="_blank"{% endif %}>
                {{- link.title -}}
                {% if external_link == true and section.settings.sub_brand_links_external_icon == true %}
                  <span class="link__icon">
                    {% render 'icon', icon: 'custom-external' %}
                  </span>
                {% endif %}
              </a>
            </li>
          {%- endfor -%}
        {%- endcapture -%}

        {%- if sub_brand_links != blank -%}
          <ul class="top-menu-bar__sub-brand-links header__linklist list--unstyled {% unless bottom_navigation %}hidden-pocket hidden-lap{% endunless %}" role="list">
            {{ sub_brand_links }}
          </ul>
        {%- endif -%}

        {%- capture info_links -%}
          {%- assign top_menu_bar_menu = section.settings.top_menu_bar_menu -%}
          {%- for link in top_menu_bar_menu.links -%}
            <li class="header__linklist-item" data-item-title="{{ link.title | escape }}">
              <a class="header__linklist-link link--animated" {% render 'link-attributes', link: link %} href="{{ link.url }}" {% if link.links.size > 0 or mega_menu_block != '' %}aria-controls="desktop-menu-{{ forloop.index }}" aria-expanded="false"{% endif %}>
                {{- link.title -}}
              </a>
            </li>
          {%- endfor -%}
        {%- endcapture -%}

        {%- if info_links != blank -%}
          <ul class="top-menu-bar__info-links header__linklist list--unstyled {% unless bottom_navigation %}hidden-pocket hidden-lap{% endunless %}" role="list">
            {{ info_links }}
          </ul>
        {%- endif -%}

      </div>

    </div>

  </top-menu-bar>

</section>

<script>
  document.documentElement.style.setProperty('--top-menu-bar-height', document.getElementById('shopify-section-{{ section.id }}').clientHeight + 'px');
</script>

{% endif %}

{% schema %}
{
  "name": "Top Menu Bar",
  "class": "shopify-section--top-menu-bar",
  "settings": [
    {
      "type": "header",
      "content": "Sub Brand",
      "info": "Settings for sub brand links. (e.g. Kyte Living)"
    },
    {
      "type": "link_list",
      "id": "sub_brand_links",
      "label": "Sub-brand Links",
      "info": "Important: This should also be set on the Header section."
    },
    {
      "type": "checkbox",
      "id": "sub_brand_links_external_open_tab",
      "label": "Open external links in new tab.",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "sub_brand_links_external_icon",
      "label": "Show icons on external links.",
      "default": true
    },
    {
      "type": "header",
      "content": "Top Bar Menu"
    },
    {
      "type": "link_list",
      "id": "top_menu_bar_menu",
      "label": "Menu"
    },
    {
      "type": "select",
      "id": "top_menu_bar_position",
      "label": "Position",
      "options": [
        {
          "value": "non_sticky",
          "label": "Non sticky"
        },
        {
          "value": "sticky_desktop",
          "label": "Sticky on desktop only"
        },
        {
          "value": "sticky_mobile",
          "label": "Sticky on mobile only"
        },
        {
          "value": "sticky",
          "label": "Sticky everywhere"
        }
      ],
      "default": "non_sticky"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background",
      "default": "#1e2d7d"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background",
      "default": "#1e2d7d"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text",
      "default": "#ffffff"
    }
  ],
  "default": {}
}
{% endschema %}