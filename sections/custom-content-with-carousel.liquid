{%- if section.settings.background_type == 'boxed' -%}
  {%- assign is_boxed = true -%}
{%- else -%}
  {%- assign is_boxed = false -%}
{%- endif -%}

{%- if section.settings.background != settings.background and section.settings.background != 'rgba(0,0,0,0)' -%}
  {%- assign blends_with_background = false -%}
{%- else -%}
  {%- assign blends_with_background = true -%}
{%- endif -%}

<style>
  #shopify-section-{{ section.id }} {
    {%- if section.settings.background == 'rgba(0,0,0,0)' -%}
      {%- assign section_background = settings.background -%}
    {%- else -%}
      {%- assign section_background = section.settings.background -%}
    {%- endif -%}

    {%- if section.settings.text_color == 'rgba(0,0,0,0)' -%}
      {%- assign heading_color = settings.heading_color -%}
      {%- assign text_color = settings.text_color -%}
    {%- else -%}
      {%- assign heading_color = section.settings.text_color -%}
      {%- assign text_color = section.settings.text_color -%}
    {%- endif -%}

    {%- if section.settings.button_background == 'rgba(0,0,0,0)' -%}
      {%- assign button_background = settings.primary_button_background -%}
    {%- else -%}
      {%- assign button_background = section.settings.button_background -%}
    {%- endif -%}

    {%- if section.settings.button_text_color == 'rgba(0,0,0,0)' -%}
      {%- assign button_text_color = settings.primary_button_text_color -%}
    {%- else -%}
      {%- assign button_text_color = section.settings.button_text_color -%}
    {%- endif -%}

    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
    --primary-button-background: {{ button_background.red }}, {{ button_background.green }}, {{ button_background.blue }};
    --primary-button-text-color: {{ button_text_color.red }}, {{ button_text_color.green }}, {{ button_text_color.blue }};

    --section-background: {{ section_background.red }}, {{ section_background.green }}, {{ section_background.blue }};
  }
</style>

{%- if section.settings.override_blend == true -%}
  {%- assign blends_with_background = false -%}
{%- endif -%}

{%- capture section_classes -%}
  {% render 'section-classes', section: section %}
{%- endcapture -%}

<section class="section {{ section_classes }} custom-content-with-carousel {% unless blends_with_background or is_boxed %}section--flush{% endunless %} section__color-wrapper">

  {% if section.settings.extra_code != blank %}
    {{ section.settings.extra_code }}
  {% endif %}

  {%- if section.settings.anchor -%}
    <a class="visually-hidden anchor" id="{{ section.settings.anchor }}" name="{{ section.settings.anchor }}"></a>
  {%- endif -%}
  
  <div class="{% if is_boxed %}container{% endif %} {% unless blends_with_background %}vertical-breather{% endunless %}">
    
    {%- assign text_position = section.settings.text_position -%}
    {%- assign text_alignment = section.settings.text_alignment -%}

    <div class="content-box content-box--{{ section.settings.text_width }} content-box--text-{{ text_alignment }} content-box--{{ text_position }} {% if section.settings.mobile_reduce_padding %}no-margin--mobile{% endif %}">

      {% comment %} Header {% endcomment %}

      {%- capture header_content -%}

        {%- capture header_images -%}

          {% for i in (1..3) %}
            {%- capture image_setting -%}header_image_{{ i }}{%- endcapture -%}
            {%- assign header_image = section.settings[image_setting] -%}
            {%- if header_image != blank -%}
              {{ header_image | image_url: width: 200 | image_tag: sizes: '200px', widths: '200,300,400', class: 'section-header-image', width: header_image.width, height: header_image.height, alt: header_image.alt }}
            {%- endif -%}
          {% endfor %}

        {%- endcapture -%}

        {%- if header_images != blank -%}
          <div class="section-header-images">
            {{ header_images }}
          </div>
        {%- endif -%}

        {%- if section.settings.layout == "horizontal" -%}
          <div class="text-container">
        {%- endif -%}

          {%- if section.settings.subheading != blank -%}
            <h2 class="{% if section.settings.subheading_style contains 'heading' %}heading{% endif %} {{ section.settings.subheading_style }}">{{ section.settings.subheading | escape }}</h2>
          {%- endif -%}

          {%- if section.settings.title != blank -%}
            <h3 class="heading {{ section.settings.heading_style }}">{{ section.settings.title | escape }}</h3>
          {%- endif -%}

        {%- if section.settings.layout == "horizontal" -%}
          </div>
        {%- endif -%}

        {%- if section.settings.layout == "horizontal" -%}
          <div class="text-container">
        {%- endif -%}

          {%- if section.settings.intro_text != blank -%}
            <div class="{{ section.settings.intro_text_style }}">
              {{- section.settings.intro_text -}}
            </div>
          {%- endif -%}

          {%- if section.settings.content != blank -%}
            <div class="{{ section.settings.content_style }}">
              {{- section.settings.content -}}
            </div>
          {%- endif -%}

        {%- if section.settings.layout == "horizontal" -%}
          </div>
        {%- endif -%}

      {%- endcapture -%}

      {%- if header_content != blank -%}
        
        {%- if section.settings.layout == "horizontal" -%}
          {%- assign layout_class = 'horizontal-header horizontal-header--equal' -%}
        {%- else -%}
          {%- assign layout_class = 'section__header' -%}
        {%- endif -%}

        <header class="{{ layout_class }}">
          {{ header_content }}
        </header>

      {%- endif -%}


      {% comment %} Content {% endcomment %}

      {%- capture section_content -%}

        {% if section.blocks.size > 0 %}

          <native-carousel class="usp-icons-carousel">

            <div class="usp-icons-carousel__list hide-scrollbar">

              {% for block in section.blocks %}

                <native-carousel-item {% unless forloop.first %}hidden{% endunless %} id="block-{{ section.id }}-{{ block.id }}" class="usp-icons-carousel__item" {{ block.shopify_attributes }}>

                  <div class="usp-icons-carousel__item-inner">

                    {%- capture icon -%}
                      {%- if block.settings.icon_code != blank -%}
                        {{ block.settings.icon_code }}
                      {%- elsif block.settings.icon != blank -%}
                        {%- assign mobile_size = 80 | times: block.settings.icon.aspect_ratio | ceil -%}
                        {%- assign tablet_size = 80 | times: block.settings.icon.aspect_ratio | ceil -%}
                        {%- assign desktop_size = 80 | times: block.settings.icon.aspect_ratio | ceil -%}
                        <img loading="lazy" sizes="(max-width: 740px) {{ mobile_size }}px, (max-width: 999px) {{ tablet_size }}px, {{ desktop_size }}px" class="special-promotion-item__image" {% render 'image-attributes', image: block.settings.icon, sizes: '300,400,500,600,700,800,900,1000,1100,1200,1300,1400,1500,1600,1700,1800,1900,2000' %}>
                      {%- endif -%}
                    {%- endcapture -%}

                    {%- if icon != blank -%}
                      <div class="usp-icons-carousel__icon">
                        {{ icon }}
                      </div>
                    {%- endif -%}

                    <div class="usp-icons-carousel__item-description">

                      <h5 class="usp-icons-carousel__item-heading heading">{{ block.settings.title }}</h5>
                      <div class="usp-icons-carousel__item-content minor">{{ block.settings.content }}</div>

                    </div>

                  </div>

                </native-carousel-item>

              {% endfor %}

            </div>

            {%- if section.blocks.size > 1 -%}
              <page-dots class="usp-icons-carousel__dots dots-nav dots-nav--centered hidden-lap-and-up">
                {%- for block in section.blocks -%}
                  <button class="dots-nav__item tap-area" {% if forloop.first %}aria-current="true"{% endif%}>
                    <span class="visually-hidden">{{ 'general.accessibility.go_to_slide' | t: num: forloop.index }}</span>
                  </button>
                {%- endfor -%}
              </page-dots>
            {%- endif -%}

          </native-carousel>

        {% endif %}

      {%- endcapture -%}

      {%- if section_content != blank -%}
        <div class="section__content container text-container {% if section.settings.mobile_reduce_padding %}no-padding--mobile{% endif %}">
          {{ section_content }}
        </div>
      {%- endif -%}


      {% comment %} Footer {% endcomment %}

      {%- capture footer_content -%}

        {%- capture footer_images -%}

          {% for i in (1..3) %}
            {%- capture image_setting -%}footer_image_{{ i }}{%- endcapture -%}
            {%- assign footer_image = section.settings[image_setting] -%}
            {%- if footer_image != blank -%}
              {{ footer_image | image_url: width: 200 | image_tag: sizes: '200px', widths: '200,300,400', class: 'section-footer-image', width: footer_image.width, height: footer_image.height, alt: footer_image.alt }}
            {%- endif -%}
          {% endfor %}

        {%- endcapture -%}

        {%- if footer_images != blank -%}
          <div class="section-footer-images">
            {{ footer_images }}
          </div>
        {%- endif -%}

        {%- if section.settings.button_text != blank -%}
          <div class="button-wrapper">
            <a href="{{ section.settings.button_link }}" class="button {{ section.settings.button_style }} {{ section.settings.button_size }}">
              <span class="button__text">{{ section.settings.button_text | escape }}</span>
              {% if block.settings.button_icon != "" %}
                <span class="button__icon">{%- include 'icon' with section.settings.button_icon, direction_aware: true -%}</span>
              {% endif %}
            </a>
          </div>
        {%- endif -%}

        {%- if section.settings.buttons_text != blank -%}
          <div class="{{ section.settings.buttons_text_style }}">
            {{ section.settings.buttons_text }}
          </div>
        {%- endif -%}        
      {%- endcapture -%}

      {%- if footer_content != blank -%}
        <div class="section__footer container">
          {{ footer_content }}
        </div>
      {%- endif -%}

    </div>

  </div>

</section>

{% schema %}
{
  "name": "🪁 Content with Carousel",
  "class": "shopify-section--content-carousel",
  "settings": [
    {
      "type": "header",
      "content": "🪁 Kyte Settings"
    },
    {
      "type": "select",
      "id": "background_type",
      "label": "Background",
      "options": [
        {
          "value": "full_width",
          "label": "Full width"
        },
        {
          "value": "boxed",
          "label": "Boxed"
        }
      ],
      "default": "full_width"
    },
    {
      "type": "select",
      "id": "text_width",
      "label": "Text width",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        },
        {
          "value": "larger",
          "label": "Larger"
        },
        {
          "value": "fill",
          "label": "Fill screen"
        }
      ],
      "default": "medium"
    },
    {
      "type": "select",
      "id": "text_position",
      "label": "Text position",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "Text alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "select",
      "id": "layout",
      "label": "Layout",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "horizontal",
          "label": "Horizontal"
        },
      ],
      "default": "horizontal"
    },
    {
      "type": "checkbox",
      "id": "mobile_reduce_padding",
      "label": "Reduce padding on mobile.",
      "default": true
    },
    {
      "type": "header",
      "content": "Section Settings"
    },
    {
      "type": "checkbox",
      "id": "use_padding",
      "label": "Use Padding"
    },
    {
      "type": "checkbox",
      "id": "box_shadow",
      "label": "Shadow"
    },
    {
      "type": "header",
      "content": "Header Images",
    },
    {
      "type": "image_picker",
      "id": "header_image_1",
      "label": "Image 1"
    },
    {
      "type": "image_picker",
      "id": "header_image_2",
      "label": "Image 2"
    },
    {
      "type": "image_picker",
      "id": "header_image_3",
      "label": "Image 3"
    },
    {
      "type": "header",
      "content": "Subheading",
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "Text",
      "default": "Subheading"
    },
    {
      "type": "select",
      "id": "subheading_style",
      "label": "Style",
      "options": [
        {
          "group": "Subheading",
          "value": "",
          "label": "Default"
        },
        {
          "group": "Subheading",
          "value": "heading--small",
          "label": "Subheading"
        },
        {
          "group": "Subheading",
          "value": "heading--xsmall",
          "label": "Subheading - Small"
        },
        {
          "group": "Text",
          "value": "text--xxsmall",
          "label": "Tiny"
        },
        {
          "group": "Text",
          "value": "text--xsmall",
          "label": "Extra Small"
        },
        {
          "group": "Text",
          "value": "text--small",
          "label": "Small"
        },
        {
          "group": "Text",
          "value": "text--large",
          "label": "Large"
        }
      ],
      "default": "heading--small"
    },
    {
      "type": "header",
      "content": "Heading",
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Talk about your brand"
    },
    {
      "type": "select",
      "id": "heading_style",
      "label": "Style",
      "default": "h2",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "group": "Subheading",
          "value": "heading--small",
          "label": "Subheading"
        },
        {
          "group": "Subheading",
          "value": "heading--xsmall",
          "label": "Subheading - Small"
        },
        {
          "group": "Heading",
          "value": "h1",
          "label": "Heading 1"
        },
        {
          "group": "Heading",
          "value": "h2",
          "label": "Heading 2"
        },
        {
          "group": "Heading",
          "value": "h3",
          "label": "Heading 3"
        },
        {
          "group": "Heading",
          "value": "h4",
          "label": "Heading 4"
        },
        {
          "group": "Heading",
          "value": "h5",
          "label": "Heading 5"
        },
        {
          "group": "Heading",
          "value": "h6",
          "label": "Heading 6"
        }
      ]
    },
    {
      "type": "header",
      "content": "Intro Text",
    },
    {
      "type": "richtext",
      "id": "intro_text",
      "label": "Text"
    },
    {
      "type": "select",
      "id": "intro_text_style",
      "label": "Style",
      "default": "text--large",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "group": "Text",
          "value": "text--xxsmall",
          "label": "Tiny"
        },
        {
          "group": "Text",
          "value": "text--xsmall",
          "label": "Extra Small"
        },
        {
          "group": "Text",
          "value": "text--small",
          "label": "Small"
        },
        {
          "group": "Text",
          "value": "text--large",
          "label": "Large"
        }
      ]
    },
    {
      "type": "header",
      "content": "Content",
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "Text"
    },
    {
      "type": "select",
      "id": "content_style",
      "label": "Style",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "group": "Text",
          "value": "text--xxsmall",
          "label": "Tiny"
        },
        {
          "group": "Text",
          "value": "text--xsmall",
          "label": "Extra Small"
        },
        {
          "group": "Text",
          "value": "text--small",
          "label": "Small"
        },
        {
          "group": "Text",
          "value": "text--large",
          "label": "Large"
        }
      ],
      "default": ""
    },
    {
      "type": "header",
      "content": "Footer Images",
    },
    {
      "type": "image_picker",
      "id": "footer_image_1",
      "label": "Image 1"
    },
    {
      "type": "image_picker",
      "id": "footer_image_2",
      "label": "Image 2"
    },
    {
      "type": "image_picker",
      "id": "footer_image_3",
      "label": "Image 3"
    },
    {
      "type": "header",
      "content": "Button",
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    },
    {
      "type": "select",
      "id": "button_size",
      "label": "Button size",
      "options": [
        {
          "value": "button--small",
          "label": "Small"
        },
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "button--large",
          "label": "Large"
        }
      ],
      "default": ""
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "Button style",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "button--primary",
          "label": "Primary"
        },
        {
          "value": "button--secondary",
          "label": "Secondary"
        }
      ],
      "default": ""
    },
    {
      "type": "select",
      "id": "button_icon",
      "label": "Button Icon",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "nav-arrow-right",
          "label": "Nav Arrow Right",
          "group": "Navigation"
        },
        {
          "value": "nav-arrow-right-small",
          "label": "Nav Arrow Right - Small",
          "group": "Navigation"
        }
      ]
    },
    {
      "type": "liquid",
      "id": "button_extra_attributes",
      "label": "Button Attributes"
    },
    {
      "type": "header",
      "content": "Text Below Buttons",
    },
    {
      "type": "richtext",
      "id": "buttons_text",
      "label": "Text"
    },
    {
      "type": "select",
      "id": "buttons_text_style",
      "label": "Style",
      "options": [
        {
          "group": "Subheading",
          "value": "",
          "label": "Default"
        },
        {
          "group": "Subheading",
          "value": "heading--small",
          "label": "Subheading"
        },
        {
          "group": "Subheading",
          "value": "heading--xsmall",
          "label": "Subheading - Small"
        },
        {
          "group": "Text",
          "value": "text--xxsmall",
          "label": "Tiny"
        },
        {
          "group": "Text",
          "value": "text--xsmall",
          "label": "Extra Small"
        },
        {
          "group": "Text",
          "value": "text--small",
          "label": "Small"
        },
        {
          "group": "Text",
          "value": "text--large",
          "label": "Large"
        }
      ],
      "default": "heading--small"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "header",
      "content": "🪁 Advanced"
    },
    {
      "type": "text",
      "id": "styling_class",
      "label": "Styling Class",
      "info": "For advanced use."
    },
    {
      "type": "text",
      "id": "anchor",
      "label": "Anchor",
      "info": "Anchor name used for jump links.",
      "placeholder": "#anchor"
    },
    {
      "type": "checkbox",
      "id": "section_extra_padding",
      "label": "Extra Vertical Padding"
    },
    {
      "type": "checkbox",
      "id": "override_blend",
      "label": "Stop section merging with previous section",
      "default": false,
      "info": "If the section \"merges\" with the previous and next sections, this separates them again."
    },
    {
      "type": "liquid",
      "id": "extra_code",
      "label": "Extra Code",
      "info": "Misc. Liquid code for embellishments."
    }
  ],
  "blocks": [
    {
      "type": "item",
      "name": "Item",
      "settings": [
        {
          "type": "image_picker",
          "id": "icon",
          "label": "Icon"
        },
        {
          "type": "liquid",
          "id": "icon_code",
          "label": "Icon (SVG)"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Your title"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Content",
          "default": "<p>Short content about a unique selling proposition of your product.</p>"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "🪁 Content with Carousel",
      "settings": {}
    }
  ]
}
{% endschema %}