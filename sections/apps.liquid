{%- if section.settings.background_type == 'boxed' -%}
  {%- assign is_boxed = true -%}
  {%- assign blends_with_background = false -%}
{%- else -%}
  {%- assign is_boxed = false -%}
{%- endif -%}

{%- if section.settings.background != settings.background and section.settings.background != 'rgba(0,0,0,0)' -%}
  {%- assign blends_with_background = false -%}
{%- else -%}
  {%- assign blends_with_background = true -%}
{%- endif -%}

{%- if section.settings.override_blend == true -%}
  {%- assign blends_with_background = false -%}
{%- endif -%}


<style>

  {% comment %} Background {% endcomment %}

  {%- if section.settings.background != blank and section.settings.background != 'rgba(0,0,0,0)' -%}
    {%- assign section_background = section.settings.background -%}
  {%- endif -%}

  {%- if section.settings.inner_background != blank and section.settings.inner_background != 'rgba(0,0,0,0)' -%}
    {%- assign section_inner_background = section.settings.inner_background -%}
  {%- endif -%}

  {% comment %} Content {% endcomment %}

  {%- if section.settings.heading_color != blank and section.settings.heading_color != 'rgba(0,0,0,0)' -%}
    {%- assign heading_color = section.settings.heading_color -%}
  {%- endif -%}

  {%- if section.settings.subheading_color != blank and section.settings.subheading_color != 'rgba(0,0,0,0)' -%}
    {%- assign subheading_color = section.settings.subheading_color -%}
  {%- endif -%}

  {%- if section.settings.text_color != blank and section.settings.text_color != 'rgba(0,0,0,0)' -%}
    {%- assign text_color = section.settings.text_color -%}
  {%- endif -%}

  {% comment %} Button {% endcomment %}

  {%- if section.settings.button_background != blank and section.settings.button_background != 'rgba(0,0,0,0)' -%}
    {%- assign button_background = section.settings.button_background -%}
  {%- endif -%}

  {%- if section.settings.button_text_color != blank and section.settings.button_text_color != 'rgba(0,0,0,0)' -%}
    {%- assign button_text_color = settings.primary_button_text_color -%}
  {%- endif -%}


  {% if section_inner_background != blank and section_inner_background != 'rgba(0,0,0,0)' and is_boxed %}
    {%- assign has_inner_background = true -%}
  {% endif %}

  {% if is_boxed and has_inner_background %}
    {%- assign blends_with_background = false -%}
  {% endif %}

  #shopify-section-{{ section.id }} {

    {% if heading_color != blank %}
      --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    {% endif %}

    {% if text_color != blank %}
      --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
    {% endif %}

    {% if button_background != blank %}
      --primary-button-background: {{ button_background.red }}, {{ button_background.green }}, {{ button_background.blue }};
    {% endif %}

    {% if button_text_color != blank %}
      --primary-button-text-color: {{ button_text_color.red }}, {{ button_text_color.green }}, {{ button_text_color.blue }};
    {% endif %}

    --section-background: {{ section_background.red }}, {{ section_background.green }}, {{ section_background.blue }};

  }

  #shopify-section-{{ section.id }} .button {

    --button-background: {{ button_background.red }}, {{ button_background.green }}, {{ button_background.blue }};
    --button-text-color: {{ button_text_color.red }}, {{ button_text_color.green }}, {{ button_text_color.blue }};

  }

  #shopify-section-{{ section.id }} .section__color-wrapper--boxed {

    {% if has_inner_background == true %}
      --section-background: {{ section_inner_background.red }}, {{ section_inner_background.green }}, {{ section_inner_background.blue }};
    {% endif %}

  }

</style>

{%- assign text_position = section.settings.text_position -%}
{%- assign text_alignment = section.settings.text_alignment -%}

{%- capture section_classes -%}
  {% render 'section-classes', section: section %}
{%- endcapture -%}

<section class="section {{ section_classes }} 
  section--apps
  {% if is_boxed %}
    section__color-wrapper
    {% if has_inner_background %}section--use-padding{% endif %}
  {% else %}
    section--flush
  {% endif %}">

  {% if section.settings.extra_code != blank %}
    {{ section.settings.extra_code }}
  {% endif %}

  {%- if section.settings.anchor -%}
    <a class="visually-hidden anchor" id="{{ section.settings.anchor }}" name="{{ section.settings.anchor }}"></a>
  {%- endif -%}

  <div {% if is_boxed %}class="container"{% endif %}>
    <div class="{% if is_boxed and has_inner_background %}section__color-wrapper section__color-wrapper--boxed{% endif %}">
      <div class="{% unless is_boxed %}container{% endunless %} {% unless blends_with_background %}vertical-breather vertical-breather--extra-tight{% endunless %}">

        {%- if section.settings.subheading != blank or section.settings.title != blank or section.settings.content != blank -%}

          <div class="content-box content-box--text-{{ text_alignment }} content-box--{{ text_position }} content-box--{{ section.settings.text_width }} text-container">

            {%- if section.settings.subheading != blank -%}
              <h2 class="{% if section.settings.subheading_style contains 'heading' %}heading{% endif %} {{ section.settings.subheading_style }}">{{ section.settings.subheading | escape }}</h2>
            {%- endif -%}

            {%- if section.settings.title != blank -%}
              <h3 class="heading {{ section.settings.heading_style }}">{{ section.settings.title | escape }}</h3>
            {%- endif -%}

            {%- if section.settings.intro_text != blank -%}
              <div class="{{ section.settings.intro_text_style }}">
                {{- section.settings.intro_text -}}
              </div>
            {%- endif -%}

            {%- if section.settings.content != blank -%}
              <div class="{{ section.settings.content_style }}">
                {{- section.settings.content -}}
              </div>
            {%- endif -%}

          </div>

        {%- endif -%}

        <div class="{% if section.settings.include_horizontal_margins %}container{% endif %} {% if section.settings.mobile_no_horizontal_padding %}container--no-horizontal-padding--mobile{% endif %} {% if section.settings.include_vertical_margins %}vertical-breather{% endif %}">
          {%- for block in section.blocks -%}
            {%- render block -%}
          {%- endfor -%}
        </div>

        {%- if section.settings.button_text != blank -%}

          <div class="content-box content-box--text-{{ text_alignment }} content-box--{{ text_position }} text-container">
            <div class="button-wrapper">
              <a href="{{ section.settings.button_link }}" class="button button--primary">{{ section.settings.button_text | escape }}</a>
            </div>
          </div>

        {%- endif -%}

      </div>
    </div>
  </div>

</section>
{% schema %}
{
  "name": "Apps",
  "class": "shopify-section--apps",
  "disabled_on": {
    "groups": ["custom.overlay"]
  },
  "settings": [
    {
      "type": "header",
      "content": "🪁 Kyte Settings"
    },
    {
      "type": "header",
      "content": "Section Settings"
    },
    {
      "type": "select",
      "id": "text_width",
      "label": "Text width",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        },
        {
          "value": "fill",
          "label": "Fill screen"
        }
      ],
      "default": "medium"
    },
    {
      "type": "select",
      "id": "text_position",
      "label": "Text position",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "Text alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "select",
      "id": "background_type",
      "label": "Background",
      "options": [
        {
          "value": "full_width",
          "label": "Full width"
        },
        {
          "value": "boxed",
          "label": "Boxed"
        }
      ],
      "default": "full_width"
    },
    {
      "type": "header",
      "content": "Subheading",
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "Text",
      "default": "Subheading"
    },
    {
      "type": "select",
      "id": "subheading_style",
      "label": "Style",
      "options": [
        {
          "group": "Subheading",
          "value": "",
          "label": "Default"
        },
        {
          "group": "Subheading",
          "value": "heading--small",
          "label": "Subheading"
        },
        {
          "group": "Subheading",
          "value": "heading--xsmall",
          "label": "Subheading - Small"
        },
        {
          "group": "Text",
          "value": "text--xxsmall",
          "label": "Tiny"
        },
        {
          "group": "Text",
          "value": "text--xsmall",
          "label": "Extra Small"
        },
        {
          "group": "Text",
          "value": "text--small",
          "label": "Small"
        },
        {
          "group": "Text",
          "value": "text--large",
          "label": "Large"
        }
      ],
      "default": "heading--small"
    },
    {
      "type": "header",
      "content": "Heading",
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Talk about your brand"
    },
    {
      "type": "select",
      "id": "heading_style",
      "label": "Style",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "group": "Subheading",
          "value": "heading--small",
          "label": "Subheading"
        },
        {
          "group": "Subheading",
          "value": "heading--xsmall",
          "label": "Subheading - Small"
        },
        {
          "group": "Heading",
          "value": "h1",
          "label": "Heading 1"
        },
        {
          "group": "Heading",
          "value": "h2",
          "label": "Heading 2"
        },
        {
          "group": "Heading",
          "value": "h3",
          "label": "Heading 3"
        },
        {
          "group": "Heading",
          "value": "h4",
          "label": "Heading 4"
        },
        {
          "group": "Heading",
          "value": "h5",
          "label": "Heading 5"
        },
        {
          "group": "Heading",
          "value": "h6",
          "label": "Heading 6"
        }
      ]
    },
    {
      "type": "header",
      "content": "Intro Text",
    },
    {
      "type": "richtext",
      "id": "intro_text",
      "label": "Text"
    },
    {
      "type": "select",
      "id": "intro_text_style",
      "label": "Style",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "group": "Text",
          "value": "text--xxsmall",
          "label": "Tiny"
        },
        {
          "group": "Text",
          "value": "text--xsmall",
          "label": "Extra Small"
        },
        {
          "group": "Text",
          "value": "text--small",
          "label": "Small"
        },
        {
          "group": "Text",
          "value": "text--large",
          "label": "Large"
        }
      ]
    },
    {
      "type": "header",
      "content": "Content",
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "Text",
      "default": "<p>Use this text to share information about your brand with your customers. Describe a product, share announcements, or welcome customers to your store.</p>"
    },
    {
      "type": "select",
      "id": "content_style",
      "label": "Style",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "group": "Text",
          "value": "text--xxsmall",
          "label": "Tiny"
        },
        {
          "group": "Text",
          "value": "text--xsmall",
          "label": "Extra Small"
        },
        {
          "group": "Text",
          "value": "text--small",
          "label": "Small"
        },
        {
          "group": "Text",
          "value": "text--large",
          "label": "Large"
        }
      ],
      "default": ""
    },
    {
      "type": "header",
      "content": "Text Below Buttons",
    },
    {
      "type": "richtext",
      "id": "buttons_text",
      "label": "Text"
    },
    {
      "type": "select",
      "id": "buttons_text_style",
      "label": "Style",
      "options": [
        {
          "group": "Subheading",
          "value": "",
          "label": "Default"
        },
        {
          "group": "Subheading",
          "value": "heading--small",
          "label": "Subheading"
        },
        {
          "group": "Subheading",
          "value": "heading--xsmall",
          "label": "Subheading - Small"
        },
        {
          "group": "Text",
          "value": "text--xxsmall",
          "label": "Tiny"
        },
        {
          "group": "Text",
          "value": "text--xsmall",
          "label": "Extra Small"
        },
        {
          "group": "Text",
          "value": "text--small",
          "label": "Small"
        },
        {
          "group": "Text",
          "value": "text--large",
          "label": "Large"
        }
      ],
      "default": "heading--small"
    },
    {
      "type": "header",
      "content": "Button",
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    },
    {
      "type": "select",
      "id": "button_size",
      "label": "Button size",
      "options": [
        {
          "value": "button--small",
          "label": "Small"
        },
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "button--large",
          "label": "Large"
        }
      ],
      "default": ""
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "Button style",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "button--primary",
          "label": "Primary"
        },
        {
          "value": "button--secondary",
          "label": "Secondary"
        }
      ],
      "default": ""
    },
    {
      "type": "select",
      "id": "button_icon",
      "label": "Button Icon",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "nav-arrow-right",
          "label": "Nav Arrow Right",
          "group": "Navigation"
        },
        {
          "value": "nav-arrow-right-small",
          "label": "Nav Arrow Right - Small",
          "group": "Navigation"
        }
      ]
    },
    {
      "type": "liquid",
      "id": "button_extra_attributes",
      "label": "Button Attributes"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "inner_background",
      "label": "Inner Background (Boxed Only)",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text"
    },
    {
      "type": "header",
      "content": "Advanced"
    },
    {
      "type": "checkbox",
      "id": "mobile_no_horizontal_padding",
      "label": "Mobile - remove horizontal padding",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "include_horizontal_margins",
      "label": "Include horizontal margins",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "include_vertical_margins",
      "label": "Include vertical margins",
      "default": true
    },
    {
      "type": "text",
      "id": "styling_class",
      "label": "Styling Class",
      "info": "For advanced use."
    },
    {
      "type": "text",
      "id": "anchor",
      "label": "Anchor",
      "info": "Anchor name used for jump links.",
      "placeholder": "#anchor"
    },
    {
      "type": "checkbox",
      "id": "use_padding",
      "label": "Use Padding"
    },
    {
      "type": "checkbox",
      "id": "box_shadow",
      "label": "Shadow"
    },
    {
      "type": "checkbox",
      "id": "section_extra_padding",
      "label": "Extra Vertical Padding"
    },
    {
      "type": "checkbox",
      "id": "override_blend",
      "label": "Stop section merging with previous section",
      "default": false,
      "info": "If the section \"merges\" with the previous and next sections, this separates them again."
    },
    {
      "type": "liquid",
      "id": "extra_code",
      "label": "Extra Code",
      "info": "Misc. Liquid code for embellishments."
    }
  ],
  "blocks": [
    {
      "type": "@app"
    }
  ],
  "presets": [
    {
      "name": "Apps"
    }
  ]
}
{% endschema %}
