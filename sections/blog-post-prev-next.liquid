{%- if blog.previous_article or blog.next_article -%}
  <div class="article__prev-next vertical-breather vertical-breather--tight {% if settings.background == settings.secondary_background %}vertical-breather--margin{% endif %}">
    <div class="container">
      <header class="section__header text-container">
        <h2 class="heading h3">{{ 'article.general.continue_reading' | t }}</h2>
      </header>

      <div class="scroller">
        <div class="scroller__inner">
          <article-list {% if settings.stagger_blog_posts_apparition %}stagger-apparition{% endif %} class="article-list article-list--scrollable">
            {%- if blog.next_article -%}
              {%- render 'article-item', article: blog.next_article, heading_size: 'h5' -%}
            {%- endif -%}

            {%- if blog.previous_article -%}
              {%- render 'article-item', article: blog.previous_article, heading_size: 'h5' -%}
            {%- endif -%}
          </article-list>
        </div>
      </div>
    </div>
  </div>
{%- endif -%}

{% schema %}
{
  "name": "Blog post navigation",
  "class": "shopify-section--blog-post-prev-next",
  "settings": [
    {
      "type": "paragraph",
      "content": "Previous and next article will be displayed (if they exists)."
    }
  ]
}
{% endschema %}