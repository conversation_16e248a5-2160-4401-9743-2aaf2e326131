{%- if section.settings.background != settings.background and section.settings.background != 'rgba(0,0,0,0)' -%}
  {%- assign blends_with_background = false -%}
{%- else -%}
  {%- assign blends_with_background = true -%}
{%- endif -%}

<style>
  #shopify-section-{{ section.id }} {
    {%- if section.settings.background == 'rgba(0,0,0,0)' -%}
      {%- assign section_background = settings.background -%}
    {%- else -%}
      {%- assign section_background = section.settings.background -%}
    {%- endif -%}

    {%- if section.settings.text_color == 'rgba(0,0,0,0)' -%}
      {%- assign heading_color = settings.heading_color -%}
      {%- assign text_color = settings.text_color -%}
    {%- else -%}
      {%- assign heading_color = section.settings.text_color -%}
      {%- assign text_color = section.settings.text_color -%}
    {%- endif -%}

    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
    --prev-next-button-background: {{ settings.background.red }}, {{ settings.background.green }}, {{ settings.background.blue }};
    --prev-next-button-color: {{ settings.text_color.red }}, {{ settings.text_color.green }}, {{ settings.text_color.blue }};

    --section-background: {{ section_background.red }}, {{ section_background.green }}, {{ section_background.blue }};
    --section-products-per-row: 2;
  }

  @media screen and (min-width: 741px) {
    #shopify-section-{{ section.id }} {
      --section-products-per-row: 3;
    }
  }

  @media screen and (min-width: 1200px) {
    #shopify-section-{{ section.id }} {
      --section-products-per-row: 4;
    }
  }
</style>

<product-recommendations section-id="{{ section.id }}" intent="related" product-id="{{ product.id }}" recommendations-count="{{ section.settings.recommendations_count }}" class="section {% unless blends_with_background %}section--flush{% endunless %}">
  {%- if recommendations.performed and recommendations.products.size > 0 -%}
    <div class="section__color-wrapper">
      <div class="container {% unless blends_with_background %}vertical-breather{% endunless %}">
        {%- if section.settings.title != blank or section.settings.subheading != blank -%}
          <header class="section__header">
            <div class="text-container">
              {%- if section.settings.subheading != blank -%}
                <h2 class="heading heading--small">{{ section.settings.subheading }}</h2>
              {%- endif -%}

              {%- if section.settings.title != blank -%}
                <h3 class="heading h3">{{ section.settings.title }}</h3>
              {%- endif -%}
            </div>
          </header>
        {%- endif -%}

        <product-list {% if settings.stagger_products_apparition %}stagger-apparition{% endif %} class="product-list product-list--center">
          {%- assign smallest_image_aspect_ratio = 0 -%}

          <div class="scroller">
            <div class="product-list__inner product-list__inner--scroller hide-scrollbar">
              {%- for product in recommendations.products -%}
                {%- assign smallest_image_aspect_ratio = smallest_image_aspect_ratio | at_least: product.featured_media.aspect_ratio -%}

                {%- capture sizes_attribute -%}(max-width: 740px) 75vw, min({{ 100.0 | divided_by: 4 | ceil }}vw, {{ 1520.0 | divided_by: 4 | ceil }}px){%- endcapture -%}
                {%- render 'product-item--slider', product: product, sizes_attribute: sizes_attribute, reveal: settings.stagger_products_apparition, aspect_ratio: section.settings.aspect_ratio -%}
              {%- endfor -%}
            </div>
          </div>

          {%- if recommendations.products_count > 4 -%}
            <prev-next-buttons class="product-list__prev-next hidden-pocket" style="--smallest-image-aspect-ratio: {{ smallest_image_aspect_ratio }}">
              <button class="product-list__arrow prev-next-button prev-next-button--prev" disabled data-action="prev">
                <span class="visually-hidden">{{ 'general.accessibility.previous' | t }}</span>
                {%- render 'icon' with 'nav-arrow-left', block: true, direction_aware: true -%}
              </button>

              <button class="product-list__arrow prev-next-button prev-next-button--next" data-action="next">
                <span class="visually-hidden">{{ 'general.accessibility.next' | t }}</span>
                {%- render 'icon' with 'nav-arrow-right', block: true, direction_aware: true -%}
              </button>
            </prev-next-buttons>
          {%- endif -%}
        </product-list>
      </div>
    </div>
  {%- endif -%}
</product-recommendations>

{% schema %}
{
  "name": "Related products",
  "class": "shopify-section--product-recommendations",
  "enabled_on": {
    "templates": ["product"]
  },
  "settings": [
    {
      "type": "select",
      "id": "aspect_ratio",
      "label": "Product Image Aspect Ratio",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "natural",
          "label": "Natural"
        },
        {
          "value": "short",
          "label": "Short (4:3)"
        },
        {
          "value": "square",
          "label": "Square (1:1)"
        },
        {
          "value": "tall",
          "label": "Tall (2:3)"
        }
      ],
      "default": ""
    },
    {
      "type": "paragraph",
      "content": "Dynamic recommendations change and improve with time. Create manual product recommendations using the Shopify Search & Discovery app. [Learn more](https://help.shopify.com/en/manual/online-store/search-and-discovery/product-recommendations)."
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "Subheading"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "You may also like"
    },
    {
      "type": "range",
      "id": "recommendations_count",
      "min": 4,
      "max": 10,
      "label": "Recommendations count",
      "default": 4
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text",
      "default": "rgba(0,0,0,0)"
    }
  ],
  "presets": [
    {
      "name": "Related products"
    }
  ]
}
{% endschema %}