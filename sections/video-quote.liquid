{%- if section.settings.background != settings.background and section.settings.background != 'rgba(0,0,0,0)' -%}
  {%- assign blends_with_background = false -%}
{%- else -%}
  {%- assign blends_with_background = true -%}
{%- endif -%}

{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
  CSS
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}

<style>
  #shopify-section-{{ section.id }} {
    {%- if section.settings.background == 'rgba(0,0,0,0)' -%}
      {%- assign section_background = settings.background -%}
    {%- else -%}
      {%- assign section_background = section.settings.background -%}
    {%- endif -%}

    {%- if section.settings.text_color == 'rgba(0,0,0,0)' -%}
      {%- assign heading_color = settings.heading_color -%}
      {%- assign text_color = settings.text_color -%}
    {%- else -%}
      {%- assign heading_color = section.settings.text_color -%}
      {%- assign text_color = section.settings.text_color -%}
    {%- endif -%}

    {%- if section.settings.background_type == 'boxed' -%}
      {%- assign play_button_background = section_background -%}
      {%- assign play_button_arrow = text_color -%}
    {%- else -%}
      {%- assign play_button_background = text_color -%}
      {%- assign play_button_arrow = settings.heading_color -%}
    {%- endif -%}

    --heading-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
    --play-button-background: {{ play_button_background.red }}, {{ play_button_background.green }}, {{ play_button_background.blue }};
    --play-button-arrow: {{ play_button_arrow.red }}, {{ play_button_arrow.green }}, {{ play_button_arrow.blue }};

    --section-accent-background: {{ section.settings.accent_background.red }} {{ section.settings.accent_background.green }} {{ section.settings.accent_background.blue }} / {{ section.settings.accent_background.alpha }};

    --section-background: {{ section_background.red }}, {{ section_background.green }}, {{ section_background.blue }};
  }
</style>

{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
  JavaScript: This section composes the "external-video" element, but does not have dedicated custom element
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}

<section class="section custom-video-quote {% if section.settings.background_overlap == true %}video-quote-background--overlap{% endif %} {% if section.settings.background_type == 'full_width' or section.settings.background_type == 'boxed' and blends_with_background == false %}section--flush{% endif %}">
  {%- capture section_header -%}
    {%- if section.settings.subheading != blank or section.settings.title != blank -%}
      <div class="{% if section.settings.background_type == 'boxed' %}section__header{% endif %} text-container">
        
        {%- if section.settings.subheading != blank -%}
          <h2 class="heading heading--small">{{ section.settings.subheading | escape }}</h2>
        {%- endif -%}

        {%- if section.settings.text != blank -%}
          <div class="gallery__caption-content h3">
            {{ section.settings.text }}
          </div>
        {%- endif -%}

        <div class="gallery__caption-cite">
          
          {%- if section.settings.author != blank -%}
            <p class="gallery__caption-name heading heading--xsmall">{{ section.settings.author | escape }}</p>
          {%- endif -%}

           {%- if section.settings.location != blank -%}
            <p class="gallery__caption-location">{{ section.settings.location | escape }}</p>
          {%- endif -%}

        </div>
        
      </div>
    {%- endif -%}
  {%- endcapture -%}

  {%- capture video_content -%}
    <div class="video-section video-section--{% if section.settings.background_type == 'boxed' %}boxed{% else %}full{% endif %} video-section--{{ section.settings.video_size }}">
      <external-video {% if section.settings.autoplay %}autoplay{% endif %} provider="{{ section.settings.video_url.type | escape }}" class="video-wrapper {% if section.settings.autoplay %}video-wrapper--inert{% endif %} {% if section.settings.background_type == 'full_width' and section.settings.video_size != 'auto' %}video-wrapper--cover{% endif %}">
        <div class="video-wrapper__poster">
          {%- if section.settings.image != blank -%}
            <img class="video-wrapper__poster-image" loading="lazy" sizes="(max-width: 740px) calc(100vw - 24px * 2), var(--container-max-width)" {% render 'image-attributes', image: section.settings.image, sizes: '400,500,600,700,800,900,1000,1200,1400,1600,1800,2000,2200,2400,2600,2800' %}>
          {%- else -%}
            {{- 'lifestyle-1' | placeholder_svg_tag: 'video-wrapper__poster-image placeholder-background' -}}
          {%- endif -%}

          <div class="video-wrapper__poster-content {% if section.settings.background_type != 'boxed' %}content-box content-box--large content-box--text-center{% endif %}">
            {%- unless section.settings.autoplay -%}
              <button type="button" class="video-wrapper__play-button video-wrapper__play-button--large video-wrapper__play-button--ripple" title="{{ 'general.accessibility.play_video' | t | escape }}">
                {%- render 'icon' with 'play', width: 72, height: 72 -%}
              </button>
            {%- endunless -%}

            {%- if section.settings.background_type != 'boxed' -%}
              {{- section_header -}}
            {%- endif -%}
          </div>
        </div>

        <template>
          {%- if section.settings.video_url.type == 'youtube' -%}
            <iframe title="{{ section.settings.title | escape }}" src="https://www.youtube.com/embed/{{ section.settings.video_url.id }}?playsinline=1&autoplay=1{% if section.settings.autoplay %}&controls=0&mute=1&loop=1{% endif %}&playlist={{ section.settings.video_url.id }}&enablejsapi=1&rel=0&modestbranding=1&origin={{ 'https://' | append: request.host | url_encode }}" allow="autoplay; encrypted-media" allowfullscreen="allowfullscreen"></iframe>
            {%- elsif section.settings.video_url.type == 'vimeo' -%}
            <iframe title="{{ section.settings.title | escape }}" src="https://player.vimeo.com/video/{{ section.settings.video_url.id }}?autoplay=1&autopause=1{% if section.settings.autoplay %}&background=1&loop=1&muted=1{% endif %}&transparent=0&responsive=1&portrait=0&title=0&byline=0&color={{ settings.text_color | remove_first: '#' }}" allow="autoplay; encrypted-media;" allowfullscreen="allowfullscreen"></iframe>
          {%- endif -%}
        </template>
      </external-video>
    </div>
  {%- endcapture -%}

  {%- if section.settings.background_type == 'boxed' -%}
    <div class="section__color-wrapper">
      <div class="container">
        <div {% unless blends_with_background %}class="vertical-breather"{% endunless %}>
          {{- video_content -}}
          {{- section_header -}}
        </div>
      </div>
    </div>
  {%- else -%}
    {{- video_content -}}
  {%- endif -%}
</section>

{% schema %}
{
  "name": "🪁 Video Quote",
  "class": "shopify-section--video",
  "settings": [
    {
      "type": "text",
      "id": "subheading",
      "label": "Subheading"
    },
    {
      "type": "textarea",
      "id": "text",
      "label": "Content"
    },
    {
      "type": "text",
      "id": "author",
      "label": "Author"
    },
    {
      "type": "text",
      "id": "location",
      "label": "Location"
    },
    {
      "type": "video_url",
      "id": "video_url",
      "accept": ["vimeo", "youtube"],
      "label": "Video",
      "default": "https://www.youtube.com/watch?v=_9VUPq3SxOc"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Cover image",
      "info": "2000 x 1125px .jpg recommended. Required if you turn off autoplay."
    },
    {
      "type": "select",
      "id": "background_type",
      "label": "Video mode",
      "options": [
        {
          "value": "full_width",
          "label": "Full width"
        },
        {
          "value": "boxed",
          "label": "Boxed"
        }
      ],
      "default": "full_width"
    },
    {
      "type": "checkbox",
      "id": "background_overlap",
      "label": "Background overlap",
      "info": "Add an accent background color.",
      "default": true
    },
    {
      "type": "select",
      "id": "video_size",
      "label": "Video size",
      "options": [
        {
          "value": "auto",
          "label": "Original ratio"
        },
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ],
      "default": "auto"
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Autoplay",
      "info": "Video is muted automatically to allow autoplay.",
      "default": true
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background",
      "default": "rgba(0,0,0,0)",
      "info": "Only used for boxed background."
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "accent_background",
      "label": "Accent background",
      "default": "rgba(0,0,0,0)"
    }
  ],
  "presets": [
    {
      "name": "🪁 Video Quote"
    }
  ]
}
{% endschema %}