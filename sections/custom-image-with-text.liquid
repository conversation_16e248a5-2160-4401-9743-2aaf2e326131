{%- if section.settings.background_type == 'boxed' -%}
  {%- assign is_boxed = true -%}
{%- else -%}
  {%- assign is_boxed = false -%}
{%- endif -%}

<style>
  #shopify-section-{{ section.id }} {

    {% comment %} Background {% endcomment %}

    {%- if section.settings.background != blank and section.settings.background != 'rgba(0,0,0,0)' -%}
      {%- assign section_background = section.settings.background -%}
    {%- endif -%}
    
    {% comment %} Content {% endcomment %}

    {%- if section.settings.heading_color != blank and section.settings.heading_color != 'rgba(0,0,0,0)' -%}
      {%- assign heading_color = section.settings.heading_color -%}
    {%- else -%}
      {%- assign heading_color = settings.heading_color -%}
    {%- endif -%}

    {%- if section.settings.subheading_color != blank and section.settings.subheading_color != 'rgba(0,0,0,0)' -%}
      {%- assign subheading_color = section.settings.subheading_color -%}
    {%- else -%}
      {%- assign subheading_color = settings.subheading_color -%}
    {%- endif -%}

    {%- if section.settings.text_color != blank and section.settings.text_color != 'rgba(0,0,0,0)' -%}
      {%- assign text_color = section.settings.text_color -%}
    {%- else -%}
      {%- assign text_color = settings.text_color -%}
    {%- endif -%}

    {% comment %} Buttons {% endcomment %}

    {%- if section.settings.button_background == 'rgba(0,0,0,0)' -%}
      {%- assign button_background = settings.primary_button_background -%}
    {%- else -%}
      {%- assign button_background = section.settings.button_background -%}
    {%- endif -%}

    {%- if section.settings.button_text_color == 'rgba(0,0,0,0)' -%}
      {%- assign button_text_color = settings.primary_button_text_color -%}
    {%- else -%}
      {%- assign button_text_color = section.settings.button_text_color -%}
    {%- endif -%}

    --subheading-color: {{ subheading_color.red }}, {{ subheading_color.green }}, {{ subheading_color.blue }};
    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
    
    --primary-button-background: {{ button_background.red }}, {{ button_background.green }}, {{ button_background.blue }};
    --primary-button-text-color: {{ button_text_color.red }}, {{ button_text_color.green }}, {{ button_text_color.blue }};

    --section-accent-background: {{ section.settings.accent_background.red }} {{ section.settings.accent_background.green }} {{ section.settings.accent_background.blue }} / {{ section.settings.accent_background.alpha }};
    --section-autoplay-duration: {{ section.settings.cycle_speed }}s;
    --section-animation-play-state: paused;

    {% if section_background %}
      --section-background: {{ section.settings.background.red }} {{ section.settings.background.green }} {{ section.settings.background.blue }} / {{ section.settings.accent_background.alpha }};
    {% endif %}

  }

  {%- if section.settings.background_overlap != 'both' -%}
    /* We have to remove some extra spacing adding in non-overlap mode */
    @media screen and (max-width: 999px) {
      {%- if section.settings.background_overlap == 'image' -%}
        {%- unless is_boxed -%}
          #shopify-section-{{ section.id }} .section {
            margin-top: 0;
          }

          #shopify-section-{{ section.id }} .image-with-text {
            margin-bottom: calc(-1 * var(--vertical-breather)) !important;
          }
        {%- else -%}
          #shopify-section-{{ section.id }} .image-with-text {
            margin-bottom: calc(-1 * var(--container-gutter)) !important;
          }
        {%- endunless -%}
      {%- elsif section.settings.background_overlap == 'text' -%}
        {%- unless is_boxed -%}
          #shopify-section-{{ section.id }} .section {
            margin-bottom: 0;
          }

          #shopify-section-{{ section.id }} .image-with-text {
            margin-top: calc(-1 * var(--vertical-breather)) !important;
          }
        {%- else -%}
          #shopify-section-{{ section.id }} .image-with-text {
            margin-top: calc(-1 * var(--container-gutter)) !important;
          }
        {%- endunless -%}
      {%- endif -%}
    }
  {%- endif -%}
</style>

{%- if section.settings.override_blend == true -%}
  {%- assign blends_with_background = false -%}
{%- endif -%}

{%- capture section_classes -%}
  {% render 'section-classes', section: section %}
{%- endcapture -%}

<section class="section {{ section_classes }} custom-image-with-text {% if section_background %}section__color-wrapper{% endif %} {% if section.settings.background_overlap == 'both' and is_boxed == false %}section--flush{% endif %}">

  {% if section.settings.extra_code != blank %}
    {{ section.settings.extra_code }}
  {% endif %}

  {%- if section.settings.anchor -%}
    <a class="visually-hidden anchor" id="{{ section.settings.anchor }}" name="{{ section.settings.anchor }}"></a>
  {%- endif -%}
  
  <div {% if is_boxed %}class="container"{% endif %}>
    {%- assign first_block = section.blocks.first -%}

    <image-with-text {% if section.settings.reveal_on_scroll %}reveal-on-scroll{% endif %} class="image-with-text {% if is_boxed %}image-with-text--boxed{% endif %} {% if section.settings.image_position == 'right' %}image-with-text--reverse{% endif %} image-with-text--overlap-{{ section.settings.background_overlap }}" style="--image-aspect-ratio: {{ first_block.settings.image.aspect_ratio | default: 1 }}; --image-max-height: {{ first_block.settings.image.height }}px">
      <div class="{% unless is_boxed %}container{% endunless %}">
        <div class="image-with-text__wrapper">
          {%- assign first_image_aspect_ratio = first_block.settings.image.aspect_ratio | default: 1 -%}
          {%- assign image_count = 0 -%}

          <div class="image-with-text__image-wrapper">

            {%- for block in section.blocks -%}
              {%- if block.settings.image != blank -%}
                {%- capture id_attribute -%}block-{{ block.id }}-{{ block.settings.image.id }}{%- endcapture -%}
                {%- assign is_hidden = true -%}

                {%- if image_count == 0 -%}
                  {%- assign is_hidden = false -%}
                {%- endif -%}

                {%- if section.settings.reveal_on_scroll -%}
                  {%- if is_hidden -%}
                    {{- block.settings.image | image_url: width: block.settings.image.width | image_tag: id: id_attribute, loading: 'lazy', sizes: '(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px', widths: '600,700,800,1000,1200,1400', reveal-visibility: true, hidden: true, class: 'image-with-text__image' -}}
                  {%- else -%}
                    {{- block.settings.image | image_url: width: block.settings.image.width | image_tag: id: id_attribute, loading: 'lazy', sizes: '(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px', widths: '600,700,800,1000,1200,1400', reveal-visibility: true, class: 'image-with-text__image' -}}
                  {%- endif -%}
                {%- else -%}
                  {%- if is_hidden -%}
                    {{- block.settings.image | image_url: width: block.settings.image.width | image_tag: id: id_attribute, loading: 'lazy', sizes: '(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px', widths: '600,700,800,1000,1200,1400', hidden: true, class: 'image-with-text__image' -}}
                  {%- else -%}
                    {{- block.settings.image | image_url: width: block.settings.image.width | image_tag: id: id_attribute, loading: 'lazy', sizes: '(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px', widths: '600,700,800,1000,1200,1400', class: 'image-with-text__image' -}}
                  {%- endif -%}
                {%- endif -%}

                {%- assign image_count = image_count | plus: 1 -%}
              {%- endif -%}
            {%- endfor -%}

            {%- if image_count == 0 -%}
              {%- if section.settings.reveal_on_scroll -%}
                {{- 'image' | placeholder_svg_tag: 'image-with-text__image placeholder-background' | replace: '<svg', '<svg reveal-visibility' -}}
              {%- else -%}
                {{- 'image' | placeholder_svg_tag: 'image-with-text__image placeholder-background' -}}
              {%- endif -%}
            {%- endif -%}
          </div>

          <div class="image-with-text__content-wrapper">
            <div class="image-with-text__content-list">
              {%- for block in section.blocks -%}
                <image-with-text-item {% if block.settings.image != blank and image_count > 1 %}attached-image="block-{{ block.id }}-{{ block.settings.image.id }}"{% endif %} {% unless forloop.first %}hidden{% endunless %} class="image-with-text__content text-container text--{{ section.settings.text_alignment }}" {{ block.shopify_attributes }}>
                  {%- if block.settings.subheading != blank -%}
                    <h2 class="heading heading--small">
                      <split-lines {% if section.settings.reveal_on_scroll or forloop.first != true %}reveal{% endif %}>{{ block.settings.subheading | escape }}</split-lines>
                    </h2>
                  {%- endif -%}

                  {%- if block.settings.title != blank -%}
                    <h3 class="heading h2">
                      <split-lines {% if section.settings.reveal_on_scroll or forloop.first != true %}reveal{% endif %}>{{ block.settings.title | escape }}</split-lines>
                    </h3>
                  {%- endif -%}

                  <div class="image-with-text__text-wrapper" {% if section.settings.reveal_on_scroll or forloop.first != true %}reveal{% endif %}>
                    {%- if block.settings.content != blank -%}
                      {{- block.settings.content -}}
                    {%- endif -%}

                    {%- if block.settings.button_text != blank or block.settings.drawer_button_text != blank -%}
                      <div class="button-wrapper">

                        {% comment %}
                          Link Button
                        {% endcomment %}

                        {%- if block.settings.button_text != blank -%}
                          
                          {% if block.settings.button_extra_attributes != blank %}
                            <button class="button {{ block.settings.button_size }} {{ block.settings.button_style }}" {{ block.settings.button_extra_attributes }}>
                          {% else %}
                            <a href="{{ block.settings.button_link }}" class="button {{ block.settings.button_size }} {{ block.settings.button_style }}">
                          {% endif %}

                            <span class="button__text">{{ block.settings.button_text }}</span>
                            {% if block.settings.button_icon != "" %}
                              <span class="button__icon">{%- include 'icon' with block.settings.button_icon, block: true, direction_aware: true -%}</span>
                            {% endif %}

                          {% if block.settings.button_extra_attributes != blank %}
                            </button>
                          {% else %}
                            </a>
                          {% endif %}

                        {% endif %}

                        {% comment %}
                          Drawer/Popover Button
                        {% endcomment %}

                        {% if block.settings.drawer_button_text != blank and block.settings.drawer_button_page != blank %}
                          
                          <button is="toggle-button" class="button {{ block.settings.button_size }} {{ block.settings.drawer_button_style }} hidden-tablet-and-up" aria-controls="section-{{ section.id }}-block-{{ block.id }}-popover" aria-expanded="false">
                            <span class="button__text">{{ block.settings.drawer_button_text }}</span>
                            {% if block.settings.drawer_button_icon != "" %}
                              <span class="button__icon">{%- include 'icon' with block.settings.drawer_button_icon, block: true, direction_aware: true -%}</span>
                            {% endif %}
                          </button>
                          <button is="toggle-button" class="button {{ block.settings.button_size }} {{ block.settings.drawer_button_style }} hidden-phone" aria-controls="section-{{ section.id }}-block-{{ block.id }}-drawer" aria-expanded="false">
                            <span class="button__text">{{ block.settings.drawer_button_text }}</span>
                            {% if block.settings.drawer_button_icon != "" %}
                              <span class="button__icon">{%- include 'icon' with block.settings.drawer_button_icon, block: true, direction_aware: true -%}</span>
                            {% endif %}
                          </button>
                          
                          {%- comment -%}Drawer is for tablet and desktop{%- endcomment -%}
                          <drawer-content id="section-{{ section.id }}-block-{{ block.id }}-drawer" class="drawer drawer--large hidden-phone">
                            <span class="drawer__overlay"></span>

                            <header class="drawer__header">
                              <p class="drawer__title heading h6">{{- block.settings.drawer_button_page.title -}}</p>

                              <button type="button" class="drawer__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
                                {%- render 'icon' with 'close' -%}
                              </button>
                            </header>

                            <div class="drawer__content drawer__content--padded-start">
                              <div class="rte">
                                {{- block.settings.drawer_button_page.content -}}
                              </div>
                            </div>
                          </drawer-content>

                          {%- comment -%}Popover is for mobile{%- endcomment -%}
                          <popover-content id="section-{{ section.id }}-block-{{ block.id }}-popover" class="popover hidden-tablet-and-up">
                            <span class="popover__overlay"></span>

                            <header class="popover__header">
                              <p class="popover__title heading h3">{{- block.settings.drawer_button_page.title -}}</p>

                              <button type="button" class="popover__close-button tap-area tap-area--large" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
                                {%- render 'icon' with 'close' -%}
                              </button>
                            </header>

                            <div class="popover__content">
                              <div class="rte">
                                {{- block.settings.drawer_button_page.content -}}
                              </div>
                            </div>
                          </popover-content>

                        {% endif %}

                      </div>
                    {%- endif -%}
                  </div>
                </image-with-text-item>
              {%- endfor -%}
            </div>

            {%- comment -%}If we have two blocks we show the navigation{%- endcomment -%}
            {%- if section.blocks.size > 1 -%}
              <page-dots animation-timer class="image-with-text__navigation">
                {%- for block in section.blocks -%}
                  <button type="button" class="image-with-text__navigation-item" {% if forloop.first %}aria-current="true"{% endif %}>
                    <span class="heading heading--small">{{- block.settings.tab_label | escape -}}</span>
                  </button>
                {%- endfor -%}
              </page-dots>
            {%- endif -%}
          </div>
        </div>
      </div>
    </image-with-text>
  </div>
</section>

{% schema %}
{
  "name": "🪁 Image with text",
  "class": "shopify-section--image-with-text",
  "max_blocks": 2,
  "disabled_on": {
    "groups": ["header", "custom.overlay"]
  },
  "settings": [
    {
      "type": "header",
      "content": "🪁 Kyte Settings"
    },
    {
      "type": "checkbox",
      "id": "use_padding",
      "label": "Use Padding"
    },
    {
      "type": "checkbox",
      "id": "box_shadow",
      "label": "Shadow"
    },
    {
      "type": "checkbox",
      "id": "section_extra_padding",
      "label": "Extra Vertical Padding"
    },
    {
      "type": "header",
      "content": "Section Settings"
    },
    {
      "type": "checkbox",
      "id": "reveal_on_scroll",
      "label": "Reveal on scroll",
      "info": "Show animation when section becomes visible.",
      "default": true
    },
    {
      "type": "select",
      "id": "background_type",
      "label": "Background",
      "options": [
        {
          "value": "full_width",
          "label": "Full width"
        },
        {
          "value": "boxed",
          "label": "Boxed"
        }
      ],
      "default": "full_width"
    },
    {
      "type": "select",
      "id": "background_overlap",
      "label": "Background overlap",
      "info": "Add an accent background color below.",
      "options": [
        {
          "value": "image",
          "label": "Image"
        },
        {
          "value": "text",
          "label": "Text"
        },
        {
          "value": "both",
          "label": "Both"
        }
      ],
      "default": "image"
    },
    {
      "type": "select",
      "id": "image_position",
      "label": "Desktop image position",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "left"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "Text alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "range",
      "id": "cycle_speed",
      "min": 3,
      "max": 20,
      "step": 1,
      "unit": "s",
      "label": "Change text every",
      "default": 8
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background"
    },
    {
      "type": "color",
      "id": "accent_background",
      "label": "Accent background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "header",
      "content": "🪁 Advanced"
    },
    {
      "type": "text",
      "id": "styling_class",
      "label": "Styling Class",
      "info": "For advanced use."
    },
    {
      "type": "text",
      "id": "anchor",
      "label": "Anchor",
      "info": "Anchor name used for jump links.",
      "placeholder": "#anchor"
    },
    {
      "type": "checkbox",
      "id": "contain",
      "label": "Contain and clip content",
      "default": false,
      "info": "Check this if this section has a scrolling text animation or botanical illustrations."
    },
    {
      "type": "checkbox",
      "id": "override_blend",
      "label": "Stop section merging with previous section",
      "default": false,
      "info": "If the section \"merges\" with the previous and next sections, this separates them again."
    },
    {
      "type": "liquid",
      "id": "extra_code",
      "label": "Extra Code",
      "info": "Misc. Liquid code for embellishments."
    }
  ],
  "blocks": [
    {
      "type": "item",
      "name": "Item",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "1400 x 1400px .jpg recommended"
        },
        {
          "type": "text",
          "id": "tab_label",
          "label": "Tab label",
          "info": "Only shown if 2 text blocks are set",
          "default": "Item"
        },
        {
          "type": "text",
          "id": "subheading",
          "label": "Subheading",
          "default": "Subheading"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Image with text"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Content",
          "default": "<p>Use image with text to give your customers insight into your brand. Select imagery and text that relates to your style and story.</p>"
        },
        {
          "type": "text",
          "id": "button_text",
          "label": "Button text"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "Button link"
        },
        {
          "type": "select",
          "id": "button_size",
          "label": "Button size",
          "options": [
            {
              "value": "button--small",
              "label": "Small"
            },
            {
              "value": "",
              "label": "Default"
            },
            {
              "value": "button--large",
              "label": "Large"
            }
          ],
          "default": ""
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Button style",
          "options": [
            {
              "value": "",
              "label": "Default"
            },
            {
              "value": "button--primary",
              "label": "Primary"
            },
            {
              "value": "button--secondary",
              "label": "Secondary"
            }
          ],
          "default": ""
        },
        {
          "type": "select",
          "id": "button_icon",
          "label": "Button Icon",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "nav-arrow-right",
              "label": "Nav Arrow Right",
              "group": "Navigation"
            },
            {
              "value": "nav-arrow-right-small",
              "label": "Nav Arrow Right - Small",
              "group": "Navigation"
            }
          ]
        },
        {
          "type": "liquid",
          "id": "button_extra_attributes",
          "label": "Button Attributes"
        },
        {
          "type": "header",
          "content": "Drawer Button"
        },
        {
          "type": "text",
          "id": "drawer_button_text",
          "label": "Button text"
        },
        {
          "type": "select",
          "id": "drawer_button_size",
          "label": "Button size",
          "options": [
            {
              "value": "button--small",
              "label": "Small"
            },
            {
              "value": "",
              "label": "Default"
            },
            {
              "value": "button--large",
              "label": "Large"
            }
          ],
          "default": ""
        },
        {
          "type": "select",
          "id": "drawer_button_style",
          "label": "Button style",
          "options": [
            {
              "value": "",
              "label": "Default"
            },
            {
              "value": "button--primary",
              "label": "Primary"
            },
            {
              "value": "button--secondary",
              "label": "Secondary"
            }
          ],
          "default": ""
        },
        {
          "type": "select",
          "id": "drawer_button_icon",
          "label": "Button Icon",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "nav-arrow-right",
              "label": "Nav Arrow Right",
              "group": "Navigation"
            },
            {
              "value": "nav-arrow-right-small",
              "label": "Nav Arrow Right - Small",
              "group": "Navigation"
            }
          ]
        },
        {
          "type": "page",
          "id": "drawer_button_page",
          "label": "Page",
          "info": "Feature a page for size option"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "🪁 Image with text",
      "settings": {},
      "blocks": [
        {
          "type": "item"
        }
      ]
    }
  ]
}
{% endschema %}