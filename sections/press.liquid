{%- if section.settings.background != settings.background and section.settings.background != 'rgba(0,0,0,0)' -%}
  {%- assign blends_with_background = false -%}
{%- else -%}
  {%- assign blends_with_background = true -%}
{%- endif -%}

<style>
  #shopify-section-{{ section.id }} {
    {%- if section.settings.background == 'rgba(0,0,0,0)' -%}
      {%- assign section_background = settings.background -%}
    {%- else -%}
      {%- assign section_background = section.settings.background -%}
    {%- endif -%}

    {%- if section.settings.text_color == 'rgba(0,0,0,0)' -%}
      {%- assign heading_color = settings.heading_color -%}
      {%- assign text_color = settings.text_color -%}
    {%- else -%}
      {%- assign heading_color = section.settings.text_color -%}
      {%- assign text_color = section.settings.text_color -%}
    {%- endif -%}

    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
    --section-background: {{ section_background.red }}, {{ section_background.green }}, {{ section_background.blue }};
  }
</style>

<section class="section {% unless blends_with_background %}section--flush{% endunless %}">
  <div class="section__color-wrapper {% unless blends_with_background %}vertical-breather{% endunless %}">
    <div class="container">
      <press-list {% if section.settings.reveal_on_scroll %}reveal-on-scroll{% endif %} class="press-list">
        <div class="press-list__wrapper">
          {%- for block in section.blocks -%}
            <press-item {% unless forloop.first %}hidden{% endunless %} id="block-{{ section.id }}-{{ block.id }}" class="press-list__item" {{ block.shopify_attributes }}>
              <blockquote class="press-list__item-content blockquote blockquote--center">
                <split-lines {% if section.settings.reveal_on_scroll or forloop.first == false %}reveal-visibility{% endif %}>{{ block.settings.content | escape }}</split-lines>
              </blockquote>
            </press-item>
          {%- endfor -%}
        </div>

        <div class="press-list__logo-list-wrapper hide-scrollbar">
          <page-dots align-selected class="press-list__logo-list">
            {%- for block in section.blocks -%}
              <button class="press-list__logo-item tap-area" {% if forloop.first %}aria-current="true"{% endif %}>
                {%- if block.settings.logo != blank -%}
                  <span class="visually-hidden">{{ 'general.accessibility.go_to_slide' | t: num: forloop.index }}</span>

                  {%- capture image_widths -%}{{ block.settings.logo_width }}, {{ block.settings.logo_width | times: 2 }}, {{ block.settings.logo_width | times: 3 }}{%- endcapture -%}
                  {%- capture sizes_attribute -%}{{ block.settings.logo_width }}px{%- endcapture -%}
                  {%- capture style_attribute -%}max-width: {{ block.settings.logo_width }}px{%- endcapture -%}

                  {{- block.settings.logo | image_url: width: block.settings.logo.width | image_tag: loading: 'lazy', style: style_attribute, sizes: sizes_attribute, widths: image_widths, class: 'press-list__logo-image' -}}
                {%- else -%}
                  <span class="heading h6">Logo {{ forloop.index }}</span>
                {%- endif -%}
              </button>
            {%- endfor -%}
          </page-dots>
        </div>
      </press-list>
    </div>
  </div>
</section>

{% schema %}
{
  "name": "Press",
  "class": "shopify-section--press",
  "disabled_on": {
    "groups": ["header", "custom.overlay"]
  },
  "max_blocks": 6,
  "blocks": [
    {
      "type": "item",
      "name": "Press testimonial",
      "settings": [
        {
          "type": "image_picker",
          "id": "logo",
          "label": "Logo",
          "info": "300 x 75px .png recommended"
        },
        {
          "type": "range",
          "id": "logo_width",
          "min": 25,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Logo width",
          "default": 100
        },
        {
          "type": "text",
          "id": "content",
          "label": "Content",
          "default": "Write some content about what they are saying about your store."
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "checkbox",
      "id": "reveal_on_scroll",
      "label": "Reveal on scroll",
      "info": "Show animation when section becomes visible.",
      "default": true
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text",
      "default": "rgba(0,0,0,0)"
    }
  ],
  "presets": [
    {
      "name": "Press",
      "blocks": [
        {
          "type": "item"
        }
      ]
    }
  ]
}
{% endschema %}