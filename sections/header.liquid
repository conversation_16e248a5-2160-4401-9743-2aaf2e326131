{%- comment -%}
IMPLEMENTATION NOTE: the header in Focal is pretty complex as it allows a lot of different layouts. In order to make
  this code as efficient as possible and avoid as many reflows, we are using a lot of different CSS variables. If you
  need to touch this code, make sure to do it with EXTRA CARE as it may have some unwanted side effects
{%- endcomment -%}

<style>
  :root {
    --enable-sticky-header: {% if section.settings.enable_sticky_header %}1{% else %}0{% endif %};
    --enable-transparent-header: {% if request.page_type == 'index' and section.settings.enable_transparent_header %}1{% else %}0{% endif %};
    --loading-bar-background: {{ settings.header_text_color.red }}, {{ settings.header_text_color.green }}, {{ settings.header_text_color.blue }}; /* Prevent the loading bar to be invisible */
  }

  #shopify-section-{{ section.id }} {
    {%- assign header_border_color = settings.header_background | color_mix: settings.header_text_color, 85 -%}

    --header-background: {{ settings.header_background.red }}, {{ settings.header_background.green }}, {{ settings.header_background.blue }};
    --header-text-color: {{ settings.header_text_color.red }}, {{ settings.header_text_color.green }}, {{ settings.header_text_color.blue }};
    --header-border-color: {{ header_border_color.red }}, {{ header_border_color.green }}, {{ header_border_color.blue }};
    --reduce-header-padding: {% if section.settings.reduce_desktop_padding %}1{% else %}0{% endif %};

    {%- if section.settings.enable_sticky_header -%}
      position: -webkit-sticky;
      position: sticky;
      top: 0;
    {%- else -%}
      position: relative;
    {%- endif -%}

    z-index: 4;
  }

  {%- if section.settings.enable_sticky_header -%}
    .shopify-section--announcement-bar ~ #shopify-section-{{ section.id }} {
      top: calc(var(--enable-sticky-announcement-bar, 0) * var(--announcement-bar-height, 0px));
    }
  {%- endif -%}

  {%- if request.page_type == 'index' and section.settings.enable_transparent_header -%}
    #shopify-section-{{ section.id }} {
      margin-bottom: calc(-1 * (var(--header-height, 0px) + var(--enable-sticky-announcement-bar, 0) * var(--announcement-bar-height, 0px)));
    }

    .js #shopify-section-{{ section.id }} .header--transparent {
      {%- if settings.header_text_color == section.settings.transparent_header_text_color -%}
        {%- assign header_bubble_text_color = settings.header_background -%}
      {%- else -%}
        {%- assign header_bubble_text_color = settings.header_text_color -%}
      {%- endif -%}

      --header-background: transparent;
      --header-text-color: {{ section.settings.transparent_header_text_color.red }}, {{ section.settings.transparent_header_text_color.green }}, {{ section.settings.transparent_header_text_color.blue }};
      --header-border-color: {{ section.settings.transparent_header_text_color.red }}, {{ section.settings.transparent_header_text_color.green }}, {{ section.settings.transparent_header_text_color.blue }}, 0.15;
      --header-transparent-bubble-text-color: {{ header_bubble_text_color.red }}, {{ header_bubble_text_color.green }}, {{ header_bubble_text_color.blue }};
    }
  {%- endif -%}

  #shopify-section-{{ section.id }} .header__logo-image {
    max-width: {{ section.settings.mobile_logo_max_width }}px;
  }

  @media screen and (min-width: 741px) {
    #shopify-section-{{ section.id }} .header__logo-image {
      max-width: {{ section.settings.logo_max_width }}px;
    }
  }

  @media screen and (min-width: 1200px) {
    {%- if section.settings.header_layout == 'logo_left_navigation_inline' -%}
      /* For this navigation we have to move the logo at the first and add a margin */
      [dir="rtl"] .header__logo {
        margin-left: 40px;
      }

      [dir="ltr"] .header__logo {
        margin-right: 40px;
      }

      .header__logo {
        order: -1;
      }

      @media screen and (min-width: 741px) {
        .header__secondary-links {
          max-width: max-content;
          margin-inline-start: 48px;
        }
      }
    {%- endif -%}

    {%- if section.settings.header_layout == 'logo_left_navigation_center' -%}
      /* For this navigation we have to move the logo and make sure the navigation takes the whole width */
      .header__logo {
        order: -1;
        flex: 1 1 0;
      }

      .header__inline-navigation {
        flex: 1 1 auto;
        justify-content: center;
        max-width: max-content;
        margin-inline: 48px;
      }
    {%- endif -%}
  }

  {%- if request.page_type == '404' -%}
    .shopify-section--404 {
      /* For the 404 page, we re-use the same colors as the header for design reason */
      --background: {{ settings.header_background.red }}, {{ settings.header_background.green }}, {{ settings.header_background.blue }};
      --heading-color: {{ settings.header_text_color.red }}, {{ settings.header_text_color.green }}, {{ settings.header_text_color.blue }};
      --text-color: {{ settings.header_text_color.red }}, {{ settings.header_text_color.green }}, {{ settings.header_text_color.blue }};
      --primary-button-background: {{ settings.header_text_color.red }}, {{ settings.header_text_color.green }}, {{ settings.header_text_color.blue }};
      --primary-button-text-color: {{ settings.header_background.red }}, {{ settings.header_background.green }}, {{ settings.header_background.blue }};
    }
  {%- endif -%}
</style>

<store-header {% if section.settings.enable_sticky_header %}sticky{% endif %} {% if request.page_type == 'index' and section.settings.enable_transparent_header %}transparent{% endif %} class="header {% if settings.background == settings.header_background %}header--bordered{% endif %} {% if request.page_type == 'index' and section.settings.enable_transparent_header %}header--transparent{% endif %}" role="banner">
  {%- assign menu = section.settings.navigation_menu -%}

  <div class="container">
    <div class="header__wrapper">
      <!-- LEFT PART -->
      <nav class="header__inline-navigation" {% unless section.settings.header_layout == 'logo_center_search_open' %}role="navigation"{% endunless %}>
        {%- if menu.links.size > 0 and section.settings.header_layout == 'logo_left_navigation_inline' or section.settings.header_layout == 'logo_left_navigation_center' or section.settings.header_layout == 'logo_center_navigation_inline' -%}
          {%- render 'desktop-menu', menu: menu -%}
        {%- endif -%}

        <div class="header__icon-list {% if section.settings.header_layout == 'logo_center_search_open' %}hidden-desk{% endif %}">
          {%- if menu.links.size > 0 -%}
            <button is="toggle-button" class="header__icon-wrapper tap-area {% unless section.settings.header_layout == 'drawer' %}hidden-desk{% endunless %}" aria-controls="mobile-menu-drawer" aria-expanded="false">
              <span class="visually-hidden">{{ 'header.general.navigation' | t }}</span>
              {%- render 'icon' with 'header-hamburger' -%}
            </button>
          {%- endif -%}

          <a href="{{ routes.search_url }}" is="toggle-link" class="header__icon-wrapper tap-area {% if section.settings.header_layout == 'logo_center_search_open' %}hidden-lap{% endif %} hidden-desk" aria-controls="search-drawer" aria-expanded="false" aria-label="{{ 'search.general.title' | t | escape }}">
            {%- render 'icon' with 'header-search' -%}
          </a>
        </div>

        {%- if section.settings.header_layout == 'logo_center_search_open' -%}
          <div class="header__search-bar predictive-search hidden-pocket">
            <form class="predictive-search__form" action="{{ routes.search_url }}" method="get" role="search">
              <input type="hidden" name="type" value="product">

              {%- render 'icon' with 'header-search' -%}
              <input class="predictive-search__input" is="predictive-search-input" type="text" name="q" autocomplete="off" autocorrect="off" aria-controls="search-drawer" aria-expanded="false" aria-label="{{ 'search.general.title' | t }}" placeholder="{{ 'search.general.search_placeholder' | t }}">
            </form>
          </div>
        {%- endif -%}
      </nav>

      <!-- LOGO PART -->
      {%- capture logo -%}

        {%- assign logo_image = section.settings.logo -%}

        {% if section.settings.logo_link %}
          {%- assign logo_link = section.settings.logo_link -%}
        {% else %}
          {%- assign logo_link = routes.root_url -%}
        {% endif %}

        <a class="header__logo-link" href="{{ logo_link }}">

          {%- if section.settings.logo != blank -%}
            {%- assign logo_size = section.settings.logo_max_width | at_least: section.settings.mobile_logo_max_width | times: 2 %}
            <span class="visually-hidden">{{ shop.name }}</span>
            <img class="header__logo-image" width="{{ logo_image.width }}" height="{{ logo_image.height }}" src="{{ logo_image | image_url: width: logo_size }}" alt="{{ logo_image.alt | escape }}">

            {%- if request.page_type == 'index' and section.settings.enable_transparent_header and section.settings.transparent_logo != blank -%}
              <img class="header__logo-image header__logo-image--transparent" width="{{ section.settings.transparent_logo.width }}" height="{{ section.settings.transparent_logo.height }}" src="{{ section.settings.transparent_logo | image_url: width: logo_size }}" alt="{{ section.settings.transparent_logo.alt | escape }}">
            {%- endif -%}
          {%- else -%}
            <span class="header__logo-text heading h5">{{ shop.name }}</span>
          {%- endif -%}

        </a>

      {%- endcapture -%}

      {%- capture return_logo_content -%}

        {%- if section.settings.logo_return_link != blank -%}
          {%- assign return_link = section.settings.logo_return_link.url -%}
        {%- else -%}
          {%- assign return_link = routes.root_url -%}
        {%- endif -%}

        {%- if section.settings.logo_return != blank -%}
          {%- assign return_logo_image = section.settings.logo_return -%}
          <a href="{{ return_link }}" class="header__logo-return-link" target="_blank">
            <img class="header__return-logo-image" width="{{ return_logo_image.width }}" height="{{ return_logo_image.height }}" src="{{ return_logo_image | image_url: width: logo_size }}" alt="{{ return_logo_image.alt | escape }}">
          </a>
        {%- elsif section.settings.logo_return_text != blank -%}
          <a href="{{ return_link }}" class="header__logo-return-link link--animated" target="_blank">
            {{ section.settings.logo_return_text }}
          </a>
        {%- endif -%}
        
      {%- endcapture -%}

      {%- capture return_logo -%}
        {%- if return_logo_content != blank -%}
          <div class="header__return-logo hidden-pocket hidden-lap">
            {{ return_logo_content }}
          </div>
        {%- endif -%}
      {%- endcapture -%}

      {%- if request.page_type == 'index' -%}
        <div class="header__logo">
          <h1 class="header__logo-container">{{ logo }}</h1>
          {{ return_logo }}
        </div>
      {%- else -%}
        <span class="header__logo">
          <div class="header__logo-container">{{ logo }}</div>
          {{ return_logo }}
        </span>
      {%- endif -%}

      <!-- SECONDARY LINKS PART -->
      <div class="header__secondary-links">
        {%- if section.settings.show_country_selector and localization.available_countries.size > 1 -%}
          {%- assign country_selector = true -%}
        {%- endif -%}

        {%- if section.settings.show_locale_selector and localization.available_languages.size > 1 -%}
          {%- assign locale_selector = true -%}
        {%- endif -%}

        {%- if locale_selector or country_selector -%}
          {%- form 'localization', id: 'header-localization-form', class: 'header__cross-border hidden-pocket' -%}
            {%- if country_selector -%}
              <div class="popover-container">
                <input type="hidden" name="country_code" value="{{ localization.country.iso_code }}">
                <span class="visually-hidden">{{ 'header.general.country' | t }}</span>

                <button type="button" is="toggle-button" class="popover-button text--small" aria-expanded="false" aria-controls="header-localization-form-currency">
                  {{- localization.country.name }} ({{ localization.country.currency.iso_code }} {% if localization.country.currency.symbol %}{{ localization.country.currency.symbol }}{%- endif -%})
                  {%- render 'icon' with 'chevron', width: 9, height: 6, inline: true -%}
                </button>

                <popover-content id="header-localization-form-currency" class="popover">
                  <span class="popover__overlay"></span>

                  <header class="popover__header">
                    <span class="popover__title heading h6">{{- 'header.general.country' | t -}}</span>

                    <button type="button" class="popover__close-button tap-area tap-area--large" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
                      {%- render 'icon' with 'close' -%}
                    </button>
                  </header>

                  <div class="popover__content popover__content--restrict">
                    <div class="popover__choice-list">
                      {%- for country in localization.available_countries -%}
                        <button type="submit" name="country_code" value="{{ country.iso_code }}" class="popover__choice-item">
                          <span class="popover__choice-label" {% if country.iso_code == localization.country.iso_code %}aria-current="true"{% endif %}>
                            {{- country.name }} ({{ country.currency.iso_code }} {% if country.currency.symbol %}{{ country.currency.symbol }}{%- endif -%})
                          </span>
                        </button>
                      {%- endfor -%}
                    </div>
                  </div>
                </popover-content>
              </div>
            {%- endif -%}

            {%- if locale_selector -%}
              <div class="popover-container">
                <input type="hidden" name="locale_code" value="{{ localization.language.iso_code }}">
                <span class="visually-hidden">{{ 'header.general.language' | t }}</span>

                <button type="button" is="toggle-button" class="popover-button text--small" aria-expanded="false" aria-controls="header-localization-form-locale">
                  {{- localization.language.endonym_name | capitalize -}}
                  {%- render 'icon' with 'chevron', width: 9, height: 6, inline: true -%}
                </button>

                <popover-content id="header-localization-form-locale" class="popover">
                  <span class="popover__overlay"></span>

                  <header class="popover__header">
                    <span class="popover__title heading h6">{{- 'header.general.language' | t -}}</span>

                    <button type="button" class="popover__close-button tap-area tap-area--large" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
                      {%- render 'icon' with 'close' -%}
                    </button>
                  </header>

                  <div class="popover__content">
                    <div class="popover__choice-list">
                      {%- for language in localization.available_languages -%}
                        <button type="submit" name="locale_code" value="{{ language.iso_code }}" class="popover__choice-item">
                          <span class="popover__choice-label" {% if language.iso_code == localization.language.iso_code %}aria-current="true"{% endif %}>
                            {{- language.endonym_name | capitalize -}}
                          </span>
                        </button>
                      {%- endfor -%}
                    </div>
                  </div>
                </popover-content>
              </div>
            {%- endif -%}
          {%- endform -%}
        {%- endif -%}

        <div class="header__icon-list">
          {%- comment -%}IMPLEMENTATION NOTE: this icon is hidden by the "popup" section if it is disabled{%- endcomment -%}
          {%- comment -%}
          <button is="toggle-button" class="header__icon-wrapper tap-area hidden-phone {% unless section.settings.show_icons %}hidden-desk{% endunless %}" aria-controls="newsletter-popup" aria-expanded="false">
            <span class="visually-hidden">{{ 'header.general.newsletter' | t }}</span>
            {%- render 'icon' with 'header-email' -%}
          </button>
          {%- endcomment -%}

          {%- if section.settings.header_layout != 'logo_center_search_open' -%}
            <a href="{{ routes.search_url }}" is="toggle-link" class="header__icon-wrapper tap-area hidden-pocket hidden-lap {% unless section.settings.show_icons %}hidden-desk{% endunless %}" aria-label="{{ 'search.general.title' | t | escape }}" aria-controls="search-drawer" aria-expanded="false">
              {%- render 'icon' with 'custom-search' -%}
            </a>
          {%- endif -%}

          {%- if shop.customer_accounts_enabled -%}
            <a href="{% if customer %}{{ routes.account_url }}{% else %}{{ routes.account_login_url }}{% endif %}" class="header__icon-wrapper tap-area hidden-phone {% unless section.settings.show_icons %}hidden-desk{% endunless %}" aria-label="{% if customer %}{{ 'header.general.account' | t | escape }}{% else %}{{ 'header.general.login' | t | escape }}{% endif %}">
              {%- render 'icon' with 'custom-account' -%}
            </a>
          {%- endif -%}

          <a href="{{ routes.cart_url }}" {% unless settings.cart_type == 'page' or request.page_type == 'cart' %}is="toggle-link" aria-controls="mini-cart" aria-expanded="false"{% endunless %} class="header__icon-wrapper tap-area {% unless section.settings.show_icons %}hidden-desk{% endunless %}" aria-label="{{ 'header.general.cart' | t | escape }}" data-no-instant>
            {%- case settings.cart_icon -%}
              {%- when 'shopping_bag' -%}
                {%- render 'icon' with 'header-cart' -%}

              {%- when 'shopping_cart' -%}
                {%- render 'icon' with 'custom-cart' -%}

              {%- when 'tote_bag' -%}
                {%- render 'icon' with 'header-tote-bag' -%}
            {%- endcase -%}


            {%- liquid

              comment
                Custom code that removes "addon" products from the
                cart item total bubble.
              endcomment

              assign cart_count = 0

              for line_item in cart.items

                assign exclude_from_cart_count = false
                assign product_has_addon_token = false

                for prop in line_item.properties
                  if prop contains '_addonToken'
                    assign product_has_addon_token = true
                    break
                  endif
                endfor
                if product_has_addon_token == true
                  assign exclude_from_cart_count = true
                  for prop in line_item.properties
                    if prop contains '_hasAddons'
                      assign exclude_from_cart_count = false
                      break
                    endif
                  endfor
                endif

                unless exclude_from_cart_count == true
                  assign cart_count = cart_count | plus: line_item.quantity
                endunless

              endfor

            -%}

            <cart-count class="header__cart-count header__cart-count--floating bubble-count" data-cart-count="{{ cart_count }}" >{{ cart_count }}</cart-count>
          </a>
        </div>

        {%- unless section.settings.show_icons -%}
          <ul class="header__linklist list--unstyled hidden-pocket hidden-lap" role="list">
            {%- comment -%}
              <!-- NOTE: There is really not enough space to fit this, so this is commented by default in Liquid -->
              <li  class="header__linklist-item">
                <button is="toggle-button" aria-controls="newsletter-popup" aria-expanded="false">{{ 'header.general.newsletter' | t }}</button>
              </li>
            {%- endcomment -%}

            {%- if section.settings.header_layout != 'logo_center_search_open' -%}
              <li class="header__linklist-item">
                <a href="{{ routes.search_url }}" is="toggle-link" aria-controls="search-drawer" aria-expanded="false">{{ 'search.general.title' | t | escape }}</a>
              </li>
            {%- endif -%}

            {%- if shop.customer_accounts_enabled -%}
              <li class="header__linklist-item">
                <a href="{% if customer %}{{ routes.account_url }}{% else %}{{ routes.account_login_url }}{% endif %}">
                  {%- if customer -%}
                    {{ 'header.general.account' | t }}
                  {%- else -%}
                    {{ 'header.general.login' | t }}
                  {%- endif -%}
                </a>
              </li>
            {%- endif -%}

            <li class="header__linklist-item">
              <a href="{{ routes.cart_url }}" {% unless settings.cart_type == 'page' %}is="toggle-link" aria-controls="mini-cart" aria-expanded="false"{% endunless %} data-no-instant>
                {{- 'header.general.cart' | t | escape -}}
                <cart-count class="header__cart-count bubble-count">{{ cart.item_count }}</cart-count>
              </a>
            </li>
          </ul>
        {%- endunless -%}
      </div>
    </div>

    {%- if menu.links.size > 0 and section.settings.header_layout == 'logo_center_search_open' -%}
      <nav class="header__bottom-navigation hidden-pocket hidden-lap" role="navigation">
        {% render 'desktop-menu', menu: menu, bottom_navigation: true %}
      </nav>
    {%- endif -%}
  </div>
</store-header>

{%- comment -%}
------------------------------------------------------------------------------------
  CART NOTIFICATION: it is used to show the notification when a product is added in message mode
------------------------------------------------------------------------------------
{%- endcomment -%}

<cart-notification global hidden class="cart-notification {% unless section.settings.enable_sticky_header %}cart-notification--fixed{% endunless %}"></cart-notification>

{%- comment -%}
------------------------------------------------------------------------------------
MOBILE MENU DRAWER
------------------------------------------------------------------------------------
{%- endcomment -%}

{%- assign menu = section.settings.sidebar_navigation_menu | default: section.settings.navigation_menu -%}
{%- render 'mobile-menu', menu: menu, section: section -%}

{%- comment -%}
------------------------------------------------------------------------------------
SEARCH DRAWER
------------------------------------------------------------------------------------
{%- endcomment -%}

{%- render 'predictive-search' -%}

<script>
  (() => {
    const headerElement = document.getElementById('shopify-section-{{ section.id }}'),
      headerHeight = headerElement.clientHeight,
      headerHeightWithoutBottomNav = headerElement.querySelector('.header__wrapper').clientHeight;

    document.documentElement.style.setProperty('--header-height', headerHeight + 'px');
    document.documentElement.style.setProperty('--header-height-without-bottom-nav', headerHeightWithoutBottomNav + 'px');
  })();
</script>

<script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": {{ shop.name | json }},
    {% if section.settings.logo %}
      "logo": {{ section.settings.logo | image_url: width: section.settings.logo.width | prepend: 'https:' | json }},
    {% endif %}
    "url": {{ shop.url | append: page.url | json }}
  }
</script>

{% schema %}
{
  "name": "Header",
  "class": "shopify-section--header",
  "settings": [
    {
      "type": "header",
      "content": "🪁 Sub Brand",
      "info": "Settings for sub brand links. (e.g. Kyte Living)."
    },
    {
      "type": "link_list",
      "id": "sub_brand_links",
      "label": "Sub-brand Links",
      "info": "Important: This should also be set on the Top Menu Bar section."
    },
    {
      "type": "checkbox",
      "id": "sub_brand_links_external_open_tab",
      "label": "Open external links in new tab.",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "sub_brand_links_external_icon",
      "label": "Show icons on external links.",
      "default": true
    },
    {
      "type": "header",
      "content": "🪁 Return Link"
    },
    {
      "type": "image_picker",
      "id": "logo_return",
      "label": "Return Logo",
      "info": "280 x 80px .png recommended"
    },
    {
      "type": "text",
      "id": "logo_return_text",
      "label": "Return Logo Text",
      "info": "Use a text link instead of a logo"
    },
    {
      "type": "url",
      "id": "logo_return_link",
      "label": "Return Logo Link"
    },
    {
      "type": "header",
      "content": "Header Settings"
    },
    {
      "type": "checkbox",
      "id": "enable_sticky_header",
      "label": "Enable sticky header",
      "default": true
    },
    {
      "type": "header",
      "content": "Logo"
    },
    {
      "type": "url",
      "id": "logo_link",
      "label": "Logo link",
      "info": "Overrides standard home link."
    },
    {
      "type": "image_picker",
      "id": "logo",
      "label": "Image",
      "info": "280 x 80px .png recommended"
    },
    {
      "type": "range",
      "id": "logo_max_width",
      "min": 40,
      "max": 300,
      "step": 5,
      "unit": "px",
      "label": "Image width",
      "default": 140
    },
    {
      "type": "range",
      "id": "mobile_logo_max_width",
      "min": 25,
      "max": 170,
      "step": 5,
      "unit": "px",
      "label": "Mobile image width",
      "default": 100
    },
    {
      "type": "header",
      "content": "Navigation"
    },
    {
      "type": "link_list",
      "id": "navigation_menu",
      "label": "Menu",
      "default": "main-menu"
    },
    {
      "type": "link_list",
      "id": "sidebar_navigation_menu",
      "label": "Mobile menu",
      "info": "Select a different menu for mobile sidebar. If none is set the main menu is used."
    },
    {
      "type": "link_list",
      "id": "secondary_menu",
      "label": "Mobile menu - Secondary",
      "info": "Secondary mobile menu shown below links."
    },
    {
      "type": "header",
      "content": "Desktop",
      "info": "Those settings apply on large screen only"
    },
    {
      "type": "select",
      "id": "header_layout",
      "label": "Layout",
      "options": [
        {
          "value": "logo_left_navigation_inline",
          "label": "Logo left, navigation inline"
        },
        {
          "value": "logo_left_navigation_center",
          "label": "Logo left, navigation center"
        },
        {
          "value": "logo_center_navigation_inline",
          "label": "Logo center, navigation inline"
        },
        {
          "value": "logo_center_search_open",
          "label": "Logo center, search highlighted"
        },
        {
          "value": "drawer",
          "label": "Drawer"
        }
      ],
      "default": "logo_left_navigation_inline"
    },
    {
      "type": "checkbox",
      "id": "show_icons",
      "label": "Show icons",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "reduce_desktop_padding",
      "label": "Reduce padding",
      "default": false
    },
    {
      "type": "header",
      "content": "Language selector",
      "info": "To add a language, go to your [language settings.](/admin/settings/languages)"
    },
    {
      "type": "checkbox",
      "id": "show_locale_selector",
      "label": "Show language selector",
      "default": true
    },
    {
      "type": "header",
      "content": "Country/region selector",
      "info": "To add a country/region, go to your [currency settings.](/admin/settings/payments)"
    },
    {
      "type": "checkbox",
      "id": "show_country_selector",
      "label": "Show country/region selector",
      "default": false
    },
    {
      "type": "header",
      "content": "Transparent header",
      "info": "Home page only. For best results, use an image section such as slideshow."
    },
    {
      "type": "checkbox",
      "id": "enable_transparent_header",
      "label": "Enable",
      "default": false
    },
    {
      "type": "color",
      "id": "transparent_header_text_color",
      "label": "Text color",
      "default": "#ffffff"
    },
    {
      "type": "image_picker",
      "id": "transparent_logo",
      "label": "Logo image",
      "info": "Uses the dimensions set on your main logo."
    },
    {
      "type": "header",
      "content": "Search"
    },
    {
      "type": "link_list",
      "id": "search_menu",
      "label": "Quick links menu",
      "info": "Display quick links when empty. This menu has limited support for dropdown items."
    }
  ],
  "blocks": [
    {
      "type": "mega_menu",
      "name": "Mega menu",
      "settings": [
        {
          "type": "header",
          "content": "Shop by Color"
        },
        {
          "type": "checkbox",
          "id": "colors_enable",
          "label": "Enable \"Shop by Color\"",
          "default": true
        },
        {
          "type": "text",
          "id": "colors_link_group_handle",
          "label": "Color Link Group Handle",
          "info": "Enter the handle of a Color Link Group here to show color swatches with links",
          "placeholder": "kyte-living-women"
        },
        {
          "type": "text",
          "id": "menu_item",
          "label": "Menu item",
          "info": "Enter menu item to apply a mega menu dropdown. [Learn more](https://support.maestrooo.com/article/709-mega-menu)."
        },
        {
          "type": "select",
          "id": "images_position",
          "label": "Images position",
          "info": "When featuring 4 images or more, we recommend to not use any dropdown links to keep the navigation easy to use.",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "right"
        },
        {
          "type": "header",
          "content": "Image 1"
        },
        {
          "type": "text",
          "id": "hide_markets_1",
          "label": "Hide in countries",
          "info": "Important! Use 2-country letter codes and commas. For example: CA,FR,UK"
        },
        {
          "type": "image_picker",
          "id": "image_1",
          "label": "Image",
          "info": "560 x 420px .jpg recommended"
        },
        {
          "type": "text",
          "id": "image_1_heading",
          "label": "Heading"
        },
        {
          "type": "text",
          "id": "image_1_text",
          "label": "Text"
        },
        {
          "type": "url",
          "id": "image_1_link",
          "label": "Link"
        },
        {
          "type": "header",
          "content": "Image 2"
        },
        {
          "type": "text",
          "id": "hide_markets_2",
          "label": "Hide in countries",
          "info": "Important! Use 2-country letter codes and commas. For example: CA,FR,UK"
        },
        {
          "type": "image_picker",
          "id": "image_2",
          "label": "Image",
          "info": "560 x 420px .jpg recommended"
        },
        {
          "type": "text",
          "id": "image_2_heading",
          "label": "Heading"
        },
        {
          "type": "text",
          "id": "image_2_text",
          "label": "Text"
        },
        {
          "type": "url",
          "id": "image_2_link",
          "label": "Link"
        },
        {
          "type": "header",
          "content": "Image 3"
        },
        {
          "type": "text",
          "id": "hide_markets_3",
          "label": "Hide in countries",
          "info": "Important! Use 2-country letter codes and commas. For example: CA,FR,UK"
        },
        {
          "type": "image_picker",
          "id": "image_3",
          "label": "Image",
          "info": "560 x 420px .jpg recommended"
        },
        {
          "type": "text",
          "id": "image_3_heading",
          "label": "Heading"
        },
        {
          "type": "text",
          "id": "image_3_text",
          "label": "Text"
        },
        {
          "type": "url",
          "id": "image_3_link",
          "label": "Link"
        },
        {
          "type": "header",
          "content": "Image 4"
        },
        {
          "type": "text",
          "id": "hide_markets_4",
          "label": "Hide in countries",
          "info": "Important! Use 2-country letter codes and commas. For example: CA,FR,UK"
        },
        {
          "type": "image_picker",
          "id": "image_4",
          "label": "Image",
          "info": "560 x 420px .jpg recommended"
        },
        {
          "type": "text",
          "id": "image_4_heading",
          "label": "Heading"
        },
        {
          "type": "text",
          "id": "image_4_text",
          "label": "Text"
        },
        {
          "type": "url",
          "id": "image_4_link",
          "label": "Link"
        },
        {
          "type": "header",
          "content": "Image 5"
        },
        {
          "type": "text",
          "id": "hide_markets_5",
          "label": "Hide in countries",
          "info": "Important! Use 2-country letter codes and commas. For example: CA,FR,UK"
        },
        {
          "type": "image_picker",
          "id": "image_5",
          "label": "Image",
          "info": "560 x 420px .jpg recommended"
        },
        {
          "type": "text",
          "id": "image_5_heading",
          "label": "Heading"
        },
        {
          "type": "text",
          "id": "image_5_text",
          "label": "Text"
        },
        {
          "type": "url",
          "id": "image_5_link",
          "label": "Link"
        },
        {
          "type": "header",
          "content": "Image 6"
        },
        {
          "type": "image_picker",
          "id": "image_6",
          "label": "Image",
          "info": "560 x 420px .jpg recommended"
        },
        {
          "type": "text",
          "id": "image_6_heading",
          "label": "Heading"
        },
        {
          "type": "text",
          "id": "image_6_text",
          "label": "Text"
        },
        {
          "type": "url",
          "id": "image_6_link",
          "label": "Link"
        }
      ]
    }
  ]
}
{% endschema %}
