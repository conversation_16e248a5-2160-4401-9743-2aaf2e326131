{% liquid
assign ss = section.settings

comment
  FREE GIFTS - CART WIDE
endcomment

capture free_gift_settings
  assign id = 0
  assign pid = ss.cart_free_gift | default: ""
  assign vid = ss.cart_free_gift_variant | default: 0

  if pid != ""
    assign prod = all_products[pid]
    assign id = prod.selected_or_first_available_variant.id
  endif

  if vid > 0
    assign id = vid
  endif

  assign car = 0
  assign car_setting_str = ss.cart_amount_required | default: ""
  if car_setting_str != ""
    assign threshes = car_setting_str | remove: " " | split: ","
    for t in threshes
      assign parts = t | split: ":"
      assign currency_code = parts | first | upcase

      if currency_code == cart.currency.iso_code
        assign car = parts | last | times: 100
        break
      endif
    endfor
  endif

  assign cvr = ss.cart_variant_required | default: 0
  assign cmg = ss.cart_max_gifts | default: 0
  if id > 0
    echo "{"
      echo '"type":"cart",'
      echo '"giftId":' | append: id | append: ','
      echo '"requiredId":' | append: cvr | append: ','
      echo '"requiredAmount":' | append: car | append: ','
      echo '"giftMax":' | append: cmg
    echo "}"
  endif
endcapture

comment
  FREE GIFTS - PRODUCT WIDE
endcomment

capture reward_ids
  assign p = all_products[ss.product_required]
  echo "["
  for v in p.variants
    echo v.id
    unless forloop.last
      echo ","
    endunless
  endfor
  echo "]"
endcapture

capture product_gift

  assign pcr = 0
  assign pcr_setting_str = ss.product_count_required | default: ""
  if pcr_setting_str != ""
    assign threshes = pcr_setting_str | remove: " " | split: ","
    for t in threshes
      assign parts = t | split: ":"
      assign currency_code = parts | first | upcase

      if currency_code == cart.currency.iso_code
        assign pcr = parts | last | times: 100
        break
      endif
    endfor
  endif

  assign fgp = ss.free_gift_for_product | default: 0
  assign mgp = ss.maximum_gifts_for_product | default: 0
  if fgp > 0
    echo "{"
      echo '"type":"product",'
      echo '"validIds":' | append: reward_ids | append: ','
      echo '"requiredAmount":' | append: pcr | append: ','
      echo '"giftId":' | append: fgp | append: ','
      echo '"giftMax":' | append: mgp
    echo "}"
  endif
endcapture
%}

<div id="FreeGiftsAddon"
     data-cart='{{ free_gift_settings }}'
     data-single-product='{{ product_gift }}'></div>

{% schema %}
{
  "name": "Free Gift Settings",
  "settings": [
    {
      "type": "header",
      "content": "Cart Based Gift"
    },
    {
      "type": "paragraph",
      "content": "Set up a free gift offering with every purchase, or when certain requirements are met"
    },
    {
      "type": "product",
      "id": "cart_free_gift",
      "label": "A Product ID for a free gift",
      "info": "This will choose the first available variant ID"
    },
    {
      "type": "number",
      "id": "cart_free_gift_variant",
      "label": "A specific variant ID for free gift",
      "info": "Overrides the above product choice"
    },
    {
      "type": "text",
      "id": "cart_amount_required",
      "label": "Cart cost required for free gift (optional)",
      "info": "Use the locale format to assign country-specific amounts, separated by commas: USD:100,CAD:135"
    },
    {
      "type": "number",
      "id": "cart_variant_required",
      "label": "Variant required for free gift (optional)"
    },
    {
      "type": "number",
      "id": "cart_max_gifts",
      "label": "Maximum number of free gifts to provide",
      "info": "Leaving this empty or at 0 means that there will be no limit for how many gifts are given every time the requirements are met"
    },
    {
      "type": "header",
      "content": "Product Based Gift"
    },
    {
      "type": "paragraph",
      "content": "Reward a customer for purchasing so many of a specific product."
    },
    {
      "type": "product",
      "id": "product_required",
      "label": "The product a customer must purchase",
      "info": "This product and all of its variants add up to the total for a reward"
    },
    {
      "type": "text",
      "id": "product_count_required",
      "label": "Cart cost required for free product-based gift (optional)",
      "info": "Use the locale format to assign country-specific amounts, separated by commas: USD:100,CAD:135"
    },
    {
      "type": "number",
      "id": "free_gift_for_product",
      "label": "The variant to reward the customer with for reaching the target"
    },
    {
      "type": "number",
      "id": "maximum_gifts_for_product",
      "label": "Maximum number of rewards",
      "info": "A setting of 0 means no limit"
    }
  ]
}
{% endschema %}
