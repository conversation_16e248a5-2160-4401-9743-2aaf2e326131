{%- if section.settings.background != settings.background and section.settings.background != 'rgba(0,0,0,0)' -%}
  {%- assign blends_with_background = false -%}
{%- else -%}
  {%- assign blends_with_background = true -%}
{%- endif -%}

<style>
  #shopify-section-{{ section.id }} {
    {%- if section.settings.background == 'rgba(0,0,0,0)' -%}
      {%- assign section_background = settings.background -%}
    {%- else -%}
      {%- assign section_background = section.settings.background -%}
    {%- endif -%}

    {%- if section.settings.text_color == 'rgba(0,0,0,0)' -%}
      {%- assign heading_color = settings.heading_color -%}
      {%- assign text_color = settings.text_color -%}
    {%- else -%}
      {%- assign heading_color = section.settings.text_color -%}
      {%- assign text_color = section.settings.text_color -%}
    {%- endif -%}

    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};

    --section-background: {{ section_background.red }}, {{ section_background.green }}, {{ section_background.blue }};
  }

</style>

<section class="bespoke-faq-videos section {% unless blends_with_background %}section--flush{% endunless %}">

  {%- if section.settings.anchor -%}
    <a class="visually-hidden anchor" id="{{ section.settings.anchor }}" name="{{ section.settings.anchor }}"></a>
  {%- endif -%}

  <div class="container">
    <div class="section__color-wrapper section__color-wrapper--boxed">
      
      <faq-videos class="faq-videos">

        <div class="faq-videos__inner">

          <div class="faq-videos__playback">

            {% assign first_video = section.blocks | where: 'type', 'video' | first %}

            {%- capture video_poster -%}
              {%- if first_video.settings.image -%}
                {{- first_video.settings.image | image_url: width: first_video.settings.image.width | image_tag: loading: 'lazy', sizes: '(max-width: 740px) calc(100vw - 24px * 2), 1600px', widths: '400,500,600,700,800,900,1000,1200,1400,1600,1800,2000,2200,2400,2600,2800' -}}
              {%- elsif first_video.settings.video -%}
                {{- first_video.settings.video.preview_image | image_url: width: first_video.settings.video.preview_image.width | image_tag: loading: 'lazy', sizes: '(max-width: 740px) calc(100vw - 24px * 2), 1600px', widths: '400,500,600,700,800,900,1000,1200,1400,1600,1800,2000,2200,2400,2600,2800' -}}
              {%- else -%}
                {{- 'product-1' | placeholder_svg_tag: 'video-wrapper__poster-image placeholder-background' -}}
              {%- endif -%}
            {%- endcapture -%}

            <native-video {% if section.settings.autoplay %}autoplay{% endif %} class="native-video--stopped video-wrapper video-wrapper--native" style="--aspect-ratio: {{ first_video.settings.video.aspect_ratio }}">
              {{- video_poster -}}

              {%- if first_video != blank -%}
                <template>
                  {{- first_video.settings.video | video_tag: controls: section.settings.show_video_controls, autoplay: section.settings.autoplay, muted: section.settings.autoplay, playsinline: section.settings.autoplay, loop: section.settings.autoplay -}}
                </template>  
              {%- endif -%}
            </native-video>

          </div>

          <div class="faq-videos__content">
          
            <div class="faq-videos__content-inner">

              {%- if section.settings.title != blank -%}
                <h3 class="heading h3">{{ section.settings.title | escape }}</h3>
              {%- endif -%}

              <div class="faq-videos-list">

                {% for block in section.blocks %}

                  {%- capture video_image_url -%}
                    {%- if block.settings.image -%}
                      {{- block.settings.image | image_url: width: block.settings.image.width, sizes: '(max-width: 740px) calc(100vw - 24px * 2), 1600px', widths: '400,500,600,700,800,900,1000,1200,1400,1600,1800,2000,2200,2400,2600,2800' -}}
                    {%- elsif block.settings.video -%}
                      {{- block.settings.video.preview_image | image_url: width: block.settings.video.preview_image.width, sizes: '(max-width: 740px) calc(100vw - 24px * 2), 1600px', widths: '400,500,600,700,800,900,1000,1200,1400,1600,1800,2000,2200,2400,2600,2800' -}}
                    {%- else -%}
                      {%- assign placeholder_image = 'product-' | append: forloop.index -%}
                      {{- placeholder_image | placeholder_svg_tag: 'video-wrapper__poster-image placeholder-background' -}}
                    {%- endif -%}
                  {%- endcapture -%}

                  {%- capture video_image -%}
                    {%- if block.settings.image -%}
                      {{- block.settings.image | image_url: width: block.settings.image.width | image_tag: loading: 'lazy', sizes: '(max-width: 740px) calc(100vw - 24px * 2), 1600px', widths: '400,500,600,700,800,900,1000,1200,1400,1600,1800,2000,2200,2400,2600,2800' -}}
                    {%- elsif block.settings.video -%}
                      {{- block.settings.video.preview_image | image_url: width: block.settings.video.preview_image.width | image_tag: loading: 'lazy', sizes: '(max-width: 740px) calc(100vw - 24px * 2), 1600px', widths: '400,500,600,700,800,900,1000,1200,1400,1600,1800,2000,2200,2400,2600,2800' -}}
                    {%- else -%}
                      {%- assign placeholder_image = 'product-' | append: forloop.index -%}
                      {{- placeholder_image | placeholder_svg_tag: 'video-wrapper__poster-image placeholder-background' -}}
                    {%- endif -%}
                  {%- endcapture -%}

                  <button class="faq-video"
                    data-video='{{- block.settings.video | video_tag: controls: section.settings.show_video_controls, autoplay: section.settings.autoplay, muted: section.settings.autoplay, playsinline: section.settings.autoplay, loop: section.settings.autoplay -}}'
                    data-video-url='{{ block.settings.video.sources.first.url }}'
                    data-video-mime='{{ block.settings.video.sources.first.mime_type }}'
                    data-video-image='{{ video_image }}'
                    data-video-image-url='{{ video_image_url }}'
                    {{ block.shopify_attributes }}
                  >
                    <div class="faq-video__image">
                      <div class="faq-video__image-overlay">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="white" stroke-linecap="round" stroke-linejoin="round"/>
                          <path d="M10 8L16 12L10 16V8Z" stroke="white" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                      </div>
                      {%- if block.settings.image -%}
                        {{- block.settings.image | image_url: width: block.settings.image.width | image_tag: loading: 'lazy', sizes: '(max-width: 740px) calc(100vw - 24px * 2), 1600px', widths: '400,500,600,700,800,900,1000,1200,1400,1600,1800,2000,2200,2400,2600,2800' -}}
                      {%- elsif block.settings.video -%}
                        {{- block.settings.video.preview_image | image_url: width: block.settings.video.preview_image.width | image_tag: loading: 'lazy', sizes: '(max-width: 740px) calc(100vw - 24px * 2), 1600px', widths: '400,500,600,700,800,900,1000,1200,1400,1600,1800,2000,2200,2400,2600,2800' -}}
                      {%- else -%}
                        {%- assign placeholder_image = 'product-' | append: forloop.index -%}
                        {{- placeholder_image | placeholder_svg_tag: 'video-wrapper__poster-image placeholder-background' -}}
                      {%- endif -%}
                    </div>
                    <div class="faq-video__text">
                      <span class="text">{{ block.settings.title }}</span>
                    </div>
                  </button>

                {% else %}

                  {%- for i in (1..4) -%}
                    
                    {%- capture placeholder_image -%}product-{% cycle '1', '2', '3', '4' %}{%- endcapture -%}

                    <button class="faq-video">
                      <div class="faq-video__image">
                        <div class="faq-video__image-overlay">
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="white" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M10 8L16 12L10 16V8Z" stroke="white" stroke-linecap="round" stroke-linejoin="round"/>
                          </svg>
                        </div>
                        {{- placeholder_image | placeholder_svg_tag: 'video-wrapper__poster-image placeholder-background' -}}
                      </div>
                      <div class="faq-video__text">
                        <span class="text">Video Title {{ i }}</span>
                      </div>
                    </button>

                  {%- endfor -%}

                {% endfor %}

              </div>

            </div>

          </div>

        </div>

      </faq-videos>

    </div>
  </div>
</section>

{% schema %}
{
  "name": "🪁 FAQ Videos",
  "class": "shopify-section--faq-videos",
  "disabled_on": {
    "groups": ["header", "custom.overlay"]
  },
  "blocks": [
    {
      "type": "video",
      "name": "Video",
      "settings": [
        {
          "type": "video",
          "id": "video",
          "label": "Video",
          "info": "Replaces the external video if both are set."
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Video Description or Title"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Thumbnail image"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "header",
      "content": "Anchors"
    },
    {
      "type": "text",
      "id": "anchor",
      "label": "Anchor",
      "default": "faq-videos",
      "info": "Anchor name used for jump links.",
      "placeholder": "#anchor"
    },
    {
      "type": "header",
      "content": "Section Settings"
    },
    {
      "type": "header",
      "content": "Content Settings"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Talk about your brand"
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Enable video autoplay",
      "info": "Video are muted automatically to allow autoplay",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_video_controls",
      "label": "Show video controls",
      "info": "Only applicable with native video.",
      "default": true
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "rgba(0,0,0,0)"
    }
  ],
  "presets": [
    {
      "name": "🪁 FAQ Videos",
      "settings": {}
    }
  ]
}
{% endschema %}