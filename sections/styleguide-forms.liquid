<a class="section-anchor" id="styleguide-forms" name="styleguide-forms"></a>

<section class="styleguide-section styleguide-forms" data-section-type="styleguide-forms" data-section-id="{{ section.id }}">

  <div class="container">

    <div class="page-header">
      <h2>Forms</h2>
    </div>

    <div class="page-content page-content--large">

      <h3 class="docs-subtitle">Basic Form Elements</h3>

      <div class="Rte rte rte--preview">

        <form>

          <fieldset>

            <legend>Fields</legend>

            <label for="text1">Text Field</label>
            <input type="text" id="text1">

            <label for="text2">Placeholder</label>
            <input type="text" id="text2" placeholder="Placeholder text">

            <label for="text3">Disabled Field</label>
            <input type="text" id="text3" disabled="disabled">

            <label for="text4">Label</label>
            <input type="text" id="text4">

            <label for="text5" class="error">Text Field (Error)</label>
            <input type="text" id="text5" class="error" value="Invalid Entry">

            <label for="select1">Select Field</label>
            <select id="select1">
              <option value="0">-- Choose --</option>
              <option value="1">Option 1</option>
              <option value="2">Option 2</option>
              <option value="3">Option 3</option>
            </select>

          </fieldset>

          <h4>Large</h4>

          <fieldset>

            <legend>Checkboxes</legend>

            <div class="checkboxes">

              <input type="checkbox" name="checkbox-1" id="checkbox-1">
              <label for="checkbox-1">Checkbox 1</label>

              <input type="checkbox" name="checkbox-2" id="checkbox-2">
              <label for="checkbox-2">Checkbox 2</label>

              <input type="checkbox" name="checkbox-3" id="checkbox-3">
              <label for="checkbox-3">Checkbox 3</label>

            </div>

          </fieldset>

          <fieldset>

            <legend>Radio Buttons</legend>

            <div class="checkboxes">

              <input type="radio" name="radio-group-1" id="radio-group-1-1">
              <label for="radio-group-1-1">Radio 1</label>

              <input type="radio" name="radio-group-1" id="radio-group-1-2">
              <label for="radio-group-1-2">Radio 2</label>

              <input type="radio" name="radio-group-1" id="radio-group-1-3">
              <label for="radio-group-1-3">Radio 3</label>

            </div>

          </fieldset>

          <h4>Normal</h4>

          <fieldset>

            <legend>Checkboxes</legend>

            <div class="checkboxes">

              <input type="checkbox" name="checkbox-1" id="checkbox-1">
              <label for="checkbox-1">Checkbox 1</label>

              <input type="checkbox" name="checkbox-2" id="checkbox-2">
              <label for="checkbox-2">Checkbox 2</label>

              <input type="checkbox" name="checkbox-3" id="checkbox-3">
              <label for="checkbox-3">Checkbox 3</label>

            </div>

          </fieldset>

          <fieldset>

            <legend>Radio Buttons</legend>

            <div class="checkboxes">

              <input type="radio" name="radio-group-2" id="radio-group-2-1">
              <label for="radio-group-2-1">Radio 1</label>

              <input type="radio" name="radio-group-2" id="radio-group-2-2">
              <label for="radio-group-2-2">Radio 2</label>

              <input type="radio" name="radio-group-2" id="radio-group-2-3">
              <label for="radio-group-2-3">Radio 3</label>

            </div>

          </fieldset>

          <label for="textarea1">Textarea</label>
          <textarea id="textarea1" placeholder="Placeholder text"></textarea>

          <label for="file1">File Field</label>
          <input id="file1" type="file">

        </form>

      </div>

      <h3 class="docs-subtitle">Theme Form Elements</h3>

      <div class="Rte rte rte--preview">

        <form>

          <fieldset>

            <legend>Fields</legend>

            <div class="input">
              <input class="input__field" type="text" id="theme-input-1">
              <label class="input__label" for="theme-input-1">Text Field</label>
            </div>

            <div class="input">
              <input class="input__field" type="text" id="theme-input-2" placeholder="Placeholder text">
              <label class="input__label" for="theme-input-2">Placeholder</label>
            </div>

            <div class="input">
              <input class="input__field" type="text" id="theme-input-3" disabled="disabled">
              <label class="input__label" for="theme-input-3">Disabled Field</label>
            </div>

            <div class="input">
              <input class="input__field" type="text" id="theme-input-4">
              <label class="input__label" for="theme-input-4">Label</label>
            </div>

            <div class="input">
              <input class="input__field" type="text" id="theme-input-5" class="error" value="Invalid Entry">
              <label class="input__label" for="theme-input-5" class="error">Text Field (Error)</label>
            </div>

            <div class="input">
              <select class="select" id="theme-select-1">
                <option value="0">-- Choose --</option>
                <option value="1">Option 1</option>
                <option value="2">Option 2</option>
                <option value="3">Option 3</option>
              </select>
              <label for="theme-select-1">Select Field</label>
            </div>

          </fieldset>

          <fieldset>

            <legend>Checkboxes</legend>

            <div class="checkboxes">

              <input type="checkbox" name="checkbox-1" id="checkbox-1">
              <label for="checkbox-1">Checkbox 1</label>

              <input type="checkbox" name="checkbox-2" id="checkbox-2">
              <label for="checkbox-2">Checkbox 2</label>

              <input type="checkbox" name="checkbox-3" id="checkbox-3">
              <label for="checkbox-3">Checkbox 3</label>

            </div>

          </fieldset>

          <fieldset>

            <legend>Radio Buttons</legend>

            <div class="checkboxes">

              <input type="radio" name="radio-group-1" id="radio-group-1-1">
              <label for="radio-group-1-1">Radio 1</label>

              <input type="radio" name="radio-group-1" id="radio-group-1-2">
              <label for="radio-group-1-2">Radio 2</label>

              <input type="radio" name="radio-group-1" id="radio-group-1-3">
              <label for="radio-group-1-3">Radio 3</label>

            </div>

          </fieldset>

          <fieldset>

            <legend>Checkboxes</legend>

            <div class="checkboxes">

              <input type="checkbox" name="checkbox-1" id="checkbox-1">
              <label for="checkbox-1">Checkbox 1</label>

              <input type="checkbox" name="checkbox-2" id="checkbox-2">
              <label for="checkbox-2">Checkbox 2</label>

              <input type="checkbox" name="checkbox-3" id="checkbox-3">
              <label for="checkbox-3">Checkbox 3</label>

            </div>

          </fieldset>

          <fieldset>

            <legend>Radio Buttons</legend>

            <div class="checkboxes">

              <input type="radio" name="radio-group-2" id="radio-group-2-1">
              <label for="radio-group-2-1">Radio 1</label>

              <input type="radio" name="radio-group-2" id="radio-group-2-2">
              <label for="radio-group-2-2">Radio 2</label>

              <input type="radio" name="radio-group-2" id="radio-group-2-3">
              <label for="radio-group-2-3">Radio 3</label>

            </div>

          </fieldset>

          <label for="textarea1">Textarea</label>
          <textarea id="textarea1" placeholder="Placeholder text"></textarea>

          <label for="file1">File Field</label>
          <input id="file1" type="file">

        </form>

      </div>

      <h3 class="docs-subtitle">Notes and Form Errors</h3>

      <div class="Rte rte rte--preview">

        <div class="note">
          <div class="note__text"><strong>This is a standard message.</strong> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Repudiandae facere quam repellat obcaecati, mollitia corrupti non odio quasi, reiciendis deserunt?</div>
        </div>

        <div class="note note--primary">
          <div class="note__text"><strong>This is a primary message.</strong> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Vel cum ratione, qui repellat architecto nemo nihil nisi rerum distinctio, sapiente.</div>
        </div>

        <div class="note note--secondary">
          <div class="note__text"><strong>This is a secondary message.</strong> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Cumque est ducimus inventore facilis placeat assumenda sed, fugit asperiores id, deleniti.</div>
        </div>

        <div class="note note--tertiary">
          <div class="note__text"><strong>This is a tertiary message.</strong> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Ipsa numquam iusto ipsum commodi excepturi consequatur corrupti nemo harum voluptatibus eius.</div>
        </div>

        <div class="note note--success">
          <div class="note__text"><strong>This is a success message.</strong> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Facere id doloremque totam nihil. Fuga culpa delectus, officia asperiores nihil mollitia!</div>
        </div>

        <div class="note note--warning">
          <div class="note__text"><strong>This is a warning message.</strong> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Expedita, modi. Tempora beatae accusamus ratione at inventore numquam, pariatur! Doloremque, beatae.</div>
        </div>

        <div class="note note--danger">
          <div class="note__text"><strong>This is a danger message.</strong> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Tenetur beatae tempore similique quam maiores, eveniet ab eligendi? Nam, voluptatibus, unde.</div>
        </div>

        <div class="note note--info">
          <div class="note__text"><strong>This is a info message.</strong> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Illum soluta aspernatur adipisci veniam nam quasi quibusdam labore dolore, praesentium vero.</div>
        </div>

        <div class="note note--error">
          <div class="note__text">
            <strong>This is an error message.</strong>
            <ul>
              <li>Bullets can offer more insight to the error</li>
            </ul>
          </div>
        </div>

      </div>

      <h3 class="docs-subtitle">Buttons</h3>

      <button class="button button--small button--primary" type="button" name="button" id="buttonsInverted">Inverted</button>
      <button class="button button--small button--primary" type="button" name="button" id="buttonsRounded">Rounded</button>
      <button class="button button--small button--primary" type="button" name="button" id="buttonsLight">Light</button>
      <button class="button button--small button--primary" type="button" name="button" id="buttonsDisabled">Disabled</button>

      <div class="Rte rte rte--preview" data-button-container>

        <h4>Tiny</h4>

        <div class="button-wrapper button-wrapper--left">
          <button class="button button--tiny">Default</button>
          <button class="button button--tiny button--primary">Primary</button>
          <button class="button button--tiny button--secondary">Secondary</button>
          <button class="button button--tiny button--tertiary">Tertiary</button>
          <button class="button button--tiny button--success">Success</button>
          <button class="button button--tiny button--warning">Warning</button>
          <button class="button button--tiny button--danger">Danger</button>
          <button class="button button--tiny button--info">Info</button>
          <button class="button button--tiny button--link">Link</button>
        </div>

        <h4>Small</h4>

        <div class="button-wrapper button-wrapper--left">
          <button class="button button--small">Default</button>
          <button class="button button--small button--primary">Primary</button>
          <button class="button button--small button--secondary">Secondary</button>
          <button class="button button--small button--tertiary">Tertiary</button>
          <button class="button button--small button--success">Success</button>
          <button class="button button--small button--warning">Warning</button>
          <button class="button button--small button--danger">Danger</button>
          <button class="button button--small button--info">Info</button>
          <button class="button button--small button--link">Link</button>
        </div>

        <h4>Normal</h4>

        <div class="button-wrapper button-wrapper--left">
          <button class="button">Default</button>
          <button class="button button--primary">Primary</button>
          <button class="button button--secondary">Secondary</button>
          <button class="button button--tertiary">Tertiary</button>
          <button class="button button--success">Success</button>
          <button class="button button--warning">Warning</button>
          <button class="button button--danger">Danger</button>
          <button class="button button--info">Info</button>
          <button class="button button--link">Link</button>
        </div>

        <h4>Large</h4>

        <div class="button-wrapper button-wrapper--left">
          <button class="button button--large">Default</button>
          <button class="button button--large button--primary">Primary</button>
          <button class="button button--large button--secondary">Secondary</button>
          <button class="button button--large button--tertiary">Tertiary</button>
          <button class="button button--large button--success">Success</button>
          <button class="button button--large button--warning">Warning</button>
          <button class="button button--large button--danger">Danger</button>
          <button class="button button--large button--info">Info</button>
          <button class="button button--large button--link">Link</button>
        </div>

        <h4>Huge</h4>

        <div class="button-wrapper button-wrapper--left">
          <button class="button button--huge">Default</button>
          <button class="button button--huge button--primary">Primary</button>
          <button class="button button--huge button--secondary">Secondary</button>
          <button class="button button--huge button--tertiary">Tertiary</button>
          <button class="button button--huge button--success">Success</button>
          <button class="button button--huge button--warning">Warning</button>
          <button class="button button--huge button--danger">Danger</button>
          <button class="button button--huge button--info">Info</button>
          <button class="button button--huge button--link">Link</button>
        </div>

      </div>

    </div>

  </div>

  <script type="text/javascript">

  document.querySelector("#buttonsInverted").addEventListener("click", function(e){
    var controlButton = e.target;
    controlButton.closest("section").querySelector("[data-button-container]").querySelectorAll(".button").forEach(function(e){
      if (e.classList.contains("button--inverted")) {
        e.classList.remove("button--inverted");
        controlButton.classList.remove("button--inverted");
      }
      else {
        e.classList.add("button--inverted");
        controlButton.classList.add("button--inverted");
      }
    });
  });

  document.querySelector("#buttonsRounded").addEventListener("click", function(e){
    var controlButton = e.target;
    controlButton.closest("section").querySelector("[data-button-container]").querySelectorAll(".button").forEach(function(e){
      if (e.classList.contains("button--rounded")) {
        e.classList.remove("button--rounded");
        controlButton.classList.remove("button--inverted");
      }
      else {
        e.classList.add("button--rounded");
        controlButton.classList.add("button--inverted");
      }
    });
  });

  document.querySelector("#buttonsLight").addEventListener("click", function(e){
    var controlButton = e.target;
    controlButton.closest("section").querySelector("[data-button-container]").querySelectorAll(".button").forEach(function(e){
      if (e.classList.contains("button--light")) {
        e.classList.remove("button--light");
        controlButton.classList.remove("button--inverted");
      }
      else {
        e.classList.add("button--light");
        controlButton.classList.add("button--inverted");
      }
    });
  });

  document.querySelector("#buttonsDisabled").addEventListener("click", function(e){
    var controlButton = e.target;
    controlButton.closest("section").querySelector("[data-button-container]").querySelectorAll(".button").forEach(function(e){
      if (e.disabled == false) {
        e.disabled = true;
        controlButton.classList.remove("button--inverted");
      }
      else {
        e.disabled = false;
        controlButton.classList.add("button--inverted");
      }
    });
  });

  </script>

</section>
