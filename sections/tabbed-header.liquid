{%- comment -%}
IMPLEMENTATION NOTE: the header in Focal is pretty complex as it allows a lot of different layouts. In order to make
  this code as efficient as possible and avoid as many reflows, we are using a lot of different CSS variables. If you
  need to touch this code, make sure to do it with EXTRA CARE as it may have some unwanted side effects
{%- endcomment -%}

<style>

  :root {

    --enable-sticky-header: {% if section.settings.enable_sticky_header %}1{% else %}0{% endif %};
    --loading-bar-background: {{ settings.header_text_color.red }}, {{ settings.header_text_color.green }}, {{ settings.header_text_color.blue }}; /* Prevent the loading bar to be invisible */

    {% comment %}

    --loading-bar-background:
    --header-background:
    --header-text-color:
    
    {% endcomment %}

  }

  #mobile-menu-drawer,
  #shopify-section-{{ section.id }} {

    {%- if section.settings.background != blank and section.settings.background != 'rgba(0,0,0,0)' -%}
      --header-background: {{ section.settings.background.red }}, {{ section.settings.background.green }}, {{ section.settings.background.blue }};
      --background: {{ section.settings.background.red }}, {{ section.settings.background.green }}, {{ section.settings.background.blue }};
    {%- else -%}
      --header-background: {{ settings.tabbed_header_background_1.red }}, {{ settings.tabbed_header_background_1.green }}, {{ settings.tabbed_header_background_1.blue }};
      --background: {{ settings.tabbed_header_background_1.red }}, {{ settings.tabbed_header_background_1.green }}, {{ settings.tabbed_header_background_1.blue }};
    {%- endif -%}

    {%- if section.settings.background_2 != blank and section.settings.background_2 != 'rgba(0,0,0,0)' -%}
      --background-2: {{ section.settings.background_2.red }}, {{ section.settings.background_2.green }}, {{ section.settings.background_2.blue }};
    {%- else -%}
      --background-2: {{ settings.tabbed_header_background_2.red }}, {{ settings.tabbed_header_background_2.green }}, {{ settings.tabbed_header_background_2.blue }};
    {%- endif -%}

    {%- if section.settings.text_color != blank and section.settings.text_color != 'rgba(0,0,0,0)' -%}
      --header-text-color: {{ section.settings.text_color.red }}, {{ section.settings.text_color.green }}, {{ section.settings.text_color.blue }};
      --text-color: {{ section.settings.text_color.red }}, {{ section.settings.text_color.green }}, {{ section.settings.text_color.blue }};
    {%- else -%}
      --header-text-color: {{ settings.tabbed_header_text_color.red }}, {{ settings.tabbed_header_text_color.green }}, {{ settings.tabbed_header_text_color.blue }};
      --text-color: {{ settings.tabbed_header_text_color.red }}, {{ settings.tabbed_header_text_color.green }}, {{ settings.tabbed_header_text_color.blue }};
    {%- endif -%}

    {%- if section.settings.link_color != blank and section.settings.link_color != 'rgba(0,0,0,0)' -%}
      --link-color: {{ section.settings.link_color.red }}, {{ section.settings.link_color.green }}, {{ section.settings.link_color.blue }};
    {%- else -%}
      --link-color: {{ settings.tabbed_header_link_color.red }}, {{ settings.tabbed_header_link_color.green }}, {{ settings.tabbed_header_link_color.blue }};
    {%- endif -%}

    {%- if section.settings.logo_color != blank and section.settings.logo_color != 'rgba(0,0,0,0)' -%}
      --logo-color: {{ section.settings.logo_color.red }}, {{ section.settings.logo_color.green }}, {{ section.settings.logo_color.blue }};
    {%- else -%}
      --logo-color: {{ settings.tabbed_header_logo_color.red }}, {{ settings.tabbed_header_logo_color.green }}, {{ settings.tabbed_header_logo_color.blue }};
    {%- endif -%}

    {%- if section.settings.icon_color != blank and section.settings.icon_color != 'rgba(0,0,0,0)' -%}
      --icon-color: {{ section.settings.icon_color.red }}, {{ section.settings.icon_color.green }}, {{ section.settings.icon_color.blue }};
    {%- else -%}
      --icon-color: {{ settings.tabbed_header_icon_color.red }}, {{ settings.tabbed_header_icon_color.green }}, {{ settings.tabbed_header_icon_color.blue }};
    {%- endif -%}

    {%- if section.settings.border_color != blank and section.settings.border_color != 'rgba(0,0,0,0)' -%}
      --header-border-color: {{ section.settings.border_color.red }}, {{ section.settings.border_color.green }}, {{ section.settings.border_color.blue }};
    {%- else -%}
      --header-border-color: {{ settings.tabbed_header_border_color.red }}, {{ settings.tabbed_header_border_color.green }}, {{ settings.tabbed_header_border_color.blue }};
    {%- endif -%}

    {%- if section.settings.button_color != blank and section.settings.button_color != 'rgba(0,0,0,0)' -%}
      --button-color: {{ section.settings.button_color.red }}, {{ section.settings.button_color.green }}, {{ section.settings.button_color.blue }};
    {%- else -%}
      --button-color: {{ settings.tabbed_header_button_color.red }}, {{ settings.tabbed_header_button_color.green }}, {{ settings.tabbed_header_button_color.blue }};
    {%- endif -%}

    {%- if section.settings.bubble_color != blank and section.settings.bubble_color != 'rgba(0,0,0,0)' -%}
      --bubble-color: {{ section.settings.bubble_color.red }}, {{ section.settings.bubble_color.green }}, {{ section.settings.bubble_color.blue }};
    {%- else -%}
      --bubble-color: {{ settings.tabbed_header_bubble_color.red }}, {{ settings.tabbed_header_bubble_color.green }}, {{ settings.tabbed_header_bubble_color.blue }};
    {%- endif -%}

  }

  #shopify-section-{{ section.id }} {

    {%- if section.settings.enable_sticky_header -%}
      position: -webkit-sticky;
      position: sticky;
      top: 0;
    {%- else -%}
      position: relative;
    {%- endif -%}

    z-index: 4;
  }

  {%- if section.settings.enable_sticky_header -%}
    .shopify-section--announcement-bar ~ #shopify-section-{{ section.id }} {
      top: calc(var(--enable-sticky-announcement-bar, 0) * var(--announcement-bar-height, 0px));
    }
  {%- endif -%}

  #shopify-section-{{ section.id }} .header__logo-image {
    max-width: {{ section.settings.mobile_logo_max_width }}px;
  }

  @media screen and (min-width: 741px) {
    #shopify-section-{{ section.id }} .header__logo-image {
      max-width: {{ section.settings.logo_max_width }}px;
    }
  }

  {%- if request.page_type == '404' -%}
    .shopify-section--404 {
      /* For the 404 page, we re-use the same colors as the header for design reason */
      --background: {{ settings.header_background.red }}, {{ settings.header_background.green }}, {{ settings.header_background.blue }};
      --heading-color: {{ settings.header_text_color.red }}, {{ settings.header_text_color.green }}, {{ settings.header_text_color.blue }};
      --text-color: {{ settings.header_text_color.red }}, {{ settings.header_text_color.green }}, {{ settings.header_text_color.blue }};
      --primary-button-background: {{ settings.header_text_color.red }}, {{ settings.header_text_color.green }}, {{ settings.header_text_color.blue }};
      --primary-button-text-color: {{ settings.header_background.red }}, {{ settings.header_background.green }}, {{ settings.header_background.blue }};
    }
  {%- endif -%}

  {%- for block in section.blocks -%}

    #shopify-section-{{ section.id }} #desktop-menu-{{ forloop.index }} {
      {% if block.settings.background != blank and block.settings.background != 'rgba(0,0,0,0)' %}
        --block-background: {{ block.settings.background.red }}, {{ block.settings.background.green }}, {{ block.settings.background.blue }};
      {% endif %}
      {% if block.settings.text_color != blank and block.settings.text_color != 'rgba(0,0,0,0)' %}
        --text-color: {{ block.settings.text_color.red }}, {{ block.settings.text_color.green }}, {{ block.settings.text_color.blue }};
      {% endif %}
      {% if block.settings.heading_color != blank and block.settings.heading_color != 'rgba(0,0,0,0)' %}
        --heading-color: {{ block.settings.heading_color.red }}, {{ block.settings.heading_color.green }}, {{ block.settings.heading_color.blue }};
      {% endif %}
      {% if block.settings.subheading_color != blank and block.settings.subheading_color != 'rgba(0,0,0,0)' %}
        --subheading-color: {{ block.settings.subheading_color.red }}, {{ block.settings.subheading_color.green }}, {{ block.settings.subheading_color.blue }};
      {% endif %}
    }

  {%- endfor -%}
  
</style>


{%- capture floating_badge -%}

  {%- if section.settings.floating_badge_enable == true and section.settings.floating_badge_image != blank -%}
  
    {%- capture styles -%}
      --badge-color-background: {{ section.settings.floating_badge_color_background.rgb }};
      --badge-color-border: {{ section.settings.floating_badge_color_border }};
      --badge-color-label-background: {{ section.settings.floating_badge_color_label_background.rgb }};
      --badge-color-label-text: {{ section.settings.floating_badge_color_label_text.rgb }};
      --badge-border-width: {{ section.settings.floating_badge_border_width }}px;
    {%- endcapture -%}
    
    {%- if section.settings.floating_badge_link != blank -%}
      <a href="{{ section.settings.floating_badge_link }}">
    {%- endif -%}
      <header-floating-badge class="header-floating-badge header-floating-badge--loaded" style="{{ styles }}">
        <div class="header-floating-badge__inner">
          <div class="header-floating-badge__image">
            {{- section.settings.floating_badge_image | image_url: width: section.settings.floating_badge_image.width | image_tag: loading: 'lazy' -}}
          </div>
        </div>
        {%- if section.settings.floating_badge_label_text != blank -%}
          <div class="header-floating-badge__label">
            <div class="header-floating-badge__label-inner">
              {{ section.settings.floating_badge_label_text }}
            </div>
          </div>
        {%- endif -%}
      </header-floating-badge>
    {%- if section.settings.floating_badge_link != blank -%}
      </a>
    {%- endif -%}
    
  {%- endif -%}

{%- endcapture -%}


<store-header {% if section.settings.enable_sticky_header %}sticky{% endif %} class="header tabbed-header {% if settings.background == settings.header_background %}header--bordered{% endif %}" role="banner">
  {%- assign menu = section.settings.navigation_menu -%}

  <div class="container">
    <div class="header__wrapper">

      {%- capture expanding_logo_part -%}
        <!-- EXPANDING LOGO PART -->

        {%- liquid

          assign sub_brands = metaobjects['sub_brand'].values

          for sub_brand in sub_brands
            if request.page_type == "page"
              if page.metafields.sub_brand.sub_brand
                assign current_sub_brand = page.metafields.sub_brand.sub_brand.value
              endif
            elsif request.page_type == "collection"
              if collection.metafields.sub_brand.sub_brand
                assign current_sub_brand = collection.metafields.sub_brand.sub_brand.value
              endif
            elsif request.page_type == "product"
              if collection.metafields.sub_brand.sub_brand
                assign current_sub_brand = collection.metafields.sub_brand.sub_brand.value
              elsif product.metafields.sub_brand.sub_brand
                assign current_sub_brand = product.metafields.sub_brand.sub_brand.value
              endif
            endif
            if sub_brand == current_sub_brand
              assign active_sub_brand = sub_brand
              break
            endif
          endfor

          if active_sub_brand != blank
            assign logo_image = active_sub_brand.logo
            if active_sub_brand.landing_page != blank
              assign logo_link = active_sub_brand.landing_page.value.url
            elsif active_sub_brand.link != blank
              assign logo_link = active_sub_brand.link.url
            endif
          else
            assign logo_image = settings.logo_image
            assign logo_link = routes.root_url
          endif
          
        -%}

        <div class="header__expanding-logo hidden-lap hidden-pocket">
          <a class="header__logo-link header__logo-link--image" href="{{ logo_link }}">
            
            {%- if logo_image != blank -%}
              <span class="header__logo-link-inner">
                {{- logo_image | image_url: width: logo_image.width | image_tag: loading: 'lazy', widths: '800,1200,1400,1600', class: 'header__logo-image' -}}
              </span>
            {%- else -%}
              <span class="header__logo-text heading h5">{{ shop.name }}</span>
            {%- endif -%}

          </a>
        </div>

        <a class="header__logo-link {% if use_logo_image == true %}header__logo-link--image{% endif %} header__logo-link-static hidden-desk" href="{{ logo_link }}">

          {%- if logo_image != blank -%}
            <span class="header__logo-link-inner">
              {{- logo_image | image_url: width: logo_image.width | image_tag: loading: 'lazy', widths: '800,1200,1400,1600', class: 'header__logo-image' -}}
            </span>
          {%- else -%}
            <span class="header__logo-text heading h5">{{ shop.name }}</span>
          {%- endif -%}

        </a>

      {%- endcapture -%}

      {%- capture search_part -%}

        <div class="header__search-bar predictive-search hidden-pocket">
          <form class="predictive-search__form" action="{{ routes.search_url }}" method="get" role="search">
            <input type="hidden" name="type" value="product">
            <label class="predictive-search-input-wrapper" for="predictive-search-input">
              {%- render 'icon' with 'header-search' -%}
              <input class="predictive-search__input" is="predictive-search-input" type="text" name="q" autocomplete="off" autocorrect="off" aria-controls="search-drawer" aria-expanded="false" aria-label="{{ 'search.general.title' | t }}" placeholder="{{ 'search.general.search_placeholder' | t }}" id="predictive-search-input">
            </label>
          </form>
        </div>

      {%- endcapture -%}

      {%- capture icon_part -%}

        <!-- Icon Part -->

        <div class="header__icon-list">

          <a href="{{ routes.search_url }}" is="toggle-link" class="header__icon-wrapper tap-area hidden-lap hidden-desk" aria-controls="search-drawer" aria-expanded="false" aria-label="{{ 'search.general.title' | t | escape }}">
            {%- render 'icon' with 'header-search' -%}
          </a>

          {%- if shop.customer_accounts_enabled -%}
            <a href="{% if customer %}{{ routes.account_url }}{% else %}{{ routes.account_login_url }}{% endif %}" class="header__icon-wrapper tap-area" aria-label="{% if customer %}{{ 'header.general.account' | t | escape }}{% else %}{{ 'header.general.login' | t | escape }}{% endif %}">
              {%- render 'icon' with 'custom-account' -%}
            </a>
          {%- endif -%}

          <a href="{{ routes.cart_url }}" {% unless settings.cart_type == 'page' or request.page_type == 'cart' %}is="toggle-link" aria-controls="mini-cart" aria-expanded="false"{% endunless %} class="header__icon-wrapper tap-area " aria-label="{{ 'header.general.cart' | t | escape }}" data-no-instant>
            
            {%- case settings.cart_icon -%}
              {%- when 'shopping_bag' -%}
                {%- render 'icon' with 'header-cart' -%}

              {%- when 'shopping_cart' -%}
                {%- render 'icon' with 'custom-cart' -%}

              {%- when 'tote_bag' -%}
                {%- render 'icon' with 'header-tote-bag' -%}
            {%- endcase -%}

            {%- liquid

              comment
                Custom code that removes "addon" products from the
                cart item total bubble.
              endcomment

              assign cart_count = 0

              for line_item in cart.items

                assign exclude_from_cart_count = false
                assign product_has_addon_token = false

                for prop in line_item.properties
                  if prop contains '_addonToken'
                    assign product_has_addon_token = true
                    break
                  endif
                endfor
                if product_has_addon_token == true
                  assign exclude_from_cart_count = true
                  for prop in line_item.properties
                    if prop contains '_hasAddons'
                      assign exclude_from_cart_count = false
                      break
                    endif
                  endfor
                endif

                unless exclude_from_cart_count == true
                  assign cart_count = cart_count | plus: line_item.quantity
                endunless

              endfor

            -%}

            <cart-count class="header__cart-count header__cart-count--floating bubble-count">{{ cart_count }}</cart-count>
          </a>
        </div>

      {%- endcapture -%}

      {%- capture menu_part -%}

        {{ expanding_logo_part }}

        <div class="header__menu">
          {%- render 'desktop-menu', menu: menu -%}
        </div>

      {%- endcapture -%}

      <!-- LEFT PART -->
      <nav class="header__inline-navigation" role="navigation">

        <div class="header__icon-list hidden-desk">

          {%- if menu.links.size > 0 -%}
            <button is="toggle-button" class="header__icon-wrapper tap-area hidden-desk" aria-controls="mobile-menu-drawer" aria-expanded="false">
              <span class="visually-hidden">{{ 'header.general.navigation' | t }}</span>
              {%- render 'icon' with 'header-hamburger' -%}
            </button>
          {%- endif -%}
          
        </div>

        {{ menu_part }}

      </nav>

      <!-- RIGHT PART -->
      <div class="header__secondary-links">
        {%- if section.settings.show_country_selector and localization.available_countries.size > 1 -%}
          {%- assign country_selector = true -%}
        {%- endif -%}

        {%- if section.settings.show_locale_selector and localization.available_languages.size > 1 -%}
          {%- assign locale_selector = true -%}
        {%- endif -%}

        {%- if locale_selector or country_selector -%}
          {%- form 'localization', id: 'header-localization-form', class: 'header__cross-border hidden-pocket' -%}
            {%- if country_selector -%}
              <div class="popover-container">
                <input type="hidden" name="country_code" value="{{ localization.country.iso_code }}">
                <span class="visually-hidden">{{ 'header.general.country' | t }}</span>

                <button type="button" is="toggle-button" class="popover-button text--small" aria-expanded="false" aria-controls="header-localization-form-currency">
                  {{- localization.country.name }} ({{ localization.country.currency.iso_code }} {% if localization.country.currency.symbol %}{{ localization.country.currency.symbol }}{%- endif -%})
                  {%- render 'icon' with 'chevron', width: 9, height: 6, inline: true -%}
                </button>

                <popover-content id="header-localization-form-currency" class="popover">
                  <span class="popover__overlay"></span>

                  <header class="popover__header">
                    <span class="popover__title heading h6">{{- 'header.general.country' | t -}}</span>

                    <button type="button" class="popover__close-button tap-area tap-area--large" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
                      {%- render 'icon' with 'close' -%}
                    </button>
                  </header>

                  <div class="popover__content popover__content--restrict">
                    <div class="popover__choice-list">
                      {%- for country in localization.available_countries -%}
                        <button type="submit" name="country_code" value="{{ country.iso_code }}" class="popover__choice-item">
                          <span class="popover__choice-label" {% if country.iso_code == localization.country.iso_code %}aria-current="true"{% endif %}>
                            {{- country.name }} ({{ country.currency.iso_code }} {% if country.currency.symbol %}{{ country.currency.symbol }}{%- endif -%})
                          </span>
                        </button>
                      {%- endfor -%}
                    </div>
                  </div>
                </popover-content>
              </div>
            {%- endif -%}

            {%- if locale_selector -%}
              <div class="popover-container">
                <input type="hidden" name="locale_code" value="{{ localization.language.iso_code }}">
                <span class="visually-hidden">{{ 'header.general.language' | t }}</span>

                <button type="button" is="toggle-button" class="popover-button text--small" aria-expanded="false" aria-controls="header-localization-form-locale">
                  {{- localization.language.endonym_name | capitalize -}}
                  {%- render 'icon' with 'chevron', width: 9, height: 6, inline: true -%}
                </button>

                <popover-content id="header-localization-form-locale" class="popover">
                  <span class="popover__overlay"></span>

                  <header class="popover__header">
                    <span class="popover__title heading h6">{{- 'header.general.language' | t -}}</span>

                    <button type="button" class="popover__close-button tap-area tap-area--large" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
                      {%- render 'icon' with 'close' -%}
                    </button>
                  </header>

                  <div class="popover__content">
                    <div class="popover__choice-list">
                      {%- for language in localization.available_languages -%}
                        <button type="submit" name="locale_code" value="{{ language.iso_code }}" class="popover__choice-item">
                          <span class="popover__choice-label" {% if language.iso_code == localization.language.iso_code %}aria-current="true"{% endif %}>
                            {{- language.endonym_name | capitalize -}}
                          </span>
                        </button>
                      {%- endfor -%}
                    </div>
                  </div>
                </popover-content>
              </div>
            {%- endif -%}
          {%- endform -%}
        {%- endif -%}

        {{ search_part }}

        {{ icon_part }}

      </div>

    </div>

  </div>

  {{ floating_badge }}

</store-header>

{%- comment -%}
------------------------------------------------------------------------------------
  CART NOTIFICATION: it is used to show the notification when a product is added in message mode
------------------------------------------------------------------------------------
{%- endcomment -%}

<cart-notification global hidden class="cart-notification {% unless section.settings.enable_sticky_header %}cart-notification--fixed{% endunless %}"></cart-notification>

{%- comment -%}
------------------------------------------------------------------------------------
MOBILE MENU DRAWER
------------------------------------------------------------------------------------
{%- endcomment -%}

{%- assign menu = section.settings.sidebar_navigation_menu | default: section.settings.navigation_menu -%}
{%- render 'mobile-menu', menu: menu, section: section -%}

{%- comment -%}
------------------------------------------------------------------------------------
SEARCH DRAWER
------------------------------------------------------------------------------------
{%- endcomment -%}

{%- render 'predictive-search' -%}

<script>
  (() => {
    const headerElement = document.getElementById('shopify-section-{{ section.id }}'),
      headerHeight = headerElement.clientHeight,
      headerHeightWithoutBottomNav = headerElement.querySelector('.header__wrapper').clientHeight;

    document.documentElement.style.setProperty('--header-height', headerHeight + 'px');
    document.documentElement.style.setProperty('--header-height-without-bottom-nav', headerHeightWithoutBottomNav + 'px');
  })();
</script>

<script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": {{ shop.name | json }},
    {% if logo_image %}
      "logo": {{ logo_image | image_url: width: logo_image.width | prepend: 'https:' | json }},
    {% endif %}
    "url": {{ shop.url | append: page.url | json }}
  }
</script>

{% schema %}
{
  "name": "🪁 Tabbed Header",
  "class": "shopify-section--header",
  "settings": [
    {
      "type": "header",
      "content": "Header Settings"
    },
    {
      "type": "checkbox",
      "id": "enable_sticky_header",
      "label": "Enable sticky header",
      "default": true
    },
    {
      "type": "header",
      "content": "Logo"
    },
    {
      "type": "url",
      "id": "logo_link",
      "label": "Logo link",
      "info": "Overrides standard home link."
    },
    {
      "type": "range",
      "id": "logo_max_width",
      "min": 40,
      "max": 300,
      "step": 5,
      "unit": "px",
      "label": "Image width",
      "default": 140
    },
    {
      "type": "range",
      "id": "mobile_logo_max_width",
      "min": 25,
      "max": 170,
      "step": 5,
      "unit": "px",
      "label": "Mobile image width",
      "default": 100
    },
    {
      "type": "header",
      "content": "Navigation"
    },
    {
      "type": "link_list",
      "id": "navigation_menu",
      "label": "Menu",
      "default": "main-menu"
    },
    {
      "type": "link_list",
      "id": "sidebar_navigation_menu",
      "label": "Mobile menu",
      "info": "Select a different menu for mobile sidebar. If none is set the main menu is used."
    },
    {
      "type": "link_list",
      "id": "secondary_menu",
      "label": "Mobile menu - Secondary",
      "info": "Secondary mobile menu shown below links."
    },
    {
      "type": "header",
      "content": "Language selector",
      "info": "To add a language, go to your [language settings.](/admin/settings/languages)"
    },
    {
      "type": "checkbox",
      "id": "show_locale_selector",
      "label": "Show language selector",
      "default": true
    },
    {
      "type": "header",
      "content": "Country/region selector",
      "info": "To add a country/region, go to your [currency settings.](/admin/settings/payments)"
    },
    {
      "type": "checkbox",
      "id": "show_country_selector",
      "label": "Show country/region selector",
      "default": false
    },
    {
      "type": "header",
      "content": "Transparent header",
      "info": "Home page only. For best results, use an image section such as slideshow."
    },
    {
      "type": "header",
      "content": "Search"
    },
    {
      "type": "link_list",
      "id": "search_menu",
      "label": "Quick links menu",
      "info": "Display quick links when empty. This menu has limited support for dropdown items."
    },
    {
      "type": "header",
      "content": "🪁 Floating Badge",
      "info": "Enables a floating badge that can be linked to a sale, promotion or other page."
    },
    {
      "type": "checkbox",
      "id": "floating_badge_enable",
      "label": "Enable"
    },
    {
      "type": "image_picker",
      "id": "floating_badge_image",
      "label": "Image",
      "info": "Image or icon on the floating badge."
    },
    {
      "type": "url",
      "id": "floating_badge_link",
      "label": "Link"
    },
    {
      "type": "text",
      "id": "floating_badge_label_text",
      "label": "Badge text",
      "info": "Shows in a colored strip under the icon."
    },
    {
      "type": "range",
      "id": "floating_badge_border_width",
      "min": 0,
      "max": 5,
      "step": 1,
      "unit": "px",
      "label": "Border width",
      "default": 3
    },
    {
      "type": "paragraph",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "floating_badge_color_background",
      "label": "Background color",
      "default": "#ffffff"
    },
    {
      "type": "color_background",
      "id": "floating_badge_color_border",
      "label": "Border color"
    },
    {
      "type": "color",
      "id": "floating_badge_color_label_background",
      "label": "Label background",
      "default": "#425A51"
    },
    {
      "type": "color",
      "id": "floating_badge_color_label_text",
      "label": "Label text",
      "default": "#ffffff"
    },
    {
      "type": "header",
      "content": "🪁 Colors",
      "info": "Overrides the colors under \"🪁 Tabbed Header\" theme settings."
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background"
    },
    {
      "type": "color",
      "id": "background_2",
      "label": "Background 2"
    },
    {
      "type": "color",
      "id": "link_color",
      "label": "Links"
    },
    {
      "type": "color",
      "id": "logo_color",
      "label": "Logo"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Headings"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text",
      "info": "Search input placeholder"
    },
    {
      "type": "color",
      "id": "icon_color",
      "label": "Icons"
    },
    {
      "type": "color",
      "id": "button_color",
      "label": "Button color",
      "info": "Hover state of search input."
    },
    {
      "type": "color",
      "id": "border_color",
      "label": "Borders"
    },
    {
      "type": "color",
      "id": "bubble_color",
      "label": "Cart count bubble"
    }
  ],
  "blocks": [
    {
      "type": "mega_menu",
      "name": "Mega menu",
      "settings": [
        {
          "type": "header",
          "content": "Header"
        },
        {
          "type": "text",
          "id": "header_title",
          "label": "Title"
        },
        {
          "type": "header",
          "content": "Footer"
        },
        {
          "type": "text",
          "id": "footer_button_text",
          "label": "Button text"
        },
        {
          "type": "url",
          "id": "footer_button_link",
          "label": "Button link"
        },
        {
          "type": "select",
          "id": "footer_button_size",
          "label": "Button size",
          "options": [
            {
              "value": "button--small",
              "label": "Small"
            },
            {
              "value": "",
              "label": "Default"
            },
            {
              "value": "button--large",
              "label": "Large"
            }
          ],
          "default": ""
        },
        {
          "type": "select",
          "id": "footer_button_style",
          "label": "Button style",
          "options": [
            {
              "value": "",
              "label": "Default"
            },
            {
              "value": "button--primary",
              "label": "Primary"
            },
            {
              "value": "button--secondary",
              "label": "Secondary"
            }
          ],
          "default": ""
        },
        {
          "type": "select",
          "id": "footer_button_icon",
          "label": "Button Icon",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "nav-arrow-right",
              "label": "Nav Arrow Right",
              "group": "Navigation"
            },
            {
              "value": "nav-arrow-right-small",
              "label": "Nav Arrow Right - Small",
              "group": "Navigation"
            }
          ]
        },
        {
          "type": "header",
          "content": "Shop by Color"
        },
        {
          "type": "checkbox",
          "id": "colors_enable",
          "label": "Enable \"Shop by Color\"",
          "default": true
        },
        {
          "type": "text",
          "id": "colors_link_group_handle",
          "label": "Color Link Group Handle",
          "info": "Enter the handle of a Color Link Group here to show color swatches with links",
          "placeholder": "kyte-living-women"
        },
        {
          "type": "text",
          "id": "menu_item",
          "label": "Menu item",
          "info": "Enter menu item to apply a mega menu dropdown. [Learn more](https://support.maestrooo.com/article/709-mega-menu)."
        },
        {
          "type": "select",
          "id": "images_position",
          "label": "Images position",
          "info": "When featuring 4 images or more, we recommend to not use any dropdown links to keep the navigation easy to use.",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "right"
        },
        {
          "type": "header",
          "content": "Image 1"
        },
        {
          "type": "text",
          "id": "hide_markets_1",
          "label": "Hide in countries",
          "info": "Important! Use 2-country letter codes and commas. For example: CA,FR,UK"
        },
        {
          "type": "image_picker",
          "id": "image_1",
          "label": "Image",
          "info": "560 x 420px .jpg recommended"
        },
        {
          "type": "text",
          "id": "image_1_heading",
          "label": "Heading"
        },
        {
          "type": "text",
          "id": "image_1_text",
          "label": "Text"
        },
        {
          "type": "url",
          "id": "image_1_link",
          "label": "Link"
        },
        {
          "type": "header",
          "content": "Image 2"
        },
        {
          "type": "text",
          "id": "hide_markets_2",
          "label": "Hide in countries",
          "info": "Important! Use 2-country letter codes and commas. For example: CA,FR,UK"
        },
        {
          "type": "image_picker",
          "id": "image_2",
          "label": "Image",
          "info": "560 x 420px .jpg recommended"
        },
        {
          "type": "text",
          "id": "image_2_heading",
          "label": "Heading"
        },
        {
          "type": "text",
          "id": "image_2_text",
          "label": "Text"
        },
        {
          "type": "url",
          "id": "image_2_link",
          "label": "Link"
        },
        {
          "type": "header",
          "content": "Image 3"
        },
        {
          "type": "text",
          "id": "hide_markets_3",
          "label": "Hide in countries",
          "info": "Important! Use 2-country letter codes and commas. For example: CA,FR,UK"
        },
        {
          "type": "image_picker",
          "id": "image_3",
          "label": "Image",
          "info": "560 x 420px .jpg recommended"
        },
        {
          "type": "text",
          "id": "image_3_heading",
          "label": "Heading"
        },
        {
          "type": "text",
          "id": "image_3_text",
          "label": "Text"
        },
        {
          "type": "url",
          "id": "image_3_link",
          "label": "Link"
        },
        {
          "type": "header",
          "content": "Image 4"
        },
        {
          "type": "text",
          "id": "hide_markets_4",
          "label": "Hide in countries",
          "info": "Important! Use 2-country letter codes and commas. For example: CA,FR,UK"
        },
        {
          "type": "image_picker",
          "id": "image_4",
          "label": "Image",
          "info": "560 x 420px .jpg recommended"
        },
        {
          "type": "text",
          "id": "image_4_heading",
          "label": "Heading"
        },
        {
          "type": "text",
          "id": "image_4_text",
          "label": "Text"
        },
        {
          "type": "url",
          "id": "image_4_link",
          "label": "Link"
        },
        {
          "type": "header",
          "content": "Image 5"
        },
        {
          "type": "text",
          "id": "hide_markets_5",
          "label": "Hide in countries",
          "info": "Important! Use 2-country letter codes and commas. For example: CA,FR,UK"
        },
        {
          "type": "image_picker",
          "id": "image_5",
          "label": "Image",
          "info": "560 x 420px .jpg recommended"
        },
        {
          "type": "text",
          "id": "image_5_heading",
          "label": "Heading"
        },
        {
          "type": "text",
          "id": "image_5_text",
          "label": "Text"
        },
        {
          "type": "url",
          "id": "image_5_link",
          "label": "Link"
        },
        {
          "type": "header",
          "content": "Image 6"
        },
        {
          "type": "image_picker",
          "id": "image_6",
          "label": "Image",
          "info": "560 x 420px .jpg recommended"
        },
        {
          "type": "text",
          "id": "image_6_heading",
          "label": "Heading"
        },
        {
          "type": "text",
          "id": "image_6_text",
          "label": "Text"
        },
        {
          "type": "url",
          "id": "image_6_link",
          "label": "Link"
        },
        {
          "type": "header",
          "content": "Appearance"
        },
        {
          "type": "select",
          "id": "header_title_style",
          "label": "Header title style",
          "default": "heading h3",
          "options": [
            {
              "value": "",
              "label": "Default"
            },
            {
              "group": "Heading",
              "value": "heading h1",
              "label": "Heading 1"
            },
            {
              "group": "Heading",
              "value": "heading h2",
              "label": "Heading 2"
            },
            {
              "group": "Heading",
              "value": "heading h3",
              "label": "Heading 3"
            },
            {
              "group": "Heading",
              "value": "heading h4",
              "label": "Heading 4"
            },
            {
              "group": "Heading",
              "value": "heading h5",
              "label": "Heading 5"
            }
          ]
        },
        {
          "type": "select",
          "id": "image_heading_style",
          "label": "Image heading style",
          "options": [
            {
              "value": "",
              "label": "Default"
            },
            {
              "group": "Subheading",
              "value": "heading heading--small subheading",
              "label": "Subheading"
            },
            {
              "group": "Subheading",
              "value": "heading heading--xsmall subheading",
              "label": "Subheading - Small"
            },
            {
              "group": "Text",
              "value": "text--xxsmall",
              "label": "Tiny"
            },
            {
              "group": "Text",
              "value": "text--xsmall",
              "label": "Extra Small"
            },
            {
              "group": "Text",
              "value": "text--small",
              "label": "Small"
            },
            {
              "group": "Text",
              "value": "text--large",
              "label": "Large"
            }
          ]
        },
        {
          "type": "select",
          "id": "image_text_style",
          "label": "Image text style",
          "options": [
            {
              "group": "Subheading",
              "value": "",
              "label": "Default"
            },
            {
              "group": "Subheading",
              "value": "heading heading--small subheading",
              "label": "Subheading"
            },
            {
              "group": "Subheading",
              "value": "heading heading--xsmall subheading",
              "label": "Subheading - Small"
            },
            {
              "group": "Text",
              "value": "text--xxsmall",
              "label": "Tiny"
            },
            {
              "group": "Text",
              "value": "text--xsmall",
              "label": "Extra Small"
            },
            {
              "group": "Text",
              "value": "text--small",
              "label": "Small"
            },
            {
              "group": "Text",
              "value": "text--large",
              "label": "Large"
            }
          ]
        },
        {
          "type": "header",
          "content": "Colors"
        },
        {
          "type": "color",
          "id": "background",
          "label": "Background"
        },
        {
          "type": "color",
          "id": "heading_color",
          "label": "Heading color"
        },
        {
          "type": "color",
          "id": "subheading_color",
          "label": "Subheading color"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text color"
        },
        {
          "type": "color",
          "id": "button_background",
          "label": "Button background"
        },
        {
          "type": "color",
          "id": "button_text_color",
          "label": "Button text"
        }
      ]
    },
    {
      "type": "mega_menu_products",
      "name": "Mega menu (Products)",
      "settings": [
        {
          "type": "header",
          "content": "Header"
        },
        {
          "type": "text",
          "id": "header_title",
          "label": "Title"
        },
        {
          "type": "header",
          "content": "Footer"
        },
        {
          "type": "text",
          "id": "footer_button_text",
          "label": "Button text"
        },
        {
          "type": "url",
          "id": "footer_button_link",
          "label": "Button link"
        },
        {
          "type": "select",
          "id": "footer_button_size",
          "label": "Button size",
          "options": [
            {
              "value": "button--small",
              "label": "Small"
            },
            {
              "value": "",
              "label": "Default"
            },
            {
              "value": "button--large",
              "label": "Large"
            }
          ],
          "default": ""
        },
        {
          "type": "select",
          "id": "footer_button_style",
          "label": "Button style",
          "options": [
            {
              "value": "",
              "label": "Default"
            },
            {
              "value": "button--primary",
              "label": "Primary"
            },
            {
              "value": "button--secondary",
              "label": "Secondary"
            }
          ],
          "default": ""
        },
        {
          "type": "select",
          "id": "footer_button_icon",
          "label": "Button Icon",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "nav-arrow-right",
              "label": "Nav Arrow Right",
              "group": "Navigation"
            },
            {
              "value": "nav-arrow-right-small",
              "label": "Nav Arrow Right - Small",
              "group": "Navigation"
            }
          ]
        },
        {
          "type": "header",
          "content": "Shop by Color"
        },
        {
          "type": "checkbox",
          "id": "colors_enable",
          "label": "Enable \"Shop by Color\"",
          "default": true
        },
        {
          "type": "text",
          "id": "colors_link_group_handle",
          "label": "Color Link Group Handle",
          "info": "Enter the handle of a Color Link Group here to show color swatches with links",
          "placeholder": "kyte-living-women"
        },
        {
          "type": "text",
          "id": "menu_item",
          "label": "Menu item",
          "info": "Enter menu item to apply a mega menu dropdown. [Learn more](https://support.maestrooo.com/article/709-mega-menu)."
        },
        {
          "type": "select",
          "id": "images_position",
          "label": "Images position",
          "info": "When featuring 4 images or more, we recommend to not use any dropdown links to keep the navigation easy to use.",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "right"
        },
        {
          "type": "header",
          "content": "Product 1"
        },
        {
          "type": "product",
          "id": "product_1",
          "label": "Product"
        },
        {
          "type": "text",
          "id": "product_1_hide_market",
          "label": "Hide Markets"
        },
        {
          "type": "header",
          "content": "Product 2"
        },
        {
          "type": "product",
          "id": "product_2",
          "label": "Product"
        },
        {
          "type": "text",
          "id": "product_2_hide_market",
          "label": "Hide Markets"
        },
        {
          "type": "header",
          "content": "Product 3"
        },
        {
          "type": "product",
          "id": "product_3",
          "label": "Product"
        },
        {
          "type": "text",
          "id": "product_3_hide_market",
          "label": "Hide Markets"
        },
        {
          "type": "header",
          "content": "Product 4"
        },
        {
          "type": "product",
          "id": "product_4",
          "label": "Product"
        },
        {
          "type": "text",
          "id": "product_4_hide_market",
          "label": "Hide Markets"
        },
        {
          "type": "header",
          "content": "Product 5"
        },
        {
          "type": "product",
          "id": "product_5",
          "label": "Product"
        },
        {
          "type": "text",
          "id": "product_5_hide_market",
          "label": "Hide Markets"
        },
        {
          "type": "header",
          "content": "Product 6"
        },
        {
          "type": "product",
          "id": "product_6",
          "label": "Product"
        },
        {
          "type": "text",
          "id": "product_6_hide_market",
          "label": "Hide Markets"
        },
        {
          "type": "header",
          "content": "Appearance"
        },
        {
          "type": "select",
          "id": "header_title_style",
          "label": "Header title style",
          "default": "heading h3",
          "options": [
            {
              "value": "",
              "label": "Default"
            },
            {
              "group": "Heading",
              "value": "heading h1",
              "label": "Heading 1"
            },
            {
              "group": "Heading",
              "value": "heading h2",
              "label": "Heading 2"
            },
            {
              "group": "Heading",
              "value": "heading h3",
              "label": "Heading 3"
            },
            {
              "group": "Heading",
              "value": "heading h4",
              "label": "Heading 4"
            },
            {
              "group": "Heading",
              "value": "heading h5",
              "label": "Heading 5"
            }
          ]
        },
        {
          "type": "select",
          "id": "image_heading_style",
          "label": "Image heading style",
          "options": [
            {
              "value": "",
              "label": "Default"
            },
            {
              "group": "Subheading",
              "value": "heading heading--small subheading",
              "label": "Subheading"
            },
            {
              "group": "Subheading",
              "value": "heading heading--xsmall subheading",
              "label": "Subheading - Small"
            },
            {
              "group": "Text",
              "value": "text--xxsmall",
              "label": "Tiny"
            },
            {
              "group": "Text",
              "value": "text--xsmall",
              "label": "Extra Small"
            },
            {
              "group": "Text",
              "value": "text--small",
              "label": "Small"
            },
            {
              "group": "Text",
              "value": "text--large",
              "label": "Large"
            }
          ]
        },
        {
          "type": "select",
          "id": "image_text_style",
          "label": "Image text style",
          "options": [
            {
              "group": "Subheading",
              "value": "",
              "label": "Default"
            },
            {
              "group": "Subheading",
              "value": "heading heading--small subheading",
              "label": "Subheading"
            },
            {
              "group": "Subheading",
              "value": "heading heading--xsmall subheading",
              "label": "Subheading - Small"
            },
            {
              "group": "Text",
              "value": "text--xxsmall",
              "label": "Tiny"
            },
            {
              "group": "Text",
              "value": "text--xsmall",
              "label": "Extra Small"
            },
            {
              "group": "Text",
              "value": "text--small",
              "label": "Small"
            },
            {
              "group": "Text",
              "value": "text--large",
              "label": "Large"
            }
          ]
        },
        {
          "type": "header",
          "content": "Colors"
        },
        {
          "type": "color",
          "id": "background",
          "label": "Background"
        },
        {
          "type": "color",
          "id": "heading_color",
          "label": "Heading color"
        },
        {
          "type": "color",
          "id": "subheading_color",
          "label": "Subheading color"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text color"
        },
        {
          "type": "color",
          "id": "button_background",
          "label": "Button background"
        },
        {
          "type": "color",
          "id": "button_text_color",
          "label": "Button text"
        }
      ]
    }
  ]
}
{% endschema %}
