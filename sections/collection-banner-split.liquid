{%- if section.settings.background_type == 'boxed' -%}
  {%- assign is_boxed = true -%}
{%- else -%}
  {%- assign is_boxed = false -%}
{%- endif -%}

<style>

  {%- assign text_position = section.settings.text_position | split: '_' | first -%}

  {%- case text_position -%}
    {%- when 'top' -%}
      {%- assign section_items_alignment = 'flex-start' -%}
    {%- when 'middle' -%}
      {%- assign section_items_alignment = 'center' -%}
    {%- when 'bottom' -%}
      {%- assign section_items_alignment = 'flex-end' -%}
  {%- endcase -%}

  #shopify-section-{{ section.id }} .image-overlay {
    --heading-color: {{ section.settings.image_text_color.red }}, {{ section.settings.image_text_color.green }}, {{ section.settings.image_text_color.blue }};
    --text-color: {{ section.settings.image_text_color.red }}, {{ section.settings.image_text_color.green }}, {{ section.settings.image_text_color.blue }};
    --section-items-alignment: center;
    /* --section-overlay-color: {{ section.settings.image_overlay_color.red }}, {{ section.settings.image_overlay_color.green }}, {{ section.settings.image_overlay_color.blue }}; */
    --section-overlay-opacity: {{ section.settings.image_overlay_opacity | divided_by: 100.0 }};
    --section-overlay-color: {{ section.settings.text_background.red }}, {{ section.settings.text_background.green }}, {{ section.settings.text_background.blue }};
  }

  #shopify-section-{{ section.id }} .heading {
    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
  }

  #shopify-section-{{ section.id }} .image-overlay__text-container {
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
  }


</style>

<section class="section {% unless is_boxed %}section--flush{% endunless %} custom-collection-banner--split">
  {%- comment -%}
  ------------------------------------------------------------------------------------------
  TOP PART (IMAGE AND BREADCRUMB)
  ------------------------------------------------------------------------------------------
  {%- endcomment -%}

  {% comment %}
  {%- capture breadcrumb -%}
    <nav aria-label="{{ 'general.breadcrumb.title' | t }}" class="breadcrumb breadcrumb--floating text--xsmall hidden-phone">
      <ol class="breadcrumb__list" role="list">
        <li class="breadcrumb__item">
          <a class="breadcrumb__link" href="{{ routes.root_url }}">{{ 'general.breadcrumb.home' | t }}</a>
        </li>

        <li class="breadcrumb__item">
          <span class="breadcrumb__link" aria-current="page">
            {%- if collection.handle == 'all' -%}
              {{- 'collection.general.all_products' | t -}}
            {%- else -%}
              {{- collection.title -}}
            {%- endif -%}
          </span>
        </li>
      </ol>
    </nav>
  {%- endcapture -%}
  {% endcomment %}

  <div {% if is_boxed %}class="container"{% endif %}>

    {%- assign text_alignment = section.settings.text_position | split: '_' | last -%}
  
    <image-with-text-overlay reveal-on-scroll parallax class="image-overlay {% unless collection.image and section.settings.show_collection_image %}image-overlay--no-image{% endunless %} image-overlay--{{ section.settings.section_height }}" {% if section.settings.section_height == 'auto' %}style="--image-aspect-ratio: {{ collection.image.aspect_ratio }}"{% endif %}>

      <div class="image-overlay__split-images">

        {%- if collection.image and section.settings.show_collection_image -%}

          <div class="image-overlay__split-image image-overlay__split-image--image">

            <div class="image-overlay__image-wrapper" {% if section.settings.section_height == 'auto' %}style="padding-bottom: {{ 100.0 | divided_by: collection.image.aspect_ratio }}%"{% endif %}>
              {%- comment -%}Performance note: this image must not be lazyloaded as it contributes to the LCP{%- endcomment -%}
              <picture>
                <source media="(max-width: 740px)" {% render 'image-attributes', image: collection.image, sizes: '400,500,600,700,800,900,1000', height_constraint: 600, crop: 'center', ignore_src: true %}>
                <img class="image-overlay__image" reveal {% render 'image-attributes', image: collection.image, sizes: '600,700,800,1000,1200,1400,1600,1800,2000,2200,2400,2600' %}>
              </picture>

            </div>

          </div>

        {%- endif -%}

        <div class="image-overlay__split-image image-overlay__split-image--content">

          <div class="text-container">

            {%- if section.settings.collection_sub_title != blank -%}
              <h2 class="heading heading--small">
                <split-lines reveal>{{ section.settings.collection_sub_title }}</split-lines>
              </h2>
            {%- endif -%}
  
            {%- if section.settings.show_collection_title -%}
              <h1 class="heading h1">
                <split-lines reveal>{{ collection.title }}</split-lines>
              </h1>
            {%- endif -%}

            {%- capture collection_description -%}

              {%- if section.settings.use_metafield_description != blank -%}
                {{ collection.metafields.custom.short_description }}  
              {%- else -%}
                {%- if collection.metafields.custom.short_description != blank -%}
                  {{ collection.metafields.custom.short_description }}  
                {%- else -%}
                  {{ collection.description }}
                {%- endif -%}
              {%- endif -%}

            {%- endcapture -%}

            {%- if collection_description != blank and section.settings.show_collection_description == true -%}
              <div class="image-overlay__text-container" reveal>
                {{ collection_description }}
              </div>
            {%- endif -%}

            {%- if section.settings.usps_list != blank -%}
              {%- assign usps = section.settings.usps_list -%}
            {%- elsif section.settings.usp_group != blank -%}
              {%- assign usps = section.settings.usp_group.usps.value -%}
            {%- endif -%}

            {%- capture collection_usps -%}
              {%- for usp in usps -%}
                {% render 'usp-icon', usp: usp, show_description: false %}
              {%- endfor -%}
            {%- endcapture -%}

            {%- if collection_usps != blank -%}
              <div class="image-overlay__usp-container">
                <div class="usp-icons">
                  <div class="usp-icons__inner hide-scrollbar">
                    {{ collection_usps }}                  
                  </div>
                </div>
              </div>
            {%- endif -%}

          </div>

        </div>

      </div>

    </image-with-text-overlay>
    
  </div>

  {%- comment -%}
  ------------------------------------------------------------------------------------------
  QUICK LINKS PART
  ------------------------------------------------------------------------------------------
  {%- endcomment -%}

  {%- if section.settings.quick_links_menu_handle != blank -%}
    {%- capture menu_handle -%}{{ section.settings.quick_links_menu_handle }}{%- endcapture -%}
    {%- assign quick_links_menu = linklists[menu_handle] -%}
  {%- elsif section.settings.quick_links != blank -%}
    {%- assign quick_links_menu = section.settings.quick_links -%}
  {%- endif -%}

  {% capture quick_links_title %}
    {%- if section.settings.quick_links_title != blank -%}
      {{- section.settings.quick_links_title -}}
    {%- else -%}
      {{- quick_links_menu.title -}}
    {%- endif -%}
  {% endcapture %}

  {%- if quick_links_menu.links.size > 0 -%}
    <link-bar class="link-bar">
      <div class="container">
        <div class="link-bar__wrapper">

          {% if section.settings.quick_links_hide_title == true %}
            <span class="link-bar__title heading heading--small text--subdued">{{ quick_links_title }}</span>
          {% endif %}

          <div class="link-bar__scroller hide-scrollbar">
            <ul class="link-bar__linklist list--unstyled" role="list">
              {%- for link in quick_links_menu.links -%}
                <li class="link-bar__link-item {% if link.active %}link-bar__link-item--selected{% else %}text--subdued{% endif %}">
                  <a href="{{ link.url }}" class="link-bar__link {% if link.active %}{% else %}link--animated{% endif %}">{{ link.title }}</a>
                </li>
              {%- endfor -%}
            </ul>
          </div>

        </div>
      </div>
    </link-bar>
  {%- endif -%}
</section>

{% schema %}
{
  "name": "🪁 Banner with USPs",
  "class": "shopify-section--collection-banner",
  "settings": [
    {
      "type": "header",
      "content": "🪁 Kyte Settings"
    },
    {
      "type": "header",
      "content": "USPs",
      "info": "Select a USP Group or individual USPs to display for this collection."
    },
    {
      "type": "metaobject",
      "metaobject_type": "usp_group",
      "id": "usp_group",
      "label": "USPs (Group)"
    },
    {
      "type": "metaobject_list",
      "metaobject_type": "usps",
      "id": "usps_list",
      "label": "USPs",
      "info": "Overrides USP Group"
    },
    {
      "type": "header",
      "content": "Images"
    },
    {
      "type": "paragraph",
      "content": "To change collection descriptions or collection images, [edit your collections](/admin/collections)."
    },
    {
      "type": "checkbox",
      "id": "show_collection_image",
      "label": "Show collection image",
      "default": true
    },
    {
      "type": "header",
      "content": "Title"
    },
    {
      "type": "text",
      "id": "collection_sub_title",
      "label": "Collection subtitle"
    },
    {
      "type": "checkbox",
      "id": "show_collection_title",
      "label": "Show collection title",
      "default": true
    },
    {
      "type": "header",
      "content": "Description"
    },
    {
      "type": "checkbox",
      "id": "show_collection_description",
      "label": "Show collection description",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "use_metafield_description",
      "label": "Use only metafield description",
      "default": true
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "background_type",
      "label": "Background",
      "options": [
        {
          "value": "full_width",
          "label": "Full width"
        },
        {
          "value": "boxed",
          "label": "Boxed"
        }
      ],
      "default": "full_width"
    },
    {
      "type": "select",
      "id": "text_width",
      "label": "Text width",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        },
        {
          "value": "fill",
          "label": "Fill screen"
        }
      ],
      "default": "medium"
    },
    {
      "type": "select",
      "id": "text_position",
      "label": "Text position",
      "options": [
        {
          "value": "top_left",
          "label": "Top left"
        },
        {
          "value": "top_center",
          "label": "Top center"
        },
        {
          "value": "top_right",
          "label": "Top right"
        },
        {
          "value": "middle_left",
          "label": "Middle left"
        },
        {
          "value": "middle_center",
          "label": "Middle center"
        },
        {
          "value": "middle_right",
          "label": "Middle right"
        },
        {
          "value": "bottom_left",
          "label": "Bottom left"
        },
        {
          "value": "bottom_center",
          "label": "Bottom center"
        },
        {
          "value": "bottom_right",
          "label": "Bottom right"
        }
      ],
      "default": "middle_center"
    },
    {
      "type": "select",
      "id": "section_height",
      "label": "Image height",
      "options": [
        {
          "value": "auto",
          "label": "Original image ratio"
        },
        {
          "value": "clamp",
          "label": "Clamp"
        },
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ],
      "info": "Choose \"Original image ratio\" to not cut images. [Learn more](https://help.shopify.com/en/manual/online-store/images/theme-images#best-practices-for-slideshows-and-full-width-images)",
      "default": "small"
    },
    {
      "type": "header",
      "content": "Color",
      "info": "Only applicable when collection image is used."
    },
    {
      "type": "color",
      "id": "image_text_color",
      "label": "Text",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "text_background",
      "label": "Text Background",
      "default": "#FBF6F3"
    },
    {
      "type": "color",
      "id": "image_overlay_color",
      "label": "Overlay",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "image_overlay_opacity",
      "label": "Overlay opacity",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "default": 30
    },
    {
      "type": "header",
      "content": "Navigation"
    },
    {
      "type": "link_list",
      "id": "quick_links",
      "label": "Quick links",
      "info": "This menu won't show dropdown items. (Overridden by the menu_handle field below)"
    },
    {
      "type": "checkbox",
      "id": "quick_links_hide_title",
      "label": "Hide Quick Links menu title",
      "default": false
    },
    {
      "type": "text",
      "id": "quick_links_menu_handle",
      "label": "Quick Links Menu Handle",
      "info": "Set the handle on the menu_handle metafield on the collection."
    },
    {
      "type": "text",
      "id": "quick_links_title",
      "label": "Quick Links Title"
    }
  ]
}
{% endschema %}