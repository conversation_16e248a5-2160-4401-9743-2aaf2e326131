<style>

  #shopify-section-{{ section.id }} .link-bar {

    {%- if section.settings.background != blank and section.settings.background != 'rgba(0,0,0,0)' -%}
      {%- assign background = section.settings.background -%}
    {%- else -%}
      {%- assign background = settings.background -%}
    {%- endif -%}

    {%- if section.settings.heading_color != blank and section.settings.heading_color != 'rgba(0,0,0,0)' and section.settings.heading_color != background -%}
      {%- assign heading_color = section.settings.heading_color -%}
    {%- else -%}
      {%- assign heading_color = settings.heading_color -%}
    {%- endif -%}

    {%- if section.settings.text_color != blank and section.settings.text_color != 'rgba(0,0,0,0)' and section.settings.text_color != background -%}
      {%- assign text_color = section.settings.text_color -%}
    {%- else -%}
      {%- assign text_color = settings.text_color -%}
    {%- endif -%}

    --background: {{ background.red }}, {{ background.green }}, {{ background.blue }};
    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};

  }

</style>

<link-bar class="link-bar">
  <div class="container">
    <div class="link-bar__wrapper">

      <span class="link-bar__title heading heading--small">{{ section.settings.title }}</span>

      <div class="link-bar__scroller hide-scrollbar">
        
        <ul class="link-bar__linklist list--unstyled" role="list">

          {% for block in section.blocks %}

            {% assign selected = block.settings.highlighted %}

            <li class="link-bar__link-item {% if selected == true %}link-bar__link-item--selected{% endif %}" {{ block.shopify_attributes }}>
              <a href="{{ block.settings.url }}" class="link-bar__link link--animated {% if selected == true %}text--underlined{% endif %}">{{ block.settings.title }}</a>
            </li>

          {% endfor %}

        </ul>

      </div>
    </div>
  </div>
</link-bar>

{% schema %}
{
  "name": "Link Bar Navigation",
  "class": "shopify-section--link-bar-nav",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Title"
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "rgba(255,255,255,0.8)"
    }
  ],
  "blocks": [
    {
      "type": "link",
      "name": "Link",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Link Title",
          "default": "Link Title"
        },
        {
          "type": "url",
          "id": "url",
          "label": "Link URL"
        },
        {
          "type": "checkbox",
          "id": "highlighted",
          "label": "Highlighted"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Link Bar Navigation",
      "settings": {}
    }
  ]
}
{% endschema %}