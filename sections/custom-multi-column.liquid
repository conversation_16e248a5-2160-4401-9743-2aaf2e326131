{%- if section.settings.background != settings.background and section.settings.background != 'rgba(0,0,0,0)' -%}
  {%- assign blends_with_background = false -%}
{%- else -%}
  {%- assign blends_with_background = true -%}
{%- endif -%}

<style>
  
  #shopify-section-{{ section.id }} {

    {%- if section.settings.background == 'rgba(0,0,0,0)' -%}
      {%- assign section_background = settings.background -%}
    {%- else -%}
      {%- assign section_background = section.settings.background -%}
    {%- endif -%}

    {%- if section.settings.text_color == 'rgba(0,0,0,0)' -%}
      {%- assign text_color = settings.text_color -%}
    {%- else -%}
      {%- assign text_color = section.settings.text_color -%}
    {%- endif -%}

    {%- if section.settings.heading_color == 'rgba(0,0,0,0)' -%}
      {%- assign heading_color = settings.heading_color -%}
    {%- else -%}
      {%- assign heading_color = section.settings.heading_color -%}
    {%- endif -%}

    {%- if section.settings.subheading_color == 'rgba(0,0,0,0)' -%}
      {%- assign subheading_color = settings.subheading_color -%}
    {%- else -%}
      {%- assign subheading_color = section.settings.subheading_color -%}
    {%- endif -%}

    {%- if section.settings.button_background == 'rgba(0,0,0,0)' -%}
      {%- assign button_background = settings.primary_button_background -%}
    {%- else -%}
      {%- assign button_background = section.settings.button_background -%}
    {%- endif -%}

    {%- if section.settings.button_text_color == 'rgba(0,0,0,0)' -%}
      {%- assign button_text_color = settings.primary_button_text_color -%}
    {%- else -%}
      {%- assign button_text_color = section.settings.button_text_color -%}
    {%- endif -%}

    --subheading-color: {{ subheading_color.red }}, {{ subheading_color.green }}, {{ subheading_color.blue }};
    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
    
    --button-background: {{ button_background.red }}, {{ button_background.green }}, {{ button_background.blue }};
    --button-text-color: {{ button_text_color.red }}, {{ button_text_color.green }}, {{ button_text_color.blue }};
    
    --prev-next-button-background: {{ settings.background.red }}, {{ settings.background.green }}, {{ settings.background.blue }};
    --prev-next-button-color: {{ settings.text_color.red }}, {{ settings.text_color.green }}, {{ settings.text_color.blue }};

    --section-background: {{ section_background.red }}, {{ section_background.green }}, {{ section_background.blue }};

    {%- if section.settings.overlay_color != 'rgba(0,0,0,0)' and section.settings.overlay_color != blank -%}
      {%- assign overlay_color = section.settings.overlay_color -%}
      --image-overlay-color: {{ overlay_color.red }}, {{ overlay_color.green }}, {{ overlay_color.blue }};
      --image-overlay-opacity: {{ section.settings.overlay_opacity | divided_by: 100.00 }};
    {%- endif -%}

    {%- if section.settings.overlay_bottom_offset != 0 -%}
      --bottom-offset: {{ section.settings.overlay_bottom_offset }}px;
    {%- endif -%}

  }

  {% comment %} Block Colors {% endcomment %}

  #shopify-section-{{ section.id }} .multi-column__item {
    
    {% if section.settings.block_subheading_color != blank and section.settings.block_subheading_color != 'rgba(0,0,0,0)' and section.settings.block_subheading_color != section.settings.subheading_color -%}
      --subheading-color: {{ section.settings.block_subheading_color.red }}, {{ section.settings.block_subheading_color.green }}, {{ section.settings.block_subheading_color.blue }};
    {% endif -%}

    {% if section.settings.block_heading_color != blank and section.settings.block_heading_color != 'rgba(0,0,0,0)' and section.settings.block_heading_color != section.settings.heading_color -%}
      --heading-color: {{ section.settings.block_heading_color.red }}, {{ section.settings.block_heading_color.green }}, {{ section.settings.block_heading_color.blue }};
    {% endif -%}

    {% if section.settings.block_text_color != blank and section.settings.block_text_color != 'rgba(0,0,0,0)' and section.settings.block_text_color != section.settings.text_color -%}
      --text-color: {{ section.settings.block_text_color.red }}, {{ section.settings.block_text_color.green }}, {{ section.settings.block_text_color.blue }};
      color: RGB(var(--text-color));
    {% endif -%}

  }

  {%- if section.settings.link_color != 'rgba(0,0,0,0)' and section.settings.link_color != text_color  -%}
    #shopify-section-{{ section.id }} .multi-column__link {
      --text-color: {{ section.settings.link_color.red }}, {{ section.settings.link_color.green }}, {{ section.settings.link_color.blue }};
      color: RGB(var(--text-color));
    }
  {%- endif -%}


  {%- if section.settings.title == blank and section.settings.subheading == blank and section.settings.content == blank -%}
    #shopify-section-{{ section.id }} {
      --vertical-breather: 40px; /* Only on multi-column section, due to its specific usage we reduce spacing when no content */
    }

    {%- if blends_with_background or section.settings.mobile_item_size == 'small' -%}
      /* Reduce the margin on small devices to create a slightly better layout */
      @media screen and (max-width: 999px) {
        #shopify-section-{{ section.id }} {
          --vertical-breather: var(--container-gutter);
        }
      }
    {%- endif -%}
  {%- endif -%}

  {%- for block in section.blocks -%}

    #block-{{ section.id }}-{{ block.id }} {
      {%- if block.settings.overlay_color != 'rgba(0,0,0,0)' and block.settings.overlay_color != blank -%}
        --image-overlay-color: {{ block.settings.overlay_color.red }}, {{ block.settings.overlay_color.green }}, {{ block.settings.overlay_color.blue }};
      {%- endif %}
      {%- if block.settings.overlay_opacity != section.settings.overlay_opacity and block.settings.overlay_opacity != 0 -%}
        --image-overlay-opacity: {{ block.settings.overlay_opacity | divided_by: 100.00 }};
      {%- endif %}
    }

    {%- if block.settings.image_border != 'rgba(0,0,0,0)' -%}
      #block-{{ section.id }}-{{ block.id }} .multi-column__image-wrapper {
        border: 1px solid {{ block.settings.image_border }};
      }
    {%- endif -%}

  {%- endfor -%}

</style>

{%- if section.settings.override_blend == true -%}
  {%- assign blends_with_background = false -%}
{%- endif -%}

{%- capture section_classes -%}
  {% render 'section-classes', section: section %}
{%- endcapture -%}

<section class="section {{ section_classes }} {% unless blends_with_background %}section--flush{% endunless %}">

  {% if section.settings.extra_code != blank %}
    {{ section.settings.extra_code }}
  {% endif %}

  {%- if section.settings.anchor -%}
    <a class="visually-hidden anchor" id="{{ section.settings.anchor }}" name="{{ section.settings.anchor }}"></a>
  {%- endif -%}

  <div class="section__color-wrapper">
    <div class="container {% unless blends_with_background %}vertical-breather{% endunless %}">
      {%- if section.settings.title != blank or section.settings.subheading != blank or section.settings.content != blank -%}
        <header class="section__header {% if section.settings.content != blank %}section__header--tight{% endif %} section__header--{{ section.settings.column_alignment }}">
          <div class="text-container text--{{ section.settings.heading_alignment }}">
            {%- if section.settings.subheading != blank -%}
              <h2 class="heading heading--small">{{ section.settings.subheading }}</h2>
            {%- endif -%}

            {%- if section.settings.title != blank -%}
              <h3 class="heading h2">{{ section.settings.title }}</h3>
            {%- endif -%}

            {%- if section.settings.content != blank -%}
              {% if section.settings.content_style != "" %}
                {%- capture tag -%}<p class="{{ section.settings.content_style }}"{%- endcapture -%}
                {{- section.settings.content | replace: "<p", tag -}}
              {% else %}
                {{- section.settings.content -}}
              {% endif %}
            {%- endif -%}

          </div>
        </header>
      {%- endif -%}

      {%- if section.blocks.size > 0 -%}
        <multi-column {% if section.settings.stack_items %}stack{% endif %} {% if section.settings.reveal_on_scroll %}stagger-apparition{% endif %} class="multi-column multi-column--pocket-{{ section.settings.mobile_item_size }} multi-column--{{ section.settings.desktop_item_size }} multi-column--spacing-{{ section.settings.spacing }}">
          {%- assign align_arrows_on_image = true -%}
          {%- assign smallest_image_aspect_ratio = 0 -%}

          <div {% unless section.settings.stack_items %}class="scroller"{% endunless %}>
            <scrollable-content class="multi-column__inner multi-column__inner--{{ section.settings.column_alignment }} {% unless section.settings.stack_items %}multi-column__inner--scroller{% endunless %}">
              {%- case section.settings.desktop_item_size -%}
                {%- when 'pico' -%}
                  {%- assign desktop_items_per_row = 8 -%}

                {%- when 'small' -%}
                  {%- assign desktop_items_per_row = 7 -%}

                {%- when 'medium' -%}
                  {%- assign desktop_items_per_row = 5 -%}

                {%- when 'large' -%}
                  {%- assign desktop_items_per_row = 3 -%}
              {%- endcase -%}

              {%- if section.settings.stack_items -%}
                {%- case section.settings.mobile_item_size -%}
                  {%- when 'small' -%}
                    {%- assign mobile_calculated_size = 'calc(50vw - 48px)' -%}
                    {%- assign tablet_calculated_size = 'calc(20vw - 80px)' -%}

                  {%- when 'medium' -%}
                    {%- assign mobile_calculated_size = 'calc(100vw - 48px)' -%}
                    {%- assign tablet_calculated_size = 'calc(25vw - 80px)' -%}

                  {%- when 'large' -%}
                    {%- assign mobile_calculated_size = 'calc(100vw - 48px)' -%}
                    {%- assign tablet_calculated_size = 'calc(33vw - 80px)' -%}
                {%- endcase -%}
              {%- else -%}
                {%- case section.settings.mobile_item_size -%}
                  {%- when 'small' -%}
                    {%- assign mobile_calculated_size = '25vw' -%}
                    {%- assign tablet_calculated_size = '20vw' -%}

                  {%- when 'medium' -%}
                    {%- assign mobile_calculated_size = '35vw' -%}
                    {%- assign tablet_calculated_size = '26vw' -%}

                  {%- when 'large' -%}
                    {%- assign mobile_calculated_size = '56vw' -%}
                    {%- assign tablet_calculated_size = '36vw' -%}
                {%- endcase -%}
              {%- endif -%}

              {%- case section.settings.spacing -%}
                {%- when 'tight' -%}
                  {%- assign desktop_item_gap = 24 -%}

                {%- when 'normal' -%}
                  {%- assign desktop_item_gap = 40 -%}

                {%- when 'loose' -%}
                  {%- assign desktop_item_gap = 60 -%}
              {%- endcase -%}

              {%- assign desktop_items_per_row_minus_one = desktop_items_per_row | minus: 1 -%}
              {%- assign gap_width = desktop_item_gap | divided_by: desktop_items_per_row | times: desktop_items_per_row_minus_one -%}

              {%- for block in section.blocks -%}
                {%- capture block_content -%}

                  {%- capture image_content -%}

                    {%- if block.settings.title != blank and section.settings.heading_over_image == true -%}
                      <p class="heading {{ section.settings.heading_style }}">{{ block.settings.title | escape }}</p>
                    {%- endif -%}

                    {%- if block.settings.content != blank and section.settings.content_over_image == true -%}
                      {%- assign content_style = section.settings.block_content_style -%}
                      {% if content_style != "" %}
                        {%- capture tag -%}<p class="{{ content_style }}"{%- endcapture -%}
                        {{- block.settings.content | replace: "<p", tag -}}
                      {% else %}
                        {{- block.settings.content -}}
                      {% endif %}
                    {%- endif -%}

                    {%- if block.settings.link_text != blank and section.settings.button_over_image == true -%}
                      {%- if block.settings.link_style contains 'button' -%}
                        <div class="button-wrapper">
                          <div class="multi-column__button {{ section.settings.link_style }} {% if section.settings.link_style contains 'button' %}{{ section.settings.button_style }}{% endif %}">{{ block.settings.link_text | escape }}</div>
                        </div>
                      {%- else -%}
                        <div class="multi-column__link {{ section.settings.link_style }} {% if section.settings.link_style contains 'button' %}{{ section.settings.button_style }}{% endif %}">{{ block.settings.link_text | escape }}</div>
                      {%- endif -%}
                    {%- endif -%}

                  {%- endcapture -%}

                  {%- capture text_content -%}

                    {%- if block.settings.title != blank and section.settings.heading_over_image != true -%}
                      <p class="heading {{ section.settings.heading_style }}">{{ block.settings.title | escape }}</p>
                    {%- endif -%}

                    {%- if block.settings.content != blank and section.settings.content_over_image != true -%}
                      {%- assign content_style = section.settings.block_content_style -%}
                      {% if content_style != "" %}
                        {%- capture tag -%}<p class="{{ content_style }}"{%- endcapture -%}
                        {{- block.settings.content | replace: "<p", tag -}}
                      {% else %}
                        {{- block.settings.content -}}
                      {% endif %}
                    {%- endif -%}

                    {%- if block.settings.link_text != blank and section.settings.button_over_image != true -%}
                      {%- if block.settings.link_style contains 'button' -%}
                        <div class="button-wrapper">
                          <a href="{{ block.settings.link_url }}" class="multi-column__button {{ section.settings.link_style }} {% if section.settings.link_style contains 'button' %}{{ section.settings.button_style }}{% endif %}">{{ block.settings.link_text | escape }}</a>
                        </div>
                      {%- else -%}
                        <a href="{{ block.settings.link_url }}" class="multi-column__link {{ section.settings.link_style }} {% if section.settings.link_style contains 'button' %}{{ section.settings.button_style }}{% endif %}">{{ block.settings.link_text | escape }}</a>
                      {%- endif -%}
                    {%- endif -%}

                  {%- endcapture -%}

                  {%- if block.settings.image != blank -%}

                    {%- assign smallest_image_aspect_ratio = smallest_image_aspect_ratio | at_least: block.settings.image.aspect_ratio -%}
                    {%- capture sizes_attribute -%}(max-width: 740px) {{ mobile_calculated_size }}, (max-width: 999px) {{ tablet_calculated_size }}, {{ 1520.0 | divided_by: desktop_items_per_row | minus: gap_width | ceil }}px{%- endcapture -%}

                    {%- if block.settings.text_alignment != section.settings.block_text_alignment and block.settings.text_alignment != "" -%}
                      {%- assign content_alignment = block.settings.text_alignment  -%}
                    {%- else -%}
                      {%- assign content_alignment = section.settings.block_text_alignment  -%}
                    {%- endif -%}

                    {%- if block.settings.link_url != blank -%}
                      <a href="{{ block.settings.link_url }}" class="multi-column__image-wrapper image-zoom" style="max-width: {{ block.settings.image_width }}%; width: {{ block.settings.image.width }}px">
                        {%- if image_content != blank -%}
                          <div class="multi-column__image-content text--{{ content_alignment }} {% if section.settings.overlay_color != 'rgba(0,0,0,0)' and section.settings.overlay_color != blank %}multi-column__image-overlay{% endif %}">
                            {{ image_content | replace: "<a", "<div " | replace: "</a>", "</div>" }}
                          </div>
                        {%- endif -%}
                        {{- block.settings.image | image_url: width: block.settings.image.width | image_tag: loading: 'lazy', sizes: sizes_attribute, widths: '200,300,400,500,600,700,800,900,1000,1100,1200', class: 'multi-column__image' -}}
                      </a>
                    {%- else -%}
                      <div class="multi-column__image-wrapper" style="max-width: {{ block.settings.image_width }}%; width: {{ block.settings.image.width }}px">
                        {%- if image_content != blank -%}
                          <div class="multi-column__image-content text--{{ content_alignment }} {% if section.settings.overlay_color != 'rgba(0,0,0,0)' and section.settings.overlay_color != blank %}multi-column__image-overlay{% endif %}">
                            {{ image_content }}
                          </div>
                        {%- endif -%}
                        {{- block.settings.image | image_url: width: block.settings.image.width | image_tag: loading: 'lazy', sizes: sizes_attribute, widths: '200,300,400,500,600,700,800,900,1000,1100,1200', class: 'multi-column__image' -}}
                      </div>
                    {%- endif -%}
                  {%- else -%}
                    {%- assign align_arrows_on_image = false -%}
                  {%- endif -%}

                  {%- if text_content != blank -%}
                    
                    {%- if block.settings.text_alignment != section.settings.block_text_alignment and block.settings.text_alignment != "" -%}
                      {%- assign content_alignment = block.settings.text_alignment  -%}
                    {%- else -%}
                      {%- assign content_alignment = section.settings.block_text_alignment  -%}
                    {%- endif -%}

                    <div class="multi-column__text-container text--{{ content_alignment }} text-container">
                      {{ text_content }}
                    </div>

                  {%- endif -%}
                {%- endcapture -%}

                {% comment %} Content {% endcomment %}

                {%- if block_content != blank -%}
                  <div id="block-{{ section.id }}-{{ block.id }}" class="multi-column__item multi-column__item--align-{{ block.settings.vertical_alignment }} {% if block.settings.link_url != blank %}image-zoom{% endif %}" {% if section.settings.reveal_on_scroll %}reveal{% endif %} {{ block.shopify_attributes }}>
                    {{- block_content -}}
                  </div>
                {%- endif -%}

              {%- endfor -%}
            </scrollable-content>

            {%- unless section.settings.stack_items -%}
              <prev-next-buttons class="multi-column__prev-next {% unless align_arrows_on_image %}multi-column__prev-next--no-image{% endunless %} hidden-pocket" {% if align_arrows_on_image %}style="--smallest-image-aspect-ratio: {{ smallest_image_aspect_ratio }}"{% endif %}>
                <button class="multi-column__arrow prev-next-button prev-next-button--prev" disabled>
                  <span class="visually-hidden">{{ 'general.accessibility.previous' | t }}</span>
                  {%- render 'icon' with 'nav-arrow-left', block: true, direction_aware: true -%}
                </button>

                <button class="multi-column__arrow prev-next-button prev-next-button--next">
                  <span class="visually-hidden">{{ 'general.accessibility.next' | t }}</span>
                  {%- render 'icon' with 'nav-arrow-right', block: true, direction_aware: true -%}
                </button>
              </prev-next-buttons>
            {%- endunless -%}
          </div>
        </multi-column>
      {%- endif -%}
    </div>
  </div>
</section>

{% schema %}
{
  "name": "🪁 Multi-column",
  "class": "shopify-section--custom-multi-column",
  "disabled_on": {
    "groups": ["header", "custom.overlay"]
  },
  "blocks": [
    {
      "name": "Column",
      "type": "item",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "1200 x 1200px .jpg recommended"
        },
        {
          "type": "range",
          "id": "image_width",
          "min": 10,
          "max": 100,
          "unit": "%",
          "step": 1,
          "label": "Image width",
          "default": 100
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Your content"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Content",
          "default": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>"
        },
        {
          "type": "text",
          "id": "link_text",
          "label": "Link text"
        },
        {
          "type": "url",
          "id": "link_url",
          "label": "Link"
        },
        {
          "type": "select",
          "id": "text_alignment",
          "label": "Text alignment",
          "options": [
            {
              "value": "",
              "label": "Default"
            },
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": ""
        },
        {
          "type": "select",
          "id": "vertical_alignment",
          "label": "Vertical alignment",
          "options": [
            {
              "value": "start",
              "label": "Top"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "end",
              "label": "Bottom"
            }
          ],
          "default": "start"
        },
        {
          "type": "header",
          "content": "Colors",
          "info": "Overrides for section colors"
        },
        {
          "type": "range",
          "id": "overlay_opacity",
          "label": "Overlay opacity",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "%",
          "default": 0
        },
        {
          "type": "color",
          "id": "overlay_color",
          "label": "Overlay Color",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "image_border",
          "label": "Image border",
          "default": "rgba(0,0,0,0)"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "header",
      "content": "🪁 Kyte Settings"
    },
    {
      "type": "text",
      "id": "styling_class",
      "label": "Styling Class",
      "info": "For advanced use."
    },
    {
      "type": "text",
      "id": "anchor",
      "label": "Anchor",
      "info": "Anchor name used for jump links.",
      "placeholder": "#anchor"
    },
    {
      "type": "checkbox",
      "id": "use_padding",
      "label": "Use Padding"
    },
    {
      "type": "checkbox",
      "id": "box_shadow",
      "label": "Shadow"
    },
    {
      "type": "checkbox",
      "id": "section_extra_padding",
      "label": "Extra Vertical Padding"
    },
    {
      "type": "checkbox",
      "id": "override_blend",
      "label": "Stop section merging with previous section",
      "default": false,
      "info": "If the section \"merges\" with the previous and next sections, this separates them again."
    },
    {
      "type": "liquid",
      "id": "extra_code",
      "label": "Extra Code",
      "info": "Misc. Liquid code for embellishments."
    },
    {
      "type": "header",
      "content": "Section Settings"
    },
    {
      "type": "header",
      "content": "Subheading"
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "Subheading",
      "default": "Subheading"
    },
    {
      "type": "header",
      "content": "Headings"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Multi-column"
    },
    {
      "type": "select",
      "id": "heading_alignment",
      "label": "Text alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "Content"
    },
    {
      "type": "select",
      "id": "content_style",
      "label": "Content Style",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "text--xsmall",
          "label": "Tiny"
        },
        {
          "value": "text--small",
          "label": "Small"
        },
        {
          "value": "text--large",
          "label": "Large"
        }
      ],
      "default": ""
    },
    {
      "type": "header",
      "content": "Link / Button"
    },
    {
      "type": "select",
      "id": "link_style",
      "label": "Link style",
      "options": [
        {
          "value": "link",
          "label": "Link",
          "group": "Links"
        },
        {
          "value": "link--animated",
          "label": "Animated Link",
          "group": "Links"
        },
        {
          "value": "heading heading--small link",
          "label": "Subheading Link",
          "group": "Subheading"
        },
        {
          "value": "button button--tiny",
          "label": "Tiny",
          "group": "Buttons"
        },
        {
          "value": "button button--small",
          "label": "Small",
          "group": "Buttons"
        },
        {
          "value": "button",
          "label": "Normal",
          "group": "Buttons"
        }
      ],
      "default": "link"
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "Button style",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "button--primary",
          "label": "Primary"
        },
        {
          "value": "button--secondary",
          "label": "Secondary"
        },
        {
          "value": "button--tertiary",
          "label": "Tertiary"
        }
      ],
      "default": "",
      "info": "Only applies if a button link style is chosen."
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "checkbox",
      "id": "reveal_on_scroll",
      "label": "Reveal on scroll",
      "info": "Show animation when section becomes visible.",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "stack_items",
      "label": "Stack items",
      "default": false
    },
    {
      "type": "header",
      "content": "Column Layout"
    },
    {
      "type": "select",
      "id": "column_alignment",
      "label": "Column alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "select",
      "id": "mobile_item_size",
      "label": "Mobile/tablet column size",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ],
      "default": "medium"
    },
    {
      "type": "select",
      "id": "desktop_item_size",
      "label": "Desktop column size",
      "options": [
        {
          "value": "pico",
          "label": "X-Small"
        },
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ],
      "default": "medium"
    },
    {
      "type": "select",
      "id": "spacing",
      "label": "Spacing",
      "options": [
        {
          "value": "tight",
          "label": "Small"
        },
        {
          "value": "normal",
          "label": "Medium"
        },
        {
          "value": "loose",
          "label": "Large"
        }
      ],
      "default": "normal"
    },
    {
      "type": "header",
      "content": "Column Content"
    },
    {
      "type": "select",
      "id": "block_content_style",
      "label": "Column content style",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "text--xsmall",
          "label": "Tiny"
        },
        {
          "value": "text--small",
          "label": "Small"
        },
        {
          "value": "text--large",
          "label": "Large"
        }
      ],
      "default": ""
    },
    {
      "type": "select",
      "id": "heading_style",
      "label": "Column heading Style",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "h1",
          "label": "Heading 1",
          "group": "Headings"
        },
        {
          "value": "h2",
          "label": "Heading 2",
          "group": "Headings"
        },
        {
          "value": "h3",
          "label": "Heading 3",
          "group": "Headings"
        },
        {
          "value": "h4",
          "label": "Heading 4",
          "group": "Headings"
        },
        {
          "value": "h5",
          "label": "Heading 5",
          "group": "Headings"
        },
        {
          "value": "heading--small",
          "label": "Subheading",
          "group": "Subheadings"
        },
        {
          "value": "heading--xsmall",
          "label": "Subheading - Small",
          "group": "Subheadings"
        },
        {
          "value": "text",
          "label": "Default",
          "group": "Body Text"
        },
        {
          "value": "text--xsmall",
          "label": "Tiny",
          "group": "Body Text"
        },
        {
          "value": "text--small",
          "label": "Small",
          "group": "Body Text"
        },
        {
          "value": "text--large",
          "label": "Large",
          "group": "Body Text"
        }
      ],
      "default": "h4"
    },
    {
      "type": "select",
      "id": "block_text_alignment",
      "label": "Column text alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "header",
      "content": "Content over image",
      "info": "Choose the content you want to show over the image."
    },
    {
      "type": "checkbox",
      "id": "heading_over_image",
      "label": "Heading",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "content_over_image",
      "label": "Content",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "button_over_image",
      "label": "Button/Link",
      "default": false
    },
    {
      "type": "range",
      "id": "overlay_bottom_offset",
      "min": 0,
      "max": 20,
      "unit": "px",
      "step": 5,
      "label": "Offset",
      "default": 0,
      "info": "Offsets the overlay from the bottom edge of the image."
    },
    {
      "type": "header",
      "content": "Section Colors"
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "subheading_color",
      "label": "Subheading color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button color",
      "default": "rgba(0,0,0,0)",
      "info": "Only applies to buttons."
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text color",
      "default": "rgba(0,0,0,0)",
      "info": "Only applies to buttons."
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "header",
      "content": "Column Colors"
    },
    {
      "type": "color",
      "id": "block_heading_color",
      "label": "Heading color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "block_subheading_color",
      "label": "Subheading color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "block_text_color",
      "label": "Text color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "link_color",
      "label": "Link color",
      "default": "rgba(0,0,0,0)",
      "info": "Only applies to links, overrides default text color."
    },
    {
      "type": "color",
      "id": "overlay_color",
      "label": "Overlay Color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "range",
      "id": "overlay_opacity",
      "label": "Overlay opacity",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "🪁 Multi-column",
      "blocks": [
        {
          "type": "item",
          "settings": {
            "title": "Column 1"
          }
        },
        {
          "type": "item",
          "settings": {
            "title": "Column 1"
          }
        },
        {
          "type": "item",
          "settings": {
            "title": "Column 1"
          }
        },
        {
          "type": "item",
          "settings": {
            "title": "Column 1"
          }
        }
      ]
    }
  ]
}
{% endschema %}