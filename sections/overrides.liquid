<style>

  :root {

    {% comment %} Background {% endcomment %}
  
    {%- if section.settings.background != blank and section.settings.background != 'rgba(0,0,0,0)' -%}
      {%- assign background = section.settings.background -%}
      --background: {{ background.red }}, {{ background.green }}, {{ background.blue }};
    {%- endif -%}
  
    {% comment %} Typography {% endcomment %}
  
    {%- if section.settings.heading_color != blank and section.settings.heading_color != 'rgba(0,0,0,0)' -%}
      {%- assign heading_color = section.settings.heading_color -%}
      --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    {%- endif -%}
  
    {%- if section.settings.subheading_color != blank and section.settings.subheading_color != 'rgba(0,0,0,0)' -%}
      {%- assign subheading_color = section.settings.subheading_color -%}
      --subheading-color: {{ subheading_color.red }}, {{ subheading_color.green }}, {{ subheading_color.blue }};
    {%- endif -%}
  
    {%- if section.settings.text_color != blank and section.settings.text_color != 'rgba(0,0,0,0)' -%}
      {%- assign text_color = section.settings.text_color -%}
      --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
    {%- endif -%}
  
    {% comment %} Primary Button {% endcomment %}
  
    {%- if section.settings.button_background == 'rgba(0,0,0,0)' -%}
      {%- assign button_background = settings.primary_button_background -%}
      --primary-button-background: {{ button_background.red }}, {{ button_background.green }}, {{ button_background.blue }};
    {%- endif -%}
  
    {%- if section.settings.button_text_color == 'rgba(0,0,0,0)' -%}
      {%- assign button_text_color = settings.primary_button_text_color -%}
      --primary-button-text-color: {{ button_text_color.red }}, {{ button_text_color.green }}, {{ button_text_color.blue }};
    {%- endif -%}

  }

</style>

{% schema %}
{
  "name": "🪁 Global Overrides",
  "class": "shopify-section--overrides",
  "settings": [
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background color"
    },
    {
      "type": "color",
      "id": "subheading_color",
      "label": "Subheading color"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text"
    }
  ],
  "presets": [
    {
      "name": "Overrides"
    }
  ]
}
{% endschema %}
